package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: GameInfoDto
 * @author: gaoming
 * @date: 2021/07/01
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameInfoDto implements Serializable {
    private static final long serialVersionUID = 4021492155585942621L;

    /**
     * 游戏图标
     */
    private String game_icon = "";

    /**
     * 游戏名称
     */
    private String game_name = "";
}
