package com.bilibili.mgk.platform.common;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 构建消息对象。
 *
 * <AUTHOR>
 * @since 2018年06月06日
 */
@Data
public abstract class AbstractMessageBuilder {

    // 最多不超过1000个
    private Map<String, String> params;
    private List<Number> mids;
    private String mc;
    private String title;
    private Integer dataType;
    private String context;

    public AbstractMessageBuilder(String context, String mc, String title, Integer dataType, List<Number> mids){
        this.context = context;
        this.mc = mc;
        this. title = title;
        this.dataType = dataType;
        this.mids = mids;
        this.params = new HashMap<>();
    }

    public AbstractMessageBuilder putParam(String key, String value) {
        if (key != null && !key.isEmpty() && value != null && !value.isEmpty()) {
            this.params.put(key, value);
        }
        return this;
    }

    public Message build() {
        for (Map.Entry<String, String> entry : params.entrySet()) {
            context = context.replace(entry.getKey(), entry.getValue());
        }
        String mid_list = mids.isEmpty() ? "" : mids.stream()
                .filter(Objects::nonNull).distinct().map(Number::toString)
                .collect(Collectors.joining(","));
        return new Message(mc, title, dataType, context, mid_list);
    }

    @Data
    public static class Message {
        private String mc;
        private String title;
        private Integer data_type;
        private String context;
        private String mid_list;

        Message(String mc, String title, Integer data_type, String context, String mid_list) {
            this.mc = mc;
            this.title = title;
            this.data_type = data_type;
            this.context = context;
            this.mid_list = mid_list;
        }
    }

}
