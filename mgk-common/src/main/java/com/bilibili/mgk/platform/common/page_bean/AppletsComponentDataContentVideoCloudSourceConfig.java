package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @file: AppletsComponentDataContentVideoCloudSourceConfig
 * @author: gaoming
 * @date: 2021/12/03
 * @version: 1.0
 * @description:
 **/

@AllArgsConstructor
@Builder
@NoArgsConstructor
@Data
public class AppletsComponentDataContentVideoCloudSourceConfig implements Serializable {
    private static final long serialVersionUID = -1630280585829705561L;

    private Integer id;
    private Integer biz_id;
    private String cover;
    private String url;
    private String name;
    private Integer width;
    private Integer height;
    private Integer duration;
    private Integer size;

}
