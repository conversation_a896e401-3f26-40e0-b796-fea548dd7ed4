package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018年8月26日
 * @desc 宏参数枚举
 */
@AllArgsConstructor
public enum MacroParamEnum {
    MID(1, "__MID__", "mid","0"),
    BUVID(2, "__BUVID__", "buvid",""),
    IMEI(3, "__IMEI__", "imei",""),
    DUID(4, "__DUID__","duid", ""),
    IDFA(5, "__IDFA__","idfa", ""),
    ANDROID_ID(6, "__ANDROIDID__", "android_id",""),
    OS(7, "__OS__","os", "3"),
    REQUESTID(8, "__REQUESTID__","request_id", ""),
    SOURCEID(9, "__SOURCEID__","source_id", "0"),
    CREATIVEID(10, "__CREATIVEID__","creative_id", "0"),
    TRACKID(11, "__TRACKID__","track_id", ""),
    ADTYPE(12, "__ADTYPE__","adtype", "")
    ;

    @Getter
    private Integer id;
    @Getter
    private String macro;
    /**
     * 拼接落地页宏参数名称
     */
    @Getter
    private String paramName;
    @Getter
    private String defaultVal;

    public static MacroParamEnum getById(int id) {
        for (MacroParamEnum bean : values()) {
            if (bean.getId() == id) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MacroParamEnum " + id);
    }

    public static String getDefaultValByMacro(String macro) {
        for (MacroParamEnum bean : values()) {
            if (bean.getMacro().equals(macro)) {
                return bean.getDefaultVal();
            }
        }
        return macro;
    }

}
