package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @file: MgkInspirationArticleStatusEnum
 * @author: gaoming
 * @date: 2021/03/23
 * @version: 1.0
 * @description:
 **/
@AllArgsConstructor
@Getter
public enum MgkInspirationArticleStatusEnum {
    /**
     * 启用
     */
    ENABLE(0, "启用"),

    /**
     * 禁用
     */
    DISABLE(1, "禁用");
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static MgkInspirationArticleStatusEnum getByCode(int code) {
        for (MgkInspirationArticleStatusEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkInspirationArticleStatusEnum " + code);
    }
}
