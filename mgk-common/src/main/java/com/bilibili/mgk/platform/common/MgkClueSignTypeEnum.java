package com.bilibili.mgk.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @ClassName MgkClueSignTypeEnum
 * <AUTHOR>
 * @Date 2022/5/18 7:02 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
public enum MgkClueSignTypeEnum {
    /**
     * MD5
     */
    MD5(1, "MD5"),
    /**
     * SHA_256
     */
    HMAC_SHA_256(2, "SHA_256"),

    ;
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static MgkClueSignTypeEnum getByCode(int code) {
        for (MgkClueSignTypeEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code MgkClueSignTypeEnum " + code);
    }
}
