package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.criteria.CriteriaBuilder;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName OpenFullScreenConfig
 * <AUTHOR>
 * @Date 2022/4/22 7:33 下午
 * @Version 1.0
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NativeOpenFullScreenConfig implements Serializable {
    private static final long serialVersionUID = 75745371608889L;

    private String title;
    private String icon;
    private String cover;
    private String desc;
    private String jump_url;
    private String callup_url;
    private List<String> report_urls;
    private ButtonUpgrade button;
    private VideoUpgrade video;
    private Long report_time;
    private Integer show_style;
    private String weburl;
}
