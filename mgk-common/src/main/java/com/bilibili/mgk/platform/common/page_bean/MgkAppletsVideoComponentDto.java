package com.bilibili.mgk.platform.common.page_bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

/**
 * @file: MgkAppletsVideoComponentDto
 * @author: gaoming
 * @date: 2021/12/03
 * @version: 1.0
 * @description: 小程序的视频组件的格式 这种定义是因为一开始没有约束小程序的格式，todo 和前端约定小程序的格式，后端需要校验
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MgkAppletsVideoComponentDto implements Serializable {
    private static final long serialVersionUID = 2206633801924182626L;

    @Builder.Default
    private String id = UUID.randomUUID().toString().replaceAll("-", "");

    @Builder.Default
    private String name = "joint-video-fixed-top";

    @Builder.Default
    private Boolean active = true;


    class Data implements Serializable{
        private static final long serialVersionUID = -8433778653071513300L;

    }

}
