#!/bin/bash

ROOT=$(pwd -P)
WORKSPACE=$(dirname ${ROOT})
BAPIS_GO_DIR="${WORKSPACE}/bapis-go"
ROOT_NAME=$(basename $(pwd))

findAllDir() {
  files=$(find . -type f |grep ".proto" |grep -v ".git" |grep -v "cheese/service/season/episode")
  for file in ${files}
  do
    filename=${file:2}
    completeProtoFilePath=${ROOT}/${filename}
    completePbFile=${BAPIS_GO_DIR}/${filename}
    completePBFilePathInBapisGo=$(echo ${completePbFile} | awk '{gsub(".proto",".pb.go",$1);print $0}')
    completePBFilePathInBapis=$(echo ${completeProtoFilePath} | awk '{gsub(".proto",".pb.go",$1);print $0}')
    dirPBfilePath=$(dirname ${completePBFilePathInBapisGo})
    echo "begin generate proto3 code for file ${ROOT}/${filename}"
    kratos proto grpc ${filename}
    find ${completePBFilePathInBapisGo}
    if [[ $? -eq 0 ]]; then
      echo "${completePBFilePathInBapisGo} already exists, removing"
      rm -rf ${completePBFilePathInBapisGo}
      echo "mv ${completePBFilePathInBapis} ${dirPBfilePath}"
      mv ${completePBFilePathInBapis} ${dirPBfilePath}
    else
      echo "${completeProtoFilePath} not found, directly move"
      echo "mv ${completePBFilePathInBapis} ${dirPBfilePath}"
      mv ${completePBFilePathInBapis} ${dirPBfilePath}
    fi
    echo ""
  done
}

findAllDir