syntax = "proto3";
package passport.service;

import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "passport.service";
option go_package = "buf.bilibili.co/bapis/bapis-gen/passport/service;api";
option java_package = "com.bapis.passport.service";
option java_multiple_files = true;

// import "google/api/annotations.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

message LoginLog {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
    int64 timestamp = 2 [(gogoproto.jsontag) = "timestamp", json_name = "timestamp"];
    int64 loginip = 3 [(gogoproto.jsontag) = "loginip", json_name = "loginip"];
    int64 type = 4 [(gogoproto.jsontag) = "type", json_name = "type"];
    string server = 5 [(gogoproto.jsontag) = "server", json_name = "server"];
    string login_ip_str = 6 [(gogoproto.jsontag) = "login_ip_str", json_name = "login_ip_str"];

}

message GetLoginLogsReq {
    int64 mid = 1 [(gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
    int32 limit = 2 [(gogoproto.moretags) = "form:\"limit\" validate:\"required\""];
    int32 delta = 3 [(gogoproto.moretags) = "form:\"delta\""];
}

message GetLoginLogsReply {
    repeated LoginLog loginLogs = 1 [(gogoproto.jsontag) = "loginLogs", json_name = "loginLogs"];
}

message AddBlockUserRecordReq {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name ="mid"];
    string operator = 2 [(gogoproto.jsontag) = "operator", json_name ="operator"];
    string remark = 3 [(gogoproto.jsontag) = "remark", json_name ="remark"];
    string tel = 4 [(gogoproto.jsontag) = "tel", json_name ="tel"];
    string email = 5 [(gogoproto.jsontag) = "email", json_name="email"];
    string cname = 6 [(gogoproto.jsontag) = "cname", json_name ="cname"];
    string cid = 7 [(gogoproto.jsontag) = "cid", json_name ="cid"];
    string uid = 8 [(gogoproto.jsontag) = "uid", json_name ="uid"];
}

message AddTelBlackListReq {
    // 手机号
    string tel = 1 [(gogoproto.jsontag) = "tel", json_name ="tel"];
    // 国家码, eg:86
    string code = 2 [(gogoproto.jsontag) = "code", json_name ="code"];
    string operator = 3 [(gogoproto.jsontag) = "operator", json_name ="operator"];
    string remark = 4 [(gogoproto.jsontag) = "remark",json_name ="remark"];
}

message DelTelBlackListReq {
    // 手机号
    string tel = 1 [(gogoproto.jsontag) = "tel"];
    // 国家码, eg:86
    string code = 2 [(gogoproto.jsontag) = "code"];
    string operator = 3 [(gogoproto.jsontag) = "operator"];
    string remark = 4 [(gogoproto.jsontag) = "remark"];
}

message OperateReq {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid",(gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
    string operator = 2 [(gogoproto.jsontag) = "operator", json_name = "operator",(gogoproto.moretags) = "form:\"operator\" validate:\"required\""];
    int64 operator_id = 3 [(gogoproto.jsontag) = "operator_id", json_name = "operator_id"];
    string remark = 4 [(gogoproto.jsontag) = "remark", json_name = "remark",(gogoproto.moretags) = "form:\"remark\" validate:\"required\""];
}

// 空的message，对应真实service只返回error，没有具体返回值
message EmptyStruct {}

message CheckSafeReq {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name ="mid"];
    int32 question = 2 [(gogoproto.jsontag) = "question", json_name ="question"];
    string answer = 3 [(gogoproto.jsontag) = "answer", json_name ="answer"];
}

message CheckSafeReply {
    bool result = 1 [(gogoproto.jsontag) = "result", json_name ="result"];
    bool not_set = 2 [(gogoproto.jsontag) = "not_set", json_name = "not_set"];
}

message CheckRegReq {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name ="mid"];
    int64 regTime = 2 [(gogoproto.jsontag) = "reg_time", json_name ="reg_time"];
    int32 regType = 3 [(gogoproto.jsontag) = "reg_type", json_name ="reg_type"];
    string regAddr = 4 [(gogoproto.jsontag) = "reg_addr", json_name ="reg_addr"];
    bool loosely = 5 [(gogoproto.jsontag) = "loosely", json_name ="loosely"];
}

message CheckRegReply {
    int64 regTime = 1 [(gogoproto.jsontag) = "reg_time", json_name ="reg_time"];
    bool regType = 2 [(gogoproto.jsontag) = "reg_type", json_name ="reg_type"];
    bool regAddr = 3 [(gogoproto.jsontag) = "reg_addr", json_name ="reg_addr"];
}

message CheckLoginAddrsReq {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name ="mid"];
    int32 limit = 2 [(gogoproto.jsontag) = "limit", json_name ="limit"];
    repeated string loginAddrs = 3 [(gogoproto.jsontag) = "login_addrs", json_name ="login_addrs"];
}

message CheckLoginAddrsReply {
    map<string,bool> result = 1 [(gogoproto.jsontag) = "result", json_name ="result"];
}

message CheckPwdReq {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name ="mid"];
    repeated string pwds = 2 [(gogoproto.jsontag) = "pwds", json_name ="pwds"];
}

message CheckPwdReply {
    map<string,bool> result = 1 [(gogoproto.jsontag) = "result", json_name ="result"];
}

message UpdatePwdReply {
    string pwd = 1 [(gogoproto.jsontag) = "pwd", json_name ="pwd"];
}

message AddUserTelPastReq {
    int64  Mid = 1 [(gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
    int64  Cid = 2 [(gogoproto.moretags) = "form:\"cid\" validate:\"required\""];
    string Tel = 3 [(gogoproto.moretags) = "form:\"tel\" validate:\"required\""];
}

service Passport {
    rpc LoginLogs (GetLoginLogsReq) returns (GetLoginLogsReply);

    // 添加用户封禁记录
    rpc AddBlockUserRecord(AddBlockUserRecordReq) returns (EmptyStruct);
    // 添加手机号黑名单
    rpc AddTelBlackList(AddTelBlackListReq) returns (EmptyStruct);
    // 移除手机号黑名单
    rpc DelTelBlackList(DelTelBlackListReq) returns (EmptyStruct);
    // 删除用户
    rpc DeleteUser(OperateReq) returns (EmptyStruct);
    // 解绑手机
    rpc UnbindTel(OperateReq) returns (EmptyStruct);
    // 解绑邮箱
    rpc UnbindEmail(OperateReq) returns (EmptyStruct);
    // 更新密码
    rpc UpdatePwd(OperateReq) returns (UpdatePwdReply);
    // 重置密保问题
    rpc ResetUserSafeQuestion (OperateReq) returns (EmptyStruct);
    // 将用户的手机号加入手机号黑名单
    rpc BlockUserTel (OperateReq) returns (EmptyStruct);

    // 检查密保
    rpc CheckSafe(CheckSafeReq) returns (CheckSafeReply);
    // 检查注册信息
    rpc CheckReg(CheckRegReq) returns (CheckRegReply);
    // 检查历史密码
    rpc CheckHistoryPwd(CheckPwdReq) returns (CheckPwdReply);

    // 账号内部使用
    rpc AddUserTelPast(AddUserTelPastReq) returns (EmptyStruct);
}