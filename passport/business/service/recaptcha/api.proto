syntax = "proto3";

import "google/api/annotations.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

package passport.business.service.recaptcha.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/passport/business.service.recaptcha;api";
option java_package = "com.bapis.passport.business.service.recaptcha";
option java_multiple_files = true;

option (gogoproto.goproto_getters_all) = false;

service Recaptcha {
    rpc SyncCaptcha (SyncCaptchaReq) returns (EmptyReply) {
        option (google.api.http) = {
            post: "/x/internal/recaptcha/sync_captcha";
        };
    }
}

message EmptyReply {
}

message SyncCaptchaReq {
    int32 type = 1 [(gogoproto.jsontag) = "type",(gogoproto.moretags) = 'form:"type"'];
    SyncCaptcha captcha = 2 [(gogoproto.jsontag) = "captcha",(gogoproto.moretags) = 'form:"captcha"'];
    SyncImgCaptcha imgCaptcha = 3 [(gogoproto.jsontag) = "img_captcha",(gogoproto.moretags) = 'form:"img_captcha"'];
    SyncSmsCaptcha smsCaptcha = 4 [(gogoproto.jsontag) = "sms_captcha",(gogoproto.moretags) = 'form:"sms_captcha"'];
    SyncEmailCaptcha emailCaptcha = 5 [(gogoproto.jsontag) = "email_captcha",(gogoproto.moretags) = 'form:"email_captcha"'];
}

message SyncCaptcha {
    string token = 1 [(gogoproto.jsontag) = "token",(gogoproto.moretags) = 'form:"token"'];
    string business = 2 [(gogoproto.jsontag) = "business",(gogoproto.moretags) = 'form:"business"'];
    string type = 3 [(gogoproto.jsontag) = "type",(gogoproto.moretags) = 'form:"type"'];
}

message SyncImgCaptcha {
    string token = 1 [(gogoproto.jsontag) = "token",(gogoproto.moretags) = 'form:"token"'];
    string code = 2 [(gogoproto.jsontag) = "code",(gogoproto.moretags) = 'form:"code"'];
}

message SyncSmsCaptcha {
    string token = 1 [(gogoproto.jsontag) = "token",(gogoproto.moretags) = 'form:"token"'];
    string type = 2 [(gogoproto.jsontag) = "type",(gogoproto.moretags) = 'form:"type"'];
    string tel = 3 [(gogoproto.jsontag) = "tel",(gogoproto.moretags) = 'form:"tel"'];
    int32 cid = 4 [(gogoproto.jsontag) = "cid",(gogoproto.moretags) = 'form:"cid"'];
    string code = 5 [(gogoproto.jsontag) = "code",(gogoproto.moretags) = 'form:"code"'];
    string ip = 6 [(gogoproto.jsontag) = "ip",(gogoproto.moretags) = 'form:"ip"'];
}

message SyncEmailCaptcha {
    string token = 1 [(gogoproto.jsontag) = "token",(gogoproto.moretags) = 'form:"token"'];
    string type = 2 [(gogoproto.jsontag) = "type",(gogoproto.moretags) = 'form:"type"'];
    string email = 3 [(gogoproto.jsontag) = "email",(gogoproto.moretags) = 'form:"email"'];
    string code = 4 [(gogoproto.jsontag) = "code",(gogoproto.moretags) = 'form:"code"'];
    string ip = 5 [(gogoproto.jsontag) = "ip",(gogoproto.moretags) = 'form:"ip"'];
}