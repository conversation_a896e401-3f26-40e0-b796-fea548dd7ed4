syntax = "proto3";

package collection.splash.v1;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
// import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/collection.splash/service;api";
option java_package = "com.bapis.collection.splash.service";
option java_multiple_files = true;
option (wdcli.appid) = "collection.splash";

service CollectionSplash {
  // 新增典藏闪屏配置，返回新增配置对应的闪屏id
  rpc AddSplash(AddSplashReq) returns (SetSplashReply);
  // 修改典藏闪屏配置，未修改的字段依旧需传递原值，修改成功返回id:1
  rpc UpdateSplash(UpdateSplashReq) returns (SetSplashReply);
  // 删除典藏闪屏配置，需传递闪屏id，成功返回id:1
  rpc DeleteSplash(SplashReq) returns (SetSplashReply);
  // 查询单个典藏闪屏配置，需传递闪屏id
  rpc Splash(SplashReq) returns (SplashReply);
  // 查询所有在线的典藏闪屏配置，已删除的典藏闪屏不会返回
  rpc SplashList(.google.protobuf.Empty) returns (SplashListReply);
}

message AddSplashReq{
  string img_name = 1       [(gogoproto.moretags) = 'form:"img_name"'];
  string img_url_normal = 2 [(gogoproto.moretags) = 'form:"img_url_normal"'];
  string img_url_full = 3   [(gogoproto.moretags) = 'form:"img_url_full"'];
  string img_url_pad = 4    [(gogoproto.moretags) = 'form:"img_url_pad"'];
}

message UpdateSplashReq{
  int64 id = 1              [(gogoproto.moretags) = 'form:"id" validate:"required"'];
  string img_name = 2       [(gogoproto.moretags) = 'form:"img_name"'];
  string img_url_normal = 3 [(gogoproto.moretags) = 'form:"img_url_normal"'];
  string img_url_full = 4   [(gogoproto.moretags) = 'form:"img_url_full"'];
  string img_url_pad = 5    [(gogoproto.moretags) = 'form:"img_url_pad"'];
}

message SplashReq{
  int64 id = 1 [(gogoproto.moretags) = 'form:"id" validate:"required"'];
}

message SetSplashReply{
  int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
}

message SplashReply{
  Splash splash = 1 [(gogoproto.jsontag) = "splash", json_name = "splash"];
}

message SplashListReply{
  repeated Splash list = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}

message Splash{
  int64 id = 1              [(gogoproto.jsontag) = "id", json_name = "id"];
  string img_name = 2       [(gogoproto.jsontag) = "img_name", json_name = "img_name"];
  int64 mode = 3            [(gogoproto.jsontag) = "mode", json_name = "mode"];
  string img_url = 4        [(gogoproto.jsontag) = "img_url", json_name = "img_url"];
  string img_url_normal = 5 [(gogoproto.jsontag) = "img_url_normal", json_name = "img_url_normal"];
  string img_url_full = 6   [(gogoproto.jsontag) = "img_url_full", json_name = "img_url_full"];
  string img_url_pad = 7    [(gogoproto.jsontag) = "img_url_pad", json_name = "img_url_pad"];
  int64 logo_hide = 8       [(gogoproto.jsontag) = "logo_hide", json_name = "logo_hide"];
  int64 logo_mode = 9       [(gogoproto.jsontag) = "logo_mode", json_name = "logo_mode"];
  string logo_img_url = 10  [(gogoproto.jsontag) = "logo_img_url", json_name = "logo_img_url"];
  int64 ctime = 11          [(gogoproto.jsontag) = "ctime", json_name = "ctime"];
  int64 mtime = 12          [(gogoproto.jsontag) = "mtime", json_name = "mtime"];
  bool is_deleted = 13     [(gogoproto.jsontag) = "is_deleted", json_name = "is_deleted"];
}