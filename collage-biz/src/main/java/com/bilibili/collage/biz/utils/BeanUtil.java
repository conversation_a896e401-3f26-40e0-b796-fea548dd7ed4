package com.bilibili.collage.biz.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bilibili.utils.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.*;

public class BeanUtil {

    private static Logger logger = LoggerFactory.getLogger(BeanUtil.class);

    public static JSONObject jsonRemoveParams(Object ob, String removeParams){
        JSONObject jsonObject;
        if(ob instanceof JSONObject){
            jsonObject = (JSONObject) ob;
        } else {
            jsonObject = JSONUtil.parseObj(ob);
        }

        // 获取所有一级Key
        Set<String> keySet = jsonObject.keySet();
        if(keySet.contains(removeParams)){
            jsonObject.remove(removeParams);
            keySet.remove(removeParams);
        }
        // 通过一级Key遍历
        for (String key : keySet) {
            try{
                Object object = jsonObject.get(key);
                if(object == null){
                    continue;
                }
                if(object instanceof JSONArray){
                    JSONArray jsonArray = (JSONArray) jsonObject.get(key);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        Object o1 = jsonArray.get(i);
                        if(o1 == null){
                            continue;
                        }
                        if(o1 instanceof JSONArray || o1 instanceof JSONObject){
                            jsonRemoveParams(jsonArray.get(i), removeParams);
                        }
                    }
                } else if(object instanceof JSONObject){
                    jsonRemoveParams(object, removeParams);
                } else {
                    continue;
                }
            }catch (Exception ex){
                System.out.println(ex);
            }
        }
        return jsonObject;
    }

    public static <T> Map<String, Object> compare(T obj1, T obj2) {

        Map<String, Object> result = new HashMap<>();
        try {
            Field[] fs = obj1.getClass().getDeclaredFields();
            for (Field f : fs) {
                f.setAccessible(true);
                Object v1 = f.get(obj1);
                Object v2 = f.get(obj2);
                if (!Objects.equals(v1, v2)) {
                    result.put(f.getName(), v2);
                }
            }
        }catch (Exception e){
            logger.error("BeanUtil.compare error",e);
            return result;
        }
        return result;
    }

    public static void setProperty(Object obj,Map<String,Object> pros){
        Class c = obj.getClass();
        pros.forEach((propertyName,value)->{
            try {
                Field f = c.getDeclaredField(propertyName);
                f.setAccessible(true);
                f.set(obj, value);
            }catch (Exception e){
                logger.error("BeanUtil.setProperty error=",e);
            }
        });

    }
}
