package com.bilibili.collage.biz.dao;

import com.bilibili.collage.biz.po.MyFavouritePo;
import com.bilibili.collage.biz.po.MyFavouritePoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MyFavouriteDao {
    long countByExample(MyFavouritePoExample example);

    int deleteByExample(MyFavouritePoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MyFavouritePo record);

    int insertSelective(MyFavouritePo record);

    List<MyFavouritePo> selectByExample(MyFavouritePoExample example);

    MyFavouritePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MyFavouritePo record, @Param("example") MyFavouritePoExample example);

    int updateByExample(@Param("record") MyFavouritePo record, @Param("example") MyFavouritePoExample example);

    int updateByPrimaryKeySelective(MyFavouritePo record);

    int updateByPrimaryKey(MyFavouritePo record);
}