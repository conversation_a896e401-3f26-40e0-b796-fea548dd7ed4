package com.bilibili.collage.biz.service.school.doctree.model;

import com.biz.common.doc.tree.common.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/6
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SanlianDocTreeNode extends SanlianDocNode implements SnakeCaseBody {


    @ApiModelProperty("子节点")
    private List<SanlianDocTreeNode> children;


    public void setTreeTypeRecursive(SanlianSchoolTreeType treeType) {
        this.setTreeType(treeType);
        if (children != null) {
            children.forEach(child -> child.setTreeTypeRecursive(treeType));
        }
    }
}
