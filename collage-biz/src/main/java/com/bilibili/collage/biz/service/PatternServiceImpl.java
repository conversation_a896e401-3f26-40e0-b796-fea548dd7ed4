package com.bilibili.collage.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.collage.api.dto.*;
import com.bilibili.collage.api.service.*;
import com.bilibili.collage.biz.dao.*;
import com.bilibili.collage.biz.dao.ext.ExtMgkCollageLayerDao;
import com.bilibili.collage.biz.po.*;
import com.bilibili.collage.biz.render.PatternRender;
import com.bilibili.mgk.platform.common.*;
import com.google.common.base.Strings;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/19
 * 模版
 **/
@Service
@Slf4j
public class PatternServiceImpl implements IPatternService {

    @Autowired
    private CollageValidService collageValidService;

    @Autowired
    private PatternRender patternRender;

    @Autowired
    private ICollageService collageService;

    @Autowired
    private MgkCollagePatternDao mgkCollagePatternDao;

    @Autowired
    private MgkCollageLayerDao mgkCollageLayerDao;

    @Autowired
    private ExtMgkCollageLayerDao extMgkCollageLayerDao;

    @Autowired
    private IFontLibraryService fontLibraryService;

    @Autowired
    private IPatternTagService patternTagService;

    @Autowired
    private MgkCollageSizeDao mgkCollageSizeDao;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private ICollageSizeService collageSizeService;

    @Autowired
    private MgkCollageLayerStrokeDao mgkCollageLayerStrokeDao;


    @Override
    public PageResult<PatternDto> queryPatternByPage(QueryCollagePatternDto queryParam) {
        MgkCollagePatternPoExample example = getMgkCollagePatternPoExample(queryParam);
        Long total = mgkCollagePatternDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }
        List<PatternDto> patternList = queryPattern(queryParam);
        if (CollectionUtils.isEmpty(patternList)) {
            return PageResult.emptyPageResult();
        }
        return PageResult.<PatternDto>builder()
                .total(total.intValue())
                .records(patternList)
                .build();
    }

    @Override
    public List<PatternDto> queryPattern(QueryCollagePatternDto queryParam) {

        List<PatternDto> patternList = queryPatternBaseInfo(queryParam);
        List<Integer> patternIds = patternList.stream().map(PatternDto::getId).collect(Collectors.toList());
        // 获取图层
        Map<Integer, List<LayerDto>> layerMap = this.getLayerListByPatternIds(patternIds);
        patternList.forEach(item -> {
            List<LayerDto> layers = layerMap.get(item.getId());
            layers.sort(Comparator.comparingInt(LayerDto::getSeq));
            item.setLayerDtos(layers);
        });
        return patternList;
    }

    @Override
    public List<PatternDto> queryPatternBaseInfo(QueryCollagePatternDto queryParam) {

        MgkCollagePatternPoExample example = getMgkCollagePatternPoExample(queryParam);
        List<MgkCollagePatternPo> pos = mgkCollagePatternDao.selectByExample(example);
        return pos.stream().map(this::convertPatternPo).collect(Collectors.toList());
    }

    @Override
    public PageResult<PatternDto> queryPatternBaseInfoByPage(QueryCollagePatternDto queryParam) {

        MgkCollagePatternPoExample example = getMgkCollagePatternPoExample(queryParam);
        Long total = mgkCollagePatternDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }
        List<PatternDto> patternList = queryPatternBaseInfo(queryParam);
        if (CollectionUtils.isEmpty(patternList)) {
            return PageResult.emptyPageResult();
        }
        return PageResult.<PatternDto>builder()
                .total(total.intValue())
                .records(patternList)
                .build();
    }

    @Override
    public List<PatternDto> getPatternByName(String name) {
        Assert.notNull(name, "模版命名不能为空");
        MgkCollagePatternPoExample example = getMgkCollagePatternPoExample(QueryCollagePatternDto.builder().name(name).build());
        List<MgkCollagePatternPo> pos = mgkCollagePatternDao.selectByExample(example);
        return pos.stream().map(this::convertPatternPo).collect(Collectors.toList());
    }

    @Override
    public PatternDto getPatternWithLayerById(Integer patternId) {

        PatternDto patternDto = this.getPatternBaseInfoById(patternId);
        // 获取模版下的图层
        List<LayerDto> layers = this.getLayerListByPatternId(patternDto.getId());
        layers.sort(Comparator.comparingInt(LayerDto::getSeq));

        // 获取图层下的字体
        List<Integer> fontIds = layers.stream().map(LayerDto::getFontFamilyId).collect(Collectors.toList());
        Map<Integer, CollageFontLibraryDto> fontMap = fontLibraryService.getFontLibraryMapByIds(fontIds);
        layers.forEach(item -> {
            CollageFontLibraryDto fontDto = fontMap.getOrDefault(item.getFontFamilyId(), CollageFontLibraryDto.builder().build());
            item.setFontFamilyUrl(fontDto.getUrl());
        });

        patternDto.setLayerDtos(layers);
        return patternDto;
    }

    @Override
    public PatternDto getPatternBaseInfoById(Integer patternId) {
        Assert.notNull(patternId, "模版id不能为空");
        MgkCollagePatternPo po = mgkCollagePatternDao.selectByPrimaryKey(patternId);
        Assert.notNull(po, "不存在id为[" + patternId + "]的模版");
        PatternDto patternDto = this.convertPatternPo(po);
        // 获取尺寸信息
        MgkCollageSizePo mgkCollageSizePo = mgkCollageSizeDao.selectByPrimaryKey(po.getCollageSizeId());
        patternDto.setWidth(mgkCollageSizePo.getWidth());
        patternDto.setHeight(mgkCollageSizePo.getHeight());
        return patternDto;
    }

    @Override
    public List<LayerDto> getLayerListByPatternId(Integer patternId) {

        return this.getLayerListByQuery(QueryCollageLayerDto.builder()
                .patternId(patternId)
                .isDeleted(IsDeleted.VALID.getCode())
                .build());
    }

    @Override
    public List<LayerDto> queryLayerList(QueryCollageLayerDto query) {
        return this.getLayerListByQuery(query);
    }

    @Override
    public Map<Integer, List<LayerDto>> getLayerListByPatternIds(List<Integer> patternIds) {

        List<LayerDto> layers = this.getLayerListByQuery(QueryCollageLayerDto.builder()
                .patternIds(patternIds)
                .isDeleted(IsDeleted.VALID.getCode())
                .build());
        return layers.stream().collect(Collectors.groupingBy(LayerDto::getPatternId));
    }

    private List<LayerDto> getLayerListByQuery(QueryCollageLayerDto query) {

        MgkCollageLayerPoExample example = getMgkCollageLayerPoExample(query);
        List<MgkCollageLayerPo> pos = mgkCollageLayerDao.selectByExample(example);
        // 字体翻译
        List<Integer> fontIds = pos.stream().map(MgkCollageLayerPo::getFontFamilyId).distinct().collect(Collectors.toList());
        Map<Integer, CollageFontLibraryDto> fontMap = fontLibraryService.getFontLibraryMapByIds(fontIds);
        List<LayerDto> layerDtos = pos.stream().map(po -> this.convertLayerPo(po, fontMap)).collect(Collectors.toList());
        // 设置图层描边
        setLayerStroke(layerDtos);
        return layerDtos;
    }

    private void setLayerStroke(List<LayerDto> layerDtos) {
        List<Integer> layerIds = layerDtos.stream().map(LayerDto::getId).collect(Collectors.toList());
        List<StrokeDto> strokeDtos = getLayerStrokeListByLayerIds(layerIds);
        Map<Integer, List<StrokeDto>> strokeMap = strokeDtos.stream().collect(Collectors.groupingBy(StrokeDto::getLayerId));

        layerDtos.forEach(layerDto -> {
            Integer layerDtoId = layerDto.getId();
            if (strokeMap.containsKey(layerDtoId)) {
                layerDto.setStrokeDtos(strokeMap.get(layerDtoId));
            }
        });
    }

    private List<StrokeDto> getLayerStrokeListByLayerIds(List<Integer> layerDtoIds) {

        if (CollectionUtils.isEmpty(layerDtoIds)) {
            return Collections.emptyList();
        }
        List<MgkCollageLayerStrokePo> pos = getLayerStrokePosByLayerIds(layerDtoIds);

        return pos.stream().map(this::convertLayerStrokePo2Dto).collect(Collectors.toList());
    }

    private StrokeDto convertLayerStrokePo2Dto(MgkCollageLayerStrokePo po) {
        StrokeDto dto = StrokeDto.builder()
                .id(po.getId())
                .width(po.getWidth())
                .layerId(po.getLayerId())
                .build();
        if (!StringUtils.isEmpty(po.getColor())) {
            dto.setColor(RgbaDto.valueOf(po.getColor()));
        }
        return dto;
    }

    private List<MgkCollageLayerStrokePo> getLayerStrokePosByLayerIds(List<Integer> layerDtoIds) {
        MgkCollageLayerStrokePoExample example = new MgkCollageLayerStrokePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andLayerIdIn(layerDtoIds);
        return mgkCollageLayerStrokeDao.selectByExample(example);
    }


    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void createPattern(Operator operator, PatternDto patternDto) {

        collageValidService.validCreatePattern(patternDto);

        Integer patternId = savePattern(operator, patternDto);

        // 图层渲染由前端完成
//        renderAsync(patternId);
    }

    private Integer savePattern(Operator operator, PatternDto patternDto) {
        MgkCollagePatternPo patternPo = new MgkCollagePatternPo();
        patternPo.setName(patternDto.getName());
        patternPo.setCollageSizeId(patternDto.getCollageSizeId());
        patternPo.setIndustryIds(StringUtil.join(patternDto.getIndustryIds(), ","));
        patternPo.setCreator(operator.getOperatorName());
        patternPo.setEdition(patternDto.getEdition());
        patternPo.setRenderImage(patternDto.getRenderImage());
        patternPo.setPatternId(snowflakeIdWorker.nextId());
        // 保存模版标签
        Integer tagId = savePatternTag(patternDto.getTagCode());
        patternPo.setTagId(tagId);
        // 保存模板的比例
        CollageSizeDto collageSizeDto = collageSizeService.getCollageSizeById(patternDto.getCollageSizeId());
        patternPo.setPatternRadio(collageSizeDto.getWidth() * 10000 / collageSizeDto.getHeight());

        mgkCollagePatternDao.insertSelective(patternPo);
        Integer patternId = patternPo.getId();

        List<MgkCollageLayerStrokePo> strokePos = new ArrayList<>();

        patternDto.getLayerDtos().forEach(layerDto -> {
            MgkCollageLayerPo layerPo = convertLayerDto(layerDto, patternId);
            mgkCollageLayerDao.insertSelective(layerPo);
            strokePos.addAll(layerDto.getStrokeDtos().stream().map(strokeDto -> convertStrokeDto2Po(strokeDto, layerPo.getId())).collect(Collectors.toList()));
        });

        if (!CollectionUtils.isEmpty(strokePos)) {
            strokePos.forEach(strokePo -> mgkCollageLayerStrokeDao.insertSelective(strokePo));
        }
        return patternId;
    }

    private MgkCollageLayerStrokePo convertStrokeDto2Po(StrokeDto strokeDto, Integer layerId) {
        return MgkCollageLayerStrokePo.builder()
                .color(strokeDto.getColor() != null ? strokeDto.getColor().toRgbaExp() : "")
                .layerId(layerId)
                .width(strokeDto.getWidth())
                .build();
    }

    private Integer savePatternTag(String tagCode) {

        if (StringUtils.isEmpty(tagCode)) {
            return null;
        }
        PatternTagDto tagDto = patternTagService.getPatternTagByCode(tagCode);
        if (tagDto != null) {
            return tagDto.getId();
        }
        return patternTagService.createPatternTag(tagCode);
    }

    public void updateRenderUrl(Integer patternId, String rendered) {

        MgkCollagePatternPo patternPo = new MgkCollagePatternPo();
        patternPo.setId(patternId);
        patternPo.setRenderImage(collageService.replaceHttpsProtocol(rendered));
        mgkCollagePatternDao.updateByPrimaryKeySelective(patternPo);
    }

    @Override
    public String render(PatternDto patternDto) {
        try {
            return patternRender.renderAndUploadToBfs(patternDto);
        } catch (Exception e) {
            log.error("模版渲染失败.", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public String render(List<LayerDto> layerDtos) {
        try {
            return patternRender.renderAndUploadToBfs(layerDtos);
        } catch (Exception e) {
            log.error("模版渲染失败.", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 异步渲染
     *
     * @param patternId
     */
    @Override
    public void renderAsync(Integer patternId) {
        CompletableFuture.supplyAsync(() -> render(getPatternWithLayerById(patternId).getLayerDtos()))
                .thenAccept(rendered -> updateRenderUrl(patternId, rendered));
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void editLayer(Operator operator, PatternDto patternDto) {

        collageValidService.validEditLayer(patternDto);

        updateLayer(operator, patternDto);

        updateRenderUrl(patternDto.getId(), patternDto.getRenderImage());

        // 图层渲染由前端完成
//        renderAsync(patternDto.getId());
    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void editBaseInfoPattern(Operator operator, PatternDto patternDto) {
        collageValidService.validEditBaseInfoPattern(patternDto);
        updateBaseInfoPattern(operator, patternDto);
    }

    private void updateBaseInfoPattern(Operator operator, PatternDto patternDto) {
        // 更新模版表
        MgkCollagePatternPo patternPo = new MgkCollagePatternPo();
        patternPo.setId(patternDto.getId());
        patternPo.setIndustryIds(StringUtil.join(patternDto.getIndustryIds(), ","));
        patternPo.setName(patternDto.getName());
        patternPo.setPatternCover(patternDto.getPatternCover());
        // 更新模版标签
        Integer tagId = updatePatternTag(patternDto.getTagCode());
        patternPo.setTagId(tagId);
        mgkCollagePatternDao.updateByPrimaryKeySelective(patternPo);
    }

    private Integer updatePatternTag(String tagCode) {

        if (StringUtils.isEmpty(tagCode)) {
            return 0;
        }
        PatternTagDto tagDto = patternTagService.getPatternTagByCode(tagCode);
        if (tagDto != null) {
            return tagDto.getId();
        }
        return patternTagService.createPatternTag(tagCode);
    }

    private void updateLayer(Operator operator, PatternDto patternDto) {

        // 删除此模版下所有图层
        deleteLayerByPatternId(patternDto.getId());
        // 插入或更新此模版下的图层
        patternDto.getLayerDtos().forEach(layerDto -> {
            MgkCollageLayerPo layerpo = convertLayerDto(layerDto, patternDto.getId());

            Integer layerId = layerDto.getId();

            if (Utils.isPositive(layerId)) {
                // 更新
                layerpo.setIsDeleted(IsDeleted.VALID.getCode());
                mgkCollageLayerDao.updateByPrimaryKeySelective(layerpo);
                // 更新描边
                updateStrokes(operator, layerId, layerDto.getStrokeDtos());
            } else {
                // 插入图层
                mgkCollageLayerDao.insertSelective(layerpo);
                // 插入描边
                insertStrokes(operator, layerId, layerDto.getStrokeDtos());
            }
        });
    }

    public void insertStrokes(Operator operator, Integer layerId, List<StrokeDto> strokeDtos) {
        strokeDtos.forEach(dto -> {
            dto.setLayerId(layerId);
            insertStroke(operator, dto);
        });
    }

    public void updateStrokes(Operator operator, Integer layerId, List<StrokeDto> strokeDtos) {

        // 删除此图层下所有的描边
        deleteLayerStrokesByLayerId(layerId);
        strokeDtos.forEach(dto -> {
            Integer strokeId = dto.getId();
            if (Utils.isPositive(strokeId)) {
                // 更新描边
                updateStroke(operator, dto);
            } else {
                // 插入描边
                insertStroke(operator, dto);
            }
        });
    }

    public void deleteLayerStrokesByLayerId(Integer layerId) {
        MgkCollageLayerStrokePoExample example = new MgkCollageLayerStrokePoExample();
        example.or().andLayerIdEqualTo(layerId);
        MgkCollageLayerStrokePo po = MgkCollageLayerStrokePo.builder().isDeleted(IsDeleted.DELETED.getCode()).build();
        mgkCollageLayerStrokeDao.updateByExampleSelective(po, example);
    }

    public void insertStroke(Operator operator, StrokeDto dto) {
        MgkCollageLayerStrokePo po = MgkCollageLayerStrokePo.builder().build();
        BeanUtils.copyProperties(dto, po);
        po.setColor(dto.getColor() == null ? "" : dto.getColor().toRgbaExp());
        int res = mgkCollageLayerStrokeDao.insertSelective(po);
        Assert.isTrue(Utils.isPositive(res), "插入文字描边失败");
    }

    public void updateStroke(Operator operator, StrokeDto dto) {
        MgkCollageLayerStrokePo po = MgkCollageLayerStrokePo.builder().build();
        BeanUtils.copyProperties(dto, po);
        po.setIsDeleted(IsDeleted.VALID.getCode());
        if (dto.getColor() != null) {
            po.setColor(dto.getColor().toRgbaExp());
        }
        int res = mgkCollageLayerStrokeDao.updateByPrimaryKeySelective(po);
        Assert.isTrue(Utils.isPositive(res), "更新文字描边失败");
    }

    @Override
    public void deletePattern(Integer patternId) {

    }

    @Transactional(value = "collagePlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void updateStatus(Operator operator, Integer patternId, Integer status) {
        collageValidService.validUpdatePatternStatus(patternId, status);
        MgkCollagePatternPo po = new MgkCollagePatternPo();
        po.setId(patternId);
        po.setStatus(status);
        mgkCollagePatternDao.updateByPrimaryKeySelective(po);
    }

    @Override
    public void deleteLayerByPatternId(Integer patternId) {
        MgkCollageLayerPo po = new MgkCollageLayerPo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());
        MgkCollageLayerPoExample example = getMgkCollageLayerPoExample(QueryCollageLayerDto.builder().patternId(patternId).build());
        mgkCollageLayerDao.updateByExampleSelective(po, example);
    }

    private MgkCollagePatternPoExample getMgkCollagePatternPoExample(QueryCollagePatternDto patternDto) {
        MgkCollagePatternPoExample example = new MgkCollagePatternPoExample();
        if (patternDto == null) {
            return example;
        }
        MgkCollagePatternPoExample.Criteria criteria = example.or();
        if (!Strings.isNullOrEmpty(patternDto.getName())) {
            criteria.andNameLike("%" + patternDto.getName() + "%");
        }
        ObjectUtils.setList(patternDto::getIds, criteria::andIdIn);
        ObjectUtils.setList(patternDto::getCollageSizeIds, criteria::andCollageSizeIdIn);
        ObjectUtils.setList(patternDto::getTagIds, criteria::andTagIdIn);
        ObjectUtils.setList(patternDto::getPatternRadios, criteria::andPatternRadioIn);
        ObjectUtils.setObject(patternDto::getTagId, criteria::andTagIdEqualTo);

        if (!CollectionUtils.isEmpty(patternDto.getIndustryIds())) {
            String industryIds = patternDto.getIndustryIds().stream().map(String::valueOf).collect(Collectors.joining(","));
            criteria.andIndustryIdsLike("%" + industryIds + "%");
        }
        ObjectUtils.setObject(patternDto::getStatus, criteria::andStatusEqualTo);
        ObjectUtils.setObject(patternDto::getCollageSizeId, criteria::andCollageSizeIdEqualTo);
        if (null != patternDto.getIndustryId()) {
            criteria.andIndustryIdsLike("%" + patternDto.getIndustryId() + "%");
        }

        ObjectUtils.setObject(patternDto::getEdition, criteria::andEditionEqualTo);

        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        Page page = patternDto.getPageInfo();
        if (page != null) {
            example.setLimit(page.getLimit());
            example.setOffset(page.getOffset());
        }
        example.setOrderByClause("mtime desc, id desc");
        return example;
    }

    private MgkCollageLayerPoExample getMgkCollageLayerPoExample(QueryCollageLayerDto layerDto) {
        MgkCollageLayerPoExample example = new MgkCollageLayerPoExample();
        MgkCollageLayerPoExample.Criteria criteria = example.or();
        ObjectUtils.setInteger(layerDto::getId, criteria::andIdEqualTo);
        ObjectUtils.setString(layerDto::getName, criteria::andNameEqualTo);
        ObjectUtils.setInteger(layerDto::getCategoryId, criteria::andCategoryIdEqualTo);
        ObjectUtils.setInteger(layerDto::getPatternId, criteria::andPatternIdEqualTo);
        ObjectUtils.setList(layerDto::getWorksIds, criteria::andWorksIdIn);
        ObjectUtils.setList(layerDto::getPatternIds, criteria::andPatternIdIn);
        ObjectUtils.setObject(layerDto::getIsDeleted, criteria::andIsDeletedEqualTo);
        example.setOrderByClause("mtime desc, id desc");
        return example;
    }

    private PatternDto convertPatternPo(MgkCollagePatternPo po) {

        Integer[] industryIds = Arrays.stream(po.getIndustryIds().split(","))
                .filter(s -> s.length() > 0).map(Integer::parseInt)
                .toArray(Integer[]::new);
        return PatternDto.builder()
                .id(po.getId())
                .name(po.getName())
                .collageSizeId(po.getCollageSizeId())
                .industryIds(industryIds)
                .renderImage(po.getRenderImage())
                .creator(po.getCreator())
                .status(po.getStatus())
                .tagId(po.getTagId())
                .isDeleted(po.getIsDeleted())
                .mtime(po.getMtime())
                .edition(po.getEdition())
                .patternId(po.getPatternId())
                .patternRadio(po.getPatternRadio())
                .patternCover(po.getPatternCover())
                .build();
    }

    private LayerDto convertLayerPo(MgkCollageLayerPo po, Map<Integer, CollageFontLibraryDto> fontMap) {
        CollageFontLibraryDto fontDto = fontMap.getOrDefault(po.getFontFamilyId(), CollageFontLibraryDto.builder().build());
        LayerDto layerDto = LayerDto.builder()
                .id(po.getId())
                .name(po.getName())
                .category(CollageLayerEnum.getByCode(po.getCategoryId()))
                .patternId(po.getPatternId())
                .width(po.getWidth())
                .height(po.getHeight())
                .x(po.getXAxis())
                .y(po.getYAxis())
                .rotate(po.getRotate())
                .seq(po.getSeq())
                .imageUrl(po.getImageUrl())
                .imageLock(po.getImageLock())
                .text(po.getTextVal())
                .fontFamilyId(po.getFontFamilyId())
                .fontFamilyName(fontDto.getName())
                .fontFamilyUrl(fontDto.getUrl())
                .fontSize(po.getFontSize())
                .fontStyle(po.getFontStyle())
                .textAlign(po.getTextAlign())
                .fontWeight(po.getFontWeight())
                .underline(po.getUnderline())
                .linethrough(po.getLinethrough())
                .textCross(po.getTextCross())
                .textOrient(po.getTextOrient())
                .shadowX(po.getShadowX())
                .shadowY(po.getShadowY())
                .shadowBlur(po.getShadowBlur())
                .shadowWidth(po.getShadowWidth())
                .build();
        if (po.getOpacity() != null) {
            layerDto.setOpacity(po.getOpacity().floatValue() / 100);
        }
        if (!StringUtils.isEmpty(po.getBgColor())) {
            layerDto.setBgColor(RgbaDto.valueOf(po.getBgColor()));
        }
        if (!StringUtils.isEmpty(po.getTextColor())) {
            layerDto.setTextColor(RgbaDto.valueOf(po.getTextColor()));
        }

        if (!StringUtils.isEmpty(po.getShadowColor())) {
            layerDto.setShadowColor(RgbaDto.valueOf(po.getShadowColor()));
        }

        if (po.getTextVal() != null) {
            try {
                byte[] byteTextVal = (new Base64()).decode(po.getTextVal());
                layerDto.setText(new String(byteTextVal, "utf-8"));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return layerDto;
    }

    private MgkCollageLayerPo convertLayerDto(LayerDto layer, Integer patternId) {
        MgkCollageLayerPo layerPo = new MgkCollageLayerPo();
        layerPo.setId(layer.getId());
        layerPo.setName(layer.getName());
        layerPo.setPatternId(patternId);
        layerPo.setWorksId(0);
        layerPo.setCategoryId(layer.getCategory().getCode());
        layerPo.setType(layer.getCategory().getType().getCode());
        layerPo.setXAxis(layer.getX());
        layerPo.setYAxis(layer.getY());
        layerPo.setWidth(layer.getWidth());
        layerPo.setHeight(layer.getHeight());
        layerPo.setRotate(layer.getRotate());
        Float opacity = layer.getOpacity() * 100;
        layerPo.setOpacity(opacity.intValue());
        layerPo.setSeq(layer.getSeq());
        layerPo.setBgColor(layer.getBgColor() != null ? layer.getBgColor().toRgbaExp() : "");

        layerPo.setShadowX(layer.getShadowX());
        layerPo.setShadowY(layer.getShadowY());
        layerPo.setShadowBlur(layer.getShadowBlur());
        layerPo.setShadowWidth(layer.getShadowWidth());
        layerPo.setShadowColor(layer.getShadowColor() != null ? layer.getShadowColor().toRgbaExp() : "");

        if (layer.getCategory().getType().equals(LayerTypeEnum.IMAGE)) {
            layerPo.setImageUrl(collageService.replaceHttpsProtocol(layer.getImageUrl()));
            layerPo.setImageLock(layer.getImageLock());
        }
        if (layer.getCategory().getType().equals(LayerTypeEnum.TEXT)) {
            if (layer.getText() != null) {
                try {
                    layerPo.setTextVal(Base64.encodeBase64String(layer.getText().getBytes("utf-8")));
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            }
            layerPo.setFontFamilyId(layer.getFontFamilyId());
            layerPo.setTextColor(layer.getTextColor() != null ? layer.getTextColor().toRgbaExp() : "");
            layerPo.setTextAlign(layer.getTextAlign());
            layerPo.setFontStyle(layer.getFontStyle());
            layerPo.setFontWeight(layer.getFontWeight());
            layerPo.setFontSize(layer.getFontSize());
            layerPo.setUnderline(layer.getUnderline());
            layerPo.setLinethrough(layer.getLinethrough());
            layerPo.setTextCross(layer.getTextCross());
            layerPo.setTextOrient(layer.getTextOrient());
        }
        return layerPo;
    }
}
