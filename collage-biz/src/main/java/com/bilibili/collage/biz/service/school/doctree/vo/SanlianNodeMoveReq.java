package com.bilibili.collage.biz.service.school.doctree.vo;

import com.bilibili.collage.biz.service.school.doctree.model.SanlianSchoolTreeType;
import com.biz.common.doc.tree.common.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/12/6
 */
@Data
@Accessors(chain = true)
public class SanlianNodeMoveReq implements SnakeCaseBody {

    @ApiModelProperty("目标节点树类型")
    private SanlianSchoolTreeType treeType;

    @ApiModelProperty(value = "节点id")
    private Long nodeId;

    @ApiModelProperty(value = "目标父节点id，如果移动到目标树根节点下，那么填空")
    private Long targetParentId;

    /**
     * 默认为false，移动子孙节点伴随着重建所有受影响节点的path，当前版本不支持 forceMoveNodeWithDescendants
     */
    @ApiModelProperty(value = "是否强制移动子孙节点")
    private Boolean force;


    private SanlianNodeUpdateReq withUpdateReq;

}
