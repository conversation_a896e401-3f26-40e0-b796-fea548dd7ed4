syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
// import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "big.point.skuservice";
option go_package = "buf.bilibili.co/bapis/bapis-gen/vip/bpoint.sku;api";
option (gogoproto.goproto_getters_all) = false;
option java_package = "com.bapis.vip.bpoint.sku";
option java_multiple_files = true;

package big.point.skuservice.v1;

enum StateEnum {
    All = 0;     // all
    Pre = 1;     // 预发步
    Publish = 2; // 已发布
    Off = 3;     // 下架
}

enum RankTypeEnum {
    Unknown = 0;
    Sales = 1;     // 销量
    ReviewSales = 101;  // 审核态销量
}

enum InventoryOptEnum {
     DefaultReduce = 0;     // 减少库存
     Withhold = 5;          // 预扣
     WithholdReduce = 6;    // 预扣减少库存
     WithholdBack = 7;      // 预扣返还
}

message PromotionPrice {
    int64 price = 1 [(gogoproto.jsontag) = "price", json_name = "price"];  // 折扣价
    int64 type = 2 [(gogoproto.jsontag) = "type", json_name = "type"];   // 类型  1-折扣 2-秒杀
    int64 discount = 3 [(gogoproto.jsontag) = "discount", json_name = "discount"]; // 折扣
    string label = 4 [(gogoproto.jsontag) = "label", json_name = "label"]; // 8折/秒杀
}

message SkuPrice {
    int64 origin = 1 [(gogoproto.jsontag) = "origin", json_name = "origin"]; // 原价
    PromotionPrice promotion = 2 [(gogoproto.jsontag) = "promotion", json_name = "promotion"]; // 折扣价
    int64 sale = 3 [(gogoproto.jsontag) = "sale", json_name = "sale"]; // 最低价
}

message SkuInventory {
    int64 available_num = 1 [(gogoproto.jsontag) = "available_num", json_name = "available_num"]; // 已放量库存
    int64 used_num = 2  [(gogoproto.jsontag) = "used_num", json_name = "used_num"]; // 已使用库存
    int64 surplus_num = 3 [(gogoproto.jsontag) = "surplus_num", json_name = "surplus_num"]; // 剩余库存
}

message SkuBase {
    string token = 1;
    string title = 2;
    string picture = 3 [(gogoproto.jsontag) = "picture", json_name = "picture"];
    repeated string rotation_pictures = 4 [(gogoproto.jsontag) = "rotation_pictures", json_name = "rotation_pictures"];
    SkuPrice price = 5 [(gogoproto.jsontag) = "price", json_name = "price"];
    SkuInventory inventory = 6 [(gogoproto.jsontag) = "inventory", json_name = "inventory"];
    int64 user_type = 7;  // 1-不限制 2-大会员 3-年度
    int64 exchange_limit_type = 8; // 兑换限制类型： 1 无限制 2 单用户限次 3 单月限次 4 单日限次
    int64 exchange_limit_num = 9 [(gogoproto.jsontag) = "exchange_limit_num", json_name = "exchange_limit_num"];  // 兑换限制数量
    int64 start_time = 10;
    int64 end_time = 11;
    StateEnum state = 12;
    int64 priority = 13;
}

message SkuAttribute {
    int64 id = 1;
    int64 type = 2; //  1-兑换，2-权益详情，3-货品
    string content = 3 [(gogoproto.jsontag) = "content", json_name = "content"]; // 内容，结构化字符串，需自行解析
}

message SkuRep{
     SkuBase base = 1; // 基础信息
     repeated SkuAttribute attribute = 2; // 属性信息
}

message GetSkuByTokenReq {
    string token = 1;
    bool with_attribute = 2; // 是否需要属性信息
}

message SkuMapRep{
     map<string, SkuRep> skus = 1; // 没找到的话，对应 SkuRep 为 nil，key 为 sku token
}

message GetSkusByTokensReq {
    repeated string tokens = 1;
    bool with_attribute = 2; // 是否需要属性信息
}

message BatchSkusByCategoryRep {
    repeated SkuRep skus = 1; // sku
    int64 total = 2;
}

message BatchSkusByCategoryReq {
    int64 category_id = 1;   // 为 0 时返回全部
    int64 pn = 2 [(gogoproto.moretags) = 'form:"pn"']; // 页码，从 1 开始
    int64 ps = 3 [(gogoproto.moretags) = 'form:"ps"'];  // 分页数量
    RankTypeEnum rank_type = 4;
    bool with_total = 5;
    bool with_attribute = 6;
}

message Category {
    int64 id = 1;
    string name = 2;
    StateEnum state = 3;
}

message GetCategoryReq {
    StateEnum state = 1;   // 状态
    bool in_review = 2;  // 是否 ios 审核态
}

message GetCategoryRep {
    repeated Category category = 1;
}


message GetSkuGoodsByTokenReq {
    string token = 1;
}

message GetSkuGoodsByTokenRep {
    int64 id = 1;
    int64 goods_id = 2; //某种货品的唯一 id，业务方根据 goods_id 感知对应哪种货品，进行对应货品的发货
    int64 delivery_type = 3; // 类型： 1-直冲
    map<string, string> content = 4; // 货品详情，以大会员批次举例就是 content = {"token":"1","app_key": "2"}
}

message GetMemberSkuPriceByTokenReq {
    string token = 1;
    int64 mid = 2;
}

message GetMemberSkuPriceByTokenRep {
    int64 origin = 1; // 原价
    int64 lower = 2; // 最低价
}

message ReduceSkuInventoryRep {
    SkuInventory inventory = 1;  // 消减后的库存数量
}

message ReduceSkuInventoryReq {
    string token = 1;
    int64 number = 2;      // 消减数量
    string order_no = 3;   // 幂等单号
    InventoryOptEnum opt = 4;  // 默认为 Reduce
    bool with_info = 5;        // 是否需要操作后的库存信息（所有库存，已使用库存，剩余库存），默认为否
    string remark = 6;         // 备注，操作原因
}

service BigPointSku {
  // 获取某个分类下的 sku 列表
  rpc GetSkusByCategory(BatchSkusByCategoryReq) returns (BatchSkusByCategoryRep);
  // 获取 sku 分类
  rpc GetCategory(GetCategoryReq) returns (GetCategoryRep);
  // 批量 sku 信息
  rpc GetSkusByTokens(GetSkusByTokensReq) returns (SkuMapRep);
}