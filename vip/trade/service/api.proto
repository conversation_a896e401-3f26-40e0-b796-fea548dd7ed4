// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
//import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";
package vip.trade.service.v1;

option (wdcli.appid) = "ogv.vip-trade.vip-trade-service";
option go_package = "buf.bilibili.co/bapis/bapis-gen/vip/trade.service;v1";
option java_package = "com.bapis.vip.trade.service";
option java_multiple_files = true;

service Trade {
    // 价格
    rpc PricePanel(PricePanelReq) returns (PricePanelResp);
    rpc PriceInfoByPIDList(PriceInfoByPIDListReq) returns (PriceInfoByPIDListResp);
    rpc OgvPanel(OgvPanelReq) returns (OgvPanelResp);   // 加更礼面板

    // 下单
    rpc OrderCreateLexi (OrderCreateLexiReq) returns (OrderCreateReply);  // lexi 下单
    rpc OrderCreateActivity (OrderCreateActivityReq) returns (OrderCreateReply);  // 活动 下单
    rpc OrderCreateRenew (OrderCreateRenewReq) returns (OrderCreateReply); // 创建续费订单
    rpc CreateOrderTokenLexi (CreateOrderTokenLexiReq) returns (OrderCreateTokenReply); // lexi 面板 pc 订单二维码token.
    rpc CreateOrderToken (CreateOrderTokenReq) returns (OrderCreateTokenReply); // 老面板 pc 订单二维码token.
    rpc OrderTokenPay (OrderTokenPayReq) returns (OrderTokenPayReply);     // pc token 扫码创建订单，询价下单.
    rpc OrderClose (OrderCloseReq) returns (OrderCloseReply);

    // 纯签约
    rpc ActSignInfo(ActSignInfoReq) returns (ActSignInfoResp);
    rpc OrderOnlySign(OrderOnlySignReq) returns (OrderOnlySignResp);

    // 查单
    rpc OrderTokenInfo (OrderTokenInfoReq) returns (OrderTokenInfoReply); // 二维码信息
    rpc UserPayOrder (UserPayOrderReq) returns (UserPayOrderResp);  // 用户支付成功订单
    rpc QueryOrderByNo (QueryOrderByNoReq) returns (QueryOrderByNoReply);

    // 回调
    rpc PayOrderNotify (PayOrderNotifyReq) returns (.google.protobuf.Empty); // 支付回调
    rpc ClearOrderCache(ClearOrderCacheReq) returns (ClearOrderCacheReply); // 清理缓存
    // 面板缓存
    rpc CachePanelOrderSnapInfo(CachePanelOrderSnapInfoReq) returns (CachePanelOrderSnapInfoReply); // 面板快照

    // ios 降级支付，ios 避税使用其他支付渠道
    rpc DegradePayOrderInfo (DegradePayOrderInfoReq) returns (DegradePayOrderInfoReply); // 获取降级支付信息
    rpc DegradePayOrderParam (DegradePayOrderParamReq) returns (DegradePayOrderParamReply); // 生成降级支付订单参数

    // 用户支付相关
    rpc UserBpInfo(UserBpInfoReq) returns (UserBpInfoReply);
    rpc UserCanBuyIapSub (UserCanBuyIapSubReq) returns (UserCanBuyIapSubReply);

    // 搭售商品
    rpc GenerateOrderBuyGift (GenerateOrderBuyGiftReq) returns (.google.protobuf.Empty);    // 生成搭售商品
    rpc GetOrderBuyGift (GetOrderBuyGiftReq) returns (GetOrderBuyGiftReply);   // 获取订单的赠品
    rpc ProcessOrderSuccessProducts (ProcessOrderSuccessProductsReq) returns (ProcessOrderSuccessProductsReply); // 处理并获取订单的赠品
    rpc CancelProduceOrderProducts (CancelProduceOrderProductsReq) returns (.google.protobuf.Empty);         // 取消订单赠品的发放

    // 群组处理
    rpc ProcessOrderSuccessGroup (ProcessOrderSuccessGroupReq) returns (ProcessOrderSuccessGroupReply);     // 订单成功，群组处理

    // 折扣相关
    rpc GetUserGoodsidCount(GetUserGoodsidCountReq) returns (GetUserGoodsidCountReply);   // 支付宝立减优惠
    rpc AutoRenewPriceUse (AutoRenewPriceUseReq) returns (google.protobuf.Empty);  // 使用大会员代扣折扣
}

enum PricePanelEnum {
    RecommendPanel = 0; // 推荐面板（默认
    OrdinaryPanel = 1;  // 普通面板
    UpgradePanel = 2;   // 超大升级面板（请求面板中包含超大升级价格才下发
}

enum IrrSkuAdsScene {
    Panel = 0;
    UnionVip = 1;
}

enum SubTypeEnum {
    NoSub = 0;
    Sub = 1;
}


enum PriceUnitEnum {
    PriceUnitUnknown = 0;
    PriceUnitMonth = 1;
    PriceUnitDay = 2;
}

enum ProductTypeEnum {
    ProductTypeUnknown = 0;
    ProductTypeVip = 1; // 大会员
    ProductTypeTv = 2; // 四端会员
}

enum OrderStatusEnum {
    OrderStatusUnknown = 0;
    OrderStatusWaiting = 1; // 待支付
    OrderStatusSuccess = 2; // 支付成功
    OrderStatusFail = 3; // 支付失败
}

message PricePanelReq {
    repeated PricePanelEnum panels = 1;
    int64 mid = 2;
    string buvid = 3;
    string platform = 4;
    string mobi_app = 5;
    string device = 6;
    string panel_type = 7;
    int64 build = 8;
    Ads ads = 9;
    Ctrl ctrl = 10;
    message Ads {
        int64 tips_id = 1;
        string valid_channel = 2;
        string cashier_scene = 3;
        int64 season_id = 4;               // 单片id
        int32 season_type = 5;             // 单片类型
        int32 season_status = 6;           // 单片付费状态
        bool season_support_discount = 7;  // 单片付费是否支持折扣
    }
    message Ctrl {
        bool disable_upgrade = 1;          // 禁用升级超大价格
        bool disable_algo = 2;             // 禁用算法价格
    }
}

message PricePanelResp {
    PricePanelEnum selected = 1;            // 选中哪个套餐列表
    repeated VipPrice recommend_panel = 2;  // 推荐套餐
    repeated VipPrice ordinary_panel = 3;   // 普通套餐
    repeated VipPrice upgrade_panel = 4;    // 升级超大套餐
    string switch_ordinary_text = 5;        // 切换普通套餐文案
    string switch_recommend_text = 6;       // 切换推荐套餐文案
}

message VipPrice {
    int64 pid = 1;                          // 价格id
    string product_name = 2;                // 产品名称
    string product_id = 3;                  // 产品id
    int32 product_type = 4;                 // 产品类型，1-大会员；2-tv会员
    int32 sub_type = 5;                     // 是否自动续费，0-否；1-是
    int32 sku_type = 6;                     // 商品类型，0-普通；1-异形sku
    int32 unit_num = 7;                     // 售卖数量
    int32 unit = 8;                         // 售卖单位
    string subscript_text = 9;              // 角标文案
    string remark = 10;                     // 备注
    double publish_price = 11;              // 刊例价
    double sell_price = 12;                 // 售卖价
    int32 currency = 13;                    // 货币类型
    int32 selected = 14;                    // 是否默认选中
    int32 sort = 15;                        // 序号
    int64 mtime = 16;                       // 配置修改时间
    int32 user_config_type = 17;            // 用户类型
    string strategy_id = 18;                // 智能定价策略id
    int64 tips_id = 19;                     // 投放创意id
    map<string, string> track_params = 20;  // 投放跟踪参数
    Discount discount = 21;                 // 折扣信息
    AlipayDiscount alipay_discount = 22;    // 支付宝立减优惠
    AdsMaterial ads_material = 23;          // 投放定制物料
    Irregular irregular = 24;               // 异形sku数据
    BuyGift buy_gift = 25;                  // 买赠数据
    AddPurchase add_purchase = 26;          // 零元购/加价购数据
    SeasonGift season_gift = 27;            // 赠单片
    FastTrackAndExtraReleaseGift fast_track_and_extra_release_gift = 28;    // 超前点播&加更礼包
    message Discount {
        int64 discount_id = 1;              // 折扣id
        int32 discount_type = 2;            // 折扣类型
        string product_id = 3;              // 产品id
        string subscript_text = 4;          // 角标文案
        double discount_price = 5;          // 折扣价
        string remark = 6;                  // 备注
        int64 expire = 7;                   // 过期时间戳
        string token = 8;                   // 红包token
    }
    message AlipayDiscount {
        string goods_id = 1;
        int64 tips_id = 2;                  // 创意 id
    }
    message AdsMaterial {
        string superscript_text = 1;
    }
    message Irregular {
        repeated int64 ids = 1;
        string token = 2;
        string product_image = 3;
        repeated Meta meta_list = 4;
        message Meta {
            string token = 1;
            string title = 2;
            string sub_title = 3;
            string picture = 4;
            string goods_type = 5;
            double origin_price = 6;
            double sale_price = 7;
        }
    }
    message BuyGift {
        string tip_note_title = 1;
        string tip_note_image = 2;
        string product_image = 3;
        int64 task_id = 4;
        int32 commodity_type = 5;
        string commodity_id = 6;
        string benefit_name = 7;
        string benefit_image = 8;
        string benefit_link = 9;
    }
    message AddPurchase {
        int32 style = 1;
        repeated Meta meta_list = 2;
        message Meta {
            string token = 1;
            string title = 2;
            string sub_title = 3;
            string picture = 4;
            string goods_type = 5;
            double origin_price = 6;
            double sale_price = 7;
            string label_text = 8;
            int64 priority = 9;
            int64 pay_discount_id = 10;
        }
    }
    message SeasonGift {
        int64 max_season_price = 1;
    }
    message FastTrackAndExtraReleaseGift {
        string token = 1;
        string title = 2;
        string subtitle = 3;
        string vip_icon = 4;
        string vip_title = 5;
        string vip_subtitle = 6;
        repeated Meta meta_list = 7;
        message Meta {
            string icon = 1;
            string title = 2;
            string subtitle = 3;
            string detail_text = 4;
            string detail_url = 5;
        }
    }
}

message OgvPanelReq {
    int64 mid = 1;
    string buvid = 2;
    string platform = 3;
    string mobi_app = 4;
    string device = 5;
    int64 build = 6;
    Ads ads = 7;
    message Ads {
        int64 tips_id = 1;
        string valid_channel = 2;
        string cashier_scene = 3;
        int64 season_id = 4;
    }
}

message OgvPanelResp {
    repeated VipPrice panel_list = 1;
}

// 生成买赠记录
message GenerateOrderBuyGiftReq {
    int64 mid = 1;
    int64 task_id = 2;
    int64 tips_id = 3;
    string order_no = 4;
    int64 pid = 5;
    int64 commodity_type = 6;
    string commodity_id = 7;
    string extra = 8;
    IrrSkuAdsScene irr_ads_scene = 9;
}

message ProcessOrderSuccessProductsReq {
    string order_no = 1;
    repeated int64 commodity_type = 2;
}

message ProcessOrderSuccessProductsReply {
   repeated OrderBuyGift buy_gift_list = 1;
}

message OrderBuyGift {
    int64 tips_id = 1;
    int64 task_id = 2;
    int64 commodity_type = 3;
    string commodity_id = 4;
    string extra = 5;
    int64 delivery_status = 6;
}

message OrderCreateLexiReq {
    int64 mid = 1;
    string scene = 2;
    string snap_token = 3;
    string de_snap_token = 4;

    string platform = 5;
    string mobi_app = 6;
    string device = 7;
    string buvid = 8;
    int64 build = 9;
    string IP = 10;
    string user_agent = 11;
    int64 app_id = 12;
    string app_sub_id = 13;
    int64 pay_tips_id = 14;

    int32 dtype = 15;
    string pay_channel = 16;
    string pay_channel_id = 17;
    string real_channel = 18;
    int64 ios_code_support_type = 19;
    int64 merge_pay_and_sign = 20;
    string return_url = 21;

    int64 pid = 22;
    int64 discount_id = 23;
    CreateOrderPriceReq price = 24;
    int64 order_type=25;
    string alipay_goods_id = 26;
    string coupon_token = 27;
    string discount_token = 28;
    OrderCreateLexiBuyGiftReq buy_gift = 29;
}

message OrderCreateLexiBuyGiftReq {
    string buy_gift_type = 1;
    string buy_gift_id = 2;
    int64  buy_give_season = 3;
    string tying_sale_type = 4;
    string tying_sale_id = 5;
}

message CreateOrderPriceReq{
    string panel_type = 2;
    PriceUnitEnum unit = 3;
    int64 unit_num = 4;
    int32 order_type = 5;
    int64 product_type = 6;
    int64 group_id = 7;
}

message CreateOrderTokenLexiReq{
    OrderCreateLexiReq param = 1; // 请求
    string report_info = 2; // 埋点信息,json
}

message OrderCreateActivityReq {
    OrderCreateLexiReq param = 1;
    string act_token = 2;
    string act_params = 3;
    string third_account_encrypt = 4;
    int32 third_account_type = 5;
    bool allow_degrade_payway = 6;
}

message CreateOrderTokenReq{
    OrderCreateLexiReq param = 1;
    string report_info = 2; // 埋点信息,json
}

message OrderCreateRenewReq {
    int64 mid = 1;
    CreateOrderPriceReq price = 2;
    string platform = 3;
    int64 app_id = 4;
    string app_sub_id = 5;
    int64 dtype = 6;
    string plan_id = 7;
    string pay_contract_no = 8;
    int64 auto_renew_type = 9;
    int64 pay_type = 10;
    string sign_order_no = 11;
}

// 通过token获取二维码
message OrderCreateTokenReply {
    int64 expire_at = 1 [(gogoproto.jsontag) = "expire_at"]; // 过期时间
    string url = 2 [(gogoproto.jsontag) = "url"]; // 地址
    string token = 3 [(gogoproto.jsontag) = "token"]; // token
    double money = 4 [(gogoproto.jsontag) = "money"]; // 订单价格
}

// 通过token进行真正的下单请求
message OrderTokenPayReq {
    string token = 1 [(gogoproto.moretags) = 'form:"token" validate:"required"']; // 订单的token，在订单支付流程中，与orderNo值一样
}

// 通过token进行真正的下单返回
message OrderTokenPayReply {
    int64 expire_at = 1 [(gogoproto.jsontag) = "expire_at"]; // 过期时间
    double payment_money = 2 [(gogoproto.jsontag) = "payment_money"]; // 支付金额
    string bpay_param = 3 [(gogoproto.jsontag) = "bpay_param"]; // 支付参数
    string order_no = 4 [(gogoproto.jsontag) = "order_no"]; // 订单号
    string order_title = 5 [(gogoproto.jsontag) = "order_title"]; // 标题
    string order_desc = 6 [(gogoproto.jsontag) = "order_desc"]; // 简介
}


message OrderCreateReply {
    string pay_param = 1;
    double dprice = 2;
    double oprice = 3;
    double coupon_money = 4;
    OrderCreateDegrade degrade_pay_info = 5;
    string order_no = 6;
}

message OrderCreateDegrade {
    string degrade_pay_way = 1  [(gogoproto.jsontag) = "degradePayWay"];
    string degrade_pay_link = 2  [(gogoproto.jsontag) = "degradePayLink"];
    string order_id = 3 [(gogoproto.jsontag) = "orderId"];
}

message OrderCloseReq {
    string order_no = 1;
}

message OrderCloseReply {
    string result = 1;
}

message ActSignInfoReq {
    int64 mid = 1;
    repeated string act_token_list = 2;
}

message ActSignInfoResp {
    repeated ActSignGift sign_gift_list = 1;
    message ActSignGift {
        string act_token = 1;
        int32 product_type = 2;
        int64 months = 3;
        string gift_type = 4;
        int32 gift_num = 5;
        string gift_unit = 6;
        string gift_name = 7;
    }
}

message OrderOnlySignReq {
    int32 sign_scene = 1;
    int64 mid = 2;
    string ip = 3;
    string platform = 4;
    string mobi_app = 5;
    string device = 6;
    int64 build = 7;
    int64 app_id = 8;
    int32 dtype = 9;
    string return_url = 10;
    string pay_channel = 11;
    string pay_channel_id = 12;
    string real_channel = 13;
    int64 pid = 14;
    int64 discount_id = 15;
    string act_token = 16;
    string panel_type = 17;
    int32 product_type = 18;
    int64 months = 19;
    string snap_token = 20;
    SignGift sign_gift = 21;
    message SignGift {
        string gift_type = 1;
        int32 deduction = 2;
        int32 days = 3;
        int32 propId = 4;
    }
}

message OrderOnlySignResp {
    string order_no = 1;
    string pay_param = 2;
}

message CachePanelOrderSnapInfoReq {
    int64 mid = 1;
    string token = 2;
    string content = 3;
    string degrade_content = 4;
}

message CachePanelOrderSnapInfoReply{
}


message DegradePayOrderInfoReq {
  int64 mid = 1;
  string order_no = 2;
}

message DegradePayOrderInfoReply {
    int64 expire_time = 1;
    string product_name = 2;
    double price = 3;
    string channel_key = 4;
    int64 pid = 5;
    int64 discount_id = 6;
}

message DegradePayOrderParamReq {
  int64 mid = 1;
  string order_no = 2;
  string pay_channel = 3;
  string pay_channel_id = 4;
  string real_channel = 5;
  int64 merge_pay_and_sign = 6;
}

message DegradePayOrderParamReply {
  string pay_param = 1;
}


message GetUserGoodsidCountReq {
    int64 mid = 1;
}

message GetUserGoodsidCountReply {
    repeated GetUserGoodsidCountInfo list = 1;
}

message GetUserGoodsidCountInfo {
    int64 count = 1;
    string goodsid = 2;
}

message UserBpInfoReq {
  int64 mid = 1;
  string platform = 2;
  string mobi_app = 3;
  string device = 4;
}

message UserBpInfoReply {
  double available_bp = 1;
}

message UserPayOrderReq {
    int64 mid = 1;
    int64 stime = 2;
}

message UserPayOrderResp {
   bool has_pay_order = 1;
}

message PayOrderNotifyReq {
    // 由于json中的值就是number，如果用string来接就会报错，本结构中所有的奇怪的类型接收全部是因为这个原因（例如uid）
    int64  tx_id            = 1 [(gogoproto.jsontag) = "TxId"];
    // SUCCESS FAIL
    string  pay_status      = 2 [(gogoproto.jsontag) = "PayStatus"];
    // 支付渠道，alipay(支付宝)、open_alipay(支付宝2.0)、ali_global（支付宝跨境）、wechat(微信) ,wx_global(微信跨境) ,paypal(paypal), iap(In App Purchase)、qpay(QQ支付)、huabei(花呗支付)、ali_bank（网银支付）、bocom（交行信用卡支付）、bp（B币支付）、ott(云视听小电视支付)、ali_withhold(支付宝代扣)、ali_period_withhold(支付宝周期性代扣)、wechat_score（微信信用分）、ali_huabei(花呗)、ali_score(支付宝预授权) 、cmbPay(招行一网通支付)
    string  pay_channel     = 3 [(gogoproto.jsontag) = "PayChannel"];
    // 支付渠道id, 用户实际选择的支付实体渠道。(payChannel 代表笼统的微信、支付宝等第三方渠道， payChannelId 代表实际签约的实体渠道 id)
    int64   pay_channel_id  = 4 [(gogoproto.jsontag) = "PayChannelId"];
    // 实际支付金额（如果是虚拟币，则乘以100），单位：分
    int64   pay_amount      = 5 [(gogoproto.jsontag) = "PayAmount"];
    string  order_id        = 6 [(gogoproto.jsontag) = "OrderId"];
    int64   customer_id     = 7 [(gogoproto.jsontag) = "CustomerId"];
    // IAP代扣过期时间，毫秒值，重复通知返回的expiredTime是一样的
    int64   expired_time    = 8 [(gogoproto.jsontag) = "ExpiredTime"];
    string  pay_msg_content = 9 [(gogoproto.jsontag) = "payMsgContent"];
    string  order_pay_time  = 10 [(gogoproto.jsontag) = "orderPayTime"];
    PayMsgContent   pay_msg_content_object = 11;
}

message PayMsgContent {
    string product_id              = 1 [(gogoproto.jsontag) = "productId"];
    // 苹果自动续费的originalTransactionId
    string original_transaction_id = 2 [(gogoproto.jsontag) = "originalTransactionId"];
    // false 是否是免费试用(Boolean), true：表示是免费试用，false:表示不是免费试用
    bool   iap_trial_period        = 3 [(gogoproto.jsontag) = "iapTrialPeriod"];
    // 第三方优惠金额（单位 分）
    string third_coupon_amount     = 4 [(gogoproto.jsontag) = "thirdCouponAmount"];
    // 支付失败原因
    string fail_reason             = 5 [(gogoproto.jsontag) = "failReason"];
    string uid                     = 6 [(gogoproto.jsontag) = "uid"];
    string channel_discount_product = 7 [(gogoproto.jsontag) = "channelDiscountProduct"];
}

message AutoRenewPriceUseReq {
    int64 mid = 1;
    string order_no = 2;
    int64 pid = 3;
    bool is_auto_renew_not_first = 4;
}

message ProcessOrderSuccessGroupReq {
  string order_no = 1;
}

message ProcessOrderSuccessGroupReply {
}

message GetOrderBuyGiftReq {
    string order_no = 1;
    repeated int64 commodity_types = 2;
}

message GetOrderBuyGiftReply {
    repeated OrderBuyGift buy_gift_list = 1;
}

message ClearOrderCacheReq {
    string order_no = 1;
    int64 mid = 2;
}

message ClearOrderCacheReply {

}

message QueryOrderByNoReq {
  string order_no = 1;
}

message QueryOrderByNoReply {
  string order_no = 1;
  int64 mid = 2;
  int64 app_id = 3;
  string app_sub_id = 4;
  int64 pid = 5;
  ProductTypeEnum product_type = 6; // sku 类型
  int64 days = 7;
  int64 months = 8;
  string act_token = 9; // 活动页订单会有 actoken，收银台订单无
  string panel_type = 10;
  int64 ctime = 11; // 秒级时间戳
  int64 mtime = 12; // 秒级时间戳
  SubTypeEnum sub_type = 13; // 0-非连包 1-连包
  OrderStatusEnum status = 14; // 订单状态
}

message PriceInfoByPIDListReq {
    repeated int64 list = 1;
}

message VipPriceInfo {
    int64   id = 1;
    SubTypeEnum   sub_type = 2;
    int64   unit_num = 3;
    PriceUnitEnum   unit_type = 4;
    string  panel_type = 5;
    ProductTypeEnum   product_type = 6;
    int64  currency = 7;
    int64   group_id = 8;
}

message PriceInfoByPIDListResp {
    map<int64,VipPriceInfo> price_info_map = 1;
}

message CancelProduceOrderProductsReq {
    string order_no = 1;
    int64 commodity_type = 2;
}

message UserCanBuyIapSubReq {
    int64 mid = 1 ;
    int64 group_id = 2 [(gogoproto.moretags) = 'form:"group_id"']; // 0-所有群组
}

message UserCanBuyIapSubReply {
    int64 status = 1; // 1:处于 iap 订阅期，不可购买; 2:不处于 iap 订阅期，可购买;
}

message OrderTokenInfoReq {
    string token = 1;
}

message OrderTokenInfoReply {
    bool is_trade_order = 1; // 是否交易订单流程
}
