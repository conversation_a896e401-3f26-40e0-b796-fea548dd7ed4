syntax = "proto3";

package pgc.service.season.playlist.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/pgc/service.season.playlist;v1";
option java_package = "com.bapis.pgc.service.season.playlist";
option java_multiple_files = true;

// import "github.com/gogo/protobuf/gogoproto/gogo.proto";

message QueryPlaylistsReq {
    // 片单id
    repeated int64 playlist_id = 1;
    // 渠道/端
    string channel = 2;
    // 场景/分发位
    string scenario = 3;
}

message QueryPlaylistsResp {
    repeated PlaylistPb playlist = 1;
}

message QueryPlaylistsByItemReq {
    // 条目源id
    repeated string source_id = 1;
    // 条目源类型
    string source_type = 2;
    // 渠道/端
    string channel = 3;
    // 场景/分发位
    string scenario = 4;
}

message QueryPlaylistsByItemResp {
    repeated PlaylistPb playlist = 1;
}

// 片单
message PlaylistPb {
    // 片单id
    int64 id = 1;
    // 主题色
    string color = 2;
    // 片单标题
    string title = 3;
    // 片单简介
    string summary = 4;
    // 背景图
    string bg_cover = 5;
    // 方图
    string square_cover = 6;
    // 横图
    string horizontal_cover = 7;
    // 片单条目
    repeated PlaylistItemPb item = 8;
    // 推荐up主mid
    int64 mid = 9;
    // 更多片单链接
    string more_url = 10;
    // 话题页
    string topic_link = 11;
    // 是否出现在策略推荐
    bool show_in_recommend = 12;
    // 是否为奖项片单
    bool is_award_playlist = 13;
}

// 片单条目
message PlaylistItemPb {
    // 条目源id
    string source_id = 1;
    // 条目类型
    string source_type = 2;
    // 推荐语
    string subtitle = 3;
    // 自定义封面
    string custom_cover = 4;
}