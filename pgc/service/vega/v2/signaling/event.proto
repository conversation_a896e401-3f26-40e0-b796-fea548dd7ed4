syntax = "proto3";
package pgc.service.vega.v2.signaling;

option java_multiple_files = true;
option java_package = "com.bapis.pgc.service.vega.v2.signaling";
option go_package = "buf.bilibili.co/bapis/bapis-gen/pgc/service.vega.v2.signaling;v1";

enum EventType {
  // 建连，InBoundFrame.body 对应 ConnectReq
  EVENT_TYPE_CONNECT = 0;
  // 断连，InBoundFrame.body 对应 DisconnectReq
  EVENT_TYPE_DISCONNECT = 1;
  // 订阅，InBoundFrame.body 对应 SubscribeReq
  EVENT_TYPE_SUBSCRIBE = 2;
  // 取订，InBoundFrame.body 对应 UnsubscribeReq
  EVENT_TYPE_UNSUBSCRIBE = 3;
}

message Event {
  EventType type = 1;

  string buvid = 2;
  // 可以是empty，表示未登录
  string mid = 3;

  // 发生事件时的时间戳
  int64 timestamp = 4;
}