syntax = "proto3";
package pgc.service.activity.medal.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/pgc/service.activity.medal;api";
option java_package = "com.bapis.pgc.service.activity.medal";
option java_multiple_files = true;

service NumDispatch {
    // 发号
    rpc Dispatch (DispatchReq) returns (DispatchReply);
}

// DispatchReq
message DispatchReq {
    // 业务code  https://info.bilibili.co/pages/viewpage.action?pageId=*********
    string biz_code = 1;
}

// DispatchReply
message DispatchReply {
    // 生成编号
    string number = 1;
}