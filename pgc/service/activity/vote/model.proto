syntax = "proto3";
package pgc.service.activity.vote;

option go_package = "buf.bilibili.co/bapis/bapis-gen/pgc/service.activity.vote;api";
option java_package = "com.bapis.pgc.service.activity.vote";
option java_multiple_files = true;

// 业务类型
enum BizType {
    // 声优
    VOICE_ACTOR = 0;
}

// MyStatusReq
message MyStatusReq {
    // 活动id
    int32 activity_id = 1;
    //mid
    int64 mid = 2;
    // 业务类型
    BizType biz_type = 3;
}

// MyStatusReply
message MyStatusReply {
    // 剩余投票数
    int32 votes_remain = 1;
}

// 我投票支持的选手请求
message MyVotedPlayersReq {
    // 活动id
    int32 activity_id = 1;
    //mid
    int64 mid = 2;
    // 业务类型
    BizType biz_type = 3;
}

// 我投票支持的选手响应
message MyVotedPlayersReply {
    // 投票历史
    repeated PlayerModel voted_players = 1;
}

// 选手信息
message PlayerModel {
    // 选手id
    int64 pid = 1;
    // 名字
    string name = 2;
    // 头像
    string avatar = 3;
    // 投票数
    int32 votes = 4;
    // 话题id
    int32 topic_id = 5;
    // 拼音名
    string pinyin_name = 6;
    // 选手mid
    int64 mid = 7;
    // 状态（0=正常 1=淘汰 2=待复活）
    int32 status = 8;
}

// 投票请求
message VoteReq {
    // 活动id
    int32 activity_id = 1;
    // mid
    int64 mid = 2;
    // 选手id
    int64 pid = 3;
    // 票数
    int32 votes = 4;
    // 技能
    int32 magic_id = 5;
    // 邀请者mid 不传代表不是由邀请后投票的
    int64 invitor_mid = 6;
    // 业务类型
    BizType biz_type = 7;
    // 请求上下文
    ReqContext req_context = 8;
}

// 投票结果
message VoteReply {
    // 选手id
    int64 pid = 1;
    // 名字
    string name = 2;
    // 头像
    string avatar = 3;
}

// 请求上下文
message ReqContext {
    string buvid = 1;
    string ip = 2;
    string platform = 3;
    string mobi_app = 4;
    string path = 5;
    string app_key = 6;
    string referer = 7;
    string user_agent = 8;
    string build = 9;
}

// 场景
enum Scene {
    // 未知
    LOGIN_SCENE = 0;
    // 声优
    SHARE_SCENE = 1;
    // 开通季度大会员
    OPEN_MONTH_VIP_SCENE = 2;
    // 开通年度大会员
    OPEN_YEAR_VIP_SCENE = 3;
    // 观看视频
    WATCH_VIDEO_SCENE = 4;
}

// 增加投票请求
message AddVoteReq {
    // 活动id
    int32 activity_id = 1;
    // mid
    int64 mid = 2;
    // 票数
    int32 votes = 3;
    // 业务类型（增加投票数规则会因业务而不同）
    BizType biz_type = 4;
    // 场景
    Scene scene = 5;
}

// 增加投票响应
message AddVoteReply {
    // id
    int32 id = 1;
}

// 增加魔法请求
message AddMagicReq {
    // 活动id
    int32 activity_id = 1;
    // mid
    int64 mid = 2;
    // 票数
    MagicType magic_type = 3;
    // 魔法值
    int32 magic_value = 4;
    // 业务类型（增加魔法规则会因业务而不同）
    BizType biz_type = 5;
    // 场景
    Scene scene = 6;
}

// 增加魔法响应
message AddMagicReply {
    // id
    int32 id = 1;
}

// 魔法类型
enum MagicType {
    // 未知
    UNKNOWN = 0;
    // 投票加倍卡
    VOTE_PLUS = 1;
}

// 最新选手排行榜请求
message LatestPlayerRankReq {
    // 活动id
    int32 activity_id = 1;
    // 1=周榜 2=总榜
    int32 type = 2;
    // 业务类型
    BizType biz_type = 3;
}

// 最新选手排行榜响应
message LatestPlayerRankReply {
    // 列表
    repeated PlayerModel items = 1;
    // 榜单开始时间戳
    int64 start = 2;
    // 榜单结束时间戳
    int64 end = 3;
}

// 活动选手信息请求
message ActivityPlayersReq {
    // 活动id
    int32 activity_id = 1;
    // 业务类型
    BizType biz_type = 2;
}

// 活动选手信息响应
message ActivityPlayersReply {
    // 选手信息
    repeated PlayerModel players = 1;
}

// 合并魔法请求
message CombineMagicReq {
    // 待合成的id
    repeated int32 magic_ids = 1;
    // mid
    int64 mid = 2;
    // 业务类型
    BizType biz_type = 3;
}

// 合并魔法响应
message CombineMagicReply {
    // 合成后的魔法
    MagicModel new_magic = 1;
}

// MagicModel
message MagicModel {
    // id
    int32 id = 1;
    // 类型 参见MagicType
    int32 magic_type = 2;
    // 魔法值
    int32 magic_value = 3;
}

// 我的魔法请求
message MyAvailableMagicsReq {
    // 活动id
    int32 activity_id = 1;
    // mid
    int64 mid = 2;
    // 业务类型
    BizType biz_type = 3;
}

// 我的魔法响应
message MyAvailableMagicsReply {
    // 魔法列表
    repeated MagicModel magics = 1;
}

message TaskModel {
    // 类型 参见TaskType
    int32 type = 1;
    // 任务目标
    int32 task_aim = 2;
    // 已完成任务
    int32 task_finished = 3;
    // 发生日期
    int64 happen_date = 4;
}

// 我的任务请求
message MyTasksReq {
    // 活动id
    int32 activity_id = 1;
    // mid
    int64 mid = 2;
    // 业务类型
    BizType biz_type = 3;
}

// 我的任务响应
message MyTasksReply {
    // 任务列表
    repeated TaskModel tasks = 1;
}

// 生成排行榜
message RunRankReq {
    // 活动id
    int32 activity_id = 1;
    // mid
    int64 start = 2;
    // mid
    int64 end = 3;
    // 1=周榜 2=总榜
    int32 type = 4;
    // 是否强制刷新
    bool refresh = 5;
    // 业务类型
    BizType biz_type = 6;
}

// 预置任务请求
message PresetTaskReq {
    // 活动id
    int32 activity_id = 1;
    // mid
    int64 mid = 2;
    // 业务类型
    BizType biz_type = 3;
    // 任务类型
    TaskType task_type = 4;
}

// 预置任务请求
message PresetTaskReply {
    // id
    int32 id = 1;
}

// 任务类型
enum TaskType {
    // 未知
    UNCERTAIN = 0;
    // 登录
    LOGIN = 1;
    // 分享
    SHARE = 2;
    // 观看视频
    WATCH_VIDEO = 3;
    // 连续3天投票
    CONTINUOUS_VOTE_IN_3_DAYS = 4;
    // 累计5天投票
    VOTE_FOR_5_DAYS = 5;
    // 累计7天投票
    VOTE_FOR_7_DAYS = 6;
    // 邀请投票
    INVITE_VOTE = 7;
    // 开通季卡
    OPEN_MONTH_VIP = 8;
    // 开通年卡
    OPEN_YEAR_VIP = 9;
}