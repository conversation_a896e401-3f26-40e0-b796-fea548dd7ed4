syntax = "proto3";

/*
 * v0.1.0
 * production相关枚举定义
 */
package pgc.servant.production.sub;

option go_package = "buf.bilibili.co/bapis/bapis-gen/pgc/servant.production;v1";
option java_package = "com.bapis.pgc.servant.production";
option java_multiple_files = true;

enum SubDemandType {

  //未知
  UNKNOWN = 0;

  //媒资生产
  MEDIA_PRODUCTION = 1;

  //视频介质生产
  VIDEO_PRODUCTION = 2;

  //视频介质换源
  VIDEO_CHANGE = 3;

  //策略绑定
  STRATEGY_BIND = 4;

}