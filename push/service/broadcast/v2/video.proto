syntax = "proto3";

package push.service.broadcast.v2;

option go_package = "buf.bilibili.co/bapis/bapis-gen/push/service.broadcast.v2;v2";
option java_multiple_files = true;
option java_package = "com.bapis.push.service.broadcast.v2";

// 视频在线排行，仅使用在房间 video:// 类型
service BroadcastVideoAPI {
    // 通过cid获取在线人数top N
    rpc TopOnlineByCid(TopOnlineReq) returns(TopOnlineReply);
    // 通过aid获取在线人数top N
    rpc TopOnlineByAid(TopOnlineReq) returns(TopOnlineReply);
    // 多维度统计在线人数
    rpc Online(OnlineReq) returns (OnlineReply);
}

// 视频在线
message TopOnlineReq {
    // top返回数量
    int32 limit = 1;
}
message TopOnline  {
    int64 id = 1;
    int32 count = 2;
}
message TopOnlineReply {
    repeated TopOnline items = 1;
}

message OnlineReq {
    // video, ugc, ogv
    string business = 1; // required
    int64 aid = 2; // required
    int64 cid = 3;
}

message OnlineReply {
    // video://{aid}/{cid}
    // {mobiApp}+ugc://{aid}/{cid}
    // {mobiApp}+ogv://{aid}/{cid}
    map<string, int64> rooms = 1;
    int64 total = 2;
}
