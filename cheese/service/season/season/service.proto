syntax = "proto3";

package cheese.season.service.season.v1;

import "cheese/service/season/season/model.proto";
import "google/protobuf/empty.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/cheese/service.season.season;api";
option java_package = "com.bapis.cheese.service.season.season";
option java_multiple_files = true;
option (wdcli.appid) = "cheese.season.service";

//season service
service Season {
    // 新增或更新season
    rpc Save (SaveReq) returns (SaveReply) {
        option deprecated = true;
    };
    // season分页
    rpc Page (PageReq) returns (PageReply);
    // 添加批次
    rpc AddBatch (AddBatchReq) returns (AddBatchReply) {
        option deprecated = true;
    };
    // season简略信息
    rpc Cards (SeasonCardsReq) returns (SeasonCardsReply);
    // season详细信息
    rpc Profile (SeasonProfileReq) returns (SeasonProfileReply);
    // up主上传的season
    rpc UserSeason (UserSeasonReq) returns (UserSeasonReply);
    // 批次卡
    rpc BatchCards (BatchCardsReq) returns (BatchCardsReply);
    // season编辑信息
    rpc EditInfos (SeasonEditInfosReq) returns (SeasonEditInfosReply) {
        option deprecated = true;
    };
    // 根据epid查找season卡信息
    rpc CardsByEpIds (CardsByEpIdsReq) returns (CardsByEpIdsReply);
    // 根据aid查找season卡信息
    rpc CardsByAids (CardsByAidsReq) returns (CardsByAidsReply) {
        option deprecated = true;
    };
    // 发送up助手消息
    rpc SendAssistMessage (SendAssistMessageReq) returns (SendAssistMessageReply) {
        option deprecated = true;
    };
    // 删除
    rpc Delete (DeleteReq) returns (google.protobuf.Empty) {
        option deprecated = true;
    };
    // 下架
    rpc Offline (OfflineReq) returns (google.protobuf.Empty) {
        option deprecated = true;
    };
    // 锁定
    rpc Lock (LockReq) returns (google.protobuf.Empty) {
        option deprecated = true;
    };
    // 打回
    rpc Back (BackReq) returns (google.protobuf.Empty) {
        option deprecated = true;
    };
    // 待审
    rpc WaitApprove (WaitApproveReq) returns (google.protobuf.Empty) {
        option deprecated = true;
    };
    // 获取未绑定的稿件视频
    rpc GetUnboundArcVideo (GetUnboundArcVideoReq) returns (GetUnboundArcVideoReply) {
        option deprecated = true;
    };
    // 删除未绑定的稿件视频
    rpc RemoveUnboundArcVideo (RemoveUnboundArcVideoReq) returns (google.protobuf.Empty) {
        option deprecated = true;
    };
    // 提交批次
    rpc SubmitBatch (SubmitBatchReq) returns (SubmitBatchReply) {
        option deprecated = true;
    };
    // 预约课程分页
    rpc PagePreview (PagePreviewReq) returns (PagePreviewReply);
    rpc RecentSendPreview (RecentSendPreviewRequest) returns (RecentSendPreviewResponse);
    // 预约课程信息
    rpc PreviewCards (PreviewCardsReq) returns (PreviewCardsReply);
    // 课程推荐
    rpc Recommend (RecommendReq) returns (RecommendReply);
    // 课程课件
    rpc SeasonFile (SeasonFileReq) returns (SeasonFileReply);
    // 课件下载
    rpc DownloadSeasonFile (DownloadSeasonFileReq) returns (DownloadSeasonFileReply);
    // 表单提交
    rpc SubmitCooperationForm (SubmitFormReq) returns (SubmitFormReply);
    // 可用的系列id (排除测试系列)
    rpc AvailableSeason (AvailableSeasonReq) returns (AvailableSeasonRely);
    // 系列摘要
    rpc SeasonDigest (SeasonDigestReq) returns (SeasonDigestReply) {
        option deprecated = true;
    };
    // 分类
    rpc Classifications (ClassificationsReq) returns (ClassificationsReply);
    // 系列分类关联信息
    rpc SeasonClassifications (SeasonClassificationsReq) returns (SeasonClassificationsReply) {
        option deprecated = true;
    };
    // 根据oid和otype查分类信息
    rpc ClassificationsByOidAndType (ClassificationsByOidAndTypeReq) returns (ClassificationsByOidAndTypeReply);
    // 系列索引排序值查询
    rpc SeasonIndexSort (SeasonIndexSortReq) returns (SeasonIndexSortReply);
    // 系列下第一个ep(可以观看)
    rpc SeasonFistEp (SeasonFistEpReq) returns (SeasonFistEpReply);
    // 系列时长
    rpc SeasonDuration (SeasonDurationReq) returns (SeasonDurationReply);
    // 系列天马卡片信息(人群包、天马卡片id)
    rpc SeasonCard (SeasonCardReq) returns (SeasonCardReply);
    // 系列扩展信息
    rpc SeasonExt (SeasonExtReq) returns (SeasonExtReply);
    // 系列二维码信息
    rpc SeasonQrCode (SeasonQrCodeReq) returns (SeasonQrCodeReply);
    // 系列找课
    rpc seasonSearch (SeasonSearchReq) returns (SeasonSearchReply);
    // 系列搜索，这个是真正的搜索
    rpc seasonSeek (SeasonSeekReq) returns (SeasonSeekReply);
    // 系列平台属性信息
    rpc SeasonPlatAttr (SeasonPlatAttrReq) returns (SeasonPlatAttrReply);
    // 根据分类id查询分类信息
    rpc ClassificationsById (ClassificationsByIdReq) returns (ClassificationsByIdReply);
    // 课程id查询预约课程信息
    rpc PreviewCardsBySeasonIds (PreviewCardsBySeasonIdsReq) returns (PreviewCardsBySeasonIdsReply);
    // 根据配置id查询课程和套餐
    rpc SelectSeasonAndProductByConfigIds (SelectSeasonAndProductByConfigIdReq) returns (SelectSeasonAndProductByConfigIdReply);
    // 获取有效的分类集合(缓存/Nullable)
    rpc FilterClassifications (google.protobuf.Empty) returns (FilterClassificationsReply);
    // 获取换课配置信息
    rpc GetChangeClassConfig (GetChangeClassConfigReq) returns (GetChangeClassConfigReply);
    // 课程创作者信息
    rpc SeasonUserInfo (SeasonUserInfoReq) returns (SeasonUserInfoReply);
    // 批量课程创作者信息
    rpc SeasonsUserInfo (SeasonsUserInfoReq) returns (SeasonsUserInfoReply);
    // 获取所有的UP主和其对应的课(Map<up_id : [season_info]>)
    rpc AllSeasonOwner (AllSeasonOwnerReq) returns (AllSeasonOwnerReply);
    // 创建更新课程维度用户相关的信息
    rpc InsertOrUpdateUserInfo (InsertOrUpdateUserInfoReq) returns (InsertOrUpdateUserInfoReply);
    // 获取课程卖点标签
    rpc GetSeasonSellPointTag (GetSeasonSellPointTagRequest) returns (GetSeasonSellPointTagResponse);
    // 获取添加销售记录
    rpc GetUserAddCustomerRecord (GetUserAddCustomerRecordRequest) returns (GetUserAddCustomerRecordResponse);
    // 判断是否是首课直播
    rpc CheckIsSeasonFirstLive (CheckIsSeasonFirstLiveRequest) returns (CheckIsSeasonFirstLiveResponse);
    // 商业人员管理的课程
    rpc QueryStaffManageSeason (QueryStaffManageSeasonRequest) returns (QueryStaffManageSeasonResponse);
    // 处理商业人员管理的课程
    rpc HandleStaffManageSeason (HandleStaffManageSeasonRequest) returns (HandleStaffManageSeasonResponse);
    // 系列找课
    rpc SeasonSearchV2 (SeasonSearchV2Request) returns (SeasonSearchV2Response);
    // 获取用户课程设置
    rpc GetSeasonSetting(GetSeasonSettingRequest) returns (GetSeasonSettingResponse);

    // 获取课程详情--详情页使用
    rpc ViewSeasonDetail(ViewSeasonDetailRequest) returns (ViewSeasonDetailResponse);
}

// 处理商业人员管理的课程
message HandleStaffManageSeasonRequest {
    // 处理商业人员管理的课程
    repeated HandleStaffManageSeasonInfo manage_season_infos = 1;
}

// 处理商业人员管理的课程
message HandleStaffManageSeasonInfo {
    // 员工name
    string staff_name = 1;
    // 课程id
    int32 season_id = 2;
    // 赛道id, 默认为0, manage_type是2时值不为0
    int32 track_id = 3;
    // 1课程, 2赛道
    int32 manage_type = 4;
    // 0删除, 1新增
    int32 handle_type = 5;

}

// 处理商业人员管理的课程
message HandleStaffManageSeasonResponse {
}

// 商业人员管理的课程
message QueryStaffManageSeasonRequest {
    // 员工name
    repeated string staff_name = 1;

}

// 商业人员管理的课程
message QueryStaffManageSeasonResponse {
    // 详情
    repeated StaffManageSeasonInfo staff_manage_season_infos = 1;
}


// 商业人员管理的课程
message StaffManageSeasonInfo {
    // 员工name
    string staff_name = 1;
    // 课程id
    int32 season_id = 2;
    // 课程标题
    string season_title = 3;
    // 1课程, 2赛道
    int32 manage_type = 4;
    // 赛道id, 默认为0, manage_type是2时值不为0
    int32 track_id = 5;
    // up主id
    int64 up_id = 6;
}

message ViewSeasonDetailRequest {
    // 稿件 ID
    int64 aid = 1;
    // 课时 ID
    int32 episode_id = 2;
    // 课程 ID
    int32 season_id = 3;
    // 分期课 ID
    int64 series_id = 4;
}

message ViewSeasonDetailResponse {
    // 课程详情
    SeasonDetail season_detail = 1;
    // 分期课信息列表
    SeriesInfo series_info = 2;
}

//课程开课状态
enum SeasonStartState {
    // 未开课
    NOT_START_STATE = 0;
    // 开课中
    STARTING_STATE  = 1;
    // 结课
    ENDED_STATE  = 2;
}

// 分期课信息
message SeriesInfo {
    // 分期课 ID
    int64 series_id = 1;
    // 分期课信息列表
    repeated SeriesSeasonInfo series_seasons = 2;
}

// 分期课课程信息
message SeriesSeasonInfo {
    // 课程 ID
    int32 season_id = 1;
    // 开课时间
    int64 start_time = 2;
    // 分期课程开课状态
    SeasonStartState start_state = 3;
    // 课程属性
    Rights rights = 4;
}

// 课程详情
message SeasonDetail {
    // 课程id
    int32 season_id = 1;
    // 课程标题
    string title = 2;
    // 副标题
    string subtitle = 3;
    // 1=更新中，2=已完结
    int32 release_status = 4;
    // 课时数
    int32 ep_count = 5;
    // 课程属性
    Rights rights = 6;
    // up主id
    int64 up_id = 7;
    // ios购买渠道 1:仅支持B币支付 2:支持现金支付
    int32 payment_type = 8;
    // 是否独家，废弃字段，使用 show_tag，废弃字段，使用 show_tag
    bool exclusive = 9 [deprecated = true];
    // 课程形态
    int32 season_style = 10;
    // 定价
    int32 price = 11;
    // 有效期天数，默认值0长期有效
    int32 expiry_day = 12;
    // 有效期统一到期时间
    int64 expire_at = 13;
    // 课程平台属性
    PlatRights plat_rights = 14;
    // 结业证书配置
    SeasonCredential season_credential = 15;
    // 售前社区咨询活动
    CommunityActivity community_activity = 16;
    // 购后课程社群
    CommunityGroup communityGroup = 17;
    // 是否开启水印
    bool watermark = 18;
    // 讲师信息
    string lecturer_info = 19;
    // 1=文字简介，2=图片简介
    int32 brief_type = 20;
    // 文字简介或者图片简介链接，如果是多个图片，用逗号分隔
    string brief_content = 21;
    // 图片简介
    repeated BriefImg brief_imgs = 22;
    // 直播课时数
    int32 live_ep_count = 23;
    // 开放浏览课时数
    int32 opened_ep_count = 24;
    // 封面
    string cover = 25;
    // IOS 定价
    int32 ios_price = 26;
    // 课件不显示扩展名
    bool courseware_hide_ext = 27;
    // 联合创作者
    repeated Cooperator cooperators = 28;
    // 课程亮点
    repeated SellPointTag sell_point_tags = 29;
    // 展示标签类型,1:出品,2:独家,3:首发
    int32 show_tag = 30;
}

message BriefImg {
    // 图片地址
    string url = 1;
    // width
    int32 width = 2;
    // height
    int32 height = 3;
    // 类型,1:app端,2:web端
    int32 type = 4;
}

// PlatRights 课程平台属性
message PlatRights {
    //  仅 tv 端开发,非 tv 用户都不能访问课程
    bool only_tv_open = 1;
}

// 从 credential_content 获取
message SeasonCredential {
    // 开关
    bool credential_switch = 1;
    // 是否全部课时解锁
    bool require_all = 2;
    // 解锁所需课时
    int32 progress_require = 3;
}

message CommunityGroup {
    // 是否有社群配置
    bool community_switch = 1;
    // 二维码
    repeated CommunityGroupQrCode qr_codes = 2;
}

// 从 view_json 获取
message CommunityGroupQrCode {
    // 二维码图片
    string img_url = 1;
    // 二维码标题
    string title = 2;
}

// 从 service_definition 获取
message CommunityActivity {
    //咨询账号mid
    int64 mid = 1;
    // app url
    string app_url = 2;
    // pc url
    string pc_url = 3;
}