// +bili:type=service
syntax = "proto3";
package cheese.season.service.series.v1;

import "cheese/service/season/series/model.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/cheese/service.season.series;api";
option java_package = "com.bapis.cheese.service.season.series";
option java_multiple_files = true;
option (wdcli.appid) = "cheese.season.service";

// series service
service Series {
    // 系列保存(创作中心)
    rpc SeriesSave (SeriesSaveReq) returns (SeriesSaveReply) {
        option deprecated = true;
    };
    // 系列我的课程列表-mid、season_status查询(创作中心)
    rpc SeriesMyList (SeriesMyReq) returns (SeriesMyReply) {
        option deprecated = true;
    };
    //课程拷贝(创作中心)
    rpc SeasonEpisodeCopy (SeasonEpisodeCopyReq) returns (SeasonEpisodeCopyReply) {
        option deprecated = true;
    };
    // 系列课程列表-season_id查询(课程详情)
    rpc SeriesSeasonList (SeriesSeasonListReq) returns (SeriesSeasonListReply);
    // 系列课程列表-series_id查询
    rpc SeriesCards (SeriesCardsReq) returns (SeriesCardsReply);
    //运营操作-系列课程结课状态结课、未结课(重新开课课)状态（管理平台）
    rpc SeriesSeasonStateUpdate (SeriesSeasonStateUpdateReq) returns (SeriesSeasonStateUpdateReply) {
        option deprecated = true;
    };
    // 系列课程分页-series_id、season_id查询（管理平台）
    rpc SeriesSeasonPage (SeriesSeasonPageReq) returns (SeriesSeasonPageReply) {
        option deprecated = true;
    };

    // 根据seriesId批量查询对应seasonIds
    rpc BatchSeriesSeasonIdsList (BatchSeriesSeasonIdsReq) returns (BatchSeriesSeasonIdsReply);
}