syntax = "proto3";
package cheese.service.creation.coupon.v1;

// import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/cheese/service.creation.coupon.v1;api";
option java_package = "com.bapis.cheese.service.creation.coupon.v1";
option java_multiple_files = true;
option (wdcli.appid) = "edu.pugv.creation-service";

// 创作中心优惠
service CreationCouponService {
  // 创建优惠
  rpc CreateCoupon (CreateCouponReq) returns (CreateCouponResponse);
  // 优惠券记录
  rpc MyCouponHistory(MyCouponHistoryReq) returns (MyCouponHistoryResponse);
  // 废弃优惠券
  rpc DiscardCoupon (DiscardCouponReq) returns (DiscardCouponResponse);
}

message CreateCouponReq{
  int64 mid = 1;
  int64 product_id = 2;
  CouponCreateModel coupon = 3;
}

message CouponCreateModel {
  string receive_start_time = 1;
  string receive_end_time = 2;
  string use_start_time = 3;
  string use_end_time = 4;
  CouponTypeEnum coupon_type = 5;
  double threshold = 6;
  double amount = 7;
}

enum CouponTypeEnum{
  COUPON_TYPE_NONE = 0;
  // 折扣
  COUPON_TYPE_DISCOUNT = 1;
  // 满减
  COUPON_TYPE_DEDUCT = 2;
}

message CreateCouponResponse{
  string batch_token = 1;
}

message CouponHistoryModel{
  string receive_start_time = 1;
  string receive_end_time = 2;
  string use_start_time = 3;
  string use_end_time = 4;
  CouponTypeEnum coupon_type = 5;
  double threshold = 6;
  double amount = 7;
  string batch_token = 8;
  int32 use_status = 9;
  int32 receive_status = 10;
  int32 modifier_type = 11;
  string modify_time = 12;
  string modifier = 13;
}

message MyCouponHistoryReq{
  int64 mid = 1;
  int64 product_id = 2;
}

message MyCouponHistoryResponse{
  repeated CouponHistoryModel coupon_history_models = 1;
}

message DiscardCouponReq{
  int64 mid = 1;
  int64 product_id = 2;
}

message DiscardCouponResponse{

}