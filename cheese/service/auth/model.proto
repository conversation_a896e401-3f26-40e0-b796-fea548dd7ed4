syntax = "proto3";
package cheese.auth.service.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/cheese/service.auth;api";
option java_package = "com.bapis.cheese.service.auth";
option java_multiple_files = true;

// CanWatchReq .
message CanWatchBySeasonIdReq {
    // season id
    int32 season_id = 1;
    // mid
    int64 mid = 2;
}

// CanWatchReply .
message CanWatchBySeasonIdReply {
    // result
    bool result = 1;
}


// isPaidByAidReq .
message CanWatchByEpisodeReq {
    // aid
    int32 epid = 1;
    // mid
    int64 mid = 2;
}

// CanWatchByEpisodeReply .
message CanWatchByEpisodeReply {
    // result
    bool result = 1;
}

// CanWatchByAidReq .
message CanWatchByAidReq {
    // aid
    int32 aid = 1;
    // mid
    int64 mid = 2;
}

// isPaidByAidReply .
message CanWatchByAidReply {
    // result
    bool result = 1;
}

// seasonPlayStatusReq .
message SeasonPlayStatusReq {
    // aid
    int64 aid = 1;
    // mid
    int64 mid = 2;
}

// seasonPlayStatusReply .
message SeasonPlayStatusReply {
    //试看状态(1=整集试看 2=非试看 3=5分钟试看)
    int32 watch_status = 1;
    //购买状态(1=购买 2=未购买)
    int32 pay_status = 2;
    //系列状态(1=上架 2=下架)
    int32 season_status = 3;
}

//收藏总数请求
message FavoriteCountReq {
    // up主id
    int64 mid = 1;
}
//收藏总数响应
message FavoriteCountReply {
    // 总数
    int32 total = 1;
}

//ep是否具备跳转权限req
message CanSeekByEpisodeIdReq {
    // aid
    int32 epid = 1;
    // mid
    int64 mid = 2;
    //跳转位置-单位为秒 整数类型 最小为0
    int32 seek_time = 3;
}

//ep是否具备跳转权限reply
message CanSeekByEpisodeIdReply {
    // result
    bool result = 1;
}