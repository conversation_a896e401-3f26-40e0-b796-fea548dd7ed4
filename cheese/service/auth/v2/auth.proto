syntax = "proto3";
package cheese.auth.service.v2;

import "cheese/service/auth/v2/model.proto";
import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "cheese.service.auth";
option go_package = "buf.bilibili.co/bapis/bapis-gen/cheese/service.auth.v2;api";
option java_package = "com.bapis.cheese.service.auth.v2";
option java_multiple_files = true;
service Auth {
    // 是否具备观看权限
    rpc CanWatchBySeasonId (CanWatchBySeasonIdReq) returns (CanWatchBySeasonIdReply);
    // 是否具备观看权限
    rpc CanWatchByEpisodeId (CanWatchByEpisodeReq) returns (CanWatchByEpisodeReply);
    // 只有直播鉴权时调用
    // ep_id 后续是直播回调liveInfoId过来的
    // 其他业务方不要使用
    // 是否具备观看权限(如果ep状态不能观看，会抛业务异常)
    rpc CanWatchByEpisodeIdWithException (CanWatchByEpisodeReq) returns (CanWatchByEpisodeReply) {
        option deprecated = true;
    };
    // 是否具备观看权限
    rpc CanWatchByAid (CanWatchByAidReq) returns (CanWatchByAidReply);
    // ep是否具备跳转权限
    rpc CanSeekByEpisodeId (CanSeekByEpisodeIdReq) returns (CanSeekByEpisodeIdReply);
    // 我的权益
    rpc MyRights (MyRightsReq) returns (MyRightsReply);
    rpc MyRightsList (MyRightsListReq) returns (MyRightsListReply);

    // 是否具备season观看权限
    rpc WatchRights (WatchRightsReq) returns (WatchRightsReply);
    // 权益发放
    rpc GrantRights (GrantRightsReq) returns (GrantRightsReply);
    // 批量权益发放 事务性一起成功或失败，最多50条
    rpc BatchGrantRights (BatchRightsReq) returns (BatchRightsReply);
    // 权益取消
    rpc CancelRights (CancelRightsReq) returns (CancelRightsReply);
    // 权益查询（查询主库）
    rpc QueryRightsByOrder (QueryRightsByOrderReq) returns (QueryRightsByOrderReply);
    // 权益列表查询（查询主库）
    rpc QueryRightsListByOrder (QueryRightsListByOrderReq) returns (QueryRightsListByOrderReply);
    // 用户的观看权益(退款计算在内)
    rpc MyWatchRights (MyWatchRightsReq) returns (MyWatchRightsReply);
    // 权益信息(未实现,暂无使用场景)
    rpc RightsById (RightsByIdReq) returns (RightsByIdReply);
    // 权益信息批量查询
    rpc RightsByIds (RightsByIdsReq) returns (RightsByIdsReply);
    // 是否具备观看权限 以及获取对应的订单号
    rpc CanWatchBySeasonIdAndGetOrderNo (CanWatchBySeasonIdAndGetOrderNoReq) returns (CanWatchBySeasonIdAndGetOrderNoReply);
    // 是否具备观看权限 以及获取对应的订单号
    rpc CanWatchBySeasonIdsAndGetOrderNo (CanWatchBySeasonIsdAndGetOrderNoReq) returns (CanWatchBySeasonIdsAndGetOrderNoReply);
    // 我的权益,包含expire过期信息
    rpc MyRightsWithExpire (MyRightsWithExpireReq) returns (MyRightsWithExpireReply);
    // season是否具备观看权限-批量
    rpc CanWatchBySeasonIds (CanWatchBySeasonIdsReq) returns (CanWatchBySeasonIdsReply);
    // 补差换课是否满足换购条件
    rpc CanChangeSeasonByMid (CanChangeSeasonByMidReq) returns (CanChangeSeasonByMidReply);
    // 补差换课权益发放
    rpc GrantRightsForChangeSeason (GrantRightsForChangeSeasonReq) returns (GrantRightsForChangeSeasonReply);
    rpc GetXetTicketByOrderNo (GetXetTicketByOrderNoReq) returns (GetXetTicketByOrderNoReply);
    rpc SyncXetTicketOrderNo (SyncXetTicketOrderNoReq) returns (SyncXetTicketOrderNoReply);
    // 批量取消权益
    rpc BatchCancelRightsBySeasonID (BatchCancelRightsBySeasonIDRequest) returns (BatchCancelRightsBySeasonIDResponse);
}