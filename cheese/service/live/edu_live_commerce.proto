syntax = "proto3";
package cheese.live.service.livecommerce.v1;

import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "edu.pugv.edu-live-service";
option go_package = "buf.bilibili.co/bapis/bapis-gen/cheese/service.live;api";
option java_package = "com.bapis.cheese.service.live";
option java_multiple_files = true;

service LiveCommerce {
  // 为直播提供：直播电商分区使用，查询全部的带课房间号
  rpc queryAllRoomIds (QueryAllRoomIdsReq) returns (QueryAllRoomIdsReply);

  /**
    解析虚拟商品URL，反查SPUID（商业中心调用主站课程侧）
    目前课程侧有两类课程商品，分别对应三种URL，商品类型为课程时，存在单集课时链接及课程链接，商品类型为套餐时，使用套餐链接。
    课程商品：
    单集课时：https://www.bilibili.com/cheese/play/ep49553
    课程链接：https://www.bilibili.com/cheese/play/ss420
    套餐商品：
    套餐课程：https://www.bilibili.com/cheese/pages/packageCourseDetail?productId=217
    返回主站课程侧的SPUID
   */
  rpc querySpuIdFromUrl (QuerySpuIdFromUrlReq) returns (QuerySpuIdFromUrlReply);
}

message QueryAllRoomIdsReq {}
message QueryAllRoomIdsReply {
  // 房间号
  repeated int64 room_id = 1;
}

message QuerySpuIdFromUrlReq {
  // 课程侧商品详情页
  string url = 1;
}

message QuerySpuIdFromUrlReply {
  // 课程侧商品spuid
  string spu_id = 1;
}