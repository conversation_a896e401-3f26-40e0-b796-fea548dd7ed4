syntax = "proto3";
package cheese.live.service.liveclass.v1;
import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "edu.pugv.edu-live-service";
option go_package = "buf.bilibili.co/bapis/bapis-gen/cheese/service.live;api";
option java_package = "com.bapis.cheese.service.live";
option java_multiple_files = true;

service Liveclass {
    // 上课
    rpc AttendClass (AttendClassReq) returns (AttendClassReply);
    // 下课
    rpc FinishClass(FinishClassReq) returns (FinishClassReply);
    // 获取推流码
    rpc getLiveStream (GetLiveStreamReq) returns (GetLiveStreamReply);
    // 获取敏感词列表
    rpc GetKeywordList(GetKeywordReq) returns (GetKeywordReply);
    // 添加敏感词
    rpc AddKeyWord(AddKeyWordReq) returns (AddKeyWordReply);
    // 删除敏感词
    rpc DelKeyWord(DelKeyWordReq) returns (DelKeyWordReply);
    // 用户禁言
    rpc AddSilentUser(AddSilentUserReq) returns (AddSilentUserReply);
    // 用户解禁
    rpc DelSilentUser(DelSilentUserReq) returns (DelSilentUserReply);
    // 获取房间是否开启全员禁言
    rpc GetRoomSilent(GetRoomSilentReq) returns (GetRoomSilentReply);
    // 开启或关闭房间禁言
    rpc RoomSilent(RoomSilentReq) returns (RoomSilentReply);
    // 查询是否被主播禁言
    rpc WhetherBlockUser(WhetherBlockUserReq) returns (WhetherBlockUserReply);
    // 判断主播是否在直转点白名单中
    rpc CanOpenLiveRecord(CanOpenLiveRecordReq) returns (CanOpenLiveRecordReply);
    // 中途休息，暂停上课
    rpc ClassLeave(ClassLeaveReq) returns (ClassLeaveReply);
    // 休息完毕，由来上课了
    rpc ClassBack(ClassBackReq) returns (ClassBackReply);
    // 查询课堂状态
    rpc GetClassStatus(GetClassStatusReq) returns (GetClassStatusReply);
    // 根据房间号，查询【直播中】的付费直播信息
    rpc queryLivingClassByRoomId(QueryLivingClassByRoomIdReq) returns (QueryLivingClassByRoomIdReply);
    // 根据EpLiveId查询直播信息
    rpc queryLiveClassByEpLiveId(QueryLiveClassByEpLiveIdReq) returns (QueryLiveClassByEpLiveIdReply);
    // 查询付费直播权限（在pc直播姬上，是否能显示付费直播）
    rpc queryUserLiveClassAuth(QueryUserLiveClassAuthReq) returns (QueryUserLiveClassAuthReply);
}

message AttendClassReq {
    // 操作人ID，上课老师ID
    int64 mid = 1;

    // epLive.id
    int32 ep_live_id = 2;

    // 上课的阶段：测试0、预热1、上课2
    AttendType attend_type = 3;

    // 房间号
    int64 room_id = 4 [json_name = 'room_id'];
}

enum AttendType {
    // 上课的阶段：测试0、预热1、上课2、下课3
    TEST = 0;
    PREHEAT = 1;
    ATTEND = 2;
    FINISH = 3;
    INIT = 4;
}

message AttendClassReply {
    // 推流码
    repeated LiveStream live_stream = 1;

    // 加密直播间
    RoomEncrypt room_encrypt = 2;
}

message LiveStream {
    string stream_name = 1;
    string url = 2;
}

message RoomEncrypt {
    // 是否加密
    bool is_encrypt = 1;

    // 房间进入链接，形如:https://live.bilibili.com/[roomid]
    // 如果加密房间，返回的链接后面会后缀上"?password=xxx"参数，是进入房间的密钥
    string url = 2;
}

message FinishClassReq {
    // 操作人ID，上课老师ID
    int64 mid = 1;

    // epLive.id
    int32 ep_live_id = 2;

    // 房间号
    int64 room_id = 3 [json_name = 'room_id'];
}

message FinishClassReply {

}

message GetLiveStreamReq {
    // 操作人ID，上课老师ID
    int64 mid = 1;

    // epLive.id
    int32 ep_live_id = 2;

    // 房间号
    int64 room_id = 3 [json_name = 'room_id'];
}

message GetLiveStreamReply {
    // 推流码
    repeated LiveStream live_stream = 1;

    // 加密直播间
    RoomEncrypt         room_encrypt = 2;

    // 直播间id
    int64 room_id = 3;

    // 上课的阶段：测试0、预热1、上课2
    AttendType attend_type = 4;

    // 播放地址：就是最小化的那个播放器页面
    string play_url = 5;

    // 直播的分区设置
    string live_zone = 6;
}

// 获取敏感词列表 请求
message GetKeywordReq {
    //直播间id
    int64 room_id = 1;
    //用户id
    int64 mid = 2;
}

// 获取敏感词列表 返回
message GetKeywordReply {
    //敏感词列表
    repeated string keywords = 1;
}

// 添加敏感词 请求
message AddKeyWordReq {
    //直播间id
    int64 room_id = 1;
    //用户id
    int64 mid = 2;
    //敏感词
    string keyword = 3;
}

// 添加敏感词 返回
message AddKeyWordReply {}

// 删除敏感词 请求
message DelKeyWordReq {
    //直播间id
    int64 room_id = 1;
    //用户id
    int64 mid = 2;
    //敏感词
    string keyword = 3;
}

// 删除敏感词 返回
message DelKeyWordReply {}

// 用户禁言 请求。应该传唯一的参数ep_live_id
message AddSilentUserReq {
    //直播间id
    int64 room_id = 1;
    //用户id
    int64 mid = 2;
    //被禁言用户id
    int64 tuid = 3;
    // epLive.id
    int32 ep_live_id = 4;
}

// 用户禁言 返回
message AddSilentUserReply {}

// 用户解禁 请求。应该传唯一的参数ep_live_id
message DelSilentUserReq {
    //直播间id
    int64 room_id = 1;
    //用户id
    int64 mid = 2;
    //被禁言用户id
    int64 tuid = 3;
    // epLive.id
    int32 ep_live_id = 4;
}

// 用户解禁 返回
message DelSilentUserReply {}

// 获取房间是否开启全员禁言 请求
message GetRoomSilentReq {
    //直播间id
    int64 room_id = 1;
}

// 获取房间是否开启全员禁言 返回
message GetRoomSilentReply {
    //是否全员禁言
    bool whether_all_silent = 1;
}

// 开启或关闭房间禁言 请求
message RoomSilentReq {
    //直播间id
    int64 room_id = 1;
    //用户id
    int64 mid = 2;
    //操作
    RoomSilentAction action = 3;
}

enum RoomSilentAction {
    //开启禁言
    ADDSLIENT = 0;
    //关闭禁言
    DELSLIENT = 1;
}

// 开启或关闭房间禁言 返回
message RoomSilentReply {}

// 查询是否被主播禁言 请求
message WhetherBlockUserReq {
    //直播间id
    int64 room_id = 1;
    //用户id
    int64 mid = 2;
}

// 查询是否被主播禁言 返回
message WhetherBlockUserReply {
    bool whether_silent = 1;
}

// 判断主播是否在直转点白名单中 请求
message CanOpenLiveRecordReq {
    //用户id
    int64 mid = 1;
}

// 判断主播是否在直转点白名单中 返回
message CanOpenLiveRecordReply {
    //是否在直转点白名单中
    bool can_open_live_record = 1;
}

message ClassLeaveReq {
    // 操作人ID，上课老师ID
    int64 mid = 1;

    // epLive.id
    int32 ep_live_id = 2;
}

message ClassLeaveReply {}

message ClassBackReq {
    // 操作人ID，上课老师ID
    int64 mid = 1;

    // epLive.id
    int32 ep_live_id = 2;
}

message ClassBackReply {}

message GetClassStatusReq {
    // 操作人ID，上课老师ID
    int64 mid = 1;

    // epLive.id
    int32 ep_live_id = 2;
}

message GetClassStatusReply {
    // epLive.id
    int32 ep_live_id = 1;

    /**
     * test|prepare|speaking|dealy
     * 老师操作到的状态
     */
    int32 class_status = 2;

    /**
     * test|prepare|speaking|dealy
     * 课时时间计算的状态 now() - [  ]
     */
    int32 plan_status = 3;

    /**
     * 测试开始时间
     */
    int64 test_start_time = 4;

    /**
     * 预热开始时间
     */
    int64 prepare_start_time = 5;

    /**
     * 上课时间
     */
    int64 live_start_actual_time = 6;

    /**
     * 离开时间
     */
    int64 leave_time = 7;
}

message QueryLivingClassByRoomIdReq {
    // 房间号
    int64 room_id = 1;
}

message LiveClassInfo {
    // 房间号
    int64 room_id = 1;

    /**
     * episode.id
     */
    int32 episode_id = 2;

    /**
     * 直播epLive.id
     */
    int32 ep_live_id = 3;

    /**
     * 当前状态
     */
    int32 status = 4;

    /**
     * 直播开始时间
     */
    int64 live_start_time = 5;

    /**
     * 直播结束时间
     */
    int64 live_end_time = 6;

    /**
     * 实际直播开始时间
     */
    int64 live_start_actual_time = 7;

    /**
     * 时间直播结束时间
     */
    int64 live_end_actual_time = 8;

    /**
     * 加密测试时间
     */
    int64 encrypt_time = 9;

    /**
     * 解密时间
     */
    int64 decrypt_time = 10;

    /**
     * 拖堂时间，单位s
     */
    int32 class_delay_duration = 11;

    /**
     * 0=正常 1=删除
     */
    int32 is_delete = 12;

    /**
     * 加密链接
     */
    string encrypt_url = 13;

    /**
     * 关联的season
     */
    repeated int32 season_id = 14;

    /**
     * 播放的分区，用于直播首页展示
     */
    string live_zone = 15;

    /**
     * 备注
     */
    string remark = 16;

    /**
     * 预热时间
     */
    int64 preheat_time = 17;

    /**
    * 主播ID
    */
    int64 anchor_id = 18;
}

message QueryLivingClassByRoomIdReply {
    LiveClassInfo live_class_info = 1;
}

message QueryLiveClassByEpLiveIdReq {
    // epLive.id
    int32 ep_live_id = 1;
}

message QueryLiveClassByEpLiveIdReply {
    LiveClassInfo live_class_info = 1;
}

message QueryUserLiveClassAuthReq {
    // 用户ID
    int64 mid = 1;
}

message QueryUserLiveClassAuthReply {
    // 是否 是老师 true:是 false:不是
    bool is_teacher = 1;
    // 是否 当前有场次 true:可以 false:不可以
    bool is_can_current_live = 2;
    // 不能开播的原因
    string no_can_live_reason = 3;
    // 当前场次信息
    LiveClassInfo live_class_info = 4;

    message LiveClassInfo {
        // 房间号
        int64 room_id = 1;
        // 开始时间
        int64 start_time = 2;
        // 结束时间
        int64 end_time = 3;
    }
}