syntax = "proto3";
package dynamic.service.publish.v1;
import "extension/wdcli/wdcli.proto";
import "dynamic/common/dynamic.proto";
import "dynamic/common/publish.proto";
import "dynamic/common/opus.proto";
import "google/protobuf/empty.proto";

option go_package          = "buf.bilibili.co/bapis/bapis-gen/dynamic/service.publish;api";
option java_package        = "com.bapis.dynamic.service.publish";
option java_multiple_files = true;

option (wdcli.appid) = "main.dynamic.publish-service";

service Publish {
    /** 发布相关的读接口 **/
    // 获取动态内容,用于第三方分享
    rpc GetDynContent(GetDynContentReq) returns (GetDynContentRsp);
    // 获取动态id
    rpc GetDynamicId(GetDynamicIdReq) returns (GetDynamicIdRsp);
    // 获取预约分享落地页,用于空间预约分享
    rpc GetReserveDynShareContent(GetReserveDynShareContentReq) returns (GetReserveDynShareContentRsp);

    /** 通用的内网创建动态接口 **/
    // 内网分享网页链接（生成通用模板动态）（接入前联系研发申请分配from参数的值）
    rpc ISharePage(ISharePageReq) returns (ICreateResp);
    // 内网创建文字动态（接入前联系研发申请分配from参数的值）
    rpc ICreateWord(ICreateWordReq) returns (ICreateResp);
    // 内创建图文动态（接入前联系研发申请分配from参数的值）
    rpc ICreateDraw(ICreateDrawReq) returns (ICreateResp);
    // 内网分享（接入前联系研发申请分配from参数的值）
    rpc IShareBiz(IShareBizReq) returns (ICreateResp);
    // 内网转发（接入前联系研发申请分配from参数的值）
    rpc IRepost(IRepostReq) returns (ICreateResp);
    // 内网创建新图文动态（接入前联系研发申请分配from参数的值）
    rpc ICreateOpus(ICreateOpusReq) returns (ICreateResp);

    /** 特殊场景的创建动态接口 **/
    // 创建渠道的动态卡片（仅提供预约/订阅业务方调用,不需要过审）
    rpc CreateTunnelDyn(CreateTunnelDynReq) returns (CreateTunnelDynRsp);
    // 内网创建预约动态（仅提供预约方调用,不需要过审）
    rpc ICreateReserveDyn(ICreateReserveDynReq) returns (ICreateReserveDynRsp);
    // 弹幕投票转发到动态（生成一条对视频的转发动态）
    rpc ICreateDanmuku(ICreateDanmukuReq) returns (ICreateResp);
    // 创建广告接口
    rpc ICreateAd(ICreateAdReq) returns (ICreateResp);
    // 创建直播大卡
    rpc ICreateLiveRcmd(ICreateLiveRcmdReq) returns (ICreateResp);

    /** 删除接口 **/
    // 移除动态（接入前联系研发申请分配from参数的值）
    rpc RmDyn(RmDynReq) returns (.google.protobuf.Empty);

    // 商单补绑
    rpc CommercialBind(CommercialBindReq) returns (CommercialBindRsp);

    // 创建动态--评论调用
    rpc CreateDynFromReply(CreateDynFromReplyReq) returns (ICreateResp);

    // 商品评论同步到动态 (生成纯文字/图文动态)
    rpc ICreateGoodsReplySync(ICreateGoodsReplySyncReq) returns (ICreateResp);

    // 内网编辑新图文动态
    rpc IEditOpus(IEditOpusReq) returns (IEditResp);
    // tab3编辑新图文内容：只修改文字区域的非高亮内容
    rpc Tab3EditOpus(Tab3EditOpusReq) returns (Tab3EditOpusResp);
    // 动态关联话题标识 -- 动态收录进话题后调用
    rpc DynamicAttachTopic(DynamicAttachTopicReq) returns (DynamicAttachTopicRsp);
}

// tab3编辑新图文内容请求
message Tab3EditOpusReq {
    int64 dyn_id               = 1;  // 动态id
    .dynamic.MetaDataCtrl meta = 2;  // 用户创建接口meta信息
    .opus.Opus opus            = 3;  // 发布的内容
}

// tab3编辑新图文内容返回
message Tab3EditOpusResp {
    int64 dyn_id = 1;  // 编辑成功的动态id
}

// 内网创建新图文动态请求
message ICreateOpusReq {
    int64 uid                             = 1;  // 发布者
    .dynamic.MetaDataCtrl meta            = 2;  // 用户创建接口meta信息
    .opus.Opus opus                       = 3;  // 发布的内容
    .dynamic.CreateAttachCard attach_card = 4;  // 动态附加大卡
    .dynamic.CreateOption option          = 5;  // 特殊的创建选项
    .dynamic.CreateTopic topic            = 6;  // 发布时携带的新话题
    string upload_id                      = 7;  // 发布时客户端串联用户行为链的ID
}

// 内网编辑新图文动态请求
message IEditOpusReq {
    int64 dyn_id                          = 1;  // 动态id
    int64 uid                             = 2;  // 发布者
    .dynamic.MetaDataCtrl meta            = 3;  // 用户创建接口meta信息
    .opus.Opus opus                       = 4;  // 发布的内容
    .dynamic.CreateAttachCard attach_card = 5;  // 动态附加大卡
    .dynamic.CreateOption option          = 6;  // 特殊的创建选项
    .dynamic.CreateTopic topic            = 7;  // 发布时携带的新话题
}

// 编辑接口统一回包
message IEditResp {
    int64 dyn_id   = 1;  // 编辑成功的动态id
    int64 dyn_type = 2;  // 编辑成功的动态类型
    int64 dyn_rid  = 3;  // 编辑成功的动态rid
}

// 商单补绑请求
message CommercialBindReq {
    int64 dyn_id                 = 1;  // 动态id
    int64 commercial_entity_type = 2;  // 商业类型：0花火商单；
    int64 commercial_entity_id   = 3;  // 商单id
}

// 商单补绑返回
message CommercialBindRsp {
}

enum TunnelType {
    TUNNEL_TYPE_NONE            = 0;
    TUNNEL_TYPE_SUBSCRIBE       = 1;  // 订阅卡
    TUNNEL_TYPE_RESERVE_ARC     = 2;  // 稿件预约
    TUNNEL_TYPE_RESERVE_LIVE    = 3;  // 直播预约
    TUNNEL_TYPE_RESERVE_ESPORTS = 4;  // 赛事预约
    TUNNEL_TYPE_TOPIC_SET       = 5;  // 话题集
    TUNNEL_TYPE_RESERVE_CHEESE  = 6;  // 课程预约
}

message CreateTunnelDynReq {
    uint64 uid             = 1;  // 用户id
    uint64 rid             = 2;  // 资源id
    TunnelType tunnel_type = 3;  // 业务类型
}

message CreateTunnelDynRsp {
    uint64 dyn_id = 1;  // 动态id
}

enum ReserveType {
    RESERVE_TYPE_NONE     = 0;
    RESERVE_TYPE_ARCHIVE  = 1;  // 稿件预约
    RESERVE_TYPE_LIVE     = 2;  // 直播预约
    RESERVE_TYPE_ESPORTS  = 3;  // 赛事预约
    RESERVE_TYPE_PREMIERE = 4;  // 首映预约
    RESERVE_TYPE_CHEESE   = 5;  // 课程预约
}

message ShareReserve {
    ReserveType type      = 1;  // 预约类型
    string title          = 2;  // 预约标题
    int64 live_start_time = 3;  // 直播预计开播时间
}

message DynContent {
    int64 uid            = 1;   // 发布者uid
    int32 uid_type       = 2;   // 发布者uid的类型
    int64 dyn_id         = 3;   // 动态id
    int64 dyn_type       = 4;   // 动态类型，文档:https://info.bilibili.co/pages/viewpage.action?pageId=13358955
    string content       = 5;   // 动态文本内容
    string cover         = 6;   // 封面图
    int64 timestamp      = 7;   // 时间戳
    ShareReserve reserve = 8;   // 预约分享信息
    bool in_audit        = 9;   // 审核状态
    string title         = 10;  // 标题
}

message GetDynContentReq {
    int64 uid                 = 1;
    repeated int64 dyn_ids    = 2;
    bool need_in_audit        = 3;  // 是否需要审核中的动态(默认不需要)
    dynamic.MetaDataCtrl meta = 4;  // 用户分享时携带的meta信息
}

message GetDynContentRsp {
    map<int64, DynContent> items = 1;
}

enum ReserveCreateDynType {
    RESERVE_CREATE_DYN_TYPE_NONE = 0;
    RESERVE_CREATE_DYN_TYPE_WORD = 1;  // 纯文字动态
    RESERVE_CREATE_DYN_TYPE_PIC  = 2;  // 图文动态
}

message GetDynamicIdReq {
}

message GetDynamicIdRsp {
    int64 dyn_id = 1;  // 动态id
}

message ICreateReserveDynReq {
    int64 uid                     = 1;  // uid
    int64 dyn_id                  = 2;  // 动态id
    ReserveCreateDynType dyn_type = 3;  // 预约创建动态的类型
    int64 reserve_id              = 4;  // 预约id
    string content                = 5;  // 动态文本信息
    string pictures               = 6;  // 图片信息
    bool show_dyn                 = 7;  // 是否展示动态, 不展示的动态只用于分享、唤起
    ReserveType reserve_type      = 8;  // 预约类型
}

message ICreateReserveDynRsp {}

message GetReserveDynShareContentReq {
    int64 uid                 = 1;  // uid
    int64 dyn_id              = 2;  // 动态id
    dynamic.MetaDataCtrl meta = 3;  // 客户端版本信息等
    string share_id           = 4;  // 分享页面标识，即页面pvid
    int32 share_mode          = 5;  // 分享组件所需的分享类型，1:文字，2:图片，3:链接，4:视频，5:音频
}

message GetReserveDynShareContentRsp {
    dynamic.ShareChannel share_info = 1;  // 分享渠道信息
}

// 通用模板的网页元内容(sketch结构)定义，发布接口使用，详见文档：https://info.bilibili.co/pages/viewpage.action?pageId=5429370
message Sketch {
    string title      = 1;  // 元内容标题，长度30限制
    string desc_text  = 2;  // 描述文字（文本内容第二行），长度233限制
    string text       = 3;  // 文本文字（文本内容第三行），仅限竖图通用卡片使用，长度233限制
    int64 biz_id      = 4;  // 表示业务方的id表示，对于在业务方有唯一标示的必填
    int64 biz_type    = 5;  // 业务类型，与展示时的右上角标有关，需要业务方向动态申请
    string cover_url  = 6;  // 封面图片链接地址，域名需要符合白名单
    string target_url = 7;  // 跳转链接地址，域名需要符合白名单
}

// 内网分享网页链接（生成通用模板动态）请求
message ISharePageReq {
    int64 uid                    = 1;  // 分享者mid
    dynamic.MetaDataCtrl meta    = 2;  // 用户发布时携带的meta信息
    string content               = 3;  // 分享文本内容：纯文字，不支持富文本
    int64 type                   = 4;  // 通用模板类型：2048方图 2049竖图 其他值无效
    Sketch sketch                = 5;  // 通用模板的元内容（网页内容）
    dynamic.CreateTopic topic    = 6;  // 发布时携带的新话题
    CreateOption option          = 7;  // 特殊的创建选项
    CreateContent create_content = 8;  // 发布的动态内容 
}

// 内网接口返回（通用）
message ICreateResp {
    int64 dyn_id   = 1;  // 创建成功生成的动态id
    int64 dyn_type = 2;  // 创建成功生成的动态的类型
    int64 dyn_rid  = 3;  // 创建成功生成的动态的rid
}

enum RmAction {
    MIN         = 0;  // 下限 用于参数校验
    USER_REMOVE = 1;  // 用户操作,彻底删除
    BIZ_FORBID  = 2;  // 业务方推送,驳回,临时删除
    INVALID     = 3;  // 资源失效
    DYN_FORBID  = 4;  // 动态业务驳回，来自审核或运营操作
    MAX         = 5;  // 上限 用于参数校验
}

message RmDynReq {
    int64 dyn_id              = 1;  // 动态id
    int64 uid                 = 2;  // 用户uid
    RmAction action           = 3;  // 移除动作
    string from               = 4;  // 来源；此字段用来做校验，业务方接入前需要和研发申请
    dynamic.DynRevsId revs_id = 5;  // 资源id 与 动态id与资源id 至少一个不为空
}

message ICreateWordReq {
    int64 uid                             = 1;  // 发布者
    .dynamic.MetaDataCtrl meta            = 2;  // 用户创建接口meta信息
    .dynamic.CreateContent content        = 3;  // 发布的内容
    .dynamic.CreateOption option          = 4;  // 特殊的创建选项
    .dynamic.CreateAttachCard attach_card = 5;  // 动态附加大卡
    .dynamic.CreateTopic topic            = 6;  // 发布时携带的新话题
    string upload_id                      = 7;  // 发布时客户端串联用户行为链的ID
}

message ICreateDrawReq {
    int64 uid                             = 1;  // 发布者
    .dynamic.MetaDataCtrl meta            = 2;  // 用户创建接口meta信息
    .dynamic.CreateContent content        = 3;  // 发布的内容
    repeated .dynamic.CreatePic pics      = 4;  // 图片内容
    .dynamic.CreateOption option          = 5;  // 特殊的创建选项
    .dynamic.CreateAttachCard attach_card = 6;  // 动态附加大卡
    .dynamic.CreateTopic topic            = 7;  // 发布时携带的新话题
    string upload_id                      = 8;  // 发布时客户端串联用户行为链的ID
}

// 内网分享
message IShareBizReq {
    int64 uid                 = 1;  // 分享者
    dynamic.MetaDataCtrl meta = 2;  // 用户创建接口meta信息
    CreateContent content     = 3;  // 分享的内容
    DynIdentity share_src     = 4;  // 转发源
    CreateOption option       = 5;  // 特殊的创建选项
}

// 内网转发
message IRepostReq {
    int64 uid                 = 1;  // 转发者
    dynamic.MetaDataCtrl meta = 2;  // 用户创建接口meta信息
    CreateContent content     = 3;  // 转发内容
    DynIdentity repost_src    = 4;  // 转发源
    CreateOption option       = 5;  // 特殊的创建选项
}

// 弹幕投票转发到动态（生成一条对视频的转发动态）
message ICreateDanmukuReq {
    int64 uid                 = 1;  // 发起分享的人uid
    dynamic.MetaDataCtrl meta = 2;  // 用户创建接口meta信息
    CreateContent content     = 3;  // 转发内容
    DynIdentity repost_src    = 4;  // 转发源
}

// 创建广告
message ICreateAdReq {
    int64 uid                 = 1;
    int64 uid_type            = 2;
    int64 rid                 = 3;
    int32 show                = 4;
    CreateContent content     = 5;  // 发布的内容
    int32 forbid_repost       = 6;
    dynamic.MetaDataCtrl meta = 7;  // 用户创建接口meta信息
};

message ICreateLiveRcmdReq {
    int64 uid                 = 1;  // 主播用户ID
    int64 rid                 = 2;  // 主播开播直播的唯一ID
    dynamic.MetaDataCtrl meta = 3;  // 用户创建接口meta信息
}

message CreateDynFromReplyReq {
    int64 uid                    = 1;   // 用户id
    int64 oid                    = 2;   // 评论id
    int32 only_comment           = 3;   // 图文仅评论可见: 0: 动态正常可见; 1: 只在评论可见,动态仅详情页可见
    ReplyCreateScene scene       = 4;   // 发布场景
    dynamic.MetaDataCtrl meta    = 5;   // 用户创建接口meta信息
    CreateContent content        = 6;   // 动态内容
    repeated CreatePic pics      = 7;   // 图片内容
    CreateAttachCard attach_card = 8;   // 动态附加大卡
    CreateTopic topic            = 9;   // 发布时携带的新话题
    string upload_id             = 10;  // 发布时客户端串联用户行为链的ID
    int64 reply_type             = 11;  // 评论区类型
    int64 reply_origin_id        = 12;  // 评论区id
    bool is_charged              = 13;  // 是否为充电带图评论
    int64 charged_fee            = 14;  // 充电评论的订单金额
}

enum ReplyCreateScene {
    REPLY_CREATE_SCENE_INVALID     = 0;
    REPLY_CREATE_SCENE_CREATE_WORD = 1;  // 发布纯文字动态
    REPLY_CREATE_SCENE_CREATE_DRAW = 2;  // 发布图文动态
    REPLY_CREATE_SCENE_REPOST      = 3;  // 转发动态
}

// 商品评论同步到动态 (生成一条纯文字/图文动态)
message ICreateGoodsReplySyncReq {
    int64 uid                  = 1;  // 发布者
    int64 goods_id             = 2;  // 商品id
    CreateContent content      = 3;  // 发布的内容
    dynamic.CreateScene scene  = 4;  // 发布类型
    repeated CreatePic pics    = 5;  // 图片内容
    .dynamic.CreateTopic topic = 6;  // 发布时携带的新话题
    string upload_id           = 7;  // 发布时客户端串联用户行为链的ID
}

message DynamicAttachTopicReq {
    int64 dyn_id = 1;
    string from  = 2;
}

message DynamicAttachTopicRsp {
}