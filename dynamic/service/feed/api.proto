syntax = "proto3";
package dynamic.service.feed.svr.v1;
import "extension/wdcli/wdcli.proto";
import "dynamic/common/dynamic.proto";
import "dynamic/common/draw.proto";
import "dynamic/common/publish.proto";
import "dynamic/common/opus.proto";
import "cosmo-conn/common/cosmo.proto";
import "cosmo-conn/common/card.proto";
import "google/protobuf/empty.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

option go_package          = "buf.bilibili.co/bapis/bapis-gen/dynamic/service.feed;api";
option java_package        = "com.bapis.dynamic.service.feed";
option java_multiple_files = true;

option (wdcli.appid) = "main.dynamic.feed-service";

service Feed {
    // 压测接口
    // 压测接口：综合页feed_list 刷新
    rpc MelloiGeneralNew(MelloiGeneralNewReq) returns (.google.protobuf.Empty);
    // 压测接口：综合页feed_list 翻页接口
    rpc MelloiGeneralHistory(MelloiGeneralHistoryReq) returns (.google.protobuf.Empty);
    // 压测接口：推inbox
    rpc MelloiPushInbox(MelloiPushInboxReq) returns (.google.protobuf.Empty);

    // 动态卡片物料
    // 获取纯文字，转发卡物料详情
    rpc ListWordText(WordTextReq) returns (WordTextRsp);
    // 获取小程序详情接口
    rpc ListWidget(WidgetReq) returns (WidgetRsp);
    // 通用卡物料接口
    rpc ListCommBiz(CommBizReq) returns (CommBizRsp);

    // 网关调用 - 获取动态更新数量（客户端）
    rpc UpdateNum(UpdateNumReq) returns (UpdateNumResp);
    // 网关调用 - 视频页刷新
    rpc VideoNew(VideoNewReq) returns (VideoNewRsp);
    // 网关调用 - 视频页历史
    rpc VideoHistory(VideoHistoryReq) returns (VideoHistoryRsp);
    // deprecated(已废弃)
    rpc DynVideoPlayList(DynVideoPlayListReq) returns (DynVideoPlayListRsp);
    // deprecated(已废弃)
    rpc SetDynVideoPlayFeedback(SetDynVideoPlayFeedbackReq) returns (SetDynVideoPlayFeedbackRsp);
    // 网关调用 - 动态基本信息
    rpc DynBriefs(DynBriefsReq) returns (DynBriefsRsp);
    // 网关调用 - 视频页最常访问个人feed流
    rpc VideoPersonal(VideoPersonalReq) returns (VideoPersonalRsp);
    // deprecated(已废弃)
    rpc DynPostInfo(DynPostInfoReq) returns (DynPostInfoRsp);
    // 网关调用 - 查看更多-列表
    rpc UpListViewMore(UpListViewMoreReq) returns (UpListViewMoreRsp);
    // 网关调用 - 查看更多-搜索
    rpc UpListSearch(UpListSearchReq) returns (UpListSearchRsp);
    // 网关调用 - web端动态入口及顶栏更新数
    rpc WebEntranceInfo(WebEntranceInfoReq) returns (WebEntranceInfoRsp);
    // 综合页feed_list 刷新
    rpc GeneralNew(GeneralNewReq) returns (GeneralNewRsp);
    // 综合页feed_list 翻页接口
    rpc GeneralHistory(GeneralHistoryReq) returns (GeneralHistoryRsp);
    // 动态feed流推荐序
    rpc GeneralRecommend(GeneralRecommendReq) returns (GeneralRecommendRsp);
    // 未登录态综合页，分区up聚类样式
    rpc UnLogin(UnLoginReq) returns (UnLoginRsp);
    // 未登录态feed流样式
    rpc UnLoginFeed(UnLoginFeedReq) returns (UnLoginFeedRsp);
    // 综合页feed_list 最常访问-快速消费页
    rpc GeneralPersonal(VideoPersonalReq) returns (VideoPersonalRsp);
    // 动态网关调用，快消更新进度
    rpc PersonalConsumeOffset(ConsumeOffsetReq) returns (ConsumeOffsetRsp);

    // 推荐up主换一换接口
    rpc ListUpRecommend(UpRecommendReq) returns (UpRecommendRsp);
    // 未登录态轻浏览
    rpc UnloginLight(UnloginLightReq) returns (UnloginLightRsp);
    // 登录态轻浏览
    rpc DynLight(GeneralHistoryReq) returns (GeneralHistoryRsp);
    // 动态空间页历史
    rpc SpaceHistory(SpaceHistoryReq) returns (SpaceHistoryRsp);
    // 动态详情页
    rpc DynDetail(DynDetailReq) returns (DynDetailRsp);
    // 评论侧专用的评论动态详情页
    rpc DynDetailForReply(DynDetailReq) returns (DynDetailRsp);
    // 审核后台专用的动态详情接口
    rpc DynDetailForAudit(DynDetailForAuditReq) returns (DynDetailRsp);
    // 动态详情页-点赞列表
    rpc LikeList(LikeListReq) returns (LikeListRsp);
    // 动态详情页-转发列表
    rpc RepostList(RepostListReq) returns (RepostListRsp);
    // deprecated(已废弃)
    rpc HotRepostList(RepostListReq) returns (RepostListRsp);
    // tv版最常访问头像  —— Deprecated：功能下线
    rpc TvUpList(TvUpListReq) returns (TvUpListRsp);
    // tv版最常访问-全部视频页
    rpc TvFeed(TvFeedReq) returns (TvFeedRsp);
    // tv版最常访问-up主视频页/追番追剧页/直播页  —— Deprecated：功能下线
    rpc TvPersonal(TvPersonalReq) returns (TvPersonalRsp);
    // deprecated(已废弃)
    rpc GeneralFriends(GeneralFriendsReq) returns (GeneralFriendsRsp);
    // 动态简要信息，若动态id非法或不存在，则其不在回包中；
    rpc DynSimpleInfos(DynSimpleInfosReq) returns (DynSimpleInfosRsp);
    // 动态信息，SimpleInfos的升级版，若动态id非法或不存在，则其不在回包中；需要传入登陆态uid，且仅可见动态才会返回内容；
    rpc DynInfos(FetchDynInfosReq) returns (FetchDynInfosRsp);
    // 根据(type, rid)数组拉取对应DynId数组
    rpc FetchDynIdByRevs(FetchDynIdByRevsReq) returns (FetchDynIdByRevsRsp);
    // 垂搜搜索接口 网关调用  —— Deprecated：功能下线
    rpc Search(SearchReq) returns (SearchRsp);
    // 个人空间页搜索
    rpc PersonalSearch(PersonalSearchReq) returns (PersonalSearchResp);
    // @用户推荐列表
    rpc AtList(dynamic.AtListReq) returns (dynamic.AtListRsp);
    // @用户搜索列表
    rpc AtSearch(dynamic.AtSearchReq) returns (dynamic.AtListRsp);
    // 记录最近@的人
    rpc RecentAtAdd(dynamic.RecentAtAddReq) returns (.google.protobuf.Empty);
    // 最近@过的用户列表
    rpc RecentAtList(RecentAtListReq) returns (RecentAtListRsp);
    // 最常访问头像 网关调用
    rpc MixUpList(MixUpListReq) returns (MixUpListRsp);
    // 更多的最常访问头像（最常访问头像第二页） 网关调用
    rpc MoreUpList(MoreUpListReq) returns (MoreUpListRsp);
    // 视频页最常访问头像 网关调用
    rpc VideoUpList(VideoUpListReq) returns (VideoUpListRsp);
    // 动态list页story模式
    rpc GeneralStory(GeneralStoryReq) returns (GeneralStoryRsp);
    // 个人空间页story模式
    rpc SpaceStory(SpaceStoryReq) returns (SpaceStoryRsp);
    // 横插卡story模式
    rpc InsertedStory(InsertedStoryReq) returns (InsertedStoryRsp);
    // 网关调用 获取空间动态计数
    rpc SpaceNum(SpaceNumReq) returns (SpaceNumRsp);
    // 网关调用 - web获取更新数
    rpc WebUpdateNum(WebUpdateNumReq) returns (WebUpdateNumRsp);
    // 网关调用 - web刷新动态
    rpc WebNew(WebNewReq) returns (WebNewRsp);
    // 网关调用 - web端动态历史
    rpc WebHistory(WebHistoryReq) returns (WebHistoryRsp);
    // 网关调用 - web视频页最常访问个人feed流
    rpc WebPersonal(WebPersonalReq) returns (WebPersonalRsp);
    // 网关调用 - web正在直播
    rpc WebLiveUpList(WebLiveUsersReq) returns (WebLiveUsersRsp);
    // 网关调用 - web最常访问头像
    rpc WebUpList(WebUpListReq) returns (WebUpListRsp);
    // 网关调用 - web 更多的最常访问头像（最常访问头像第二页）
    rpc WebMoreUpList(WebMoreUpListReq) returns (WebMoreUpListRsp);
    // 网关调用 - web未登录热门动态
    rpc WebUnlogin(WebUnloginReq) returns (WebUnloginRsp);
    // 动态筛选器接口 —— Deprecated：功能在新版本下线
    rpc FeedFilter(FeedFilterReq) returns (FeedFilterRsp);
    // 获取单个动态配文
    rpc FetchDynamicContent(FetchDynamicContentReq) returns (FetchDynamicContentRsp);
    // 批量获取动态物料；默认只返回可见内容，获取不可见内容时需要指定参数
    rpc FetchDynMaterials(FetchDynMaterialsReq) returns (FetchDynMaterialsRsp);
    // 天马网关调用 - 获取动态基本信息
    rpc FetchPegasusFeedInfo(FetchPegasusFeedInfoReq) returns (FetchPegasusFeedInfoRsp);
    // 查询动态冻结状态
    rpc QueryDynFrozenStatus(QueryDynFrozenStatusReq) returns (QueryDynFrozenStatusRsp);
    // 获取商单动态信息
    rpc FetchCommercialDynInfo(FetchCommercialDynInfoReq) returns (FetchCommercialDynInfoRsp);
    // 获取互动列表
    rpc InteractList(InteractListReq) returns (InteractListRsp);
    // 我的tab原创内容 - 已废弃
    rpc OriginalList(OriginalListReq) returns (OriginalListRsp);
    // 评论动态点赞 - 评论专用
    rpc CommentDynamicThumb(CommentDynamicThumbReq) returns (CommentDynamicThumbRsp);
    // 评论动态点赞状态 - 评论专用
    rpc CommentDynamicThumbStates(CommentDynamicThumbStatesReq) returns (CommentDynamicThumbStatesRsp);
    // 获取关注的up的未读的视频信息，网关调用(来源于天马推荐页的推荐up banner) - 已废弃
    rpc UpVideoUnreadInfo(UpVideoUnreadInfoReq) returns (UpVideoUnreadInfoRsp);
    // 动态详情 - 搜索网关专用
    rpc DynamicDetailForSearch(DynamicDetailForSearchReq) returns (DynamicDetailForSearchRsp);
    // 内网，图片可见性修改
    rpc ModifyPicMeta(ModifyPicMetaReq) returns (ModifyPicMetaRsp);
    // 获取动态的专属充电信息 - 增值调用
    rpc DynOnlyFansInfo(DynOnlyFansInfoReq) returns (DynOnlyFansInfoRsp);
    // 充电权益中间页，增值调用 - 已废弃
    rpc UpowerSpace(UpowerSpaceReq) returns (UpowerSpaceRsp);
    // 充电权益中间页动态列表，增值调用 - 已废弃
    rpc UpowerSpaceDyns(UpowerSpaceDynsReq) returns (UpowerSpaceDynsRsp);
    // 充电权益中间页抽奖列表，增值调用 - 已废弃
    rpc UpowerSpaceLotteries(UpowerSpaceLotteriesReq) returns (UpowerSpaceLotteriesRsp);

    // 拉取标准化的图文卡片信息
    rpc OpusCard(OpusCardReq) returns (OpusCardRsp);
    // 拉取标准化的图文卡片信息 - for tab3创作中心
    rpc OpusCardForCreative(OpusCardReq) returns (OpusCardRsp);
    // 个人空间-投稿-图文tab-图文数
    rpc SpaceOpusCount(SpaceOpusCountReq) returns (SpaceOpusCountRsp);
    // 个人空间-投稿-图文tab
    rpc SpaceOpusList(SpaceOpusListReq) returns (SpaceOpusListRsp);
    // 创作中心-内容管理-我的图文列表
    rpc CreationMyOpusList(CreationMyOpusReq) returns (CreationMyOpusRsp);
    // 创作中心-内容管理-我的图文详情（from 列表点击）
    rpc CreationMyOpusDetail(CreationMyOpusDetailReq) returns (CreationMyOpusDetailRsp);
    // 创作中心 - for 悬赏平台图文管理列表
    rpc CreationListForReward(CreationListForRewardReq) returns (CreationListForRewardRsp);
    // 获取动态侧访问时间(支持OGV&合集)
    rpc FetchFavSeasonACTime(FetchFavSeasonACTimeReq) returns (FetchFavSeasonACTimeRsp);
    // 消除追更红点(包括OGV&合集)
    rpc ConsumeFavSeasonRedDot(ConsumeFavSeasonRedDotReq) returns (ConsumeFavSeasonRedDotRsp);
    // deprecated(已废弃)
    rpc FavSeasonHasUpdate(FavSeasonHasUpdateReq) returns (FavSeasonHasUpdateRsp);
    // push调用 - 获取动态更新数量 (纯净版)
    rpc DynUpdateNum(DynUpdateNumReq) returns (DynUpdateNumRsp);
    // 获取个人动态列表(仅返回所需数据,仅返回三个月内的可见动态,一页最多返回30条数据) -- up主创作中心调用
    rpc FetchPersonalDyns(FetchPersonalDynsReq) returns (FetchPersonalDynsRsp);
    // 获取用户与关注up主的亲密度
    rpc GetRelationScore(GetRelationScoreReq) returns (GetRelationScoreRsp);

    // 获取用户定时发布列表--网关调用
    rpc TimingDynList(TimingDynListReq) returns (TimingDynListRsp);
    // 获取用户定时发布详情--网关调用
    rpc TimingDynDetail(TimingDynDetailReq) returns (TimingDynDetailRsp);

    // 小组件更新动态
    rpc WidgetUpdateDyns(WidgetUpdateDynsReq) returns (WidgetUpdateDynsRsp);
}

message CreationListForRewardReq {
    int64 login_uid                  = 1;  // 用户登录id
    Page page                        = 2;
    SupportedOption supported_option = 3;
}

message CreationListForRewardRsp {
    repeated int64 opusIds = 1;  // 动态id列表
    Page page              = 2;
}

message DynUpdateNumReq {
    int64 uid = 1;  // 用户uid
}

message DynUpdateNumRsp {
    int64 update_num = 1;  // 更新数
}

// 创作中心-内容管理-我的图文详情（from 列表点击）请求
// 支持新图文类型：dynType = [2 4 64]
message CreationMyOpusDetailReq {
    int64 login_uid           = 1;  // 用户登录id
    .opus.OpusMeta opus_meta  = 2;  // 图文内容主键
    dynamic.MetaDataCtrl meta = 3;  // 版本控制元信息
}

message CreationMyOpusDetailRsp {
    dynamic.DynBrief brief               = 1;  // 仅标号[1-5]保证有效；未公开过的专栏，dynID无效
    .opus.Opus opus                      = 2;  // 图文作品
    .opus.OpusSummary summary            = 3;  // 客户端进入详情页，转发等后续操作需要
    .opus.OpusCollection opus_collection = 4;  // 图文作品集
    FilterGroup filter_group             = 5;  // 所属筛选项分组 //CreationFilterType
}

// 创作中心-内容管理-我的图文列表请求
message CreationMyOpusReq {
    int64 login_uid                  = 1;  // 用户登录id
    Page page                        = 2;
    SupportedOption supported_option = 3;
    dynamic.MetaDataCtrl meta        = 4;  // 版本控制元信息
}

enum CreationFilterType {
    ALL     = 0;
    PENDING = 1;  // 审核中、定时发布
    PUBLIC  = 2;  // 已通过、公开
    FORBID  = 3;  // 未通过
}

message SupportedOption {
    // 筛选项
    CreationFilterType filter_type = 1;
}

message Page {
    int32 pn    = 1;
    int32 ps    = 2;
    int64 total = 3;
}

message FilterGroup {
    CreationFilterType filter_type = 1;  // 非CreationFilterType_ALL
    string reason                  = 2;  // 筛选分类的原因展示文本
}

// 支持新图文类型：dynType = [2 4 64]；非创作者，且不可于创作端露出内容，不返回数据
message CreationOpusCard {
    dynamic.DynBrief brief              = 1;  // brief不包含全字段，仅标号[1-5]保证有效； 未公开过的专栏，dynID无效
    opus.OpusSummary opus_summary       = 2;  // 图文列表展示内容
    opus.OpusCollection opus_collection = 3;  // 图文文集
    FilterGroup filter_group            = 4;  // 所属筛选项分组 //CreationFilterType
}
// 列表展示的各种筛选类型的数字
message Classification {
    CreationFilterType filter_type = 1;
    int64 count                    = 2;
}
// ID列表+标题
message CreationMyOpusRsp {
    repeated Classification classification  = 1;  // 筛选项对应数字
    repeated CreationOpusCard creation_opus = 2;
    Page page                               = 3;  // 返回当前page
}
message SpaceOpusCountReq {
    int64 uid      = 1;  // 访问者的uid，没设置表示客态
    int64 host_uid = 2;  // 被访问者，也就是空间主人的uid
}

message SpaceOpusCountRsp {
    int64 count      = 1;  // 图文数量
    int64 note_count = 2;  // 笔记数量
}

message SpaceOpusListReq {
    int64 uid                            = 1;  // 访问者的uid，没设置表示客态
    int64 host_uid                       = 2;  // 被访问者，也就是空间主人的uid
    string offset                        = 3;  // 动态偏移offset
    dynamic.MetaDataCtrl meta            = 4;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 5;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_info = 6;  // 关注链信息：只需要传host_uid对应的关注数据即可，需要关注链获取点赞外露&进行充电权益判断
    repeated string type_list            = 7;  // 动态类型列表，(用作筛选项 type_list={"2"}动态, type_list={"64"}专栏 type_list={"2","64"}或type_list={}全部)
}

message SpaceOpusListRsp {
    string offset                  = 1;  // 下一页的动态偏移
    bool has_more                  = 2;  // 是否还有更多动态
    repeated dynamic.DynBrief dyns = 3;  // 动态基本信息
}

message ModifyPicMetaReq {
    int32 action         = 1;  // 1：删除 2：恢复 3：续期
    repeated string urls = 2;  // 图片列表
    string biz           = 3;  // 业务方标识
}

message ModifyPicMetaRsp {
}

// 获取商单动态信息请求
message FetchCommercialDynInfoReq {
    int64 dyn_id                  = 1;  // 动态id
    dynamic.DynRevsId dyn_revs_id = 2;  // 反查动态id；动态类型和rid意义见文档：https://info.bilibili.co/pages/viewpage.action?pageId=13358955
}

// 获取商单动态信息返回
message FetchCommercialDynInfoRsp {
    int64 dyn_id                       = 1;   // 动态id
    int64 type                         = 2;   // 动态类型，文档:https://info.bilibili.co/pages/viewpage.action?pageId=13358955
    int64 rid                          = 3;   // 资源id
    int64 s_type                       = 4;   // 动态子类型
    string content                     = 5;   // 动态内容
    repeated draw.PictureInfo pictures = 6;   // 图文动态的全部图片
    int64 ctime                        = 7;   // 创建时间
    int64 mid                          = 8;   // 发布者uid
    bool is_deleted                    = 9;   // 是否删除
    bool in_audit                      = 10;  // 是否在审核中
    bool visible                       = 11;  // 是否可见
    int64 commercial_entity_id         = 12;  // 商单id
    int64 origin_dyn_id                = 13;  // 源动态id
    CommercialEditInfo edit_info       = 14;  // 商单动态编辑信息
}

// 商单动态编辑信息
message CommercialEditInfo {
    int64 edit_time                = 1;  // 商单动态编辑时间
    SnapshotPreVersion pre_version = 2;  // 商单动态前一版本信息
}

// 商单动态前一版本信息
message SnapshotPreVersion {
    string title                       = 1;  // 前一版本标题
    string content                     = 2;  // 前一版本内容
    repeated draw.PictureInfo pictures = 3;  // 前一版本图片
}

// 批量获取动态物料请求
message FetchDynMaterialsReq {
    repeated int64 dyn_ids = 1 [(gogoproto.moretags) = 'validate:"required,gt=0,max=20"'];  // 动态id列表
    bool fetch_invisible   = 2;                                                             // 是否拉不可见内容；
}

// 动态物料
message MaterialCard {
    int64 type                         = 1;  // 动态类型，文档:https://info.bilibili.co/pages/viewpage.action?pageId=13358955
    int64 rid                          = 2;  // 业务方id
    string cover                       = 3;  // 封面或第一张图
    string title                       = 4;  // 标题
    string content                     = 5;  // 业务方文本内容
    repeated draw.PictureInfo pictures = 6;  // 图文动态的全部图片
}

// 批量获取动态物料返回
message FetchDynMaterialsRsp {
    map<int64, MaterialCard> material_cards  = 1;  // 动态物料：key动态id，value动态物料
    map<int64, MaterialCard> invisible_cards = 2;  // 不可见的动态物料：key动态id，value动态物料
}

// 动态信息接口，DynSimpleInfos的升级版
message FetchDynInfosReq {
    message DynInfoOption {
        bool stats     = 1;  // 需要拉取动态各种计数：转发数、评论数、点赞数、浏览数、举报数等；
        bool content   = 2;  // 需要拉取动态内容：标题、文本、图片等；仅可见动态才会返回内容；
        bool at        = 3;  // 需要拉取动态中@的uid列表
        bool only_fans = 4;  // 需要专属动态,如果需要，必须在入参带上uid，以判断可见性
        bool raw_url   = 5;  // 正文中返回原始url，而不是showText
    }
    repeated int64 dyn_ids                 = 1;  // 需要查询的批量动态id
    repeated dynamic.DynRevsId dyn_revs_id = 2;  // 批量反查动态id；动态类型和rid意义见文档：https://info.bilibili.co/pages/viewpage.action?pageId=13358955
    int64 uid                              = 3;  // 登陆态用户uid，用来判断可见性
    DynInfoOption option                   = 4;  // 附加选项：支持拉取除index信息之外的其他内容
}
// 动态简单信息
message DynInfo {
    DynSimpleInfo simple_info  = 1;
    MaterialCard card          = 2;
    DynExtensionInfo extension = 3;  // 动态扩展信息
};
message DynExtensionInfo {
    repeated int64 at_uids = 1;  // 动态中@的uid列表
    string jump_uri        = 2;  // 跳转链接，通常形如：https://t.bilibili.com/xxxx
    string native_jump_uri = 3;  // native的跳转链接，通常形如：bilibili://xxxx
}
// 批量获取动态信息返回
message FetchDynInfosRsp {
    map<int64, DynInfo> dyn_infos       = 1;  // 动态信息：key：动态id，value：DynInfo动态信息；和请求中的dyn_ids对应；
    map<string, DynInfo> revs_dyn_infos = 2;  // 动态信息：key：字符串，"type_rid"，value：DynInfos动态信息；和请求中的revs_ids对应；
}

message PersonalSearchReq {
    string keywords           = 1;  // 搜索文本
    int32 pn                  = 2;  // 页码
    int32 ps                  = 3;  // 页大小
    int64 mid                 = 4;  // 用户id，没登录的mid=0
    int64 upId                = 5;  // up主id，必填字段
    dynamic.MetaDataCtrl meta = 6;  // 版本控制元信息
}

message PersonalSearchResp {
    repeated SearchDynamic dynamics = 1;  // 动态
    int32 total                     = 2;  // 命中搜索总数
    repeated string tokens          = 3;  // 分词结果
}

message SearchDynamic {
    int64 dynamicId           = 1;  // 动态id
    map<string, string> extra = 2;  // 其他输出信息
}

// 压测接口：推inbox
message MelloiPushInboxReq {
    int64 uid = 1;  // mid
}

// 压测接口：综合页feed_list 刷新请求
message MelloiGeneralNewReq {
    int64 uid = 1;  // mid
}

// 压测接口：综合页feed_list 翻页接口请求
message MelloiGeneralHistoryReq {
    int64 uid             = 1;  // mid
    string history_offset = 2;  // 动态偏移history_offset
}

message DynSimpleInfoOption {
    bool stats     = 1;  // 需要拉动态各种计数：转评赞浏举
    bool only_fans = 2;  // 充电动态也正常返回simpleInfo信息
}

// 动态简单信息请求
message DynSimpleInfosReq {
    repeated int64 dyn_ids                 = 1;  // 需要查询的批量动态id
    repeated dynamic.DynRevsId dyn_revs_id = 2;  // 批量反查动态id；动态类型和rid意义见文档：https://info.bilibili.co/pages/viewpage.action?pageId=13358955
    int64 uid                              = 3;  // 登陆态用户uid，用来判断可见性
    DynSimpleInfoOption option             = 4;  // 附加选项：支持拉取除index信息之外的其他内容
}

// 动态简要信息返回
message DynSimpleInfosRsp {
    map<int64, DynSimpleInfo> dyn_simple_infos = 1;  // key:dyn_id,value:DynSimpleInfo
}

// 动态简单信息
message DynSimpleInfo {
    int64 dyn_id           = 1;   // 动态id
    int64 ctime            = 2;   // 动态发布时间
    int64 rid              = 3;   // 业务方id，和请求中rids字段对应
    int64 type             = 4;   // 动态支持的业务方类型，和请求中的type字段对应
    int64 uid              = 5;   // 动态所属uid
    int32 uid_type         = 6;   // 动态所属uid的类型
    DynSimpleInfo origin   = 7;   // 转发动态的源动态信息；仅转发动态才有此字段
    bool visible           = 8;   // 动态状态是否可见（只从acl判断可见性，不特殊处理场景和充电等特殊逻辑）
    dynamic.DynStats stats = 9;   // 动态各种计数：转评赞浏举；注：接口不提供origin的计数信息
    bool in_audit          = 10;  // 动态是否在审核中
    bool index_only        = 11;  // 动态仅详情页可见，不进分发渠道
    int64 stype            = 12;  // 动态子类型，详见文档：https://info.bilibili.co/pages/viewpage.action?pageId=13358955
    bool only_fans         = 13;  // 动态是否是充电动态
};

// tv版最常访问头像请求
message TvUpListReq {
    int64 uid                            = 1;  // mid
    dynamic.VersionCtrlMeta version_ctrl = 2;  // 版本控制元信息
}

enum TvUpType {
    UP_TYPE_LIVE  = 0;  // 0直播
    UP_TYPE_UP    = 1;  // 1普通up
    UP_TYPE_MOVIE = 2;  // 2追剧
    UP_TYPE_COMIC = 3;  // 3追番
}

message TvUpItem {
    int64 uid  = 1;  // mid
    int32 type = 2;  // 类型-TvUpType
}

// tv版最常访问头像返回
message TvUpListRsp {
    repeated TvUpItem list = 1;
    int32 tv_sort_strategy = 2;  // 实验分组；0基线：按照更新时间排序，1粉版，2自定义：按照Q值排序
}

message TvFeedReq {
    int64 uid                            = 1;  // 访问者uid，即登录态uid
    string offset                        = 2;  // 第一页传空；非第一页传上次返回的offset
    dynamic.VersionCtrlMeta version_ctrl = 3;  // 版本控制元信息
}

message TvFeedRsp {
    bool has_more                  = 1;  // 是否还有更多
    string offset                  = 2;  // 下一页的动态偏移
    repeated dynamic.DynBrief dyns = 3;  // 动态基本信息列表
}

message TvPersonalReq {
    int64 uid                            = 1;  // 访问者uid，即登录态uid
    TvUpItem up_item                     = 2;  // 被访问者uid+type
    string offset                        = 3;  // 第一页传空；非第一页传上次返回的offset
    dynamic.VersionCtrlMeta version_ctrl = 4;  // 版本控制元信息
}

message TvPersonalRsp {
    bool has_more                  = 1;  // 是否还有更多
    string offset                  = 2;  // 下一页的动态偏移
    repeated dynamic.DynBrief dyns = 3;  // 动态基本信息列表
}

message GeneralFriendsReq {
    int64 uid                            = 1;  // 登录用户uid
    string offset                        = 2;  // 动态偏移offset
    repeated string type_list            = 3;  // 动态类型列表
    dynamic.VersionCtrlMeta version_ctrl = 4;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 5;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_info = 6;  // 关注链信息（包括登录用户自己）
}

message GeneralFriendsRsp {
    string history_offset          = 1;  // 下一页的动态偏移
    bool has_more                  = 2;  // 是否还有更多动态
    repeated dynamic.DynBrief dyns = 3;  // 动态基本信息列表
    dynamic.FoldInfo fold_info     = 4;  // 折叠信息
}

// 查看更多-列表请求
message UpListViewMoreReq {
    int64 uid                            = 1;  // mid
    int32 sort_type                      = 2;  // 排序策略：1推荐排序，2最常访问，3最近关注，其他值为默认排序
    dynamic.VersionCtrlMeta version_ctrl = 3;  // 版本控制元信息
}

// 直播信息
message UpListLiveItem {
    int32 state   = 1;  // 直播状态，0未直播 1直播中
    int64 room_id = 2;  // 房间号
    string link   = 3;  // 直播链接
}

// 查看更多-列表单条数据
message UpListItem {
    int64 uid                = 1;  // mid
    int32 speacial_attention = 2;  // 特别关注，0否 1是
    int32 reddot_state       = 3;  // 小红点状态，0没有 1有
    UpListLiveItem live_info = 4;  // 直播信息
    int32 premiere_state     = 5;  // 首映状态，0非首映状态 1首映状态
    int64 avid               = 6;  // 首映状态对应的稿件id
}

// 排序类型
message SortType {
    int32 sort_type       = 1;  // 排序策略：1推荐排序，2最常访问，3最近关注
    string sort_type_name = 2;  // 排序策略名称
}

// 查看更多-列表返回
message UpListViewMoreRsp {
    repeated UpListItem items    = 1;
    repeated SortType sort_types = 2;  // 排序类型列表
    bool show_more_sort_types    = 3;  // 是否展示更多的排序策略
    int32 default_sort_type      = 4;  // 默认排序策略
}

// 查看更多-搜索请求
message UpListSearchReq {
    int64 mid                            = 1;  // mid
    string name                          = 2;  // key
    string real_ip                       = 3;  // ip
    dynamic.VersionCtrlMeta version_ctrl = 4;  // 版本控制元信息
}

// 查看更多-搜索返回
message UpListSearchRsp {
    repeated UpListItem items = 1;
}

enum TabType {
    INVALID_TAB_TYPE = 0;
    TAB_TYPE_GENERAL = 1;
    TAB_TYPE_VIDEO   = 2;
}

message OffsetInfo {
    int32 tab        = 1;
    string type_list = 2;
    string offset    = 3;
}

message UpdateNumReq {
    int64 uid                            = 1;
    repeated OffsetInfo offsets          = 2;
    dynamic.VersionCtrlMeta version_ctrl = 3;
    int64 new_install                    = 4;  // 是否是首次安装启动 1-是 0-否
    repeated int64 recent_follows        = 5;  // 最近关注up_mid列表，FIFO，新关注的UP在末尾
}

message UpdateNumResp {
    string red_type         = 1;  // 红点类型 - count-数字红点 point-普通红点 no_point-没有红点，@Deprecated 弃用，仅依赖StyleInfo
    uint64 update_num       = 2;  // 更新数，不做展示逻辑使用，展示仅依赖StyleInfo
    string default_tab      = 3;
    StyleInfo special_style = 4;  // 下发样式
    BubbleInfo bubble       = 5;  // 气泡
}

enum BgType {
    BG_TYPE_DEFAULT = 0;  // 默认样式 风车
    BG_TYPE_FACE    = 1;  // 头像样式
}

enum CornerType {
    CORNER_TYPE_TEXT      = 0;  // 角标样式 文案类型
    CORNER_TYPE_ANIMATION = 1;  // 角标样式 动效类型
    CORNER_TYPE_STATIC    = 2;  // 角标样式 静态类型
    CORNER_TYPE_RED_DOT   = 3;  // 角标样式 小红点
    CORNER_TYPE_NUMBER    = 4;  // 角标样式 数字，样式内容还是按照文案角标下发
}

// @Deprecated 弃用
enum StyleType {
    STYLE_TYPE_NONE   = 0;
    STYLE_TYPE_LIVE   = 1;  // 直播用户
    STYLE_TYPE_DYN_UP = 2;  // 动态up
}

enum FaceType {
    FACE_TYPE_NONE          = 0;
    FACE_TYPE_LIVE          = 1;  // 直播用户
    FACE_TYPE_DYN_UP        = 2;  // 动态up
    FACE_TYPE_OGV_UP        = 3;  // ogv头像
    FACE_TYPE_COLLECTION_UP = 4;  // 合集头像
}

enum BubbleModuleType {
    BUBBLE_MODULE_NONE    = 0;
    BUBBLE_MODULE_UP      = 1;  // up主头像
    BUBBLE_MODULE_CONTENT = 2;  // 普通文案
    BUBBLE_MODULE_TIP     = 3;  // 提示文案
    BUBBLE_MODULE_PIC     = 4;  // 图片
}

enum TipIconType {
    TIP_ICON_NONE      = 0;  // 无效值，表示没有icon
    TIP_ICON_STATIC    = 1;  // 静态图
    TIP_ICON_ANIMATION = 2;  // 动效图
}

message Color {
    string color_day   = 1;  // 日间颜色
    string color_night = 2;  // 夜间颜色
}

message StyleInfo {
    BgType bg_type         = 1;  // 1:背景为默认风车 2: 背景为用户头像
    CornerType corner_type = 2;  // 角标类型，@Deprecated 弃用，见CornerInfo
    int32 display_time     = 3;  // 样式展示时间 单位:秒 为0时不消失
    string corner_mark     = 4;  // 角标文案，@Deprecated 弃用，见CornerInfo
    UserInfo user          = 5;  // 用户头像属性，当bg_type=2有效
    StyleType type         = 6;  // 头像样式类型，@Deprecated 弃用，见UserInfo
    string track_id        = 7;  // 点击事件关联id
    CornerInfo corner      = 8;  // 角标

    message UserInfo {
        int64 uid          = 1;  // 用户id
        string face        = 2;  // 头像
        FaceType type      = 3;  // 头像样式类型
        Color border_color = 4;  // 边框颜色
    }
    message CornerInfo {
        CornerType corner_type = 1;  // 角标类型
        oneof corner_item {
            CornerContent corner_content = 2;  // 文案角标
            string corner_icon           = 3;  // 图标角标
        }
    }
    message CornerContent {
        string corner_text      = 1;  // 角标文案
        Color corner_text_color = 2;  // 角标文案颜色
        Color corner_bg_color   = 3;  // 角标背景色
    }
}

message BubbleInfo {
    repeated BubbleModule modules = 1;
    string track_id               = 2;  // 上报关联id
    string bubble_recall_content  = 3;  // 命中低关注策略下发的关联内容

    message BubbleModule {
        BubbleModuleType module_type = 1;
        oneof module_item {
            BubbleModuleUp ups          = 2;  // up主头像
            BubbleModuleContent content = 3;  // 文案
            BubbleModuleTip tip         = 4;  // 提示语
            BubbleModulePicture pic     = 5;  // 图片
        }
    }

    message BubbleModuleUp {
        repeated UserInfo users = 1;  // 用户头像
    }

    message BubbleModuleContent {
        string content = 1;  // 文案
    }

    message BubbleModuleTip {
        TipIconType icon_type = 1;  // 图标类型
        string icon           = 2;  // 图标
        string text           = 3;  // tip文案
        Color text_color      = 4;  // tip文案颜色
    }

    message BubbleModulePicture {
        string pic_biz_type         = 1;  // 图片业务属性，埋点上报使用
        string pic_url_day          = 2;  // 日间图片url
        string pic_url_night        = 3;  // 日间图片url
        int64 pic_width             = 4;  // 图片宽度
        int64 pic_height            = 5;  // 图片高度
        bool rounded_corner         = 6;  // 是否圆角
        int64 rounded_corner_radius = 7;  // 圆角半径 单位px
    }
}

message VideoNewReq {
    int64 uid                            = 1;  // 用户uid
    string update_baseline               = 2;  // 当前客户端缓存的最新一条动态id；用来计算回包中的update_num，表示从该动态id之后有多少条更新;
    string assist_baseline               = 3;  // 视频页的时候传递综合页当前更新的最大值
    repeated string type_list            = 4;  // 动态类型列表
    dynamic.VersionCtrlMeta version_ctrl = 5;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 6;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_info = 7;  // 关注链信息
}

message VideoNewRsp {
    int64 update_num               = 1;  // 动态更新数量
    string history_offset          = 2;  // 下一页（history接口）的动态偏移
    string update_baseline         = 3;  // 下次刷新时传入
    bool has_more                  = 4;  // 是否还有更多
    repeated dynamic.DynBrief dyns = 5;  // 动态基本信息列表
    dynamic.FoldInfo fold_info     = 6;  // 折叠信息
}

message VideoHistoryReq {
    int64 uid                            = 1;  // 用户uid
    string offset                        = 2;  // 动态偏移
    int64 page                           = 3;  // 用户浏览到哪一页
    repeated string type_list            = 4;  // 动态类型列表
    dynamic.VersionCtrlMeta version_ctrl = 5;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 6;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_info = 7;  // 关注链信息
}

message VideoHistoryRsp {
    string history_offset          = 1;  // 下一页（history接口）的动态偏移
    bool has_more                  = 2;  // 是否还有更多
    repeated dynamic.DynBrief dyns = 3;  // 动态基本信息列表
    dynamic.FoldInfo fold_info     = 4;  // 折叠信息
}

message TabRecallUp {
    int64 tab_recall_uid         = 1;  // 底tab召回的up主uid
    StyleType tab_recall_type    = 2;  // 底tab召回的样式类型
    string track_id              = 3;  // 点击事件关联id
    string bubble_recall_content = 4;  // 端上关注-气泡曝光后回传的承接内容
}

message GeneralNewReq {
    int64 uid                            = 1;  // 登录用户uid
    string update_baseline               = 2;  // 当前客户端缓存的最新一条动态id；用于计算回包中更新数update_num = list(dyn_id>update_baseline).len;
    repeated string type_list            = 3;  // 动态类型列表
    dynamic.VersionCtrlMeta version_ctrl = 4;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 5;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_info = 6;  // 关注链信息（包括登录用户自己）
    RcmdUPsParam rcmd_ups_param          = 7;  // 推荐up主入参
    AdParam ad_param                     = 8;  // 广告参数
    TabRecallUp tab_recall               = 9;  // 底tab头像召回up主信息
}

message StoryUPCard {
    int64 pos                              = 1;  // 出现在feed流列表的插入位置
    repeated dynamic.StoryUpItem story_ups = 2;  // story横插卡up主列表
    bool show_publish_entrance             = 3;  // story横插卡中是否需要出现发布入口
    dynamic.StoryFoldInfo fold_info        = 4;  // story横插卡折叠信息
    dynamic.StoryPublishInfo publish_info  = 5;  // story横插卡发布入口信息
    string title                           = 6;  // story横插卡标题
}

message GeneralNewRsp {
    int64 update_num               = 1;  // 动态更新数量
    string history_offset          = 2;  // 下一页（GeneralHistory接口）的动态偏移
    string update_baseline         = 3;  // 下次请求时传入, 用于计算回包中更新数update_num
    bool has_more                  = 4;  // 是否还有更多动态
    repeated dynamic.DynBrief dyns = 5;  // 动态基本信息列表
    dynamic.FoldInfo fold_info     = 6;  // 折叠信息
    RcmdUPCard rcmd_ups            = 7;  // 推荐关注用户
    StoryUPCard story_up_card      = 8;  // story横插卡
}

message GeneralHistoryReq {
    int64 uid                            = 1;  // 登录用户uid
    string history_offset                = 2;  // 动态偏移history_offset
    int64 page                           = 3;  // 用户浏览到哪一页
    repeated string type_list            = 4;  // 动态类型列表
    AdParam ad_param                     = 5;  // 广告参数
    dynamic.VersionCtrlMeta version_ctrl = 6;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 7;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_info = 8;  // 关注链信息（包括登录用户自己）
}

message GeneralHistoryRsp {
    string history_offset          = 1;  // 下一页（GeneralHistory接口）的动态偏移
    bool has_more                  = 2;  // 是否还有更多动态
    repeated dynamic.DynBrief dyns = 3;  // 动态基本信息列表
    dynamic.FoldInfo fold_info     = 4;  // 折叠信息
}

message WebNewReq {
    int64 uid                            = 1;  // 登录用户uid
    string update_baseline               = 2;  // 当前客户端缓存的最新一条动态id；用于计算回包中更新数update_num = list(dyn_id>update_baseline).len;
    repeated string type_list            = 3;  // 动态类型列表
    dynamic.FeedInfoCtrl info_ctrl       = 4;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_info = 5;  // 关注链信息（包括登录用户自己）
    dynamic.MetaDataCtrl meta            = 6;  // 版本控制元信息
}

message WebNewRsp {
    int64 update_num               = 1;  // 动态更新数量
    string history_offset          = 2;  // 下一页（GeneralHistory接口）的动态偏移
    string update_baseline         = 3;  // 下次请求时传入, 用于计算回包中更新数update_num
    bool has_more                  = 4;  // 是否还有更多动态
    repeated dynamic.DynBrief dyns = 5;  // 动态基本信息列表
    dynamic.FoldInfo fold_info     = 6;  // 折叠信息
}

message WebHistoryReq {
    int64 uid                            = 1;  // 登录用户uid
    string history_offset                = 2;  // 动态偏移history_offset
    int64 page                           = 3;  // 用户浏览到哪一页
    repeated string type_list            = 4;  // 动态类型列表
    dynamic.FeedInfoCtrl info_ctrl       = 5;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_info = 6;  // 关注链信息（包括登录用户自己）
    dynamic.MetaDataCtrl meta            = 7;  // 版本控制元信息
}

message WebHistoryRsp {
    string history_offset          = 1;  // 下一页（GeneralHistory接口）的动态偏移
    bool has_more                  = 2;  // 是否还有更多动态
    repeated dynamic.DynBrief dyns = 3;  // 动态基本信息列表
    dynamic.FoldInfo fold_info     = 4;  // 折叠信息
}

message WebPersonalReq {
    int64 uid                             = 1;  // 访问者uid，即登录态uid
    int64 host_uid                        = 2;  // 被访问者，也就是空间主人的uid
    string offset                         = 3;  // 第一页传空；非第一页传上次返回的next_offset
    dynamic.FeedInfoCtrl info_ctrl        = 4;
    dynamic.AttentionInfo attention_users = 5;
    repeated string type_list             = 6;  // 动态类型列表
    dynamic.MetaDataCtrl meta             = 7;  // 版本控制元信息
}

message WebPersonalRsp {
    bool has_more                  = 1;
    string offset                  = 2;
    string read_offset             = 3;
    repeated dynamic.DynBrief dyns = 4;
    dynamic.FoldInfo fold_info     = 5;
}

message WebLiveUsersReq {
    int64 uid                            = 1;  // mid
    dynamic.AttentionInfo attention_info = 2;  // 关注链信息（包括登录用户自己）
}

message WebLiveUsersRsp {
    repeated WebLiveUpListItem live_up_list = 1;
    int64 following_live_total              = 2;
}

message WebUpListReq {
    int64 uid                            = 1;
    dynamic.AttentionInfo attention_info = 2;  // 关注链信息（包括登录用户自己）
    repeated string type_list            = 3;  // 动态类型列表
}

message WebMoreUpListReq {
    int64 uid                            = 1;
    dynamic.AttentionInfo attention_info = 2;  // 关注链信息（包括登录用户自己）
    repeated int64 first_page_uid_list   = 3;  // 第一页已经获取到的mid_list，去重用，请传入有效值。
    repeated string type_list            = 4;  // 动态类型列表
}

message WebLiveUpListItem {
    int64 uid              = 1;  // 用户信息
    bool is_reserve_recall = 2;  // 是否是预约召回
    string link            = 3;  // 直播间链接
}

message WebUpListItem {
    bool has_update        = 1;  // 是否显示小红点, 仅动态up主使用
    int64 uid              = 2;  // 用户信息
    bool is_reserve_recall = 3;  // 是否是预约召回
}

message WebUpListRsp {
    repeated WebUpListItem up_list = 1;
    repeated int64 uid_list        = 2;  // 头像的uid列表，提供给端上用于请求第二页最常访问头像
    bool has_more_dyn_up_list      = 3;  // 是否有第二页最常访问头像
}

message WebMoreUpListRsp {
    repeated WebUpListItem up_list = 1;
}

message WebUnloginReq {
    int64 fake_uid                 = 1;  // 生成的假uid 用于去重
    bool is_refresh                = 2;  // 是否刷新
    dynamic.FeedInfoCtrl info_ctrl = 3;  // 显示控制
    dynamic.MetaDataCtrl meta      = 4;  // 版本控制元信息
}

message WebUnloginRsp {
    repeated dynamic.DynBrief dyns = 1;  // 动态基本信息列表
    bool has_more                  = 2;  // 是否还有更多
}

message SpaceNumReq {
    int64 uid       = 1;  // 被访问者，也就是空间主人的uid
    int64 login_uid = 2;  // 访问者uid，为0表示客态
}

message SpaceNumRsp {
    int64 dynNum = 1;
}

message WebUpdateNumReq {
    int64 uid                 = 1;
    repeated string type_list = 2;  // 动态类型列表
    string offset             = 3;
}

message WebUpdateNumRsp {
    uint64 update_num = 1;  // 更新数量
}

message DynBriefsReq {
    int64 uid                             = 1;  // 用户id
    repeated int64 dyn_ids                = 2;  // 动态ID列表
    dynamic.VersionCtrlMeta version_ctrl  = 3;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl        = 4;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_users = 5;  // 登录用户的关注链
}

message DynBriefsRsp {
    repeated dynamic.DynBrief dyns = 1;  // 动态基本信息列表
}

message VideoPersonalReq {
    int64 uid                             = 1;  // 访问者uid，即登录态uid
    int64 host_uid                        = 2;  // 被访问者，也就是空间主人的uid
    bool is_preload                       = 3;  // 预加载参数；1：预加载，不更新小红点
    string offset                         = 4;  // 第一页传空；非第一页传上次返回的next_offset
    dynamic.VersionCtrlMeta version_ctrl  = 5;
    dynamic.FeedInfoCtrl info_ctrl        = 6;
    dynamic.AttentionInfo attention_users = 7;
    string footprint                      = 8;   // 头像请求唯一id
    repeated string type_list             = 9;   // 动态类型列表
    dynamic.DynUidType host_uid_type      = 10;  // 被访问者类型
    string track_info                     = 11;  // 埋点用,json字符串,结构体参考message TrackInfo
}

message VideoPersonalRsp {
    bool has_more                  = 1;
    string offset                  = 2;
    string read_offset             = 3;
    repeated dynamic.DynBrief dyns = 4;
    dynamic.FoldInfo fold_info     = 5;
    int64 already_read_offset      = 6;  // ogv或合集上次看到位置
}

// deprecated(已废弃)
message DynVideoPlayListReq {
    int64 uid                      = 1;  // 用户uid
    string offset                  = 2;  // 动态偏移
    repeated string type_list      = 3;  // 动态类型列表
    dynamic.FeedInfoCtrl info_ctrl = 4;  // 动态卡片内容控制
    bool is_first_page             = 5;  // 是否为第一页
}

// deprecated(已废弃)
message DynVideoPlayListRsp {
    string history_offset          = 1;  // 下一页的动态偏移
    bool has_more                  = 2;  // 是否还有更多
    repeated dynamic.DynBrief dyns = 3;  // 动态基本信息列表
    bool show_remove_icon          = 4;  // 是否支持移除
}

// deprecated(已废弃)
message SetDynVideoPlayFeedbackReq {
    int64 uid              = 1;  // 用户uid
    int64 dyn_id           = 2;  // 动态id
    FeedAction feed_action = 3;  // 反馈行为
}

// deprecated(已废弃)
enum FeedAction {
    REMOVE = 0;  // 移除
    REJOIN = 1;  // 重新加入
}

// deprecated(已废弃)
message SetDynVideoPlayFeedbackRsp {
}

message DynPostInfoReq {
    uint64 uid                           = 1;
    repeated dynamic.DynPostItem dyns    = 2;
    dynamic.VersionCtrlMeta version_ctrl = 3;
    dynamic.PostInfoCtrl info_ctrl       = 4;
};

message DynPostInfoRsp {
    map<int64, dynamic.DynPostInfo> dyns = 1;
};

message WebEntranceInfoReq {
    int64 uid            = 1;  // 登录者uid
    int64 video_offset   = 2;  // web端缓存的视频类型的最新动态
    int64 article_offset = 3;  // web端缓存的专栏类型的最新动态
    int64 alltype_offset = 4;  // web端缓存的所有类型的最新动态
    bool need_head_icon  = 5;  // 是否需要最常访问头像
}

message WebEntranceInfoRsp {
    dynamic.IconType icon_type                    = 1;  // 入口图标类型
    int64 show_uid                                = 2;  // 入口图标对应的uid的头像; 在icon需要展示头像时该字段有效
    int64 video_num                               = 3;  // 更新的视频数
    int64 article_num                             = 4;  // 更新的专栏数
    repeated dynamic.DynBasicInfo dyn_basic_infos = 5;  // 更新视频列表
}

message RcmdUPsParam {
    int64 dislike_ts = 1;
}

message AdParam {
    string ad_extra = 1;  // 综合页请求广告所需字段，由客户端-网关透传
}

// 推荐信息
message UpRecommend {
    string reason    = 1;  // 推荐理由
    int64 tid        = 2;
    int64 second_tid = 3;
};

// 推荐用户
message RcmdUser {
    int64 uid             = 1;  // 用户id
    UpRecommend recommend = 2;  // 推荐信息
};

// 低关注推荐up主列表插入feed流的卡片
message RcmdUPCard {
    string track_id                        = 1;  // 接口返回，透传
    int32 type                             = 2;  // 推荐up列表的类型； 	空动态列表 =1；低关注 =2; 分区聚类推荐up+视频（空列表）=3
    int32 pos                              = 3;  // 出现在feed流列表的插入位置
    repeated RcmdUser users                = 4;  // 推荐up主列表
    RcmdOption opts                        = 5;  // 分区聚类推荐选项
    repeated dynamic.RcmdRegion region_ups = 6;  // 分区聚类推荐up+视频
    string server_info                     = 7;  // 透传到客户端的埋点字段。
};

message UnLoginReq {
    dynamic.VersionCtrlMeta app_meta = 1;  // 客户端公共参数，buvid必须有
}

message UnLoginRsp {
    RcmdOption opts                        = 1;  // 分区聚类推荐选项
    repeated dynamic.RcmdRegion region_ups = 2;  // 分区聚类推荐up+视频
    string server_info                     = 3;  // 透传到客户端的埋点字段
}

message UnLoginFeedReq {
    int64 fake_uid  = 1;  // 假uid 用于去重
    bool is_refresh = 2;  // 是否刷新
};
message UnLoginFeedRsp {
    repeated dynamic.DynBrief briefs = 1;  // 旧ui图文推荐流
    bool has_more                    = 2;
};

message RcmdOption {
    bool show_title = 1;  // 视频是否展示标题
}

message WordTextReq {
    int64 uid           = 1;  // 登录用户Uid
    repeated int64 rids = 2;  // 需请求文字内容的纯文字或转发动态
}

message WordTextRsp {
    map<int64, string> content = 1;  // key: rid; value: 动态文本内容
}

message WidgetReq {
    int64 uid           = 1;  // 登录用户Uid
    repeated int64 rids = 2;  // 小程序卡动态的rid
}

message WidgetRsp {
    map<int64, dynamic.ProgramItem> items = 1;  // key: rid; value: 小程序卡详情 WidgetItem
}

message CommBizReq {
    // 登录用户Uid
    int64 uid = 1;
    // 小程序卡动态的rid
    repeated int64 rids = 2;
}

message CommBizEntry {
    int64 rid                = 1;
    dynamic.BizVest vest     = 2;
    dynamic.BizSketch sketch = 3;
    opus.Opus opus           = 4;
}

message CommBizRsp {
    // key: rid; value: 通用卡详情，待废弃
    map<int64, dynamic.BizEntry> items = 1;
    // key: rid; value: 通用卡详情，带opus
    map<int64, CommBizEntry> biz_items = 2;
}

message UpRecommendReq {
    int64 uid                            = 1;  // 登录用户id
    int64 dislikeTs                      = 2;  // 上一次不感兴趣的ts，单位：秒；该字段透传给搜索
    dynamic.VersionCtrlMeta version_ctrl = 3;  // 客户端公共参数
}

message UpRecommendRsp {
    RcmdUPCard rcmd_ups = 1;  // 推荐关注用户
}

message UnloginLightReq {
    int64 fake_uid                       = 1;  // 前端构造的假uid
    string history_offset                = 2;  // 动态偏移，用于过滤
    repeated string type_list            = 3;  // 动态类型列表，用于过滤
    dynamic.VersionCtrlMeta version_ctrl = 4;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 5;  // 动态卡片内容控制
}

message UnloginLightRsp {
    repeated dynamic.DynBrief dyns = 1;  // 动态基本信息
}

message SpaceHistoryReq {
    int64 uid                            = 1;  // 访问者的uid，没设置表示客态
    int64 host_uid                       = 2;  // 被访问者，也就是空间主人的uid
    string history_offset                = 3;  // 动态偏移history_offset
    int64 page                           = 4;  // 用户浏览到哪一页
    dynamic.VersionCtrlMeta version_ctrl = 5;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 6;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_info = 7;  // 关注链信息（包括登录用户自己），需要关注链获取点赞外露
    repeated string type_list            = 8;  // 动态类型列表
}

message SpaceHistoryRsp {
    string history_offset          = 1;  // 下一页的动态偏移
    bool has_more                  = 2;  // 是否还有更多动态
    repeated dynamic.DynBrief dyns = 3;  // 动态基本信息
    dynamic.FoldInfo fold_info     = 4;  // 折叠信息
}

message DynDetailReq {
    int64 uid                            = 1;  // 访问者的uid，没设置表示客态
    int64 host_uid                       = 2;  // 被访问者，也就是空间主人的uid
    int64 dyn_id                         = 3;  // 动态id
    dynamic.DynRevsId dyn_revs_id        = 4;  // 反查动态id
    AdParam ad_param                     = 5;  // 广告参数，用于相关推荐
    dynamic.VersionCtrlMeta version_ctrl = 6;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 7;  // 动态卡片内容控制
}

message DynDetailRsp {
    dynamic.DynBrief dyn          = 1;  // 动态基本信息
    dynamic.RelatedRcmd recommend = 2;  // 相关推荐
}
message DynDetailForAuditReq {
    int64 dyn_id = 1;  // 动态id
}

message LikeListReq {
    int64 uid                            = 1;  // 访问者的uid
    int64 dyn_id                         = 2;  // 动态id
    dynamic.DynRevsId dyn_revs_id        = 3;  // 反查动态id
    int64 uid_offset                     = 4;  // 上一页最后一个uid
    int64 page_number                    = 5;  // 第几页，关注用户会放在第一页最前面
    int64 page_size                      = 6;  // 每页几条
    dynamic.VersionCtrlMeta version_ctrl = 7;  // 版本控制元信息
    dynamic.AttentionInfo attention_info = 8;  // 登录用户的关注链，关注用户会出现在点赞列表的最前面
}

message LikeItem {
    int64 uid    = 1;  // 用户uid
    int64 attend = 2;  // 是否关注：1关注 0不关注 默认0
}

message LikeListRsp {
    repeated LikeItem like_list = 1;  // 点赞列表
    int64 has_more              = 2;  // 是否还有更多
    int64 total_count           = 3;  // 点赞总数
}

message RepostListReq {
    int64 uid                            = 1;  // 访问者的uid
    int64 dyn_id                         = 2;  // 动态id
    dynamic.DynRevsId dyn_revs_id        = 3;  // 反查动态id
    string offset                        = 4;  // 偏移,使用上一页回包中的offset字段；热门转发列表时范围为0-50，普通转发列表时第一页填空
    int64 page_size                      = 5;  // 每页几条
    dynamic.VersionCtrlMeta version_ctrl = 6;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 7;  // 动态卡片内容控制
}

message RepostListRsp {
    string offset                  = 1;  // 偏移
    repeated dynamic.DynBrief dyns = 2;  // 动态基本信息
    int64 has_more                 = 3;  // 是否还有更多
    int64 total_count              = 4;  // 转发总数
}

message FetchDynIdByRevsReq {
    repeated dynamic.DynRevsId dyn_revs_ids = 1;
}

message FetchDynIdByRevsItem {
    dynamic.DynRevsId dyn_revs_id = 1;
    int64 dyn_id                  = 2;
}

message FetchDynIdByRevsRsp {
    repeated FetchDynIdByRevsItem items = 1;
}

// 动态搜索请求
message SearchReq {
    string s                             = 1;  // 搜索关键字
    int32 page_num                       = 2;  // 当前页数
    int32 page_size                      = 3;  // 一页展示条数
    int64 uid                            = 4;  // 用户uid 需要登录态 必传
    dynamic.MetaDataCtrl meta            = 5;  // 版本控制元信息
    dynamic.AttentionInfo attention_info = 6;  // 关系链
    dynamic.FeedInfoCtrl info_ctrl       = 7;  // 动态卡片内容控制
}

// 动态搜索响应
message SearchRsp {
    int64 total                        = 1;  // 搜索总条数
    bool has_more                      = 2;  // 是否还可以继续翻页
    string track_id                    = 3;  // 关联搜索服务id
    repeated dynamic.DynBrief dyn_list = 4;  // 动态基本信息
    string version                     = 5;  // 服务端版本
}

enum MixUpListFrom {
    MIX_UPLIST_FROM_NONE       = 0;
    MIX_UPLIST_FROM_GW         = 1;  // 网关
    MIX_UPLIST_FROM_MELLOI     = 2;  // 压测平台
    MIX_UPLIST_FROM_BOTTOM_TAB = 3;  // 底tab
    MIX_UPLIST_FROM_SLARK      = 4;  // slark平台
}

message MixUpListReq {
    int64 uid                            = 1;  // mid
    int64 last_req_timestamp             = 2;  // 最后一次请求时间
    dynamic.VersionCtrlMeta version_ctrl = 3;  // 版本控制元信息，已废弃，优先使用meta字段
    MixUpListFrom from                   = 4;  // 请求来源
    int64 tab_recall_uid                 = 5;  // 底tab召回的up主uid
    StyleType tab_recall_type            = 6;  // 底tab召回的样式类型
    dynamic.MetaDataCtrl meta            = 7;  // 版本控制元信息，替代version_ctrl字段
    repeated string type_list            = 8;  // 动态类型列表
}

enum MoreUpListFrom {
    MORE_UPLIST_FROM_NONE    = 0;
    MORE_UPLIST_FROM_GENERAL = 1;  // 综合页
    MORE_UPLIST_FROM_VIDEO   = 2;  // 视频页
}

message MoreUpListReq {
    int64 uid                            = 1;  // mid
    dynamic.VersionCtrlMeta version_ctrl = 2;  // 版本控制元信息
    MoreUpListFrom from                  = 3;  // 请求来源
    repeated int64 first_page_uid_list   = 4;  // 第一页已经获取到的mid_list，去重用，请传入有效值。
    repeated string type_list            = 5;  // 动态类型列表s
}

enum UplistShowType {
    UPLIST_SHOW_TYPE_NONE = 0;
    UPLIST_SHOW_TYPE_OLD  = 1;  // 老样式
    UPLIST_SHOW_TYPE_NEW  = 2;  // 新样式
}

enum MixUpListType {
    MIX_UPLIST_TYPE_NONE       = 0;
    MIX_UPLIST_TYPE_LIVE       = 1;  // 直播用户
    MIX_UPLIST_TYPE_DYN_UP     = 2;  // 动态up
    MIX_UPLIST_TYPE_PREMIERE   = 3;  // 首映的up
    MIX_UPLIST_TYPE_OGV        = 4;  // ogv
    MIX_UPLIST_TYPE_COLLECTION = 5;  // 合集
}

enum BottomTabStrategyType {
    // A: 只出预约+特别关注 B: 预约+特别关注+1个月有效看播 C: 预约+特别关注+3个月有效看播 D: 预约+关注的up
    TAB_STRATEGY_TYPE_NONE = 0;
    DYN_EFFECT_CORNER_B    = 1;   // 预约+特别关注+1个月有效看播 动效角标
    DYN_EFFECT_CORNER_C    = 2;   // 预约+特别关注+3个月有效看播 动效角标
    DYN_EFFECT_CORNER_D    = 3;   // 预约+关注的up 动效角标
    WORD_CORNER_B          = 4;   // 预约+特别关注+1个月有效看播 文字角标
    WORD_CORNER_C          = 5;   // 预约+特别关注+3个月有效看播 文字角标
    WORD_CORNER_D          = 6;   // 预约+关注的up 文字角标
    DYN_EFFECT_AVATAR_A    = 7;   // 只出预约+特别关注 动效头像
    DYN_EFFECT_AVATAR_B    = 8;   // 预约+特别关注+1个月有效看播 动效头像
    WORD_AVATAR_A          = 9;   // 只出预约+特别关注 文字头像
    WORD_AVATAR_B          = 10;  // 预约+特别关注+1个月有效看播 文字头像
}

message LiveItem {
    int64 room_id              = 1;   // 直播房间id
    int64 ruid                 = 2;   // 直播用户uid
    string runame              = 3;   // 直播用户昵称
    string face                = 4;   // 直播头像
    string jump_url            = 5;   // 直播间跳链
    int64 area_v2_id           = 6;   // 分区v2 id
    string area_v2_name        = 7;   // 分区v2 名称
    int64 area_v2_parent_id    = 8;   // 分区v2 父分区id
    string area_v2_parent_name = 9;   // 分区v2 名称
    int64 live_start           = 10;  // 直播开播时间
    string rec_reason          = 11;  // 直播推荐理由
    string live_cover          = 12;  // 直播房间封面图
}

message UserInfo {
    int64 uid    = 1;  // 用户uid
    string uname = 2;  // 用户昵称
    string face  = 3;  // 用户头像
}

message DisplayStyle {
    string rect_text       = 1;  // 圆角矩形内文案
    string rect_text_color = 2;  // 圆角矩形内文案颜色
    string rect_icon       = 3;  // 圆角矩形内图标
    string rect_bg_color   = 4;  // 圆角矩形背景色
    string outer_animation = 5;  // 外圈动效
}

message MixUpListItem {
    MixUpListType type                    = 1;   // 用户头像类型
    bool has_update                       = 2;   // 是否显示小红点, 仅动态up主使用
    bool has_post_separator               = 3;   // 是否添加分隔符(用于直播列表和up主列表之间)
    bool is_reserve_recall                = 4;   // 是否是预约召回
    bool is_bottom_tab_recall             = 5;   // 是否是底tab召回
    UserInfo user_profile                 = 6;   // 用户信息
    LiveItem live_info                    = 7;   // 直播信息
    DisplayStyle display_style_normal     = 8;   // 直播头像样式(普通)
    DisplayStyle display_style_dark       = 9;   // 直播头像样式(深色)
    int64 style_id                        = 10;  // 直播头像样式id, 用于客户端上报
    int64 avid                            = 11;  // 首映预约up稿件avid，用于点击头像跳转
    bool is_esports_recall                = 12;  // 是否是赛事召回
    string rec_reason                     = 13;  // 直播推荐理由
    string live_cover                     = 14;  // 直播房间封面图
    string track_info                     = 15;  // 埋点用,json字符串,结构体参考message TrackInfo
    dynamic.UpdateContentType update_type = 16;  // 更新小红点类型
    bool show_new_follow_bubble           = 17;  // 是否为底tab气泡承接提权
}

message MixUpListViewMore {
    bool show                      = 1;  // 是否展示查看更多
    bool show_mix_fixed_entry      = 2;  // 是否在综合页头像模块右上角加固定入口
    bool show_personal_fixed_entry = 3;  // 是否在快消页头像模块右上角加固定入口
    string text                    = 4;  // 展示的文案
    string inner_title             = 5;  // 点进去之后的标题文案
}

// @Deprecated 已废弃，勿用
message BottomTabInfo {
    bool hit_bottom_tab_strategy              = 1;  // 是否命中底tab策略
    BottomTabStrategyType bottom_tab_strategy = 2;  // 命中的底tab策略
    UserInfo user_profile                     = 3;  // 直播用户信息
}

message MixUpListRsp {
    UplistShowType show_style               = 1;   // 控制客户端展示样式
    int64 total_live_num                    = 2;   // 正在直播用户总数
    int64 show_live_num                     = 3;   // 展示的直播用户数量
    bool show_more_button                   = 4;   // 是否展示更多按钮(仅老样式使用)
    bool module_title_switch                = 5;   // 控制客户端是否显示标题文案
    string module_title                     = 6;   // 标题文案
    repeated MixUpListItem list             = 7;   // 直播和动态up主列表
    repeated int64 up_list                  = 9;   // 头像的uid列表，提供给端上用于请求第二页最常访问头像
    MixUpListViewMore view_more             = 10;  // 查看更多
    string footprint                        = 11;  // 头像请求唯一id
    dynamic.DebugDynUplist debug_dyn_uplist = 12;  // 调试数据 @Deprecated 已废弃，勿用
    BottomTabInfo bottom_tab_info           = 13;  // @Deprecated 已废弃，勿用
    int64 dyn_live_uplist_strategy          = 14;  // 最常访问直播头像实验策略信息
    bool has_more_dyn_up_list               = 15;  // 是否有第二页最常访问头像
}

message MoreUpListRsp {
    repeated MixUpListItem list = 1;  // 动态up主列表（仅动态头像）
}

// 视频页最常访问头像请求
message VideoUpListReq {
    int64 uid                 = 1;  // mid
    dynamic.MetaDataCtrl meta = 2;  // 版本控制元信息
    MixUpListFrom from        = 3;  // 请求来源
}

// 视频页最常访问头像返回
message VideoUpListRsp {
    string module_title         = 1;  // 标题文案
    string show_all_tips        = 2;  // 客户端展示全部的文案，默认是"全部"
    repeated MixUpListItem list = 3;  // 动态up主列表
    repeated int64 up_list      = 4;  // 头像的uid列表，提供给端上用于请求第二页最常访问头像
    string footprint            = 5;  // 头像请求唯一id
    bool has_more_dyn_up_list   = 6;  // 是否有第二页最常访问头像
}

message GeneralStoryReq {
    int64 uid                            = 1;  // 访问者uid
    string offset                        = 2;  // 第一页传空；非第一页传上次返回的offset
    int64 rid                            = 3;  // 资源id
    int64 page_number                    = 4;  // 第几页（用于报表统计）
    int64 page_size                      = 5;  // 每页几条
    repeated string type_list            = 6;  // 动态类型列表（废弃）
    dynamic.VersionCtrlMeta version_ctrl = 7;  // 版本控制元信息
    string extra_param                   = 8;  // 额外参数
}

message GeneralStoryRsp {
    bool has_more      = 1;  // 是否还有更多
    string offset      = 2;  // 下一页的动态偏移
    repeated int64 rid = 3;  // 资源id
}

message SpaceStoryReq {
    int64 uid                            = 1;  // 访问者uid
    int64 host_uid                       = 2;  // 被访问者，也就是空间主人的uid
    string offset                        = 3;  // 第一页传空；非第一页传上次返回的offset
    int64 rid                            = 4;  // 资源id
    int64 page_number                    = 5;  // 第几页（用于报表统计）
    int64 page_size                      = 6;  // 每页几条
    repeated string type_list            = 7;  // 动态类型列表
    dynamic.VersionCtrlMeta version_ctrl = 8;  // 版本控制元信息
}

message SpaceStoryRsp {
    bool has_more      = 1;  // 是否还有更多
    string offset      = 2;  // 下一页的动态偏移
    repeated int64 rid = 3;  // 资源id
}

message NextOffset {
    int64 uid     = 1;  // 下一页的被访问者，当进入到浏览7天前动态视频时，会赋值为0
    string offset = 2;  // 下一页的动态偏移
}

message InsertedStoryReq {
    int64 uid                 = 1;  // 访问者uid
    NextOffset next_offset    = 2;  // 下一页的被访问者和动态偏移
    int64 uid_pos             = 3;  // story横插卡up主列表中点击的第几个up，从0开始
    int64 page_number         = 4;  // 第几页（用于报表统计，不用于实际的查询）
    int64 page_size           = 5;  // 每页几条
    repeated string type_list = 6;  // 动态类型列表
    dynamic.MetaDataCtrl meta = 7;  // 版本控制元信息
}

message InsertedStoryRsp {
    bool has_more                     = 1;  // 是否还有更多
    NextOffset next_offset            = 2;  // 下一页的被访问者和动态偏移
    repeated int64 rid                = 3;  // 资源id（废弃）
    repeated dynamic.RidInfo rid_info = 4;  // 资源信息，后续都用这个，防止联合投稿rid一样，uid不一样情况
}

message FetchDynamicContentReq {
    dynamic.DynRevsId dyn_revs = 1;  // 动态revs记录
    int64 dyn_id               = 2;  // 动态id, revs与dyn_id同时传值只取revs
}

message FetchDynamicContentRsp {
    string content = 1;  // 动态配文
    string title   = 2;  // 动态标题
}

message FeedFilterReq {
    int64 uid                            = 1;  // 用户uid
    string offset                        = 2;  // 动态id 翻页偏移
    string tab                           = 3;  // 用户选择的tab; 1视频：video;2未观看：unseen；3继续观看：continueWatching；4直播：onLive
    int64 page                           = 4;  // 页数
    dynamic.MetaDataCtrl meta            = 6;  // 版本控制元信息
    dynamic.FeedInfoCtrl info_ctrl       = 7;  // 动态卡片内容控制
    dynamic.AttentionInfo attention_info = 8;  // 关注链信息
}

message FeedFilterRsp {
    string offset                  = 1;  // 下一页的动态偏移
    bool has_more                  = 2;  // 是否还有更多
    repeated dynamic.DynBrief dyns = 3;  // 动态基本信息列表
}

message FetchPegasusFeedInfoReq {
    repeated int64 dyn_ids = 1 [(gogoproto.moretags) = 'validate:"required,gt=0,max=20"'];  // 动态id列表
}

message FetchPegasusFeedInfoRsp {
    repeated PegasusFeedInfo dyn_infos = 1;
}

message PegasusFeedInfo {
    int64 dyn_id                = 1;  // 动态id
    int64 uid                   = 2;  // up主id
    bool audit_pass             = 3;  // 是否过审
    int64 sensitive_level       = 4;  // 敏感等级
    bool visiable               = 5;  // 是否可展示-true：可以展示；false：不可露出
    int64 report_user_count     = 6;  // 举报用户数
    int64 like_num              = 7;  // 点赞数
    MaterialCard card           = 8;  // 图文物料
    opus.OpusSource opus_source = 9;  // 图文数据来源。
}

message QueryDynFrozenStatusReq {
    repeated int64 dyn_ids = 1;
}
message QueryDynFrozenStatusRsp {
    map<int64, DynStatusInfo> dyn_status_infos = 1;
}
message DynStatusInfo {
    int64 dyn_id         = 1;
    DynStatus dyn_status = 2;
}
enum DynStatus {
    DEFAULT                     = 0;
    DYN_FROZEN                  = 1;  // 动态冻结
    ONLY_FANS_DYN_FORBID_REPOST = 2;  // 专属动态禁转
}

message ConsumeOffsetReq {
    int64 uid                        = 1;  // 访问者
    int64 host_uid                   = 2;  // 被访问者
    int64 read_offset                = 3;  // 消费进度
    int64 scene                      = 4;  // 快消-视频页=3 ;快消-综合页 = 4
    dynamic.DynUidType host_uid_type = 5;  // 被访问者类型
}

message ConsumeOffsetRsp {
}

message InteractListReq {
    int64 uid                            = 1;  // 访问者的uid
    int64 dyn_id                         = 2;  // 动态id
    dynamic.DynRevsId dyn_revs_id        = 3;  // 反查动态id
    string offset                        = 4;  // offset 服务端下发
    int64 page_size                      = 5;  // 页数
    dynamic.MetaDataCtrl meta            = 6;  // 版本控制元信息
    dynamic.AttentionInfo attention_info = 7;  // 登录用户的关注链
}

message InteractItem {
    dynamic.InteractType type = 1;  // 互动类型 1、点赞 2、转发 0、无意义
    int64 uid                 = 2;  // 用户uid
    int64 attend              = 3;  // 关注关系 0-互不关注 1-被关注(粉丝) 2-普通关注 3-特别关注 4-互相关系(好友)
    string rcmd_reason        = 4;  // 推荐理由
}

message InteractListRsp {
    repeated InteractItem list = 1;  // 互动列表
    int64 has_more             = 2;  // 是否还有更多
    int64 total                = 3;  // 互动总数
    string offset              = 4;  // offset
}

message OriginalListReq {
    int64 uid                            = 1;  // 登录用户uid
    string offset                        = 2;  // 动态偏移offset
    repeated string type_list            = 3;  // 动态类型列表
    dynamic.VersionCtrlMeta version_ctrl = 4;  // 版本控制元信息
}

message OriginalListRsp {
    string history_offset  = 1;  // 下一页的动态偏移
    bool has_dyn_ids       = 2;  // 动态id列表是否为空
    repeated int64 dyn_ids = 3;  // 动态id列表
}

message CommentDynamicThumbReq {
    int64 uid                 = 1;   // 点赞用户id
    int64 dyn_id              = 2;   // 动态id
    ThumbAction thumb_action  = 3;   // 操作
    dynamic.MetaDataCtrl meta = 4;   // 版本控制元信息
    int64 rpid                = 5;   // 评论id
    int64 type                = 6;   // 评论类型
    int64 oid                 = 7;   // 评论区id
    string uri                = 8;   // 评论落地页(web)
    string native_uri         = 9;   // 评论落地页(app)
    string spmid              = 10;  // 轻互动上报参数，详见需求文档：https://www.tapd.cn/20095661/prong/stories/view/1120095661004458406
    string from_spmid         = 11;  // 轻互动上报参数，详见需求文档：https://www.tapd.cn/20095661/prong/stories/view/1120095661004458406
    string action_id          = 12;  // 轻互动上报参数，详见需求文档：https://www.tapd.cn/20095661/prong/stories/view/1120095661004458406
}

message CommentDynamicThumbRsp {
}

enum ThumbAction {
    UNSPECIFIED    = 0;
    LIKE           = 1;  // 点赞
    CANCEL_LIKE    = 2;  // 取消点赞
    DISLIKE        = 3;  // 点踩
    CANCEL_DISLIKE = 4;  // 取消点踩
}

message CommentDynamicThumbStatesReq {
    int64 uid              = 1;  // 用户id
    repeated int64 dyn_ids = 2;  // 动态id,最多50条
}

message CommentDynamicThumbStatesRsp {
    map<int64, ThumbState> thumb_states = 1;  // 动态点赞状态:key:dynId,value:state
}

enum ThumbState {
    STATE_UNSPECIFIED = 0;
    STATE_LIKE        = 1;  // 点赞
    STATE_DISLIKE     = 2;  // 点踩
}

message UpVideoUnreadInfoReq {
    int64 mid                 = 1;  // 主态用户
    int64 max_num             = 2;  // 需要获取的最大计数信息
    dynamic.MetaDataCtrl meta = 3;  // meta元信息
    dynamic.AttentionInfo ups = 4;  // 关注的up
}

message UnReadInfo {
    int64 cnt = 1;  // 未读数
}

message UpVideoUnreadInfoRsp {
    map<int64, UnReadInfo> ups_unread = 1;  // up未读的信息
}

message DynamicDetailForSearchReq {
    repeated int64 dyn_ids = 1 [(gogoproto.moretags) = 'validate:"required,gt=0,max=20"'];  // 动态id列表
}

message DynamicDetailForSearchRsp {
    repeated SearchDynamicDetail list = 1;  // 动态详情列表
}

message SearchDynamicDetail {
    int64 dyn_id          = 1;
    UserInfo user_profile = 2;  // 用户信息
    MetaStats stats       = 3;  // 动态状态信息
    int64 ctime           = 4;  // 发布时间（unix时间戳）
}

message MetaStats {
    int64 view  = 1;  // 浏览量
    int64 like  = 2;  // 点赞数
    int64 reply = 3;  // 评论量
};

message DynOnlyFansInfoReq {
    repeated int64 dyn_ids = 1 [(gogoproto.moretags) = 'validate:"required,gt=0,max=20"'];  // 动态id列表
}

message DynOnlyFansInfo {
    int64 dyn_id       = 1;  // 动态id
    int64 uid          = 2;  // up主uid
    bool visiable      = 3;  // 动态是否公开可见，判断标准：未登录用户在详情页可以看到动态，即视为可见
    bool only_fans     = 4;  // 是否是专属动态
    int64 charge_level = 5;  // 档位信息
    int64 dyn_type     = 6;  // 动态类型，文档:https://info.bilibili.co/pages/viewpage.action?pageId=13358955
    int64 rid          = 7;  // 动态资源id
    int64 ctime        = 8;  // 动态发布时间，秒
    bool not_disturb   = 9;  // 是否免打扰，true为专属动态设置了免打扰
}

message DynOnlyFansInfoRsp {
    map<int64, DynOnlyFansInfo> only_fans_infos = 1;  // 动态专属充电信息
}

enum OnlyFansType {
    ONLY_FANS_LOW  = 0;  // 低档位专属
    ONLY_FANS_HIGH = 1;  // 高档位专属
}

message UpowerSpaceReq {
    int64 uid                       = 1;  // 用户uid
    int64 up_uid                    = 2;  // up主uid
    repeated OnlyFansType type_list = 3;  // 专属类型
    dynamic.MetaDataCtrl meta       = 4;  // 版本控制元信息
}

message UpowerSpaceDyn {
    int64 dyn_id       = 1;  // 动态id
    int64 dyn_type     = 2;  // 动态类型，文档:https://info.bilibili.co/pages/viewpage.action?pageId=13358955
    int64 uid          = 3;  // 发布者uid
    int64 ctime        = 4;  // 动态创建时间，秒
    int64 charge_level = 5;  // 专属动态档位信息
    MaterialCard card  = 6;  // 图文物料
    string jump_url    = 7;  // 详情页跳链
}

message UpowerSpaceLottery {
    int64 ctime     = 1;  // 抽奖发布时间，秒
    string content  = 2;  // 抽奖文案
    string jump_url = 3;  // 详情页跳链
}

message UpowerSpaceRsp {
    map<int64, int64> dyn_counts     = 1;  // 专属动态个数，档位(OnlyFansType)->个数
    map<int64, int64> lottery_counts = 2;  // 专属抽奖个数，档位(OnlyFansType)->个数
    UpowerSpaceDyn dyn_info          = 3;  // 外露专属动态信息
    UpowerSpaceLottery lottery_info  = 4;  // 外露专属抽奖信息
}

message UpowerSpaceDynsReq {
    int64 uid                       = 1;  // 用户uid
    int64 up_uid                    = 2;  // up主uid
    repeated OnlyFansType type_list = 3;  // 专属类型
    string offset                   = 4;  // 动态偏移offset，服务端下发，默认空
    int64 page_size                 = 5;  // 每页几条
    dynamic.MetaDataCtrl meta       = 6;  // 版本控制元信息
}

message UpowerSpaceDynsRsp {
    bool has_more                = 1;  // 还有更多
    string offset                = 2;  // 下一页的动态偏移
    repeated UpowerSpaceDyn dyns = 3;  // 动态列表
}

message UpowerSpaceLotteriesReq {
    int64 uid                       = 1;  // 用户uid
    int64 up_uid                    = 2;  // up主uid
    repeated OnlyFansType type_list = 3;  // 专属类型
    string offset                   = 4;  // 动态偏移offset，服务端下发，默认空
    int64 page_size                 = 5;  // 每页几条
    dynamic.MetaDataCtrl meta       = 6;  // 版本控制元信息
}

message UpowerSpaceLotteriesRsp {
    bool has_more                         = 1;  // 还有更多
    string offset                         = 2;  // 下一页的动态偏移
    repeated UpowerSpaceLottery lotteries = 3;  // 抽奖列表
}
message OpusCardReq {
    // 登陆态用户的uid
    int64 uid = 1;
    // 调用环境meta信息，服务端调用时能传尽传
    cosmo.CosmoMeta meta = 2;
    // 批量查询的图文ID，不超过20个
    repeated int64 opus_ids = 3;
    // 需要拉取内容的选项
    OpusCardReqOption option = 4;
}
message OpusCardRsp {
    map<int64, cosmo.OpusCard> cards = 1;  // opusId对应的图文卡片信息
}
message OpusCardReqOption {
    bool summary                                       = 1;  // 是否返回仅返回摘要信息（简化文本格式且上限500字），true是 false否
    repeated cosmo.EntityCountType count_types         = 2;  // 需要返回哪些计数信息
    repeated cosmo.EntityActionStatusType action_types = 3;  // 需要返回哪些用户和图文内容的交互信息
    bool author                                        = 4;  // 是否需要简要的作者信息：OpusCard.Author，true是 false否
    bool extend                                        = 5;  // 是否需要附加信息：OpusCard.Extend，true是 false否
    bool new_topic                                     = 6;  // 是否需要新话题信息：OpusCard.Extend.NewTopic，true是 false否
}

enum RefreshMode {
    REFRESH_MODE_DEFAULT          = 0;  // 默认刷新方式，无意义，不确定刷新方式时使用
    REFRESH_MODE_DROP_DOWN        = 1;  // 下拉刷新
    REFRESH_MODE_TOP_TAB_CLICK    = 2;  // 点击动态顶部"关注"刷新
    REFRESH_MODE_BOTTOM_TAB_CLICK = 3;  // 点击动态底tab刷新
}

message GeneralRecommendReq {
    int64 uid                            = 1;  // 登录用户uid
    dynamic.AttentionInfo attention_info = 2;  // 关注链信息（包括登录用户自己）
    RefreshMode refresh_mode             = 3;  // 刷新方式，仅page=1有效
    int64 page                           = 4;  // 页码号，从1开始
    string offset                        = 5;  // offset，透传服务端下发，默认空
    dynamic.FeedInfoCtrl info_ctrl       = 6;  // 动态卡片内容控制
    dynamic.VersionCtrlMeta version_ctrl = 7;  // 版本控制元信息
    TabRecallUp tab_recall               = 8;  // 底tab头像召回up主信息
}

message GeneralRecommendCard {
    dynamic.DynBrief dyn = 1;  // 动态基本信息列表
    string track_info    = 2;  // 埋点用，json字符串，结构体参考message TrackInfo
}

message GeneralRecommendRsp {
    repeated GeneralRecommendCard cards = 1;  // 推荐卡
    bool has_more                       = 2;  // 是否还有更多动态
    string offset                       = 3;  // offset，服务端下发
}

message FetchFavSeasonACTimeReq {
    int64 mid                      = 1;  // 访问者mid
    repeated int64 ogv_sids        = 2;
    repeated int64 collection_sids = 3;
}

message FetchFavSeasonACTimeRsp {
    map<int64, int64> ogv_access_time        = 1;  // 动态侧记录的ogv访问时间 ogv seasonid -> 访问时间戳
    map<int64, int64> collection_access_time = 2;  // 动态侧记录的合集访问时间 合集 seasonid -> 访问时间戳
}

message ConsumeFavSeasonRedDotReq {
    int64 mid = 1;  // 访问者mid
}

message ConsumeFavSeasonRedDotRsp {
}

message FavSeasonHasUpdateReq {
    int64 mid                      = 1;  // 访问者mid
    repeated int64 ogv_sids        = 2;  // ogv seasonid
    repeated int64 collection_sids = 3;  // 合集 seasonid
}

message FavSeasonHasUpdateRsp {
    map<int64, bool> ogv_has_update        = 1;  // ogv 是否更新
    map<int64, bool> collection_has_update = 2;  // 合集 是否更新
}

// 最近@的人列表
message RecentAtListReq {
    int64 uid = 1;  // 访问用户uid
}
message RecentAtListRsp {
    message At {
        int64 uid          = 1;  // @的用户
        int64 recent_at_ts = 2;  // @发生的时间
    }
    repeated At at_info = 1;
}

message FetchPersonalDynsReq {
    int64 uid     = 1;  // 当前登陆用户uid
    string offset = 2;  // 上一页动态偏移,第一页时默认为空
}

message FetchPersonalDynsRsp {
    message DynItem {
        int64 dyn_id                       = 1;  // 动态id
        int64 uid                          = 2;  // up主uid
        int64 dyn_type                     = 3;  // 动态类型，文档:https://info.bilibili.co/pages/viewpage.action?pageId=13358955
        int64 rid                          = 4;  // 动态资源id
        int64 ctime                        = 5;  // 动态发布时间，秒
        string cover                       = 6;  // 封面或第一张图
        string title                       = 7;  // 标题
        string content                     = 8;  // 业务方文本内容
        repeated draw.PictureInfo pictures = 9;  // 图文动态的全部图片
    }

    repeated DynItem dyns = 1;
    bool has_more         = 2;  // 是否还有更多
    string history_offset = 3;  // 下一页的动态偏移
}

message GetRelationScoreReq {
    int64 uid                      = 1;  // uid
    repeated int64 attention_users = 2;  // 关注链uid
}

message GetRelationScoreRsp {
    repeated RelationScore items = 1;
}

message RelationScore {
    int64 up_uid          = 1;   // up_uid
    double new_dyn_qscore = 10;  // 新动态q值
}

message TimingDynListReq {
    int64 uid                 = 1;  // 登陆用户id
    dynamic.MetaDataCtrl meta = 2;  // 版本控制元信息
}

message TimingDynListRsp {
    repeated TimingDynListItem list = 1;  // 定时发布列表
}

enum TimingDynState {
    TIMING_DYN_DEFAULT      = 0;  // 无意义
    TIMING_DYN_WAIT_AUDIT   = 1;  // 待审核
    TIMING_DYN_IN_AUDIT     = 2;  // 审核中
    TIMING_DYN_AUDIT_PASS   = 3;  // 审核通过
    TIMING_DYN_AUDIT_REJECT = 4;  // 审核拒绝
}

message TimingDynListItem {
    dynamic.DynBrief brief        = 1;  // brief不包含全字段，仅标号[1-5]、9保证有效
    opus.OpusSummary opus_summary = 2;  // 图文列表展示内容
    TimingDynState state          = 3;  // 定时发布草稿状态
}

message TimingDynDetailReq {
    int64 uid                 = 1;  // 登陆用户id
    int64 dyn_id              = 2;  // 动态id
    dynamic.MetaDataCtrl meta = 3;  // 版本控制元信息
}

message TimingDynDetailRsp {
    dynamic.DynBrief brief    = 1;  // brief不包含全字段，仅标号[1-5]、9保证有效
    .opus.Opus opus           = 2;  // 图文作品
    .opus.OpusSummary summary = 3;  // 客户端进入详情页，转发等后续操作需要
    TimingDynState state      = 4;  // 定时发布草稿状态
}

message WidgetUpdateDynsReq {
    int64 uid                         = 1;
    dynamic.MetaDataCtrl version_ctrl = 2;
    int64 count                       = 3;  // 拉取数量，默认2条
}

message WidgetUpdateDynsRsp {
    repeated dynamic.DynBrief dyns = 1;
}