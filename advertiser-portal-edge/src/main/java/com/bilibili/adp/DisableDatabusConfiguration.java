package com.bilibili.adp;

import com.bilibili.adp.cpc.databus.DatabusClient;
import com.bilibili.adp.cpc.databus.DatabusPropertyWrapperBo;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusSubLifeCycle;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

@Configuration
@AutoConfigureAfter(value = {DatabusProperties.class, DatabusPropertyWrapperBo.class})
@AutoConfigureBefore(value = {DatabusSubLifeCycle.class, DatabusClient.class})
public class DisableDatabusConfiguration {

    private final DatabusProperties databusProperties;

    private final List<DatabusPropertyWrapperBo> databusPropertyWrappers;

    private final List<DatabusClient> databusClients;

    public DisableDatabusConfiguration(DatabusProperties databusProperties, List<DatabusPropertyWrapperBo> databusPropertyWrappers, List<DatabusClient> databusClients) {
        this.databusProperties = databusProperties;
        this.databusPropertyWrappers = databusPropertyWrappers;
        this.databusClients = databusClients;
    }

    @PostConstruct
    public void disableDatabus() {
        databusProperties.getProperties().forEach((key, value) -> value.getSub().setConsumer(0));

        databusPropertyWrappers.forEach(propertyWrapperBo -> propertyWrapperBo.setThreads(0));

        databusClients.forEach(databusClient -> databusClient.setEnabled(false));
    }
}
