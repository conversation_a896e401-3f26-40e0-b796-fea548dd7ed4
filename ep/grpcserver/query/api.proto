syntax = "proto3";
package ep.grpcserver.api;
option go_package="buf.bilibili.co/bapis/bapis-gen/ep/grpcserver.query;api";
option java_package="com.bapis.ep.grpcserver.query";
option (wdcli.appid) = "test.ep.grpcserver";
option java_multiple_files = true;

// import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";


// 定义Hello服务
service Hello {
  // 定义SayHello方法
  rpc SayHello(HelloRequest) returns (HelloResponse) {}
}



// HelloRequest 请求结构
message HelloRequest {
  message Detail {
    string name = 1 ;
    int64 age = 2 ;
  }
  // mid
  repeated int64 mid = 1 ;
  // 增量
  double count = 2 ;
  // 修改原因
  string reason = 3 ;
  // 操作类型
  string operate = 4 ;
  // ip
  Detail msg = 5;
  repeated string ip = 6;
  repeated Detail detail = 7;
}
message EmptyStruct {}
// HelloResponse 响应结构
message HelloResponse {
  string message = 1;
}