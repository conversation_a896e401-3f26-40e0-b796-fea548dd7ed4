// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

// import "github.com/gogo/protobuf/gogoproto/gogo.proto";
// import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package demo.service.v1;

// NOTE: 最后请删除这些无用的注释 (゜-゜)つロ

option go_package = "buf.bilibili.co/bapis/bapis-gen/silverbullet/gaia.service.factor.light;api";
option java_package = "com.bapis.silverbullet.gaia.service.factor.light";
option java_multiple_files = true;
option (wdcli.appid) = "main.silverbullet.gaia-factor-light-service";

service FactorService {
  //获取因子
  rpc AccAndFactors(FactorsReq) returns (FactorsReply);
}


message FactorsReq{
  repeated string factor_names = 1;
  map<string, Value> env = 2;
  int64 scene_id = 3;
  int64 ts = 4;
  string scene_name = 5;
}

message FactorsReply{
  map<string, Value> factors = 1; //每一个因子的最终结果
  map<string, Value> eval_result = 2; //中间因子计算结果
}

enum Type{
  FactorInt = 0;
  FactorString = 1;
  FactorDouble = 2;
  FactorBool = 3;
  FactorIntList = 4;
  FactorStringList = 5;
  FactorDoubleList = 6;
  FactorBoolList = 7;
  FactorJson = 8;
  FactorError = 9;
  FactorMap = 10;
  FactorAggrStruct = 11; //可以当作json来处理
  Error = 100;//意外的类型
  AllList = 10000;
}

message Value{
  Type type = 1;
  int64  int_value = 2;
  string string_value = 3;
  bool bool_value = 4;
  double double_value = 5;
  repeated int64 int_values = 6;
  repeated string string_values = 7;
  repeated bool bool_values = 8;
  repeated double double_values = 9;
}