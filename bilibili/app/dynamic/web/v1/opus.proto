syntax = "proto3";

package bilibili.app.dynamic.web.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bilibili/app.dynamic.web.v1;v1";
option java_multiple_files = true;
option java_package = "com.bapis.bilibili.app.dynamic.web.v1";
option objc_class_prefix = "BAPIAppDynamicWebV1";

import "google/api/annotations.proto";

service WebOpus {
  // 图文收藏夹 feed 流
  rpc FavFeed(FavFeedReq) returns (FavFeedResp) {
    option (google.api.http) = {
      get: "x/polymer/web-dynamic/v1/opus/feed/fav"
    };
  }

  rpc Favlist(FavlistReq) returns (FavlistResp) {
    option (google.api.http) = {
      get: "x/polymer/web-dynamic/v1/opus/favlist"
    };
  }
}

// FavFeedReq 图文收藏夹 feed 流请求
message FavFeedReq {
  int64 page = 1;
  int64 page_size = 2;
  // 时区 offset 分钟，默认 -480
  int64 timezone_offset = 3;
}

// FavFeedResp 图文收藏夹 feed 流响应
message FavFeedResp {
  repeated IOpusFeedItem items = 1;
  bool has_more = 2;
  string offset = 3;
  int64 update_num = 4;
  int64 update_baseline = 5;
}

// CoverInfo 封面信息
message CoverInfo {
  string url = 1;
  double width = 2;
  double height = 3;
}

// StatInfo 统计信息
message StatInfo {
  // undefined 就不展示，其他都有值，比如"0"
  string view = 1;
  string like = 2;
}

// BadgeInfo 徽章信息
message BadgeInfo {
  // 专属动态
  string text = 1;
  // #FFFFFF
  string color = 2;
  // #FF6699
  string bg_color = 3;
  // 有就渲染，一个src 地址
  string icon_prefix = 4;
}

// AuthorInfo 作者信息
message AuthorInfo {
  string name = 1;
  string face = 2;
  int64 mid = 3;
}

// IOpusFeedItem 动态流内容项
message IOpusFeedItem {
  string jump_url = 1;
  int64 opus_id = 2;
  // 文案
  string content = 3;
  BadgeInfo badge = 4;
  AuthorInfo author = 5;
  CoverInfo cover = 6;
  StatInfo stat = 7;
  string pub_time = 8;
}

message FavlistReq {
  int64 page = 1;
  int64 page_size = 2;
  // 时区 offset 分钟，默认 -480
  int64 timezone_offset = 3;
}

message FavlistResp {
  repeated IFavOpusItem items = 1;
  bool has_more = 2;
  int32 total = 3;
}

message IFavOpusItem {
  int64 opus_id = 1;
  string title = 2;
  string content = 3;
  string cover = 4;
  string time_text = 5;
  bool is_expired = 6;
  string jump_url = 7;
}