// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package bilibili.app.translation.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bilibili/app.translation.v1;v1";
option java_multiple_files = true;
option java_package = "com.bapis.bilibili.app.translation.v1";
option objc_class_prefix = "BAPIAppTranslationV1";

service Translation {
  // 一键翻译，适用于单个业务 ID
  rpc TranslationSimple(TranslationSimpleReq) returns (TranslationSimpleReply);
  // 批量翻译，适用于多个业务 ID
  rpc TranslationBatch(TranslationBatchReq) returns (TranslationBatchReply);
}

message TranslationSimpleReq {
  // 业务类型，如 ARC / REPLY / OPUS / LIVE
  TranslationBusiness biz_type = 1;
  // 业务 ID
  string business_id = 2;
  // spmid 当前页面
  string spmid = 3;
  // 可选翻译的字段
  // ARC: title, introduction
  // LIVE: title
  repeated string fields = 4;
  // 待翻译的原文本, 需要与fields一一对应
  repeated string text = 5;
}

message TranslationSimpleReply {
  // 翻译结果
  repeated TranslatedField results = 1;
}

message TranslatedField {
  // 字段名（如 title、desc、content）
  string field = 1;
  // 原文
  string original_text = 2;
  // 翻译后的文本
  string translated_text = 3;
  // 是否翻译成功
  bool is_translated = 4;
  // 实际翻译的语言
  string translated_lang = 5;
  // 实际翻译的地区代码，如 "US"、"CN"
  string translated_region = 6;
  // 实际翻译的书写系统，如 "Hans"、"Latn"
  string translated_script = 7;
}

message TranslationBatchReq {
  // spmid 当前页面(批量处理时以该字段为准)
  string spmid = 1;
  // 待翻译的不同业务类型数据
  repeated TranslationSimpleReq items = 2;
}

message TranslationBatchReply {
  repeated TranslationSimpleReply results = 1;
}

enum TranslationBusiness {
  //未指定
  TRANS_BIZ_UNSPECIFIED = 0;
  //稿件
  TRANS_BIZ_ARC = 1;
  //评论
  TRANS_BIZ_REPLY = 2;
  //图文
  TRANS_BIZ_OPUS = 3;
  //直播
  TRANS_BIZ_LIVE = 4;
}