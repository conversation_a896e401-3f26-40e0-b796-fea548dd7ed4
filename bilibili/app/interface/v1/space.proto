syntax = "proto3";

// `override.GO_PACKAGE:"bapis/bapis-gen-gogo,bilibili/app/archive/v1/archive.proto,buf.bilibili.co/bapis/bapis-gen-gogo/archive/service"`
// `override.GO_PACKAGE:"bapis/bapis-gen,bilibili/app/archive/v1/archive.proto,buf.bilibili.co/bapis/bapis-gen/archive/service"`

package bilibili.app.interface.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/bilibili/app.interface.v1;v1";
option java_multiple_files = true;
option java_package = "com.bapis.bilibili.app.interfaces.v1";
option objc_class_prefix = "BAPIAppInterfaceV1";

import "bilibili/app/archive/middleware/v1/preload.proto";
import "bilibili/app/archive/v1/archive.proto";
import "bilibili/app/dynamic/v2/dynamic_card.proto";

service Space {
  // 空间搜索标签列表
  rpc SearchTab(SearchTabReq) returns (SearchTabReply);
  // 空间搜索稿件列表
  rpc SearchArchive(SearchArchiveReq) returns (SearchArchiveReply);
  // 空间搜索动态列表
  rpc SearchDynamic(SearchDynamicReq) returns (SearchDynamicReply);
  // 空间预约修改时间
  rpc UpdateReserveStartTime(UpdateReserveStartTimeReq) returns (UpdateReserveStartTimeReply);
}

enum From {
  // 稿件
  ArchiveTab = 0;
  // 动态
  DynamicTab = 1;
}

message SearchTabReq {
  // 关键词
  string keyword = 1;
  // up主mid
  int64 mid = 2;
  // 来源
  From from = 3;
}

message SearchTabReply {
  // 展示哪个标签页
  int64 focus = 1;
  // 标签页列表
  repeated Tab tabs = 2;
}

message Tab {
  string title = 1;
  string uri = 2;
}

message SearchArchiveReq {
  // 关键词
  string keyword = 1;
  // up主mid
  int64 mid = 2;
  int64 pn = 3;
  int64 ps = 4;
  // 秒开用参数
  .bilibili.app.archive.middleware.v1.PlayerArgs player_args = 5;
}

message SearchArchiveReply {
  repeated Arc archives = 1;
  // total 总数
  int64 total = 2;
}

message Arc {
  // 稿件信息
  .archive.service.v1.Arc archive = 1;
  // 跳转地址含秒开地址
  string uri = 2;
  // 播放数文案
  string view_content = 3;
  // 0:vv 1:vt
  int64 icon_type = 4;
  // 封面icon
  string cover_icon = 5;
  // 是否折叠
  bool is_fold = 6;
  // 课堂稿件
  bool is_pugv = 7;
  // 发布文案
  string publish_time_text = 8;
  // 角标
  repeated string badges = 9;
  // 是否自见
  bool is_oneself = 10;
}


message SearchDynamicReq {
  // 关键词
  string keyword = 1;
  // up主mid
  int64 mid = 2;
  int64 pn = 3;
  int64 ps = 4;
  // 秒开用参数
  .bilibili.app.archive.middleware.v1.PlayerArgs player_args = 5;
}

message SearchDynamicReply {
  repeated Dynamic dynamics = 1;
  // total 总数
  int64 total = 2;
}

message Dynamic {
  // 动态信息
  .bilibili.app.dynamic.v2.DynamicItem dynamic = 1;
}

message UpdateReserveStartTimeReq{
  int64 sid = 1; // 预约id
  int64 new_live_plan_start_time = 2; // 新修改的开播时间，时间戳
}

message UpdateReserveStartTimeReply{
  string desc_text = 1 ;
}