syntax = "proto3";

package bilibili.broadcast.message.main;

import "bilibili/app/dynamic/v2/paragraph.proto";
import "google/protobuf/empty.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/bilibili/broadcast.message.main;v1";
option java_multiple_files = true;
option java_package = "com.bapis.bilibili.broadcast.message.main";
option objc_class_prefix = "BAPIBroadcastMessageMain";


service Search {
  rpc ChatResultPush(google.protobuf.Empty) returns (stream ChatResult);
}

// 气泡结构
message Bubble {
  repeated bilibili.app.dynamic.v2.Paragraph paragraphs = 1;
  repeated CardItem item = 2; //
}

enum LikeState{
  // 默认状态
  DEFAULT = 0;
  // 点赞
  LIKE = 1;
  // 点踩
  DISLIKE = 2;
}

message ChatResult {
  int32 code = 1; // 为0表示结果可用，1表示该query不会出结果，< 0表示其他错误
  string session_id = 2;
  repeated Bubble bubbles = 3;
  string rewrite_word = 4; // 改写词
  string title = 5; // 标题
  string business = 6; // 业务
  int64 message_id = 7; // 对象id
  LikeState like_state = 8; // 点赞状态
  int64 like_number = 9;// 点赞数
}

message CardItem {
  string uri = 1;
  string param = 2;
  string goto = 3;
  oneof card_item {
    ArticleCard article = 4;//article
    VideoCard av = 5;//av
  }
}

message ArticleCard {
  string title = 1;
  string desc = 2;
  string cover = 3;
  string author = 4;
  int64 mid = 5;
  int32 view = 6;
  int32 like = 7;
  int32 reply = 8;
  string pub_time = 9;
  string badge = 10;
}

message ReasonStyle {
  string text = 1;
  string text_color = 2;
  string text_color_night = 3;
  string bg_color = 4;
  string bg_color_night = 5;
  string border_color = 6;
  string border_color_night = 7;
  int32 bg_style = 8;
}

message VideoCard{
  string title = 1;
  string desc = 2;
  string cover = 3;
  string author = 4;
  string duration = 5;
  repeated ReasonStyle new_rec_tags = 6;
  repeated ReasonStyle badges = 7;
  string show_card_desc_2 = 8;
  string view_content = 9;
  int32 icon_type = 10;//1:vv 33:vt
}