syntax = "proto3";

package bilibili.broadcast.message.fission;

import "google/protobuf/empty.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/bilibili/broadcast.message.fission;v1";
option java_multiple_files = true;
option java_package = "com.bapis.bilibili.broadcast.message.fission";
option objc_class_prefix = "BAPIBroadcastMessageFission";

service Fission {
    rpc GameNotify(google.protobuf.Empty) returns (stream GameNotifyReply);
}

message GameNotifyReply {
    uint32 type = 1; // 类型字段
    string data = 2; // 数据字段
}