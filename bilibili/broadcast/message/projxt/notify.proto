syntax = "proto3";

import "google/protobuf/empty.proto";

package bilibili.broadcast.message.projxt;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bilibili/broadcast.message.projxt;v1";
option java_multiple_files = true;
option java_package = "com.bapis.bilibili.broadcast.message.projxt";
option objc_class_prefix = "BAPIBroadcastMessageProjxt";

message Notification {
    // 消息唯一id
    int64 msg_id = 1;
    // 消息类型
    int32 msg_type = 2;
    // 消息发送者mid
    int64 sender = 3;
    // 消息内容
    string body = 4;
}

service Notify {
    rpc Notify(google.protobuf.Empty) returns (stream Notification);
}