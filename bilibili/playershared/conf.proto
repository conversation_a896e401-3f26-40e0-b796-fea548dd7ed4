syntax = "proto3";

package bilibili.playershared;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bilibili/playershared;playershared";
option java_package = "com.bapis.bilibili.playershared";
option java_multiple_files = true;
option objc_class_prefix = "BAPIPlayershared";

enum ConfType {
  // default
  NoType = 0;
  //镜像反转
  FLIPCONF = 1;
  //视频投屏
  CASTCONF = 2;
  //反馈
  FEEDBACK = 3;
  //字幕
  SUBTITLE = 4;
  //播放速度
  PLAYBACKRATE = 5;
  //定时停止播放
  TIMEUP = 6;
  //播放方式
  PLAYBACKMODE = 7;
  //画面尺寸
  SCALEMODE = 8;
  //后台播放
  BACKGROUNDPLAY = 9;
  //顶
  LIKE = 10;
  //踩
  DISLIKE = 11;
  //投币
  COIN = 12;
  //充电
  ELEC = 13;
  //分享
  SHARE = 14;
  //截图/gif
  SCREENSHOT = 15;
  //锁屏
  LOCKSCREEN = 16;
  //相关推荐
  RECOMMEND = 17;
  //倍速
  PLAYBACKSPEED = 18;
  //清晰度
  DEFINITION = 19;
  //选集
  SELECTIONS = 20;
  //下一集
  NEXT = 21;
  //编辑弹幕
  EDITDM = 22;
  //小窗
  SMALLWINDOW = 23;
  //播放震动
  SHAKE = 24;
  //外层面板弹幕设置（实验组1）
  OUTERDM = 25;
  //三点内弹幕设置（实验组2）
  INNERDM = 26;
  //全景
  PANORAMA = 27;
  //杜比
  DOLBY = 28;
  //滤镜
  COLORFILTER = 29;
  //无损音频
  LOSSLESS = 30;
  //一起看入口
  FREYAENTER = 31;
  //全屏一起看入口
  FREYAFULLENTER = 32;
  //片头片尾跳过设置
  SKIPOPED = 33;
  //录屏
  RECORDSCREEN = 34;
  //配音
  DUBBING = 35;
  //听视频入口
  LISTEN = 36;
  //稍后再看
  WATCH_LATER = 37;
  //系统录屏
  SYSTEM_RECORD = 38;
}

message PlayArcConf {
  // key->ConfType
  map<int32, ArcConf> arc_confs = 1;
}

// ArcConf 稿件维度
message ArcConf {
  //是否可用（可见）
  bool is_support = 1;
  //是否禁用
  bool disabled = 2;
  //提示信息
  ExtraContent extra_content = 3;
  //不支持的场景：1:首映
  repeated UnsupportScene unsupport_scene = 4;
  //不支持的状态：0：none；1：half；2：full；
  UnsupportState unsupport_state = 5;
}

enum UnsupportScene {
  UNKNOWN_SCENE = 0;
  PREMIERE = 1;
}

enum UnsupportState {
  NONE = 0;
  HALF = 1;
  FULL = 2;
}

message ExtraContent {
  //禁用的原因
  string disabled_reason = 1;
  //禁用的错误码
  int64 disabled_code = 2;
}

// PlayConf 播放三点配置信息
message PlayDeviceConf {
  // key->ConfType
  map<int32, DeviceConf> device_confs = 1;
}

message ConfValue {
  oneof value {
    //配置是否打开
    bool switch_val = 1;
    //枚举配置中选中的值
    int64 selected_val = 2;
  }
}

// 设备维度.
message DeviceConf {
  // 播放配置的值
  ConfValue conf_value = 1;
}

enum SettingItemType {
  // default
  SETTING_NONE = 0;
  //倍速(半屏幕，story)
  SETTING_PLAYBACK_RATE = 1;
  //稍后再看(半屏幕，story)
  SETTING_WATCH_LATER = 2;
  //缓存
  SETTING_DOWNlOAD = 3;
  //小窗(半屏幕，story)
  SETTING_SMALL_WINDOW = 4;
  //一起看
  SETTING_FREYAENTER = 5;
  //自动连播
  SETTING_PLAYBACK_MODE = 6;
  //循环播放
  SETTING_LOOP_PLAYBACK = 7;
  //定时开关(半屏幕，story)
  SETTING_TIMING_SWITCH = 8;
  //后台听视频(半屏幕，story)
  SETTING_BACKGROUND_PLAY = 9;
  //字幕(半屏幕，story)
  SETTING_SUBTITLE = 10;
  //字幕切换(半屏幕，story)
  SETTING_SUBTITLE_EXCHANGE = 11;
  //镜像反转(半屏幕，story)
  SETTING_FLIP_CONF = 12;
  //更多播放设置(半屏幕，story)
  SETTING_MORE_PLAY = 13;
  //播放震动
  SETTING_SHAKE = 14;
  //跳过头尾
  SETTING_SKIP_OPED = 15;
  //笔记
  SETTING_NOTE = 16;
  //举报(半屏幕，story)
  SETTING_REPORT = 17;
  //播放反馈(半屏幕,(story)
  SETTING_FEEDBACK = 18;
  //赠送好友
  SETTING_FREE_GIFT = 19;
  //配音
  SETTING_DUB = 20;
  //听视频（全屏）
  SETTING_LISTEN = 21;
  //电视投屏（全屏story半屏)
  SETTING_PROJECT = 22;
  //画面尺寸（全屏）
  SETTING_PIC_SIZE = 23;
  //全景播放（全屏）
  SETTING_PANORAMA = 24;
  //色觉辅助（全屏）
  SETTING_VISION_ASSIST = 25;
  //编辑播放器（全屏）
  SETTING_EDIT = 26;
  //不想看(半屏幕，story)
  SETTING_DISLIKE = 27;
  //必火
  SETTING_BIHUO = 28;
  //单双击手势(story)
  SETTING_GESTURE = 29;
  //弹幕设置(story)
  SETTING_DM = 30;
  //清晰度(story)
  SETTING_DEFINITION = 31;
  //反馈建议(story)
  SETTING_SUGGEST = 32;
  //自动上滑(story)
  SETTING_AUTOMATIC_SCROLL = 33;
  //互动视频进度回溯(半屏幕)
  SETTING_BACKTRACKING = 34;
  //AI配音
  SETTING_AI_AUDIO = 35;
  //AI配音 选项切换
  SETTING_AI_AUDIO_EXCHANGE = 36;
}

//half item 基础信息
message SettingBase{
  //左icon
  string left_icon = 1;
  //左title
  string left_title = 2;
  //item type
  SettingItemType type = 3;
  //是否可点击
  SettingControl control = 4;
  //上报
  map<string, string> report = 5;
}


message SettingItem{
  //基础信息
  SettingBase base = 1;
  //ui样式 分类
  SettingItemStyle style = 2;
  oneof value {
    //查看更多 独有value
    SettingMore more = 3;
    // 横排icon
    SettingVertical vertical = 4;
    //开关类型 额外信息
    SettingSwitch switch = 5;
  }
}

message SettingSwitch {
  //设置 文案 附属角标
  Badge badge = 1;
}

message SettingControl{
  //是否禁用
  bool disabled = 1;
  //禁用的原因
  string disabled_reason = 2;
  //true：禁用置灰； 默认false：按钮置灰
  bool disable_gray = 3;
}

message SettingGroup{
  //标题（全屏用）
  string title = 1;
  //group内item
  repeated SettingItem items = 2;
  //排列类型
  GroupStyle group_style = 3;
}

enum GroupStyle{
  //竖排
  GROUP_STYLE_DEFAULT = 0;
  //横排
  GROUP_STYLE_HORIZON = 1;
}


enum SettingItemStyle {
  // default
  SETTING_STYLE_NONE = 0;
  //开关
  SETTING_STYLE_SWITCH = 1;
  //查看更多
  SETTING_STYLE_MORE = 2;
  //选项
  SETTING_STYLE_SELECT = 3;
  // 横排小icon类型
  SETTING_STYLE_VERTICAL = 4;
}

message SettingVertical{
  // 跳转地址,有则可跳转，无则不可点击
  string url = 1;
  //跳转类型
  SettingJumpType jump_type = 2;
  //是否需要登录
  bool need_login = 3;
  //角标
  Badge badge = 4;
}

// 角标信息
message Badge {
  // 角标文案
  string text = 1;
  // 文案日间色值
  string text_color = 2;
  // 文案夜间色值
  string text_color_night = 3;
  // 背景日间色值
  string bg_color = 4;
  // 背景夜间色值
  string bg_color_night = 5;
  // 边框日间色值
  string border_color = 6;
  // 边框夜间色值
  string border_color_night = 7;
  // 角标样式：1为填充模式
  int32 bg_style = 8;
}

message SettingMore{
  // 跳转地址,有则可跳转，无则不可点击
  string url = 1;
  //右title
  string right_title = 2;
  //右icon
  string right_icon = 3;
  //跳转类型
  SettingJumpType jump_type = 4;
  //是否需要登录
  bool need_login = 5;
  //角标
  Badge badge = 6;
}

//跳转类型
enum SettingJumpType{
  //未知
  SETTING_JUMP_TYPE_NONE = 0;
  //跳转到新页面(app内浏览器打开)
  SETTING_JUMP_TYPE_OPEN_URL = 1;
  //半屏拉起页面
  SETTING_JUMP_TYPE_HALF_SCREEN = 2;
  //跳转到新页面(app外浏览器打开)
  SETTING_JUMP_TYPE_OPEN_URL_BY_OUTER_BROWSER = 3;
}