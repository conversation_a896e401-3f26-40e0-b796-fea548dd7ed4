syntax = "proto3";

package bilibili.cheese.gateway.player.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bilibili/cheese.gateway.player.v1;v1";
option java_multiple_files = true;
option java_package = "com.bapis.bilibili.cheese.gateway.player.v1";
option objc_class_prefix = "BAPICheeseGatewayPlayerV1";

import "bilibili/app/playurl/v1/playurl.proto";

// PlayURL 播放地址
service PlayURL {
    // 获取播放地址和云控配置信息
    rpc PlayView (PlayViewReq) returns (PlayViewReply);
    // 获取投屏地址
    rpc Project (ProjectReq) returns (ProjectReply);
}

// PlayViewReq 播放view请求参数
message PlayViewReq {
    // ep_id
    int64 ep_id = 1;
    // cid 互动视频或者垫片需要
    int64 cid = 2;
    // qn清晰度
    int64 qn = 3;
    // fnver和fnval标识视频格式
    int32 fnver = 4;
    // fnver和fnval标识视频格式
    int32 fnval = 5;
    // 下载参数 0-非下载 1-下载flv 2-下载dash
    uint32 download = 6;
    // 返回url是否强制使用域名(非ip地址), 1-http域名 2-https域名
    int32 force_host = 7;
    // 是否需要4k清晰度
    bool fourk = 8;
    // spmid 当前页面
    string spmid = 9;
    // from_spmid 上级页面
    string from_spmid = 10;
    // 青少年模式
    int32 teenagers_mode = 11;
    // 优先返回视频格式(h264 ,h265)
    bilibili.app.playurl.v1.CodeType prefer_codec_type = 12;
    // 是否强制请求预览视频
    bool is_preview = 13;
    // 是否强制请求清流
    bool is_raw_stream = 14;
    // widevine等级
    SecurityLevel security_level = 15;
}

// widevine等级
enum SecurityLevel {
    // 不支持
    LEVEL_UNKNOWN = 0;
    // L1
    LEVEL_L1 = 1;
    // L2
    LEVEL_L2 = 2;
    // L3
    LEVEL_L3 = 3;
}

// ProjectReq 投屏参数
message ProjectReq {
    // ep_id
    int64 ep_id = 1;
    // cid 互动视频或者垫片需要
    int64 cid = 2;
    // qn清晰度
    int64 qn = 3;
    // fnver和fnval标识视频格式
    int32 fnver = 4;
    // fnver和fnval标识视频格式
    int32 fnval = 5;
    // 下载参数 0-非下载 1-下载flv 2-下载dash
    uint32 download = 6;
    // 返回url是否强制使用域名(非ip地址), 1-http域名 2-https域名
    int32 force_host = 7;
    // 是否需要4k清晰度
    bool fourk = 8;
    // spmid 当前页面
    string spmid = 9;
    // from_spmid 上级页面
    string from_spmid = 10;
    // 使用协议 默认乐播=0，自建协议=1，云投屏=2
    int32 protocol = 11;
    // 投屏设备 默认其他=0，OTT设备=1
    int32 device_type = 12;
    // 是否返回flv格式视频(true:flv格式  false:mp4格式)
    bool flv_proj = 13;
    // 稿件ID
    int64 aid = 14;
}

//  PlayViewReply 播放页返回结果
message PlayViewReply {
    // play基础信息
    bilibili.app.playurl.v1.VideoInfo video_info = 1;
    //云控配置信息, 是否禁用
    PlayAbilityConf play_conf = 2;
    // 业务需要的其他信息
    PlayViewBusinessInfo business = 3;
    //风控
    RiskControl risk_control = 4;
}

// 云控配置信息, 是否禁用
message PlayAbilityConf {
    //后台播放
    bool background_play_disable = 1;
    //镜像反转
    bool flip_disable = 2;
    //返回视频的是否支持投屏
    bool cast_disable = 3;
    //反馈
    bool feedback_disable = 4;
    //字幕
    bool subtitle_disable = 5;
    //播放速度
    bool playback_rate_disable = 6;
    //定时停止播放
    bool time_up_disable = 7;
    //播放方式
    bool playback_mode_disable = 8;
    //画面尺寸
    bool scale_mode_disable = 9;
    //顶
    bool like_disable = 10;
    //踩
    bool dislike_disable = 11;
    //投币
    bool coin_disable = 12;
    //充电
    bool elec_disable = 13;
    //分享
    bool share_disable = 14;
    //截图/gif
    bool screen_shot_disable = 15;
    //锁屏
    bool lock_screen_disable = 16;
    //相关推荐
    bool recommend_disable = 17;
    //倍速
    bool playback_speed_disable = 18;
    //清晰度
    bool definition_disable = 19;
    //选集
    bool selections_disable = 20;
    //下一集
    bool next_disable = 21;
    //编辑弹幕
    bool edit_dm_disable = 22;
    //外层面板弹幕设置（实验组1）
    bool outer_dm_disable = 25;
    //三点内弹幕设置（实验组2）
    bool inner_dm_disable = 26;
    //画中画(小窗和画中画)
    bool small_window_disable = 27;
    //不支持投屏原因错误码
    string cast_disable_reason_code = 28;
    //不支持投屏原因文案
    string cast_disable_reason_text = 29;
}

// ProjectReply 投屏地址返回结果
message ProjectReply {
    bilibili.app.playurl.v1.PlayURLReply project = 1;
}

// 其他业务信息
message PlayViewBusinessInfo {
    // drm技术类型
    DrmTechType drm_tech_type = 1;
}

// drm技术类型
enum DrmTechType {
    // 无
    NON = 0;
    // fair play
    FAIR_PLAY = 1;
    // widevine
    WIDE_VINE = 2;
    // bili_drm
    BILI_DRM = 3;
}

// 其他业务信息
message RiskControl {
    //是否需要发送短信
    bool need_send_sms = 1;
    //风险拦截标题
    string title = 2;
    //风险文案
    string risk_message = 3;
    //风险拦截按钮文案
    string action_desc = 4;
    //发送短信的地址
    string send_sms_url = 5;
    //需要发短信的设备
    string buvid = 6;
}