syntax = "proto3";

package bilibili.intl.app.opus.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bilibili/intl.app.opus.v1;v1";
option java_multiple_files = true;
option java_package = "com.bapis.bilibili.intl.app.opus.v1";
option objc_class_prefix = "BAPIIntlAppOpusV1";

import "bilibili/intl/app/opus/common/opus.proto";

// C端渲染用Opus，直接封装原始的Opus数据，并提供额外的页面模块
message OpusForConsumer {
  // 图文id
  int64 opus_id = 1;
  // 封面：仅在列表页中展示图片使用。
  // 客户端和发布器不需要填充该字段，只需要按需读取即可。
  // 服务端和网关会在列表页场景填充该字段，供列表渲染使用。
  OpusCoverForConsumer cover = 2;
  // 详情页的头部信息：包括 标题、图文顶部大封面（专栏就是封面）等。
  // 仅详情页使用，后续可拓展。
  OpusHeaderForConsumer header = 3;
  // 正文内容：图片段落、文字段落
  // 无论是详情还是列表页，都可以按顺序渲染，依次往下排即可。
  OpusContentForConsumer content = 4;
  // 交互计数信息。
  // 详情页需展示在页面底部。
  // 收藏/创作中心等列表等客户端按情况排列即可。
  OpusInteractionStat interaction_stat = 5;
  // 扩展信息，主要是一些业务表现无关的元数据信息。
  // 可用作埋点，后台异步操作等等。
  OpusExtendInfo extend_info = 6;
  // web/h5发布器的原始html内容，因兼容性保留。
  // NA客户端不用处理。
  string raw_content = 7;
  // 图文发布侧的元信息，包括审核状态等
  OpusCreationInfo creation_info = 8;
  // 图文三点结构
  OpusThreePointInfo three_point = 9;
}

message OpusThreePointInfo {
  // 三点列表
  repeated OpusThreePointItem items = 1;
}

message OpusThreePointItem {
  // 三点类型
  OpusThreePointType type = 1;
  // 三点文案
  string text = 2;
  // 三点数据
  oneof data {
    //收藏数据
    FavData fav_data = 3;
  }
}

message FavData {
  //收藏状态
  bool fav = 1;
  //已收藏->未收藏，展示的文案：my list
  string fav_text = 2;
  //未收藏->已收藏，状态变化后，展示的文案：remove from my list
  string fav_checked_text = 3;
}

message OpusCreationInfo {
  //图文审核状态
  OpusACL acl = 1;
  //图文审核状态文案
  string acl_text = 2;
  //非公开状态，点击封面，toast文案
  OpusACLToast toast = 3;
  //非公开状态，点击问号，弹窗文案
  OpusACLPopup popup = 4;
}

message OpusACLToast {
  // toast样式
  OpusACLToastStyle toast_style = 1;
  // 非公开状态，点击提示文案
  oneof acl_toast {
    // 简单文字
    string acl_toast_simple = 2;
  }
}

message OpusACLPopup {
  string text = 1;
  string btn_text = 2;
}

message OpusCoverForConsumer {
  // 原始的opus Cover
  bilibili.intl.app.opus.common.OpusCover origin_opus_cover = 1;
}

message OpusHeaderForConsumer {
  // 原始的opus Header
  bilibili.intl.app.opus.common.OpusHeader origin_opus_header = 1;
  // 冗余的opusSrcType，端上不同type的标题样式不同。由网关设置。
  bilibili.intl.app.opus.common.OpusSource opus_source_type = 2;
  // 用于渲染的作者信息：名称/头像等等
  OpusAuthor author_info = 3;
  // 图文发布时间
  OpusPubInfo pub_info = 4;
}

message OpusPubInfo {
  //格式化后的发布时间，基于 origin_pub_info
  string pub_time = 1;
  //发布ip地址对应的城市
  string city = 2;
  //发布经纬度信息
  double latitude = 3;
  double longitude = 4;
  //跳转链接
  string uri = 5;
}

message OpusContentForConsumer {
  // 原始的opus Content
  bilibili.intl.app.opus.common.OpusContent origin_opus_content = 1;
}

message OpusExtendInfo {
  // 冗余的opus id
  int64 opus_id = 1;
  // 卡片点击的跳转链接，为空则不跳转。
  // 收藏/创作中心列表会用到。
  string card_jump_url = 2;
  // 图文作品发布类型 一般作为业务标记+埋点使用。
  // 仅做一些布局/页面样式区分用。客户端的具体业务逻辑不应该强依赖该类型。
  bilibili.intl.app.opus.common.OpusSource opus_source_type = 3;
  // 原始的发布内容的元信息，包括发布时间/发布人等。客户端可以读，用作埋点之类的需求。
  // 客户端和发布器不需要填充该字段。
  bilibili.intl.app.opus.common.PubInfo origin_pub_info = 4;
}

message OpusAuthor {
  int64 uid = 1;
  string name = 2;
  string avatar = 3;
  // 当前登录用户对该用户是否已关注
  bool is_following = 4;
  // 用户认证信息
  OfficialInfo official_info = 5;

  int32 sex = 6;
  int32 age = 7;
  // 用户语聊房等级
  repeated LevelInfo level_info = 8;
  // 用户跳转链接
  string uri = 9;
}

message OfficialInfo {
  //角色
  int64 role = 1;
  //角色icon
  string icon = 2;
}

message LevelInfo {
  string level = 1;
  string icon = 2;
  string icon_bg_color = 3;
}

message OpusInteractionStat {
  // 一字排开的交互按钮：分享/点赞/评论等
  repeated InteractionButton interact_btns = 1;
}

enum InteractionType {
  // 无效交互类型，遇到直接忽略
  INTERACTION_INVALID = 0;
  // 分享
  INTERACTION_SHARE = 1;
  // 点赞
  INTERACTION_LIKE = 2;
  // 收藏
  INTERACTION_FAV = 3;
  // 评论
  INTERACTION_COMMENT = 4;
  // 浏览量
  INTERACTION_VIEW = 5;
}

// 图文的访问权限控制信息
enum OpusACL {
  // 无效的权限
  OPUS_ACL_NONE = 0;
  // 公开
  OPUS_ACL_PUBLIC = 1;
  // 已驳回
  OPUS_ACL_REJECTED = 2;
  // 审核中
  OPUS_ACL_AUDIT = 3;
}

enum OpusThreePointType {
  OPUS_THREE_POINT_NONE = 0;
  //收藏
  OPUS_THREE_POINT_FAV = 1;
  //举报
  OPUS_THREE_POINT_REPORT = 2;
  //删除
  OPUS_THREE_POINT_DELETE = 3;
  //分享
  OPUS_THREE_POINT_SHARE = 4;
}

enum OpusACLToastStyle {
  // 简单文字
  OPUS_ACL_TOAST_SIMPLE = 0;
  // 带按钮的toast
  OPUS_ACL_TOAST_BTN = 1;
}

message InteractionButton {
  // 交互类型：转赞评等等
  InteractionType interaction_type = 1;
  // 属性相关：
  // 这个iconBtn是否可以有选中态。
  // 例如：点赞后变个色/收藏后变个色
  bool can_check = 2;
  // 属性相关：
  // 这个iconBtn是否处于选中态。
  // 例如：已点赞/已收藏
  bool is_checked = 3;
  // 交互量数据，如果为0就只展示icon本身。
  int64 stat_number = 4;
  // 交互相关：
  // 是否禁止交互。具体行为以及是否要实现，取决于交互类型。
  bool ban_interact = 5;
  // 交互量数据 文字展示
  string stat_number_text = 6;
}
