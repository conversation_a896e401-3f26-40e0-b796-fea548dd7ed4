syntax = "proto3";

// `buf.bilibili.co:"bapis/bapis-gen,prefergogo"`

package bilibili.dagw.component.avatar.v1.plugin;

option go_package          = "buf.bilibili.co/bapis/bapis-gen/bilibili/dagw.component.avatar.v1.plugin;plugin";
option java_multiple_files = true;
option java_package        = "com.bapis.bilibili.dagw.component.avatar.v1.plugin";
option objc_class_prefix   = "BAPIDagwComponentAvatarV1Plugin";

message CommentDoubleClickConfig {
  // 评论双击特效
  Interaction interaction = 1;
  // 放大倍数
  double animation_scale = 2;
}

message Interaction {
  string nft_id = 1;
  bool enabled = 2;
  string itype = 3;
  string metadata_url = 4;
}