package com.bilibili.sycp.personal.mgk.biz.common.constants;

public class Constants {

    public static final String MGK_LAUNCH_URL_PROD_PC = "https://gaoneng.bilibili.com/tetris/page/?pageId=%s";

    public static final String MGK_LAUNCH_URL_TEST_PC = "http://cm-mng.bilibili.co/tetris/page/?pageId=%s";

    public static final String MGK_PAGE_MACRO_PARAM = "buvid=__BUVID__&mid=__MID__&imei=__IMEI__&duid=__DUID__&idfa=__IDFA__&android_id=__ANDROIDID__&os=__OS__&request_id=__REQUESTID__&source_id=__SOURCEID__&track_id=__TRACKID__&creative_id=__CREATIVEID__&adtype=__ADTYPE__";

    //现在的落地页上会有两个立即提交的按钮，添加此参数是为了让前端去掉其中一个
    public static final String REMOVE_SECOND_BUTTON_CONFIG = "is_gaoneng_fake=1";

    //表单隐私协议名称
    public static final String PRIVACY_TEXT = "《个人信息授权书与保护声明》";

    //表单隐私协议名称
    public static final String PRIVACY_LINK = "https://www.bilibili.com/blackboard/activity-dQw40P7c6U.html?account_name=%s&mid=%s";

    //个人建站兜底落地页
    public static final String PERSONAL_MGK_BACK_UP_TEST_URL = "http://uat-cm.bilibili.com/site-h5/page.html#/?pageId=%s";

    //个人建站兜底落地页
    public static final String PERSONAL_MGK_BACK_UP_PROD_URL = "http://b.bilibili.com/site-h5/page.html#/?pageId=%s";

}
