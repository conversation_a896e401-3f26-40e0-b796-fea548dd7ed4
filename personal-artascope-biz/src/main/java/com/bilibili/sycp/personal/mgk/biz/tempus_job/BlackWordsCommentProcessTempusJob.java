
package com.bilibili.sycp.personal.mgk.biz.tempus_job;

import com.bilibili.sycp.personal.mgk.biz.api.comment_component.ICommentComponentService;
import com.bilibili.sycp.personal.mgk.biz.databus.FlyCommentComponentChangeProducer;
import com.bilibili.sycp.personal.mgk.biz.grpc.CommentRpcService;
import com.bilibili.sycp.personal.mgk.biz.impl.form.FormService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.util.Optional;


/**
 * <AUTHOR>
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BlackWordsCommentProcessTempusJob implements BasicProcessor {

    public static final String ID = "blackWordsCommentProcessJob";

    public static final long LIMIT = 100;

    public static final int PARTITION = 10;

    private final CommentRpcService commentRpcService;

    private final ICommentComponentService commentComponentService;

    private final FlyCommentComponentChangeProducer componentChangeProducer;

    private final FormService formService;


    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();

        String jobParams = Optional.ofNullable(context.getJobParams()).orElse("S");
        logger.info("Current job params:{}", jobParams);
        log.info("BlackWordsCommentProcessTempusJob start time:{}", System.currentTimeMillis());
        System.out.println("DEBUG: BlackWordsCommentProcessTempusJob start time: " + System.currentTimeMillis());

        formService.personalCommentBlackWordProcess(0L);

        log.info("BlackWordsCommentProcessTempusJob end time:{}", System.currentTimeMillis());
        System.out.println("DEBUG: BlackWordsCommentProcessTempusJob end time: " + System.currentTimeMillis());

        return jobParams.contains("F") ? new ProcessResult(false) : new ProcessResult(true, "success");
    }
}
