package com.bilibili.sycp.personal.mgk.biz.impl.form;

import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.mgk.platform.api.data.dto.ExtFormDataDto;
import com.bilibili.mgk.platform.api.data.dto.QueryReportDataParamDto;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.bilibili.sycp.personal.mgk.biz.api.account.IAccountService;
import com.bilibili.sycp.personal.mgk.biz.api.archive.IArchiveService;
import com.bilibili.sycp.personal.mgk.biz.api.archive.dto.ArchiveBo;
import com.bilibili.sycp.personal.mgk.biz.api.data.IPersonalMgkDataService;
import com.bilibili.sycp.personal.mgk.biz.api.form.IFormService;
import com.bilibili.sycp.personal.mgk.biz.api.form.dto.*;
import com.bilibili.sycp.personal.mgk.biz.api.landing_page.ILandingPageService;
import com.bilibili.sycp.personal.mgk.biz.api.landing_page.dto.NewLandingPageDto;
import com.bilibili.sycp.personal.mgk.biz.common.constants.IsDeleted;
import com.bilibili.sycp.personal.mgk.biz.common.dto.Operator;
import com.bilibili.sycp.personal.mgk.biz.common.dto.PageResult;
import com.bilibili.sycp.personal.mgk.biz.common.enums.*;
import com.bilibili.sycp.personal.mgk.biz.common.util.SnowflakeIdWorker;
import com.bilibili.sycp.personal.mgk.biz.common.util.TimeUtil;
import com.bilibili.sycp.personal.mgk.biz.common.util.Utils;
import com.bilibili.sycp.personal.mgk.biz.config.MgkDataSourceConfig;
import com.bilibili.sycp.personal.mgk.biz.dao.ad.generated.tables.pojos.AccAccountPo;
import com.bilibili.sycp.personal.mgk.biz.dao.mgk.generated.DefaultSchema;
import com.bilibili.sycp.personal.mgk.biz.dao.mgk.generated.tables.pojos.MgkFormItemPo;
import com.bilibili.sycp.personal.mgk.biz.dao.mgk.generated.tables.pojos.MgkFormPo;
import com.bilibili.sycp.personal.mgk.biz.dao.mgk.generated.tables.pojos.MgkLandingPagePo;
import com.bilibili.sycp.personal.mgk.biz.dao.mgk.generated.tables.records.PersonalMgkCommentComponetRecord;
import com.bilibili.sycp.personal.mgk.biz.impl.block.BlackWordsService;
import com.bilibili.sycp.personal.mgk.biz.impl.message.MessageSendService;
import com.google.common.collect.Lists;
import com.mysema.commons.lang.Pair;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class FormService implements IFormService {

    private static final Logger logger = LoggerFactory.getLogger(FormService.class);

    private final DSLContext mgkDslContext;

    private final ILandingPageService landingPageService;

    private final SnowflakeIdWorker snowflakeIdWorker;

    private final IArchiveService archiveService;

    private final ISoaLandingPageService soaLandingPageService;

    private final IPersonalMgkDataService personalMgkDataService;

    private final BlackWordsService blackWordsService;

    private final IAccountService accountService;

    private final MessageSendService messageSendService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private final static DefaultSchema mgk = DefaultSchema.DEFAULT_SCHEMA;

    private static final String COMMENT_FORM_FLOAT_PAGE_CONFIG = "{\"name\":\"$name\",\"title\":\"$title\",\"header\":0,\"page_bg_color\":\"\",\"page_bg_url\":\"\",\"blocks\":[{\"id\":\"2549837c-617e-42eb-98da-2f78516bfcfd\",\"name\":\"fixed-block\",\"active\":false,\"components\":[],\"data\":{\"bg\":\"#ffffff\",\"height\":200},\"hasError\":false},{\"id\":\"30a9dd16-ea26-46ad-85cf-bb6fdbec27c0\",\"name\":\"block\",\"active\":false,\"components\":[{\"id\":\"005bbdf4-b5a3-48c4-8337-6226f849c3c7\",\"name\":\"plain-text-normal\",\"active\":false,\"data\":{\"content\":{\"content\":\"$label\"},\"style\":{\"fontName\":\"normal\",\"color\":\"#333\",\"fontSize\":13,\"letterSpacing\":0,\"lineHeight\":28,\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"none\",\"textAlign\":\"left\"},\"layout\":{\"resizeWidth\":1,\"resizeHeight\":1,\"aspectRatio\":0,\"left\":9,\"top\":10,\"width\":357,\"height\":28,\"rotate\":0}},\"fixed\":\"\",\"position\":true,\"hasError\":false}],\"data\":{\"bg\":\"#ffffff\",\"height\":30},\"hasError\":false},{\"id\":\"72a79733-4705-46b5-9a38-8dadb61527a2\",\"name\":\"block\",\"active\":false,\"components\":[{\"id\":\"0b71cc89-e1a3-4337-8235-55c74bbef789\",\"name\":\"form-normal\",\"active\":false,\"data\":{\"form\":{\"title\":\"\",\"successMessage\":\"\",\"id\":\"$formId\",\"useTitle\":0},\"advanceSetting\":{\"count\":{\"status\":false,\"order\":\"asc\",\"middle\":100,\"position\":\"top\",\"prefix\":\"目前已有\",\"suffix\":\"人参与活动\",\"style\":0,\"color\":\"#FE5656\"},\"link\":{\"status\":false,\"type\":\"linkEvent\",\"behavior\":{\"url\":\"\",\"scheme\":\"\"}},\"recentSubmit\":{\"status\":false,\"style\":\"scrollWall\",\"styleColor\":\"default\",\"useTimeDesc\":true,\"desc\":\"xx分钟前\"},\"locationButton\":{\"status\":false,\"color\":\"#0A8AFA\"}},\"style\":{\"inputStyle\":3,\"themeColor\":\"#0A8AFA\",\"background\":\"#FFFFFF\",\"color\":{\"text\":\"#333333\",\"inputPlaceholder\":\"#cccccc\",\"inputBg\":\"#FFFFFF\",\"inputBorder\":\"#f0f0f0\"}},\"buttonStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":16,\"backgroundColor\":\"#0A8AFA\",\"borderColor\":\"#0A8AFA\",\"borderWidth\":1,\"borderRadius\":4,\"animation\":\"breath\"},\"layout\":{\"resizeWidth\":1,\"resizeHeight\":0,\"aspectRatio\":0,\"left\":0,\"top\":0,\"width\":375,\"height\":184,\"rotate\":0}},\"fixed\":\"\",\"position\":true,\"hasError\":false}],\"data\":{\"bg\":\"#ffffff\",\"height\":334},\"hasError\":false}],\"page_id\":null}";
    private static final String COMMENT_FORM_FLOAT_PAGE_NAME_SUFFIX = "评论区表单";
    private static final Timestamp COMMENT_FLOAT_PAGE_EFFECTIVE_END_TIME = TimeUtil.isoTimeStr2Timestamp("2032-01-10 00:00:00");
    private static final String _NAME = "$name";
    private static final String _TITLE = "$title";
    private static final String _LABEL = "$label";
    private static final String _FORM_ID = "$formId";
    //0x011111  新建站401 0x100010 页面含有微信小游戏 0x010100 老建站
    private static final String COMMENT_FORM_FLOAT_PAGE_VERSION = "0x011111";

    @Override
    @Transactional(transactionManager = MgkDataSourceConfig.MGK_TX_MGR, rollbackFor = Exception.class)
    public long create(Operator operator, NewMgkFormDto newMgkFormDto) {
        //校验 黑词
        Pair<Boolean, String> conversionUrlCheckResult = blackWordsService.checkInBlackWord(newMgkFormDto.getConversionUrlText());
        Assert.isTrue(!conversionUrlCheckResult.getFirst(), "输入信息不符合规范，包含违规引流敏感词");
        Pair<Boolean, String> generalCommentTextCheckResult = blackWordsService.checkInBlackWord(newMgkFormDto.getGeneralCommentText());
        Assert.isTrue(!generalCommentTextCheckResult.getFirst(), "输入信息不符合规范，包含违规引流敏感词");

        var formId = this.createForm(newMgkFormDto);

        this.createFormItem(formId, newMgkFormDto.getItems());

        var mgkFormItemDto = newMgkFormDto.getItems().stream()
                .filter(item -> FormItemTypeEnum.TELEPHONE.getId().equals(item.getType()))
                .findFirst().orElse(null);
        Assert.notNull(mgkFormItemDto, "表单项数据不包含手机号,请您重新创建");
        var pageId = generateFormLandingPage(operator, formId, newMgkFormDto.getPageTitle(), mgkFormItemDto.getLabel());

        //生成组件信息
        mgkDslContext.newRecord(mgk.PERSONAL_MGK_COMMENT_COMPONET)
                .setGeneralCommentText(newMgkFormDto.getGeneralCommentText())
                .setConversionUrlText(newMgkFormDto.getConversionUrlText()).setFormId(formId)
                .setTextLocation(newMgkFormDto.getTextLocation()).setPageId(pageId).insert();

        return formId;
    }

    @Override
    @Transactional(transactionManager = MgkDataSourceConfig.MGK_TX_MGR, rollbackFor = Exception.class)
    public long update(Operator operator, NewMgkFormDto update) {
        var pageResult = archiveService.getArcsByModuleId(operator.getOperatorId(),
                update.getFormId(), 1, 1);
        Assert.isTrue(pageResult.getTotal() == 0, "当前组件已绑定视频,不允许编辑");

        //校验 黑词
        Pair<Boolean, String> conversionUrlCheckResult = blackWordsService.checkInBlackWord(update.getConversionUrlText());
        Assert.isTrue(!conversionUrlCheckResult.getFirst(), "输入信息不符合规范，包含违规引流敏感词");
        Pair<Boolean, String> generalCommentTextCheckResult = blackWordsService.checkInBlackWord(update.getGeneralCommentText());
        Assert.isTrue(!generalCommentTextCheckResult.getFirst(), "输入信息不符合规范，包含违规引流敏感词");

        var reportData = personalMgkDataService
                .getReportDatas(QueryReportDataParamDto.builder().removeCheat(true)
                        .accountId(operator.getOperatorId()).formId(update.getFormId()).build());
        Assert.isTrue(CollectionUtils.isEmpty(reportData), "当前组件已产生数据,不允许编辑");

        var formRecord = Optional.ofNullable(mgkDslContext.fetchOne(mgk.MGK_FORM,
                mgk.MGK_FORM.FORM_ID.eq(update.getFormId()),
                mgk.MGK_FORM.STATUS.eq(FormStatusEnum.USING.getCode()))).orElseThrow(() -> new StatusRuntimeException(
                Status.INVALID_ARGUMENT.withDescription("查找不到有效的表单记录")));
        formRecord.setPageTitle(update.getPageTitle()).setName(update.getName()).store();

        deleteFormItem(update.getFormId());
        this.createFormItem(update.getFormId(), update.getItems());

        var commentComponetRecord =
                Optional.ofNullable(mgkDslContext.fetchOne(mgk.PERSONAL_MGK_COMMENT_COMPONET,
                                mgk.PERSONAL_MGK_COMMENT_COMPONET.FORM_ID.eq(update.getFormId())))
                        .orElseThrow(() -> new StatusRuntimeException(
                                Status.INVALID_ARGUMENT.withDescription("invalid formId " + update.getFormId())));
        commentComponetRecord.setGeneralCommentText(update.getGeneralCommentText())
                .setTextLocation(update.getTextLocation())
                .setConversionUrlText(update.getConversionUrlText())
                .store();

        var formDto = getPageFormByFormId(update.getFormId());
        soaLandingPageService.refreshLandingPageCache(Long.parseLong(formDto.getPageId()));
        return update.getFormId();
    }

    private long createForm(NewMgkFormDto newMgkFormDto) {
        var formId = snowflakeIdWorker.nextId();

        var formPo = new MgkFormPo();
        formPo.setFormId(formId);
        formPo.setAccountId(newMgkFormDto.getAccountId());
        formPo.setName(StringUtils.trimWhitespace(newMgkFormDto.getName()));
        formPo.setStatus(FormStatusEnum.USING.getCode());
        formPo.setFormType(MgkFormTypeEnum.getByCode(newMgkFormDto.getFormType()).getCode());
        formPo.setPageTitle(newMgkFormDto.getPageTitle());
        formPo.setComeFrom(newMgkFormDto.getComeFrom() == null ? ComeFromEnum.WEB.getCode() : newMgkFormDto.getComeFrom());
        var record = mgkDslContext.newRecord(mgk.MGK_FORM);
        record.from(formPo);
        record.insert();

        return formId;
    }

    private void deleteFormItem(long formId) {
        mgkDslContext.update(mgk.MGK_FORM_ITEM).set(mgk.MGK_FORM_ITEM.IS_DELETED, IsDeleted.DELETED)
                .where(mgk.MGK_FORM_ITEM.FORM_ID.eq(formId)).execute();
    }

    private void createFormItem(long formId, List<NewMgkFormItemDto> formItemDtos) {
        Stream.iterate(0, i -> i + 1).limit(formItemDtos.size()).forEach(i -> {
            var formItemId = snowflakeIdWorker.nextId();
            var itemDto = formItemDtos.get(i);
            var itemPo = new MgkFormItemPo();
            BeanUtils.copyProperties(itemDto, itemPo);
            itemPo.setSortNumber(i);
            itemPo.setFormId(formId);
            itemPo.setFormItemId(formItemId);
            itemPo.setOptionsVal("[]");
            var record = mgkDslContext.newRecord(mgk.MGK_FORM_ITEM);
            record.from(itemPo);
            record.insert();
        });
    }

    private long generateFormLandingPage(Operator operator, Long formId, String title, String label) {
        var landingPageDto = NewLandingPageDto.builder()
                .accountId(operator.getOperatorId()).effectiveStartTime(new Timestamp(System.currentTimeMillis()))
                .effectiveEndTime(COMMENT_FLOAT_PAGE_EFFECTIVE_END_TIME).isModel(IsModelEnum.COMMENT.getCode())
                .templateStyle(TemplateStyleEnum.APPLETS.getCode()).type(LandingPageTypeEnum.APPLETS.getCode())
                .pageVersion(COMMENT_FORM_FLOAT_PAGE_VERSION).formId(0L).build();

        landingPageDto.setFormId(formId);
        landingPageDto.setFormIds(Lists.newArrayList(formId));
        landingPageDto.setName(COMMENT_FORM_FLOAT_PAGE_NAME_SUFFIX + formId);
        landingPageDto.setTitle(title);
        var config = COMMENT_FORM_FLOAT_PAGE_CONFIG
                .replace(_FORM_ID, formId.toString())
                .replace(_NAME, landingPageDto.getName())
                .replace(_TITLE, landingPageDto.getTitle())
                .replace(_LABEL, label);
        landingPageDto.setConfig(config);
        return landingPageService.create(operator, landingPageDto);
    }


    @Override
    public PageResult<MgkFormDto> getPageFormDtos(QueryFormParamDto query) {
        var condition = buildCondition(query);
        var count = mgkDslContext.fetchCount(mgk.MGK_FORM, condition);
        if (!Utils.isPositive(count)) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        var page = query.getPage();
        var records = mgkDslContext.selectFrom(mgk.MGK_FORM)
                .where(condition)
                .orderBy(mgk.MGK_FORM.MTIME.desc())
                .offset(page.getOffset()).limit(page.getLimit()).fetch();
        var formDtos = records.stream()
                .map(record -> record.into(MgkFormDto.class)).collect(Collectors.toList());

        //表单ids
        var formIds = formDtos.stream().map(MgkFormDto::getFormId)
                .collect(Collectors.toList());

        //查询表单项信息
        var formId2itemsMap = convertFormItemPos2Dtos(getFormItemPosInFormIds(formIds)).stream()
                .collect(Collectors.groupingBy(MgkFormItemDto::getFormId));
        formDtos = formDtos.stream().peek(mgkFormDto ->
                mgkFormDto.setItems(formId2itemsMap.get(mgkFormDto.getFormId()))
        ).collect(Collectors.toList());

        //获取联系包和联系信息映射
        var comments = mgkDslContext.selectFrom(mgk.PERSONAL_MGK_COMMENT_COMPONET)
                .where(mgk.PERSONAL_MGK_COMMENT_COMPONET.FORM_ID.in(formIds))
                .fetch();
        var formId2Comment = comments.stream()
                .collect(Collectors.toMap(PersonalMgkCommentComponetRecord::getFormId,
                        Function.identity()));

        formDtos = formDtos.stream().peek(formDto -> {
            var commentComponetRecord = formId2Comment.getOrDefault(formDto.getFormId(), new PersonalMgkCommentComponetRecord());
            formDto.setGeneralCommentText(commentComponetRecord.getGeneralCommentText());
            formDto.setTextLocation(commentComponetRecord.getTextLocation());
            formDto.setConversionUrlText(commentComponetRecord.getConversionUrlText());
            if (!Utils.isPositive(commentComponetRecord.getFormId()) || IsDeleted.DELETED == commentComponetRecord.getIsDeleted()) {
                formDto.setStatus(FormStatusEnum.FORBIDDEN.getCode());
            }
        }).collect(Collectors.toList());

        var formId2ExtFormDataMap = personalMgkDataService.getFormId2ExtFormDataMap(formIds);
        formDtos = formDtos.stream().peek(dto -> {
            ExtFormDataDto extFormData = formId2ExtFormDataMap.getOrDefault(dto.getFormId(),
                    ExtFormDataDto.builder().formDataCount(0).recentSubmitTime(null).build());
            dto.setFormDataCount(extFormData.getFormDataCount());
            dto.setRecentDataSubmitTime(extFormData.getRecentSubmitTime());
        }).collect(Collectors.toList());

        return PageResult.<MgkFormDto>builder().total(count).records(formDtos).build();
    }

    @Override
    public List<MgkFormDto> getBaseFormDtos(QueryFormParamDto query) {
        var condition = buildCondition(query);
        var records = mgkDslContext.selectFrom(mgk.MGK_FORM)
                .where(condition)
                .orderBy(mgk.MGK_FORM.MTIME.desc())
                .fetch();
        var formDtos = records.stream()
                .map(record -> record.into(MgkFormDto.class)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(formDtos)) {
            return new ArrayList<>();
        }

        //表单ids
        var formIds = formDtos.stream().map(MgkFormDto::getFormId)
                .collect(Collectors.toList());

        //获取联系包和联系信息映射
        var comments = mgkDslContext.selectFrom(mgk.PERSONAL_MGK_COMMENT_COMPONET)
                .where(mgk.PERSONAL_MGK_COMMENT_COMPONET.FORM_ID.in(formIds),
                        mgk.PERSONAL_MGK_COMMENT_COMPONET.IS_DELETED.eq(IsDeleted.VALID)).fetch();
        var formId2PageId = comments.stream()
                .collect(Collectors.toMap(PersonalMgkCommentComponetRecord::getFormId,
                        PersonalMgkCommentComponetRecord::getPageId));
        return formDtos.stream()
                .filter(r -> null != formId2PageId.get(r.getFormId()))
                .peek(formDto -> formDto.setPageId(formId2PageId.get(formDto.getFormId()).toString()))
                .collect(Collectors.toList());
    }

    public Condition buildCondition(QueryFormParamDto query) {
        var condition = mgk.MGK_FORM.IS_DELETED.eq(IsDeleted.VALID);
        condition = condition.and(Utils.isPositive(query.getAccountId()) ?
                mgk.MGK_FORM.ACCOUNT_ID.eq(query.getAccountId()) : DSL.noCondition());
        condition = condition.and(CollectionUtils.isEmpty(query.getFormIds()) ?
                DSL.noCondition() : mgk.MGK_FORM.FORM_ID.in(query.getFormIds()));
        condition = condition.and(CollectionUtils.isEmpty(query.getStatusList()) ?
                DSL.noCondition() : mgk.MGK_FORM.STATUS.in(query.getStatusList()));
        condition = condition.and(StringUtils.hasText(query.getNameLike()) ?
                mgk.MGK_FORM.NAME.like("%" + query.getNameLike() + "%") : DSL.noCondition());
        condition = condition.and(Objects.isNull(query.getBeginTime()) ?
                DSL.noCondition() : mgk.MGK_FORM.MTIME.greaterOrEqual(query.getBeginTime()));
        condition = condition.and(Objects.isNull(query.getEndTime()) ?
                DSL.noCondition() : mgk.MGK_FORM.MTIME.lessOrEqual(query.getEndTime()));
        return condition;
    }

    private List<MgkFormItemPo> getFormItemPosInFormIds(List<Long> formIds) {
        if (CollectionUtils.isEmpty(formIds)) {
            return Collections.emptyList();
        }

        var itemRecords = mgkDslContext.selectFrom(mgk.MGK_FORM_ITEM)
                .where(mgk.MGK_FORM_ITEM.FORM_ID.in(formIds),
                        mgk.MGK_FORM_ITEM.IS_DELETED.eq(IsDeleted.VALID))
                .fetch();

        return itemRecords.stream().map(record -> record.into(MgkFormItemPo.class))
                .collect(Collectors.toList());
    }

    private List<MgkFormItemDto> convertFormItemPos2Dtos(List<MgkFormItemPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().sorted(Comparator.comparing(MgkFormItemPo::getSortNumber))
                .map(this::convertFormItemPo2Dto).collect(Collectors.toList());
    }

    private MgkFormItemDto convertFormItemPo2Dto(MgkFormItemPo po) {
        var dto = MgkFormItemDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Override
    public MgkFormDto getPageFormByFormId(long formId) {
        var record = mgkDslContext.selectFrom(mgk.MGK_FORM)
                .where(mgk.MGK_FORM.FORM_ID.eq(formId),
                        mgk.MGK_FORM.STATUS.eq(FormStatusEnum.USING.getCode()),
                        mgk.MGK_FORM.IS_DELETED.eq(IsDeleted.VALID))
                .fetchOne();
        Assert.notNull(record, "找不到对应的表单数据");

        //查询表单项信息
        var formDto = record.into(MgkFormDto.class);
        var formId2itemsMap = convertFormItemPos2Dtos(
                getFormItemPosInFormIds(Lists.newArrayList(formDto.getFormId()))).stream()
                .collect(Collectors.groupingBy(MgkFormItemDto::getFormId));
        formDto.setItems(formId2itemsMap.get(formDto.getFormId()));


        //查询评论组件信息
        var commentRecord = mgkDslContext.selectFrom(mgk.PERSONAL_MGK_COMMENT_COMPONET)
                .where(mgk.PERSONAL_MGK_COMMENT_COMPONET.FORM_ID.eq(formDto.getFormId()),
                        mgk.PERSONAL_MGK_COMMENT_COMPONET.IS_DELETED.eq(IsDeleted.VALID))
                .fetchOne();
        Assert.notNull(commentRecord, "查询不到表单对应的组件信息");
        formDto.setConversionUrlText(commentRecord.getConversionUrlText());
        formDto.setGeneralCommentText(commentRecord.getGeneralCommentText());
        formDto.setTextLocation(commentRecord.getTextLocation());
        formDto.setPageId(commentRecord.getPageId().toString());

        return formDto;
    }

    @Override
    @Transactional(transactionManager = MgkDataSourceConfig.MGK_TX_MGR, rollbackFor = Exception.class)
    public void delete(Operator operator, Long formId) {
        var formDto = getPageFormByFormId(formId);
        PageResult<ArchiveBo> pageResult = archiveService.getArcsByModuleId(operator.getOperatorId(),
                formDto.getFormId(), 1, 1);
        Assert.isTrue(pageResult.getTotal() == 0, "当前组件已绑定视频,不允许删除");

        var reportData = personalMgkDataService
                .getReportDatas(QueryReportDataParamDto.builder().removeCheat(true)
                        .accountId(operator.getOperatorId()).formId(formId).build());
        Assert.isTrue(CollectionUtils.isEmpty(reportData), "当前组件已产生转化数据,不允许删除");

        mgkDslContext.update(mgk.MGK_FORM).set(mgk.MGK_FORM.STATUS, FormStatusEnum.DELETED.getCode())
                .where(mgk.MGK_FORM.FORM_ID.eq(formId)).execute();

        var pageId = Long.parseLong(formDto.getPageId());
        mgkDslContext.update(mgk.MGK_LANDING_PAGE).set(mgk.MGK_LANDING_PAGE.STATUS, LandingPageStatusEnum.DELETED.getCode())
                .where(mgk.MGK_LANDING_PAGE.PAGE_ID.eq(pageId)).execute();

        soaLandingPageService.refreshLandingPageCache(pageId);
    }

    //每周执行一次
    public void personalCommentBlackWordProcess(Long formId) {

        logger.info("personalCommentBlackWordProcess start formId:{}", formId);
        com.bilibili.adp.common.bean.Operator personMgkOperator = com.bilibili.adp.common.bean.Operator.builder()
                .operatorName("person-mgk")
                .operatorType(OperatorType.SYSTEM)
                .build();

        messageSendService.blackMidMessageRedisInit();

        Long tmpId = 0L;
        while (true) {
            try {
                SelectConditionStep<PersonalMgkCommentComponetRecord> conditionStep = mgkDslContext.selectFrom(mgk.PERSONAL_MGK_COMMENT_COMPONET)
                        .where(mgk.PERSONAL_MGK_COMMENT_COMPONET.IS_DELETED.eq(IsDeleted.VALID),
                                mgk.PERSONAL_MGK_COMMENT_COMPONET.ID.greaterThan(tmpId));
                if (Utils.isPositive(formId)) {
                    conditionStep = conditionStep.and(mgk.PERSONAL_MGK_COMMENT_COMPONET.FORM_ID.eq(formId));
                }

                Result<PersonalMgkCommentComponetRecord> personalMgkCommentComponetRecords = conditionStep
                        .orderBy(mgk.PERSONAL_MGK_COMMENT_COMPONET.ID.asc())
                        .fetch();
                if (CollectionUtils.isEmpty(personalMgkCommentComponetRecords)) {
                    break;
                }

                tmpId = personalMgkCommentComponetRecords.get(personalMgkCommentComponetRecords.size() - 1).getId();

                //符合黑词 下线落地页
                for (PersonalMgkCommentComponetRecord personalMgkCommentComponetRecord : personalMgkCommentComponetRecords) {
                    if (blackWordsService.checkInBlackWord(personalMgkCommentComponetRecord.getGeneralCommentText()).getFirst() || blackWordsService.checkInBlackWord(personalMgkCommentComponetRecord.getConversionUrlText()).getFirst()) {
                        // disbale 落地页
                        // 发消息
                        Long pageId = personalMgkCommentComponetRecord.getPageId();
                        try {
                            soaLandingPageService.disable(personMgkOperator, pageId);
                            logger.info("disable page {}, {}", pageId, personalMgkCommentComponetRecord);
                        } catch (Exception e) {
                            logger.info("disable page error {} , {} , {} ", personMgkOperator, pageId, personalMgkCommentComponetRecord);
                            logger.error("error ", e);
                        }
                        personalMgkCommentComponetRecord.setIsDeleted(IsDeleted.DELETED)
                                .update();

                        MgkLandingPagePo landingPagePo = landingPageService.getByPageId(pageId);
                        AccAccountPo accountPo = accountService.getAccountById(landingPagePo.getAccountId());
                        messageSendService.blackMidMessageSendOnceADay(accountPo);
                    }
                }
            } catch (Exception e) {
                logger.error("error ", e);
            }
        }
        logger.info("personalCommentBlackWordProcess end formId:{}", formId);
    }
}
