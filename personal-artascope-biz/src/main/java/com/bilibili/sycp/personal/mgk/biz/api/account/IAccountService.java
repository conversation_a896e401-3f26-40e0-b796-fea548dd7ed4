package com.bilibili.sycp.personal.mgk.biz.api.account;

import com.bilibili.sycp.personal.mgk.biz.api.account.bo.PersonMgkBlackMidBo;
import com.bilibili.sycp.personal.mgk.biz.api.account.dto.AccountCardResponse;
import com.bilibili.sycp.personal.mgk.biz.api.account.dto.BizAccountStautsDto;
import com.bilibili.sycp.personal.mgk.biz.common.exception.ServiceException;
import com.bilibili.sycp.personal.mgk.biz.dao.ad.generated.tables.pojos.AccAccountPo;
import com.bilibili.sycp.personal.mgk.biz.dao.mgk.generated.tables.pojos.MgkAutoCreateAccInfoPo;
import com.mysema.commons.lang.Pair;

public interface IAccountService {

    AccAccountPo getAccountById(Integer accountId);

    AccountCardResponse queryCorpInfo(Long mid) throws ServiceException;

    Integer checkPersonalFlyAccess(Long mid);

    boolean checkRealName(Long mid);

    boolean checkAccountLabel(int accountId, int labelId);

    Pair<Boolean, String> checkPermissionSubmitEd(Long mid) throws ServiceException;

    Pair<Boolean, String> checkPermission(Long mid);

    Boolean hasSubmitForm(Long mid);

    Integer checkAndCreate(Long mid) throws ServiceException;

    MgkAutoCreateAccInfoPo queryManagerAccByMid(long mid);

    Boolean checkInJewelryBlackList(Long mid);

    PersonMgkBlackMidBo getBlackMidRule(Long mid);

    Boolean checkIsRiskTags(Long mid);

    BizAccountStautsDto checkBusinessAccount(Long mid);
}
