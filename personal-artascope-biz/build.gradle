buildscript {
    dependencies {
        classpath libs["buf-gradle-plugin"]
    }
    repositories {
        maven { url "https://nexus.bilibili.co/content/repositories/releases/" }
        maven { url "https://nexus.bilibili.co/content/groups/public/" }
    }
}

plugins {
    id 'java'
}


apply plugin: 'com.bilibili.buf'
// 插件的参数声明，具体参数可参照下面的完整配置项。注意在一个build.gradle中不要同时声明两个bufGenerateParam
bufGenerateParam {
    bufVersion = '1.23.1'
    modules = [
            "buf.bilibili.co/archive/service",
            "buf.bilibili.co/ad/component",
            "buf.bilibili.co/up.archive/service",
            "buf.bilibili.co/ad/external",
            "buf.bilibili.co/account/service.member.v1",
            "buf.bilibili.co/ad/mgk.comment",
            "buf.bilibili.co/platform/interface.shorturl",
            "buf.bilibili.co/community/interface.reply",
            "buf.bilibili.co/account/service",
            "buf.bilibili.co/ad/crm.account",
            "buf.bilibili.co/ad/mgk.business_tool",
            "buf.bilibili.co/ad/account.label",
            "buf.bilibili.co/ad/crm.account",
            "buf.bilibili.co/silverbullet/gaia.interface",
            "buf.bilibili.co/ad/business.acc"
    ]
    classifier = "release/2025-04-07/biz_account_qualification"
}

group 'com.bilibili.sycp'
version '0.0.1-SNAPSHOT'


dependencies {
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.7.0'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.7.0'

    implementation libs['jooq']
    implementation libs['jooq-meta']
    implementation libs['jooq-codegen']
    implementation libs['lombok']

    implementation libs['bouncycastle']
    implementation libs['swagger']
    implementation libs['jackson-databind']
    implementation libs['redisson']
    implementation libs['springboot-starter-thymeleaf']
    implementation libs['javers']
    implementation libs['jjwt-api']
    implementation libs['jjwt-impl']
    implementation libs['xxl']
    implementation libs['mysql']
    implementation libs["spring-jdbc"]
    implementation libs["spring-data-redis"]
//    implementation libs["spring-boot-autoconfigure"]
    implementation libs['bjcom']
    implementation libs['bjcom-sso']
    implementation libs['netty']
    implementation libs['bv']

    implementation venus['config']
    implementation venus['starter']
    implementation venus['naming-discovery']
    implementation venus['logging']
    implementation libs['paladin-client']

    // gradle 5.0以上版本注解处理不再compile classpath，需要增加 annotation processor path
    annotationProcessor libs['lombok']

    annotationProcessor libs['mapstruct-processor']
    implementation libs['mapstruct']
    implementation libs['grpc']
    implementation libs['mgk-portal']

    implementation component['pleiades-mysql']
    implementation component['rpc-client']
    implementation component['rpc-server']
    implementation libs['commons-beanutils']

    implementation(libs['databus']) {
        exclude group: 'org.springframework.boot', module: 'spring-boot-autoconfigure'
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }

}

test {
    useJUnitPlatform()
}

jar{
    enabled = true
}

tasks.withType(JavaCompile){
    options.encoding = "UTF-8"
}