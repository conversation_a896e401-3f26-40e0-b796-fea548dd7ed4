syntax = "proto3";
import "extension/wdcli/wdcli.proto";
package bcg.ones.minos;

option (wdcli.appid) = "sycpb.test.business-ones-minos.v2";
option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/onesv2.minos";
option java_package = "com.bapis.bcg.ones.minosv2";
option java_multiple_files = true;

service OnesMinosV2Service {
  //账户诊断
  rpc Conclusion(ConclusionReq) returns (AccountConclusionVo);
  //行业洞察
  rpc IndustryInsight(IndustryInsightReq) returns (AccountIndustryInsightVo);
  // 异常单元分页列表
  rpc AbnormalUnitListNew(AbnormalUnitListNewReq) returns (AbnormalUnitListNewResp);
  // 异常单元一键起量，保留当天
  rpc SaveBoostUnits(SaveBoostUnitsReq) returns (SaveBoostUnitsResp);
  // 关键指标数据 账户和单元使用的是同一个接口
  rpc Summary(DiagnosisV3QueryDto) returns (AccountSummaryVoP3);
  //单元异常情况概述
  rpc AbnormalUnitNew(AbnormalUnitNewReq) returns (AbnormalUnitNewResp);
  // 投放历程
  rpc LaunchHistory(LaunchHistoryReq) returns (LaunchHistoryResp);
  // 首页成本比值
  rpc CostRatio(CostRatioReq) returns (CostRatioResp);
  // 首页异常单元
  rpc AbnormalUnitHomepage(AbnormalUnitHomepageReq) returns (AbnormalUnitHomepageResp);
  // 大盘洞察
  rpc Cubes(CubesReq) returns (CubesResp);
}

// 大盘洞察请求
message CubesReq {
  int64 account_id = 1; //账户ID
}

message CubesResp {
  repeated string x_axis_data = 1;
  repeated string y_axis_data = 2;
  repeated CubeCell traffic_intensity_distribution = 3;
  repeated CubeCell show_ecpm = 4;
  repeated CubeCell all_show = 5;
  repeated CubeCell cate_show = 6;
}

message CubeCell {
  string x = 1;
  string y = 2;
  string value = 3;
  repeated IndustryRankList industry_rank_list = 4;
  int32 rank = 5;
}

message IndustryRankList {
  string label = 1;
  string value = 2;
  int32 rank = 3;
}

// 首页异常单元
message AbnormalUnitHomepageReq {
  int64 account_id = 1; //账户ID
  repeated string key_list = 2; //所需指标列表
}

message AbnormalUnitHomepageResp {
  int32 qianchengbeng = 1; // 欠成本
  int32 konghao = 2; // 空耗
  int32 diaoliang = 3; // 掉量
  int32 chaochengbeng = 4; // 超成本
  int32 lengqidong = 5; // 冷启动
}

// 首页成本比值
message CostRatioReq {
  int64 account_id = 1; //账户ID
}

message CostRatioResp {
  float value = 1; // 当前值
  repeated float today_line = 2; // 曲线
  repeated string x_axis_data = 3; // 坐标轴
}

// 异常单元一键起量
message SaveBoostUnitsReq {
  int64 account_id = 1; // 账户id
  string unit_ids = 2; // 单元id之间用英文逗号分隔
  int64 from_page = 3; // 传1 代表欠成本  保留字段
  int64 module = 4; // 传1 代表一键起量功能  保留字段
}
message SaveBoostUnitsResp {
  string res = 1; // 结果 成功为success，失败为失败原因
}

//单元诊断
message AbnormalUnitNewReq {
  int64 unit_id = 1; //单元id
}
message AbnormalUnitNewResp {
  int64 is_abnormal = 1; //是否异常
  string result = 2; //提示信息
  repeated AbnormalUnitNewResultResp data = 3; //数据信息
}
message AbnormalUnitNewResultResp {
  int64 is_abnormal = 1; //是否一场
  string result = 2; //提示信息
  string type = 3; //类型信息
}

message LaunchHistoryReq {
  int64 account_id = 1; //账户id
  int64 unit_id = 2;//单元id
  int64 start_time = 3;//开始时间
  int64 end_time = 4;//结束时间
}
message LaunchHistoryResp {
  double declines = 1;//-1/0/1  下降/持平/上升
  string declines_filter_name = 2; //名称
  double declines_ratio = 3; //漏斗环比
  repeated LaunchHistoryResponse data = 4; //漏斗信息
}
message LaunchHistoryResponse {
  string label = 1; //漏斗名称
  double value = 2; //通过率
  double qoq_value = 3; //环比通过率
  DwsUnitFilterStatI1hrV2DLineResponse current_chart = 4;
  DwsUnitFilterStatI1hrV2DLineResponse industry_chart = 5;
}
message DwsUnitFilterStatI1hrV2DLineResponse {
  string axis_label = 1; //单位
  repeated string legend_data = 2;//折线图说明
  repeated string x_axis_data_by_hour = 3; //折线图横坐标小时维度
  repeated string x_axis_data_by_day = 4; //折线图横坐标天维度
  repeated SeriesData series_data_by_hour = 5; //折线图纵坐标小时维度
  repeated SeriesData series_data_by_day = 6; //折线图纵坐标天维度
}
message SeriesData {
  repeated double data = 1;
  string name = 2;
}









//账户诊断
// 用于账户诊断的请求参数
message ConclusionReq {
  int64 account_id_list = 1; //还是账户id,给以后的多账户查询预留的
  int64 from_time = 2; //开始时间 13位时间戳
  int64 to_time = 3; //结束时间 13位时间戳
  int64 target = 4; //浅层优化目标
  int64 deep_target = 5; //深层优化目标
}
// 用于账户诊断的返回值
message AccountConclusionVo {
  AccountConclusionItemDetailForChargeRadio charge_ratio = 1; //计费比
  AccountConclusionItemDetailForAbnormalUnit abnormal_unit = 2; //异常单元
  AccountConclusionItemDetailForIndustryInsight industry_insight = 3; //行业洞察
}
// 账户诊断结论信息 行业洞察 实体类
message AccountConclusionItemDetailForIndustryInsight{
  string title = 1; //标题
  string  tips = 2; //tips
  AccountConclusionItemDetailForDecimal total_cost = 3; //总消耗
  AccountConclusionItemDetailForDecimal cost_higher_account_radio = 4; //消耗超过二级行业账户占比
  AccountConclusionItemDetailForDecimal cost_higher_account_radio_first = 8; //消耗超过一级行业账户占比
  AccountConclusionItemDetailForDecimal second_category_avg_cost = 5; //二级行业平均消耗
  string label = 6; //label
  string value_type = 7; //value_type
}
// 账户诊断结论信息 计费比 实体类
message AccountConclusionItemDetailForChargeRadio{
  string title = 1; //标题
  string  tips = 2; //tips
  AccountConclusionItemDetailForDecimal cost_charge_ratio = 3; //成本计费比
  AccountConclusionItemDetailForDecimal advv = 4; //advv
  AccountConclusionItemDetailForDecimal second_category_avg_charge_radio = 5; //二级行业平均计费比
  AccountConclusionItemDetailForDecimal first_category_avg_charge_radio = 6; //一级行业平均计费比 更新
  AccountConclusionItemDetailForDecimal second_category_avg_advv = 7; //二级行业平均advv
  AccountConclusionItemDetailForDecimal effective_optimization_radio = 8; //有效优化率
  string label = 9; //label
  string value_type = 10; //value_type
}
// 账户诊断结论信息 异常单元 实体类
message AccountConclusionItemDetailForAbnormalUnit{
  string title = 1; //标题
  string  tips = 2; //tips
  AccountConclusionItemDetailForInteger abnormal_unit_count = 3; //异常单元个数
  AccountConclusionItemDetailForDecimal abnormal_unit_radio = 4; //异常单元占比
  AccountConclusionItemDetailForInteger number_idle_unit_count = 5; //空耗单元个数
  AccountConclusionItemDetailForInteger dropping_unit_count = 6; //掉量单元个数
  AccountConclusionItemDetailForInteger over_cost_unit_count = 7; //超成本单元个数
  AccountConclusionItemDetailForInteger below_cost_unit_count = 8; //欠成本单元个数
  AccountConclusionItemDetailForInteger low_volume_unit_count = 9; //不起量单元个数
  string label = 10; //label
  string value_type = 11; //value_type
  AccountConclusionItemDetailForDecimal abnormal_unit_cost_radio = 12; //异常单元消耗占比
}
message AccountConclusionItemDetailForInteger{
  int64 value = 1; //值
  bool  is_display = 2; //是否展示
  string label = 3; //label
  string value_type = 4; //value_type
}
message AccountConclusionItemDetailForDecimal{
  float value = 1; //值
  bool  is_display = 2; //是否展示
  string label = 3; //label
  string value_type = 4; //value_type
}

//行业洞察
// 用于行业洞察的请求值
message IndustryInsightReq {
  int64 account_id_list = 1; //还是账户id,给以后的多账户查询预留的
  int64 from_time = 2; //开始时间 13位时间戳
  int64 to_time = 3; //结束时间 13位时间戳
  int64 category = 4; //一级还是二级行业

}
// 用于行业洞察的返回值
message AccountIndustryInsightVo {
  AccountIndustryInsightItemDetail ranking_current = 1; //当前账户行业排行
  repeated AccountIndustryInsightItemDetail ranking_top_10_list = 2; //排行榜
}
// 当前账户行业排行
message AccountIndustryInsightItemDetail{
  int64 account_id = 1; //账户id
  AccountIndustryInsightItemDetailForDecimal account_cost = 2; //账户消耗
  AccountIndustryInsightItemDetailForDecimal avg_unit_cost = 3; //单元平均消耗
  AccountIndustryInsightItemDetailForDecimal ctr = 4; //点击率
  AccountIndustryInsightItemDetailForDecimal conversion_rate = 5; //转化率
  AccountIndustryInsightItemDetailForDecimal conversion_cost = 6; //转化成本
  AccountIndustryInsightItemDetailForInteger active_video_assets_count = 7; //在投视频素材数
  AccountIndustryInsightItemDetailForDecimal avg_active_video_assets_cost = 8; //平均有效视频素材消耗
  string label = 9; //label
  string value_type = 10; //value_type
}
// 用于行业洞察的返回值
message AccountIndustryInsightItemDetailForDecimal {
  float value = 1; //值
  float exceed_account_proportion = 2; //超过账户占比
  string tips = 3; //tips
  string label = 4; //label
  string value_type = 5; //value_type
}
message AccountIndustryInsightItemDetailForInteger {
  int64 value = 1; //值
  float exceed_account_proportion = 2; //超过账户占比
  string tips = 3; //tips
  string label = 4; //label
  string value_type = 5; //value_type
}
// 用于异常单元的请求值
message AbnormalUnitListNewReq {
  int64 account_id = 1; //账户ID
  string sort_field = 2; //排序字段
  int64 sort_type = 3; //排序类型 0-正序, 1-倒序
}
// 用于异常单元的返回值
message AbnormalUnitListNewResp {
  map<string, AbnormalUnitDataNewVoList> data = 1;
}
message   AbnormalUnitDataNewVoList{
  repeated AbnormalUnitDataNewVo data = 1; //异常单元列表
}
// 用于异常单元的返回值
message AbnormalUnitDataNewVo {
  int64 account_id = 1; //账户ID
  int64 unit_id = 2; //单元id
  int64 abnormal_type = 3; //异常类型
  string target = 4; //优化目标
  float cost = 5; //今日消耗（单位：元）
  float cost_ratio = 6; //消耗（单位：元）环比
  float ecpm = 7; //ecpm
  float ecpm_ratio = 8; //e比
  float cvr = 9; //浅层cvr
  float cvr_ratio = 10; //浅层cvr比
  float  pcvr = 11; //浅层pcvr
  float  pcvr_ratio = 12; //浅层pcvr比
  float  two_stage_bid = 13; //浅层出价
  float  cost_per_conv = 14; //浅层成本
  float  cost_per_conv_ratio = 15; //浅层成本比
  float  jfb = 16; //浅层计费比
  float  jfb_ratio = 17; //浅层计费比环比
  float  dcvr = 18; //深层cvr
  float  dcvr_ratio = 19; //深层cvr比
  float  dpcvr = 20; //深层pcvr
  float  dpcvr_ratio = 21; //深层pcvr比
  float deep_bid = 22; //深层出价
  float  dcost_per_conv = 23; //浅层成本
  float  dcost_per_conv_ratio = 24;//深层成本比
  float  djfb = 25; //深层计费比
  float  djfb_ratio = 26; //深层计费比环比
}
// 用于关键指标的请求值
message DiagnosisV3QueryDto {
  int64 account_id = 1; //账户ID
  int64 unit_id = 2; //单元id
  int64 from_time = 3; //开始时间
  int64 to_time = 4; //结束时间
  int64 target = 5; //浅层优化目标
  int64 deep_target = 6; //深层优化目标
  string key_list = 7; //所需指标列表
}

// 用于关键指标的返回值
message AccountSummaryVoP3 {
  ////////3.1.1.基础指标 /////////
  ItemDetailForDecimal cost = 1; //消耗
  ItemDetailForInteger show_count = 2; //展现
  ItemDetailForInteger click_count = 3; //点击
  ItemDetailForDecimal cpm = 28; //千次展现价格
  ItemDetailForDecimal click_through_rate = 4; //CTR
  ItemDetailForDecimal budget = 5; //预算
  ItemDetailForInteger unit_count = 6; //有效单元数
  ItemDetailForDecimal arpu = 7; //arpu
  ItemDetailForDecimal cost_ratio = 29; //成本比值

  ////////3.1.2.浅层指标 /////////
  ItemDetailForInteger conv_count = 8; //浅层转化次数
  ItemDetailForDecimal conversion_rate = 9; //浅层转化率
  ItemDetailForDecimal cost_per_conversion = 10; //浅层转化成本
  ItemDetailForDecimal customer_bid = 11; //浅层客户出价
  ItemDetailForDecimal ctcvr = 12; //浅层CTCVR

  ////////3.1.3.深层指标 /////////
  ItemDetailForInteger deep_conv_count = 13; //深层转化数
  ItemDetailForDecimal deep_conversion_rate = 14; //点击-深层转化率（原深层转化率）
  ItemDetailForDecimal deep_cost_per_conversion = 15; //深层转化成本
  ItemDetailForDecimal deep_customer_bid = 30; //深层客户出价
  ItemDetailForDecimal deep_ctcvr = 16; //深层CTCVR
  ItemDetailForDecimal light_to_deep_conv_rate = 17; //深层转化率（浅转-深转 转化率）

  ////////3.1.4.带货指标 /////////
  ItemDetailForFloat play_count = 18; //播放量
  ItemDetailForDecimal play_rate = 19; //播放率
  ItemDetailForDecimal cost_per_play = 20; //播放成本
  ItemDetailForFloat comment_show_count = 21; //评论链接曝光量
  ItemDetailForFloat comment_click_count = 22; //评论链接点击量
  ItemDetailForDecimal comment_click_rate = 23; //评论链接点击率
  ItemDetailForDecimal comment_show_click_rate = 24; //评论链接曝光点击率
  ItemDetailForFloat callup_count = 25; //唤起次数
  ItemDetailForDecimal callup_rate = 26; //唤起率
  ItemDetailForDecimal cost_per_callup = 27; //唤起成本
  int64 plan_id = 31; //计划ID
  string product_name = 32; //品牌名称
  string cat_one = 33; //一级行业
  string cat_two = 34; //二级行业
  repeated string  xaxis = 35; //折线图横坐标数据
  repeated string  xaxis_byday = 36; //折线图横坐标数据
  string light_target = 37; //浅层目标
  string deep_target = 38; //深层目标

  ////////3.1.5. 四期新指标 /////////
  ItemDetailForDecimal show_ecpm = 39; // ecpm 曝光纬度
  ItemDetailForDecimal win_rate = 40; //  竞价胜出率

}


message ItemDetailForFloat {
  float value = 1; //值
  repeated float current_data = 2; //纵坐标：当前账户值
  repeated float current_data_byday = 3; //纵坐标：当前账户值分日
  repeated float average_data = 4; //纵坐标：行业平均值
  repeated float current_data_1d = 5; //纵坐标：单元昨日当前账户值
  repeated float average_data_1d = 6; //纵坐标：单元昨日二级行业平均值
  repeated float average_2th_data = 7; //纵坐标：账户二级行业平均值
  repeated float  average_2th_data_byday = 8; //纵坐标：账户二级行业平均值分日
  repeated float  average_data_byday = 9; //纵坐标：一级行业 平均值 分日
  string  label = 10; //名称
  string  value_type = 11; //值
  int64  ratio_type = 12; //环比类型：-1-降 0-平 1-升
  string  ratio_value = 13; //环比值
}


message ItemDetailForInteger {
  int64 value = 1; //值
  repeated int64 current_data = 2; //纵坐标：当前账户值
  repeated int64 current_data_byday = 3; //纵坐标：当前账户值分日
  repeated int64 average_data = 4; //纵坐标：行业平均值
  repeated int64 current_data_1d = 5; //纵坐标：单元昨日当前账户值
  repeated int64 average_data_1d = 6; //纵坐标：单元昨日二级行业平均值
  repeated int64 average_2th_data = 7; //纵坐标：账户二级行业平均值
  repeated int64  average_2th_data_byday = 8; //纵坐标：账户二级行业平均值分日
  repeated int64  average_data_byday = 9; //纵坐标：一级行业 平均值 分日
  string  label = 10; //名称
  string  value_type = 11; //值
  int64  ratio_type = 12; //环比类型：-1-降 0-平 1-升
  string  ratio_value = 13; //环比值
}

message ItemDetailForDecimal {
  float value = 1; //值
  repeated float current_data = 2; //纵坐标：当前账户值
  repeated float current_data_byday = 3; //纵坐标：当前账户值分日
  repeated float average_data = 4; //纵坐标：行业平均值
  repeated float current_data_1d = 5; //纵坐标：单元昨日当前账户值
  repeated float average_data_1d = 6; //纵坐标：单元昨日二级行业平均值
  repeated float average_2th_data = 7; //纵坐标：账户二级行业平均值
  repeated float  average_2th_data_byday = 8; //纵坐标：账户二级行业平均值分日
  repeated float  average_data_byday = 9; //纵坐标：一级行业 平均值 分日
  string  label = 10; //名称
  string  value_type = 11; //值
  int64  ratio_type = 12; //环比类型：-1-降 0-平 1-升
  string  ratio_value = 13; //环比值
  repeated float current_data_1d_byday = 14; //纵坐标：当前账户值 环比分日
  repeated float average_data_1d_byday = 15; //纵坐标：二级行业 环比分日
}
