syntax = "proto3";

package bcg.mixer;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/mixer;v1";
option java_package = "com.bapis.bcg.mixer";
option java_multiple_files = true;

message DynamicMixerRequest {
	int64 mid = 1;
	string buvid = 2;
	string ip = 3;
	string mobi_app = 4;  	//设备 iphone/android
	string ad_extra = 5;  	//客户端透传
	string build = 6;  	//版本号
	string open_event = 7; 	//冷启标志
	repeated int64 dynamic_ids = 8; 	//动态id列表数组，从大到小有序，id越大，时间越新
	string from = 9;   	//来源, "feed", "space", "feed_detail", "space_detail" ； 分别是feed页插广告，空间页请求沉淀广告；feed点击进入详情页；空间页点击进入详情页
	string device = 10;  	 //iphone、ipad、android
	int32 req_version = 20;    //请求版本，默认为0，1表示facade
	string from_track_id = 21;  //主站唯一track_id。标识来源
	repeated int64 av_ids = 22;  //当次请求中的视频id列表
	string exp_id = 23; // mid命中动态实验的id
    string nat_response = 24; //对应动态网关DynDescs json string
    string biz_response = 25; //商业引擎返回结果 base64加密
    repeated int64 fold_dynamic_ids = 26; //被折叠
}

message DynamicMixerResponse {
	int32 code = 1;
	string msg = 2;
	ResponseData data = 3;
}

message ResponseData {
    repeated AdDynamicCard ad_cards = 1;
}

message AdDynamicCard {
    int64 dynamic_id = 1;
    int32 card_index = 2;
}