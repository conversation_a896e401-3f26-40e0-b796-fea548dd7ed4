syntax = "proto3";

package bcg.stat;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/stat;api";
option java_package = "com.bapis.bcg.stat";
option java_multiple_files = true;

message GameLiveTrackId {
  string mid = 1;
  string buvid = 2;
  string request_id = 3;
  int64 account_id = 4;
  string sales_type = 5;
  int32 device_type = 6;
  int64 up_mid = 7;
  int32 game_channel_id = 8; // 默认为0:游戏B包 1:广告包
  int64 custom_id = 9; // 广告主客户id
  int64 order_id = 10;// 长周期订单id，确认不再使用，复用为一键起量id (小小su)
  int64 room_id = 11; // 直播间id
  int64 live_key = 12; // 直播间key
  int32 game_base_id = 13; // 游戏基础id
  int32 game_id = 14; // 游戏id
  int64 card_id = 15; // 卡片id
}