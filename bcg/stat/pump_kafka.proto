syntax = "proto3";

package bcg.stat;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/stat;api";
option java_package = "com.bapis.bcg.stat";
option java_multiple_files = true;

message PumpKafkaData {
  // 业务id
  int64 businessId = 1;
  // 业务类型
  BusinessType businessType = 2;
  // 更新时间戳
  int64 mtime = 3;
  // 数据的最新小时时间戳
  int64 latestHourTimestamp = 4;
  // 日花费统计
  AdStatInfo dailyStatInfo = 5;
  // 总花费统计
  AdStatInfo totalStatInfo = 6;
  // 分场景花费统计（天级）
  map<string, SceneStat> dailySceneStatInfo = 7;
  // 一键起量用 unitId
  int64 unitId = 8;
  // 最新小时花费统计
  AdStatInfo latestHourStatInfo=9;
}

// 广告花费结构体，花费单位均为毫分
message AdStatInfo {
  int64 show = 1;
  int64 click = 2;
  // 花费总金额
  int64 costMilli = 3;
  // 分桶花费
  repeated int64 bucketCostMilli = 4;
}

// 场景结构体，花费单位均为毫分
message SceneStat {
  // 当前场景已花费总金额
  int64 costMilli = 1;
  // 当前场景分桶花费金额
  repeated int64 bucketCostMilli = 2;
  // 当前场景当天总曝光
  int64 show = 3;
}

enum BusinessType {
  // 未知
  UNKNOWN = 0;
  // 账户
  ACCOUNT = 1;
  // 计划
  PLAN = 2;
  // 单元
  UNIT = 3;
  // 一键起量
  ACCELERATE = 4;
  // 品牌排期
  SCHEDULE = 5;
  // 创意
  CREATIVE = 6;
  // 一键起量v1
  ACCELERATE_V1 = 7;
}