syntax = "proto3";

package bcg.sunspot.ad.dto;


option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/sunspot.ad.dto;v1";
option java_package = "com.bapis.bcg.sunspot.ad.dto";
option java_multiple_files = true;

 
message UpInfo {

    //up主mid
    int64 up_mid = 1;

    //UP主是否拥有托管广告权限
    bool uploader_entitle_ad = 2;

    //UP主是否拥有互选位权限
    bool uploader_entitle_recommend_source = 3;

    //UP主是否开启托管广告
    bool uploader_trust_ad = 4;

    //UP主视频是否开启托管广告
    bool uploader_video_trust_ad =5;

    //UP主开启托管广告的视频ID
    repeated int64 allowed_video_ids = 6;

    //UP主屏蔽创意id
    repeated int64 banned_creative_ids = 7;

    //UP主屏蔽一级行业id
    repeated int32 allowed_first_industry_ids = 8;

    //UP主屏蔽应用名
    repeated int64 banned_app_names = 9;

    //UP主屏蔽落地页
    repeated int64 banned_app_urls = 10;

    //是否支持企业号框下托管广告
    bool uploader_enterprise_ad = 11;

    //up主投放企业号屏蔽的mid列表
    repeated int64 uploader_block_aids = 12;

    //up主投放企业号屏蔽的aid列表
    repeated int64 uploader_block_mids = 13;

    repeated int32 uploader_block_first_category_ids = 14;

    repeated int64 uploader_block_creative_ids = 15;

    repeated int64 uploader_block_app_package_names = 16;

    repeated int64 uploader_block_urls = 17;

    bool enable_cm_order = 18;

    repeated int64 block_cm_aids = 19;

    repeated int32 block_cm_category_ids = 20;

    // 资源位下广告标识标志位
    int32 place_ad_mark = 21;
}

message UpMid {
	int64 up_mid = 1;
}