syntax = "proto3";

package bcg.rta.ad.vo;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/rta.ad.vo;v1";

option java_package = "com.bapis.bcg.rta.ad.vo";
option java_multiple_files = true;

message RtaDetail {

  // 对应账户
  int64 accountId = 1;

  // 竞价系数，放到和sku一个级别，非必填
  double rtaBidRatio = 2;

  // 出价
  double advPrice = 3;

  // rta info 详情
  repeated string skuList = 4;

  repeated OcpxInfo ocpxInfos = 5;

  string rtaRequestExtra = 6;

  // 客户自定义账户层级tag
  int64 rtaUserTag = 7;
  
  // 人物价值分
  double personValue = 8;

  // 1-正常参竞；2-异常默认参竞；3-命中缓存参竞
  int32 status = 9;
}

message OcpxInfo {
  // ocpx优化目标
  int32 ocpxType = 1;

  // cpa出价
  double cpaPrice = 2;

  // cpa出价系数
  double cpaPriceRatio = 3;
}

message RtaDevice {
  //设备类型
  int32 osType = 1;

  string idfa = 2;

  string imei = 3;

  string oaid = 4;

  string bootTimeInSec = 5; //系统冷启时间

  string sysFileTime = 6; //系统最近更新时间

  string ip = 7;         // 设备终端ip

  map<string, string> extra = 8;    // 额外字段，dsp选择性使用，包括上面的ip字段（上线后单独的ip字段废弃）
}

message AccountInfo {
  uint64 accountId = 1;

  // 取值范围[1,10]; 0为无效值
  uint32 expId = 2;
}

message RtaResponseVo {

  int32 code = 1;

  string message = 2;

  // 参竞部分信息详情，statusCode
  repeated RtaDetail rtaBidDetailList = 3;

}

message RtaRequestVo {

  // 此请求的唯一id
  string requestId = 1;

  // 请求账户id
  repeated int64 accountIdList = 2;

  // 设备信息
  RtaDevice device = 3;

  int64 time = 4;

  // 调试模式，日志输出关键字 rtaDebug
  bool enableDebug = 5;

  int64 mid = 6;

  // 请求账户信息
  repeated AccountInfo accountInfoList = 7;

  // 是否为转发流量
  bool forwardFlow = 8;

  // 请求的buvid
  string buvid = 9;

  // 微信小游戏命中实验,请求账户返回all_yes
  bool wgame_all_yes = 10;

  // 场景ID
  int32 moni_scene_type = 11;

  // 实验分组
  repeated int64 agg_tunnel_snap_id_list = 12;

  // 实时探针中的已安装app列表
  repeated int32 installed_app_ids = 13;
}


message PrepareAdBidDetailRequest {
  string request_id = 1;
  int32 source_id = 2;
  RtaDevice device = 3;
  repeated AdInfo ad_info_list = 4;
  int32 age = 5;
  int32 gender = 6;
  string city = 7;
  int32 scene = 8;
  // 是否为转发流量
  bool forwardFlow = 9;
}

message AdInfo {
  int64 unit_id = 1;
  int64 creative_id = 2;
  int64 account_id = 3;
  string sku_id = 4;
}

message PrepareAdBidDetailResponse {
}




message QueryAdBidDetailRequest {
  string request_id = 1;
  int32 source_id = 2;

  // 是否为转发流量
  bool forwardFlow = 3;
}

message QueryAdBidDetailResp {
  repeated AdBidDetail ad_bid_detail_list = 1;
}


message AdBidDetail {
  int64 unit_id = 1;
  int64 creative_id = 2;
  float ad_bid = 3;
}