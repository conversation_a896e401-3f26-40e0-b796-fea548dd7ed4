syntax = "proto3";

package bcg.cassini.dto;
// import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/cassini.dto;v1";

option java_package = "com.bapis.bcg.cassini.dto";
option java_multiple_files = true;  
import "bcg/cassini/rapi/enums/enum.proto"; 

message FrequencyControlIndexEntity {
    map<int64, FrequencyControlList> account_frequency_control_map = 1;

}

message FrequencyControlList {
    repeated FrequencyControl frequency_controls = 1;
    
}

message FrequencyControl {
    string name = 1;

    /**
     * 频控单位
     */
    sycpb.cpm.cassini.bs.enums.FrequencyUnit.Enum frequency_unit = 2;

    /**
     * 控制次数
     */
    int32 frequency_limit = 3;

    /**
     * 频控跨度
     */
    int32 frequency_interval = 4;
    /**
     * 频控跨度针对VIP用户
     */
    int32 frequency_interval_for_vip = 5;
    /**
     * 频控次数对VIP用户
     */
    int32 frequency_limit_for_vip = 6;
    /**
     * 频控单位对VIP用户
     */
     sycpb.cpm.cassini.bs.enums.FrequencyUnit.Enum frequency_unit_for_vip = 7;

}