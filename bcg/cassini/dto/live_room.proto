syntax = "proto3";

package bcg.cassini.dto;
// import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/cassini.dto;v1";

option java_package = "com.bapis.bcg.cassini.dto";
option java_multiple_files = true;

message LiveRoomIndexEntity {
    map<int64, LiveRoom> live_room_map = 1;
}

message LiveBookIndexEntity {
    repeated LiveBook live_book_list = 1;
}

message LiveRoom {

    int64 room_id = 1;

    string room_title = 2;

    string room_area = 3;

    int64 room_popularity = 4;

    int64 uid = 5;

    string streamer_name = 6;
    
    string streamer_face = 7;

    int32 status = 8; //0未开播，1直播中；2轮播中；

    uint64 live_id = 9; // 场次ID

    repeated LiveRoomCartItem cart_items = 10; // 商品列表

    int64 start_time = 11; // 开播时间

    LiveRoomCartItem introduce_goods = 12; // 直播带货商品
}

//直播预约
message LiveBook {
    int64 book_id = 1;
    int32 state = 2; //100-预约已经绑定稿件，可被预约，其他-不可预约
}

message LiveRoomCartItem {
    int64 goods_sort_id = 1; // 商品外显排序ID。
    string goods_id = 2; // 直播带货的商品ID。
    int64 goods_status = 3; // 0未讲解中 1讲解中。
    string item_id = 4; // 视频带货的商品id
    string one_item_id =5; // 带货统一的商品id
}