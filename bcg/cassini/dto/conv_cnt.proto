syntax = "proto3";

package bcg.cassini.dto;
// import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/cassini.dto;v1";

option java_package = "com.bapis.bcg.cassini.dto";
option java_multiple_files = true;

message NaturalAdsConversionInfo {
    //linux时间戳，聚合转化时，最后一个转化发生的时间，单位s
    int64 group_time = 1; 
    //单元id
    int64 unit_id = 2;
    //创意id
    int64 creative_id = 3;
    //包含商业流量和自然流量的
    int32 sales_type = 4;
    //ocpx target type
    string ocpx_target_type = 5;
    // 聚合后的转化数据
    int64 cv_num = 6;
}