syntax = "proto3";

package sycpb.cpm.cassini.bs.enums;

option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/cassini.rapi.enums;api";
option java_package = "com.bapis.bcg.cassini.rapi.enums";
option java_multiple_files = true;

message IntelligentTarget {
    enum Enum {
        UNKNOWN = 0;
        DEVICE = 1;          //设备定向
        TIME = 2;            //时段定向
        CROWD = 3;           //人群包定向
        GENDER = 4;          //性别定向
        CTAG = 5;            //视频兴趣定向
        AREA = 6;            //地域定向
        AGE = 7;             //年龄定向
        NET = 8;             //网络环境定向
        BRAND = 9;           //设备品牌定向
        APPCATEGORY = 10;    //APP兴趣定向
        TAG = 11;            //商业兴趣定向
        VIDEO = 12;          //视频关键词定向
        FAN = 13;            //粉丝定向
        STYPE = 14;          //分区兴趣人群定向
        VIDEO_CTY = 15;      //视频所属分区定向
        BRUSH = 16;          //动态feeds刷次定向
        PROGRAMMATIC = 17;   //程序化创意定向
    }
}

message MobileApp {
    enum Enum {
        UNKNOWN = 0;
        IPHONE = 1;
        WHITE = 2;
        IPAD = 3;
        ANDROID = 4;
        ANDROID_B = 5;
        IPHONE_G = 6;
        IPAD_G = 7;
        ANDROID_G = 8;
        IPHONE_B = 9;


        // IPHONE("iphone", RequestOsEnum.IOS),
        // WHITE("white", RequestOsEnum.IOS),
        // IPAD("ipad", RequestOsEnum.IOS),
        // ANDROID("android", RequestOsEnum.ANDROID),
        // ANDROID_B("android_b", RequestOsEnum.ANDROID),
        // IPHONE_G("iphone_g", RequestOsEnum.IGNORE),
        // IPAD_G("ipad_g", RequestOsEnum.IGNORE),
        // ANDROID_G("android_g", RequestOsEnum.IGNORE),

        // UNKNOWN("unknown", RequestOsEnum.UNKNOWN);
    }
}

// NOTICE!!! it's different from RTB's
message LocationPlatform {
    enum Enum {
        UNKNOWN = 0;
        PC = 1;
        IPHONE = 2;
        ANDROID = 3;
        IPAD = 4;
    }
}

// 创意横竖屏
message Orientation {
    enum Enum {

        DEFAULT = 0; //默认

        LANDSCAPE = 1; //横屏

        PORTRAIT = 2;  //竖屏
    }
}

message AdType {
    enum Enum {
        UNSPECIFIED = 0;

        DPA = 1;

        CONTENT_UP = 2;

        PERSONAL_UP = 3;

        BUSINESS_UP = 4;

        PRIMARY = 5;

        // 签约
        SIGN_UP = 6;
        
        GUARD_COUNT = 7;
    }
}


message BusinessType {
    enum Enum {
        OTHER = 0;

        // 个人起飞
        PERSONAL_UP = 1;

        // 内容起飞
        CONTENT_UP = 2;

        // 商业起飞
        BUSINESS_UP = 3;

        // 非起飞 CPC
        CPC = 4;

        // 非起飞CPM
        CPM = 5;
        
        // 签约
        SIGN_UP = 6;
        
        GUARD_COUNT = 7;
    }
}

message SubBusinessType {
    enum Enum {
        UNKNOWN = 0;

        // 签约金
        BONUS = 1;

        // 托管
        MANDATE = 2;
    }
}

message SalesType {
    enum Enum {
        UNKNOWN = 0;
        CPM = 11;
        CPC = 12;
        GD = 21;
        CPT = 31;
        LBS_ONLINE = 51;
        SELECTIVE_CPM = 32;
        SELECTIVE_CPC = 33;
        SELECTIVE_CPS = 34;
        SELECTIVE_CPT = 35;
        SPLASH_CPT = 41;
        SPLASH_GD = 42;
        TOPVIEW_CPT = 43;
        ADX = 101;
    }
}

message FrequencyUnit {
    enum Enum {
        NONE = 0;
        DAYS = 1;
        WEEKS = 2;
        MONTHS = 3;
        THRDAY = 4;
        HOURS = 5;
    }
}


message TagTargetType {
    enum Enum {
        NONE = 0;
        ACCURATELY = 1;
        FUZZY = 2;
    }
}

message ImageStyle {
    enum Enum {
        UNKNOWN = 0;
        SINGLE_ROW = 1;
        DOUBLE_ROW = 2;
        /**
        * 使用版本命名增加扩展性
        */
        DOUBLE_ROW_V1 = 3;
    }
}

message AcType {
    enum Enum {
        UNKNOWN = 0;
        ACCOUNT = 1;
        COMPANY = 2;
    }
}

message NegativeFedBackGrade {
    enum Enum {
        NONE = 0;
        CREATIVE = 1;
        ACCOUNT = 2;
        COMPANY = 3;
        FIRST_CATEGORY = 4;
        SECOND_CATEGORY = 5;
        COMPANY_PRODUCTID = 6;
        VIDEOID = 7;
        UPMID = 8;
        VID_SECOND_DISTRICT = 11;
        SOURCE_ID = 12;
    }
}


message AdpVersion {
    enum Enum {

        OLD = 0;
        NEW = 1;
        UP_NEW = 2;

    }
}


message OcpcTargetType {
    enum Enum {
        NONE = 0;
        ANDROID_DOWNLOAD = 1;
        ACTIVATION = 2;
        APPOINTMENT = 3;
        FORM = 4;
        ORDER = 5;
        ANDROID_INSTALL = 6;
        REGISTER = 7;
        VIDEO_PLAY = 9;
        FAN_INCREASE = 10;
        USER_COST = 11;
        GAME_ACTIVE_API = 12;
        CLUE_VALID = 13;
        LP_CALLUP = 14;
        RETENTION = 16;
        APP_CALLUP = 17;
        FORM_USER_COST = 18;
        LP_CALLUP_SUCC = 19;
        LP_CALLUP_SUCC_STAY = 20;
        ENTERPRISE_ACCOUNT_SUBSCRIBE = 21;
        ACCOUNT_SUBSCRIBE = 22;
        VIEW_DYNAMIC_PAGE = 25;
    }
}

message ResourceType {
    enum Enum {
        OTHER = 0;
        FEEDS = 1;
        RELATED_RECOMMEND = 2;
        UP_SELECTIVE = 3;
        MOVE_BANNER = 4;
    }
}

message SceneBitType {
    enum Enum {
        UNKNOWN             = 0;
        MOBILE_FEEDS        = 1;
        MOBILE_VIDEO_PAGE   = 2;
        MOBILE_DYNAMIC      = 4;
        PC_HOME             = 8;
        PC_VIDEO_PAGE       = 16;
        STORY               = 32;
    }
}

message AlgorithmResourceType {
    enum Enum {
        OTHER               = 0; //不限
        MOBILE_FEEDS        = 1; //移动端信息流
        MOBILE_VIDEO_PAGE   = 2; //移动端播放页
        PC_FEEDS            = 3; //pc端信息流
        PC_VIDEO_PAGE       = 4; //pc端播放页
        UNDERFRAME          = 5; //框下资源位
        MOBILE_DYNAMIC      = 6; //动态信息流
    }
}

message RequestNetworkEnum {
    enum Enum {
        UNKNOWN = 0;
        WIFI = 1;
        MOBILE = 2;
    }
}

message BudgetType {
    enum Enum {
        UNKNOWN = 0;
        DAILY = 1; // 日预算
        TOTALLY = 2; // 总预算
    }
}

message SpeedMode {
    enum Enum {
        UNKNOWN = 0;
        SMOOTH = 1; // 平滑投放
        ACCELERATED = 2; // 加速投放
    }
}


message CreativeStyleAbility {
    enum Enum {
        UNKNOWN = 0;

        STATIC_IMAGE = 1;

        GIF = 2;

        STATIC_VIDEO = 3;

        INLINE = 4;

    // STATIC_IMAGE(1, "静态图文"),

    // GIF(2, "动态图文"),

    // STATIC_VIDEO(3, "静态视频"),

    // INLINE(4, "广告位播放视频");
        STORY_VIDEO = 7;
    }
}

message PromotionPurposeType {
    enum Enum {

        NONE = 0;

        /**
        * APP推广
        */
        APP_PURPOSE = 1;

        /**
        * 落地页
        */
        LANDING_PAGE = 2;

        /**
        * 视频页
        */
        VIDEO = 3;

        /**
        * 应用下载
        */
        APP_DOWNLOAD = 4;

        /**
        * 会员购
        */
        SHOP_GOODS = 5;

        /**
        * 上架游戏
        */
        ON_SHELF_GAME = 6;

        /**
        * 投稿内容（内容起飞，商业起飞）
        */
        ARCHIVE_CONTENT = 7;

        /**
        * 直播间
        */
        LIVE_ROOM = 8;

        /**
        *  电商
        */
        DPA_SHOP_GOODS = 9;

        /**
        * OGV落地页
        */
        OGV_LINK = 10;

        /**
        * OGV应用下载
        */
        OGV_APP_DOWN = 101;

        /**
        * 淘宝电商
        */
        TAOBAO_SHOP_GOODS = 102;


        /**
        * DPA 商品目录
        */
        DPA_GOODS_LIST = 11;

        // 企业号推广
        ENTERPRISE_PROMOTION = 12;
    }
}

message CreativeType {
    enum Enum {
        IMAGE_TYPE = 0;

        VIDEO_TYPE = 1;

        FEEDS_IMAGE_TYPE = 2;

        FEEDS_VIDEO_TYPE = 3;
    }
}

message ProgrammaticMaterialType {
    enum Enum {
        UNKNOWN = 0;

        IMAGE = 1;

        VIDEO = 2;

        GIF = 3;

        TITLE = 4;

        TRIPLE_IMAGE = 5;

        VIDEO_COVER = 6;
    }
}

message SensitivityLevel {
    enum Enum {
        // RTB没有这个配置，加这个仅仅是为了能够通过编译
        UNKNOWN = 0;

        HIGH = 16124;

        NORMAL = 16126;

        POTENTIAL = 16165;

        LOW = 16366;

        COLD = 16367;
    }
}

message ExpectImprove {
    enum Enum {
        NONE = 0;

        PLAYCOUNTS = 1;

        FANSINCREASE = 2;
    }
}

message CreativeCtrWeightStatus {
    enum Enum {
        UNKNOWN = 0;

        NORMAL = 1;

        CLOSETO = 2;

        DOWNGRADING = 3;
    }
}

message CoarseSortCounterType {
    enum Enum {
        DEFAULT             = 0;

        OLD_INNER           = 1;

        OLD_OUTER           = 2;

        NEW_INNER           = 3;

        NEW_OUTER           = 4;

        SUPPORTED           = 5;

        UNSUPPORTED_COLD    = 6;

        UNSUPPORTED_HOT     = 7;

        SMALL_BUDGET        = 8;
    }
}

message OcpxConversionFilterLevel {
    enum Enum {
        NOTLIMIT        = 0;

        COMPANY_GROUP   = 1;

        ACCOUNT         = 2;

        PLAN            = 3;

        UNIT            = 4;
    }
}

message MarkBitType {
    enum Enum {
        // 搜索突破
        SEARCH_BREAK = 0;
        // ad_click
        AD_CLICK_BREAK = 1;
        // 框下场景下，根据avid选择需要突破的单元
        AVID_BREAK = 2;
        // 根据用户mid或者buvid选择需要突破的单元
        USER_BREAK = 3;
    }
}

message LauTargetIdType {
    enum Enum {
        ACCOUNT = 0;

        PLAN    = 1;

        UNIT    = 2;

        CREATIVE    = 3;

        SCHEDULE    = 4;
    }
}

// Source Scene
message SourceSceneType {
    enum Enum {
        UNKNOWN         = 0;

        FEEDS           = 1;

        PLAYPAGE        = 2;

        PC_HOME         = 3;

        PC_VIDEO_PAGE   = 4;

        UNDERFRAME      = 5;

        MOBILE_DYNAMIC  = 6;
    }
}

// Intelligence Augment
message IntelligenceAugmentTarget {
    enum Enum {
        UNKNOWN     = 0;
        AGE_TAG_ID  = 165;
        GENDER_TAG_ID   = 169;
        CROWD_TAG_ID    = 172;
    }
}

// Augment hive log
message AugmentHiveLogID {
    enum Enum {
        AD_TAG_AUGMENT_ID = 0;
        CROWD_AUGMENT_ID = 1;
        INTELLI_CROWD_AUGMENT_ID = 2;
    }
};