syntax = "proto3";

package proto.rpc.cassini;

import "extension/wdcli/wdcli.proto"; 
option (wdcli.appid) = "sycpb.cpm.cassini-bs";
option go_package = "buf.bilibili.co/bapis/bapis-gen/bcg/cassini.rapi;api";
option java_package = "com.bapis.bcg.cassini.rapi";
option java_multiple_files = true;
option cc_generic_services = true; 

import "bcg/cassini/rapi/enums/enum.proto"; 
import "bcg/cassini/rapi/dmp/dmp_user_profile_model.proto"; 

message Conversion {
    int64 mid = 1;
    repeated int64 company_group_ids = 2;
    repeated int64 account_ids = 3;
    repeated int64 plan_ids = 4;
    repeated int64 unit_ids = 5;
}

message ContextInfo {
    string query_id = 1;

    string request_source = 2;

    int64 request_time = 3;

    bool is_video_page_recommend = 4;

    int64 aid = 5;

    int32 video_category = 6;

    repeated int32 vctags = 7;

    repeated int32 prefer_stype_ids = 8;

    int64 up_mid = 9;

    // 动态当前刷次
    int32 dynamic_brush_times = 10;

    bool is_dynamic_page = 11;

    // 敏感创意样式ID
    repeated int64 sensitive_creative_tag_ids = 12;

    // 创意形态 枚举Id
    repeated int64 style_ability_enum_ids = 13;

    // 小游戏横屏
    bool landscape = 14;
    
	
    // 创意分级策略对应容忍分数
    int32 creative_grade_score = 15;

    repeated int32 prefer_tag_ids = 16;

    // 用户偏好Topic ID map <id, weight>, 有tagidlist 计算得来
    map<int32, double> prefer_topic_ids = 17;


    bool feeds = 18;

    sycpb.cpm.cassini.bs.enums.ImageStyle.Enum image_style = 19;

    // 去重卡片类新
    repeated int32 avoid_card_type_id_set = 20;

    // Feeds去重广告ID(现为单元ID)表

    repeated int64 feeds_unit_id_set = 21;

    // 最近若干次请求的 (账户OR公司,创意分类)
    repeated AccountBusinessCategoryTuple recent_account_business_category_tuple_set_for_feeds = 22;

    repeated AccountBusinessCategoryTuple recent_account_business_category_tuple_set_for_video = 23;

    repeated CompanyGroupOrAccountProductInfo co_group_or_acc_hours_tuple_info_for_dynamic = 24;

    repeated CompanyGroupOrAccountProductInfo co_group_or_acc_days_tuple_info_for_dynamic = 25;

    repeated CompanyGroupProductTuple co_group_or_acc_multi_brush_tuple_set_for_dynamic = 26;

    bool enable_company_product_filter = 27;

    repeated AccountBusinessCategoryTuple recent_company_group_product_tuple_set_for_feeds = 28;

    repeated int64 sensitive_category_ids = 29;

    repeated int64 sensitive_account_ids = 30;
    	
    repeated int64 sensitive_company_ids = 31;

    map<int32, DislikeGradeConcreteSet> dislike_grade_concrete_ids = 32;

    // ---------------------bsq 新增字段开始---------------------
    int32 beta0 = 33;
    
    int32 os = 34;

    string vender = 35;

    string model = 36;

    string screen_size = 37;

    string province = 38;

    string city = 39;

    sycpb.cpm.cassini.bs.enums.RequestNetworkEnum.Enum net_work = 40;

    repeated AppInfo app_info = 41;

    string av_duration = 42;
    	
    int32 av_is_vertical_scrren = 43;

    int32 av_play_duration_precent = 44;

    string from = 45;

    repeated LocationSource location_source = 46;
	
	map<int32, bool> outer_beat_inner_map = 47;

    // ---------------------bsq 新增字段结束---------------------


    repeated int32 resource_id_list = 48;

    /**
     * 单对象，冷却时间类
     */
    map<string, DynamicPage> typed_latest_single_coolings = 49;


    // 起飞视频id -> 对应展示次数， 用于频控
    map<int64, ShowCountEntry> video_id_frequency_control_group_show_counts = 50;

    repeated int64 video_avoid_unit_id_set = 51;

    // 单元ID->对此用户近期展现次数, 用于按广告设置频控方式做频次控制
    map<int64, int64> unit_show_counts = 52;

    map<int64, int64> image_show_counts = 53;

    map<int64, int64> title_show_counts = 54;

    map<int64, int64> unit_show_config_counts = 55;

    // 账户频控组 -> 对应展示次数， 用于频控
    map<string, int64> account_frequency_control_group_show_counts = 56;

    // 最近短时间内展现的单元ID，用于短期频次控制
    repeated int64 recent_unit_ids = 57;

    map<string, int64> style_ability_cooling = 58;

    // 限定刷次内， 创意形态 * cardType N刷去重
    repeated string style_ability_brush = 59;

    // pc端特定资源组 公司或账号*产品 1分钟内去重
    map<string, CompanyGroupProductTupleSet> recent_co_group_or_acc_product_for_sources = 60;

	    // 无偏流程
    bool random_result = 61;

    // 关闭个性化
    bool is_close_personalized_recommend = 62;
	map<string, BlockIds> freq_block_ids = 63;
	    /**
     * 是否避让Gif/inline
     * 是否会舍弃广告卡片 1-会, 0-不会
     * @return
     */
    int32 resist_gif = 64;

    // 小游戏appid
    string mini_game_app_id = 65;

    // 供识别PC流量
    bool is_pc_home = 66;
    bool is_pc_video_page = 67;


    /**
     * 单对象，N刷去重类
     */
    map<string, TypedSingleTargetFrequencyValue> typed_single_target_frequency_map = 68;

    bool hit_inline_planb = 69;

    //UP主是否拥有托管广告权限
    bool uploader_entitle_ad = 70;

    //UP主是否拥有互选位权限
    bool uploader_entitle_recommend_source = 71;

    //UP主是否开启托管广告
    bool uploader_trust_ad = 72;

    //UP主视频是否开启托管广告
    bool uploader_video_trust_ad =73;


    int32 inline_able = 74;

    // up主托管广告位屏蔽的行业一级类目(新)
    repeated int32 uploader_block_first_category_ids = 75;

    // up主托管广告位屏蔽的行业二级类目（新）
    repeated int32 uploader_block_second_category_ids = 76;

    // up主托管广告位屏蔽的creative ids
    repeated int64 uploader_block_creative_ids = 77;

    // up主托管广告位屏蔽的app name
    repeated int64 uploader_block_app_package_names = 78;

    // up主托管广告位屏蔽的落地页
    repeated int64 uploader_block_url = 79;

    // 用户已转化信息
    Conversion conversion_info = 80;

    // app定向信息
    repeated AppInfo app_tag = 81;
}

message TypedSingleTargetFrequencyValue {
    repeated int64 value = 1;
}
message BlockIds {
    repeated string ids = 1;
}
message CompanyGroupProductTupleSet {
    repeated CompanyGroupProductTuple company_group_product_tuples = 1;

}

message ShowCountEntry {
    int64 show_count = 1;

    int64 time = 2;
}

message DynamicPage {
    map<string, int64> map = 1;
}
						 

message AppInfo {
    int32 id = 1;

    int32 type = 2;
}

message EventEntry {
    int64 id = 1;

    int64 time = 2;
}

message LocationSource {
    int32 source_id = 1;
    
    int32 index = 2;
    
    repeated int32 template_id_list = 3;

    bool support_adx = 4;

    int32 card_index = 5;
    
    int32 resource_id = 6;

    int32 page_id = 7;

    sycpb.cpm.cassini.bs.enums.LocationPlatform.Enum platform = 8;

    bool feeds = 9;

    bool video_page_recommend = 10;
    
    int32 dynamic_page = 11;

    repeated string frequency_source_names = 12;

    bool is_trust_ad_source = 13;
}

message DislikeGradeConcreteSet {
    repeated string dislike_grade_concretes = 1;
}

message CompanyGroupOrAccountProductInfo {

    CompanyGroupProductTuple company_group_product_tuple = 1;

    int32 show_count = 2;
}

message AccountBusinessCategoryTuple {
    sycpb.cpm.cassini.bs.enums.AcType.Enum ac_type = 1;

    int64 ac_id = 2;

    int32 business_category_level = 3;

    int32 business_category_id = 4;
}

message CompanyGroupProductTuple {
    sycpb.cpm.cassini.bs.enums.AcType.Enum ac_type = 1;

    int64 ac_id = 2;

    int32 product_id = 3;
}

message TargetingCriteria {
    repeated int32 source_ids = 1;

    repeated string areas = 2;

    repeated int32 genders = 3;

    repeated int32 oss = 4;

    /**
     * 网络定向ID列表
     */
    repeated int32 networks = 5;

    /**
     * 分区定向ID列表
     */
    repeated int32 categories = 6;

    /**
     * 设备品牌定向列表
     */
    repeated string device_brands = 7;

    /**
     * APP类别定向列表
     */
    repeated int32 app_categorys = 8;

    repeated int64 fans_mid = 9;

    // repeated int64 not_fans_mid = 10;

    /**
     * 是否是通投
     */
    repeated int32 no_limited_launch = 11;

    repeated int32 available_sources = 12;

    repeated string age_format_confidence = 13;

    repeated string downgrade_survivors = 14;

    repeated int32 programmatic_set = 15;
}

message RecallFilterStrategy {
    string ad_type = 1;
    string name = 2;
    string version = 3;
    int32 limit = 4;
}

message CommonFilterStrategy {
    string strategy_name = 1;
}

message SourceFilterStrategy {
    string strategy_name = 1;
}

message TruncationStrategy {

}

message SearchUnit {
    sycpb.cpm.cassini.bs.enums.AdType.Enum ads_type = 1;

    repeated RecallFilterStrategy recall_filter_strategies = 2;

    repeated CommonFilterStrategy common_filter_strategies = 3;

    repeated SourceFilterStrategy source_filter_strategies = 4;

    repeated TruncationStrategy truncation_strategies = 5;
}

message SearchInfo {
    string source_type = 1;

    repeated SearchUnit search_units = 2;

    TargetingCriteria targeting_criteria = 3;

}

message UserInfo {

    // 对移动端，为APP版本号; 对PC端为0
    int32 build_version = 1;

    // 是否大会员
    bool is_vip = 2;

    // 用户使用APP类型; 对于PC为{@link MobileAppEnum#UNKNOWN}
    sycpb.cpm.cassini.bs.enums.MobileApp.Enum mobile_app = 4;

    sycpb.cpm.cassini.bs.enums.LocationPlatform.Enum location_platform = 5;
    
    // 二期
    // ---------------------bsq新增字段-----------------------
    int32 gender = 6;

    int64 mid = 7;

    string buvid = 8;

    int32 age = 9;

    string mid_player_page_7d_clk = 10;

    map<int32, string> mid_player_page_srcid_7d_clk = 11;
    
    repeated EventEntry last_ad_click = 12;

    repeated EventEntry last_ad = 13;
    // ---------------------bsq新增字段-----------------------

    // 用户所属人群ID列表
    repeated int32 crowd_ids = 14;

    sycpb.cpm.cassini.bs.dmp.UserProfileModel user_profile_model = 15;

	// 拉黑名单
    repeated int64 blacklist_mids = 16;

    // 被拉黑名单
    repeated int64 blacklisted_mids = 17;

    // 播放页up主屏蔽的个人起飞名单，播放页的个人起飞专用
    repeated int64 personal_fly_shield_list = 18;


    // PC端Cookie "sid". 移动端无
    string sid = 19;

    string idfa = 20;
    
    string imei = 21;
    
    string oaid = 22;

    string new_oaid = 23;

}


message CreativeFreqFilter{
    string ten_brush_freq_target = 1;

    string cluster_exp = 2;

    bool new_duplicated_unit_filter = 3;

    bool new_similar_unit_filter = 4;
}
message PCExperiment {
    bool use_pc_experiment = 1;

    string pc_home_pctr_strategy = 2;

    string pc_video_page_pctr_strategy = 3;

    int64 pc_cpc_ctr_threshold = 4;

    int64 pc_home_ecpm_threshold = 5;

    int64 pc_video_page_ecpm_threshold = 6;
}

message ProgrammaticTemplate {
    int32 template_id                   = 1;

    int32 source_id                     = 2;

    repeated int64 material_id_list     = 3;

    repeated int64 image_id_list        = 4;

    repeated int64 video_id_list        = 5;
}

message ProgrammaticTitle {
    int64 title_id  = 1;

    string title    = 2;
}

message Experiment {
    string name = 1;

    bool dmp_on_prefer_tag_exp = 2;

    repeated int32 exp_prefer_tag_ids = 3;

    bool remove_frequency_control = 4;

    bool brush_duplicate_filter_for_vip = 5;

    bool brush_duplicate_filter_for_normal = 6;

    // 是否去除公司组*创意分类频控
    bool remove_com_creative_type_filter = 7;

    string fly_cpa_config_key = 8;

    string cpa_config_key = 9;


    /* 二期 */
    // 是否开启人群包暗投
    bool enable_crowd_secretly = 10;

    // 人群包暗投KEY
    string crowd_secretly_exp_key = 11;

    int32 dmp_ignored_type = 12;

    bool resource_type_truncate = 13;
    
    double inner_ctr_q_factor = 14;

    double outer_ctr_q_factor = 15;

    bool cpc_tag_filter = 16;

    bool rebuild_source = 17;

    string downgrade_strategy = 18;

    string smart_bid_ratio_exp_key = 19;

    bool ocpx_two_stage_truncate = 20;

    bool is_ocpc_new = 21;

    string pcvr_cpm_config_key = 22;

    string pcvr_cpc_config_key = 23;

    bool is_cvr_config_gamma = 24;

    bool on_filter_optimize = 25;

    bool feeds_close_small_budget = 26;

    bool playpage_close_small_budget = 27;

    bool feeds_close_small_budget_dpa = 28;

    bool playpage_close_small_budget_dpa = 29;

    bool content_up_feeds_small_budget = 30;

    bool content_up_playpage_small_budget = 31;

    bool cold_boot_budget = 32;

    bool use_redis_stock_config = 33;

    int32 ctr_truncation_new_outer_unit_num = 34;

    int32 ctr_truncation_old_inner_unit_num = 35;

    int32 ctr_truncation_old_outer_unit_num = 36;

    bool enable_ctr_truncation = 37;

    int32 enter_coarse_sort_inner_count = 38;

    int32 enter_coarse_sort_outer_count = 39;

    int32 coarse_sort_pick_creatives_count = 40;

    int32 coarse_sort_pick_poster_creatives_ratio = 41;

    bool ctr_explore_strategy = 42;
    
    int32 first_adunit_chosen_ratio = 43;

    int32 person_up_cutoff_limit = 44;

    int32 content_up_cutoff_limit = 45;

    bool video_page_random_result = 46;

    bool coarse_sort_by_predict = 47;

    int32 ctr_truncation_new_inner_unit_num = 48;

    map<string, bool> frequency_strategy_switchs = 49;

    int32 freq_limit = 50;

    // UV 时间频控实验， 0-未命中实验，1-命中实验组-系统默认时间频控， 2-命中实验组-无时间频控
    int32 uv_time_freq_strategy = 51;

    map<string, int32> creative_frequency_limit = 52;

    map<string, string> ue_factor_parameter = 53;

    string recall_strategy = 54;

    bool is_dpa_cut_by_recall = 55;

    CreativeFreqFilter creative_freq_filter = 56;

    map<string, string> new_creative_control = 57;

    int64 play_page_bsq_ecpm = 58;

    bool is_content_up_config_lambda = 59;

    map<string, string> coarse_cvr_variable_map = 60;

    PCExperiment pc_experiment = 61;

    // new budget smooth
    bool is_budget_smooth_exp_control = 62;
	
    string budget_smooth_exp_key = 63;

	bool is_dpa2_open = 64;

	int32 enter_coarse_sort_dpa_count = 65;

	int32 ctr_truncation_dpa_unit_num = 66;
	
	int32 ctr_truncation_dpa_unit_num_cpm = 67;
	
    bool small_budget_control_disable = 68;	 
	
	ContentUpCutOff content_up_cutoff = 69;
	
	map<string, int32> content_up_cold_recall_exp = 70;

    bool open_business_up_cpc = 71;

    int32 person_up_ctr_truncation_new_outer_unit_num = 72;

    int32 person_up_ctr_truncation_new_inner_unit_num = 73;

    int32 person_up_ctr_truncation_old_outer_unit_num = 74;

    int32 person_up_ctr_truncation_old_inner_unit_num = 75;

    int32 content_up_ctr_truncation_new_outer_unit_num = 76;

    int32 content_up_ctr_truncation_new_inner_unit_num = 77;

    int32 content_up_ctr_truncation_old_outer_unit_num = 78;

    int32 content_up_ctr_truncation_old_inner_unit_num = 79;

    bool person_up_cutoff_by_poster_ecpm = 80;

    bool content_up_cutoff_by_poster_ecpm = 81;

    double personal_up_ecpm_weight = 82;

    string ocpm_two_stage_pcvr_truncate_exp_key = 83;

    bool is_campaign_budget_smooth = 84;

    string ocpc_2nd_phase_bottom_logic_exp_key = 85;

    //slb ab exp parameters
    map<string, string> ab_map = 200;
}
message ContentUpCutOff {
     bool open_cold_unit_switch = 1;
     int32 content_up_cut_off_limit = 2;
     int32 content_up_cold_cut_off_limit = 3;
     string cold_ocpm_feeds_exp_key = 4;
     string cold_ocpm_playpage_exp_key = 5;
}

message BsRequest {
    ContextInfo context_info = 1;

    SearchInfo search_info = 2;

    UserInfo user_info = 3;

    // for metrics generation
    bool is_enable_log = 4;

    // for dry run
    bool debug = 5;

    Experiment experiment = 6;
}

message FilterProcess {
    string filter_name = 1;
    string filer_idx = 2;
    repeated uint64 filtered_unit_id = 3;
}

message BsMetrics {
    repeated FilterProcess filter_process = 1;
}

message AdCreative {
    int64 creative_id = 1;

    int32 template_id = 2;

    //宏替换创意标题
    string macro_title = 3;

    //宏替换描述
    string macro_description = 4;

    //宏替换跳转链接
    string macro_jump_url = 5;

    //宏替换展示监控
    string macro_show_url = 6;

    //宏替换点击监控
    string macro_click_url = 7;

    //宏替换唤醒监控
    string macro_callup_url = 8;

    //宏替换动态模板
    string macro_dynamic_layout = 9;

    //DPA2.0 外部商品ID
    int64 outer_id = 10;

    //DPA策略
    string dpa_strategy = 11;

    //DPA2.0 内部商品ID
    int64 product_id =12;

    repeated ProgrammaticTemplate programmatic_template_infos = 13;

    repeated ProgrammaticTitle programmatic_titles = 14;
}

message AccountTagFrequencyControlInfo {
    int32 tag = 1;

    string name = 2;

    sycpb.cpm.cassini.bs.enums.FrequencyUnit.Enum freq_unit = 3;

    int32 freq_limit = 4;

    int32 freq_interval = 5;
}

message AdUnit {
    int64 unit_id = 1;

    repeated AdCreative creative_list = 2;

    int64 doc_id = 3;

    int32 template_id = 4;

    sycpb.cpm.cassini.bs.enums.AdType.Enum ads_type = 5;

    sycpb.cpm.cassini.bs.enums.BusinessType.Enum biz_type = 6;

    double pctr = 7;

    double ecpm  = 8;

    double pcvr = 9;

    sycpb.cpm.cassini.bs.enums.BudgetType.Enum unit_budget_type = 39;

    int64 unit_budget = 40;

    sycpb.cpm.cassini.bs.enums.BudgetType.Enum plan_budget_type = 41;

    int64 plan_budget = 42;

    // 命中扩量维度标记
    int32 hit_aug_bits = 43;

    repeated AccountTagFrequencyControlInfo account_tag_frequency_control_infos = 44;
}


message SearchResult {
    int32 source_id = 1;

    repeated AdUnit ad_units = 2;
 

    bool is_mercy = 3;
						
    int32 actual_inner = 4;

    int32 actual_outer = 5;

    /* begin adding for as*/
    SourceStat inner_stat = 6; // 历史累计的stat信息 source_id=>inner_stat和outer_stat

    SourceStat outer_stat = 7;
    /* end adding for as*/


    bool is_content_fly_mercy = 8;
}
    /* begin adding for as*/
    // 资源位的广告统计
    message SourceStat {

        uint64 show = 1;

        uint64 click = 2;

        double cost = 3;

    }

message BsResponse {
    string query_id = 1;

    BsMetrics metrics = 2;

    string debug_message = 3;

    repeated SearchResult search_results = 4;
    
}

message PingRequest {
    int32 ping = 1;
}

message PingResponse {
    int32 pong = 1;
}

service BsService {
    rpc retrieve(BsRequest) returns (BsResponse);
    rpc ping(PingRequest) returns (PingResponse);
}