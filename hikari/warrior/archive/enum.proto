syntax = "proto3";

package hikari.warrior.archive;
option go_package = "buf.bilibili.co/bapis/bapis-gen/hikari/warrior.archive;api";
option java_package = "com.bapis.hikari.warrior.archive";
option java_multiple_files = true;

enum ApprovedArchiveState {
    // 未知
    APPROVED_ARCHIVE_STATE_UNKNOWN = 0;
    // 过审
    APPROVED = 1;
    // 打回
    REFUSE = -1;
    // 删除
    DELETED = -99;
    // 锁定
    LOCKED = -101;
}

enum ArchiveState {
    // 初稿待审
    ARCHIVE_INIT_WAIT_AUDIT = 0;
    // 初稿过审
    ARCHIVE_INIT_PASS = 11;
    // 初稿待会
    ARCHIVE_INIT_BACK = -11;
    // 修复待审
    ARCHIVE_EDIT_WAIT_AUDIT = -20;
    // 修复过审
    ARCHIVE_EDIT_PASS = 21;
    // 修复待会
    ARCHIVE_EDIT_BACK = -21;
    // 删除
    ARCHIVE_DELETED = -99;
    // 锁定
    ARCHIVE_LOCKED = -101;
}

enum ArchiveType {
    // 未知
    ARCHIVE_TYPE_UNKNOWN = 0;
    // 图文(富文本)
    RICH_TEXT = 1;
}

enum ChannelResourceScene {
    // 未知
    SCENE_UNKNOWN = 0;
    // 全局置顶
    SCENE_GLOBAL_TOP = 1;
    // 频道首页置顶
    SCENE_CHANNEL_HOME_TOP = 2;
}

enum ChannelResourceType {
    // 未知
    RESOURCE_TYPE_UNKNOWN = 0;
    // 公告
    RESOURCE_TYPE_NOTICE = 1;
    // 活动
    RESOURCE_TYPE_ACTIVITY = 2;
}

enum ChannelResourceLinkType {
    // 未知
    RESOURCE_LINK_TYPE_UNKNOWN = 0;
    // 链接
    RESOURCE_LINK_TYPE_LINK = 1;
    // 稿件
    RESOURCE_LINK_TYPE_ARCHIVE = 2;
}

enum ChannelOwnerType {
    // 未知
    OWNER_TYPE_UNKNOWN = 0;
    // 版主
    OWNER = 1;
    // 副版主
    VICE_OWNER = 2;
}

enum OperatorType {
    // 未知
    OPERATOR_TYPE_UNKNOWN = 0;
    // 用户
    USER = 1;
    // 管理员
    MANAGER = 2;
}

enum ChannelOwnerState {
    // 默认值版主
    OWNER_STATE_UNKNOWN = 0;
    // 有效版主
    ACTIVE = 1;
    // 无效版主
    DISABLED = -1;
}