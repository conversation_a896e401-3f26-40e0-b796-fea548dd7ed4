syntax = "proto3";

package live.xuser.v1;

import "extension/wdcli/wdcli.proto"; // 如果是在bapis仓库写这行，去掉下面一行

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/xuser.v1;v1";
option java_package = "com.bapis.live.xuser.v1";
option java_multiple_files = true;
option (wdcli.appid) = "live.xuser"; // 注意请填写能够被discovery服务发现的appid！！！

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

// UserExp 相关服务
service UserExp {
    // GetUserExp 获取用户经验与等级信息,支持批量
    rpc GetUserExp (GetUserExpReq) returns (GetUserExpResp);
}

// GetUserExpReq 请求
message GetUserExpReq {
    repeated int64 uids = 1 [(gogoproto.moretags) = 'form:"uids" validate:"gt=0,required"'];
}

// GetUserExpResp 响应
message GetUserExpResp {
    map<int64, LevelInfo> data = 1 [(gogoproto.jsontag) = "data", json_name = "data"];
}

message LevelInfo {
    int64 uid = 1 [(gogoproto.jsontag) = "uid", json_name = "uid"];
    // 用户等级信息
    UserLevelInfo userLevel = 2 [(gogoproto.jsontag) = "userLevel", json_name = "userLevel"];
    // 主播等级信息省略
}

message UserLevelInfo {
    // 当前用户等级
    int64 level = 1 [(gogoproto.jsontag) = "level", json_name = "level"];
    // 其余信息省略,需要再添加
}

// 姥爷服务,空服务用于避免cli重命名
service Vip {
    // Info 返回用户vip信息(防止cli重名使用,接口未开放)
    rpc Info(UidReq) returns (InfoReply);
}

message UidReq {
    int64 uid = 1 [(gogoproto.moretags) = "validate:\"gt=0,required\""];
}

message InfoReply {

}


