syntax = "proto3";
package live.xuser.v1;
option  go_package          = "buf.bilibili.co/bapis/bapis-gen/live/xuser.v1.guide_status;guide_status";
option  java_package        = "com.bapis.live.xuser.v1.guide_status";
option  java_multiple_files = true;
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "live.xuser";  // 注意请填写能够被discovery服务发现的appid！！！
import "google/protobuf/empty.proto";


enum StatusType {
    UNKNOWN_S = 0;
}

service GuideStatus {
    //获取引导状态
    rpc GetStatus(GetStatusReq) returns (GetStatusResp);
    //设置引导状态
    rpc SetStatus(SetStatusReq) returns (google.protobuf.Empty);
}

message GetStatusReq{
    int64 room_id = 1   [(gogoproto.moretags) = 'form:"room_id"'];
}

message GetStatusResp{
    repeated GuideInfo guide_info = 1 [(gogoproto.jsontag) = "guide_info"];
}

message SetStatusReq{
    //业务ID KV中的key
    string biz_type = 1 [(gogoproto.moretags) = 'form:"biz_type" validate:"required"'];
    //房间号
    int64 room_id = 2   [(gogoproto.moretags) = 'form:"room_id"'];
    //如果status是0 则删除这个状态
    int64 status = 3 [(gogoproto.moretags) = 'form:"status"'];
    //秒 持续时间 不填默认为1天
    int64 last_time = 4 [(gogoproto.moretags) = 'form:"last_time"'];
}

message GuideInfo{
    string biz_type = 1 [(gogoproto.jsontag) = "biz_type"];
    int64 status = 2 [(gogoproto.jsontag) = "status"];
}