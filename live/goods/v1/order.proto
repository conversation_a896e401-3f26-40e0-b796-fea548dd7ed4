syntax = "proto3";
package live.goods.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/goods.v1;v1";
option java_package = "com.bapis.live.goods.v1";
option java_multiple_files = true;
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
import "live/goods/v1/goods.proto";

option (wdcli.appid) = "live.live.goods";

service order {
  // 订单 下单检查商品
  rpc CheckGoods(CheckGoodsReq) returns (CheckGoodsResp);
  // 扣减库存 扣缓存
  rpc DeductionStockCache(DeductionStockCacheReq) returns (DeductionStockCacheResp);
  // 扣减库存 扣数据库
  rpc DeductionStock(DeductionStockReq) returns (DeductionStockResp);

  // 获取商品信息
  rpc GetGoodsInfoForOrder(GetGoodsInfoForOrderReq) returns (.live.goods.v1.GoodsInfoSpuData);
  // 根据iap product_id 获取商品信息
  rpc GetGoodsInfoByProductId(GetGoodsInfoByProductIdReq) returns (.live.goods.v1.GoodsInfoSpuData);

}
message GetGoodsInfoByProductIdReq {
  // 业务方
  int64 biz_id = 1;
  // iap product_id
  string product_id = 2 ;
}
message GetGoodsInfoForOrderReq {
  // 业务方
  int64 biz_id = 1;
  // spu_id
  int64 spu_id = 2 ;
  // sku_id
  int64 sku_id = 3 ;
  // mid
  int64 mid = 4 ;
  // 检查售卖状态 默认检查只给上线 传true不检查
  bool check_sale = 5 ;

}



message CheckGoodsReq {
  // spu_id
  int64 spu_id = 1 [(gogoproto.jsontag) = "spu_id", json_name = "spu_id", (gogoproto.moretags) = 'form:"spu_id"'];
  // 用户id
  int64 uid = 2 [(gogoproto.jsontag) = "uid", json_name = "uid", (gogoproto.moretags) = 'form:"uid"'];
  // goods_id  商品skuId
  int64 goods_id = 3 [(gogoproto.jsontag) = "goods_id", json_name = "goods_id", (gogoproto.moretags) = 'form:"goods_id"'];
  // 商品分类
  int64 goods_cate = 4 [(gogoproto.jsontag) = "goods_cate", json_name = "goods_cate", (gogoproto.moretags) = 'form:"goods_cate"'];
  // 装扮购买月 -1 表示永久
  int64 add_month = 5 [(gogoproto.jsontag) = "add_month", json_name = "add_month", (gogoproto.moretags) = 'form:"add_month"'];
  // biz_id
  int64 biz_id = 6 [(gogoproto.jsontag) = "biz_id", json_name = "biz_id", (gogoproto.moretags) = 'form:"biz_id"'];
}
message CheckGoodsResp {
  // spu_id
  int64 spu_id = 1 [(gogoproto.jsontag) = "spu_id", json_name = "spu_id", (gogoproto.moretags) = 'form:"spu_id"'];
  // sku_id
  int64 sku_id = 2 [(gogoproto.jsontag) = "sku_id", json_name = "sku_id", (gogoproto.moretags) = 'form:"sku_id"'];
  // 商品创作者uid
  int64 owner_uid = 3 [(gogoproto.jsontag) = "owner_uid", json_name = "owner_uid", (gogoproto.moretags) = 'form:"owner_uid"'];
  // 业务的sku类型 1：普通套装，2：永久套装
  int64 biz_sku_type = 4 [(gogoproto.jsontag) = "biz_sku_type", json_name = "biz_sku_type", (gogoproto.moretags) = 'form:"biz_sku_type"'];
  // 商品分类
  int64 goods_cate = 5 [(gogoproto.jsontag) = "goods_cate", json_name = "goods_cate", (gogoproto.moretags) = 'form:"goods_cate"'];
  // 商品父级分类
  int64 parent_goods_cate = 6 [(gogoproto.jsontag) = "parent_goods_cate", json_name = "parent_goods_cate", (gogoproto.moretags) = 'form:"parent_goods_cate"'];
  // 业务来源 0 直播营收 1 装扮 2 盘古 3 课程
  int64 biz_source = 7 [(gogoproto.jsontag) = "biz_source", json_name = "biz_source", (gogoproto.moretags) = 'form:"biz_source"'];
  // 分组id
  int64 group_id = 8 [(gogoproto.jsontag) = "group_id", json_name = "group_id", (gogoproto.moretags) = 'form:"group_id"'];
  // sku 价格
  int64 price = 9 [(gogoproto.jsontag) = "price", json_name = "price", (gogoproto.moretags) = 'form:"price"'];
  // 商品名称
  string sku_name = 10 [(gogoproto.jsontag) = "sku_name", json_name = "sku_name", (gogoproto.moretags) = 'form:"sku_name"'];
  // 订单处理业务方
  int64 service_id = 11 [(gogoproto.jsontag) = "service_id", json_name = "service_id", (gogoproto.moretags) = 'form:"service_id"'];
  // 新老装扮标记  老装扮返回true
  bool flag_old_goods = 12 [(gogoproto.jsontag) = "flag_ol_goods", json_name = "flag_ol_goods", (gogoproto.moretags) = 'form:"flag_ol_goods"'];
}

message DeductionStockReq {
  //
  int64 sku_id = 1 [(gogoproto.jsontag) = "sku_id", json_name = "sku_id", (gogoproto.moretags) = 'form:"sku_id"'];
  // 数量
  int64 num = 2 [(gogoproto.jsontag) = "num", json_name = "num", (gogoproto.moretags) = 'form:"num"'];
  // 订单id
  string order_id = 3 [(gogoproto.jsontag) = "order_id", json_name = "order_id", (gogoproto.moretags) = 'form:"order_id"'];
  // 用户uid
  int64 uid = 4 [(gogoproto.jsontag) = "uid", json_name = "uid", (gogoproto.moretags) = 'form:"uid"'];
  // 操作方式 0 表示 下单购买 1 表示 赠送预留
  int64 action_type = 5 [(gogoproto.jsontag) = "action_type", json_name = "action_type", (gogoproto.moretags) = 'form:"action_type"'];
}

message DeductionStockResp {
  // 剩余库存
  int64 after_stock = 1 [(gogoproto.jsontag) = "after_stock", json_name = "after_stock", (gogoproto.moretags) = 'form:"after_stock"'];
}

message DeductionStockCacheReq {
  //
  int64 sku_id = 1 [(gogoproto.jsontag) = "sku_id", json_name = "sku_id", (gogoproto.moretags) = 'form:"sku_id"'];
  // 数量
  int64 num = 2 [(gogoproto.jsontag) = "num", json_name = "num", (gogoproto.moretags) = 'form:"num"'];
  // 订单id
  string order_id = 3 [(gogoproto.jsontag) = "order_id", json_name = "order_id", (gogoproto.moretags) = 'form:"order_id"'];
  // 用户uid
  int64 uid = 4 [(gogoproto.jsontag) = "uid", json_name = "uid", (gogoproto.moretags) = 'form:"uid"'];
}
message DeductionStockCacheResp {
  // 剩余库存
  int64 after_stock = 1 [(gogoproto.jsontag) = "after_stock", json_name = "after_stock", (gogoproto.moretags) = 'form:"after_stock"'];
}