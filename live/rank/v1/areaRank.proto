syntax = "proto3";
package live.rank.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/live/rank.v1;v1";
option java_package = "com.bapis.live.rank.v1";
option java_multiple_files = true;
option (wdcli.appid) = "live.live.rank";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

service AreaRank {
  // C端-查询当前房间需要展示的分区榜列表
  rpc FrontendAreaRankGetRoomRankList(FrontendAreaRankGetRoomRankListReq) returns (FrontendAreaRankGetRoomRankListResp);
  // C端-查询当前房间单个榜单数据
  rpc FrontendAreaRankGetRoomRankInfo(FrontendAreaRankGetRoomRankInfoReq) returns (FrontendAreaRankGetRoomRankInfoResp);
}
message FrontendAreaRankGetRoomRankListReq {
  // 主播ID
  int64 ruid = 1;
  // 二级分区ID
  int64 area_id = 2;
  // 一级分区ID
  int64 parent_area_id = 3;
  // 终端
  int64 terminal = 4;
}
message FrontendAreaRankGetRoomRankListResp {
  // 榜单配置列表
  repeated AreaRankRoomRankConfItem items = 1 [(gogoproto.jsontag) = "items", json_name = "items"];
  // web 端轮播时间、毫秒
  int64 rotation_cycle_time_web = 2 [(gogoproto.jsontag) = "rotation_cycle_time_web", json_name = "rotation_cycle_time_web"];
}

message FrontendAreaRankGetRoomRankInfoReq {
  // 分区榜配置ID
  int64 conf_id = 1;
  // 主播ID
  int64 ruid = 2;
}
message FrontendAreaRankGetRoomRankInfoResp {
  // 列表
  repeated AreaRankRoomRankScoreItem items = 1;
  // 当前用户
  AreaRankRoomRankScoreOwner owner = 2;
  // 配置
  AreaRankConfItem conf = 3;
}

// 分区榜配置
message  AreaRankConfItem {
  // 主键ID
  int64 id = 1;
  // 榜单类型 1:分区 2:功能
  int64 rank_type = 2;
  // 功能类型 1:大航海数量 2:带货
  int64 feature_type = 3;
  // 展示分区 0:不展示 1:展示
  int64 show_areas = 4;
  // 榜单ID、底层存储的榜单ID
  int64 rank_id = 5;
  // 榜单名称、对外展示
  string rank_name = 6;
  // 积分名称、对外展示
  string score_name = 7;
  // 榜单ICON URL、对外展示
  IconUrl icon_url = 8;
  // 说明文案、对外展示
  string desc_content = 9;
  // 榜单周期 1:总榜 2:周榜 3:日榜 4:小时榜
  int64 cycle_type = 10;
  // 是否展示结榜倒计时 1:展示 0:不展示
  int64 show_countdown_time = 11;
  // 榜单展示总人数限制、默认50
  int64 item_display_num = 12;
  // 开始时间
  int64 start_time = 13;
  // 结束时间
  int64 end_time = 14;
  // 规则列表
  repeated Rule rules = 15;
  // 主播黑名单规则ID
  int64 black_rule_master = 16;
  // 用户黑名单规则ID
  int64 black_rule_user = 17;
  // 操作人
  string operator = 18;
  // 二级分区列表
  repeated int64 area_ids = 19;
  // 加分配置
  ScoreConf  score_conf = 20;
  int64 ctime = 21;
  int64 mtime = 22;
  // 状态 0:未上线 1:已上线 2:已下线
  int64 rank_status = 23;
  // 是否删除 0:未删除  1:删除
  int64 is_delete = 24;
  // 是否是永久榜单 0:非永久  1:永久  只是展示用，实际控制靠结束时间
  int64 is_permanent = 25;

  message ScoreConf {
    // 分区榜开关
    /*
      guard   大航海
      gold    金瓜子、付费礼物
      super_chat  醒目留言
      like    点赞
      danmu   弹幕
      user_watch_time 用户观看
      voice_num     连麦次数
      voice_times   连麦时长
    */
    map<string, int64> area_switch = 1;
    // 带货榜开关、接口动态下发
    map<string, int64> shop_switch = 2;
    // 大航海榜开关，只有一个 guard
    map<string, int64> guard_switch = 3;
  }
  message  Rule {
    // 标题
    string title = 1;
    // 内容
    string content = 2;
  }
  message IconUrl {
    string blue = 1;
    string pink = 2;
    string grey = 3;
  }
}
message AreaRankConfScoreShopObj {
  //  行为合并、多少个数值加一次分
  int64 merge_num = 1;
  // 每次加多少分
  int64 exchange = 2;
  // 数据源用户单日上限 - 分数
  int64 threshold_day_for_source_uid_score = 3 [(gogoproto.jsontag) = "threshold_day_for_source_uid_score"];
  // 数据源用户小时上限 - 分数
  int64 threshold_hour_for_source_uid_score = 4 [(gogoproto.jsontag) = "threshold_hour_for_source_uid_score"];
}

message AreaRankRoomRankConfItem {
  // 分区榜配置ID
  int64 conf_id = 1  [(gogoproto.jsontag) = "conf_id"];
  // 分区榜名称
  string rank_name = 2 [(gogoproto.jsontag) = "rank_name"];
  // 用户ID
  int64 uid = 3 [(gogoproto.jsontag) = "uid"];
  // 排名
  int64 rank = 4 [(gogoproto.jsontag) = "rank"];
  // icon_url
  string icon_url_blue = 5 [(gogoproto.jsontag) = "icon_url_blue"];
  string icon_url_pink = 6 [(gogoproto.jsontag) = "icon_url_pink"];
  string icon_url_grey = 7 [(gogoproto.jsontag) = "icon_url_grey"];

  // 跳转地址 - 直播姬+粉播
  string jump_url_link = 8 [(gogoproto.jsontag) = "jump_url_link"];
  // 跳转地址 - PC直播姬
  string jump_url_pc = 9 [(gogoproto.jsontag) = "jump_url_pc"];
  // 跳转地址 - 粉看
  string jump_url_pink = 10 [(gogoproto.jsontag) = "jump_url_pink"];
  // 跳转地址 - Web房间页
  string jump_url_web = 11 [(gogoproto.jsontag) = "jump_url_web"];
}
message AreaRankRoomRankScoreItem {
  // 用户ID
  int64 uid = 1;
  // 排名
  int64 rank = 2;
  // 分区
  int64 score = 3;
}
message AreaRankRoomRankScoreOwner {
  // 用户ID
  int64 uid = 1;
  // 分数
  int64 score = 2;
  // 排名
  int64 rank = 3;
  //  分数差异 rank=1 领先下一名XX分、rank=1～50 距离上一名XX分、rank>50 距离上榜XX分
  int64 diff_score = 4;
  // 是否被拉黑、黑名单
  bool is_blocked = 5;
}