syntax = "proto3";
package live.payplay.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/live/pay.play.v1;v1";
option java_package = "com.bapis.live.pay.play.v1";
option java_multiple_files = true;
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "live.payplay";

enum PayLiveClassErrCode {
    PayLiveClassUndefined = 0; // 无意义 忽略
    DbExecErr = 10001; //数据库执行错误
    ParamsNotValid = 10002; //参数不合法
    RoomNotExist = 20002; //房间号不存在
    GoodsNotFound = 20003; //票务信息不存在
    TimeConflict = 20004; //时间冲突
    EpidAlreadyExisted = 30001; //epid已建立相关直播设置
    PayLiveInfoNotFound = 30002; //pay_live_id记录不存在
    TimeUpdateForbidden = 30003; //禁止修改付费直播时间段
}

service PayLiveClass {

    //创建一个付费直播间
    rpc createPayLiveRoom (CreatePayLiveRoomReq) returns (CreatePayLiveRoomResp);

    //更新一个付费直播间
    rpc updatePayLiveRoom (UpdatePayLiveRoomReq) returns (UpdatePayLiveRoomResp);

    // 删除直播课程
    rpc updatePayLiveStatus (UpdatePayLiveStatusReq) returns (UpdatePayLiveStatusResp);

    // 判断是否是付费课堂
    rpc checkPayLiveRoom (CheckPayLiveRoomReq) returns (CheckPayLiveRoomResp);

    // 付费课堂延长有效期
    rpc extendPayLiveRoomValidity (ExtendPayLiveRoomValidityReq) returns (ExtendPayLiveRoomValidityResp);

    // 获取付费直播信息
    rpc getInfoByPayLiveId(GetInfoByPayLiveIdReq) returns(GetInfoByPayLiveIdResp);
}


service PayLive {
    //付费直播间鉴权
    rpc liveValidate (LiveValidateReq) returns (LiveValidateResp);
    //获取票务id列表
    rpc getMyTicketIds(GetMyTicketIdsReq) returns(GetMyTicketIdsResp);
    //赠票（奖励、后台）
    rpc sendTicket (SendTicketReq) returns (SendTicketResp);
}

message CreatePayLiveRoomReq {
    // 课程场次id
    int64 epid = 1 [(gogoproto.moretags) = 'json:"epid" form:"epid" validate:"required"'];
    // face
    string face = 2 [(gogoproto.moretags) = 'json:"face" form:"face" validate:"required"'];
    // 标题
    string title = 3 [(gogoproto.moretags) = 'json:"title" form:"title" validate:"required"'];
    // 房间id
    int64 room_id = 4 [(gogoproto.moretags) = 'json:"room_id" form:"room_id" validate:"required"'];
    // 开始时间,秒级时间戳
    int64 start_time = 5 [(gogoproto.moretags) = 'json:"start_time" form:"start_time" validate:"required"'];
    // 结束时间,秒级时间戳
    int64 end_time = 6 [(gogoproto.moretags) = 'json:"end_time" form:"end_time" validate:"required"'];
    // 跳转链接
    string link_url = 7 [(gogoproto.moretags) = 'json:"link_url" form:"link_url" validate:"required"'];
    // ip限制，0不限制，1仅限大陆，2仅限港澳台，3大陆+港澳台
    int64 ip_limit = 8 [(gogoproto.moretags) = 'json:"ip_limit" form:"ip_limit"'];
}

message CreatePayLiveRoomResp {
    // 付费直播id
    int64 pay_live_id = 1 [(gogoproto.jsontag) = "pay_live_id", json_name = "pay_live_id"];
}

message UpdatePayLiveRoomReq {
    // 付费直播id
    int64 pay_live_id = 1 [(gogoproto.moretags) = 'json:"pay_live_id" form:"pay_live_id" validate:"required"'];
    // 课程场次id
    int64 epid = 2 [(gogoproto.moretags) = 'json:"epid" form:"epid" validate:"required"'];
    // face
    string face = 3 [(gogoproto.moretags) = 'json:"face" form:"face" validate:"required"'];
    // 标题
    string title = 4 [(gogoproto.moretags) = 'json:"title" form:"title" validate:"required"'];
    // 房间id
    int64 room_id = 5 [(gogoproto.moretags) = 'json:"room_id" form:"room_id" validate:"required"'];
    // 开始时间,秒级时间戳
    int64 start_time = 6 [(gogoproto.moretags) = 'json:"start_time" form:"start_time" validate:"required"'];
    // 结束时间,秒级时间戳
    int64 end_time = 7 [(gogoproto.moretags) = 'json:"end_time" form:"end_time" validate:"required"'];
    // 跳转链接
    string link_url = 8 [(gogoproto.moretags) = 'json:"link_url" form:"link_url" validate:"required"'];
    // ip限制，0不限制，1仅限大陆，2仅限港澳台，3大陆+港澳台
    int64 ip_limit = 9 [(gogoproto.moretags) = 'json:"ip_limit" form:"ip_limit"'];
}

message UpdatePayLiveRoomResp {
    // 付费直播id
    int64 pay_live_id = 1 [(gogoproto.jsontag) = "pay_live_id", json_name = "pay_live_id"];
}

message UpdatePayLiveStatusReq {
    // 付费直播id
    int64 pay_live_id = 1 [(gogoproto.moretags) = 'json:"pay_live_id" form:"pay_live_id" validate:"required"'];
    // 付费直播状态,0下线，1上线
    int64 status = 2 [(gogoproto.moretags) = 'json:"status" form:"status"'];
}

message UpdatePayLiveStatusResp {

}

message CheckPayLiveRoomReq {
    // 房间id
    int64 room_id = 1 [(gogoproto.moretags) = 'json:"room_id" form:"room_id" validate:"required"'];
    // 开始时间,秒级时间戳
    int64 start_time = 2 [(gogoproto.moretags) = 'json:"start_time" form:"start_time" validate:"required"'];
    // 结束时间,秒级时间戳
    int64 end_time = 3 [(gogoproto.moretags) = 'json:"end_time" form:"end_time" validate:"required"'];
}
message CheckPayLiveRoomResp {
    // 直播状态，-1非付费直播间，1课堂直播
    int64 status = 1 [(gogoproto.moretags) = 'json:"status"'];
}

message LiveValidateReq {
    // uid
    int64 uid = 1 [(gogoproto.moretags) = 'json:"uid" form:"uid"'];
    // 房间id
    int64 room_id = 2 [(gogoproto.moretags) = 'json:"room_id" form:"room_id"'];
    // ip
    string ip = 3 [(gogoproto.moretags) = 'json:"ip" form:"ip"'];
}

message LiveValidateResp {
    int64 code = 1 [(gogoproto.moretags) = 'json:"code" form:"code"'];
    string msg = 2 [(gogoproto.moretags) = 'json:"msg" form:"msg"'];
    LiveValidateData data = 3 [(gogoproto.moretags) = 'json:"data" form:"data"'];
}

message LiveValidateData {
    // 鉴权是否通过:0未通过，1通过
    int64 permission = 1 [(gogoproto.moretags) = 'json:"permission" form:"permission"'];
    // 封面图片
    string pic = 2 [(gogoproto.moretags) = 'json:"pic" form:"pic"'];
}

message GetMyTicketIdsReq {
    // uid
    int64 uid = 1 [(gogoproto.moretags) = 'json:"uid" validate:"required"'];
}

message GetMyTicketIdsResp {
    // 票务id列表
    repeated int64 ticket_ids = 1 [(gogoproto.moretags) = 'json:"ticket_ids"'];
}

message ExtendPayLiveRoomValidityReq {
    // 付费直播id
    int64 pay_live_id = 1 [(gogoproto.moretags) = 'json:"pay_live_id" form:"pay_live_id" validate:"required"'];
    // 课程场次id
    int64 epid = 2 [(gogoproto.moretags) = 'json:"epid" form:"epid" validate:"required"'];
    // 结束时间,秒级时间戳
    int64 end_time = 3 [(gogoproto.moretags) = 'json:"end_time" form:"end_time" validate:"required"'];
}

message ExtendPayLiveRoomValidityResp {

}

message GetInfoByPayLiveIdReq{
    // 付费直播id
    int64 pay_live_id = 1 [(gogoproto.moretags) = 'json:"pay_live_id" form:"pay_live_id" validate:"required"'];
}

message GetInfoByPayLiveIdResp{
    // 付费直播id
    int64 pay_live_id = 1 [(gogoproto.moretags) = 'json:"pay_live_id" form:"pay_live_id" validate:"required"'];
    // 状态：1上线，0下线
    int64 status = 2 [(gogoproto.moretags) = 'json:"status" form:"status" validate:"required"'];
    // 标题
    string title = 3 [(gogoproto.moretags) = 'json:"title" form:"title" validate:"required"'];
    // 房间id
    int64 room_id = 4 [(gogoproto.moretags) = 'json:"room_id" form:"room_id" validate:"required"'];
    // 跳转链接
    string link_url = 5 [(gogoproto.moretags) = 'json:"link_url" form:"link_url" validate:"required"'];
    // ip限制，0不限制，1仅限大陆，2仅限港澳台，3大陆+港澳台
    int64 ip_limit = 6 [(gogoproto.moretags) = 'json:"ip_limit" form:"ip_limit"'];
    // 开始时间,秒级时间戳
    int64 start_time = 7 [(gogoproto.moretags) = 'json:"start_time" form:"start_time" validate:"required"'];
    // 结束时间,秒级时间戳
    int64 end_time = 8 [(gogoproto.moretags) = 'json:"end_time" form:"end_time" validate:"required"'];
    // 门票信息
    repeated TicketInfo ticket_info = 9 [(gogoproto.moretags) = 'json:"ticket_info" form:"ticket_info" validate:"required"'];
}
message TicketInfo {
    // 门票id
    int64 ticket_id = 1 [(gogoproto.moretags) = 'json:"ticket_id" form:"ticket_id" validate:"required"'];
    // 门票名称
    string title = 2 [(gogoproto.moretags) = 'json:"title" form:"title" validate:"required"'];
    // 门票价格
    int64 price = 3 [(gogoproto.moretags) = 'json:"price" form:"price" validate:"required"'];
    // ip限制，0不限制，1仅限大陆，2仅限港澳台，3大陆+港澳台
    int64 ip_limit = 4 [(gogoproto.moretags) = 'json:"ip_limit" form:"ip_limit"'];
    // 开始售票时间
    int64 start_time = 5 [(gogoproto.moretags) = 'json:"start_time" form:"start_time" validate:"required"'];
    // 结束售票时间
    int64 end_time = 6 [(gogoproto.moretags) = 'json:"end_time" form:"end_time" validate:"required"'];
    // 演唱会开始时间
    int64 show_start_time = 7 [(gogoproto.moretags) = 'json:"show_start_time" form:"show_start_time" validate:"required"'];
    // 演唱会结束时间
    int64 show_end_time = 8 [(gogoproto.moretags) = 'json:"show_end_time" form:"show_end_time" validate:"required"'];
}
message SendTicketReq {
    // uid
    repeated int64 uids = 1 [(gogoproto.moretags) = 'json:"uids" validate:"max=50,required"'];
    // 票务id
    int64 goods_id = 2 [(gogoproto.moretags) = 'json:"goods_id" validate:"required"'];
    // 申请人
    string apply_name = 3 [(gogoproto.moretags) = 'json:"apply_name" validate:"required"'];
    // 赠票理由
    string reason = 4 [(gogoproto.moretags) = 'json:"reason" validate:"required"'];
    // 是否发送私信,0不发送，1发送
    int64 send_message = 5 [(gogoproto.moretags) = 'json:"send_message"'];
    // 幂等字段
    string msg_id = 6 [(gogoproto.moretags) = 'json:"msg_id" validate:"required"'];
}

message SendTicketResp {
    //0:成功 1:失败不重试 2:失败重试
    int64 status = 1 [(gogoproto.jsontag) = "status"];

    //赠送结果详情
    Detail detail = 2 [(gogoproto.jsontag) = "detail"];

    message Detail {
        //赠送成功用户
        repeated int64 success = 1 [(gogoproto.jsontag) = "success"];
        //赠送失败用户（不含他人赠送的，多送没有意义，使用效果一样）
        repeated int64 fail = 2 [(gogoproto.jsontag) = "fail"];
        //其他用户已经赠送过的用户
        repeated int64 user_give = 3 [(gogoproto.jsontag) = "user_give"];
        //官方已经赠送过的用户
        repeated int64 official_give = 4 [(gogoproto.jsontag) = "official_give"];
    }
}