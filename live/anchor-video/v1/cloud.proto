syntax = "proto3";

package live.live_tool.anchor_video.v1;

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/anchor.video.v1";
option java_package = "com.bapis.live.anchor.video.v1";
option java_multiple_files = true;

option (wdcli.appid) = "live.live-tool.anchor-video";

// 视频云接口
service Cloud {
    // 通知视频云合成回放
    rpc CompositeVideo (CompositeAnchorVideoReq) returns (CompositeAnchorVideoResp);
    // 视频云元数据回调接口
    rpc UpdateCompositeRecordStatus (UpdateCompositeRecordStatusReq) returns (UpdateCompositeRecordStatusResp);
}

message CompositeAnchorVideoReq {
    // 主播uid
    int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" json:"uid" validate:"required"'];
    // 回放记录id
    int64 record_id = 2 [(gogoproto.moretags) = 'form:"record_id" json:"record_id" validate:"required"'];
    // live_key
    string live_key = 3 [(gogoproto.moretags) = 'form:"live_key" json:"live_key"'];
    // 是否分片
    int64 is_subsection = 4 [(gogoproto.moretags) = 'form:"is_subsection" json:"is_subsection"'];
    // 是否生成音频
    int64 is_audio = 5 [(gogoproto.moretags) = 'form:"is_audio" json:"is_audio"'];
    // boss bucket名称
    string boss_bucket = 6 [(gogoproto.moretags) = 'form:"boss_bucket" json:"boss_bucket"'];
}

message CompositeAnchorVideoResp {
    // 回放场次
    string live_key = 1 [(gogoproto.jsontag) = "live_key,omitempty"];
    // 预估截至时间 时间戳
    int64 estimated_time = 2 [(gogoproto.jsontag) = "estimated_time,omitempty"];
    // 返回状态码
    int64 code = 3 [(gogoproto.jsontag) = "code,omitempty"];
    // 失败原因报错
    string msg = 4 [(gogoproto.jsontag) = "msg,omitempty"];
}

message UpdateCompositeRecordStatusReq {
    // 回放场次
    string live_key = 1 [(gogoproto.moretags) = 'form:"live_key" json:"live_key" validate:"required"'];
    // 用户id 必填
    int64 uid = 2 [(gogoproto.moretags) = 'form:"uid" json:"uid" validate:"required"'];
    // 状态码
    int64 code = 3 [(gogoproto.moretags) = 'form:"code" json:"code"'];
    // 失败原因
    string msg = 4 [(gogoproto.moretags) = 'form:"msg" json:"msg"'];
    // 合并成功后的文件名称
    string filename = 5 [(gogoproto.moretags) = 'form:"filename" json:"filename"'];
    // 分片表回放纪录id
    int64 record_id = 6 [(gogoproto.moretags) = 'form:"record_id" json:"record_id"'];
    // 是否分片
    int64 is_subsection = 7 [(gogoproto.moretags) = 'form:"is_subsection" json:"is_subsection"'];
    // cid
    int64 cid = 8 [(gogoproto.moretags) = 'form:"cid" json:"cid"'];
    // 音频地址
    string audio_url = 9 [(gogoproto.moretags) = 'form:"audio_url" json:"audio_url"'];
}

message UpdateCompositeRecordStatusResp {
    // 返回状态码
    int64 code = 1 [(gogoproto.jsontag) = "code,omitempty"];
}