syntax = "proto3";
package live.reward;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/live.reward.v1;v1";
option java_package = "com.bapis.live.live.reward.v1";
option java_multiple_files = true;

option (wdcli.appid) = "live.reward";

service rewardConfig {
    // 发送打包奖励
    rpc sendReward (SendRewardReq) returns (SendRewardResp);
    // 获取奖励打包配置数据
    rpc rewardPackageConf (RewardPackageConfReq) returns (RewardPackageConfResp);
    // 获取奖励配置数据
    rpc rewardConf (RewardConfReq) returns (RewardConfResp);
}

// 获取奖励配置数据
message RewardConfReq{
    // 活动奖励id
    int64 reward_id = 1 [(gogoproto.jsontag) = 'reward_id'];
}
message RewardConfResp{
    map<string,RewardConfResp_List> reward_list = 1 [(gogoproto.jsontag) = 'reward_list'];
    message RewardConfResp_List {
        repeated RewardConfResp_Item reward_item = 1 [(gogoproto.jsontag) = 'reward_item'];
    }
    message RewardConfResp_Item {
        // 打包ID
        string package_id = 1 [(gogoproto.jsontag) = 'package_id'];
        // 主播等级
        int64 anchor_level = 2 [(gogoproto.jsontag) = 'anchor_level'];
        // 用户等级
        int64 user_level = 3 [(gogoproto.jsontag) = 'user_level'];
        // 任务等级
        int64 task_level = 4 [(gogoproto.jsontag) = 'task_level'];
        // 主播开始
        int64 anchor_start = 5 [(gogoproto.jsontag) = 'anchor_start'];
        // 主播结束
        int64 anchor_end = 6 [(gogoproto.jsontag) = 'anchor_end'];
        // 用户开始
        int64 user_start = 7 [(gogoproto.jsontag) = 'user_start'];
        // 用户结束
        int64 user_end = 8 [(gogoproto.jsontag) = 'user_end'];
        // 奖品图片地址
        string front_award_img = 10 [(gogoproto.jsontag) = 'front_award_img'];
        // 奖品名称
        string front_award_name = 11 [(gogoproto.jsontag) = 'front_award_name'];
        // 奖品描述
        string front_award_desc = 12 [(gogoproto.jsontag) = 'front_award_desc'];
        // 奖品数量 0表示此类型奖品无数量
        int64 award_num = 13 [(gogoproto.jsontag) = 'award_num'];
        // 奖励类型
        int64 award_type = 14 [(gogoproto.jsontag) = 'award_type'];
        // 奖励ID
        int64 award_id = 15 [(gogoproto.jsontag) = 'award_id'];
    }
}

// 获取奖励打包配置数据
message RewardPackageConfReq{
    // 奖励打包id
    repeated string package_ids = 1 [(gogoproto.jsontag) = 'package_ids'];
}
message RewardPackageConfResp{
    map<string,RewardPackageConfResp_List> package_map = 1 [(gogoproto.jsontag) = 'package_map'];
    message RewardPackageConfResp_List {
        repeated RewardPackageConfResp_Item item_list = 1 [(gogoproto.jsontag) = 'item_list'];
    }
    message RewardPackageConfResp_Item{
        // 奖品图片地址
        string front_award_img = 1 [(gogoproto.jsontag) = 'front_award_img'];
        // 奖品名称
        string front_award_name = 2 [(gogoproto.jsontag) = 'front_award_name'];
        // 奖品描述
        string front_award_desc = 3 [(gogoproto.jsontag) = 'front_award_desc'];
        // 奖品类型 详见 app/service/live-reward/internal/dao/reward/award/model/award_type.go
        int64 award_type = 4 [(gogoproto.jsontag) = 'award_type'];
        // 奖励数量
        int64 award_num = 5 [(gogoproto.jsontag) = 'award_num'];
        // 奖励数值ID
        int64 award_id = 6 [(gogoproto.jsontag) = 'award_id'];
    }
}

// 发送打包奖励
message SendRewardReq{
    // 上游来源
    int64 source = 1 [(gogoproto.jsontag) = "source", json_name = "source"];
    // 消息id
    string msg_id = 2 [(gogoproto.jsontag) = "msg_id", json_name = "msg_id"];
    // 奖励打包id
    string package_id = 3 [(gogoproto.jsontag) = "package_id", json_name = "package_id"];
    // 发奖用户
    repeated int64 uids = 4 [(gogoproto.jsontag) = "uids", json_name = "uids"];
    // 发奖时间
    int64 msg_time = 5 [(gogoproto.jsontag) = "msg_time", json_name = "msg_time"];
    // 额外参数
    string extra_data = 6 [(gogoproto.jsontag) = "extra_data", json_name = "extra_data"];
    // 是否申领奖励 0否 1是
    int64 is_apply = 7 [(gogoproto.jsontag) = "is_apply", json_name = "is_apply"];
    // 业务类型
    string business_type = 8 [(gogoproto.jsontag) = "business_type", json_name = "business_type"];
    // 业务ID
    string business_id = 9 [(gogoproto.jsontag) = "business_id", json_name = "business_id"];
}
message SendRewardResp{
    // 发送状态 0失败 1成功
    int64 status = 1 [(gogoproto.jsontag) = "status", json_name = "status"];
    // 失败原因
    string message = 2 [(gogoproto.jsontag) = "message", json_name = "message"];
}