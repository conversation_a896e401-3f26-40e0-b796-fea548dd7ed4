syntax = "proto3";
package live.order.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/live/order.v2;v2";
option java_package = "com.bapis.live.order.v2";
option java_multiple_files = true;
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "live.order";
option (gogoproto.goproto_getters_all) = true;
service mainStream {
    // 获取订单列表
    rpc GetMainOrderList (GetMainOrderListReq) returns (GetMainOrderListResp);
}
message GetMainOrderListReq {
    int64 uid = 1 [(gogoproto.jsontag) = "uid"];
    int64 page = 2 [(gogoproto.jsontag) = "page"];
    int64 page_size = 3 [(gogoproto.jsontag) = "page_size"];
    int64 status = 4 [(gogoproto.jsontag) = "status"];
}

message GetMainOrderListResp {
    repeated OrderInfo list = 1 [(gogoproto.jsontag) = "list"];
    // 翻页信息
    PageInfo page = 2 [(gogoproto.jsontag) = "page"];

    message PageInfo {
        int64 ps = 1  [(gogoproto.jsontag) = "ps"];
        int64 pn = 2  [(gogoproto.jsontag) = "pn"];
        int64 total = 3  [(gogoproto.jsontag) = "total"];
    }

    message OrderInfo {
        // 增值订单id
        string order_id = 1 [(gogoproto.jsontag) = "order_id"];
        // 订单状态
        int64 state = 2 [(gogoproto.jsontag) = "state"];
        // 剩余支付时间
        int64 remain_pay_time = 3 [(gogoproto.jsontag) = "remain_pay_time"];
        // 商品数量
        int64 buy_num = 4 [(gogoproto.jsontag) = "buy_num"];
        // 支付金额
        int64 paid_amount = 5 [(gogoproto.jsontag) = "paid_amount"];
        // 优惠金额
        int64 allowance = 6 [(gogoproto.jsontag) = "allowance"];
        // 支付时间
        string pay_time = 7 [(gogoproto.jsontag) = "pay_time"];
        // 商品信息
        Item item = 8 [(gogoproto.jsontag) = "item"];
        // 直播订单号
        string live_order_id = 9 [(gogoproto.jsontag) = "live_order_id"];
        // 时间戳
        int64 ts = 10  [(gogoproto.jsontag) = "ts"];

        message Item {
            // 业务方商品id
            int64 item_id = 1 [(gogoproto.jsontag) = "item_id"];
            // 商品名称
            string name = 2 [(gogoproto.jsontag) = "name"];
            // 商品图片
            string image_cover = 3 [(gogoproto.jsontag) = "image_cover"];
        }
    }
}