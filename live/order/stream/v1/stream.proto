syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

package live.order.stream.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/order.stream.v1;v1";
option (gogoproto.goproto_getters_all) = false;
option (wdcli.appid) =
    "live.live-income-base.order-stream";  // 注意请填写能够被discovery服务发现的appid
option java_package = "com.bapis.live.order.stream.v1";
option java_multiple_files = true;

service stream {
  // 签约流水接口
  rpc getContractStream(GetContractStreamReq) returns (GetContractStreamResp);
  // 签约操作日志
  rpc getContractLogStream(GetContractLogStreamReq)
      returns (GetContractLogStreamResp);
  // 签约扣款日志
  rpc getContractExecuteLogStream(GetContractExecuteLogStreamReq)
      returns (GetContractExecuteLogStreamResp);
  // 获取某天过期的签约
  rpc getExpireContract(GetExpireContractReq) returns (GetExpireContractResp);
  // 分页查询订单列表
  rpc getOrderStream(GetOrderStreamReq) returns (GetOrderStreamResp);
  // 获取收益流水列表
  rpc getAnchorStream(GetAnchorStreamReq) returns (GetAnchorStreamResp);
  // 收益统计
  rpc getAnchorStatistics(GetAnchorStatisticsReq)
      returns (GetAnchorStatisticsResp);
  // 查询一段时间内的订单记录
  rpc GetOrderStreamByTime(GetOrderStreamByTimeReq)
      returns (GetOrderStreamByTimeResp);
  //获取主播收益统计
  //    rpc getAnchorStatisticsV2 (GetAnchorStatisticsV2Req) returns
  //    (GetAnchorStatisticsV2Resp);
  // 查询一段时间内的订单记录
  rpc GetOrderStreamByTimeV2(GetOrderStreamByTimeV2Req)
      returns (GetOrderStreamByTimeV2Resp);
  // 未成年人充值
  rpc GetChildrenRechargeStream(GetChildrenRechargeStreamReq)
      returns (GetChildrenRechargeStreamResp);
  // 下载未成年人充值流水文件
  rpc DownloadChildrenRechargeStream(DownloadChildrenRechargeStreamReq)
      returns (DownloadChildrenRechargeStreamResp);
  // 获取未成年人充值文件
  rpc GetChildrenRechargeStreamFile(GetChildrenRechargeStreamFileReq)
      returns (GetChildrenRechargeStreamFileResp);
  //  用户消费流水  给礼物使用
  rpc GetOrderStreamByGift(GetUserOrderStreamReq)
      returns (GetUserOrderStreamResp);
  //  用户消费流水(包含退款流水)  给礼物使用
  rpc GetOrderStreamByGiftV2(GetUserOrderStreamReq)
      returns (GetUserOrderStreamResp);
  // 获取一段时间内用户订单记录
  rpc GetOrderStreamByTimeV3(GetOrderStreamByTimeV3Req)
      returns (GetOrderStreamByTimeV3Resp);
  // 获取订单详情记录
  rpc GetOrderDetailInfo(GetOrderDetailInfoReq)
      returns (GetOrderDetailInfoResp);
  // 根据订单ID获取指定字段
  rpc GetOrderFieldsById(GetOrderFieldsByIdReq)
      returns (GetOrderFieldsByIdResp);
  // 用户消费流水(包含订单状态描述, 如 已退款)  给游戏道具商品使用 用户纬度
  rpc GetOrderStreamByGamegiftByUid(GetUserOrderGamegiftStreamReq)
      returns (GetUserOrderGamegiftStreamResp);
  // 同步历史订单
  rpc SyncOld(SyncOldReq) returns (SyncOldResp);
}

message SyncOldReq{
    int64 id = 1 [(gogoproto.moretags) = 'form:"id" validate:"required"'];
    int64 uid = 2 [(gogoproto.moretags) = 'form:"uid"'];
}
message SyncOldResp {
}

message GetUserOrderGamegiftStreamReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
  // 上一页返回的next_id 即上页的最后一条记录ID
  int64 next_id = 2 [(gogoproto.moretags) = 'form:"next_id"'];
  // 分页数量
  int64 page_size = 3
      [(gogoproto.moretags) = 'form:"page_size" validate:"required"'];
}
message GetUserOrderGamegiftStreamResp {
  // 列表数据
  repeated OrderInfo list = 1 [(gogoproto.jsontag) = "list"];
  // 下一次请求参数中的next_id
  int64 next_id = 2 [(gogoproto.jsontag) = "next_id"];

  message OrderInfo {
    // 商品名称
    string goods_name = 1 [(gogoproto.jsontag) = 'goods_name'];
    // 订单创建时间
    string pay_time = 2 [(gogoproto.jsontag) = "pay_time"];
    // 订单状态描述
    string status = 3 [(gogoproto.jsontag) = "status_msg"];
  }
}
message OrderInfoRecord {
  string order_id = 1 [(gogoproto.jsontag) = "order_id"];
  int64 parent_goods_cate = 2 [(gogoproto.jsontag) = "parent_goods_cate"];
  int64 goods_cate = 3 [(gogoproto.jsontag) = "goods_cate"];
  int64 total_price = 4 [(gogoproto.jsontag) = "total_price"];
  int64 currency_type = 5 [(gogoproto.jsontag) = "currency_type"];
  int64 type = 6 [(gogoproto.jsontag) = "type"];
  int64 status = 7 [(gogoproto.jsontag) = "status"];
  int64 refund_status = 8 [(gogoproto.jsontag) = "refund_status"];
  int64 uid = 9 [(gogoproto.jsontag) = "uid"];
  int64 ruid = 10 [(gogoproto.jsontag) = "ruid"];
  int64 context_type = 11 [(gogoproto.jsontag) = "context_type"];
  string context_id = 12 [(gogoproto.jsontag) = "context_id"];
  int64 goods_id = 13 [(gogoproto.jsontag) = "goods_id"];
  string goods_detail = 14 [(gogoproto.jsontag) = "goods_detail"];
  int64 biz_id = 15 [(gogoproto.jsontag) = "biz_id"];
  int64 gold_couple = 16 [(gogoproto.jsontag) = "gold_couple"];
  int64 create_time = 17 [(gogoproto.jsontag) = "create_time"];
  string mtime = 18 [(gogoproto.jsontag) = "mtime"];
  string extra_data = 19 [(gogoproto.jsontag) = "extra_data"];
  int64 settlement_price = 20 [(gogoproto.jsontag) = "settlement_price"];
  int64 order_real_price = 21 [(gogoproto.jsontag) = "order_real_price"];
}
message OrderPayInfo {
  int64 pay_id = 1 [(gogoproto.jsontag) = "pay_id"];
  int64 price = 2 [(gogoproto.jsontag) = "price"];
  int64 currency_type = 3 [(gogoproto.jsontag) = "currency_type"];
  int64 status = 4 [(gogoproto.jsontag) = "status"];
  string pay_channel = 5 [(gogoproto.jsontag) = "pay_channel"];
  string pay_detail = 6 [(gogoproto.jsontag) = "pay_detail"];
  int64 pay_time = 7 [(gogoproto.jsontag) = "pay_time"];
}
message GetOrderDetailInfoReq {
  // 订单号
  string order_id = 1 [(gogoproto.moretags) = 'form:"order_id"'];
}
message GetOrderFieldsByIdReq {
  // 订单号
  string order_id = 1 [(gogoproto.moretags) = 'form:"order_id"'];
  repeated string fields = 2 [(gogoproto.moretags) = 'form:"fields"'];
  //订单创建时间 "2022-07-18 11:03:09"
  string ctime = 3 [(gogoproto.moretags) = 'form:"ctime"'];
}
message GetOrderDetailInfoResp {
  //订单详情
  OrderInfoRecord order_info = 1 [(gogoproto.jsontag) = "order_info"];
  //支付列表
  repeated OrderPayInfo pay_records = 2 [(gogoproto.jsontag) = "pay_records"];
}

message GetOrderFieldsByIdResp {
  // 对应字段的kv 序列化后的json
  string fields = 1 [(gogoproto.jsontag) = 'form:"fields"'];
}

message GetUserOrderStreamReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid", validate:"required"'];
  // 上一页返回的next_id 即上页的最后一条记录ID
  int64 next_id = 2 [(gogoproto.moretags) = 'form:"next_id"'];
  // 分页数量
  int64 page_size = 3
      [(gogoproto.moretags) = 'form:"page_size", validate:"required"'];
}

message GetUserOrderStreamResp {
  // 列表数据
  repeated OrderInfo list = 1 [(gogoproto.jsontag) = "list"];
  // 下一次请求参数中的next_id
  int64 next_id = 2 [(gogoproto.jsontag) = "next_id"];

  message OrderInfo {
    // id
    int64 id = 1 [(gogoproto.jsontag) = 'id'];
    // 房间id
    int64 room_id = 2 [(gogoproto.jsontag) = 'room_id'];
    // 是否为大航海
    int64 is_guard = 3 [(gogoproto.jsontag) = 'is_guard'];
    // 主播uid
    int64 ruid = 4 [(gogoproto.jsontag) = 'ruid'];
    // 主播昵称
    string anchor_name = 5 [(gogoproto.jsontag) = 'anchor_name'];
    // 商品id
    int64 goods_id = 6 [(gogoproto.jsontag) = 'goods_id'];
    // 商品名称
    string goods_name = 7 [(gogoproto.jsontag) = 'goods_name'];
    // 商品图片
    string goods_img = 8 [(gogoproto.jsontag) = 'goods_img'];
    // 商品数量
    int64 goods_num = 9 [(gogoproto.jsontag) = 'goods_img'];
    // 原价
    int64 coin = 10 [(gogoproto.jsontag) = 'coin'];
    // 支付价
    int64 pay_coin = 11 [(gogoproto.jsontag) = 'pay_coin'];
    // 业务id
    int64 biz_id = 12 [(gogoproto.jsontag) = "biz_id"];
    // 订单创建时间
    int64 create_time = 13 [(gogoproto.jsontag) = "create_time"];
    // 单位值
    string gift_num_unit = 14 [(gogoproto.jsontag) = "gift_num_unit"];
    // 订单状态描述
    string status_msg = 15 [(gogoproto.jsontag) = "status_msg"];
    // 语聊房相关信息区分用户流水来源
    ChatRoom chat_room = 16 [(gogoproto.jsontag) = "chat_room"];
    // 退款金额单位金瓜子
    int64 refund_price =17  [(gogoproto.jsontag) = "refund_price", json_name = "refund_price"];
    // 最后修改时间，单位s
    int64 mtime =18 [(gogoproto.jsontag) = "mtime", json_name = "mtime"];
  }
}

// 签约流水请求
message GetContractStreamReq {
  // 用户id
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid"'];
  // 签约状态 0 未签约 1 签约中 2 已解约
  int64 status = 2 [(gogoproto.moretags) = "status"];
  // 当前页数
  int64 page = 3 [(gogoproto.moretags) = 'form:"page"'];
  // 显示页数
  int64 page_size = 4 [(gogoproto.moretags) = 'form:"page_size"'];
}

// 签约流水响应
message GetContractStreamResp {
  // 列表数据
  repeated ContractInfo list = 1 [(gogoproto.jsontag) = "list"];
  // 总条数
  int64 total = 2 [(gogoproto.jsontag) = "total"];
  message ContractInfo {
    // 用户id
    int64 uid = 1 [(gogoproto.jsontag) = 'uid'];
    // 用户昵称
    string uname = 2 [(gogoproto.jsontag) = 'uname'];
    // 支付订单号
    string order_id = 3 [(gogoproto.jsontag) = 'order_id'];
    // 直播签约id
    string contract_id = 4 [(gogoproto.jsontag) = 'contract_id'];
    // 支付平台签约id
    string out_contract_id = 5 [(gogoproto.jsontag) = 'out_contract_id'];
    // 签约时间
    string signed_time = 6 [(gogoproto.jsontag) = 'signed_time'];
    // 解约时间
    string teminated_time = 7 [(gogoproto.jsontag) = 'teminated_time'];
    // 签约价格
    string signed_price = 8 [(gogoproto.jsontag) = 'signed_price'];
    // 支付渠道
    string pay_channel = 9 [(gogoproto.jsontag) = 'pay_channel'];
    // 扣费周期
    string period = 10 [(gogoproto.jsontag) = 'period'];
    // 下次扣款时间
    string next_execute_time = 11 [(gogoproto.jsontag) = 'next_execute_time'];
    // 签约状态 0 未签约 1 签约中 2 解约
    int64 status = 12 [(gogoproto.jsontag) = 'status'];
    // 主播uid
    int64 ruid = 13 [(gogoproto.jsontag) = 'ruid'];
    // 主播昵称
    string anchor_name = 14 [(gogoproto.jsontag) = 'anchor_name'];
    // 商品id
    int64 goods_id = 15 [(gogoproto.jsontag) = 'goods_id'];
    // 商品名称
    string goods_name = 16 [(gogoproto.jsontag) = 'goods_name'];
  }
}

// 获取某日过期的签约请求
message GetExpireContractReq {
  // 查询游标，第一次查询不用传。
  repeated int64 cursor = 1 [(gogoproto.moretags) = 'form:"cursor"'];
  // 执行扣费时间Unix
  int64 execute_time_unix = 2 [(gogoproto.moretags) = 'form:"execute_time_unix"'];
  // 指定的商品ID
  repeated int64 goods_ids = 3 [(gogoproto.moretags) = 'form:"goods_ids"'];
  // 指定的支付途径
  int64 pay_chanel = 4 [(gogoproto.moretags) = 'form:"pay_chanel"'];
}

// 获取某日过期的签约响应
message GetExpireContractResp {
  // 查询游标
  repeated int64 cursor = 1 [(gogoproto.jsontag) = "cursor"];
  // 是否还有数据未返回
  repeated bool has_more = 2 [(gogoproto.jsontag) = "has_more"];
  // 列表数据
  repeated ContractInfo list = 3 [(gogoproto.jsontag) = "list"];
  message ContractInfo {
    // 用户id
    int64 uid = 1 [(gogoproto.jsontag) = 'uid'];
    // 支付订单号
    string order_id = 3 [(gogoproto.jsontag) = 'order_id'];
    // 直播签约id
    string contract_id = 4 [(gogoproto.jsontag) = 'contract_id'];
    // 支付平台签约id
    string out_contract_id = 5 [(gogoproto.jsontag) = 'out_contract_id'];
    // 签约时间
    int64 signed_time = 6 [(gogoproto.jsontag) = 'signed_time'];
    // 解约时间
    int64 teminated_time = 7 [(gogoproto.jsontag) = 'teminated_time'];
    // 签约价格
    int64 signed_price = 8 [(gogoproto.jsontag) = 'signed_price'];
    // 支付渠道
    int32 pay_channel = 9 [(gogoproto.jsontag) = 'pay_channel'];
    // 扣费周期
    int64 period = 10 [(gogoproto.jsontag) = 'period'];
    // 下次扣款时间
    int64 next_execute_time = 11 [(gogoproto.jsontag) = 'next_execute_time'];
    // 签约状态 0 未签约 1 签约中 2 解约
    int64 status = 12 [(gogoproto.jsontag) = 'status'];
    // 主播uid
    int64 ruid = 13 [(gogoproto.jsontag) = 'ruid'];
    // 商品id
    int64 goods_id = 15 [(gogoproto.jsontag) = 'goods_id'];
  }
}

// 查询签约操作日志请求
message GetContractLogStreamReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid", validate:"required"'];
  // 主播uid
  int64 ruid = 2 [(gogoproto.moretags) = 'form:"ruid", validate:"required"'];
  // 商品id
  int64 goods_id = 3
      [(gogoproto.moretags) = 'form:"goods_id", validate:"required"'];
  // 当前页数
  int64 page = 4 [(gogoproto.moretags) = 'form:"page"'];
  // 显示页数
  int64 page_size = 5 [(gogoproto.moretags) = 'form:"page_size"'];
}

// 查询签约操作日志响应
message GetContractLogStreamResp {
  repeated ContractLogInfo list = 1 [(gogoproto.jsontag) = 'list'];
  int64 total = 2 [(gogoproto.jsontag) = "total"];
  message ContractLogInfo {
    // 操作 0 发起签约 1 签约成功 2 发起解约 3 解约成功 4 变更扣费时间 5
    // 解约后重新发起签约 6 同步签约状态 7 同步解约状态
    int64 action = 1 [(gogoproto.jsontag) = 'action'];
    // 创建时间
    string ctime = 2 [(gogoproto.jsontag) = 'ctime'];
    // 变更前数据
    string before_data = 3 [(gogoproto.jsontag) = 'before_data'];
    // 变更后数据
    string after_data = 4 [(gogoproto.jsontag) = 'after_data'];
  }
}

// 查询签约扣费日志请求
message GetContractExecuteLogStreamReq {
  // 签约id
  string contract_id = 1
      [(gogoproto.moretags) = 'form:"contract_id", validate:"required"'];
  // 当前页数
  int64 page = 2 [(gogoproto.moretags) = 'form:"page"'];
  // 显示页数
  int64 page_size = 3 [(gogoproto.moretags) = 'form:"page_size"'];
}

// 查询签约扣费日志响应
message GetContractExecuteLogStreamResp {
  repeated ContractExecuteLogInfo list = 1 [(gogoproto.jsontag) = 'list'];
  int64 total = 2 [(gogoproto.jsontag) = "total"];
  message ContractExecuteLogInfo {
    // 签约id
    string contract_id = 1 [(gogoproto.jsontag) = 'contract_id'];
    // 支付平台签约id
    string out_contract_id = 2 [(gogoproto.jsontag) = 'out_contract_id'];
    // 订单号
    string order_id = 3 [(gogoproto.jsontag) = 'order_id'];
    // 扣费金额
    string price = 4 [(gogoproto.jsontag) = 'price'];
    // 扣费周期
    string execute_time = 5 [(gogoproto.jsontag) = 'execute_time'];
    // 扣费状态 0 待续费 1 续费中 2 续费完成 3 续费失败 4 续费解约
    int64 status = 6 [(gogoproto.jsontag) = 'status'];
    // 创建时间
    string ctime = 7 [(gogoproto.jsontag) = 'ctime'];
    // 最后修改时间
    string mtime = 8 [(gogoproto.jsontag) = 'mtime'];
  }
}

// 订单列表请求
message GetOrderStreamReq {
  // 用户id
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid"'];
  // 主播id
  int64 ruid = 2 [(gogoproto.moretags) = 'form:"ruid"'];
  // 页数 从1开始
  int32 page = 3
      [(gogoproto.moretags) = 'form:"page" validate:"required,gt=0"'];
  // 每页展示条数 最大3000条(下载)
  int32 page_size = 4
      [(gogoproto.moretags) = 'form:"page_size" validate:"required,gt=0"'];
  // 子分类id
  int32 cate_id = 5 [(gogoproto.moretags) = 'form:"cate_id"'];
  // 订单状态 1:待支付 2:待发货 3:支付失败 4:发货中 5:发货成功 6:发货失败待退款
  // 7:发货失败不退款 8:退款中 9:退款成功 10:退款取消 11:订单关闭
  // 12:支付失败待检查 13:支付成功待退款14:支付成功退款中 15:支付成功退款成功 16
  // 发货成功退款成功
  int32 status = 6 [(gogoproto.moretags) = 'form:"status"'];
  // 开始时间
  string start_time = 7 [(gogoproto.moretags) = 'form:"start_time"'];
  // 结束时间
  string end_time = 8 [(gogoproto.moretags) = 'form:"end_time"'];
  // 父分类id
  int32 parent_cate = 10 [(gogoproto.moretags) = 'form:"parent_cate"'];
  // 订单号
  string order_id = 11 [(gogoproto.moretags) = 'form:"order_id"'];
  // 支付单号
  string third_order_id = 12 [(gogoproto.moretags) = 'form:"third_order_id"'];
  // 第三方充值订单号
  string third_recharge_order_id = 13
      [(gogoproto.moretags) = 'form:"third_recharge_order_id"'];
  // 商品id
  int64 goods_id = 14 [(gogoproto.moretags) = 'form:"goods_id"'];
}

// 订单列表响应
message GetOrderStreamResp {
  // 总条数
  int32 total = 1 [(gogoproto.jsontag) = 'total'];
  // 订单列表
  repeated OrderInfo order_list = 2 [(gogoproto.jsontag) = 'order_list'];
}

// 订单详情
message OrderInfo {
  // 订单id
  string order_id = 1 [(gogoproto.jsontag) = "order_id"];
  // 订单支付总价 单位人民币
  int64 total_price = 2 [(gogoproto.jsontag) = "total_price"];
  // 货币类型 1:金瓜子 50:b币 60:现金 100:b币与现金混合支付
  int64 currency_type = 3 [(gogoproto.jsontag) = "currency_type"];
  // 业务类型 1:充值 2:消费
  int64 type = 4 [(gogoproto.jsontag) = "type"];
  // 订单状态 1:待支付 2:待发货 3:支付失败 4:发货中 5:发货成功 6:发货失败待退款
  // 7:发货失败不退款 8:退款中 9:退款成功 10:退款取消 11:订单关闭
  // 12:支付失败待检查 13:支付成功待退款 14:支付成功退款中 15:支付成功退款成功
  int32 status = 5 [(gogoproto.jsontag) = "status"];
  // 订单购买时的平台
  string platform = 6 [(gogoproto.jsontag) = "platform"];
  // 客户端版本号
  int64 build = 7 [(gogoproto.jsontag) = "build"];
  // 客户端类型
  string mobile_app = 8 [(gogoproto.jsontag) = "mobile_app"];
  // 主播id
  int64 ruid = 9 [(gogoproto.jsontag) = "ruid"];
  // 场景 1:直播间 2:回放 3:活动页 4 同步数据占用 5 自动续费 6 演播厅 7 道具抽奖
  // 8 公众号 9 个人中心 10 未知 11 瓜子商店 12 友爱社
  int64 context_type = 10 [(gogoproto.jsontag) = "context_type"];
  // 场景id
  string context_id = 11 [(gogoproto.jsontag) = "context_id"];
  // 订单创建时间
  string create_time = 12 [(gogoproto.jsontag) = "create_time"];
  // 订单分类
  int64 parent_cate = 13 [(gogoproto.jsontag) = "parent_cate"];
  // 订单子分类
  int64 goods_cate = 14 [(gogoproto.jsontag) = "goods_cate"];
  // 商品id
  int64 goods_id = 15 [(gogoproto.jsontag) = "goods_id"];
  // 商品名称
  string goods_name = 16 [(gogoproto.jsontag) = "goods_name"];
  // 商品数量
  int64 goods_num = 17 [(gogoproto.jsontag) = "goods_num"];
  // 商品图片 可能为空
  string goods_img = 18 [(gogoproto.jsontag) = "goods_img"];
  // 商品详情 json 不同业务数据可能不同
  string goods_detail = 19 [(gogoproto.jsontag) = "goods_detail"];
  // 商品单价(原价) 单位瓜子
  int64 goods_price = 20 [(gogoproto.jsontag) = "goods_price"];
  // 购买ip
  string ip = 21 [(gogoproto.jsontag) = "ip"];
  // 购买用户uid
  int64 uid = 22 [(gogoproto.jsontag) = "uid"];
  // 用户昵称
  string uname = 23 [(gogoproto.jsontag) = "uname"];
  // 主播昵称
  string anchor_uname = 24 [(gogoproto.jsontag) = "anchor_uname"];
  // 是否结算 -1 无需结算 0 未结算 1 已结算
  int32 is_settlement = 25 [(gogoproto.jsontag) = "is_settlement"];
  // 支付信息列表
  repeated payInfo pay_list = 26 [(gogoproto.jsontag) = "pay_list"];
  message payInfo {
    // 支付单号
    int64 pay_id = 1 [(gogoproto.jsontag) = "pay_id"];
    // 支付平台单号
    string third_order_id = 2 [(gogoproto.jsontag) = "third_order_id"];
    // 货币类型
    int64 currency_type = 3 [(gogoproto.jsontag) = "currency_type"];
    // 支付金额
    string price = 4 [(gogoproto.jsontag) = "price"];
    // 支付时间
    string pay_time = 5 [(gogoproto.jsontag) = "pay_time"];
    // 支付渠道
    string pay_channel = 6 [(gogoproto.jsontag) = "pay_channel"];
    // 支付状态
    int64 pay_status = 7 [(gogoproto.jsontag) = "pay_status"];
  }
  // 补单单号
  string supplement_order_id = 27 [(gogoproto.jsontag) = "supplement_order_id"];
  // 业务id
  int64 biz_id = 28 [(gogoproto.jsontag) = "biz_id"];
  // 展示价格
  string show_price = 29 [(gogoproto.jsontag) = "show_price"];
  // 充值到账
  int64 recharge_num = 30 [(gogoproto.jsontag) = "recharge_num"];
}

//主播礼物流水
message GetAnchorStreamReq {
  //业务类型 1:礼物 2:大航海 3:醒目留言
  int32 biz_type = 1 [
    (gogoproto.jsontag) = "biz_type",
    (gogoproto.moretags) = "form:\"biz_type\" validate:\"required\""
  ];
  //请求类型 1:本场收益 2:近7天收益
  int32 req_type = 2 [
    (gogoproto.jsontag) = "req_type",
    (gogoproto.moretags) = "form:\"req_type\" validate:\"required\""
  ];
  //排序类型: 1:时间 2: 价值 可不传 默认按时间
  int32 sort_type = 3 [
    (gogoproto.jsontag) = "sort_type",
    (gogoproto.moretags) = "form:\"sort_type\""
  ];
  //当前页数 可不传 默认1
  int64 page = 4
      [(gogoproto.jsontag) = "page", (gogoproto.moretags) = "form:\"page\""];
  //主播ruid
  int64 ruid = 5 [
    (gogoproto.jsontag) = "ruid",
    (gogoproto.moretags) = "form:\"ruid\" validate:\"required\""
  ];
  //每页传输多少条数据
  int64 page_size = 6 [
    (gogoproto.jsontag) = "page_size",
    (gogoproto.moretags) = "form:\"page_size\" validate:\"required\""
  ];
}
//主播礼物流水响应
message GetAnchorStreamResp {
  message Ancstreamlist {
    //订单号
    string order_id = 1 [(gogoproto.jsontag) = "order_id"];
    //商品名称
    string goods_name = 2 [(gogoproto.jsontag) = "goods_name"];
    //商品图片
    string goods_img = 3 [(gogoproto.jsontag) = "goods_img"];
    //购买数量
    int64 goods_num = 4 [(gogoproto.jsontag) = "goods_num"];
    //用户昵称
    string uname = 5 [(gogoproto.jsontag) = "uname"];
    //赠送时间
    int64 create_time = 6 [(gogoproto.jsontag) = "create_time"];
    //价格
    int64 price = 7 [(gogoproto.jsontag) = "price"];
    //单位名称
    string unit = 8 [(gogoproto.jsontag) = "unit"];
    //大会员
    int32 userVip = 9 [(gogoproto.jsontag) = "uservip"];
    //醒目留言以人民币为单位
    string rmb_price = 10 [(gogoproto.jsontag) = "rmb_price"];
    //对应单位的图片 金瓜子图片
    string icon = 11 [(gogoproto.jsontag) = "icon"];
    //额外业务字段
    AnchorStreamExtra extra = 12 [(gogoproto.jsontag) = "extra"];
    //神秘人标记
    bool is_mystery = 13 [(gogoproto.jsontag) = "is_mystery"];
  }
  repeated Ancstreamlist list = 1 [(gogoproto.jsontag) = "list"];
  int64 total = 2 [(gogoproto.jsontag) = "total"];
}
//统计主播收益
message GetAnchorStatisticsReq {
  //请求类型 1:本场收益 2:近7天收益
  int32 req_type = 1 [
    (gogoproto.jsontag) = "req_type",
    (gogoproto.moretags) = "form:\"req_type\" validate:\"required\""
  ];
  //业务类型 0:全部 1:礼物 2:大航海 3:醒目留言 可不传 默认全部
  int32 biz_type = 2 [
    (gogoproto.jsontag) = "biz_type",
    (gogoproto.moretags) = "form:\"biz_type\""
  ];
  //主播ruid
  int64 ruid = 5 [
    (gogoproto.jsontag) = "ruid",
    (gogoproto.moretags) = "form:\"ruid\" validate:\"required\""
  ];
}
//统计主播收益响应
message GetAnchorStatisticsResp {
  //统计后金瓜子总数
  string total_price = 1 [(gogoproto.jsontag) = "total_price"];
  // 单位名称 目前是金瓜子
  string unit = 2 [(gogoproto.jsontag) = "unit"];
  // 对应单位的图片 金瓜子图片
  string icon = 3 [(gogoproto.jsontag) = "icon"];
  // 打赏人数
  int64 total_user = 4 [(gogoproto.jsontag) = "total_user"];
}

message ChatRoom {
  // 接受送礼用户类型 0/不传  默认主播 ， 1 麦位用户
  int64 receive_user_type = 1
      [(gogoproto.moretags) = 'form:"receive_user_type"'];
  // 房主的房间id
  int64 room_id = 2 [(gogoproto.moretags) = 'form:"room_id"'];
}

message AnchorStreamExtra {
  SuperChat superchat = 1 [(gogoproto.jsontag) = "superChat"];
  Guard guard = 2 [(gogoproto.jsontag) = "guard"];
  Gift gift = 3 [(gogoproto.jsontag) = "gift"];
  ChatRoom chat_room = 4 [(gogoproto.jsontag) = "chat_room"];
  message Guard {
    //开通大航海状态 1开通 2续费 3自动续费
    int32 renew_status = 1 [(gogoproto.jsontag) = "renew_status"];
  }

  message Gift {
    // biz_id
    int64 biz_id = 1 [(gogoproto.jsontag) = "biz_id"];
    //盲盒道具不为0
    int64 settlement_price = 2 [(gogoproto.jsontag) = "settlement_price"];
  }

  message SuperChat {
    // 留言内容
    string msg = 1 [(gogoproto.jsontag) = "msg"];
    // 大航海等级
    int64 guard_level = 2 [(gogoproto.jsontag) = "guard_level"];
    // 勋章等级
    int64 medal_level = 3 [(gogoproto.jsontag) = "medal_level"];
    // 边框颜色
    int64 medal_color_border = 4 [(gogoproto.jsontag) = "medal_color_border"];
    // 渐变色的起
    int64 medal_color_start = 5 [(gogoproto.jsontag) = "medal_color_start"];
    // 渐变色的止
    int64 medal_color_end = 6 [(gogoproto.jsontag) = "medal_color_end"];
    // 勋章名
    string medal_name = 7 [(gogoproto.jsontag) = "medal_name"];
    // 勋章是否点亮
    int64 is_lighted = 8 [(gogoproto.jsontag) = "is_lighted"];
    //醒目留言昵称颜色
    string uname_color = 9 [(gogoproto.jsontag) = "uname_color"];
  }
}

message GetOrderStreamByTimeReq {
  // 开始时间
  int64 start_time = 1
      [(gogoproto.moretags) = 'form:"start_time" validate:"gt=0,required"'];
  // 结束时间
  int64 end_time = 2
      [(gogoproto.moretags) = 'form:"end_time" validate:"gt=0,required"'];
  // 订单状态
  int64 status = 3 [(gogoproto.moretags) = 'form:"status"'];
  // 商品goods
  repeated int64 goods_ids = 4 [(gogoproto.moretags) = 'form:"goods_ids"'];
  // 页数 从1开始
  int64 page = 5
      [(gogoproto.moretags) = 'form:"page" validate:"required,gt=0"'];
  // 每页展示条数 最大1000条
  int64 page_size = 6
      [(gogoproto.moretags) = 'form:"page_size" validate:"required,gt=0"'];
}

message GetOrderStreamByTimeResp {
  repeated OrderInfo order_list = 1 [(gogoproto.jsontag) = "order_list"];

  message OrderInfo {
    // 购买用户uid
    int64 uid = 1 [(gogoproto.jsontag) = "uid"];
    // 主播id
    int64 ruid = 2 [(gogoproto.jsontag) = "ruid"];
    // 订单id
    string order_id = 3 [(gogoproto.jsontag) = "order_id"];
    // 订单支付总价 单位人民币
    int64 total_price = 4 [(gogoproto.jsontag) = "total_price"];
  }
}

message GetOrderStreamByTimeV2Req {
  // 开始时间
  int64 start_time = 1
      [(gogoproto.moretags) = 'form:"start_time" validate:"gt=0,required"'];
  // 结束时间
  int64 end_time = 2
      [(gogoproto.moretags) = 'form:"end_time" validate:"gt=0,required"'];
  // 订单状态
  int64 status = 3 [(gogoproto.moretags) = 'form:"status"'];
  // 商品goods
  repeated int64 goods_ids = 4 [(gogoproto.moretags) = 'form:"goods_ids"'];
  // 起始 ID
  int64 start_id = 5
      [(gogoproto.moretags) = 'form:"start_id" validate:"gt=0,required"'];
  // 请求数量
  int64 count = 6
      [(gogoproto.moretags) = 'form:"count" validate:"gt=0,required"'];
}

message GetOrderStreamByTimeV2Resp {
  repeated OrderInfo order_list = 1 [(gogoproto.jsontag) = "order_list"];

  message OrderInfo {
    // ID
    int64 id = 1 [(gogoproto.jsontag) = "id"];
    // 购买用户uid
    int64 uid = 2 [(gogoproto.jsontag) = "uid"];
    // 主播id
    int64 ruid = 3 [(gogoproto.jsontag) = "ruid"];
    // 订单id
    string order_id = 4 [(gogoproto.jsontag) = "order_id"];
    // 订单支付总价 单位人民币
    int64 total_price = 5 [(gogoproto.jsontag) = "total_price"];
  }
}

//统计主播收益
message GetAnchorStatisticsV2Req {
  //业务类型 0:全部 1:礼物 2:大航海 3:醒目留言 可不传 默认全部
  int32 biz_type = 2 [
    (gogoproto.jsontag) = "biz_type",
    (gogoproto.moretags) = "form:\"biz_type\""
  ];
  //主播ruid
  int64 ruid = 5 [
    (gogoproto.jsontag) = "ruid",
    (gogoproto.moretags) = "form:\"ruid\" validate:\"required\""
  ];
  // 父分区ids
  repeated int64 parent_area_ids = 6 [
    (gogoproto.jsontag) = "parent_area_ids",
    (gogoproto.moretags) = "form:\"parent_area_ids\""
  ];
  // 开始时间
  int64 start_time = 7 [
    (gogoproto.jsontag) = "start_time",
    (gogoproto.moretags) = "form:\"start_time\""
  ];
  // 开始时间
  int64 end_time = 8 [
    (gogoproto.jsontag) = "end_time",
    (gogoproto.moretags) = "form:\"end_time\""
  ];
}

//统计主播收益响应
message GetAnchorStatisticsV2Resp {
  //统计后金瓜子总数
  int64 total_price = 1 [(gogoproto.jsontag) = "total_price"];
}

message GetChildrenRechargeStreamReq {
  // 用户id
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"gt=0,required"'];
  // 开始时间
  int64 start_time = 2
      [(gogoproto.moretags) = 'form:"start_time" validate:"gt=0,required"'];
  // 结束时间
  int64 end_time = 3
      [(gogoproto.moretags) = 'form:"end_time" validate:"gt=0,required"'];
  // 页数
  int64 page = 4
      [(gogoproto.moretags) = 'form:"page" validate:"gt=0,required"'];
  // 每页个数
  int64 page_size = 5
      [(gogoproto.moretags) = 'form:"page_size" validate:"gt=0,required"'];
}

message GetChildrenRechargeStreamResp {
  repeated ChildrenRechargeStreamInfo list = 1 [(gogoproto.jsontag) = "list"];
  message ChildrenRechargeStreamInfo {
    // 用户id
    int64 uid = 1 [(gogoproto.jsontag) = "uid"];
    // 用户昵称
    string uname = 2 [(gogoproto.jsontag) = "uname"];
    // 商品名称
    string goods_name = 3 [(gogoproto.jsontag) = "goods_name"];
    // 商品数量
    int64 goods_num = 4 [(gogoproto.jsontag) = "goods_num"];
    // 支付金额
    double total_gold = 5 [(gogoproto.jsontag) = "total_gold"];
    // 订单 id
    string order_id = 6 [(gogoproto.jsontag) = "order_id"];
    // 下单时间
    int64 order_create_time = 7 [(gogoproto.jsontag) = "order_create_time"];
    // 充值金瓜子是否为混合支付 0非金瓜子 1否 2是bb混合 3是金仓鼠混合
    int64 mix_pay = 8 [(gogoproto.jsontag) = "mix_pay"];
    // 对账金额 支付中心-支付金额
    double paycenter_gold = 9 [(gogoproto.jsontag) = "paycenter_gold"];
  }
  // 总金额
  float total_amount = 2 [(gogoproto.jsontag) = "total_amount"];
  // 总金额-支付中心
  float total_paycenter_gold = 3 [(gogoproto.jsontag) = "total_paycenter_gold"];
  // bb手续费
  float ios_channel_fee = 4 [(gogoproto.jsontag) = "ios_channel_fee"];
  // 总数
  int64 count = 5 [(gogoproto.jsontag) = "count"];
}

message DownloadChildrenRechargeStreamReq {
  // 用户id
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"gt=0,required"'];
  // 开始时间
  int64 start_time = 2
      [(gogoproto.moretags) = 'form:"start_time" validate:"gt=0,required"'];
  // 结束时间
  int64 end_time = 3
      [(gogoproto.moretags) = 'form:"end_time" validate:"gt=0,required"'];
}

// 下载未成年人充值流水
message DownloadChildrenRechargeStreamResp {
  // 获取文件标识
  string flag = 1 [(gogoproto.jsontag) = "flag"];
}

// 获取未成年人充值流水请求
message GetChildrenRechargeStreamFileReq {
  // 文件标识
  string flag = 1 [(gogoproto.moretags) = 'form:"flag" validate:"required"'];
}

// 获取未成年人重试流水文件返回
message GetChildrenRechargeStreamFileResp {
  //  文件生成状态 0：生成中 1：生成完成 2：系统错误
  int64 status = 1 [(gogoproto.jsontag) = "status"];
  //  文件url
  string file_url = 2 [(gogoproto.jsontag) = "file_url"];
}

message GetOrderStreamByTimeV3Req {
  // 用户 ID
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid"'];
  // 商品ID
  repeated int64 goods_ids = 2 [(gogoproto.moretags) = 'form:"goods_ids"'];
  // 开始时间
  int64 start_time = 3
      [(gogoproto.moretags) = 'form:"start_time" validate:"gt=0,required"'];
  // 结束时间
  int64 end_time = 4
      [(gogoproto.moretags) = 'form:"end_time" validate:"gt=0,required"'];
  // 起始 ID
  int64 start_id = 5
      [(gogoproto.moretags) = 'form:"start_id" validate:"gt=0,required"'];
  // 请求数量
  int64 count = 6
      [(gogoproto.moretags) = 'form:"count" validate:"gt=0,required"'];
}

message GetOrderStreamByTimeV3Resp {
  repeated OrderInfo order_list = 1 [(gogoproto.jsontag) = "order_list"];

  message OrderInfo {
    // ID
    int64 id = 1 [(gogoproto.jsontag) = "id"];
    // 购买用户uid
    int64 uid = 2 [(gogoproto.jsontag) = "uid"];
    // 主播id
    int64 ruid = 3 [(gogoproto.jsontag) = "ruid"];
    // 订单id
    string order_id = 4 [(gogoproto.jsontag) = "order_id"];
    // 订单支付总价 单位人民币
    int64 total_price = 5 [(gogoproto.jsontag) = "total_price"];
  }
}