syntax = "proto3";

package live_consultate.v1;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/live.consultate.v1;v1";
option java_package = "com.bapis.live.live.consultate.v1";
option java_multiple_files = true;
import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "live.live-tool.live-consultate";

// 全链路事件节点管理
service TraceEvent {
    // 事件保存
    rpc SaveEvent(SaveEventReq) returns (SaveEventResp);

    // 删除事件版本
    rpc DeleteEventVersion(DeleteEventVersionReq) returns (DeleteEventVersionResp);

    // 事件客户端关联保存
    rpc SaveEventClientRelation(SaveEventClientRelationReq) returns (SaveEventClientRelationResp);

    // 删除事件客户端关联
    rpc DeleteEventClientRelation(DeleteEventClientRelationReq) returns (DeleteEventClientRelationResp);

    // 事件选项列表
    rpc GetEventMetaList(GetEventMetaListReq) returns (GetEventMetaListResp);

    // 事件版本查询
    rpc FindEventVersionList(FindEventVersionListReq) returns (FindEventVersionListResp);

    // 事件记录查询
    rpc EventRecordList(EventRecordListReq) returns (EventRecordListResp);
}

// 事件保存
message SaveEventReq {
    int64 event_version_id = 1 [(gogoproto.moretags) = 'form:"event_version_id"'];
    string name = 2 [(gogoproto.moretags) = 'form:"name" validate:"required"'];
    string desc = 3 [(gogoproto.moretags) = 'form:"desc" validate:"required"'];
    string category = 4 [(gogoproto.moretags) = 'form:"category" validate:"required"'];
    string content = 5 [(gogoproto.moretags) = 'form:"content" validate:"required"'];
    string action_user = 6 [(gogoproto.moretags) = 'form:"action_user" validate:"required"'];
    int64 is_template = 7 [(gogoproto.moretags) = 'form:"is_template"'];
}

message SaveEventResp {
    bool success = 1 [(gogoproto.jsontag) = "success"];
}

// 事件版本删除
message DeleteEventVersionReq {
    int64 event_version_id = 1 [(gogoproto.moretags) = 'form:"event_version_id" validate:"required"'];
    string action_user = 2 [(gogoproto.moretags) = 'form:"action_user" validate:"required"'];
}

message DeleteEventVersionResp {
    bool success = 1 [(gogoproto.jsontag) = "success"];
}

// 事件客户端版本关联
message SaveEventClientRelationReq {
    int64 event_version_id = 1 [(gogoproto.moretags) = 'form:"event_version_id" validate:"required"'];
    string platform = 2 [(gogoproto.moretags) = 'form:"platform" validate:"required"'];
    int64 build = 3 [(gogoproto.moretags) = 'form:"build" validate:"required"'];
    string action_user = 4 [(gogoproto.moretags) = 'form:"action_user" validate:"required"'];
}

message SaveEventClientRelationResp {
    bool success = 1 [(gogoproto.jsontag) = "success"];
}

// 删除事件客户端版本关联
message DeleteEventClientRelationReq {
    int64 event_version_id = 1 [(gogoproto.moretags) = 'form:"event_version_id" validate:"required"'];
    string platform = 2 [(gogoproto.moretags) = 'form:"platform" validate:"required"'];
    string action_user = 3 [(gogoproto.moretags) = 'form:"action_user" validate:"required"'];
}

message DeleteEventClientRelationResp {
    bool success = 1 [(gogoproto.jsontag) = "success"];
}

// 事件查询mate信息获取, meta_key有：event_name-事件名, build-上报版本号
message GetEventMetaListReq {
    repeated string meta_keys = 1 [(gogoproto.moretags) = 'form:"meta_keys" validate:"required"'];
    string event_name = 2 [(gogoproto.moretags) = 'form:"event_name"'];
}

message GetEventMetaListResp {
    repeated EventMetaInfo meta_info = 1 [(gogoproto.jsontag) = "meta_info"];
}

// 事件查询
message FindEventVersionListReq {
    string category = 1 [(gogoproto.moretags) = 'form:"category"'];
    string event_name = 2 [(gogoproto.moretags) = 'form:"event_name"'];
    string platform = 3 [(gogoproto.moretags) = 'form:"platform"'];
    int64 build = 4 [(gogoproto.moretags) = 'form:"build"'];
    int64 offset = 5 [(gogoproto.moretags) = 'form:"offset"'];
    int64 limit = 6 [(gogoproto.moretags) = 'form:"limit"'];
}

message FindEventVersionListResp {
    EventInfo event = 1 [(gogoproto.jsontag) = "event"];
    repeated EventVersion version_list = 2 [(gogoproto.jsontag) = "version_list"];
}

// 记录查询
message EventRecordListReq {
    string category = 1 [(gogoproto.moretags) = 'form:"category"'];
    string event_name = 2 [(gogoproto.moretags) = 'form:"event_name" validate:"required"'];
    int64 offset = 3 [(gogoproto.moretags) = 'form:"offset"'];
    int64 limit = 4 [(gogoproto.moretags) = 'form:"limit"'];
}

message EventRecordListResp {
    EventInfo event_info = 1 [(gogoproto.jsontag) = "event_info"];
    repeated EventRecord records = 2 [(gogoproto.jsontag) = "records"];
}

// 事件信息
message EventInfo {
    string name = 1 [(gogoproto.jsontag) = "name"];
    string desc = 2 [(gogoproto.jsontag) = "desc"];
    string category = 3 [(gogoproto.jsontag) = "category"];
}

// 事件记录信息
message EventRecord {
    string action_user = 1 [(gogoproto.jsontag) = "action_user"];
    int64 action_type = 2 [(gogoproto.jsontag) = "action_type"];
    string content = 3 [(gogoproto.jsontag) = "content"];
    string ctime = 4 [(gogoproto.jsontag) = "ctime"];
    string mtime = 5 [(gogoproto.jsontag) = "mtime"];
}

// 事件版本信息（含客户端关联关系）
message EventVersion {
    int64 event_version_id = 1 [(gogoproto.jsontag) = "event_version_id"];
    string content = 2 [(gogoproto.jsontag) = "content"];
    string creator = 3 [(gogoproto.jsontag) = "creator"];
    string updator = 4 [(gogoproto.jsontag) = "updator"];
    int64 is_template = 5 [(gogoproto.jsontag) = "is_template"];
    repeated EventClientInfo clients = 6 [(gogoproto.jsontag) = "clients"];
    string ctime = 7 [(gogoproto.jsontag) = "ctime"];
    string mtime = 8 [(gogoproto.jsontag) = "mtime"];
}

message EventClientInfo {
    string platform = 1 [(gogoproto.jsontag) = "platform"];
    int64 build = 2 [(gogoproto.jsontag) = "build"];
}

// 事件meta信息
message EventMetaInfo {
    string meta_key = 1 [(gogoproto.jsontag) = "meta_key"];
    repeated EventMetaDetail meta_detail = 2 [(gogoproto.jsontag) = "meta_detail"];
}

message EventMetaDetail {
    string meta_value = 1 [(gogoproto.jsontag) = "meta_value"];
    repeated string meta_list = 2 [(gogoproto.jsontag) = "meta_list"];
}