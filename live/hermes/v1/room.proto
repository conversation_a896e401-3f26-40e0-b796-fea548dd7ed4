syntax = "proto3";

package live.hermes.v1;

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
// import "google/api/annotations.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/hermes.v1;v1";
option (gogoproto.goproto_getters_all) = false;
option java_package = "com.bapis.live.hermes.v1";
option java_multiple_files = true;
option (wdcli.appid) = "live.live.hermes";

service Room {
  // 互玩房间列表
  rpc GetAllRoomList(GetAllRoomListReq) returns (GetAllRoomListResp);
}

enum RoomAppType{
  RoomAppType_AppType_None         = 0;
  RoomAppType_AppType_Interactive  = 1;
}

message RoomInfo {
  // 房间ID
  int64 RoomId = 1 [(gogoproto.jsontag) = "room_id"];
  // App类型 1-互动玩法
  RoomAppType AppType = 2 [(gogoproto.jsontag) = "app_type"];
  // AppID
  int64 AppId = 3 [(gogoproto.jsontag) = "app_id"];
  // 游戏当局ID
  string GameId = 4 [(gogoproto.jsontag) = "game_id"];
  // 游戏名
  string GameName = 5 [(gogoproto.jsontag) = "game_name"];
  // 开始时间
  int64 StartTime = 6 [(gogoproto.jsontag) = "start_time"];
  // 结束时间（在玩游戏无结束时间，默认为0）
  int64 EndTime = 7 [(gogoproto.jsontag) = "end_time"];
  // 是否在播 1-在播
  int64 IsLive = 8 [(gogoproto.jsontag) = "is_live"];
}

message GetAllRoomListReq {
}

message GetAllRoomListResp {
  // 在玩房间列表
  repeated RoomInfo RoomList = 1 [(gogoproto.jsontag) = "room_list"];
}