syntax = "proto3";

package live.streaming;
option go_package = "buf.bilibili.co/bapis/bapis-gen/live/streaming;api";
import "extension/wdcli/wdcli.proto";
option java_package = "com.bapis.live.streaming";
option java_multiple_files = true;
option (wdcli.appid) = "live.live.live-streaming";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

// DeviceMetadata, use bilibili's standard network metadata format
message DeviceMetadata {
    // RemoteIp user's current IP.
    string remote_ip = 1 [(gogoproto.moretags) = 'form:"remote_ip"', (gogoproto.jsontag) = "remote_ip"];
    // RemotePort is an optional field may not be collected.
    string remote_port = 2 [(gogoproto.moretags) = 'form:"remote_port"', (gogoproto.jsontag) = "remote_port"];
    // app build version
    string build = 3 [(gogoproto.moretags) = 'form:"build"', (gogoproto.jsontag) = "build"];
    // buvid
    string buvid = 4 [(gogoproto.moretags) = 'form:"buvid"', (gogoproto.jsontag) = "buvid"];
    // buvid3
    string buvid3 = 5 [(gogoproto.moretags) = 'form:"buvid3"', (gogoproto.jsontag) = "buvid3"];
    // user-agent
    string user_agent = 6 [(gogoproto.moretags) = 'form:"user_agent"', (gogoproto.jsontag) = "user_agent"];
}

// RespStatus denotes for response status. Not only description for error
// when exception caught, but also extended description for several succeeding situations.
// BTW, it is unacceptable to return a gRPC error in this rpc.
message RespStatus {
    int32 code = 1;
    string message = 2;
}

service Streaming {
    // 视频云推流鉴权接口 - 内部鉴权限定
    // RoomCanLive is the API for BVC server to check if a room can push stream to CDN
    rpc RoomCanLive (RoomCanLiveRequest) returns (RoomCanLiveResponse);

    // 内部开播接口，大多数安全管控逻辑都会被豁免，请谨慎确认使用场景
    rpc InnerStartLive (InnerStartLiveRequest) returns (InnerStartLiveResponse);

    // 内部关播接口，大多数安全管控逻辑都会被豁免，请谨慎确认使用场景
    rpc InnerStopLive (InnerStopLiveRequest) returns (InnerStopLiveResponse);
}

message RoomCanLiveRequest {
    // 房间 id
    int64 room_id = 1;

    // ip 地址
    string ip = 2;

    // 业务 trace id
    string room_can_live_biz_trace_id = 5;
}

message RoomCanLiveResponse {
    // 对标旧接口错误码和错误信息通过，正常业务错误（如无法开播）不返回 grpc error
    RespStatus stat = 1;

    // 是否可以开播，true / false
    bool can_live = 2;

    // Deprecated: 已弃用，请使用 `delay_biz` 代替
    // 是否在高危分区，当且仅当 `can_live` == true 时有意义
    bool in_high_risk_areas = 3;

    // 延迟时间，当且仅当 `delay_biz` 非空时有意义，若存在多个业务同时要求生产延迟流，会取最大值
    int64 delay_seconds = 4;

    // 产生延迟的业务方列表 for example: (high_risk_areas: 高危分区) (anchor_self_delay: 主播自己配置的观看延迟)
    repeated string delay_biz = 5;
}

message InnerStartLiveRequest {
    // Metadata represents current streamer's network metadata.
    DeviceMetadata device = 1 [(gogoproto.moretags) = 'form:"device"', (gogoproto.jsontag) = "device"];

    // operator_id denotes for operator ID
    // 操作者的id
    int64 operator_id = 2 [(gogoproto.moretags) = 'form:"operator_id"', (gogoproto.jsontag) = "operator_id"];

    // RoomId is the identifier to index room abstraction.
    int64 room_id = 3 [(gogoproto.moretags) = 'form:"room_id" validate:"required"', (gogoproto.jsontag) = "room_id"];

    // Platform of streamer where affects `LiveRecord`/`FlowRecord`,
    // recommended to be use standard platform enum which is defined at `Platform` Enum.
    string platform = 4 [(gogoproto.moretags) = 'form:"platform" validate:"required"', (gogoproto.jsontag) = "platform"];

    // AreaId selected during `PreLive` session.
    int64 area_id = 5 [(gogoproto.moretags) = 'form:"area_id" validate:"required"', (gogoproto.jsontag) = "area_id"];

    // Src denotes for line identifier.
    int64 src = 6 [(gogoproto.moretags) = 'form:"src"', (gogoproto.jsontag) = "src"];

    // LineChecked is an extended field for Src.
    // It is used to indicate whether the up-streaming line is checked by client.
    int64 line_checked = 7 [(gogoproto.moretags) = 'form:"line_checked"', (gogoproto.jsontag) = "line_checked"];

    // ScreenRotation represents current screen status of streamer.
    int64 screen_rotation = 8 [(gogoproto.moretags) = 'form:"screen_rotation"', (gogoproto.jsontag) = "screen_rotation"];

    // LiveType denotes for what type of streaming the user is up to start.
    int64 live_type = 9 [(gogoproto.moretags) = 'form:"live_type"', (gogoproto.jsontag) = "live_type"];
}

message InnerStartLiveResponse {
    // Stat describes current streaming stat, usually for
    // errors and exceptions.
    RespStatus stat = 1;

    // Status of stream fetched from stream-API.
    string status = 2;

    // LiveKey denotes for session identifier.
    string live_key = 3;

    // SubSessionKey is the more detailed session identifier.
    string sub_session_key = 4;

    // Rtmp grabs from stream-api or studio service.
    // Parameter splicing rules： `address` + `code`
    // Example: rtmp://live-push.bilivideo.com/live-bvc/?streamname=live_123_321&key=example_key&schedule=rtmp&pflag=1
    Rtmp rtmp = 5;

    // Protocols are a list of protocol information that may used to
    // replace StartLiveResponse_Rtmp.
    repeated Protocol protocols = 7;

    // whether change happen before responding.
    int32 change = 8;

    // Rtmp grabbed directly from stream-API responses.
    message Rtmp {
        string address = 1;
        string code = 2;
        string new_link = 3;
        string provider = 4;
        int64 type = 5;
    }

    // Protocol grabbed directly from stream-API responses.
    message Protocol {
        string protocol = 1;
        string address = 2;
        string code = 3;
        string new_link = 4;
        string provider = 5;
    }
}


message InnerStopLiveRequest {
    // DeviceMetadata represents current streamer's device/network metadata. (bilibili's standard network metadata format)
    DeviceMetadata device = 1 [(gogoproto.moretags) = 'form:"device"', (gogoproto.jsontag) = "device"];

    // operator_id denotes for operator ID
    // 操作者的id
    int64 operator_id = 2 [(gogoproto.moretags) = 'form:"operator_id"', (gogoproto.jsontag) = "operator_id"];

    // RoomId is the identifier to index room abstraction.
    int64 room_id = 3 [(gogoproto.moretags) = 'form:"room_id" validate:"required"', (gogoproto.jsontag) = "room_id"];

    // Platform of streamer (use live format, not bilibili's standard network metadata format),
    // where affects `LiveRecord`/`FlowRecord` and other downstream data,
    // recommended to be use standard platform enum which is defined at `Platform` Enum.
    string platform = 4 [(gogoproto.moretags) = 'form:"platform" validate:"required"', (gogoproto.jsontag) = "platform"];

    // Remark represents the reason of stopping live.
    string remark = 5 [(gogoproto.moretags) = 'form:"remark"', (gogoproto.jsontag) = "remark"];
}

message InnerStopLiveResponse {
    // Stat describes current streaming stat, usually for
    // errors and exceptions.
    RespStatus stat = 1;
    // whether change happen before responding.
    // 0: no change; 1: changed
    int32 change = 2;
    // Status of current room status.
    // ROUND：表示轮播；PREPARING：表示关播
    string status = 3;
}