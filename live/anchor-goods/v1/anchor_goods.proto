syntax = "proto3";
package live.live.anchor_goods.v1;

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/anchor.goods.v1";
option java_package = "com.bapis.live.anchor.goods.v1";
option java_multiple_files = true;
option (wdcli.appid) = "live.live.anchor-goods";

service Goods {
	// 查询用户在直播间是否有卡片
	rpc HasServiceCardByOrder(HasServiceCardByOrderReq) returns (HasServiceCardByOrderResp);
	// 查询服务卡详情
	rpc GetServiceCardDetail(GetServiceCardDetailReq) returns (GetServiceCardDetailResp);
	// GetUserIconShowInfo 用户一起玩icon展示逻辑
	rpc GetUserIconShowInfo(GetUserIconShowInfoReq) returns (GetUserIconShowInfoResp);
	// 赠票权限判断
	rpc CheckGetPlayTogetherTicket(CheckGetPlayTogetherTicketReq) returns (CheckGetPlayTogetherTicketResp);
	// 商品排序时获取最新商品列表
	rpc PlayTogetherAnchorSortGoodsView(PlayTogetherAnchorSortGoodsViewReq) returns (PlayTogetherAnchorSortGoodsViewResp);
	// 商品排序
	rpc AnchorSortGoods(AnchorSortGoodsReq) returns (AnchorSortGoodsResp);
	// 查询是否有服务卡需要展示
    rpc HasServiceCardByGoods(HasServiceCardByGoodsReq) returns (HasServiceCardByGoodsResp);
}

message PlayTogetherAnchorSortGoodsViewReq {
	int64 ruid = 1;
}
message PlayTogetherAnchorSortGoodsViewResp {
	repeated GoodsInfo infos = 3 [(gogoproto.jsontag) = "infos"];
}

message  GoodsInfo {
	int64 id = 1 [(gogoproto.jsontag) = "id"];
	// 模版名
	string name = 2 [(gogoproto.jsontag) = "name"];
	//服务类型
	int64 service_type = 3 [(gogoproto.jsontag) = "service_type"];
	// 游戏名
	string game_name = 4 [(gogoproto.jsontag) = "game_name"];
	//游戏分区
	string game_area = 5 [(gogoproto.jsontag) = "game_area"];
	//服务开始时间
	int64 start_time = 6  [(gogoproto.jsontag) = "start_time"];
	//服务结束时间
	int64 end_time = 7  [(gogoproto.jsontag) = "end_time"];
	//价格
	int64 price = 8 [(gogoproto.jsontag) = "price"];
	//对谁可见 0表示所有人
	int64 target = 9 [(gogoproto.jsontag) = "target"];
	//更多介绍
	string desc = 10 [(gogoproto.jsontag) = "desc"];
	//货物状态 0未发布 1已发布
	int64 state = 11 [(gogoproto.jsontag) = "state"];
	// 商品过期时间
	int64 expire_time = 12  [(gogoproto.jsontag) = "expire_time"];
	// 游戏icon
	string game_icon = 13 [(gogoproto.jsontag) = "game_icon"];
	// 审核状态 0-初始状态 1-审核中 2-审核通过 3-审核驳回
	int64 audit_status = 14 [(gogoproto.jsontag) = "audit_status"];
	// 审核驳回展示文案，一般 audit_status = 3 时展示
	string audit_text = 15 [(gogoproto.jsontag) = "audit_text"];
	// 服务类型中文名
	string service_name = 16 [(gogoproto.jsontag) = "service_name"];
}

message AnchorSortGoodsReq {
	int64 ruid = 1;
	repeated int64 goods_ids = 2;
}
message AnchorSortGoodsResp {
}

message CheckGetPlayTogetherTicketReq {
	// 用户mid
	int64 uid = 1;
	// 主播mid
	int64 ruid = 2;
	// 业务类型 gift:送礼 guard:大航海
	repeated string biz_types = 3;
}

message CheckGetPlayTogetherTicketResp {
	// 是否有权限，按照 biz_types 的顺序返回
	repeated bool has_perms = 1;
}

message HasServiceCardByOrderReq{
	int64 ruid = 1;
	int64 uid = 2;
}

message HasServiceCardByOrderResp{
	// 卡片优先级 数字越大优先级越高 ，0不显示，10公单商品、20私单商品、30待确认订单
	int64 card_level = 1;
	// 卡片过期时间 单位秒
	int64 card_expire_time = 2;
	// 跳转h5地址app
	string card_url_app = 3;
	// 跳转h5地址web
	string card_url_web = 4;
	// 是否有订单
	bool has_doing_order = 5;
}

message GetServiceCardDetailReq{
	int64 ruid = 1;
	int64 uid = 2;
}

message GetServiceCardDetailResp{
	// 卡片优先级 数字越大优先级越高 ，0不显示，10公单商品、20私单商品、30待确认订单
	int64 card_level = 1;
	//主标题
	string title = 2;
	//副标题
	string sub_title = 3;
	//游戏图标
	string game_icon = 4;
	//商品价格
	int64 goods_price = 5;
	//按钮文案
	string button_text = 6;
	//订单过期时间
	int64 order_expire_time = 7;
	//跳转的web url
	string jump_url_web = 8;
	//跳转的app url
	string jump_url_app = 9;
	// 是否有订单
	bool has_doing_order = 10;
}


message GetUserIconShowInfoReq {
	int64 uid = 1;
	int64 ruid = 2;
	int64 area_id = 3;
	int64 parent_area_id = 4;
	string filter_status = 5;
}

message GetUserIconShowInfoResp {
	// 0-未查询 1-展示按钮 2-不展示按钮
	int64 switch = 1;
	string icon_app = 2;
	string icon_web = 3;
	string url_app = 4;
	string url_web = 5;
	string icon_app_sub = 6;
	int64 show_text = 7;
}

message HasServiceCardByGoodsReq{
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"min=1,required"'];
  int64 ruid = 2 [(gogoproto.moretags) = 'form:"ruid" validate:"min=1,required"'];
  int64 room_id = 3 [(gogoproto.moretags) = 'form:"room_id" validate:"min=1,required"'];
}

message HasServiceCardByGoodsResp{
  // 卡片优先级 数字越大优先级越高 ，0不显示，10公单商品、20私单商品、30待确认订单
  int64 card_level = 1 [(gogoproto.jsontag) = "card_level"];
  // 卡片过期时间 单位秒
  int64 card_expire_time = 2 [(gogoproto.jsontag) = "card_expire_time"];
  // 跳转h5地址 App
  string card_url_app = 3 [(gogoproto.jsontag) = "card_url_app"];
  // 跳转h5地址 Web
  string card_url_web = 4 [(gogoproto.jsontag) = "card_url_web"];
  // 商品详情
  AnchorGoodsDetailResp good_detail = 5 [(gogoproto.jsontag) = "good_detail"];
}

message AnchorGoodsDetailResp {
  int64 id = 1 [(gogoproto.jsontag) = "id"];
  // 模版名
  string name = 2 [(gogoproto.jsontag) = "name"];
  //服务类型
  int64 service_type = 3 [(gogoproto.jsontag) = "service_type"];
  // 游戏名
  string game_name = 4 [(gogoproto.jsontag) = "game_name"];
  //游戏分区
  string game_area = 5 [(gogoproto.jsontag) = "game_area"];
  //服务开始时间
  string start_time = 6  [(gogoproto.jsontag) = "start_time"];
  //服务结束时间
  string end_time = 7  [(gogoproto.jsontag) = "end_time"];
  //价格
  int64 price = 8 [(gogoproto.jsontag) = "price"];
  //对谁可见 0表示所有人
  int64 target = 9 [(gogoproto.jsontag) = "target"];
  //更多介绍
  string desc = 10 [(gogoproto.jsontag) = "desc"];
  //订单创建时间
  string  create_tm = 11 [(gogoproto.jsontag) = "create_tm"];
  // 服务名
  string service_name = 12 [(gogoproto.jsontag) = "service_name"];
  string game_icon = 13 [(gogoproto.jsontag) = "game_icon"];
  string expire_time = 14  [(gogoproto.jsontag) = "expire_time"];

}