syntax = "proto3";
package live.settlementjob;
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
option go_package = "buf.bilibili.co/bapis/bapis-gen/live/settlement.job";
option java_package = "com.bapis.live.settlement.job";
option java_multiple_files = true;
option (wdcli.appid) = "live.live.settlement-job";

service VaSettlementJob  {
  // 开放平台装扮日结
  rpc OpenDressDailySettlement(OpenDressDailySettlementReq) returns (OpenDressDailySettlementResp);
  // 开放平台装扮月结
  rpc OpenDressMonthlySettlement(OpenDressMonthlySettlementReq) returns (OpenDressMonthlySettlementResp);
  // 开放平台装扮账单流水明细
  rpc OpenDressMonthlyBillDetail(OpenDressMonthlyBillDetailReq) returns (OpenDressMonthlyBillDetailResp);
  // 开放平台装扮账单支付
  rpc OpenDressMonthlyBillPayment(OpenDressMonthlyBillPaymentReq) returns (OpenDressMonthlyBillPaymentResp);
  // 开放平台收藏集日结
  rpc DataCollectionDailySettlement(DataCollectionDailySettlementReq) returns (DataCollectionDailySettlementResp);
  // 开放平台收藏集月结
  rpc DataCollectionMonthlySettlement(DataCollectionMonthlySettlementReq) returns (DataCollectionMonthlySettlementResp);
  // 开放平台收藏集账单流水明细
  rpc DataCollectionMonthlyBillDetail(DataCollectionMonthlyBillDetailReq) returns (DataCollectionMonthlyBillDetailResp);
  // 开放平台装扮账单支付
  rpc DataCollectionMonthlyBillPayment(DataCollectionMonthlyBillPaymentReq) returns (DataCollectionMonthlyBillPaymentResp);

  // 临时生成一个账单(2023H1装扮返点专用)
  rpc TmpCreateOpenDressRebateBill(TmpCreateOpenDressRebateBillReq) returns (TmpCreateOpenDressRebateBillResp);
  // 2023H1装扮返点账单提预提单
  rpc TmpOpenDressRebateMonthlySettlement(TmpOpenDressRebateMonthlySettlementReq) returns (TmpOpenDressRebateMonthlySettlementResp);

}

message TmpOpenDressRebateMonthlySettlementReq {

}

message TmpOpenDressRebateMonthlySettlementResp {

}

message TmpCreateOpenDressRebateBillReq{
  // 分成方id
  int64 partner_id = 1 [(gogoproto.jsontag) = "partner_id"];
  // 分成方
  string partner_source = 2 [(gogoproto.jsontag) = "partner_source"];
  // 装扮spu
  int64 biz_id = 3 [(gogoproto.jsontag) = "biz_id"];
  // 账期
  int64 settlement_cycle = 4 [(gogoproto.jsontag) = "settlement_cycle"];
  // 打款金额
  int64 coin = 5 [(gogoproto.jsontag) = "coin"];
  // 实际支付总金额
  int64 pay_coin = 6 [(gogoproto.jsontag) = "pay_coin"];
}
message TmpCreateOpenDressRebateBillResp{}

message OpenDressDailySettlementReq {
  // 日结的日期,2023-01-01
  string day = 1;
  // 结算分成方，选填
  int64 partner_id = 2;
}

message OpenDressDailySettlementResp {
  int64 code = 1;
  string message = 2;
}

message OpenDressMonthlySettlementReq {
  // 月结的月份,2023-01
  string month = 1;
  // 结算分成方，选填
  int64 partner_id = 2;
}

message OpenDressMonthlySettlementResp {
  int64 code = 1;
  string message = 2;
}

message OpenDressMonthlyBillDetailReq {
  // 月份
  string month = 1;
  // 结算分成方，选填
  int64 partner_id = 2;
}

message OpenDressMonthlyBillDetailResp {
  int64 code = 1;
  string message = 2;
}

message OpenDressMonthlyBillPaymentReq {
  // 月份
  string month = 1;
}

message OpenDressMonthlyBillPaymentResp  {
  int64 code = 1;
  string message = 2;
}

message DataCollectionDailySettlementReq {
  // 日结的日期,2023-01-01
  string day = 1;
  // 结算分成方，选填
  int64 partner_id = 2;
}

message DataCollectionDailySettlementResp {
  int64 code = 1;
  string message = 2;
}

message DataCollectionMonthlySettlementReq {
  // 月结的月份,2023-01
  string month = 1;
  // 结算分成方，选填
  int64 partner_id = 2;
}

message DataCollectionMonthlySettlementResp {
  int64 code = 1;
  string message = 2;
}

message DataCollectionMonthlyBillDetailReq {
  // 月份
  string month = 1;
  // 结算分成方，选填
  int64 partner_id = 2;
}

message DataCollectionMonthlyBillDetailResp {
  int64 code = 1;
  string message = 2;
}

message DataCollectionMonthlyBillPaymentReq {
  // 月份
  string month = 1;
}

message DataCollectionMonthlyBillPaymentResp  {
  int64 code = 1;
  string message = 2;
}