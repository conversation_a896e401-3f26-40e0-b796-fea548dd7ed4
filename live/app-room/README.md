## buf.bilibili.co/live/app.room

> 本文件为自动生成，无需手动改动

[![BSR Go SDK Status](https://buf.bilibili.co/badge/go/buf.bilibili.co/live/app.room/badge.svg)](https://buf.bilibili.co/sdks?module=buf.bilibili.co/live/app.room&language=Go&ref=master)
[![BSR Java SDK Status](https://buf.bilibili.co/badge/java/buf.bilibili.co/live/app.room/badge.svg)](https://buf.bilibili.co/sdks?module=buf.bilibili.co/live/app.room&language=Java&ref=master)

### Golang 使用

推荐使用 buf schema registry 直接拉取对应包，该模式会生成具有最佳兼容性的 pb.go 文件：

```Go
import "buf.bilibili.co/bapis/bapis-gen/live/app.room"
```

你可以使用 `go get` 方式获取一个分支版本：

```bash
# go get buf.bilibili.co/bapis/bapis-gen/live/app.room@mr<merge-request-id> # 推荐使用 merge request id 获取分支版本，避免复杂分支名导致的一系列问题
# go get buf.bilibili.co/bapis/bapis-gen/live/app.room@<branch>
```

也可以使用以下方式获取指定以 golang protobuf 生成的版本或 gogoproto 生成的版本：

```Go
import "buf.bilibili.co/bapis/bapis-gen-go/live/app.room"
```

```Go
import "buf.bilibili.co/bapis/bapis-gen-gogo/live/app.room" // 不推荐
```

### Java 使用

#### buf-maven-plugin

请参阅：[maven 接入手册](https://info.bilibili.co/pages/viewpage.action?pageId=472335078)

示例：

```xml
<configuration>                 
    <!-- 此处modules仅为示例！！！根据项目实际依赖的proto文件的module名来填写 -->
    <moduleArray>
        <array>buf.bilibili.co/live/app.room</array>
    </moduleArray>    
</configuration>
```

#### buf-gradle-plugin

请参阅：[gradle 接入手册](https://info.bilibili.co/pages/viewpage.action?pageId=487220938)

示例：

```gradle
bufGenerateParam {
    // 此处modules仅为示例！！！根据项目实际依赖的proto文件的module名来填写
    modules = [
        "buf.bilibili.co/live/app.room",
    ]
}
```

### 遇到问题

在使用过程中遇到任何问题可以在 buf 用户群里，或直接 @zhoujiahui 反馈。

---

### 关于 [bapis-go](https://git.bilibili.co/bapis/bapis-go) 和 [bapis-java](https://git.bilibili.co/bapis/bapis-java)

这两个仓库已经停止构建，我们强烈建议应用在接入时使用以上推荐的 buf schema registry 方式。
