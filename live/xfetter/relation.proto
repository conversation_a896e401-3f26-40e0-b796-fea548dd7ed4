syntax = "proto3";
package live.xfetter;

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/xfetter";
option java_package = "com.bapis.live.xfetter";
option java_multiple_files = true;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

// relation 相关服务
service relation {
    // GetUserOnlineAnchorList 获取某一用户首页在线主播列表 
    rpc GetUserOnlineAnchorList(GetUserOnlineAnchorListReq) returns (GetUserOnlineAnchorListResp);
    // GetFollowType 获取某一用户调用关系链
    rpc GetFollowType(GetFollowTypeReq) returns (GetFollowTypeResp);
    // GetUserLiveRoomsWeb 获取某一用户关注主播在播列表（Web端）
    rpc GetUserLiveRoomsWeb(GetUserLiveRoomsWebReq) returns (GetUserLiveRoomsWebResp);
}

enum SortType {
    /* 特殊排序 */
    // 默认排序 （不排序）
    SortTypeDefault = 0; 

    /* 按照某单一属性排序 */
    // 根据在线数量倒序排序
    SortTypeOnline = 10000;
    // 根据在线数量正序排序
    SortTypeOnlineAsc = 10001;
    // 根据开播时间戳倒序排序
    SortTypeLiveStartTimestamp = 10002;
    // 根据开播时间戳正序排序
    SortTypeLiveStartTimestampAsc = 10003;

    /* ABTest 排序方式 */
    /* 测试方案 1 倒序排序
     Q = 最近 30 天观看次数特征值 + 最近 14 天打赏金瓜子特征值 * 2
     tapd: https://www.tapd.bilibili.co/51403727/prong/stories/view/1151403727001282942
    */
    SortTypeTest1 = 30000;
}

message GetUserLiveRoomsWebReq {
    // uid 用户 ID
    int64 uid = 1 [(gogoproto.jsontag) = "uid", json_name="uid"];
    // page 页码
    int64 page = 2 [(gogoproto.jsontag) = "page", json_name="page"];
    // pagesize 每页容量
    int64 pagesize = 3 [(gogoproto.jsontag) = "pagesize", json_name="pagesize"];
    // sort_type 排序方案
    SortType sort_type = 4 [(gogoproto.jsontag) = "sort_type", json_name="sort_type"];
}

message LiveRoom {
    // title 直播间标题，偶有命名 roomname
    string title = 1 [(gogoproto.jsontag) = "title", json_name="title"];
    // room_id 直播间 ID
    int64 room_id = 2 [(gogoproto.jsontag) = "room_id", json_name="room_id"];
    // uid 用户 ID
    int64 uid = 3 [(gogoproto.jsontag) = "uid", json_name="uid"];
    // live_time 已直播时长
    int64 live_time = 4 [(gogoproto.jsontag) = "live_time", json_name="live_time"];
    // live_status 直播间状态
    int64 live_status = 5 [(gogoproto.jsontag) = "live_status", json_name="live_status"];
    // short_id 短号ID
    int64 short_id = 6 [(gogoproto.jsontag) = "short_id", json_name="short_id"];
    // area 分区 ID
    int64 area = 7 [(gogoproto.jsontag) = "area", json_name="area"];
    // area_name 分区名称
    string area_name = 8 [(gogoproto.jsontag) = "area_name", json_name="area_name"];
    // area_v2_id v2 分区 ID
    int64 area_v2_id = 9 [(gogoproto.jsontag) = "area_v2_id", json_name="area_v2_id"];
    // area_v2_name v2 分区名称
    string area_v2_name = 10 [(gogoproto.jsontag) = "area_v2_name", json_name="area_v2_name"];
    // area_v2_parent_id v2 父分区 ID
    int64 area_v2_parent_id = 11 [(gogoproto.jsontag) = "area_v2_parent_id", json_name="area_v2_parent_id"];
    // area_v2_parent_name v2 父分区名称
    string area_v2_parent_name = 12 [(gogoproto.jsontag) = "area_v2_parent_name", json_name="area_v2_parent_name"];
    // uname 用户名，偶有命名 nickname
    string uname = 13 [(gogoproto.jsontag) = "uname", json_name="uname"];
    // face 头像 URL
    string face = 14 [(gogoproto.jsontag) = "face", json_name="face"];
    // tag_name 标签列表 => sample: "以撒,minecraft,饥荒,彩虹六号,东方"
    string tag_name = 15 [(gogoproto.jsontag) = "tag_name", json_name="tag_name"];
    // tags 用户自定义标签列表 => sample: "Minecraft,皮蛋,萌,我的世界"
    string tags = 16 [(gogoproto.jsontag) = "tags", json_name="tags"];
    // cover 封面图 URL
    string cover = 17 [(gogoproto.jsontag) = "cover", json_name="cover"];
    // keyframe 关键帧 URL
    string keyframe = 18 [(gogoproto.jsontag) = "keyframe", json_name="keyframe"];
    // lock_till_tampstamp 锁定解锁时间
    int64 lock_till_tampstamp = 19 [(gogoproto.jsontag) = "lock_till_tampstamp", json_name="lock_till_tampstamp"];
    // hidden_till_tampstamp 隐藏解锁时间
    int64 hidden_till_tampstamp = 20 [(gogoproto.jsontag) = "hidden_till_tampstamp", json_name="hidden_till_tampstamp"];
    // broadcast_type ？
    int64 broadcast_type = 21 [(gogoproto.jsontag) = "broadcast_type", json_name="broadcast_type"];
    // link 直播间 URL
    string link = 22 [(gogoproto.jsontag) = "link", json_name="link"];
    // live_start_timestamp 开播时间戳
    int64 live_start_timestamp = 25 [(gogoproto.jsontag) = "live_start_timestamp", json_name="live_start_timestamp"];
    // online 在线人数
    int64 online = 26 [(gogoproto.jsontag) = "online", json_name="online"];
    // Q 排序权重，一般来说业务不需要考虑和检查，而且请不要展示给前端接口！！
    int64 q = 27 [(gogoproto.jsontag) = "q", json_name="q"];
}

message Pagination {
    // page
    int64 page = 1 [(gogoproto.jsontag) = "page", json_name="page"]; 
    // pagesize
    int64 pagesize = 2 [(gogoproto.jsontag) = "pagesize", json_name="pagesize"]; 
    // total
    int64 total = 3 [(gogoproto.jsontag) = "total", json_name="total"]; 
}

message GetUserLiveRoomsWebResp {
    repeated LiveRoom live_rooms = 1 [(gogoproto.jsontag) = "live_rooms", json_name="live_rooms"];
    Pagination pagination = 2 [(gogoproto.jsontag) = "pagination", json_name="pagination"];
}

message GetFollowTypeReq {
    int64 uid = 1 [(gogoproto.jsontag) = "uid", json_name="uid"];
}

message GetFollowTypeResp {
    map<int64, UidInfo> data = 1 [(gogoproto.jsontag) = "data", json_name="data"];
    message UidInfo {
        // 用户uid
        int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name="mid"];
        // 关注类型
        int64 attribute = 2 [(gogoproto.jsontag) = "attribute", json_name="attribute"];
        // 是否特别关注
        int64 special = 3 [(gogoproto.jsontag) = "special", json_name="special"];
    }
}

message GetUserOnlineAnchorListReq {
    // 用户 ID
    int64 uid = 1 [(gogoproto.moretags) = 'json:"uid" validate:"required" ,json_name="uid"'];
    // 用户 IP 地址
    string uip = 2 [(gogoproto.moretags) = 'json:"uip" validate:"required" ,json_name="uip"'];
    // 是否使用 HTTPS 0 - 不使用 1 - 使用
    int64 is_https = 3 [(gogoproto.jsontag) = "is_https" ,json_name="is_https"];
    // 请求来源 传入URI，如 /relation/v1/BplusApp/dynamicRoomListV2
    string req_biz = 4 [(gogoproto.moretags) = 'json:"req_biz" validate:"required" ,json_name="req_biz"'];
    // platform
    string platform = 5 [(gogoproto.moretags) = 'json:"platform" validate:"required" ,json_name="platform"'];
    // build 
    string build = 6 [(gogoproto.moretags) = 'json:"build" validate:"required" ,json_name="build"'];
    // device_name 透传 device 字段
    string device_name = 7 [(gogoproto.moretags) = 'json:"device_name" validate:"required" ,json_name="device_name"'];
    // last_request_timestamp 上次请求时间，由客户端提供，第一次请求传 0
    int64 last_request_timestamp = 8 [(gogoproto.jsontag) = "last_request_timestamp" ,json_name="last_request_timestamp"];
    // limitation 传入对应数字，非法值（<=0）会自动默认改为 20
    int64 limitation = 9 [(gogoproto.moretags) = 'json:"limitation" validate:"required" ,json_name="limitation"'];
    // network 网络状态
    string network = 10 [(gogoproto.jsontag) = "network" ,json_name="network"];
}

message Room {
    // room_id 主播房间 ID 
    int64 room_id = 1 [(gogoproto.jsontag) = "room_id",json_name="room_id"];
    // ruid 主播 ID
    int64 ruid = 2 [(gogoproto.jsontag) = "ruid",json_name="ruid"];
    // runame 主播用户名
    string runame = 3 [(gogoproto.jsontag) = "runame",json_name="runame"];
    // face 主播头像
    string face = 4 [(gogoproto.jsontag) = "face",json_name="face"];
    // jump_url 直播间跳转链接（旧）
    string jump_url = 5 [(gogoproto.jsontag) = "jump_url",json_name="jump_url"];
    // area_v2_id 分区 ID
    int64 area_v2_id = 6 [(gogoproto.jsontag) = "area_v2_id",json_name="area_v2_id"];
    // area_v2_name 分区名称
    string area_v2_name = 7 [(gogoproto.jsontag) = "area_v2_name",json_name="area_v2_name"];
    // area_v2_parent_id 父级分区 ID
    int64 area_v2_parent_id = 8 [(gogoproto.jsontag) = "area_v2_parent_id",json_name="area_v2_parent_id"];
    // area_v2_parent_name 父级分区名称
    string area_v2_parent_name = 9 [(gogoproto.jsontag) = "area_v2_parent_name",json_name="area_v2_parent_name"];
    // live_start 上次开始直播时间
    int64 live_start = 10 [(gogoproto.jsontag) = "live_start",json_name="live_start"];
    // Q 排序权重，一般来说业务不需要考虑和检查，而且请不要展示给前端接口！！
    int64 q = 11 [(gogoproto.jsontag) = "q",json_name="q"];
    // 直播间跳转链接（新）
    string link = 12 [(gogoproto.jsontag) = "link",json_name="link"];
    // 封面图-横图
    string cover = 13 [(gogoproto.jsontag) = "cover",json_name="cover"];
    // 语聊房上麦中房间信息
    map<string,string> others = 14 [(gogoproto.jsontag) = "others"]; 
    // 直播间推荐原因 红包＞天选时刻＞帮我玩＞大航海＞粉丝团>上麦中>直播中
    string reason = 15 [(gogoproto.jsontag) = "reason"];
    // 原因 icon
    string reason_icon = 16 [(gogoproto.jsontag) = "reason_icon"];
}

message GetUserOnlineAnchorListResp {
    // total_count 总数
    int64 total_count = 1 [(gogoproto.jsontag) = "total_count", json_name="total_count"];
    // rooms 房间列表
    repeated Room rooms = 2 [(gogoproto.jsontag) = "rooms", json_name="rooms"];
}