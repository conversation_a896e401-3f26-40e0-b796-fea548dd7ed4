syntax = "proto3";

package live.anchormilestone;

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/anchor.milestone";
option java_package = "com.bapis.live.anchor.milestone";
option java_multiple_files = true;

import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "live.anchor.anchor-milestone";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

service AnchorIncentivePlan {
    // 用户侧定档定薪激励计划外显
    rpc GetAnchorIncentiveInfo (GetAnchorIncentiveInfoReq) returns (GetAnchorIncentiveInfoResp);
    // 公会侧定档定薪激励计划外显信息
    rpc GetGuildIncentiveInfo (GetGuildIncentiveInfoReq) returns (GetGuildIncentiveInfoResp);
}

message GetAnchorIncentiveInfoReq {
    // uid
    int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
    // 筛选当前月 默认当月 比如 202304
    int64 cur_month = 2 [(gogoproto.moretags) = 'form:"cur_month"'];
}

message GetAnchorIncentiveInfoResp {
    // 定档定薪 开关
    bool switch = 1 [(gogoproto.jsontag) = "switch"];
    // 定档定薪状态 -1 未申报 0 评估中 1 未入选 2 激励中 3 定档+激励完成 已结束 4 公会原因退出 5 其他原因退出
    int64 state = 2 [(gogoproto.jsontag) = "state"];
    // 定档定薪激励计划具体信息
    AnchorIncentiveInfo info = 3 [(gogoproto.jsontag) = "info"];
}

message AnchorIncentiveInfo {
    // 当前月 比如 202305
    int64 cur_month = 1 [(gogoproto.jsontag) = "cur_month"];
    // 当前最新数据时间点 比如 20230510
    int64 date = 2 [(gogoproto.jsontag) = "date"];
    // 当月最高可领现金奖励 单位元
    int64 month_max_reward = 3 [(gogoproto.jsontag) = "month_max_reward"];
    // 当月解锁金额 单位元
    int64 unlock_reward = 4 [(gogoproto.jsontag) = "unlock_reward"];
    // 里程碑 增量任务
    repeated MilestoneItem milestones = 7 [(gogoproto.jsontag) = "milestones"];
    // 基础任务
    repeated TaskItem tasks = 8 [(gogoproto.jsontag) = "tasks"];
    // 激励月记录
    repeated RecordItem records = 9 [(gogoproto.jsontag) = "records"];
    // 定档定薪总配置id
    int64 config_id = 10 [(gogoproto.jsontag) = "config_id"];
    // 档位id
    int64 tier_id = 11 [(gogoproto.jsontag) = "tier_id"];
    // 档位总奖励包 单位元
    int64 tier_reward_total = 12 [(gogoproto.jsontag) = "tier_reward_total"];
    // 档位时间id 第几月
    int64 cycle_id = 13 [(gogoproto.jsontag) = "cycle_id"];
    // 定档观测月
    int64 first_month = 14 [(gogoproto.jsontag) = "first_month"];
    // 定档观测月流水 单位元
    int64 revenue_base = 15 [(gogoproto.jsontag) = "revenue_base"];
    // 本月流水 单位元
    int64 cur_month_revenue = 16 [(gogoproto.jsontag) = "cur_month_revenue"];
    // 数据更新时间戳 仅公会侧用 mtime
    int64 timestamp = 17 [(gogoproto.jsontag) = "timestamp"];
    // uid
    int64 uid = 18 [(gogoproto.jsontag) = "uid"];
    // 业务类型 1:公会新主播 2:UP主高营收 3:UP主低营收 4:公会游戏新主播 5:公会娱乐新主播 6:公会电台新主播 7:公会虚拟新主播 8:公会互玩新主播
    int64 source = 19 [(gogoproto.jsontag) = "source"];
    // 本月背包礼物流水 单位元
    int64 cur_month_package_gift_revenue = 20 [(gogoproto.jsontag) = "cur_month_package_gift_revenue"];
    // 当月解锁基础任务奖励 单位元
    int64 unlock_base_reward = 22 [(gogoproto.jsontag) = "unlock_base_reward"];
    // 当月解锁流水任务奖励 单位元
    int64 unlock_revenue_reward = 23 [(gogoproto.jsontag) = "unlock_revenue_reward"];
    // 当月主播解锁奖励发放公会
    int64 cur_month_guild_id = 24 [(gogoproto.jsontag) = "cur_month_guild_id"];
    // 当月奖励发放状态: 0-正常发放 1-当前月度转会奖励不发放
    int64 reward_state = 25 [(gogoproto.jsontag) = "unlock_reward_state"];
    // 转会后主播在当前公会剩余总奖励包（主播转会后，总奖励包减转会前月份的月度奖励包（含转会当月），for公会后台查询）
    int64 cur_guild_tier_reward_total = 26 [(gogoproto.jsontag) = "cur_guild_tier_reward_total"];
}

message RecordItem {
    // 记录月份 比如202305
    int64 month = 1 [(gogoproto.jsontag) = "month"];
    // 月度奖励包 单位元
    int64 month_reward = 2 [(gogoproto.jsontag) = "month_reward"];
    // 实际解锁奖励 单位元
    int64 unlock_reward = 3 [(gogoproto.jsontag) = "unlock_reward"];
    // 状态 0 未开始 1 进行中 2 已结束
    int64 state = 4 [(gogoproto.jsontag) = "state"];
    // 月度奖励 百分比
    int64 percent = 5 [(gogoproto.jsontag) = "percent"];
    // 当月 里程碑 增量任务
    repeated MilestoneItem milestones = 6 [(gogoproto.jsontag) = "milestones"];
    // 当月 基础任务
    repeated TaskItem tasks = 7 [(gogoproto.jsontag) = "tasks"];
    // 最终更新时间点
    int64 last_date = 8 [(gogoproto.jsontag) = "last_date"];
    // 当月营收 单位元
    int64 revenue = 9 [(gogoproto.jsontag) = "revenue"];
    // 当月背包礼物流水 单位元
    int64 package_gift_revenue = 10 [(gogoproto.jsontag) = "package_gift_revenue"];
    int64 package_gift_revenue_discount = 11 [(gogoproto.jsontag) = "package_gift_revenue_discount"];
    // 当月解锁基础任务奖励 单位元
    int64 unlock_base_reward = 22 [(gogoproto.jsontag) = "unlock_base_reward"];
    // 当月解锁流水任务奖励 单位元
    int64 unlock_revenue_reward = 23 [(gogoproto.jsontag) = "unlock_revenue_reward"];
    // 月度奖励包 单位元（基础任务）
    int64 month_base_reward = 24 [(gogoproto.jsontag) = "month_base_reward"];
    // 月度奖励包 单位元（营收任务）
    int64 month_revenue_reward = 25 [(gogoproto.jsontag) = "month_revenue_reward"];
    // 当月主播解锁奖励发放公会
    int64 cur_month_guild_id = 26 [(gogoproto.jsontag) = "cur_month_guild_id"];
    // 当月解锁奖励发reward_state放状态: 0-正常发放 1-当前月度转会奖励不发放
    int64 unlock_reward_state = 27 [(gogoproto.jsontag) = "unlock_reward_state"];
}

message TaskItem {
    // 任务类型 1:营收流水(除语聊房) 2:开播天数 4:开播时长 3:新增投稿 5:新增粉丝
    int64 task_type = 1 [(gogoproto.jsontag) = "task_type"];
    // 任务目标值
    int64 target = 2 [(gogoproto.jsontag) = "target"];
    // 任务进度值
    int64 value = 3 [(gogoproto.jsontag) = "value"];
    // 任务是否达标 0 未达标 1 已达标
    int64 state = 4 [(gogoproto.jsontag) = "state"];
    // 档位时间id 第几月
    int64 cycle_id = 5 [(gogoproto.jsontag) = "cycle_id"];
}

message MilestoneItem {
    // 里程碑id
    int64 id = 1 [(gogoproto.jsontag) = "id"];
    // 里程碑目标值 单位元
    int64 target = 2 [(gogoproto.jsontag) = "target"];
    // 奖励值 单位元
    int64 value = 3 [(gogoproto.jsontag) = "value"];
    // 状态 0 未解锁 1 已解锁
    int64 state = 4 [(gogoproto.jsontag) = "state"];
    // 流水基线 单位元 配置 不外显
    int64 base = 5 [(gogoproto.jsontag) = "base"];
    // 多得x元 单位元 配置 不外显
    int64 more = 6 [(gogoproto.jsontag) = "more"];
    // 奖励 单位元
    int64 reward = 7 [(gogoproto.jsontag) = "reward"];
    // 档位时间id 第几月
    int64 cycle_id = 8 [(gogoproto.jsontag) = "cycle_id"];
}

message GetGuildIncentiveInfoReq {
    // 批量查询用户定档定薪激励计划
    repeated int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
    // 筛选当前月 默认当月 比如 202304
    int64 cur_month = 2 [(gogoproto.moretags) = 'form:"cur_month"'];
    int64  gid = 3 [(gogoproto.moretags) = 'form:"gid"'];

}

message GetGuildIncentiveInfoResp {
    // 定档定薪列表
    repeated AnchorIncentiveInfo list = 1 [(gogoproto.jsontag) = "list"];
}