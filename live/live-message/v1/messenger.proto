syntax = "proto3";
package live.livemessage.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/live/live.message.v1;v1";
option java_package = "com.bapis.live.live.message.v1";
option java_multiple_files = true;
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "live.livemessage";

service LiveMessenger {
    // 发送系统通知
    rpc SendNotification (SendNotificationReq) returns (SendNotificationResp);

    // 发送私信
    rpc SendPrivateMessage (SendPrivateMessageReq) returns (SendPrivateMessageResp);

    // 创建消息卡片的 key
    // 如果调用方的业务场景需要失败时重试发送，则需要保存这个 key
    rpc NewMessageCardKey (NewMessageCardKeyReq) returns (NewMessageCardKeyResp);

    // 给选定用户发送消息卡片
    // 发送消息卡片前首先需要通过 NewMessageCardKey() 创建一个 key
    rpc BatchSendMessageCard (BatchSendMessageCardReq) returns (google.protobuf.Empty);
}

message SendNotificationReq {
    int64 uid = 1 [(gogoproto.moretags) = 'validate:"required"'];
    string title = 2 [(gogoproto.moretags) = 'validate:"required"'];
    string content = 3 [(gogoproto.moretags) = 'validate:"required"'];
    MC mc = 4 [(gogoproto.moretags) = 'validate:"required"'];

    message MC {
        Category category = 1;
        string custom_mc = 2;

        enum Category {
            // 使用自定义 MC
            USE_CUSTOM = 0;
            // 金仓鼠/金瓜子/银瓜子发放
            INCOME_RECEIVED = 1;
            // 金仓鼠/金瓜子/银瓜子扣除
            INCOME_DEDUCTION = 2;
            // 公会邀请函
            GUILD_INVITATION = 3;
            // 直播周报推送
            WEEKLY_REPORT = 4;
            // 用户消息
            USER_MESSAGE = 5;
            // 主播消息
            ANCHOR_MESSAGE = 6;
            // 举报
            NOTICE_REPORT = 7;
            // 被举报
            NOTICE_REPORTED = 8;
        }
    }
}

message SendNotificationResp {}

message SendPrivateMessageReq {
    int64 uid = 1 [(gogoproto.moretags) = 'validate:"required"'];
    string content = 2 [(gogoproto.moretags) = 'validate:"required"'];
    SID sid = 3 [(gogoproto.moretags) = 'validate:"required"'];

    message SID {
        ID id = 1;
        uint64 custom_id = 2;

        enum ID {
            // 使用自定义发送帐号
            USE_CUSTOM = 0;
            // 哔哩哔哩直播运营团队
            BILI_LIVE_OPERATION_TEAM = 1;
            // 哔哩哔哩直播小喇叭
            BILI_LIVE_TRUMPET = 2;
            // 哔哩哔哩直播姬
            BILI_LIVE_HIME = 3;
            // 哔哩哔哩直播
            BILI_LIVE = 4;
        }
    }
}

message SendPrivateMessageResp {
    uint64 msg_key = 1 [(gogoproto.jsontag) = "msg_key", json_name = "msg_key"];
}

message NewMessageCardKeyReq {
    // 发送帐号的 id；必须要和对应的 notify_code 匹配
    int64 sender_id = 1;

    // 消息卡的通知码，需要提前申请，和具体业务对应
    string notify_code = 2 [(gogoproto.moretags) = 'validate:"required"'];

    // 填充后台配置内容的参数；个数和申请时填写的配置内容需要对应
    repeated string params = 3;

    // 指定跳转链接；如果传了对应的跳转链接，则后台配置的默认链接被取代
    string jump_url1 = 4;
    string jump_url2 = 5;

    // 模块资源
    repeated string modular_contents = 6;
}

message NewMessageCardKeyResp {
    // 生成的 key；发送/重试发送 等操作都需要这个 key
    int64 key = 1;

    // 请求中的 sender_id；回传是为了方便调用者后续调用其他接口
    int64 sender_id = 2;
}

message BatchSendMessageCardReq {
    // 发送帐号的 id；必须要和 msg_key 匹配
    int64 sender_id = 1;

    // 消息卡片对应的 key
    int64 msg_key = 2 [(gogoproto.moretags) = 'validate:"required"'];

    // 需要发送的用户 uid 列表
    // 数量超过100会在内部被分组后发送；分组越多失败的概率越高
    repeated int64 recv_ids = 3;
}