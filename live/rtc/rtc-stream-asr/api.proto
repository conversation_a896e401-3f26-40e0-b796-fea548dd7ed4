syntax = "proto3";
package live.rtc.rtcstreamasr.v1;

// import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

option java_package = "com.bapis.live.rtc.rtc.stream.asr";
option java_multiple_files = true;
option go_package = "buf.bilibili.co/bapis/bapis-gen/live/rtc.rtc.stream.asr;v1";
option (wdcli.appid) = "live.rtc.rtc-stream-asr";

service RtcStreamAsr {
  // 创建Rtc Asr任务
  rpc StartStreamASR(StartStreamASRRequest) returns (StartStreamASRResponse);
  // 停止Rtc Asr任务
  rpc StopStreamASR(StopStreamASRRequest) returns (StopStreamASRResponse);
}

message StartStreamASRRequest {
  uint64 cid = 1;
  uint64 stream_uid = 2;
  uint64 sendto_uid = 3;
  string token = 4; // MUST set
}


message StartStreamASRResponse {
  string message = 1;
}

message StopStreamASRRequest {
  uint64 cid = 1; // MUST grater than 0
  uint64 stream_uid = 2; // MUST grater than 0
}

message StopStreamASRResponse {
  string message = 1;
}