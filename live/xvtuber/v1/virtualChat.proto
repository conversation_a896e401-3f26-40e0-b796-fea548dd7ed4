syntax = "proto3";
package live.xvtuber.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/live/xvtuber.v1;v1";
option java_package = "com.bapis.live.xvtuber.v1";
option java_multiple_files = true;
option (wdcli.appid) = "live.live.xvtuber";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

service VtuberChatService {
  // 生成沙月Token
  rpc GenerateSYToken(GenerateSYTokenReq) returns (GenerateSYTokenResp);
}

message GenerateSYTokenReq {
  int64 count = 1 [(gogoproto.moretags) = 'form:"count"', (gogoproto.jsontag) = "count"];
}

message GenerateSYTokenResp {
  repeated string list = 1 [(gogoproto.jsontag) = 'list'];
}