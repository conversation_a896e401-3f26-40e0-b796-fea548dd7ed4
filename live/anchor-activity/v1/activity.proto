syntax = "proto3";
package live.live.anchor_activity.v1;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/anchor.activity.v1;v1";
option java_package = "com.bapis.live.anchor.activity.v1";
option java_multiple_files = true;
option (wdcli.appid) = "live.live.anchor-activity";

// proto 编写手册
// import "github.com/gogo/protobuf/gogoproto/gogo.proto";
// import "google/api/annotations.proto"; // for http
// import "extension/bb/bb.proto"; // for bb

service Activity {
  // 查询用户的报名信息
  rpc SignUpInfo(SignUpInfoReq) returns (SignUpInfoResp);
  // 用户报名活动
  rpc SignUpAct(SignUpActReq) returns (SignUpActResp);
  // 查询用户的活动信息
  rpc UserActInfo(UserActInfoReq) returns (UserActInfoResp);
  // 查询用户的报名状态
  rpc IsSignUp(IsSignUpReq) returns (IsSignUpResp);
  // 压测专用，临时接口
  rpc StatLike(StatLikeReq) returns (StatLikeResp);
}

message SignUpInfoReq {
  // mid
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"min=1,required"'];
  // 活动id
  int64 act_id = 2 [(gogoproto.moretags) = 'form:"act_id" validate:"min=1,required"'];
}

message SignUpInfoResp {
  SignUpStatus sign_status = 1 [(gogoproto.jsontag) = "sign_status"];
}

message SignUpActReq {
  // mid
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"min=1,required"'];
  // 活动id
  int64 act_id = 2 [(gogoproto.moretags) = 'form:"act_id" validate:"min=1,required"'];
  // buvid
  string buvid = 3 [(gogoproto.moretags) = 'form:"buvid"'];
}

message SignUpActResp {
  // 报名信息
  SignUpStatus sign_status = 1 [(gogoproto.jsontag) = "sign_status"];
}

enum SignUpStatus {
  UNKNOWN = 0; // 未知
  NOT_SIGN_UP = 1; // 未报名无资格
  CAN_SIGN_UP = 2; // 未报名有资格
  SIGNED_UP = 3; // 已报名
  EXPIRED = 4; // 活动已结束
}

message UserActInfoReq {
  // mid
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"min=1,required"'];
  // 活动id
  int64 act_id = 2 [(gogoproto.moretags) = 'form:"act_id" validate:"min=1,required"'];
  // buvid
  string buvid = 3 [(gogoproto.moretags) = 'form:"buvid"'];
}

message UserActInfoResp {
  // 报名信息
  SignUpInfo sing_up_info = 1 [(gogoproto.jsontag) = "sing_up_info"];
  // 任务信息
  TaskInfo task_info = 2 [(gogoproto.jsontag) = "task_info"];
  // 奖励信息
  RewardInfo reward_info = 3 [(gogoproto.jsontag) = "reward_info"];
}

message SignUpInfo {
  // 报名状态
  SignUpStatus sign_status = 1 [(gogoproto.jsontag) = "sign_status"];
}

message TaskInfo {
  // 任务列表
  repeated Task tasks = 1 [(gogoproto.jsontag) = "tasks"];
}

message RewardInfo {
  // 奖励数目
  int64 reward_amount = 1 [(gogoproto.jsontag) = "reward_amount"];
  // 奖励名称
  string reward_name = 2 [(gogoproto.jsontag) = "reward_name"];
  // 阶梯奖励信息
  repeated RewardStep reward_steps = 3 [(gogoproto.jsontag) = "reward_steps"];
}

message RewardStep {
  // 奖励数目
  int64 reward_amount = 1 [(gogoproto.jsontag) = "reward_amount"];
  // 奖励链接
  string scheme = 2 [(gogoproto.jsontag) = "scheme"];
}

message Task {
  // 任务id
  int64 id = 1 [(gogoproto.jsontag) = "id"];
  // 任务名
  string title = 2 [(gogoproto.jsontag) = "title"];
  // 任务描述
  string remark = 3 [(gogoproto.jsontag) = "remark"];
  // Icon
  string icon = 4 [(gogoproto.jsontag) = "icon"];
  // 当前进度值
  int64 progress = 5 [(gogoproto.jsontag) = "progress"];
  // 任务目标值
  int64 target = 6 [(gogoproto.jsontag) = "target"];
  // 任务状态。1-待完成，2-已完成，3-已结束
  int64 status = 7 [(gogoproto.jsontag) = "status"];
  // 奖励类型。1-成长值
  int64 reward_type = 8 [(gogoproto.jsontag) = "reward_type"];
  // 奖励数目
  int64 reward_amount = 9 [(gogoproto.jsontag) = "reward_amount"];
  // 奖励名称
  string reward_name = 10 [(gogoproto.jsontag) = "reward_name"];
  // 跳转链接
  string scheme = 11 [(gogoproto.jsontag) = "scheme"];
  // 按钮文案
  string label = 12 [(gogoproto.jsontag) = "label"];
  // 主播任务Id
  int64 anchor_task_id = 13 [(gogoproto.jsontag) = "anchor_task_id"];
}

message IsSignUpReq {
  // mid
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"min=1,required"'];
  // 活动id
  int64 act_id = 2 [(gogoproto.moretags) = 'form:"act_id" validate:"min=1,required"'];
}

message IsSignUpResp {
  // 报名结果查询
  bool result = 1 [(gogoproto.jsontag) = "result"];
}

message StatLikeReq {
  // mid
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"min=1,required"'];
  // 活动id
  int64 up_mid = 2 [(gogoproto.moretags) = 'form:"up_mid" validate:"min=1,required"'];
  // aid
  int64 aid = 3 [(gogoproto.moretags) = 'form:"aid" validate:"min=1,required"'];
}

message StatLikeResp{}