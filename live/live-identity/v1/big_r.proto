syntax = "proto3";
package live.liveidentity.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/live/live.identity.v1;v1";
option java_package = "com.bapis.live.live.identity.v1";
option java_multiple_files = true;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
// import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "live/xuserreward/title.proto";
import "live/order/v2/order.proto";
import "live/user-extra/api.proto";

option (wdcli.appid) = "live.liveidentity";

service BigR {
  rpc GetBigRUserInfo (GetBigRUserInfoReq) returns (GetBigRUserInfoResp);
  rpc UpdateBigRUserInfo (UpdateBigRUserInfoReq) returns (UpdateBigRUserInfoResp);
  rpc IsBigR (IsBigRReq) returns (IsBigRResp);
  // 导入大R接口
  rpc ImportFromCsv (ImportFromCsvReq) returns (ImportFromCsvResp);
  rpc GetProtectUserInfo (GetProtectUserInfoReq) returns (GetProtectUserInfoResp);
  rpc GetProtectUserHistoryInfo (GetProtectUserHistoryInfoReq) returns (GetProtectUserHistoryInfoResp);

  // 创建or更新大R身份信息(新)
  rpc UpsertUserIdentityForBigRNew(UpsertUserIdentityForBigRReq) returns (UpsertUserIdentityForBigRResp);
  // 创建or更新大R身份信息V2(根据wealth每月用户消费数据写入大R身份)
  rpc UpsertUserIdentityForBigRV2(UpsertUserIdentityForBigRV2Req) returns (UpsertUserIdentityForBigRResp);
  // 查询用户外显"尊享俱乐部"个人信息页
  rpc GetUserBigRForClub(GetUserBigRForClubReq) returns (GetUserBigRForClubResp);
  // 查询用户是否有资格弹出"尊享俱乐部(大R分级)"入口
  rpc IsAdmittedForBigRClubEntrance(IsAdmittedForBigRClubEntranceReq) returns (IsAdmittedForBigRClubEntranceResp);
  // 领取大R分级礼物
  rpc ClickBigRLevelGiftsForClub(ClickBigRLevelGiftsForClubReq) returns (google.protobuf.Empty);
  // 大R用户查询可续期头衔列表
  rpc GetExtendUserTitlesForBigRUser(GetExtendUserTitlesForBigRUserReq) returns (GetExtendUserTitlesForBigRUserResp);
  // 大R用户续期头衔
  rpc ExtendUserTitleForBigRUser(ExtendUserTitleForBigRUserReq) returns (google.protobuf.Empty);
  // 处理大R-Binlog消息发放权益及广播
  rpc HandleBigRUserIdentityBinlog(HandleBigRUserIdentityBinlogReq) returns (google.protobuf.Empty);
  // 处理营收消息发放"航海之王"头衔权益
  rpc HandleBigRRevenueEvent(HandleBigRRevenueEventReq) returns (google.protobuf.Empty);
  // 脚本查询老大R写入新大R用户数据
  rpc HandleBigRMigrateScript(HandleBigRMigrateScriptReq) returns (google.protobuf.Empty);
  // 大R用户设置神秘人身份权限校验
  rpc CheckMysteryForBigRUser(CheckMysteryForBigRUserReq) returns (CheckMysteryForBigRUserResp);

  // 存储用户每月消费数据缓存
  rpc SaveUserMonthConsumeCache(SaveUserMonthConsumeCacheReq) returns (google.protobuf.Empty);

  // 查询用户是否为潜在大R
  rpc IsMediumR(IsMediumReq) returns (IsMediumRsp);
  // 向潜在大R推送购买一键成为大R的礼包弹窗(进房回调, 接口内部判断是否推送)
  rpc MediumRPackagePush(MediumRPackagePushReq) returns (MediumRPackagePushRsp);
  // 是否开启尊享俱乐部一键礼包功能
  rpc IsBigPackageEnabled(IsBigPackageEnabledReq) returns (IsBigPackageEnabledRsp);
  // 根据当前uid流水情况获取推荐的购买礼物
  rpc GetBigRClubBagByRevenue(GetBigRClubBagByRevenueReq) returns (GetBigRClubBagByRevenueResp);
  // 根据当前选择的礼包id获取礼包信息 (尊享礼包一期已不投放）
  // @Deprecated
  rpc GetBigRClubSelectedBag(GetBigRClubSelectedBagReq) returns (GetBigRClubSelectedBagResp);
  // 获取所有礼包信息(尊享礼包一期已不投放）
  // @Deprecated
  rpc GetBigRClubAllBag(GetBigRClubAllBagReq) returns (GetBigRClubAllBagResp);
  // 购买礼包预检(尊享礼包一期已不投放）
  // @Deprecated
  rpc BigRClubBagPreCheck(.live.order.CommonPreCheckReq) returns(.live.order.CommonPreCheckResp);
  // 购买礼包发货(尊享礼包一期已不投放）
  // @Deprecated
  rpc BigRClubBagDelivery(.live.order.CommonDeliveryReq) returns(.live.order.CommonDeliveryResp);
  // 超能礼包预检
  rpc SuperPowerBagPreCheck(.live.order.CommonPreCheckReq) returns(.live.order.CommonPreCheckResp);
  // 超能礼包发货
  rpc SuperPowerBagDelivery(.live.order.CommonDeliveryReq) returns(.live.order.CommonDeliveryResp);
  // 消费revenueEvent记录首次购买信息
  rpc HandleRevenueEventForBigRClubBag(HandleRevenueEventForBigRClubBagReq) returns (HandleRevenueEventForBigRClubBagResp);
  // 神秘人购买页面
  rpc GetMysteryBuyPage(GetMysteryBuyPageReq) returns (GetMysteryBuyPageResp);
  // 神秘人购买预检
  rpc MysteryBuyPreCheck(.live.order.CommonPreCheckReq) returns(.live.order.CommonPreCheckResp);
  // 神秘人发货
  rpc MysteryBuyDelivery(.live.order.CommonDeliveryReq) returns(.live.order.CommonDeliveryResp);
  // 会员商城，超能装扮更新与插入
  rpc SetShowEntry(SetShowEntryReq) returns (SetShowEntryResp);
  // 会员商城，超能装扮查询
  rpc QueryShowEntry(QueryShowEntryReq) returns (QueryShowEntryResp);
  // 会员商城，超能装扮删除
  rpc DelShowEntry(DelShowEntryReq) returns (DelShowEntryResp);
  // 会员商城，超能装扮上下线
  rpc OnlineShowEntry(OnlineEntryReq) returns (OnlineEntryResp);

  // 隐身特权购买页面
  rpc UserInvisibilityBuyPage(GetUserInvisibilityBuyPageReq) returns (GetUserInvisibilityBuyPageRsp);
  // 隐身特权预检
  rpc UserInvisibilityPreCheck(.live.order.CommonPreCheckReq) returns(.live.order.CommonPreCheckResp);
  // 隐身特权发货
  rpc UserInvisibilityDelivery(.live.order.CommonDeliveryReq) returns(.live.order.CommonDeliveryResp);
  // @Deprecated 超能礼包（只处理非大R），h5上线后可直接下线
  rpc GetAllSuperPowerBagInfo(GetAllSuperPowerBagInfoReq) returns (GetAllSuperPowerBagInfoResp);
  // 超能礼包2期，处理所有用户，且大R和非大R可配
  rpc GetAllSuperPowerBagInfoV2(GetAllSuperPowerBagInfoReq) returns (GetAllSuperPowerBagInfoResp);
}

message GetAllSuperPowerBagInfoReq {
  int64 uid = 1;
}

message GetAllSuperPowerBagInfoResp {
  repeated SuperPowerBagInfo bag_list = 1 [(gogoproto.jsontag) = "bag_list"];
  int64 rule_id = 2 [(gogoproto.jsontag) = "rule_id"];
  // 是否开放当前礼包
  bool is_open = 3 [(gogoproto.jsontag) = "is_open"];
}

message SuperPowerBagInfo {
  // @Deprecated 根据用户身份智能选择可购买的礼包，默认全为true
  bool can_buy = 1 [(gogoproto.jsontag) = "can_buy"];
  // 与前端约定的礼包key标识
  string bag_key = 2 [(gogoproto.jsontag) = "bag_key"];
  // 购买价格(单位: 金瓜子)
  int64 price = 3 [(gogoproto.jsontag) = "price"];
  // 实际总价值用于展示
  int64 total_show_price =4  [(gogoproto.jsontag) = "total_show_price"];
  // 商品id
  int64 goods_id = 5 [(gogoproto.jsontag) = "goods_id"];
  // 充值电池
  int64 recharge_battery = 6 [(gogoproto.jsontag) = "recharge_battery"];
  // 包裹礼物模块角标图片
  string wrap_gift_corner_pic = 7 [(gogoproto.jsontag) = "wrap_gift_corner_pic"];
  // 包裹礼物list
  repeated SuperPowerBagItem wrap_gift_list = 8 [(gogoproto.jsontag) = "wrap_gift_list"];
  // 限定礼物和进场特效的角标图片
  string special_gift_entry_corner_pic = 9 [(gogoproto.jsontag) = "special_gift_entry_corner_pic"];
  repeated SuperPowerBagItem special_gift_entry_list = 10 [(gogoproto.jsontag) = "special_gift_entry_list"];
  // 商城角标图片
  string mall_list_corner_pic = 11 [(gogoproto.jsontag) = "mall_list_corner_pic"];
  repeated SuperPowerBagItem mall_list = 12 [(gogoproto.jsontag) = "mall_list"];
  // 档位图片
  string tag_tab_pic = 13 [(gogoproto.jsontag) = "tag_tab_pic"];
  // 档位激活图片
  string tag_tab_active_pic = 14 [(gogoproto.jsontag) = "tag_tab_active_pic"];
  // 礼包名称
  string bag_name = 15 [(gogoproto.jsontag) = "bag_name"];
}

message SuperPowerBagItem {
  int64 gift_id = 1 [(gogoproto.jsontag) = "gift_id"];
  // 购买价格(单位: 金瓜子)
  int64 price = 2 [(gogoproto.jsontag) = "price"];
  string gift_name = 3 [(gogoproto.jsontag) = "gift_name"];
  string pic = 4 [(gogoproto.jsontag) = "pic"];
  string corner = 5 [(gogoproto.jsontag) = "corner"];
  string desc = 6 [(gogoproto.jsontag) = "desc"];
}



message GetUserInvisibilityBuyPageReq {
  int64 uid = 1;
}

message GetUserInvisibilityBuyPageRsp {
  // 当前请求用户是否可以购买
  bool can_buy = 1 [(gogoproto.jsontag) = "can_buy"];
  // 购买价格(单位: 电池)
  int64 origin_price = 2 [(gogoproto.jsontag) = "origin_price"];
  // 购买有效期
  int64 valid_day = 3 [(gogoproto.jsontag) = "valid_day"];
  // 神秘人套装的商品id
  int64 goods_id = 4 [(gogoproto.jsontag) = "goods_id"];
  // 当前用户的隐身功能有效期，0的话表示用户当前没有有效期内的隐身功能(单位: 秒级时间戳)
  int64 cur_expire_time = 5 [(gogoproto.jsontag) = "cur_expire_time"];
  // 本次购买后的有效期(单位: 秒级时间戳)
  int64 expire_time_after_buy = 6 [(gogoproto.jsontag) = "expire_time_after_buy"];
  // 用户隐身功能是否可用(用户已购买&在有效期内&可以使用)
  bool is_available = 7 [(gogoproto.jsontag) = "is_available"];
  // 用户当前隐身功能是否开启. 1: 关闭, 2: 开启.
  .live.live_infra.user_extra.v1.UserInvisibilityStat stat = 8 [(gogoproto.jsontag) = "stat"];
  // 规则文案id
  int64 service_rule_id = 9 [(gogoproto.jsontag) = "service_rule_id"];
}

message IsBigPackageEnabledReq {

}
message IsBigPackageEnabledRsp {
  bool is_enabled = 1;
}

message MediumRPackagePushReq {
  int64 mid = 1;
  int64 room_id = 2;
  string mobi = 3;
  int64 build = 4;
  int64 ts = 5;
}

message MediumRPackagePushRsp {}

message IsMediumReq {
  int64 mid = 1;
}

message IsMediumRsp {
  bool is_medium_r = 1;
}

message CheckMysteryForBigRUserReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
}

message CheckMysteryForBigRUserResp {
  // 是否允许设置神秘人身份
  bool is_permitted = 1 [(gogoproto.jsontag) = "is_permitted"];
  // 可以设置为神秘人身份的有效期时间戳 (is_permitted = true有值)
  int64 end_time = 2 [(gogoproto.jsontag) = "end_time"];
}

message HandleBigRMigrateScriptReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
}

message SaveUserMonthConsumeCacheReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
  // 对应月份
  string year_month_str = 2 [(gogoproto.moretags) = 'form:"year_month_str" validate:"required"'];
  // 用户每月消费累计值(金瓜子数)
  int64 monthly_consume = 3 [(gogoproto.moretags) = 'form:"monthly_consume" validate:"required,gt=0"'];
  // 对应月份的上个月最后一笔订单时间戳
  int64 last_order_time = 4 [(gogoproto.moretags) = 'form:"last_order_time"'];
}

message UpsertUserIdentityForBigRReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
  // 对应大R分级
  int64 big_r_level = 2 [(gogoproto.moretags) = 'form:"big_r_level"'];
  // 大R分级开始时间 (订单创建时间)
  int64 start_time = 3 [(gogoproto.moretags) = 'form:"start_time" validate:"required,gt=0"'];
  // 大R分级结束时间 (分级对应的结束时间)
  int64 end_time = 4 [(gogoproto.moretags) = 'form:"end_time" validate:"required,gt=0"'];
  // 幂等key(弃用，由live-identity生成)
  string msg_id = 5 [(gogoproto.moretags) = 'form:"msg_id"'];
}

enum UpsertBigRResult {
  // 无
  UBRR_NONE = 0;
  // 成功
  UBRR_SUCCESS = 1;
  // 已存在该身份
  UBRR_EXISTED = 2;
  // 失败接口直接报错
}

message UpsertUserIdentityForBigRV2Req {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'validate:"required,gt=0"'];
}

message UpsertUserIdentityForBigRResp {
  // 操作结果 0 无意义 1 成功 2 已存在该身份
  UpsertBigRResult result = 1 [(gogoproto.jsontag) = "result"];
}

message GetExtendUserTitlesForBigRUserReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
  // 分页页码
  int64 page_num = 2 [(gogoproto.moretags) = 'form:"page_num" validate:"required,gt=0"'];
  // 分页大小
  int64 page_size = 3 [(gogoproto.moretags) = 'form:"page_size" validate:"required,gt=0,lt=11"'];
}

message GetExtendUserTitlesForBigRUserResp {
  // 用户uid
  int64 uid = 1 [(gogoproto.jsontag) = "uid"];
  // 头衔列表
  repeated live.xuserreward.listItem list = 2 [(gogoproto.jsontag) = "list"];
  // PageInfo
  live.xuserreward.PageInfo page = 3 [(gogoproto.jsontag) = "page"];
}

message ExtendUserTitleForBigRUserReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
  // 头衔tid
  int64 tid = 2 [(gogoproto.moretags) = 'form:"tid" validate:"required,gt=0"'];
}

message ClickBigRLevelGiftsForClubReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
}

message HandleBigRUserIdentityBinlogReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
}

message HandleBigRRevenueEventReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
  // 每月充值大航海数量
  int64 month_count = 2 [(gogoproto.moretags) = 'form:"month_count" validate:"required"'];
  // 幂等id
  string idempotent_id = 3 [(gogoproto.moretags) = 'form:"idempotent_id" validate:"required"'];
}

message HandleBigRUserIdentityBinlogResp {
  // 是否允许进入
  bool is_admitted = 1 [(gogoproto.jsontag) = "is_admitted"];
  // icon图片
  string icon_img_url = 2 [(gogoproto.jsontag) = "icon_img_url"];
  // icon跳转链接
  string jump_url = 3 [(gogoproto.jsontag) = "jump_url"];
  // 图标红点展示 (0:不展示 1:展示)
  int64 red_point = 4 [(gogoproto.jsontag) = "red_point"];
}

message IsAdmittedForBigRClubEntranceReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid"'];
}

message IsAdmittedForBigRClubEntranceResp {
  // 是否允许进入
  bool is_admitted = 1 [(gogoproto.jsontag) = "is_admitted"];
  // app端icon图片
  string icon_img_url = 2 [(gogoproto.jsontag) = "icon_img_url"];
  // app端icon跳转链接
  string jump_url = 3 [(gogoproto.jsontag) = "jump_url"];
  // 图标红点展示 (0:不展示 1:展示)
  int64 red_point = 4 [(gogoproto.jsontag) = "red_point"];
  // web端icon图片
  string web_icon_img_url = 5 [(gogoproto.jsontag) = "web_icon_img_url"];
  // web端icon跳转链接
  string web_jump_url = 6 [(gogoproto.jsontag) = "web_jump_url"];
}

message GetUserBigRForClubReq {
  // 用户uid
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
  // platform  ios  android pc
  string platform = 2 [(gogoproto.moretags) = 'form:"platform"'];
}

message GetUserBigRForClubResp {
  // "尊享俱乐部"用户信息
  BigRClubUserInfo user_info = 1 [(gogoproto.jsontag) = "user_info"];
}

message BigRClubUserInfo {
  // 用户uid
  int64 uid = 1 [(gogoproto.jsontag) = "uid"];
  // 是否为大R用户
  bool is_big_r = 2 [(gogoproto.jsontag) = "is_big_r"];
  // 当前用户大R分级 (is_big_r = true 才有值)
  int64 cur_level = 3 [(gogoproto.jsontag) = "cur_level"];
  // 本月总投喂值 (100金瓜子 = 1投喂值)
  int64 month_total_consume = 4 [(gogoproto.jsontag) = "month_total_consume"];
  // 升级至下一等级所需投喂值 (100金瓜子 = 1投喂值)
  int64 upgrade_need_consume = 5 [(gogoproto.jsontag) = "upgrade_need_consume"];
  // 是否已建联
  bool is_binding = 6 [(gogoproto.jsontag) = "is_binding"];
  // 是否发放过航海之王头衔
  bool has_sent_guard = 7 [(gogoproto.jsontag) = "has_sent_guard"];
  // 是否发放过连续两个月达标头衔
  bool has_sent_twice_title = 8 [(gogoproto.jsontag) = "has_sent_twice_title"];
  // 大R分级信息 (按数组有序，只有[]/[1,2,3]这两种组合)
  repeated BigRLevel level_infos = 9 [(gogoproto.jsontag) = "level_infos"];
  // 是否已领取当前等级礼物
  BigRLevelRightsStatusEnum has_got_gifts = 10 [(gogoproto.jsontag) = "has_got_gifts"];
  // 是否已使用过当前等级头衔续期卡
  BigRLevelRightsStatusEnum has_used_extend_title = 11 [(gogoproto.jsontag) = "has_used_extend_title"];
  // 所有大R分级可操作资源
  repeated AllBigROperatedResource all_level_resources = 12 [(gogoproto.jsontag) = "all_level_resources"];
  // 神秘人信息
  Mystery mystery_info = 13 [(gogoproto.jsontag) = "mystery_info"];
  // 是否允许进入尊享商城
  bool is_admitted_for_mall = 14 [(gogoproto.jsontag) = "is_admitted_for_mall"];
  // 商城信息
  repeated BirRClubMallInfo mall_list =   15 [(gogoproto.jsontag) = "mall_list"];
  //超能装扮信息
  repeated BigRClubShowEntryInfo super_show_list = 16 [(gogoproto.jsontag) = "super_show_list"];
  //会员商城信息
  repeated  BigRClubShowEntryInfo vip_mall_list = 17 [(gogoproto.jsontag) = "vip_mall_list"];
}

//尊享商城信息
message BirRClubMallInfo {
  //标题
  string title = 1 [(gogoproto.jsontag) = "title"];
  //副标题
  string sub_title  = 2 [(gogoproto.jsontag) = "sub_title"];
  //图片
  string icon =   3 [(gogoproto.jsontag) = "icon"];
  // type 1表示神秘人 2表示尊享装扮守护
  int64 mall_type = 4 [(gogoproto.jsontag) = "mall_type"];

}
//会员商城、超能装扮信息
message BigRClubShowEntryInfo{
  // id
  int64 id = 1 [(gogoproto.jsontag) = "id"];
  // 展示类型,会员商城为1，超能装扮为2
  int64 type = 2 [(gogoproto.jsontag) = "type"];
  //主标题
  string text = 3 [(gogoproto.jsontag) = "text"];
  //副标题
  string  sub_text = 4 [(gogoproto.jsontag) = "sub_text"];
  //背景大图
  string back_icon = 5 [(gogoproto.jsontag) = "back_icon"];
  //轮博小图
  string show_icon = 6 [(gogoproto.jsontag) = "show_icon"];
  //获取方式
  string get_way = 7 [(gogoproto.jsontag) = "get_way"];
  //跳转链接
  string  jump_url = 8 [(gogoproto.jsontag) = "jump_url"];
  //展示顺序（1-100）
  int64 show_order = 9 [(gogoproto.jsontag) = "show_order"];
  //上线状态，1表示上线，2表示未上线
  int64 state = 10 [(gogoproto.jsontag) = "state"];
  // 可见用户限制，0表示所有用户可见，1表示仅大R可见，2表示仅非大R可见
  int64 visible_user = 11 [(gogoproto.jsontag) = "visible_user"];
  // 可见平台限制，ios/android/web，不得传空
  repeated string  visible_platform = 12 [(gogoproto.jsontag) = "visible_platform"];
}


message BigRLevel {
  // 大R分级
  int64 level = 1 [(gogoproto.jsontag) = "level"];
  // 是否已解锁该分级
  bool has_unlocked = 2 [(gogoproto.jsontag) = "is_unlock"];
  // 结束时间 (has_unlocked = true有值)
  string end_time = 3 [(gogoproto.jsontag) = "end_time"];
  // 达标所需投喂值 (100金瓜子 = 1投喂值)
  int64 level_target_consume = 4 [(gogoproto.jsontag) = "level_target_consume"];
}

message AllBigROperatedResource {
  // 所有大R分级可领取礼物
  repeated BigROperatedResource gift_resource = 1 [(gogoproto.jsontag) = "gift_resource"];
  // 所有大R分级续期卡
  BigROperatedResource extend_title_resource = 2 [(gogoproto.jsontag) = "extend_title_resource"];
}

// BigROperatedResource 大R当前等级可领取资源
message BigROperatedResource {
  // 图片url
  string img_url = 1 [(gogoproto.jsontag) = "img_url"];
  // 资源名称
  string name = 2 [(gogoproto.jsontag) = "name"];
  // 资源数量
  int64 num = 3 [(gogoproto.jsontag) = "num"];
}

enum BigRLevelRightsStatusEnum {
  // 可领取
  CAN_GET = 0;
  // 已领取
  HAS_GOT = 1;
}

message GetProtectUserInfoReq {
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"', json_name = "uid"];
}

message GetProtectUserInfoResp {
  // 0:不是保护用户 1:是保护用户
  int64 is_protected = 1 [(gogoproto.jsontag) = "is_protected", json_name = "is_protected"];
}

message GetProtectUserHistoryInfoReq {
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
}

message GetProtectUserHistoryInfoResp {
  // 状态 0:不生效 1:生效 2:历史上生效过
  int64 status = 1 [(gogoproto.jsontag) = "status"];
  // 用户昵称
  string user_name = 2 [(gogoproto.jsontag) = "user_name"];
  // 生效类型 0:号码包 1:系统录入
  int64 valid_type = 3 [(gogoproto.jsontag) = "valid_type"];
  // 用户分类
  string user_class = 4 [(gogoproto.jsontag) = "user_class"];
  // 生效时间
  string start = 5 [(gogoproto.jsontag) = "start"];
  // 失效时间
  string end = 6 [(gogoproto.jsontag) = "end"];
  // UID
  int64 uid = 7 [(gogoproto.jsontag) = "uid"];
}

message UpdateBigRUserInfoReq {
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
  string mobile_number = 2 [(gogoproto.moretags) = 'form:"mobile_number" validate:"required"'];
  string birthday = 3 [(gogoproto.moretags) = 'form:"birthday"'];
  string address = 4 [(gogoproto.moretags) = 'form:"address"'];
}

message UpdateBigRUserInfoResp {}

message GetBigRUserInfoReq {
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
}

message GetBigRUserInfoResp{
  int64 ID = 1 [(gogoproto.jsontag) = "id"];
  string MobileNumber = 2 [(gogoproto.jsontag) = "mobile_number"];
  string birthday = 3 [(gogoproto.moretags) = 'form:"birthday"'];
  string address = 4 [(gogoproto.moretags) = 'form:"address"'];
  string mtime = 5 [(gogoproto.jsontag) = "mtime"];
}

message IsBigRReq {
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
}

message IsBigRResp {
  // 0: 非大R 1:大R
  int64 is_big_r = 1 [(gogoproto.jsontag) = "is_big_r", json_name = "is_big_r"];
  int64 tag = 2 [(gogoproto.jsontag) = "tag"];
  // 大R当前状态 0 - 无大R邀请 1-有大R邀请，未填报 2-有大R邀请，已填报
  BigRConnectStatus big_r_status = 3 [(gogoproto.jsontag) = "big_r_status"];
}
enum BigRConnectStatus{
  NotBigR = 0;
  BigRNoConnect = 1;
  BigRConnected = 2;
}

message ImportFromCsvReq {
  string url = 1 [(gogoproto.moretags) = 'form:"url" validate:"required"'];
}

message ImportFromCsvResp {}


// 神秘人信息结构
message Mystery {
  // 是否为神秘人
  bool is_mystery = 1 [(gogoproto.jsontag) = "is_mystery", json_name = "is_mystery"];
  // 神秘人昵称
  string mystery_name = 2 [(gogoproto.jsontag) = "mystery_name", json_name = "mystery_name"];
}

message GetBigRClubBagByRevenueReq {
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
}

message GetBigRClubBagByRevenueResp {
  repeated BigRClubSortedBagInfo bag_list = 1 [(gogoproto.jsontag) = "bag_list"];
}

message GetBigRClubSelectedBagReq {
  int64 bag_id = 1 [(gogoproto.moretags) = 'form:"bag_id" validate:"required"'];
}

message GetBigRClubSelectedBagResp {
  // 当前礼包id，KV配置时定义
  int64 bag_id = 1 [(gogoproto.jsontag) = "bag_id"];
  // 当前礼包价格，单位为电池
  int64 bag_price = 2 [(gogoproto.jsontag) = "bag_price"];
  // 当前礼包对应的商品id
  int64 goods_id = 3 [(gogoproto.jsontag) = "goods_id"];
  // 当前礼包的所有档位礼物
  repeated BigRClubLevelGift level_gift_list = 4 [(gogoproto.jsontag) = "level_gift_list"];
  BigRClubSpecialGiftAward special_gift_award = 5 [(gogoproto.jsontag) = "special_gift_award"];
  BigRClubEffectEntry effect_entry = 6 [(gogoproto.jsontag) = "effect_entry"];
}
// 尊享礼包档位礼物
message BigRClubLevelGift {
  int64 level_id = 1 [(gogoproto.jsontag) = "level_id"];
  int64 level_selectable_count = 2  [(gogoproto.jsontag) = "level_selectable_count"];
  repeated BigRClubGiftDetail gift_list = 3  [(gogoproto.jsontag) = "gift_list"];
}


message GetBigRClubAllBagReq {

}

message GetBigRClubAllBagResp {
  repeated BigRClubSortedBagInfo bag_list = 1 [(gogoproto.jsontag) = "bag_list"];
}


// 尊享礼包档位礼物汇总排序后信息
message BigRClubSortedBagInfo {
  // 当前礼包id，KV配置时定义
  int64 bag_id = 1 [(gogoproto.jsontag) = "bag_id"];
  // 当前礼包价格，单位为电池
  int64 bag_price = 2 [(gogoproto.jsontag) = "bag_price"];
  // 当前礼包对应的商品id
  int64 goods_id = 3 [(gogoproto.jsontag) = "goods_id"];
  // 当前礼包可选的包裹礼物的总个数
  int64 selectable_total_count = 4 [(gogoproto.jsontag) = "selectable_total_count"];
  // 当前礼包可选的所有档位礼物排序后
  repeated BigRClubGiftDetail selectable_gift_list = 5 [(gogoproto.jsontag) = "selectable_gift_list"];
  // 当前礼包的专属礼物使用权
  BigRClubSpecialGiftAward special_gift_award = 6 [(gogoproto.jsontag) = "special_gift_award"];
  // 当前礼包的进场特效
  BigRClubEffectEntry effect_entry = 7  [(gogoproto.jsontag) = "effect_entry"];
}


// 尊享俱乐部包裹礼物可选礼物
message BigRClubGiftDetail {
  int64 gift_id = 1 [(gogoproto.jsontag) = "gift_id"];
  int64 gift_type = 2 [(gogoproto.jsontag) = "gift_type"];
  string gift_name = 3 [(gogoproto.jsontag) = "gift_name"];
  string gift_pic = 4 [(gogoproto.jsontag) = "gift_pic"];
  // 单位为电池
  int64 gift_price = 5 [(gogoproto.jsontag) = "gift_price"];
  // 终端展示的角标图片
  string corner_pic = 6 [(gogoproto.jsontag) = "corner_pic"];
}

// 尊享俱乐部购买礼包专属礼物使用权
message BigRClubSpecialGiftAward {
  int64 gift_id = 1 [(gogoproto.jsontag) = "gift_id"];
  string name = 2 [(gogoproto.jsontag) = "name"];
  string pic = 3 [(gogoproto.jsontag) = "pic"];
  string award_desc = 4 [(gogoproto.jsontag) = "award_desc"];
}

// 尊享俱乐部购买礼包进场特效
message BigRClubEffectEntry {
  int64 id = 1 [(gogoproto.jsontag) = "id"];
  string name = 2 [(gogoproto.jsontag) = "name"];
  string pic = 3 [(gogoproto.jsontag) = "pic"];
  string award_desc = 4 [(gogoproto.jsontag) = "award_desc"];
}

message HandleRevenueEventForBigRClubBagReq {
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid"'];
  string order_id = 2[(gogoproto.moretags) = 'form:"order_id"'];
  int64 goods_id = 3[(gogoproto.moretags) = 'form:"goods_id"'];
  string mobile_app = 4[(gogoproto.moretags) = 'form:"mobile_app"'];
  int64 create_time = 5[(gogoproto.moretags) = 'form:"create_time"'];
  int64 pay_time = 6[(gogoproto.moretags) = 'form:"pay_time"'];
}

message HandleRevenueEventForBigRClubBagResp {
  bool succ = 1 [(gogoproto.jsontag) = "succ"];
}


message GetMysteryBuyPageReq {
  int64 uid = 1   [(gogoproto.jsontag) = "uid"];
}

message GetMysteryBuyPageResp {
  // 神秘人套装购买信息
  // 神秘人套装原价
  int64 origin_price = 1 [(gogoproto.jsontag) = "origin_price"];
  // 神秘人套装折扣价，该价格不为0时表示有折扣价
  int64 discount_price = 2 [(gogoproto.jsontag) = "discount_price"];
  // 当前用户的大R身份等级
  int64 cur_big_r_level = 3   [(gogoproto.jsontag) = "cur_big_r_level"];
  // 神秘人套装有效期
  int64 valid_day = 4 [(gogoproto.jsontag) = "valid_day"];
  // 当前请求用户是否可以购买
  bool can_buy = 5 [(gogoproto.jsontag) = "can_buy"];
  // 神秘人套装的商品id
  int64 goods_id = 6 [(gogoproto.jsontag) = "goods_id"];
  // 当前用户的神秘人有效期，0的话表示用户当前没有有效期内的神秘人
  int64 cur_expire_time = 7 [(gogoproto.jsontag) = "cur_expire_time"];
  // 本次购买后的有效期
  int64 expire_time_after_buy = 8 [(gogoproto.jsontag) = "expire_time_after_buy"];
  // 神秘人开关模块是否展示
  bool mystery_switch_can_show = 9 [(gogoproto.jsontag) = "mystery_switch_can_show"];
  // 用户当前神秘人功能是否开启
  bool is_mystery_open = 10 [(gogoproto.jsontag) = "is_mystery_open"];
  // 用户当前使用的神秘人昵称
  string mystery_name = 11 [(gogoproto.jsontag) = "mystery_name"];
  // 规则文案id
  int64 service_rule_id = 12 [(gogoproto.jsontag) = "service_rule_id"];
  int64 uid   = 13 [(gogoproto.jsontag) = "uid"];
}
message SetShowEntryReq {
  // 具体信息
  BigRClubShowEntryInfo show_entry_info =1 [(gogoproto.jsontag) = "show_entry_info"];
}

message SetShowEntryResp{
}

message QueryShowEntryReq{
  // 页码,不传默认1
  int64 page = 1 [(gogoproto.jsontag) = "page"];
  // 页面长度,默认20条
  int64 page_size = 2 [(gogoproto.jsontag) = "page_size"];
}

message  QueryShowEntryResp{
  repeated  BigRClubShowEntryInfo show_entry_info =1 [(gogoproto.jsontag) = "show_entry_info"];
  // 页码
  int64 cur_page = 2 [(gogoproto.jsontag) = "cur_page"];
  // 页面长度
  int64 page_size = 3 [(gogoproto.jsontag) = "page_size"];
  // 总条数
  int64 total_count = 4 [(gogoproto.jsontag) = "total_count"];
}

message DelShowEntryReq{
  int64 id = 1 [(gogoproto.jsontag) = "id"];
}

message DelShowEntryResp{
}

message OnlineEntryReq{
  int64 id = 1 [(gogoproto.jsontag) = "id"];
  // 1表示上线，2表示未上线
  int64 if_online = 2 [(gogoproto.jsontag) = "if_online"];
}

message OnlineEntryResp{
}