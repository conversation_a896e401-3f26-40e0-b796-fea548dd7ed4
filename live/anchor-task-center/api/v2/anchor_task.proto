syntax = "proto3";
package live.anchor_task_v2;

option go_package = "buf.bilibili.co/bapis/bapis-gen/live/anchor.task.center.api.v2";
option java_package = "com.bapis.live.anchor.task.center.api.v2";
option java_multiple_files = true;
import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "live.live.anchor-task-center.v2";

// proto 编写手册
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
// import "google/api/annotations.proto"; // for http
// import "extension/bb/bb.proto"; // for bb

service AnchorTask {
  // 绑定任务
  rpc BindTask(BindTaskReq) returns (BindTaskResp);
  // 主播的任务列表
  rpc AnchorTask(AnchorTaskReq) returns (AnchorTaskResp);
  // 历史任务
  rpc HistoryAnchorTask(GetAnchorTaskHistoryReq) returns (GetAnchorTaskHistoryResp);
}

message GetAnchorTaskHistoryResp {
  //历史任务组列表
  repeated HistoryTaskGroup historyTaskGroupList = 1 [(gogoproto.jsontag) = "historyTaskGroupList"];
  // 规则页内容
  string rule = 2 [(gogoproto.jsontag) = "rule"];
  //page
  int64 page = 3 [(gogoproto.jsontag) = "page"];
  //page_size
  int64 pageSize = 4 [(gogoproto.jsontag) = "pageSize"];
}
message HistoryTaskGroup{
  // 任务组元信息
  TaskGroupBase anchorTaskGroup = 1 [(gogoproto.jsontag) = "anchorTaskGroup"];
  // 主播任务信息
  repeated AnchorBaseTasks anchorBaseTasks = 2 [(gogoproto.jsontag) = "anchorBaseTasks"];
}
message AnchorBaseTasks{
  // 任务id
  int64 taskId = 1 [(gogoproto.jsontag) = "taskId"];
  // 子任务等级信息
  repeated TaskLevels taskLevels = 2 [(gogoproto.jsontag) = "taskLevels"];
  // 奖励等级信息
  repeated RewardLevels rewardLevels = 3 [(gogoproto.jsontag) = "rewardLevels"];
}
message RewardLevels {
  //奖励等级
  int64 level = 1 [(gogoproto.jsontag) = "level"];
  //奖励信息
  repeated RewardLevelsDetail rewardInfo = 2 [(gogoproto.jsontag) = "rewardInfo"];
}
message RewardLevelsDetail {
  //奖励类型 1-金仓鼠 2-星光 3-实时星光
  int64 rewardType = 1 [(gogoproto.jsontag) = "rewardType"];
  //奖励数量
  double rewardNum = 2 [(gogoproto.jsontag) = "rewardNum"];
  //奖励名字
  string rewardName = 3 [(gogoproto.jsontag) = "rewardName"];
  //领奖时间，若完成了且领奖了的时间
  int64 rewardTime = 4 [(gogoproto.jsontag) = "rewardTime"];
  //当前层级下任务的状态
  int64 rewardStatus = 5 [(gogoproto.jsontag) = "rewardStatus"];
}
message TaskLevels{
  // 是否完成该等级
  int64 isFinished = 1 [(gogoproto.jsontag) = "isFinished"];
  // 任务等级
  int64 level = 2 [(gogoproto.jsontag) = "level"];
  // 该等级子任务目标
  repeated SubTaskTargets subTaskTargets = 3 [(gogoproto.jsontag) = "subTaskTargets"];
}
message SubTaskTargets {
  //子任务id
  int64 subTaskId = 1 [(gogoproto.jsontag) = "subTaskId"];
  //子任务类型
  int64 subTaskType = 2 [(gogoproto.jsontag) = "subTaskType"];
  //该等级指标目标值
  int64 targetNum = 3 [(gogoproto.jsontag) = "targetNum"];
  //该指标当前进度
  int64 currentNum = 4 [(gogoproto.jsontag) = "currentNum"];
  //h5文案
  string h5Text = 5 [(gogoproto.jsontag) = "h5Text"];
}
message TaskGroupBase{
  //任务组标题
  string title = 1 [(gogoproto.jsontag) = "title"];
  //任务组id
  int64 taskGroupId = 2 [(gogoproto.jsontag) = "taskGroupId"];
  //周期内 任务组开始时间
  int64 startTime = 3 [(gogoproto.jsontag) = "startTime"];
  //周期内 任务组结束时间
  int64 endTime = 4 [(gogoproto.jsontag) = "endTime"];
  //奖励发放类型：1最高档 2每档均发
  int64 rewardSentType = 5 [(gogoproto.jsontag) = "rewardSentType"];
  //历史任务奖励状态 0.未完成 1.奖励结算中 2.奖励待领取 3.奖励审核中 4.奖励审核失败 5.奖励已领取 6.奖励已过期
  int64 historyRewardStatus = 6 [(gogoproto.jsontag) = "historyRewardStatus"];
  //已领取的奖励组，拼好文案
  repeated string receivedRewardList = 7 [(gogoproto.jsontag) = "receivedRewardList"];
}

message GetAnchorTaskHistoryReq{
  // id
  int64 uid = 1 [(gogoproto.jsontag) = "uid"];
  // 多少天前 (最多60天)
  int64 daysAgo = 2 [(gogoproto.jsontag) = "daysAgo"];
  // page
  int64 page = 3 [(gogoproto.jsontag) = "page"];
  // page_size
  int64 pageSize = 4 [(gogoproto.jsontag) = "pageSize"];
  // 用户进入页面时间戳
  int64 getInTime = 5 [(gogoproto.jsontag) = "getInTime"];
}

message AnchorTaskReq {
  //主播id
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required,gt=0"'];
  // 公会gid
  int64 gid = 2 [(gogoproto.moretags) = 'form:"gid"'];
  // 人群维度 0:主播维度 2:公会维度
  int64 crowd_dimension = 3 [(gogoproto.moretags) = 'form:"crowd_dimension"'];
  // 符合这个周期时间的任务，默认当前时间
  int64 cycle_time = 4 [(gogoproto.moretags) = 'form:"cycle_time"'];
  // 一级业务线
  int64 first_business_line = 6 [(gogoproto.moretags) = 'form:"first_business_line"'];
  // 二级业务线
  int64 second_business_line = 7 [(gogoproto.moretags) = 'form:"second_business_line"'];
  // 三级业务线
  int64 third_business_line = 8 [(gogoproto.moretags) = 'form:"third_business_line"'];
  // 任务组标签
  int64 task_group_tag = 9 [(gogoproto.moretags) = 'form:"task_group_tag"'];
}

message AnchorTaskResp {
  //任务组信息
  repeated AnchorTaskGroup anchor_task_groups = 1 [(gogoproto.jsontag) = "anchor_task_groups"];
}

// 主播任务组信息，跟随用户
message AnchorTaskGroup {
  // 任务组元信息
  TaskGroup task_group = 1 [(gogoproto.jsontag) = "task_group"];
  // 主播任务信息，跟随用户
  repeated AnchorBaseTask anchor_base_tasks = 6 [(gogoproto.jsontag) = "anchor_base_tasks"];
}

// 任务组元信息，不跟随用户
message TaskGroup {
  //任务组ID
  int64 task_group_id = 1 [(gogoproto.jsontag) = "task_group_id"];
  //任务组名
  string title = 2 [(gogoproto.jsontag) = "title"];
  //任务组类型 1-门槛任务[互斥]/新人任务 2-单次任务 3-每日任务 4-每周任务
  int64 task_group_biz = 4 [(gogoproto.jsontag) = "task_group_biz"];
  // 任务组横幅说明
  string task_group_banner = 7 [(gogoproto.jsontag) = "task_group_banner"];
  // 一级业务线
  int64 first_business_line = 8 [(gogoproto.jsontag) = "first_business_line"];
  // 二级业务线
  int64 second_business_line = 9 [(gogoproto.jsontag) = "second_business_line"];
  // 三级业务线
  int64 third_business_line = 10 [(gogoproto.jsontag) = "third_business_line"];
}

// 主播基础任务，跟随用户
message AnchorBaseTask {
  // 基础任务元信息，不跟随用户变化
  BaseTask base_task = 1 [(gogoproto.jsontag) = "base_task"];
  // 子任务等级
  repeated AnchorLevel anchor_levels = 6 [(gogoproto.jsontag) = "anchor_levels"];
  // 当前等级奖励信息
  repeated AnchorSubTaskProgress anchor_sub_task_progress = 4 [(gogoproto.jsontag) = "anchor_sub_task_progress"];
}

// 基础任务元信息，不跟随用户
message BaseTask {
  // 任务id
  int64 task_id = 1 [(gogoproto.jsontag) = "task_id"];
  // 任务组id
  int64 task_group_id = 2 [(gogoproto.jsontag) = "task_group_id"];
  // 任务名称
  string title = 3 [(gogoproto.jsontag) = "title"];
  // 任务说明
  string task_remark = 5 [(gogoproto.jsontag) = "task_remark"];

}


message AnchorLevel {
  // 是否完成该等级任务
  int64 is_finished = 1 [(gogoproto.jsontag) = "is_finished"];
  // 任务等级
  int64 level = 2 [(gogoproto.jsontag) = "level"];
  // 该等级子任务目标
  repeated SubTaskTarget sub_task_targets = 3 [(gogoproto.jsontag) = "sub_task_targets"];
  // 奖励
  repeated AnchorReward anchor_rewards = 4 [(gogoproto.jsontag) = "anchor_rewards"];
}

message AnchorSubTaskProgress {
  //子任务id
  int64 sub_task_id = 1 [(gogoproto.jsontag) = "sub_task_id"];
  //子任务类型 1:关注 2:分享直播间 3:使用星光 4:弹幕 5:使用金瓜子 6:开播时长 7:开播教程
  int64 sub_task_type = 2 [(gogoproto.jsontag) = "sub_task_type"];
  // 当前进度
  int64 total = 3 [(gogoproto.jsontag) = "total"];
  // 指标名称
  string sub_task_name = 4 [(gogoproto.jsontag) = "sub_task_name"];
}


message AnchorReward {
  // 奖励类型 1:金仓鼠 2:星光 3:实时星光 4:奖励中心 5:特殊奖励
  int64 reward_type = 1 [(gogoproto.jsontag) = "reward_type"];
  // 奖励数量
  int64 reward_num = 2  [(gogoproto.jsontag) = "reward_num"];
}


message SubTaskTarget {
  //子任务id
  int64 sub_task_id = 1 [(gogoproto.jsontag) = "sub_task_id"];
  //子任务类型 1:关注 2:分享直播间 3:使用星光 4:弹幕 5:使用金瓜子 6:开播时长 7:开播教程
  int64 sub_task_type = 2 [(gogoproto.jsontag) = "sub_task_type"];
  //该等级下的指标目标值  金瓜子任务返回的是金瓜子数值，需要前端根据版本判断是否展示砖石
  int64 target_num = 3 [(gogoproto.jsontag) = "target_num"];
  //h5文案
  string h5_text = 4 [(gogoproto.jsontag) = "h5_text"];
}



message BindTaskReq {
  // 人群维度 0:主播维度 2:公会维度
  int64 crowd_dimension = 1 [(gogoproto.moretags) = 'form:"crowd_dimension"'];
  //主播id
  int64 uid = 2 [(gogoproto.moretags) = 'form:"uid"'];
  // 公会gid
  int64 gid = 3 [(gogoproto.moretags) = 'form:"gid"'];
  // 任务组id
  int64 task_group_id = 4 [(gogoproto.moretags) = 'form:"task_group_id"'];
  // 绑定任务开始时间，不传默认当前时间
  int64 start_time = 5 [(gogoproto.moretags) = 'form:"start_time"'];
}

message BindTaskResp {
  int64 bind_task_id = 1 [(gogoproto.moretags) = 'form:"bind_task_id"'];
}

