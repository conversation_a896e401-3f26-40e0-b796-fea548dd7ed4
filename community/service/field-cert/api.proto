syntax = "proto3";
import "extension/wdcli/wdcli.proto";

package community.field.cert.v1;

option go_package          = "buf.bilibili.co/bapis/bapis-gen/community/service.field.cert;api";
option java_package        = "com.bapis.community.service.field.cert";
option java_multiple_files = true;
option (wdcli.appid)       = "community.cosmo.field-cert-service";

service FieldCert {
    // 专业认证横条接口
    rpc ShowFieldCertPopUp(ShowFieldCertPopUpReq) returns (ShowFieldCertPopUpRsp);
}

message ShowFieldCertPopUpReq {
    int64 mid  = 1;    // 访问者mid
    int64 avid = 2;    // 稿件avid
}

message ShowFieldCertPopUpRsp {
    bool show_cert_query             = 1;    // 是否出专业意见黄条
    PromoteInfo promote_info         = 2;    // 专业意见黄条内容
    FieldCertPopUp field_cert_pop_up = 3;    // 专业认可弹窗
    bool has_field_cert              = 4;    // 是否认可过
    int64 field_cert_count           = 5;    // 稿件总认可人数
}

message PromoteInfo {
    string title         = 1;    // 横条内容
    int64  cert_count    = 2;    // 当前稿件的认可人数
    string desc          = 3;    // 专业意见描述
}

message FieldCertPopUp {
    string main_title  = 1;     // 主标题
    string sub_title   = 2;     // 副标题
}