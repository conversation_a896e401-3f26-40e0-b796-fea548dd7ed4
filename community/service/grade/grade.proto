// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
// import "google/api/annotations.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package grade.service.v2;

option (wdcli.appid) = "main.community.grade-service";
option go_package = "buf.bilibili.co/bapis/bapis-gen/community/service.grade;api";
option java_package = "com.bapis.community.service.grade";
option java_multiple_files = true;

option (gogoproto.goproto_enum_prefix_all) = false;
option (gogoproto.goproto_getters_all) = true;
option (gogoproto.unmarshaler_all) = true;
option (gogoproto.marshaler_all) = true;
option (gogoproto.sizer_all) = true;

service GradeV2 {
  // 创建打分
  rpc CreateGrade(CreateGradeReq) returns (CreateGradeResp);
  // 更新打分状态
  rpc UpdateGradeState(UpdateGradeStateReq) returns (UpdateGradeStateResp);
  // 用户打分
  rpc Grading(GradingReq) returns (GradingResp);
  // 查询打分信息, 批量查询
  rpc GetGrade(GetGradeReq) returns (GetGradeResp);
  // 查询用户打分记录
  rpc GetGradeRecords(GetGradeRecordsReq) returns (GetGradeRecordsResp);

  // To Admin 接口
  // 锁定评分
  rpc LockGradeV2(LockGradeV2Req) returns (LockGradeV2Resp);
  // 干预评分
  rpc ControlGrade(ControlGradeReq) returns (ControlGradeResp);
  // 查询打分信息
  rpc GetGradeForAdmin(GetGradeForAdminReq) returns (GetGradeForAdminResp);
}

enum BizCode {
  BizCodeNone = 0;
  BizCodeDanmaku = 1; // 弹幕
  BizCodeEsports = 2; // 赛事中心
}

enum GradeState {
  GradeStateNone = 0;
  GradeStateNormal = 1; // 正常
  GradeStateDeleted = 2; // 删除
}

message Grade {
  // 打分ID
  int64 id = 1 [(gogoproto.jsontag) = "id"];
  // 打分状态
  GradeState state = 2 [(gogoproto.jsontag) = "state"];
  // 分数值，平均分
  float avg = 3 [(gogoproto.jsontag) = "avg"];
  // 参与打分人数
  int64 count = 4 [(gogoproto.jsontag) = "count"];
  // 评分记录
  GradeRecord record = 5 [(gogoproto.jsontag) = "record"];
  // 分桶统计数据，i是分数，v是人数
  // 存量打分里没有，注意判断空！
  // 这是真实用户参与数据，暂不支持人工管控分数！
  // 有可能出现 count_buckets 里的总数 < count
  repeated int64 count_buckets = 6 [(gogoproto.jsontag) = "count_buckets"];
}

message GradeRecord {
  // 用户mid
  int64 mid = 1 [(gogoproto.jsontag) = "mid"];
  // 打分ID
  int64 grade_id = 2 [(gogoproto.jsontag) = "grade_id"];
  // 是否参与过打分
  bool is_graded = 3 [(gogoproto.jsontag) = "is_graded"];
  // 分数值，用户最新一次打分结果
  int32 score = 4 [(gogoproto.jsontag) = "score"];
  // 第一参与打分时间
  int64 first_time = 5 [(gogoproto.jsontag) = "first_time"];
  // 最新一次参与打分时间
  int64 latest_time = 6 [(gogoproto.jsontag) = "latest_time"];
}

message RecordItem {
  // 用户mid
  int64 mid = 1 [(gogoproto.jsontag) = "mid"];
  // 打分ID
  int64 grade_id = 2 [(gogoproto.jsontag) = "grade_id"];
}

message CreateGradeReq {
  // 业务方code
  BizCode biz_code = 1 [(gogoproto.moretags) = 'form:"biz_code" validate:"required"'];
  // 业务ID，一个业务ID挂载一个打分
  string biz_id = 2 [(gogoproto.moretags) = 'form:"biz_id" validate:"required"'];
  // 打分创建人mid，非C端用户创建的打分 mid=0
  int64 mid = 3 [(gogoproto.moretags) = 'form:"mid"'];
  // 有效打分分数区间为 [min_limit, max_limit]
  // 默认打分分数区间为 [0, 10]
  // 分数下限
  int32 min_limit = 4 [(gogoproto.moretags) = 'form:"min_limit"'];
  // 分数上限
  int32 max_limit = 5 [(gogoproto.moretags) = 'form:"max_limit"'];
}

message CreateGradeResp {
  // 评分id
  int64 id = 1;
}

message UpdateGradeStateReq {
  // 更新方式: (打分ID) 或 (业务方Code+业务方ID)
  // 打分ID
  int64 grade_id = 1 [(gogoproto.moretags) = 'form:"grade_id"'];
  // 业务方code
  BizCode code = 2 [(gogoproto.moretags) = 'form:"code"'];
  // 业务ID，一个业务ID挂载一个打分
  string biz_id = 3 [(gogoproto.moretags) = 'form:"biz_id"'];
  // 状态
  GradeState state = 4 [(gogoproto.moretags) = 'form:"state" validate:"required"'];
}

message UpdateGradeStateResp {

}

message GetGradeReq {
  // 查询方式: (打分ID) 或 (业务方Code+业务方ID)
  // 打分ID
  repeated int64 grade_id_list = 1 [(gogoproto.moretags) = 'form:"grade_id_list" validate:"max=60"'];
  // 业务方code
  BizCode code = 2 [(gogoproto.moretags) = 'form:"code"'];
  // 业务ID
  repeated string biz_id = 3 [(gogoproto.moretags) = 'form:"biz_id" validate:"max=60"'];
  // 用户mid, 传入后会返回该用户在各个打分上的结果
  int64 mid = 4 [(gogoproto.moretags) = 'form:"mid"'];
}

message GetGradeResp {
  repeated Grade grade_list = 1;
}

message GradingReq {
  // 打分方式: (打分ID) 或 (业务方Code+业务方ID)
  // 打分ID
  int64 grade_id = 1 [(gogoproto.moretags) = 'form:"grade_id"'];
  // 业务方code
  BizCode code = 2 [(gogoproto.moretags) = 'form:"code"'];
  // 业务ID，一个业务ID挂载一个打分
  string biz_id = 3 [(gogoproto.moretags) = 'form:"biz_id"'];
  // 参与打分的用户
  int64 mid = 4 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  // 分数值
  int32 score = 5 [(gogoproto.moretags) = 'form:"score"'];
  // 公参
  CommonParams common_params = 6 [(gogoproto.moretags) = 'form:"common_params"'];
}

message GradingResp {
}

message GetGradeRecordsReq {
  repeated RecordItem items = 1;
}

message GetGradeRecordsResp {
  // 评分记录
  repeated GradeRecord records = 1;
}

message LockGradeV2Req {
  int64 grade_id = 1 [(gogoproto.moretags) = 'form:"grade_id" validate:"required"'];
  // 锁定分数值
  float avg = 2 [(gogoproto.moretags) = 'form:"avg" validate:"required"'];
  // 调用方
  string caller = 3 [(gogoproto.moretags) = 'form:"caller" validate:"required"'];
  // 锁分开关
  int32 lock_state = 4 [(gogoproto.moretags) = 'form:"lock_state" validate:"required"'];
}

message LockGradeV2Resp {
  GradeForAdmin grade = 1;
}

message ControlGradeReq {
  int64 grade_id = 1 [(gogoproto.moretags) = 'form:"grade_id" validate:"required"'];
  // 干预总分值
  int64 admin_score = 2 [(gogoproto.moretags) = 'form:"admin_score" validate:"required"'];
  // 干预总人数
  int64 admin_count = 3 [(gogoproto.moretags) = 'form:"admin_count" validate:"required"'];
  // 调用方
  string caller = 4 [(gogoproto.moretags) = 'form:"caller" validate:"required"'];
}

message ControlGradeResp {
  GradeForAdmin grade = 1;
}

message GetGradeForAdminReq {
  // 打分ID
  repeated int64 grade_id_list = 1 [(gogoproto.moretags) = 'form:"grade_id_list" validate:"max=20"'];
  // 调用方
  string caller = 2 [(gogoproto.moretags) = 'form:"caller" validate:"required"'];
}

message GradeForAdmin {
  // 打分ID
  int64 id = 1 [(gogoproto.jsontag) = "id"];
  float avg = 2 [(gogoproto.jsontag) = "avg"];
  string user_score_str = 3 [(gogoproto.jsontag) = "user_score_str"];
  string user_counts_str = 4 [(gogoproto.jsontag) = "user_counts_str"];
  string admin_score_str = 5 [(gogoproto.jsontag) = "admin_score_str"];
  string admin_counts_str = 6 [(gogoproto.jsontag) = "admin_counts_str"];
  float lock_score = 7 [(gogoproto.jsontag) = "lock_score"];
  bool is_locked = 8 [(gogoproto.jsontag) = "is_locked"];
  int32 state = 9 [(gogoproto.jsontag) = "state"];
}

message GetGradeForAdminResp {
  repeated GradeForAdmin grade_list = 1;
}

// -------通用协议-------
// CommonParams 公参
message CommonParams {
  string platform = 1 [(gogoproto.moretags) = 'form:"platform"'];
  int32 build = 2 [(gogoproto.moretags) = 'form:"build"'];
  string buvid = 3 [(gogoproto.moretags) = 'form:"buvid"'];
  string mobi_app = 4 [(gogoproto.moretags) = 'form:"mobi_app"'];
  string device = 5 [(gogoproto.moretags) = 'form:"device"'];
  string channel = 6 [(gogoproto.moretags) = 'form:"channel"'];
  string model = 7 [(gogoproto.moretags) = 'form:"model"'];
  string brand = 8 [(gogoproto.moretags) = 'form:"brand"'];
  string osver = 9 [(gogoproto.moretags) = 'form:"osver"'];
  string ip = 10 [(gogoproto.moretags) = 'form:"ip"'];
  string port = 11 [(gogoproto.moretags) = 'form:"port"'];
  string origin = 12 [(gogoproto.moretags) = 'form:"origin"'];
  string referer = 13 [(gogoproto.moretags) = 'form:"referer"'];
  string user_agent = 14 [(gogoproto.moretags) = 'form:"user_agent"'];
  int32 polaris_app_id = 15 [(gogoproto.moretags) = 'form:"polaris_app_id"'];
  int32 polaris_platform = 16 [(gogoproto.moretags) = 'form:"polaris_platform"'];
  string spmid = 17 [(gogoproto.moretags) = 'form:"spmid"'];
  string from_spmid = 18 [(gogoproto.moretags) = 'form:"from_spmid"'];
}