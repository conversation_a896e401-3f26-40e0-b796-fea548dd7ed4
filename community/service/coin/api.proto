syntax = "proto3";

package community.service.coin.v1;
import "community/service/msgfeed/v2/api.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/aurora/aurora.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/community/service.coin;api";
option java_package = "com.bapis.community.service.coin";
option java_multiple_files = true;

option (aurora.appid) = "community.service.coin";
option (wdcli.appid) = "community.service.coin";

// Coin rpc
service Coin {
  // AddCoin add coin. 投币接口
  rpc AddCoin(AddCoinReq) returns (AddCoinReply);
  // ItemUserCoins get coins added of archive. 投币数量接口
  rpc ItemUserCoins(ItemUserCoinsReq) returns (ItemUserCoinsReply);
  // ItemsUserCoins get coins added of batch archive. 批量稿件投币数量接口
  rpc ItemsUserCoins(ItemsUserCoinsReq) returns (ItemsUserCoinsReply);
  // UserCoins get user coins. 用户硬币余额
  rpc UserCoins(UserCoinsReq) returns (UserCoinsReply);
  // ModifyCoins modify user coins. 修改硬币数
  rpc ModifyCoins(ModifyCoinsReq) returns (ModifyCoinsReply);
  // List get coin added list.投币列表
  rpc List(ListReq) returns (ListReply);
  // CoinsLog coins log 投币日志
  rpc CoinsLog(CoinsLogReq) returns (CoinsLogReply);
  // AddUserCoinExp add user coin exp for job
  rpc AddUserCoinExp(AddUserCoinExpReq) returns (AddUserCoinExpReply);
  // TodayExp get today coin added exp. 今日投币经验
  rpc TodayExp(TodayExpReq) returns (TodayExpReply);
  // 上月25号至本月25号的结算情况
  rpc SettleDetail(SettleDetailReq) returns (SettleDetailReply);
  // UpMemberState 更改用户投币记录状态
  rpc UpMemberState(UpMemberStateReq) returns (UpMemberStateReply);
  // 投币动画查询
  rpc MultiCoinAnimation(MultiCoinAnimationReq)
      returns (MultiCoinAnimationResp);
  //返回稿件部分投币最新用户mid
  rpc ListByAid(ListByAidReq) returns (ListByAidReply);
  // 获取投币通知跳转信息 msg-feed用
  rpc GetNotifyInfo(msgfeed.service.v2.GetNotifyInfoReq) returns (msgfeed.service.v2.GetNotifyInfoReply);
  // 撤销投币
  rpc RevocationCoins(RevocationCoinsReq) returns (RevocationCoinsResp);
}

enum Device {
  // The first value represents the default and must be == 0.
  Device_UNSPECIFIED = 0;
  Device_PC = 1;
  Device_ANDROID = 2;
  Device_ANDROID_TV = 3;
  Device_IPHONE = 4;
  Device_IPAD = 5;
  Device_WIN = 6;
  Device_H5 = 7;
  Device_ANDROID_CAR = 8;
  Device_ANDROID_THING = 9;
  Device_ANDROID_PAD = 10;
}

enum From {
  // 来源 未知
  SourceFromUnknown = 0;
  // 来源 播放器(老)
  SourceFromPlyaerOld = 1;
  // 来源 播放器(新) 点播
  SourceFromPlayerSingle = 2;
  // 来源 播放器(新) 连播
  SourceFromPlayerSeries = 3;
  // 来源 播单
  SourceFromMediaList = 4;
  // 来源 天马inline
  SourceFromTianmaInline = 5;
  // 来源 动态inline
  SourceFromDynamicInline = 6;
  // 来源 搜索inline
  SourceFromSearchInline = 7;
  // 来源 活动inline
  SourceFromActivityInline = 8;
  // 来源 短视频点播
  SourceFromStorySingle = 9;
  // 来源 短视频连播
  SourceFromStorySeries = 10;
  // 来源 联播页
  SourceFromSimulcast = 11;
  // 来源 空间头图播放
  SourceFromSpace = 12;
  // 来源 OGV影视 feed inline卡
  SourceFromFeedInline = 13;
  // 来源 OGV 一起看播放器
  SourceFromViewTogether = 14;
  // 来源 播客点播
  SourceFromListenerSingle = 15;
  // 来源 播客连播
  SourceFromListenerSeries = 16;
  // 来源 天马banner
  SourceFromBannerInline = 17;
  // 来源 天马订阅卡
  SourceFromSubscribeInline = 18;
  // 来源 垂类inline
  SourceFromVerticalInline = 19;
}

// AddCoinReply reply
message AddCoinReply {}

// AddCoinReq req
message AddCoinReq {
  // ip
  string ip = 1 [
    (gogoproto.jsontag) = "ip",
    (gogoproto.customname) = "IP",
    json_name = "ip"
  ];
  // mid
  int64 mid = 2 [
    (gogoproto.moretags) = "form:\"mid\" validate:\"required,min=1\"",
    (aurora.shardkey) = "required,backflow"
  ];
  // up 主mid
  int64 upmid = 3 [ (gogoproto.moretags) = 'form:"upid" validate:"required"' ];
  // 最大投币数
  int64 max_coin = 4 [ (gogoproto.moretags) = 'form:"max"' ];
  // aid
  int64 aid = 5 [ (gogoproto.moretags) = "form:\"aid\" validate:\"required\"" ];
  // 业务
  string business = 6
      [ (gogoproto.moretags) = "form:\"business\" validate:\"required\"" ];
  // 数量
  int64 number = 7
      [ (gogoproto.moretags) = "form:\"number\" validate:\"required,min=1\"" ];
  // 稿件typeid(稿件专用)
  int32 typeid = 8 [ (gogoproto.moretags) = "form:\"typeid\"" ];
  // 稿件发布时间
  int64 pub_time = 9 [ (gogoproto.moretags) = "form:\"pub_time\"" ];
  // mobi_app、platform和device用于确定投币设备
  string mobi_app = 10 [ (gogoproto.moretags) = 'form:"mobi_app"' ];
  string platform = 11 [ (gogoproto.moretags) = 'form:"platform"' ];
  string device = 12 [ (gogoproto.moretags) = 'form:"device"' ];
  From from = 13 [ (gogoproto.moretags) = 'form:"from"' ];
  CommonParams common_params = 14 [(gogoproto.moretags) = 'form:"common_params"'];
}

// AddUserCoinExpReply add coin exp reply
message AddUserCoinExpReply {}
// AddUserCoinExpReq req
message AddUserCoinExpReq {
  // ip
  string ip = 1 [
    (gogoproto.jsontag) = "ip",
    (gogoproto.customname) = "IP",
    json_name = "ip"
  ];
  // mid
  int64 mid = 2 [ (aurora.shardkey) = "required,backflow" ];
  // business
  string business = 3;
  // number
  int64 number = 4;
}
// CoinsLogReply reply
message CoinsLogReply {
  // log
  repeated ModelLog list = 1;
}

// CoinsLogReq req
message CoinsLogReq {
  // mid
  int64 mid = 1 [
    (gogoproto.moretags) = "form:\"mid\"  validate:\"required,min=1\"",
    (aurora.shardkey) = "required"
  ];
  // 返回最近一周还是全部
  bool recent = 2 [ (gogoproto.moretags) = "form:\"recent\"" ];
  // 翻译后的格式 还是原始格式
  bool translate = 3 [ (gogoproto.moretags) = "form:\"translate\"" ];
  // 拉取多少天前的 不传默认7天
  int64 before_day = 4 [ (gogoproto.moretags) = "form:\"before_day\"" ];
}
// ItemUserCoinsReply reply
message ItemUserCoinsReply {
  // number
  int64 number = 1 [ (gogoproto.jsontag) = "number", json_name = "number" ];
}

// ItemsUserCoinsReply
message ItemsUserCoinsReply { map<int64, int64> numbers = 1; }

// ItemUserCoinsReq req
message ItemUserCoinsReq {
  // mid
  int64 mid = 1 [
    (gogoproto.moretags) = 'form:"mid"  validate:"required"',
    (aurora.shardkey) = "required"
  ];
  // aid
  int64 aid = 2 [ (gogoproto.moretags) = "form:\"aid\" validate:\"required\"" ];
  // 业务
  string business = 3
      [ (gogoproto.moretags) = "form:\"business\" validate:\"required\"" ];
}

// ItemsUserCoinsReq req
message ItemsUserCoinsReq {
  // mid
  int64 mid = 1 [
    (gogoproto.moretags) = 'form:"mid"  validate:"required"',
    (aurora.shardkey) = "required"
  ];
  // aids
  repeated int64 aids = 2
      [ (gogoproto.moretags) = 'form:"aids" validate:"required"' ];
  // 业务
  string business = 3
      [ (gogoproto.moretags) = "form:\"business\" validate:\"required\"" ];
}

// ListReply reply
message ListReply {
  // log
  repeated ModelList list = 1;
}

// ListReq .
message ListReq {
  // mid
  int64 mid = 1 [ (aurora.shardkey) = "required" ];
  // business
  string business = 2;
  // 时间戳
  int64 ts = 3;
}

// ModelArchiveUserCoins .
message ModelArchiveUserCoins {
  // number
  int64 number = 1;
}

// ModelArgModifyCoin .
message ModelArgModifyCoin {
  // mid
  int64 mid = 1;
  // count
  double count = 2;
  // 原因
  string reason = 3;
  // ip
  string ip = 4 [
    (gogoproto.jsontag) = "ip",
    (gogoproto.customname) = "IP",
    json_name = "ip"
  ];
  // 操作人
  string operator = 5;
  // 是否要检查余额数量 默认检查 为1则不检查
  int32 check_zero = 6;
}

// ModelList .
message ModelList {
  // aid
  int64 aid = 1;
  // number
  int64 number = 2;
  // 时间戳
  int64 ts = 3;
  // ip
  uint32 ip = 4 [
    (gogoproto.jsontag) = "ip",
    (gogoproto.customname) = "IP",
    json_name = "ip"
  ];
}

// ModelLog .
message ModelLog {
  // 修改前硬币数
  double from = 1;
  // 修改后硬币数
  double to = 2;
  // ip
  string ip = 3 [
    (gogoproto.jsontag) = "ip",
    (gogoproto.customname) = "IP",
    json_name = "ip"
  ];
  // 原因
  string desc = 4;
  // 时间戳
  int64 time_stamp = 5;
}

// ModelRecord record
message ModelRecord {
  // aid
  int64 aid = 1;
  // mid
  int64 mid = 2;
  // up主id
  int64 up = 3;
  // 时间戳
  int64 timestamp = 4;
  // number
  int64 number = 5;
  // 业务
  string business = 6;
  // ip
  uint32 ip = 7 [
    (gogoproto.jsontag) = "ip",
    (gogoproto.customname) = "IP",
    json_name = "ip"
  ];
}

// ModifyCoinsReply reply
message ModifyCoinsReply {
  // result
  double result = 1;
}

// ModifyCoinsReq req
message ModifyCoinsReq {
  // mid
  int64 mid = 1 [
    (gogoproto.moretags) = "form:\"mid\" validate:\"required\"",
    (aurora.shardkey) = "required,backflow"
  ];
  // 变更的计数 例如10为硬币数加10
  double count = 2
      [ (gogoproto.moretags) = "form:\"count\" validate:\"required\"" ];
  // 改变的原因
  string reason = 3
      [ (gogoproto.moretags) = "form:\"reason\" validate:\"required\"" ];
  // ip
  string ip = 4 [
    (gogoproto.jsontag) = "ip",
    (gogoproto.customname) = "IP",
    json_name = "ip"
  ];
  // 操作人
  string operator = 5 [ (gogoproto.moretags) = "form:\"operator\"" ];
  // 是否要检查余额数量 默认检查 为1则不检查
  int32 check_zero = 6 [ (gogoproto.moretags) = "form:\"check_zero\"" ];
  // 时间戳
  int64 ts = 7;
  // 调用方的业务 如果需要幂等支持 填写这个值
  string caller = 8 [ (gogoproto.moretags) = 'form:"caller"' ];
  // 唯一id 如果需要幂等支持 填写这个值
  string uniqueID = 9 [ (gogoproto.moretags) = 'form:"uniq_id"' ];
}

// TodayExpReply reply
message TodayExpReply {
  // exp
  int64 exp = 1;
}

// TodayExpReq req
message TodayExpReq {
  // mid
  int64 mid = 1 [
    (gogoproto.moretags) = "form:\"mid\" validate:\"required,min=1\"",
    (aurora.shardkey) = "required"
  ];
}

// UserCoinsReply reply
message UserCoinsReply {
  // count
  double count = 1;
}
// UserCoinsReq req
message UserCoinsReq {
  // mid
  int64 mid = 1 [
    (gogoproto.moretags) = "form:\"mid\" validate:\"required,min=1\"",
    (aurora.shardkey) = "required"
  ];
}

message SettleDetailReq {
  // mid
  int64 mid = 1
      [ (gogoproto.moretags) = "form:\"mid\" validate:\"required,min=1\"" ];
  int64 year = 2
      [ (gogoproto.moretags) = "form:\"year\" validate:\"required,min=1\"" ];
  int64 month = 3
      [ (gogoproto.moretags) = "form:\"month\" validate:\"required,min=1\"" ];
}

message SettleDetail {
  int64 type = 1;
  string business = 2;
  int64 mid = 3;
  int64 aid = 4;
  // 获得总硬币数
  int64 coin_count = 5;
  // 获得经验数
  int64 exp_total = 6;
  // 反作弊扣除经验数
  int64 exp_sub = 7;
  // 结算状态 0 未结算 1 已经结算
  int64 state = 8;
  // 描述
  string description = 9;
  // 反作弊修改时间
  int64 itime = 10 [ (gogoproto.casttype) = "go-common/library/time.Time" ];
  int64 ctime = 11 [ (gogoproto.casttype) = "go-common/library/time.Time" ];
  int64 mtime = 12 [ (gogoproto.casttype) = "go-common/library/time.Time" ];
}

message SettleDetailReply { repeated SettleDetail records = 1; }

message UpMemberStateReq {
  int64 aid = 1
      [ (gogoproto.moretags) = "form:\"aid\" validate:\"required,min=1\"" ];
  // mid
  int64 mid = 2
      [ (gogoproto.moretags) = "form:\"mid\" validate:\"required,min=1\"" ];
  // business
  string business = 3
      [ (gogoproto.moretags) = "form:\"business\" validate:\"required\"" ];
}

message UpMemberStateReply {}

message MultiCoinAnimationReq {
  // 业务
  string business = 1
      [ (gogoproto.moretags) = 'form:"business" validate:"required"' ];
  // 对象id
  repeated int64 aids = 2
      [ (gogoproto.moretags) = 'form:"aids" validate:"required"' ];
}
message MultiCoinAnimationResp { map<int64, CoinAnimation> coin_animation = 1; }

message CoinAnimation {
  string business = 1;
  // 对象id
  int64 aid = 2;
  //投币App播放页Icon
  string coin_app_zip_icon = 3;
  string coin_app_icon_1 = 4;
  string coin_app_icon_2 = 5;
  string coin_app_icon_3 = 6;
  string coin_app_icon_4 = 7;
  //投币PCIcon
  string coin_pc_icon_1 = 8;
  string coin_pc_icon_2 = 9;
  string coin_pc_icon_3 = 10;
  string coin_pc_icon_4 = 11;
  //投币PCIcon 是否可动
  int64 is_moved_pc_icon_1 = 12;
  int64 is_moved_pc_icon_2 = 13;
}

message ListByAidReq {
  int64 aid = 1;
  string business = 2;
}
message ListByAidReply { repeated ModelListByAid list = 1; }
message ModelListByAid {
  int64 mid = 1;
  int64 ts = 2;
}

message RevocationCoinsReq {
  // ip
  string ip = 1 [
    (gogoproto.jsontag)    = "ip",
    (gogoproto.customname) = "IP",
    json_name              = "ip"
  ];
  // mid
  int64 mid = 2
  [(gogoproto.moretags) = "form:\"mid\" validate:\"required,min=1\""];
  // up 主mid
  int64 upmid = 3 [(gogoproto.moretags) = 'form:"upid" validate:"required"'];
  // aid
  int64 aid = 4 [(gogoproto.moretags) = "form:\"aid\" validate:\"required\""];
  // 业务
  string business = 5
  [(gogoproto.moretags) = "form:\"business\" validate:\"required\""];
  // 稿件typeid(稿件专用)
  int32 typeid = 6 [(gogoproto.moretags) = "form:\"typeid\""];
  // 稿件发布时间
  int64 pub_time = 7 [(gogoproto.moretags) = "form:\"pub_time\""];
  // mobi_app、platform和device用于确定投币设备
  string mobi_app = 8 [(gogoproto.moretags) = 'form:"mobi_app"'];
  string platform = 9 [(gogoproto.moretags) = 'form:"platform"'];
  string device   = 10 [(gogoproto.moretags) = 'form:"device"'];
  From from       = 11 [(gogoproto.moretags) = 'form:"from"'];
  CommonParams common_params = 12 [(gogoproto.moretags) = 'form:"common_params"'];
}

message RevocationCoinsResp {}

message CommonParams {
  int64 scene          = 1;  //0:default;1:tripleLike(三连) 详见enum CommonParamsScene
  string buvid         = 2;
  string mobi_app      = 3;
  string platform      = 4;
  int64 device_app_id  = 5;
  int64 platform_id    = 6;
  int64 build          = 7;
  string spmid         = 8;
  string from_spmid    = 9;
  string action_id     = 10;
  string channel       = 11;
  string device        = 12;
  string ua            = 13;
  string ip            = 14;
  string port          = 15;
  string refer         = 16;
  string client        = 17;
  string track_id      = 18;
  string extend_fields = 19;
}