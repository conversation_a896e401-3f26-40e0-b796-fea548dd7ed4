syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

package dm.service.model;

option go_package = "buf.bilibili.co/bapis/bapis-gen/community/service.dm;api";
option java_package = "com.bapis.community.service.dm";
option java_multiple_files = true;

option (gogoproto.goproto_enum_prefix_all) = false;
option (gogoproto.goproto_getters_all) = true;
option (gogoproto.unmarshaler_all) = true;
option (gogoproto.marshaler_all) = true;
option (gogoproto.sizer_all) = true;

message Subject {
  // 数据库id
  int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  // 父子类型
  DMType type = 2 [(gogoproto.jsontag) = "type", json_name = "type"];
  // 对象id
  int64 oid = 3 [(gogoproto.jsontag) = "oid", json_name = "oid"];
  // 父对象id
  int64 pid = 4 [(gogoproto.jsontag) = "pid", json_name = "pid"];
  // up主id
  int64 mid = 5 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // 状态
  DMSubState state = 6 [(gogoproto.jsontag) = "state", json_name = "state"];
  // 标识位
  int32 attr = 7 [(gogoproto.jsontag) = "attr", json_name = "attr"];
  // 总弹幕数
  int64 acount = 8 [(gogoproto.jsontag) = "acount", json_name = "acount"];
  // 正常、隐藏状态弹幕数
  int64 count = 9 [(gogoproto.jsontag) = "count", json_name = "count"];
  // 待审核弹幕数
  int64 mcount = 10 [(gogoproto.jsontag) = "mcount", json_name = "mcount"];
  // 移动到pool1的弹幕数，rank<=15000时，最多移动300条
  int64 moveCount = 11 [(gogoproto.jsontag) = "move_count", json_name = "move_count"];
  // 可显示弹幕上限
  int64 maxlimit = 12 [(gogoproto.jsontag) = "maxlimit", json_name = "maxlimit"];
  // 是否有子弹幕池
  DMSubChildPool childpool = 13 [(gogoproto.jsontag) = "childpool", json_name = "childpool"];
  // 创建时间
  int64 ctime = 14 [(gogoproto.jsontag) = "ctime", json_name = "ctime", (gogoproto.casttype) = "go-common/library/time.Time"];
  // 修改时间
  int64 mtime = 15 [(gogoproto.jsontag) = "mtime", json_name = "mtime", (gogoproto.casttype) = "go-common/library/time.Time"];
}

// 弹幕类型
enum DMType {
  DMTypeUnknow = 0;
  // 视频弹幕
  DMTypeVideo = 1;
}

// 弹幕主题状态
enum DMSubState {
  // 弹幕主题打开
  DMSubStateOpen = 0;
  // 弹幕主题关闭
  DMSubStateClose = 1;
}

// 弹幕主题属性位
enum DMSubAttr {
  // 允许游客弹幕
  DMSubAttrGuest = 0;
  // 允许剧透弹幕
  DMSubAttrSpolier = 1;
  // 允许活动弹幕
  DMSubAttrMission = 2;
  // 允许高级弹幕
  DMSubAttrAdvance = 3;
  // 弹幕先审后发
  DMSubAttrMonitorBefore = 4;
  // 弹幕先发后审
  DMSubAttrMonitorAfter = 5;
  // 开启蒙版
  DMSubAttrMaskOpen = 6;
  // 移动端蒙版生产完成
  DMSubAttrMblMaskReady = 7;
  // web端蒙版生产完成
  DMSubAttrWebMaskReady = 8;
  // 运营 先审后发
  DMSubAttrOperaorMonitorBefore = 9;
  // 运营 先发后审
  DMSubAttrOperaorMonitorAfter = 10;
  // 手动更改maxlimit
  DMSubAttrManualMaxlimit = 11;
  // 允许软连接
  DMSubAttrLink = 12;
  // 分段弹幕池上限
  DMSubAttrManualRestrict = 13;
  // 彩蛋弹幕
  DMSubAttrExtraScene = 14;
  // 是否包含指令弹幕
  DMSubAttrCommandDM = 15;
  // 直播转点播
  DMSubAttrLive = 16;
  // 是否包含高赞弹幕，弹幕数大于一定数量
  DMSubAttrLike = 17;
}

// 弹幕主题子弹幕池
enum DMSubChildPool {
  DMSubChildPoolNormal = 0;
  DMSubChildPoolSpecial = 1;
}

// 弹幕状态
enum DMState {
  // 普通状态
  DMStateNormal = 0;
  // 删除状态
  DMStateDelete = 1;
  // 隐藏状态
  DMStateHide = 2;
  // 屏蔽状态
  DMStateBlock = 3;
  // 过滤状态
  DMStateFilter = 4;
  // 先审后发
  DMStateMonitorBefore = 5;
  // 先发后审
  DMStateMonitorAfter = 6;
  // 敏感词过滤
  DMStateSensBlock = 7;
  // 举报删除
  DMStateReportDelete = 8;
  // 后台管理删除
  DMStateAdminDelete = 9;
  // 用户删除
  DMStateUserDelete = 10;
  // 举报脚本自动删除
  DMStateRptAutoDelete = 11;
  // 弹幕任务删除
  DMStateTaskDelete = 12;
  // ai删除
  DMStateAiDelete = 13;
  // 审核平台删除
  DMStateAuditDelete = 14;
  // 第三方过滤删除
  DMStateThirdDelete = 15;
  // 举报->隐藏状态
  DMStateReportToHide = 16;
  // 运营 先审后发
  DMStateOperaorMonitorBefore = 17;
  // 运营 先发后审
  DMStateOperaorMonitorAfter = 18;
}

// 弹幕池状态
enum DMPool {
  // 普通弹幕池
  DMPoolNormal = 0;
  // 字幕弹幕池
  DMPoolSubtitle = 1;
  // 高级弹幕池
  DMPoolSpecial = 2;
}

// 弹幕属性位值
enum DMAttrBit {
  // 保护弹幕
  DMAttrBitProtect = 0;
  // 直播弹幕
  DMAttrBitFromLive = 1;
  // 高赞弹幕
  DMAttrHighLike = 2;
}

// 弹幕模式
enum DMMode {
  DMModeUnknow = 0;
  // 正常滚动弹幕
  DMModeNormal = 1;
  // 底部弹幕
  DMModeBottom = 4;
  // 顶部弹幕
  DMModeTop = 5;
  // 逆向滚动弹幕
  DMModeReverse = 6;
  // 高级弹幕
  DMModeAdvance = 7;
  // 代码弹幕
  DMModeCode = 8;
  // BAS弹幕
  DMModeBAS = 9;
}

// 弹幕平台
enum DMPlat {
  DMPlatUnknow = 0;
  DMPlatWeb = 1;
  DMPlatAndroid = 2;
  DMPlatIPhone = 3;
  DMPlatWpM = 4;
  DMPlatIPad = 5;
  DMPlatPadHd = 6;
  DMPlatWpPc = 7;
}

// 弹幕蒙版平台
enum DMMaskPlat {
  // web
  DMMaskPlatWeb = 0;
  // mobile
  DMMaskPlatMbl = 1;
}

// 弹幕屏蔽类型
enum DMFilterType {
  DMFilterTypeText = 0;
  DMFilterTypeRegex = 1;
  DMFilterTypeUser = 2;
}

// 保护弹幕申请
enum DMProtectApply {
  // 保护弹幕被拒绝
  DMProtectApplyReject = 0;
  // 保护弹幕申请中
  DMProtectApplyPending = -1;
  // 保护弹幕被通过
  DMProtectApplyAgree = 1;
}

// 弹幕举报状态
enum DMReportState {
  // 待一审
  DMReportStateFirstInit = 0;
  // 一审删除
  DMReportStateFirstDelete = 1;
  // 待二审
  DMReportStateSecondInit = 2;
  // 二审删除
  DMReportStateSecondDelete = 3;
  // 二审忽略
  DMReportStateSecondIgnore = 4;
  // 一审忽略
  DMReportStateFirstIgnore = 5;
  // 二审脚本删除
  DMReportStateSecondAutoDelete = 6;
  // 风纪委待审(二审)
  DMReportStateJudgeInit = 7;
  // 风纪委删除(二审)
  DMReportStateJudgeDelete = 8;
  // 风纪委忽略(二审)
  DMReportStateJudgeIgnore = 9;
}

// 弹幕举报UP操作
enum DMReportUpOp {
  DMReportUpOpPending = 0;
  DMReportUpOpIgnore = 1;
  DMReportUpOpDelete = 2;
}

// 高级弹幕申请权限
enum DMUpperPermit {
  // 任何人
  DMUpperPermitAnybody = 0;
  // 仅粉丝
  DMUpperPermitFollower = 1;
  // 互相关注
  DMUpperPermitBoth = 2;
  // 没人可以申请
  DMUpperPermitNobody = 3;
}

// 弹幕转移状态
enum DMTransferState {
  // 待处理
  DMTransferStatePending = 0;
  // 完成
  DMTransferStateFinish = 1;
  // 其他
  DMTransferStateOther = 2;
}

// 弹幕字幕状态
enum DMSubtitleState {
  // 未知
  DMSubtitleStateUnknow = 0;
  // 草稿
  DMSubtitleStateDraft = 1;
  // 待审核
  DMSubtitleStatePending = 2;
  // 审核退回
  DMSubtitleStateReject = 3;
  // 删除
  DMSubtitleStateDelete = 4;
  // 发布
  DMSubtitleStatePublish = 5;
}

// 弹幕操作来源
enum DMSource {
  // 未知
  DMSourceUnknow = 0;
  // 运营后台
  DMSourceManager = 1;
  // 创作中心
  DMSourceUp = 2;
  // 播放器
  DMSourcePlayer = 3;
}

// 弹幕操作人
enum DMOperator {
  // 未知
  DMOperatorUnknow = 0;
  // 管理员
  DMOperatorAdmin = 1;
  // 用户
  DMOperatorMember = 2;
  // UP主
  DMOperatorUp = 3;
  // 协管
  DMOperatorAssist = 4;
  // 系统
  DMOperatorSystem = 5;
}

enum AdvanceMode {
  AdvanceModeUnknow = 0;
  // 高级弹幕 弹幕mode7
  AdvanceModeAdvance = 1;
  // 特殊弹幕 弹幕mode8 mode9
  AdvanceModeSpecial = 2;
}

message AdvancesReplyList {
  int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  int64 owner = 2 [(gogoproto.jsontag) = "owner", json_name = "owner"];
  int64 cid = 3 [(gogoproto.jsontag) = "cid", json_name = "cid"];
  int64 aid = 4 [(gogoproto.jsontag) = "aid", json_name = "aid"];
  string type = 5 [(gogoproto.jsontag) = "type", json_name = "type"];
  string mode = 6 [(gogoproto.jsontag) = "mode", json_name = "mode"];
  int64 mid = 7 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  int64 timestamp = 8 [(gogoproto.jsontag) = "timestamp", json_name = "timestamp"];
  int64 refund = 9 [(gogoproto.jsontag) = "refund", json_name = "refund"];
  string uname = 10 [(gogoproto.jsontag) = "uname", json_name = "uname"];
  string title = 11 [(gogoproto.jsontag) = "title", json_name = "title"];
  string cover = 12 [(gogoproto.jsontag) = "cover", json_name = "cover"];
}

enum FilterType {
  // 文本类型
  FilterTypeText = 0;
  // 正则类型
  FilterTypeRegex = 1;
  // 用户ID类型
  FilterTypeID = 2;
  FilterTypeBottom = 4;
  FilterTypeTop = 5;
  FilterTypeRev = 6;
}

enum Filter {
  FilterUnActive = 0;
  FilterActive = 1;
}

message UserFilter {
  int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  int64 mid = 2 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  FilterType type = 3 [(gogoproto.jsontag) = "type", json_name = "type"];
  string filter = 4 [(gogoproto.jsontag) = "filter", json_name = "filter"];
  string comment = 5 [(gogoproto.jsontag) = "comment", json_name = "comment"];
  int64 ctime = 6 [(gogoproto.jsontag) = "ctime", json_name = "ctime", (gogoproto.casttype) = "go-common/library/time.Time"];
  int64 mtime = 7 [(gogoproto.jsontag) = "mtime", json_name = "mtime", (gogoproto.casttype) = "go-common/library/time.Time"];
}

message UpFilter {
  int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  int64 mid = 2 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  FilterType type = 3 [(gogoproto.jsontag) = "type", json_name = "type"];
  string filter = 4 [(gogoproto.jsontag) = "filter", json_name = "filter"];
  Filter active = 5 [(gogoproto.jsontag) = "active", json_name = "active"];
  string comment = 6 [(gogoproto.jsontag) = "comment", json_name = "comment"];
  int64 ctime = 7 [(gogoproto.jsontag) = "ctime", json_name = "ctime", (gogoproto.casttype) = "go-common/library/time.Time"];
  int64 mtime = 8 [(gogoproto.jsontag) = "mtime", json_name = "mtime", (gogoproto.casttype) = "go-common/library/time.Time"];
}

message GlobalFilter {
  int64 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  FilterType type = 2 [(gogoproto.jsontag) = "type", json_name = "type"];
  string filter = 3 [(gogoproto.jsontag) = "filter", json_name = "filter"];
}

// Page 分野
message Page {
  // 当前页码
  int64 num = 1 [(gogoproto.jsontag) = "num", json_name = "num"];
  // 总页码
  int64 total = 2 [(gogoproto.jsontag) = "total", json_name = "total"];
  // 每页大小
  int64 page_size = 3 [(gogoproto.jsontag) = "size", json_name = "size"];
}

// DMIndex .
message DMIndex {
  int64 id = 1;
  int64 oid = 2;
  DMType type = 3;
  int64 mid = 4;
  int32 progress = 5;
  DMState state = 6;
  DMPool pool = 7;
  int32 attr = 8;
  int64 ctime = 9 [(gogoproto.casttype) = "go-common/library/time.Time"];
  int64 mtime = 10 [(gogoproto.casttype) = "go-common/library/time.Time"];
}

message Recommend {
  int64 ID = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  int32 Type = 2 [(gogoproto.jsontag) = "type", json_name = "type"];
  int64 Oid = 3 [(gogoproto.jsontag) = "oid", json_name = "oid"];
  RecommendType OidType = 4 [(gogoproto.jsontag) = "oid_type", json_name = "oid_type"];
  // 是否删除
  int32 State = 5 [(gogoproto.jsontag) = "state", json_name = "state"];
  // 推荐词时间段
  int64 StartTime = 6 [(gogoproto.jsontag) = "start_time", json_name = "start_time"];
  int64 EndTime = 7 [(gogoproto.jsontag) = "end_time", json_name = "end_time"];
  // 引导词
  string Guide = 8 [(gogoproto.jsontag) = "guide", json_name = "guide"];
  // 时间段内打薄规则ID
  int64 FilterIn = 9 [(gogoproto.jsontag) = "filter_in", json_name = "filter_in"];
  // 时间段外打薄规则ID
  int64 FilterOut = 10 [(gogoproto.jsontag) = "filter_out", json_name = "filter_out"];
  // 推荐词，逗号分隔
  repeated string Words = 11 [(gogoproto.jsontag) = "words", json_name = "words"];
}

// RecommendType .
enum RecommendType {
  RecommendTypeCid = 0;
}