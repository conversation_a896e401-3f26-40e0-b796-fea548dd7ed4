syntax = "proto3";
package article.admin.v1;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/community/interface.article.admin;api";
option java_package = "com.bapis.community.interfaces.article.admin";
option java_multiple_files = true;
option (wdcli.appid) = "community.article.admin";

service ArticleAdmin {
    // 修改文章状态
    rpc UpdateArticleState(UpdateArticleStateReq) returns (.google.protobuf.Empty);
}

message UpdateArticleStateReq {
    int64 aid = 1 [(gogoproto.moretags) = 'validate:"required,min=1"'];
    int64 state = 2; // https://info.bilibili.co/pages/viewpage.action?pageId=*********
    bool is_notice = 3;
    string reason = 4;
    string uname = 5;
    string remark = 6;
}