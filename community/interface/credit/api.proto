syntax = "proto3";
package main.community.credit.v1;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/community/interface.credit;api";
option java_package = "com.bapis.community.interfaces.credit";
option java_multiple_files = true;  // do not generate getXXX() method
option (gogoproto.goproto_getters_all) = false;
option (wdcli.appid) = "main.account-law.credit";

service Credit {
  // 对内容进行处理 批量接口 同步调用
  rpc IsJury(IsJuryReq) returns (IsJuryResp);
}

message IsJuryReq {
  int64 mid = 1;
}

message IsJuryResp {
  bool is = 1;
}