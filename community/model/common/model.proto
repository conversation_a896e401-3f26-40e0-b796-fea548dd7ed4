syntax = "proto3";

package model;

option go_package          = "buf.bilibili.co/bapis/bapis-gen/community/model.common;model";
option java_package        = "com.bapis.community.model.common";
option java_multiple_files = true;

// 点赞和投币接口都使用了CommonParams参数；
// todo：将thumbup/api.proto、coin/api.proto、favorite/api.proto中的CommonParams迁移到此处

// CommonParams中scene字段的枚举
enum CommonParamsScene {
    DEFAULT     = 0;  // 默认场景，点赞
    TRIPLE_LIKE = 1;  // 一键三连，同时调点赞、收藏、投币接口
    COIN        = 2;  // 投币
    UPDATE_FLAG = 3;  // 更新助力，同时调预约和点赞接口
}