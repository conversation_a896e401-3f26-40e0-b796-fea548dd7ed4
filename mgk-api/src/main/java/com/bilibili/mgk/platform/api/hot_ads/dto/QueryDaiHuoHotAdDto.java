package com.bilibili.mgk.platform.api.hot_ads.dto;

import com.bilibili.adp.common.util.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryDaiHuoHotAdDto implements Serializable {


    private static final long serialVersionUID = -6974734369568035733L;
    /**
     * 商品标题
     */
    private String itemName;

    /**
     * 资源位类型
     */
    private String itemSource;

    /**
     * 商品一级类目
     */
    private String firstCategory;

    /**
     * 商品二级类目
     */
    private String secondCategory;

    /**
     * 时间 0-七天 1-一个月
     */
    private Integer dayType;

    /**
     * 排序
     */
    private Integer orderType;

    /**
     * 黑名单创意id
     */
    private List<String> blackList;

    /**
     * 创意Id
     */
    private String creativeId;

    /**
     * 创意标题
     */
    private String creativeTitle;

    /**
     * 页码
     */
    private Page page;

    private Integer blackStatus;
}
