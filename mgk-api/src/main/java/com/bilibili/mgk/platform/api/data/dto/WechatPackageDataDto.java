package com.bilibili.mgk.platform.api.data.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName WechatDataDto
 * <AUTHOR>
 * @Date 2022/6/18 2:38 下午
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatPackageDataDto implements Serializable {
    private static final long serialVersionUID = -8972409234L;

    /**
     * 微信帐户id
     */
    private Integer wechatAccountId;

    /**
     * 微信包id
     */
    private Integer wechatPackageId;

    /**
     * 上报数据类型 0-复制 1-跳转
     */
    private Integer dataType;
}
