package com.bilibili.mgk.platform.api.click_house.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CHMgkPagePvCtrDto implements Serializable {

    private static final long serialVersionUID = -5660277586031614713L;
    /**
     * 落地页id
     */
    private Long pageId;

    /**
     * 曝光
     */
    private Long pv;

    /**
     * 点击
     */
    private Long click;

    /**
     * 消耗(毫分)
     */
    private Long costMilli;

    /**
     * 转化数
     */
    private Long convCnt;

    /**
     * 转化率 convCnt/click
     */
    private Double convRate;

    /**
     * 资源位类型id
     */
    private Long srcType;

    /**
     * 资源位类型名称
     */
    private String srcTypeName;

    /**
     * 流量来源
     */
    private Long eventSourceType;
}
