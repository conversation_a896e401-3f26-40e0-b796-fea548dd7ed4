package com.bilibili.mgk.platform.api.data.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.mgk.platform.api.data.dto.*;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/1/17
 **/
public interface IMgkDataService {

    /**
     * 插入表单数据
     *
     * @param reportDataDto
     * @return
     */
    Long insertFormData(ReportDataDto reportDataDto);

    void reportFormDataJob();

    void refreshTrackId();

    void refreshFormDataToConvTable();

    long getDataCountByFormId(Long formId);

    /**
     * 根据 formIds 查询表单数据的统计量(不包含线索通的 salesType)
     * @param formIds
     * @return
     */
    Map<Long, ExtFormDataDto> getFormId2ExtFormDataMap(List<Long> formIds, Boolean removeCheat);


    List<ReportDataDto> getReportDatas(QueryReportDataParamDto paramDto);

    List<ReportDataDto> getReportDatasWithoutCheat(QueryReportDataParamDto paramDto);

    Map<Long, List<FormItemDataDto>> getFormDataId2ItemDataMapInFormDataIds(List<Long> formDataIds);

    /**
     * 查询所有的表单项数据,不过滤反作弊逻辑
     * @param formDataIds 表单项数据id
     * @return 表单项数据
     */
    Map<Long, List<FormItemDataDto>> getFormDataId2ItemDataMapWithoutCheat(List<Long> formDataIds);

    PageResult<ReportDataDto> getReportDataByPage(QueryReportDataParamDto paramDto);

    void batchInsertFormData(NewReportDataDto newReportDataDto);

    /**
     * 根据buvid用户授权lbs历史信息
     */
    MgkLbsAuthorityDto getLbsAuthorityByBuvid(String buvid);

    List<FormItemDataDto> getFormData(Long formDataId);

    void refreshFormDataSalesTypeJob();

    void refreshLbsAuthority();

    /**
     * 根据创意ids查询表单数据
     *
     * @param paramDto
     * @return
     */
    PageResult<FormDataDto> getFormDataByPage(FormDataQueryDto paramDto);

    /**
     * 判断使用了历史手机号的用户表单提交需不需要再次用验证码验证手机号
     *
     * @param mgkPageId 建站落地页id
     * @param phoneHistoryMtime 历史手机号的更新时间
     */
    boolean needPhoneIntelligentVerifyWhenUseHistory(Long mgkPageId, Timestamp phoneHistoryMtime);

    /**
     * 判断用户使用了登陆信息绑定的手机号的表单提交需不需要再次用验证码验证手机号
     *
     * @param mgkPageId 建站落地页id
     */
    boolean needPhoneIntelligentVerifyWhenUseLogin(String mgkPageId);

}
