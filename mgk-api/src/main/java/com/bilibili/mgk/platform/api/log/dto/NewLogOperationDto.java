package com.bilibili.mgk.platform.api.log.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/1/19
 **/
@Data
@Builder
@AllArgsConstructor
public class NewLogOperationDto implements Serializable{
    private static final long serialVersionUID = -5161231815543802500L;
    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 对象ID
     */
    private Long objId;

    /**
     * 操作对象类型: 1-站点
     */
    private Integer objFlag;

    /**
     * 操作类型:1-新建 2-删除 3-修改 4-复制 5-发布 6-下线 7-管理员驳回
     */
    private Integer operateType;

    /**
     * 操作人
     */
    private String operatorUsername;

    /**
     * 操作人类型: 0-广告主 1-运营 2-系统 4-代理商 5-代理商的系统管理员 6-代理商的投放管理员 7-二级代理运营人员 100-内部LDAP登陆
     */
    private Integer operatorType;

    /**
     * 新值
     */
    private Object oldValue;

    /**
     * 旧值
     */
    private Object newValue;
}
