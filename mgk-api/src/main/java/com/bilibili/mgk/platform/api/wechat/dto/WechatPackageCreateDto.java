package com.bilibili.mgk.platform.api.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName WechatPackageCreateDto
 * <AUTHOR>
 * @Date 2022/6/1 11:05 上午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WechatPackageCreateDto implements Serializable {
    private static final long serialVersionUID = -3451495956875L;

    /**
     * 微信包id
     */
    private Integer id;

    /**
     * 帐户id
     */
    private Integer accountId;

    /**
     * 微信包名称
     */
    private String name;

    /**
     * 微信包类型 0-个人号 1-公众号 2-企业微信 3-其他
     */
    private Integer type;

    /**
     * 关联微信帐户id列表
     */
    private List<Integer> wechatAccountIds;
}
