package com.bilibili.mgk.platform.api.wechat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @ClassName WechatPackageListDto
 * <AUTHOR>
 * @Date 2022/6/1 11:26 上午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WechatPackageListDto implements Serializable {
    private static final long serialVersionUID = -435943509405L;

    /**
     * 微信包id
     */
    private Integer id;

    /**
     * 微信包名称
     */
    private String name;

    /**
     * 微信包类型 0-个人号 1-公众号 2-企业微信 3-其他
     */
    private Integer type;

    /**
     * 微信号数量
     */
    private Integer wechatAccountCount;

    /**
     * 复制数量统计
     */
    private Integer copyCount;

    /**
     * 跳转数量统计
     */
    private Integer jumpCount;

    /**
     * 最近提交数据时间
     */
    private Timestamp recentSubmitTime;

    /**
     * 创建时间
     */
    private Timestamp ctime;
}
