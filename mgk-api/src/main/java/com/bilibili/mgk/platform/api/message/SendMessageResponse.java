package com.bilibili.mgk.platform.api.message;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 消息发送结果。
 *
 * <AUTHOR>
 * @since 2018年06月06日
 */
@Data
@NoArgsConstructor
public class SendMessageResponse implements Serializable {
    private static final long serialVersionUID = 8016862330510449049L;

    private Integer code;
    private String message;
    private SendMessageResult data;

    @Data
    @NoArgsConstructor
    public static class SendMessageResult {
        private String mc;
        private Integer data_type;
        private Integer total_count;
        private Integer error_count;
        private Integer _gt_;
    }

}
