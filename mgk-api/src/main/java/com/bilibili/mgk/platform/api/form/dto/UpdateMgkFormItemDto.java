package com.bilibili.mgk.platform.api.form.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/1/20
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateMgkFormItemDto implements Serializable {

    private static final long serialVersionUID = -5189956587404278116L;
    /**
     * 表单项ID
     */
    private Long formItemId;
    /**
     * 表单项名称
     */
    private String label;

    /**
     * 表单项类型: 1-radio 2-checkbox 3-input 4-select
     */
    private Integer type;

    /**
     * 排序号
     */
    private Integer sortNumber;

    /**
     * 下拉候选项值
     */
    private String optionsVal;

    /**
     * 单选/多选项值
     */
    private String radioCheckboxVal;

    /**
     * 最小地址维度
     */
    private Integer minAddress;

    /**
     * 是否详细地址
     */
    private Integer isDetailAddress;

    /**
     * 是否允许为空: 0-不允许 1-允许
     */
    private Integer isAllowEmpty;

    /**
     * 是否唯一: 0-否 1-是
     */
    private Integer isUnique;

    /**
     * 是否提交验证: 0-否 1-是
     */
    private Integer isSubmitValidate;

    /**
     * 是否必填，0不要求必填，1要求必填
     */
    private Integer required;

    /**
     * 占位字符串
     */
    private String placeholder;

    /**
     * 默认填入内容，不传，默认值请输入
     */
    private String itemDefault;

    /**
     * 字数上限，为0不限长度
     */
    private Integer maxLength;

    /**
     * 手机号前缀+86
     */
    private String prefix;

    /**
     * 手机号自动填入授权声明点击链接
     */
    private String autoFillLink;

    /**
     * 手机号自动填入授权声明文案
     */
    private String autoFillText;

    /**
     * 是否展示自动输入选项 0-不展示 1-展示
     */
    private Integer showAutoFill;
    /**
     * 键盘类型 0-文字键盘 1-数字键盘
     */
    private Integer keyboardType;
    /**
     * 表单项子类型
     */
    private Integer subType;

    /**
     * 单选多选展示规则: 0-默认，一行一个选项 1 一行两个选项
     */
    private Integer showRule;

    /**
     * 范围设置最小值
     */
    private Integer rangeLeft;

    /**
     * 范围设置最大值
     */
    private Integer rangeRight;

    /**
     * 下拉组件配置JSON
     */
    private String options;

    //lbs表单项ID
    private Long lbsId;

}
