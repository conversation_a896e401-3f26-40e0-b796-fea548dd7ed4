package com.bilibili.mgk.platform.api.archive.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.archive.dto.MgkArchiveLikeCountDto;
import com.bilibili.mgk.platform.api.archive.dto.MgkArchiveLikeStatsReplyDto;
import com.bilibili.mgk.platform.api.archive.dto.MgkArchiveLikeStatsReqDto;

import java.util.List;

/**
 * @file: IMgkArchiveLikeService
 * @author: gaoming
 * @date: 2021/12/14
 * @version: 1.0
 * @description:
 **/
public interface IMgkArchiveLikeService {

    /**
     * 根据avid获取稿件的点赞点踩数量
     *
     * @param avid
     * @return
     * @throws ServiceException
     */
    MgkArchiveLikeCountDto getArchiveLikeCount(Long avid) throws ServiceException;

    /**
     * 批量获取稿件的点赞点踩数量
     *
     * @param avids
     * @return
     */
    List<MgkArchiveLikeCountDto> getArchivesLikeCount(List<Long> avids) throws ServiceException;

    /**
     * 查询稿件点赞信息（点赞数和是否点赞）
     *
     * @param reqDto
     * @return
     * @throws ServiceException
     */
    MgkArchiveLikeStatsReplyDto archiveLikeStats(MgkArchiveLikeStatsReqDto reqDto) throws ServiceException;

    /**
     * 获取落地页里面稿件的点赞信息
     *
     * @param pageId
     * @param mid
     * @param buvid
     * @return
     * @throws ServiceException
     */
    MgkArchiveLikeStatsReplyDto getArchiveLikeStats(Long pageId, Long mid, String buvid) throws ServiceException;


}
