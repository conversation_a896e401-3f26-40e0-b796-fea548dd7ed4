package com.bilibili.mgk.platform.api.ad_complain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/28
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateAdComplainDto {

    private Integer id;

    private String token;

    private Long mid;

    private String buvid;

    private String email;

    private Long creativeId;

    private Integer complainType;

    private Integer secondComplainType;

    private String complainText;

    private Integer sourceId;

    private List<String> complainAttachs;

    private Integer salesType;

    /**
     * 商品id
     */
    private Long pId;

    /**
     * 客户端请求id
     */
    private String requestId;

    /**
     * 投诉详情
     */
    private String complainDetail;

    private Long timestampSeconds;

    private boolean isPc;
}
