syntax = "proto3";

// import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
// import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";


package intl.service.common.inner.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/intl/common.service.inner;v1";
option java_package = "com.bapis.intl.common.service.inner";
option java_multiple_files = true;
option (wdcli.appid) = "intl.service.common-service";

service CommonInner {
  // 地区分组（面向B端）
  // 根据业务查询所有的地区分组
  // source: ogv（ogv 业务）
  rpc InnerGroupsBySource(InnerGroupsBySourceReq) returns (InnerGroupsBySourceResp);
  // CreateLocationRegionGroupInner 创建新的地区分组
  rpc InnerCreateLocationRegionGroup(InnerCreateLocationRegionGroupReq) returns (InnerCreateLocationRegionGroupResp);
  // RegionGroupByGroupIdsInner 根据 group ids 获取区域分组列表
  rpc InnerRegionGroupsByGroupIds(InnerRegionGroupsByGroupIdsReq) returns (InnerRegionGroupsByGroupIdsResp);

  // text接口
  // CreateText 创建Text
  rpc InnerCreateText(InnerCreateTextReq) returns (.google.protobuf.Empty);
  // UpdateText 更新Text
  rpc InnerUpdateText(InnerUpdateTextReq) returns (.google.protobuf.Empty);
  // QueryTextsByPage 分页查询Text
  rpc InnerTextsByPage(InnerTextsByPageReq) returns (InnerTextsByPageResp);
  // QueryNamespace 查询namespace
  rpc InnerNamespace(.google.protobuf.Empty) returns (InnerNamespaceResp);

  // Langs接口
  // QueryLangs 查询多语言信息
  rpc InnerLangsInfo(.google.protobuf.Empty) returns (InnerLangsInfoResp);

  // Regions
  // QueryRegions 查询地区
  rpc InnerRegionsInfo(.google.protobuf.Empty) returns (InnerRegionsInfoResp);
  // QueryRegionConfBusinessList 查询地区配置列表
  rpc InnerRegionConfBusinessList(.google.protobuf.Empty) returns (InnerRegionConfBusinessListResp);
  // CreateRegionConf 创建地区配置
  rpc InnerCreateRegionConf(InnerCreateRegionConfReq) returns (.google.protobuf.Empty);
  // QueryRegionConfBusinessConfigs 根据business查询地区配置
  rpc InnerRegionConfBusinessConfigs(InnerRegionConfBusinessConfigsReq) returns (InnerRegionConfBusinessConfigsResp);
  // UpdateRegionConfBusinessConfig 更新地区配置
  rpc InnerUpdateRegionConfBusinessConfig(InnerUpdateRegionConfBusinessConfigReq) returns (.google.protobuf.Empty);
  // DeleteRegionConfBusinessConfig 根据id删除地区配置
  rpc InnerDeleteRegionConfBusinessConfig(InnerDeleteRegionConfBusinessConfigReq) returns (.google.protobuf.Empty);
  // DeleteRegionConfBusinessConfig 根据business删除地区配置
  rpc InnerDeleteRegionConfBusiness(InnerDeleteRegionConfBusinessReq) returns (.google.protobuf.Empty);
}

message InnerCreateLocationRegionGroupReq {
  string   name = 1;
  string   source = 2;  // 来源业务
  int64    hidden = 3;
  int64    ord = 4;
  repeated InnerLocationRegion regions = 5;
}

message InnerCreateLocationRegionGroupResp {
  int64 group_id = 1;
  bool  ok = 2;
}

message InnerRegionGroupsByGroupIdsReq {
  repeated int64 group_ids = 1;
}

message InnerRegionGroupsByGroupIdsResp {
  repeated InnerLocationGroup groups = 1;
}

message InnerGroupsBySourceReq {
  string source = 1;
}

message InnerGroupsBySourceResp {
  repeated InnerLocationGroup groups = 1;
}

message InnerCreateTextReq{
  repeated InnerTextCreate texts = 1;
}

message InnerUpdateTextReq{
  int64          id = 1;  // 校验？
  InnerTextType  text_type = 2;
  string         namespace = 3;
  string         text_key = 4;
  string         text_value = 5;
  string         lang = 6;
}

message InnerLocationGroup {
  int64  id = 1;
  string name = 2;
  string source = 3;  // 来源业务
  int64  hidden = 4;
  int64  ord = 5;
  int64  ctime = 6;
  int64  mtime = 7;
}

message InnerTextsByPageReq{
  string lang = 1;
  string namespace = 2;
  string text_key = 3;
  int64  page_num = 4;
  int64  page_size = 5;
}

message InnerTextsByPageResp{
  repeated InnerText texts = 1;
  InnerPage page = 2;
}

message InnerNamespaceResp{
  repeated InnerNamespace namespace = 1;
}

message InnerLangsInfoResp{
  repeated InnerLang langs = 1;
}

message InnerRegionsInfoResp{
  repeated InnerRegion regions = 1;
}

message InnerRegionConfBusinessListResp{
  repeated InnerRegionConf region_confs = 1;
}

message InnerCreateRegionConfReq{
  string business = 1;
  string region = 2;
  string lang_picker = 3;
}

message InnerRegionConfBusinessConfigsReq{
  string business = 1;
}

message InnerRegionConfBusinessConfigsResp{
  repeated InnerRegionConf region_confs = 1;
}

message InnerUpdateRegionConfBusinessConfigReq{
  int64  id = 1;
  string lang_picker = 2;
}

message InnerDeleteRegionConfBusinessConfigReq{
  int64 id = 1;
}

message InnerDeleteRegionConfBusinessReq{
  string business = 1;
}

message InnerNamespace{
  string namespace = 1;
}

message InnerTextCreate{
  InnerTextType   text_type = 1;
  string          namespace = 2;
  string          text_key = 3;
  string          text_value = 4;
  string          lang = 5;
}

message InnerText{
  int64          id = 1;
  InnerTextType  text_type = 2;
  string         namespace = 3;
  string         text_key = 4;
  string         text_value = 5;
  string         lang = 6;
  int64          mtime = 7;
  int64          ctime = 8;
}

message InnerPage{
  int64 page_size = 1;
  int64 page_num  = 2;
  int64 total     = 3;
}

message InnerLang{
  int64  id = 1;
  string lang_name = 2;
  string lang_name_cn = 3;
}

message InnerRegion{
  int64  id = 1;
  string region = 2;
  int64  region_id = 3;
  string region_en = 4;
  int64  ctime = 5;
  int64  mtime = 6;
}

message InnerRegionConf{
  int64  id = 1;
  string business = 2;
  string region = 3;
  string lang_picker = 4;
}

message InnerLocationRegion {
  string region = 1;
  int64  region_id = 2;
  string region_en = 3;
}

enum InnerTextType {
  UNKNOWN = 0;
  STATIC = 1;
  TEMPLATE = 2;
  PLURAL = 3;
}