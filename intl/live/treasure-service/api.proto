// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";

package intl.live.treasure.service.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/intl/live.treasure.service;v1";
option java_package = "com.bapis.intl.live.treasure.service";
option java_multiple_files = true;
option (wdcli.appid) = "intl.live.treasure-service";


service TreasureService {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);
  // 道具价格面板
  rpc TreasurePanel(TreasurePanelReq) returns (TreasurePanelResp);
  // 道具价格面板V2
  rpc TreasurePanelV2(TreasurePanelV2Req) returns (TreasurePanelV2Resp);
  // 获取礼物信息v2
  rpc TreasureV2(TreasureV2Req) returns (TreasureV2Resp);
  // 批量获取礼物信息v2
  rpc TreasuresV2(TreasuresV2Req) returns (TreasuresV2Resp);
  // 赠送礼物v2
  rpc TreasureSendV2(TreasureSendV2Req) returns (TreasureSendV2Resp);
  // 获取礼物多语言信息v2
  rpc TreasureDescriptionByLang(TreasureDescriptionByLangReq) returns (TreasureDescriptionByLangResp);
  // 统一收单接口
  rpc OrderCreate(OrderCreateReq) returns (OrderCreateResp);
  // 支付成功回调
  //  rpc PayCallback(PayCallbackReq) returns (PayCallbackResp) {
  //    option (google.api.http) = {
  //      get: "/x/internal/live/treasure/order/callback"
  //    };
  //  };
  // 订单支付结果查询
  rpc OrderState(OrderStateReq) returns (OrderStateResp);
  // 发放免费次数
  rpc AddTrialTimes(AddTrialTimesReq) returns (AddTrialTimesResp);
  // 付费用户榜单
  rpc UserRank(UserRankReq) returns (UserRankResp);
  // 批量获取某一时间范围内房间的stars
  rpc StarsCountByMids(StarsCountByMidsReq) returns (StarsCountByMidsResp);
  // 批量获取某一时间范围内主播的收入stars(es，推荐)
  rpc SumStarsByToMid(SumStarsByToMidReq) returns (SumStarsByToMidResp);
  // 批量获取某一段时间范围内用户的消费stars(es，推荐)
  rpc SumStarsByMid(SumStarsByMidReq) returns (SumStarsByMidResp);
}

// 主播收入
message SumStarsByToMidReq {
  // 主播ids
  repeated int64 to_mids = 1 [(gogoproto.moretags) = 'validate:"max=50"'];
  // 礼物ids
  repeated int64 treasure_ids = 2[(gogoproto.moretags) = 'validate:"max=50"'];
  // 开始时间，最多30天间隔,且不允许拿60天以前的数据
  int64 stime = 3 [(gogoproto.moretags) = 'validate:"required"'];
  // 结束时间
  int64 etime = 4 [(gogoproto.moretags) = 'validate:"required"'];
}

// 主播收入
message SumStarsByToMidResp {
  // 根据stars倒序
  repeated SumStarsByToMidItem items = 1;
}

// 主播收入
message SumStarsByToMidItem {
  // 主播id
  int64 to_mid = 1;
  // stars总和
  int64 stars = 2;
}

// 用户消费
message SumStarsByMidReq {
  // 用户mids(不传则获取所有付费用户)
  repeated int64 mids = 1 [(gogoproto.moretags) = 'validate:"max=50"'];
  // 主播mids
  repeated int64 to_mids = 2 [(gogoproto.moretags) = 'validate:"max=50"'];
  // 礼物ids
  repeated int64 treasure_ids = 3 [(gogoproto.moretags) = 'validate:"max=50"'];
  // 开始时间,最多30天间隔,且不允许拿60天以前的数据
  int64 stime = 4 [(gogoproto.moretags) = 'validate:"required"'];
  // 结束时间
  int64 etime = 5 [(gogoproto.moretags) = 'validate:"required"'];
}

// 用户消费
message SumStarsByMidResp {
  repeated SumStarsByMidItem items = 1;
}

// 用户消费
message SumStarsByMidItem {
  // 用户id
  int64 mid = 1;
  // stars总和
  int64 stars = 2;
}

message StarsCountByMidsReq {
  repeated int64 mids = 1 [(gogoproto.moretags) = 'validate:"max=20,min=1,dive,gt=0,required"'];
  // 最多30天间隔,且不允许拿60天以前的数据
  int64 stime = 2 [(gogoproto.moretags) = 'validate:"required"'];
  int64 etime = 3 [(gogoproto.moretags) = 'validate:"required"'];
}

message StarsCountByMidsResp {
  map<int64, int64> items= 1;
}

message TreasurePanelReq {
  int64 mid = 1 [(gogoproto.moretags) = 'validate:"required"'];
  string platform = 2 [(gogoproto.moretags) = 'validate:"required"'];
}

message TreasurePanelResp {
  repeated Package packs = 1;
}

message TreasurePanelV2Req {
  int64 mid = 1;
  string platform = 2;
  // 标签名 1:礼物
  int64 category_id = 3;
  int64 lang_id = 4;
  int64 room_id = 5;
}

// 礼物面板v2
message TreasurePanelV2Resp {
  repeated TreasureItem list = 1;
  CategoryList category = 2;
  int64 stars = 3;
}

message CategoryList {
  repeated CategoryItem list = 1;
  int64 current_category = 2;

}

message CategoryItem {
  int64 id = 1;
  string name = 2;
}

// 礼物面板item
message TreasureItem {
  int64 id = 1;
  int32 style = 2;
  string treasure_name = 3;
  int64 stars = 4;
  string base_icon = 5;
  string flow_icon = 6;
  string webp_icon = 7;
  string corner_icon = 8;
  int32 msgbar = 9;
  int32 location_order = 10;
  TextBar text_bar = 11;
  BannerBar banner_bar = 12;
}

// 文本说明信息条
message TextBar {
  // （中间）礼物说明文本(多语言)(msgbar为1展示)
  string treasure_description = 1;
  // （左侧）文本说明的icon(多语言)(msgbar为1展示)
  string description_icon = 2;
  // 路径(多语言)  msgbar为1填入
  string text_url = 3;
  // （右侧）素材 (多语言)msgbar为1填入
  string text_img = 4;
}

message BannerBar {
  // banner路径(多语言)  msgbar为2 填入
  string banner_url = 1;
  // banner素材 (多语言)msgbar为2 填入
  string banner_img = 2;
}

message OrderCreateReq {
  int64 itemID = 1 [(gogoproto.moretags) = 'form:"item_id" validate:"required"'];
  int64 toMid = 2 [(gogoproto.moretags) = 'form:"to_mid" validate:"required"'];
  int64 number = 3 [(gogoproto.moretags) = 'validate:"min=1"'];
  int64 mid = 4 [(gogoproto.moretags) = 'validate:"required"'];
  string platform = 5 [(gogoproto.moretags) = 'validate:"required"'];
}

message OrderCreateResp {
  string orderID = 1 [(gogoproto.jsontag) = "order_id", json_name = "order_id"];
  string payData = 2 [(gogoproto.jsontag) = "pay_data", json_name = "pay_data"];
}

message OrderStateReq {
  string orderID = 1 [(gogoproto.moretags) = 'form:"order_id" validate:"required"'];
  int64 mid = 2 [(gogoproto.moretags) = 'validate:"required"'];
}

message OrderStateResp {
  Order order = 1;
}

message AddTrialTimesReq {
  string biz = 1;
  string idempotentNo = 2 [(gogoproto.moretags) = 'form:"idempotent_no"'];
  int64 mid = 3 [(gogoproto.moretags) = 'validate:"required"'];
  int64 tier = 4 [(gogoproto.moretags) = 'validate:"required"'];
  int64 category = 5 [(gogoproto.moretags) = 'validate:"required"'];
  string scene = 6;
}

message AddTrialTimesResp {
}

message Trial {
  int64 tier = 1;
}

message Package {
  int64 id = 1 [(gogoproto.customname) = "ID"];
  int64 categoryID = 2 [(gogoproto.jsontag) = "category_id", json_name = "category_id"];
  string title = 3;
  string icon = 4;
  string description = 5;
  int64 state = 6;
  repeated Item items = 7;
}

message Item {
  int64 id = 1 [(gogoproto.jsontag) = "id", (gogoproto.customname) = "ID", json_name = "id"];
  int64 packID = 2 [(gogoproto.jsontag) = "pack_id", json_name = "pack_id"];
  int64 tier = 3 [(gogoproto.jsontag) = "tier,omitempty", json_name = "tier"];
  string icon = 4 [(gogoproto.jsontag) = "icon", json_name = "icon"];
  int64 fee = 6 [(gogoproto.jsontag) = "fee,omitempty", json_name = "fee"];
  string feeUnit = 7 [(gogoproto.jsontag) = "fee_unit,omitempty", json_name = "fee_unit"];
  string currency = 8 [(gogoproto.jsontag) = "currency,omitempty", json_name = "currency"];
  string title = 9 [(gogoproto.jsontag) = "title", json_name = "title"];
  string description = 10 [(gogoproto.jsontag) = "description", json_name = "description"];
  string productID = 11 [(gogoproto.jsontag) = "product_id", json_name = "product_id"];
  int64 trialAble = 12 [(gogoproto.jsontag) = "trial_able,omitempty", json_name = "trial_able"];
  int64 userTrialAble = 13 [(gogoproto.jsontag) = "user_trial_able", json_name = "user_trial_able"];
}

message Order {
  string orderID = 1 [(gogoproto.jsontag) = "order_id", json_name = "order_id"];
  int64 state = 2 [(gogoproto.jsontag) = "state", json_name = "state"];
}

message Author {
  int64 mid = 1;
  string name = 2;
}

message UserRankReq {
  int64 roomID = 1 [(gogoproto.jsontag) = "room_id", json_name = "room_id"];
}

message UserRankResp {
  repeated UserRank userRanks = 1 [(gogoproto.jsontag) = "user_ranks", json_name = "user_ranks"];
}

message UserRank {
  int64 mid = 1;
  string name = 2;
  string face = 3;
  int64 score = 4;
}

message TreasureV2Req {
  int64 treasure_id = 1 [(gogoproto.moretags) = 'validate:"required"'];
}

message TreasureV2Resp {
  TreasureV2Item item = 1;
}

message TreasuresV2Req {
  repeated int64 treasure_ids = 1 [(gogoproto.moretags) = 'validate:"max=20,min=1,gt=0,required"'];
}

message TreasuresV2Resp {
  map<int64, TreasureV2Item> items = 1;
}

message TreasureV2Item {
  int64 id = 1;
  string name = 2;
  string note = 3;
  int32 treasure_type = 4;
  int64 stars = 5;
  int32 batch_op = 6;
  int32 msgbar = 7;
  string description_icon = 8;
  string base_icon = 9;
  string flow_icon = 10;
  string gif_icon = 11;
  string webp_icon = 12;
  string frame_icon = 13;
  string corner_icon = 14;
  int32 state = 15;
}

message TreasureDescriptionByLangReq {
  int64 treasure_id = 1;
  int64 lang_id = 2;
}

message TreasureDescriptionByLangResp {
  TreasureDescriptionByLangItem item = 1;
}

message TreasureDescriptionByLangItem {
  int64 id = 1;
  int64 lang_id = 2;
  int64 treasure_id = 3;
  string treasure_name = 4;
  string treasure_description = 5;
  string text_url = 6;
  string text_img = 7;
  string banner_url = 8;
  string banner_img = 9;
}

message TreasureSendV2Req {
  int64 mid = 1 [(gogoproto.moretags) = 'validate:"required"'];
  int64 treasure_id = 2 [(gogoproto.moretags) = 'validate:"required"'];
  int64 room_id = 3 [(gogoproto.moretags) = 'validate:"required"'];
  int64 number = 4 [(gogoproto.moretags) = 'validate:"required"'];
  int64 lang_id = 5 [(gogoproto.moretags) = 'validate:"required"'];
  int64 region_id = 6;
  string client_ip = 7 [(gogoproto.moretags) = 'validate:"required"'];
  string platform = 8 [(gogoproto.moretags) = 'validate:"required"'];
  string buvid = 9 [(gogoproto.moretags) = 'validate:"required"'];
}

message TreasureSendV2Resp {
  // 剩余的stars
  int64 stars = 1;
}