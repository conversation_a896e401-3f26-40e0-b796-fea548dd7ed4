// +bili:type=service
syntax = "proto3";
package main.account.international.identify.service.v1;

// import "google/api/annotations.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/intl/passport.service.identify;api";
option java_package = "com.bapis.intl.passport.service.identify";
option java_multiple_files = true;
option (wdcli.appid) = "main.account-international.intl-identify-service";

// GetCookieInfoReq request param for rpc CookieInfo
message GetCookieInfoReq {
  string cookie = 1;
}

// GetCookieInfoReply reply val for rpc CookieInfo
message GetCookieInfoReply {
  // 用户是否登录
  bool is_login = 1 [(gogoproto.jsontag) = "is_login", json_name = "is_login"];
  // user mid
  int64 mid = 2 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // cookie csrf
  // when token reqest this field is empty
  string csrf = 3 [(gogoproto.jsontag) = "csrfToken", json_name = "csrfToken"];
  // expire time(unix timestamp)
  int32 expires = 4 [(gogoproto.jsontag) = "expires", json_name = "expires"];
  // auth_type  用户登录授权类型  1:用户名+密码 3:邮箱 8:短信 9:ios游客 13:facebook 14:google 15:apple 16:twitter
  int32 auth_type = 5 [(gogoproto.jsontag) = "auth_type", json_name = "auth_type"];
}

// TokenReq request param for rpc TokenInfo
message GetTokenInfoReq {
  // user access token
  string token = 1 [(gogoproto.moretags) = "form:\"access_key\" validate:\"required\""];
  // buvid
  string buvid = 2 [(gogoproto.moretags) = "form:\"buvid\""];
}

// GetTokenInfoReply reply val for rpc TokenInfo
message GetTokenInfoReply {
  // 用户是否登录
  bool is_login = 1 [(gogoproto.jsontag) = "is_login", json_name = "is_login"];
  // user mid
  int64 mid = 2 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // cookie csrf
  // when token reqest this field is empty
  string csrf = 3 [(gogoproto.jsontag) = "csrfToken", json_name = "csrfToken"];
  // expire time(unix timestamp)
  int32 expires = 4 [(gogoproto.jsontag) = "expires", json_name = "expires"];
  // auth_type  用户登录授权类型  1:用户名+密码 3:邮箱 8:短信 9:ios游客 13:facebook 14:google 15:apple 16:twitter
  int32 auth_type = 5 [(gogoproto.jsontag) = "auth_type", json_name = "auth_type"];
}

service Identify {
  // GetCookieInfo identify info by cookie.
  rpc GetCookieInfo (GetCookieInfoReq) returns (GetCookieInfoReply);
  // GetTokenInfo identify info by token.
  rpc GetTokenInfo (GetTokenInfoReq) returns (GetTokenInfoReply);
}