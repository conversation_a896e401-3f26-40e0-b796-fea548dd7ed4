// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
import "google/protobuf/empty.proto";

package intl.videoup.service.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/intl/videoup.service;v1";
option (gogoproto.goproto_getters_all) = false;
option java_package = "com.bapis.intl.videoup.service";
option java_multiple_files = true;
option (wdcli.appid) = "intl.archive.videoup-service";


service IntlVideoUpService {
  // 稿件信息
  rpc AuditView(AidReq) returns (ViewReply);
  // 稿件内容池数据统计
  rpc ArcPoolStatistic(AidReq) returns(ArcPoolStatisticReply);
  // 批量稿件内容池数据统计[max200]
  rpc ArcsPoolStatistic(AidsReq) returns (ArcsPoolStatisticReply);
  // 触发同步数据至内容平台（把稿件最新数据同步到内容平台）
  rpc StationSync(AidReq) returns (.google.protobuf.Empty);
  // 审核结果回调
  rpc AuditCallback(AuditCallbackReq) returns (.google.protobuf.Empty);
  //稿件基本信息
  rpc ArcBase(AidReq) returns (ArcBaseReply);
  //[批量]稿件基本信息
  rpc BatchArcBase(AidsReq) returns (BatchArcBaseReply);
  // up主基本信息
  rpc UpsBase(MidsReq) returns(UpsBaseReply);
  // up主统计信息
  rpc UpStatistic(MidReq) returns(UpStatisticReply);
  // up主每日投稿数量
  rpc GetUpDailyArcCount(GetUpDailyArcCountReq) returns(GetUpDailyArcCountReply);
  // upsert lang
  rpc SaveLang(SaveLangReq) returns (.google.protobuf.Empty);
  // 将开放浏览的稿件锁定
  rpc Lock(LockReq) returns (.google.protobuf.Empty);
  // UP主结算类型
  rpc Settles(SettlesReq) returns (SettlesReply);
  // 签约类型列表
  rpc SignTypes(SignTypesReq) returns (SignTypesReply);
  // 稿件分区列表
  rpc Categories(CategoriesReq) returns (CategoriesReply);
  // UP主定时发布时间限制（给采集YouTube发布时间限流使用），返回该UP主允许设置的定时发布时间。
  rpc DelayTimeAllow(DelayTimeAllowReq) returns (DelayTimeAllowReply);
  // 更新风险标签
  rpc UpdateRiskMarks(UpdateRiskMarksReq) returns(.google.protobuf.Empty);
  // 移除稿件标签
  rpc RemoveArcTags(RemoveArcTagsReq) returns(.google.protobuf.Empty);
}

// 稿件状态
enum State {
  // 开放浏览
  Published = 0;
  // 锁定
  Lock = -4;
  // 打回
  Reject = -2;
  // 用户删除
  Deleted = -100;
  // 定时发布
  Delay = -40;
  // 创建已提交
  Submit = -30;
  // 转码失败
  XcodeFailed = -16;
  // 转码中
  Xcoding = -9;
  // 修复待审
  Fixed = -6;
  // 等待审核
  Wait = -1;
  // 草稿
  Draft = -31;
}

// 审核流程状态
enum AuditState {
  // 就绪，等待发送到内容平台
  Ready = 0;
  // 重试编辑失败
  SendEditRetryFailed = -4;
  // 重试添加失败
  SendAddRetryFailed = -3;
  // 编辑内容平台稿件失败（需重试）
  SentEditFailed = -2;
  // 往内容平台创建稿件失败（需重试）
  SentAddFailed = -1;
  // 正在审核
  Auditing = 1;
  // 审核完成（已回调）
  Done = 2;
}

// 稿件属性位
enum Attr{
  // 禁止转载
  NoReprint = 0;
  // 是否换源
  ReplaceVideo = 1;
}

// 投稿来源
enum UpFrom {
  Unknown = 0;
  // 国际创作者中心
  Studio = 1;
  // 主站国际投稿（隔离前的PUGC稿件）
  MainIntl = 2;
  // 后台导入主站稿件（默片）
  ImportMain = 3;
  // m站投稿
  MobileSite = 4;
  // Arcraper从国内导入
  ArcraperMain = 5;
  // Arcraper从YouTube导入
  ArcraperYoutube = 6;
  // Netflix导入
  ArcraperNetflix = 7;
  // BCN导入
  ArcraperBCN = 8;
  // APP
  App = 9;
}

//  稿件结算类型
enum Settle {
  SettleUnset = 0;
  // 未选择结类型，数据库里-1代表未选择
  UnSelect = -1;
  AnimeReview = 1;
  VideoDubbing = 2;
  VideoDubbingDialect = 3;
  VideoDubbingCreative = 4;
  VideoDubbingCreativeDialect = 5;
  AnimeSceneEditingMUL = 6;
  AnimeSceneEditingSingle = 7;
  Freestyle = 8;
  YoutuberOldVideo = 9;
  YoutuberNewVideo = 10;
  PUGC_AMV_MAD = 11;
  AnimeNewsUpdate = 12;
  Gaming = 13;
}

// 结算类型
enum SignType {
  // 未签约
  UnSigned = 0;
  // PUGC-优质 UGC
  HQ = 1;
  // PUGC-原生优质 UGC
  PUGC = 2;
  // Youtube UP主
  Youtube = 3;
  // MCN签约机构UP主
  MCN = 4;
  // 国内UP主
  Internal = 5;
  // 稿件采买 BCN:Bilibili Content Network
  BCN = 6;
}

enum SrcType {
  SrcUnknown = 0;
  // 国内供稿
  SrcInternal = 1;
  // 普通UGC
  SrcUGC = 2;
  // 海外付费
  SrcOverseaPaid = 3;
}

// 创作者细分
enum SubIdType {
  UnKnown = 0;

  SUB_ID_CONSUME_TO_PRODUCE = 100;  //消费转创作
  SUB_ID_REGIST_TO_PRODUCE = 101;    // 注册即创作
  SUB_ID_PERFECT_UGC = 200;   // 优质UGC独家
  SUB_ID_BCN = 201;    // BCN
  SUB_ID_YOUTUBER = 202;  // Youtuber
  SUB_ID_MCN = 203;   // MCN
  SUB_ID_SINGLE_UP = 300;  // 个人up
  SUB_ID_INTERNAL_MCN = 301; // 国内MCN
  SUB_ID_SILENCE = 400;   // 默片号
  SUB_ID_TECH_TEST = 401; // 技术测试号
  SUB_ID_ORGINAZATION = 500; // 机构号
  SUB_ID_OFFICIAL = 501; // 官方账号
  SUB_ID_TEST = 600;  // 测试
  SUB_ID_CHOSEN = 601;  // 精选
  SUB_ID_SHADOW = 602;  // 影子
  SUB_ID_OGV_UGC = 603;  // OGV-UGC
}

// 创作者类型
enum IdType {
  Unknow = 0;

  Id_TYPE_UGC = 1;   // 原生UGC
  Id_TYPE_PUGC = 2;  // PUGC
  Id_TYPE_INTERNAL = 3; // 国内up出海
  Id_TYPE_INHIBIT = 4; // 打压账号
  Id_TYPE_PGC = 5;     // PGC
  Id_TYPE_CONTENT_IMPORT = 6;  // 内容引入
}


message AidReq {
  // 稿件aid
  uint64 aid = 1 [(gogoproto.moretags) = 'form:"aid" validate:"gt=0,required" json:"aid"'];
  // 是否返回已删除的稿件
  bool with_deleted = 2;
}

message ViewReply {
  // 稿件信息
  ArchiveAudit archive = 1 [(gogoproto.jsontag) = "archive", json_name = "archive"];
}

message ArchiveAudit {
  // 稿件id
  uint64 aid = 1 [(gogoproto.jsontag) = "aid", json_name = "aid"];
  // 语言id
  uint32 langID = 2 [(gogoproto.jsontag) = "lang_id", json_name = "lang_id"];
  // 地区id
  uint32 regionID = 3 [(gogoproto.jsontag) = "region_id", json_name = "region_id"];
  // UP主id
  uint64 mid = 4 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // 分区id
  uint32 tid = 5 [(gogoproto.jsontag) = "tid", json_name = "tid"];
  // 标题
  string title = 6 [(gogoproto.jsontag) = "title", json_name = "title"];
  // 封面
  string cover = 7 [(gogoproto.jsontag) = "cover", json_name = "cover"];
  // 简介
  string content = 8 [(gogoproto.jsontag) = "content", json_name = "content"];
  // 属性位
  uint64 attribute = 9 [(gogoproto.jsontag) = "attribute", json_name = "attribute"];
  // 转载类型。1：转载；2：自制
  uint32 copyright = 10 [(gogoproto.jsontag) = "copyright", json_name = "copyright"];
  // 转载来源
  string source = 11 [(gogoproto.jsontag) = "source", json_name = "source"];
  // 视频时长
  uint32 duration = 12 [(gogoproto.jsontag) = "duration", json_name = "duration"];
  // 标签
  string tag = 13 [(gogoproto.jsontag) = "tag", json_name = "tag"];
  // 视频文件名
  string filename = 14 [(gogoproto.jsontag) = "filename", json_name = "filename"];
  // 切片数量
  uint32 shotCount = 15 [(gogoproto.jsontag) = "shot_count", json_name = "shot_count"];
  // 视频分辨率(以英文逗号隔开): X,Y
  string dimensions = 16 [(gogoproto.jsontag) = "dimensions", json_name = "dimensions"];
  // 稿件状态。参考State枚举
  int32 state = 17 [(gogoproto.jsontag) = "state", json_name = "state"];
  // 审核流程状态。参考AuditState枚举
  int32 auditState = 18 [(gogoproto.jsontag) = "audit_state", json_name = "audit_state"];
  // 打回理由
  string rejectReason = 19 [(gogoproto.jsontag) = "reject_reason", json_name = "reject_reason"];
  // 打回理由id
  uint32 reasonID = 20 [(gogoproto.jsontag) = "reason_id", json_name = "reason_id"];
  // 发布时间
  string ptime = 21 [(gogoproto.jsontag) = "ptime", json_name = "ptime"];
  // 投稿时间
  string ctime = 22 [(gogoproto.jsontag) = "ctime", json_name = "ctime"];
  // 编辑时间
  string mtime = 23 [(gogoproto.jsontag) = "mtime", json_name = "mtime"];
  // 定时发布时间的时区
  string timezone = 24 [(gogoproto.jsontag) = "timezone", json_name = "timezone"];
  // 定时发布时间（用户提交的值，要根据timezone解析）
  string dtime = 25 [(gogoproto.jsontag) = "dtime", json_name = "dtime"];
  // 定时发布时间（北京时区）
  string dtimeBejing = 26 [(gogoproto.jsontag) = "dtime_beijing", json_name = "dtime_beijing"];
  // 稿件属性位扩张信息
  AttrInfo attrInfo = 27 [(gogoproto.jsontag) = "attr_info", json_name = "attr_info"];
  // 新一级分区id
  uint32 CategoryID = 28 [(gogoproto.jsontag) = "category_id", json_name = "category_id"];
  // 活动id
  uint64 ActivityID = 29 [(gogoproto.jsontag) = "activity_id", json_name = "activity_id"];
  // [deprecated]ogv season title
  string OgvSeasonTitle = 30 [(gogoproto.jsontag) = "ogv_season_title", json_name="ogv_season_title", deprecated = true];
  // settle_type
  string SettleType = 31 [(gogoproto.jsontag) = "settle_type", json_name="settle_type", deprecated = true];
  // 二级分区id
  uint32 CategorySub = 32 [(gogoproto.jsontag) = "category_sub", json_name = "category_sub"];
  // 稿件结算类型
  Settle Settle = 33 [(gogoproto.jsontag) = "settle", json_name = "settle"];
  // filename 是否过期
  bool FilenameExpire = 34 [(gogoproto.jsontag) = "filename_expire", json_name = "filename_expire"];
  // 投稿来源
  UpFrom UpFrom = 35 [(gogoproto.jsontag) = "up_from", json_name = "up_from"];
  // 投稿地区
  string PubArea = 36 [(gogoproto.jsontag) = "pub_area", json_name = "pub_area"];

}

message ArcPoolStatisticReply {
  uint64 aid = 1 [(gogoproto.jsontag) = "aid", json_name = "aid"];
  int64 view_count = 2 [(gogoproto.jsontag) = "view_count", json_name = "view_count"];
  int64 reply_count = 3 [(gogoproto.jsontag) = "reply_count", json_name = "reply_count"];
  int64 like_count = 4 [(gogoproto.jsontag) = "like_count", json_name = "like_count"];
  int64 favorite_count = 5 [(gogoproto.jsontag) = "favorite_count", json_name = "favorite_count"];
  int64 share_count = 6 [(gogoproto.jsontag) = "share_count", json_name = "share_count"];
  int64 danmaku_count = 7 [(gogoproto.jsontag) = "danmaku_count", json_name = "danmaku_count"];
}

message ArcsPoolStatisticReply {
  map<uint64, ArcPoolStatisticReply> stats = 1 [(gogoproto.jsontag) = "stats", json_name = "stats"];
}

message AuditCallbackReq {
  //  稿件id
  uint64 aid = 1 [(gogoproto.moretags) = 'form:"aid" validate:"gt=0,required" json:"aid"'];
  // 审核结果。
  State state = 2 [(gogoproto.moretags) = 'form:"state" json:"state"'];
  // [字段已失效勿使用]打回/锁定理由id
  int32 reason_id = 3 [(gogoproto.moretags) = 'form:"reason_id" json:"reason_id"', deprecated = true];
  // [字段已失效勿使用]风险标
  repeated int64 risk_marks = 4 [(gogoproto.moretags) = 'form:"risk_marks" json:"risk_marks"', deprecated = true];
  // 违规类型id
  repeated int64 violation_ids = 5 [(gogoproto.moretags) = 'form:"violation_ids" json:"violation_ids"'];
  // 回调事件（空值代表旧的审核回调），先发、进审、出审
  string event = 6 [(gogoproto.moretags) = 'form:"event" json:"event"'];
  // 审核状态
  int32 audit_state = 7 [(gogoproto.moretags) = 'form:"audit_state" json:"audit_state"'];
}

message AidsReq {
  // 稿件aid
  repeated uint64 aids = 1 [(gogoproto.moretags) = 'form:"aids" json:"aids"'];
}

message ArcBase {// 稿件id
  uint64 aid = 1 [(gogoproto.jsontag) = "aid", json_name = "aid"];
  // 标题
  string title = 2 [(gogoproto.jsontag) = "title", json_name = "title"];
  // 封面
  string cover = 3 [(gogoproto.jsontag) = "cover", json_name = "cover"];
  // 稿件状态。参考State枚举
  int32 state = 4 [(gogoproto.jsontag) = "state", json_name = "state"];
  // 审核流程状态。参考AuditState枚举
  int32 audit_state = 5 [(gogoproto.jsontag) = "audit_state", json_name = "audit_state"];
  // UP主mid
  uint64 mid = 6 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // 投稿时间
  string ctime = 7 [(gogoproto.jsontag) = "ctime", json_name = "ctime"];
  // 稿件属性位扩张信息
  AttrInfo attr_info = 8 [(gogoproto.jsontag) = "attr_info", json_name = "attr_info"];
  // 新一级分区id
  uint32 category_id = 9 [(gogoproto.jsontag) = "category_id", json_name = "category_id"];
  // 活动id
  uint64 activity_id = 10 [(gogoproto.jsontag) = "activity_id", json_name = "activity_id"];
}

message ArcBaseReply {
  ArcBase base = 1 [(gogoproto.jsontag) = "base", json_name = "base"];
}

message BatchArcBaseReply {
  map<uint64, ArcBase> bases = 1 [(gogoproto.jsontag) = "bases", json_name = "bases"];
}

// 稿件属性位信息（限流等敏感属性位请不要暴露给前端！）
message AttrInfo {
  // 是否禁止转载
  bool noReprint = 1 [(gogoproto.jsontag) = "no_reprint", json_name = "no_reprint"];
  // 是否换源
  bool replace_video = 2  [(gogoproto.jsontag) = "replace_video", json_name = "replace_video"];
}

message MidsReq {
  repeated uint64 mids = 1 [(gogoproto.moretags) = 'form:"mids" validate:="gt=0,required" json:"mids"'];
}

message UpsBaseReply {
  //key：mid, value: up item
  map<uint64, UpBaseItem> ups = 1 [(gogoproto.jsontag) = "ups", json_name = "ups"];
}

message UpBaseItem {
  uint64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // 身份类型：1.普通up主 2.local up主 3.国内up主 4.内部账号
  uint32 id_type = 2 [(gogoproto.jsontag) = "id_type", json_name = "id_type"];
  // 认证类型：1.youtuber 2.学生
  uint32 cert_type = 3 [(gogoproto.jsontag) = "cert_type,omitempty", json_name = "cert_type"];
  // 首次投稿分区id
  uint32 category_id = 4 [(gogoproto.jsontag) = "category_id,omitempty", json_name = "category_id"];
  // 首次投稿时间
  string pub_time = 5 [(gogoproto.jsontag) = "pub_time,omitempty", json_name = "pub_time"];
  // 地区id
  uint32 region_id = 6 [(gogoproto.jsontag) = "region_id,omitempty", json_name = "region_id"];
  // 出生日期
  string birth = 7 [(gogoproto.jsontag) = "birth,omitempty", json_name = "birth"];
  // 联系方式
  string contact_info = 8 [(gogoproto.jsontag) = "contact_info,omitempty", json_name = "contact_info"];
  // 签约类型：0.未签约 1.优质up主签约 2.pugc签约 3.youtuber签约 4.MCN签约 5.国内up签约
  uint32 sign_type = 9 [(gogoproto.jsontag) = "sign_type", json_name = "sign_type"];
  // 底薪
  double basic_salary = 10 [(gogoproto.jsontag) = "basic_salary,omitempty", json_name = "basic_salary"];
  // 币种
  uint32 currency_type = 11 [(gogoproto.jsontag) = "currency_type,omitempty", json_name = "currency_type"];
  // 是否参与CPM激励计划
  bool is_cpm = 12 [(gogoproto.jsontag) = "is_cpm", json_name = "is_cpm"];
  // 昵称
  string username = 13 [(gogoproto.jsontag) = "username", json_name = "username"];
  // 头像
  string face = 14 [(gogoproto.jsontag) = "face", json_name = "face"];
  // 所属分区
  string category = 15 [(gogoproto.jsontag) = "category", json_name = "category"];
  // 所属地区
  string region = 16 [(gogoproto.jsontag) = "region", json_name = "region"];
  // 稿件总数
  uint64 archive_count = 17 [(gogoproto.jsontag) = "archive_count", json_name = "archive_count"];
  // 粉丝总数
  uint64 follower_count = 18 [(gogoproto.jsontag) = "follower_count", json_name = "follower_count"];
  // 二级身份类型
  uint32 sub_id_type = 19 [(gogoproto.jsontag) = "sub_id_type", json_name = "sub_id_type"];
  // vv数量
  int64 vv_count = 20 [(gogoproto.jsontag) = "vv_count", json_name = "vv_count"];
  // 距离上次活跃天数
  int32 inactive_day = 21 [(gogoproto.jsontag) = "inactive_day", json_name = "inactive_day"];
}

message MidReq {
  // 用户id
  uint64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:="gt=0,required" json:"mid"'];
}

message UpStatisticReply {
  uint64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // 稿件数
  int64 archive_count_total = 2 [(gogoproto.jsontag) = "archive_count_total", json_name = "archive_count_total"];
  int64 archive_count_7d = 3 [(gogoproto.jsontag) = "archive_count_7d", json_name = "archive_count_7d"];
  int64 archive_count_30d = 4 [(gogoproto.jsontag) = "archive_count_30d", json_name = "archive_count_30d"];
  // 播放数
  int64 view_count_total = 5 [(gogoproto.jsontag) = "view_count_total", json_name = "view_count_total"];
  int64 view_count_7d = 6 [(gogoproto.jsontag) = "view_count_7d", json_name = "view_count_7d"];
  int64 view_count_30d = 7 [(gogoproto.jsontag) = "view_count_30d", json_name = "view_count_30d"];
  // 点赞数
  int64 like_count_total = 8 [(gogoproto.jsontag) = "like_count_total", json_name = "like_count_total"];
  int64 like_count_7d = 9 [(gogoproto.jsontag) = "like_count_7d", json_name = "like_count_7d"];
  int64 like_count_30d = 10 [(gogoproto.jsontag) = "like_count_30d", json_name = "like_count_30d"];
  // 转发数
  int64 share_count_total = 11 [(gogoproto.jsontag) = "share_count_total", json_name = "share_count_total"];
  int64 share_count_7d = 12 [(gogoproto.jsontag) = "share_count_7d", json_name = "share_count_7d"];
  int64 share_count_30d = 13 [(gogoproto.jsontag) = "share_count_30d", json_name = "share_count_30d"];
  // 评论数
  int64 reply_count_total = 14 [(gogoproto.jsontag) = "reply_count_total", json_name = "reply_count_total"];
  int64 reply_count_7d = 15 [(gogoproto.jsontag) = "reply_count_7d", json_name = "reply_count_7d"];
  int64 reply_count_30d = 16 [(gogoproto.jsontag) = "reply_count_30d", json_name = "reply_count_30d"];
  // 弹幕数
  int64 dm_count_total = 17 [(gogoproto.jsontag) = "dm_count_total", json_name = "dm_count_total"];
  int64 dm_count_7d = 18 [(gogoproto.jsontag) = "dm_count_7d", json_name = "dm_count_7d"];
  int64 dm_count_30d = 19 [(gogoproto.jsontag) = "dm_count_30d", json_name = "dm_count_30d"];
  // 粉丝数
  int64 follower_count_7d = 20 [(gogoproto.jsontag) = "follower_count_7d", json_name = "follower_count_7d"];
  int64 follower_count_30d = 21 [(gogoproto.jsontag) = "follower_count_30d", json_name = "follower_count_30d"];
  int64 follower_count_total = 22 [(gogoproto.jsontag) = "follower_count_total", json_name = "follower_count_total"];
  // 距离上次活跃天数
  int32 inactive_day = 23 [(gogoproto.jsontag) = "inactive_day", json_name = "inactive_day"];
}

message GetUpDailyArcCountReq {
  // 用户id
  uint64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:="gt=0,required" json:"mid"'];
  // 开始时间
  int64 start_time = 2 [(gogoproto.moretags) = 'form:"start_time" validate:="required" json:"start_time"', (gogoproto.casttype) = "go-common/library/time.Time"];
  // 结束时间
  int64 end_time = 3 [(gogoproto.moretags) = 'form:"end_time" validate:="required" json:"end_time"', (gogoproto.casttype) = "go-common/library/time.Time"];
}

message GetUpDailyArcCountReply {
  //key：time, value: arc_count
  map<int64, uint64> arc_count = 1 [(gogoproto.jsontag) = "arc_count", json_name = "arc_count"];
}

message SaveLangReq {
  uint64 aid = 1 [(gogoproto.moretags) = 'validate:="gt=0,required"'];
  uint32 lang_id = 2 [(gogoproto.moretags) = 'validate:="gt=0,required"'];
  string cover = 3 [(gogoproto.moretags) = 'validate:="required"'];
  string title = 4 [(gogoproto.moretags) = 'validate:="required"'];
  string content = 5;
  string subtitle_url = 6;
  string subtitle_name = 7;
}

message LockReq {
  // 稿件id
  uint64 aid = 1;
  // 锁定理由id
  uint32 reason_id = 2 [deprecated = true];
}

message SettlesReq {
  // 用户id
  uint64 mid = 1 [(gogoproto.moretags) = 'form:"mid" json:"mid"'];
  // 是否只返回允许该mid选择的结算类型，false则返回所有结算类型
  bool only_allow = 2 [(gogoproto.moretags) = 'form:"only_allow" json:"only_allow"'];
  // 语言id
  uint32 lang_id = 3 [(gogoproto.moretags) = 'form:"lang_id" json:"lang_id"'];
}

message SettlesReply {
  repeated SettleItem settles = 1 [(gogoproto.jsontag) = "settles", json_name = "settles"];
}

message SettleItem {
  Settle id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
}message SignTypesReq {
  // 语言id
  uint32 lang_id = 1 [(gogoproto.moretags) = 'form:"lang_id" json:"lang_id"'];
}

message SignTypesReply {
  repeated SignTypeItem types = 1 [(gogoproto.jsontag) = "types", json_name = "types"];
}

message SignTypeItem {
  SignType id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
}

message CategoriesReq {
  // 语言id
  uint32 lang_id = 1 [(gogoproto.moretags) = 'form:"lang_id" json:"lang_id"'];
}

message CategoriesReply {
  repeated CategoryItem categories = 1 [(gogoproto.jsontag) = "categories", json_name = "categories"];
}

message CategoryItem {
  //分区id
  uint32 id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  // 分区名称
  string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
  // 子分区
  repeated CategoryItem sub = 3 [(gogoproto.jsontag) = "sub", json_name = "sub"];
}

message DelayTimeAllowReq {
  // UP主mid
  uint64 mid = 1 [(gogoproto.moretags) = 'form:"mid" json:"mid"'];
  // 一天限制多少个稿件发布
  uint32 threshold = 2 [(gogoproto.moretags) = 'form:"threshold" json:"threshold"', deprecated = true];
}

// 该UP主允许设置的定时发布时间
message DelayTimeAllowReply {
  // 定时发布时间时区
  string timezone = 1 [(gogoproto.jsontag) = "timezone", json_name = "timezone"];
  // 定时发布时间，格式：2006-01-02 15:04:05
  string dtime = 2 [(gogoproto.jsontag) = "dtime", json_name = "dtime"];
}

message UpdateRiskMarksReq {
  //  稿件id
  int64 aid = 1 [(gogoproto.moretags) = 'form:"aid" validate:"gt=0,required" json:"aid"'];
  // 风险标
  repeated int64 risk_marks = 2 [(gogoproto.moretags) = 'form:"risk_marks" json:"risk_marks"'];
}

// 移除稿件标签
message RemoveArcTagsReq {
  // 稿件aid
  int64 aid = 1 [(gogoproto.moretags) = 'validate:"gt=0,required"'];
  // 要移除的标签id
  repeated int64 tag_ids = 2 [(gogoproto.moretags) = 'validate:"gt=0,required"'];
}