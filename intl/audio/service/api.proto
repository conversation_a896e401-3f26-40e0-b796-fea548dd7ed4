// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
import "google/protobuf/empty.proto";

package intl.audio.service.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/intl/audio.service;v1";
option (gogoproto.goproto_getters_all) = false;
option java_package = "com.bapis.intl.audio.service";
option java_multiple_files = true;
option (wdcli.appid) = "intl.audio.audio-service";

// pogo 音频service
service Audio {
  // 审核结果回调
  rpc AuditCallback(AuditCallbackReq) returns (.google.protobuf.Empty);
}

message AuditCallbackReq {
  //  专辑/音频id
  int64 oid = 1 [(gogoproto.moretags) = 'form:"oid" validate:"gt=0,required" json:"oid"'];
  // 对象类型
  OType otype = 2 [(gogoproto.moretags) = 'form:"otype" json:"otype"'];
  // 审核结果。
  State state = 3 [(gogoproto.moretags) = 'form:"state" json:"state"'];
  // 违规类型id
  repeated int64 violation_ids = 4 [(gogoproto.moretags) = 'form:"violation_ids" json:"violation_ids"'];
  // 审核状态
  int32 audit_state = 5 [(gogoproto.moretags) = 'form:"audit_state" json:"audit_state"'];
}

// 专辑&音频状态
enum State {
  // 开放浏览
  Published = 0;
  // 等待审核
  Wait = -1;
  // 打回
  Rejected = -2;
  // 锁定
  Lock = -4;
  // 上传中
  Uploading = -5;
  // 上传失败
  UploadFailed = -7;
  // 转码失败
  XcodeFailed = -16;
  // 转码中
  Xcoding = -9;
  // 用户删除
  Deleted = -100;
}

// 对象类型
enum OType {
  // 专辑
  Album = 0;
  // 音频
  Episode = 1;
}