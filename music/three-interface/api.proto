syntax = "proto3";

package archive.copyright.music.three.interface.v1;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "archive.music-platform.music-three-interface";
option (gogoproto.goproto_getters_all) = false;
option go_package = "buf.bilibili.co/bapis/bapis-gen/music/three.interface;v1";
option java_package = "com.bapis.music.three.interfaces";
option java_multiple_files = true;


service MusicThreeService {
  // 通用预检接口
  rpc PreCheck(PreCheckReq) returns (PreCheckResp);
  // 通用发货
  rpc Delivery(DeliveryReq) returns (DeliveryResp);
}

message PreCheckReq {
  // 用户id
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
  // 主播/UP主id
  int64 ruid = 2 [(gogoproto.moretags) = 'form:"ruid"'];
  // 商品ID
  int64 goods_id = 3 [(gogoproto.moretags) = 'form:"goods_id"'];
  // 商品数量
  int64 goods_num = 4 [(gogoproto.moretags) = 'form:"goods_num"'];
  // 平台  ios/android/pc/h5
  string platform = 5 [(gogoproto.moretags) = 'form:"platform"'];
  // mobile_app
  string mobile_app = 6 [(gogoproto.moretags) = 'form:"mobile_app"'];
  // 商品单价
  int64 price = 7 [(gogoproto.moretags) = 'form:"price"'];
  // 附属业务信息 json格式
  string biz_extra = 8 [(gogoproto.moretags) = 'form:"biz_extra"'];
  // 折扣后价格
  int64 discount_price = 9 [(gogoproto.moretags) = 'form:"discount_price"'];
  // 自动续费价格
  int64 signed_price = 10 [(gogoproto.moretags) = 'form:"signed_price"'];
  //首次购买价格
  int64 first_price = 11 [(gogoproto.moretags) = 'form:"first_price"'];
  // ip 地址
  string ip = 12 [
    (gogoproto.jsontag) = "ip",
    (gogoproto.moretags) = 'form:"ip"',
    json_name = "ip"
  ];
  // 业务类型 101表示集卡业务
  int64 context_type = 13 [
    (gogoproto.jsontag) = "context_type",
    (gogoproto.moretags) = 'form:"context_type"',
    json_name = "context_type"
  ];
  // 业务id
  string context_id = 14 [
    (gogoproto.jsontag) = "context_id",
    (gogoproto.moretags) = 'form:"context_id"',
    json_name = "context_id"
  ];
}

message PreCheckResp {
  // 是否可购买  0:不可购买 1:可购买
  int64 can_buy = 1 [(gogoproto.jsontag) = "can_buy", json_name = "can_buy"];
  // 不可购买原因
  string reason = 2 [(gogoproto.jsontag) = "reason", json_name = "reason"];
  // 需要透传的错误码，不传会使用订单服务通用错误码
  string err_code = 3
  [(gogoproto.jsontag) = "err_code", json_name = "err_code"];
  // 修正后的订单总价，单位厘
  int64 total_price = 4
  [(gogoproto.jsontag) = "total_price", json_name = "total_price"];
  // 修正后的商品价格，单位厘
  int64 goods_price = 5
  [(gogoproto.jsontag) = "goods_price", json_name = "goods_price"];
  // 分成方ID
  int64 owner_id = 6 [(gogoproto.jsontag) = "owner_id", json_name = "owner_id"];
}

message DeliveryReq {
  // 用户id
  int64 uid = 1 [(gogoproto.moretags) = 'form:"uid" validate:"required"'];
  // 主播/UP主id
  int64 ruid = 2 [(gogoproto.moretags) = 'form:"ruid"'];
  // 商品ID
  int64 goods_id = 3 [(gogoproto.moretags) = 'form:"goods_id"'];
  // 商品数量
  int64 goods_num = 4 [(gogoproto.moretags) = 'form:"goods_num"'];
  // 平台  ios/android/pc/h5
  string platform = 5 [(gogoproto.moretags) = 'form:"platform"'];
  // mobile_app
  string mobile_app = 6 [(gogoproto.moretags) = 'form:"mobile_app"'];
  // 订单金额
  int64 total_price = 7 [(gogoproto.moretags) = 'form:"total_price"'];
  // 订单号
  string order_id = 8
  [(gogoproto.moretags) = 'form:"order_id" validate:"required"'];
  // 订单创建时间
  string order_create_time = 9
  [(gogoproto.moretags) = 'form:"order_create_time"'];
  // 101: 集卡业务
  int64 context_type = 10 [(gogoproto.moretags) = 'form:"context_type"'];
  // 场景id
  string context_id = 11 [(gogoproto.moretags) = 'form:"context_id"'];
  // 附属业务信息 json格式
  string biz_extra = 12 [(gogoproto.moretags) = 'form:"biz_extra"'];
  // 商品单价
  int64 price = 13 [
    (gogoproto.jsontag) = "price",
    (gogoproto.moretags) = 'form:"price"',
    json_name = "price"
  ];
}

message DeliveryResp {
  // 发货状态
  // 0: 未发货(订单会重试发货)
  // 100: 发货成功
  // 101: 发货失败(会退款)
  // 102: 发货失败(不会退款)
  int64 status = 1 [(gogoproto.jsontag) = "status", json_name = "status"];
}