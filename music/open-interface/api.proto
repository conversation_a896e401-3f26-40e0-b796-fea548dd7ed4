syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

package archive.music.open.interface.v1;

option (wdcli.appid) = "main.archive.music-interface";
option go_package = "buf.bilibili.co/bapis/bapis-gen/music/open.interface;v1";
option java_package = "com.bapis.music.open.interfaces";
option java_multiple_files = true;
option (gogoproto.goproto_getters_all) = true;

service MusicOpenInterfaceService {
  // 批量创建作品&授权
  rpc BatchAuth(BatchAuthReq) returns (BatchAuthResp);
}

enum MusicType {
  // 未知
  MusicTypeUnknown = 0;
  // 原唱歌曲
  MusicTypeOriSong = 1;
  // 翻唱歌曲
  MusicTyeCovSong = 2;
  // 原创纯音乐
  MusicTyeOriAbs = 3;
  // 翻作纯音乐
  MusicTyeCovAbs = 4;
}

enum MediumType {
  // 未知
  MediumTypeUnknown = 0;
  // 音频
  MediumTypeMusic = 1;
  // 稿件
  MediumTypeArc = 2;
}

enum AuthArea {
  // 未知
  AuthAreaUnknown = 0;
  // 全球
  AuthAreaGlobal = 1;
  // 中国大陆
  AuthAreaMainland = 2;
  // 大中华区
  AuthAreaChina = 3;
}

message AuthInfo {
  int64 uid = 1;                // 艺人站内ID
  int64 aid = 2;               // aid
  string music_key = 3;         // 音频boss_key
  MusicType music_type = 4;     // 作品类型
  string music_name = 5;        // 音乐名称
  string language = 6;          // 语种
  string music_style = 7;       // 音乐风格
  string lyric_key = 8;         // 歌词boss_key
  string singer = 9;            // 表演者
  string lyricist = 10;         // 词作者
  string composer = 11;         // 曲作者
  string record = 12;           // 录音师
  string cover_key = 13;        // 封面boss_key
  MediumType medium_type = 14;  // 上传方式
  string auth_files = 15;       // 授权文件 多个用,连接
  AuthArea auth_area = 16;      // 授权地区
  int64 auth_start = 17 [(gogoproto.casttype) = "go-common/library/time.Time"];  // 授权开始时间
  int64 auth_end = 18 [(gogoproto.casttype) = "go-common/library/time.Time"];  // 授权结束时间
  string username = 19;                                        // 授权姓名
  string id_card = 20;                                         // 授权卡号
  int64 card_type = 21;  // 授权卡号类型
  string auth_type = 22;  // 认证信息是否 手动填写 /  系统接口下发 1
  string album_name = 23; // 专辑名称
  int64 publish_time = 24 [(gogoproto.casttype) = "go-common/library/time.Time"];  // 授权开始时间
  int64 duration = 25;
  string isrc = 26;
  string third_id = 27;
  string upc = 28;
}

message FailedAuthInfo {
  AuthInfo auth_info = 1;
  string msg = 2;
}

message BatchAuthReq {
  string token = 1;
  repeated AuthInfo auth_info = 2;
}

message BatchAuthResp {
  repeated FailedAuthInfo failed_auth_info = 2;
}