syntax = "proto3";
package main.account.ugcpay.service.model;
option go_package = "buf.bilibili.co/bapis/bapis-gen/account/ugcpay.service.model;v1";
option java_package = "com.bapis.account.service.ugcpay.rank.model";
option java_multiple_files = true;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

message RankElecPrepUPProto {
  int64 CountUPTotalElec = 1;
  int64 Count = 2;
  int64 UPMID = 3;
  int64 Size = 4;
  repeated RankElecPrepElementProto List = 5;
}

message RankElecPrepAVProto {
  RankElecPrepUPProto RankElecPrepUPProto = 1
      [(gogoproto.nullable) = false, (gogoproto.embed) = true];
  int64 AVID = 2;
}

message RankElecPrepElementProto {
  int64 MID = 1 [(gogoproto.jsontag) = "pay_mid", json_name = "pay_mid"];
  int64 Rank = 2 [
    (gogoproto.jsontag) = "rank",
    json_name = "rank"
  ];
  uint32 TrendType = 3 [
    (gogoproto.jsontag) = "trend_type",
    json_name = "trend_type"
  ];
  int64 Amount = 4 [(gogoproto.jsontag) = "-"];
  ElecMessageProto Message = 5 [
    (gogoproto.jsontag) = "",
    (gogoproto.nullable) = false,
    (gogoproto.embed) = true
  ];
  int64 UpMID = 6 [(gogoproto.jsontag) = "mid", json_name = "mid"];
}

message RankElecUPProto {
  int64 CountUPTotalElec = 1 [(gogoproto.jsontag) = "total", json_name = "total"];
  int64 Count = 2 [(gogoproto.jsontag) = "count", json_name = "count"];
  int64 UPMID = 3 [(gogoproto.jsontag) = "-"];
  int64 Size = 4 [ (gogoproto.jsontag) = "-"];
  repeated RankElecElementProto List = 5 [(gogoproto.jsontag) = "list", json_name = "list"];
  string rank_title = 6; //榜单文字，老充电无，新充电有
  string rank_url = 7; //榜单跳转链接，老充电无，新充电有
}

message RankElecAVProto {
  int64 CountUPTotalElec = 1
      [(gogoproto.jsontag) = "total", json_name = "total"];
  int64 Count = 2 [(gogoproto.jsontag) = "count", json_name = "count"];
  int64 AVID = 3 [(gogoproto.jsontag) = "-"];
  int64 UPMID = 4 [(gogoproto.jsontag) = "-"];
  int64 Size = 5 [ (gogoproto.jsontag) = "-"];
  repeated RankElecElementProto List = 6
      [(gogoproto.jsontag) = "list", json_name = "list"];
}

message RankElecElementProto {
  RankElecPrepElementProto RankElecPrepElementProto = 1 [
    (gogoproto.jsontag) = "",
    (gogoproto.nullable) = false,
    (gogoproto.embed) = true
  ];
  VIPInfoProto VIP = 2
      [(gogoproto.jsontag) = "vip_info", json_name = "vip_info"];
  string Nickname = 3 [(gogoproto.jsontag) = "uname", json_name = "uname"];
  string Avatar = 4 [(gogoproto.jsontag) = "avatar", json_name = "avatar"];
}

message ElecMessageProto {
  string Message = 1 [(gogoproto.jsontag) = "message", json_name = "message"];
  bool Hidden = 2
      [(gogoproto.jsontag) = "-"];
}

message VIPInfoProto {
  int32 Type = 1 [(gogoproto.jsontag) = "vipType", json_name = "vipType"];
  int32 Status = 2 [(gogoproto.jsontag) = "vipStatus", json_name = "vipStatus"];
  int64 DueDate = 3
      [(gogoproto.jsontag) = "vipDueMsec", json_name = "vipDueMsec"];
}