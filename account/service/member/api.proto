syntax = "proto3";
package account.service.member.v1;

import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "account.service.member";
option go_package = "buf.bilibili.co/bapis/bapis-gen/account/service.member.v1;api";
option java_package = "com.bapis.account.service.member";
option java_multiple_files = true;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

message MidReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 真实ip
  string realIP = 3 [ (gogoproto.jsontag) = "realIP", json_name = "realIP" ];
}

message MemberMidReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 远端ip
  string remoteIP = 2 [ (gogoproto.jsontag) = "remoteIP", json_name = "remoteIP" ];
}

message MemberMidsReq {
  // mid 列表
  repeated int64 mids = 1 [ (gogoproto.jsontag) = "mids", json_name = "mids" ];
  // 远端ip
  string remoteIP = 2 [ (gogoproto.jsontag) = "remoteIP", json_name = "remoteIP" ];
}

message MidByRealnameCardsReq {
  // 身份证号
  repeated string card_code = 1
      [ (gogoproto.moretags) =
            "form:\"card_code,split\" validate:\"required\"" ];
  // 国家
  int32 country = 2 [
    (gogoproto.jsontag) = "country", json_name = "country",
    (gogoproto.moretags) = "form:\"country\""
  ];
  // 证件类型
  int32 card_type = 3 [
    (gogoproto.jsontag) = "card_type", json_name = "card_type",
    (gogoproto.moretags) = "form:\"card_type\""
  ];
}

// 空的message，对应真实service只返回error，没有具体返回值
message EmptyStruct {}

message LevelInfoReply {
  // 当前的等级信息
  int32 cur = 1 [ (gogoproto.jsontag) = "current_level", json_name = "current_level" ];
  // 当前等级的经验阀值
  int32 min = 2 [ (gogoproto.jsontag) = "current_min", json_name = "current_min" ];
  // 当前的经验值
  int32 now_exp = 3 [ (gogoproto.jsontag) = "current_exp", json_name = "current_exp" ];
  // 下个等级的经验阀值
  int32 next_exp = 4 [ (gogoproto.jsontag) = "next_exp", json_name = "next_exp" ];
  // 上一次经验等级升级的时间
  int64 levelUp = 5 [(gogoproto.jsontag) = "level_up", json_name = "level_up", (gogoproto.casttype) = "go-common/library/time.Time"];

}

message UserLogReply {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // ip
  string ip = 2 [ (gogoproto.jsontag) = "ip", json_name = "ip" ];
  // 时间戳
  int64 ts = 3 [ (gogoproto.jsontag) = "ts", json_name = "ts" ];
  // logId
  string log_id = 4 [ (gogoproto.jsontag) = "log_id", json_name = "log_id" ];
  // 具体内容
  map<string, string> content = 5 [ (gogoproto.jsontag) = "content", json_name = "content" ];
}

message UserLogsReply {
  // UserLog的集合
  repeated UserLogReply user_logs = 1 [ (gogoproto.jsontag) = "user_logs", json_name = "user_logs" ];
}

message AddExpReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 增量
  double count = 2 [ (gogoproto.jsontag) = "count", json_name = "count" ];
  // 修改原因
  string reason = 3 [ (gogoproto.jsontag) = "reason", json_name = "reason" ];
  // 操作类型
  string operate = 4 [ (gogoproto.jsontag) = "operate", json_name = "operate" ];
  // ip
  string ip = 5 [ (gogoproto.jsontag) = "ip", json_name = "ip" ];
}

message ExpStatReply {
  // 是否获取过登陆奖励
  bool login = 1 [ (gogoproto.jsontag) = "login", json_name = "login" ];
  // 是否获取过看视频的奖励
  bool watch = 2 [ (gogoproto.jsontag) = "watch_av", json_name = "watch_av" ];
  // 是否获得投币奖励
  int64 coin = 3 [ (gogoproto.jsontag) = "coins_av", json_name = "coins_av" ];
  // 是否获得分享过视频的奖励
  bool share = 4 [ (gogoproto.jsontag) = "share_av", json_name = "share_av" ];
}

// member
message BaseInfoReply {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 名称
  string name = 2 [ (gogoproto.jsontag) = "name", json_name = "name" ];
  // 性别
  int64 sex = 3 [ (gogoproto.jsontag) = "sex", json_name = "sex" ];
  // 头像
  string face = 4 [ (gogoproto.jsontag) = "face", json_name = "face" ];
  // 签名
  string sign = 5 [ (gogoproto.jsontag) = "sign", json_name = "sign" ];
  // 排名
  int64 rank = 6 [ (gogoproto.jsontag) = "rank", json_name = "rank" ];
  // 生日
  int64 birthday = 7 [
    (gogoproto.jsontag) = "birthday", json_name = "birthday",
    (gogoproto.casttype) = "go-common/library/time.Time"
  ];
  // 状态
  int64 status = 8 [(gogoproto.jsontag) = "status", json_name = "status"];
  // 账号是否已注销
  int32 is_deleted = 9 [(gogoproto.jsontag) = "is_deleted", json_name = "is_deleted"];
  // 注册账号被管控
  int32 in_reg_audit = 10 [(gogoproto.jsontag) = "in_reg_audit", json_name = "in_reg_audit"];
  int32 face_nft = 11 [(gogoproto.jsontag) = "face_nft", json_name = "face_nft"];
  int32 is_senior_member = 12 [(gogoproto.jsontag) = "is_senior_member", json_name = "is_senior_member"];
}

message OfficialInfoReply {
  // 角色类别
  int32 role = 1
      [ (gogoproto.jsontag) = "role", json_name = "role" ];
  // 标题
  string title = 2 [ (gogoproto.jsontag) = "title", json_name = "title" ];
  // 描述
  string desc = 3 [ (gogoproto.jsontag) = "desc", json_name = "desc" ];
}

message BaseInfosReply {
  // member基本信息集合
  map<int64, BaseInfoReply> base_infos = 1;
}

message MemberInfoReply {
  // 基本信息
  BaseInfoReply base_info = 1;
  // 等级信息
  LevelInfoReply level_info = 2;
  // 官方认证信息
  OfficialInfoReply official_info = 3;
}

message MemberInfosReply { map<int64, MemberInfoReply> member_infos = 1; }

message NickUpdatedReply {
  // 昵称是否修改过
  bool nick_updated = 1 [ (gogoproto.jsontag) = "nick_updated", json_name = "nick_updated" ];
}

message OfficialDocReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 名称
  string name = 2 [ (gogoproto.jsontag) = "name", json_name = "name" ];
  // 审核状态
  int32 state = 3
      [ (gogoproto.jsontag) = "state", json_name = "state" ];
  // 角色
  int32 role = 4
      [ (gogoproto.jsontag) = "role", json_name = "role" ];
  // 标题
  string title = 5 [ (gogoproto.jsontag) = "title", json_name = "title" ];
  // 描述
  string desc = 6 [ (gogoproto.jsontag) = "desc", json_name = "desc" ];
  // 拒绝原因
  string reject_reason = 7 [ (gogoproto.jsontag) = "reject_reason", json_name = "reject_reason" ];
  // 真实名字
  int32 realname = 8
      [ (gogoproto.jsontag) = "realname", json_name = "realname" ];
  // 操作人
  string operator = 9 [ (gogoproto.jsontag) = "operator", json_name = "operator" ];
  // 电话
  string telephone = 10 [ (gogoproto.jsontag) = "telephone", json_name = "telephone" ];
  // 邮箱
  string email = 11 [ (gogoproto.jsontag) = "email", json_name = "email" ];
  // 地址
  string address = 12 [ (gogoproto.jsontag) = "address", json_name = "address" ];
  // 公司
  string company = 13 [ (gogoproto.jsontag) = "company", json_name = "company" ];
  // 统一信用代码
  string credit_code = 14 [ (gogoproto.jsontag) = "credit_code", json_name = "credit_code" ];
  // 组织机构
  string organization = 15 [ (gogoproto.jsontag) = "organization", json_name = "organization" ];
  // 组织机构类型
  string organization_type = 16 [ (gogoproto.jsontag) = "organization_type", json_name = "organization_type" ];
  // 营业执照
  string business_license = 17 [ (gogoproto.jsontag) = "business_license", json_name = "business_license" ];
  // 企业规模
  string business_scale = 18 [ (gogoproto.jsontag) = "business_scale", json_name = "business_scale" ];
  // 企业等级
  string business_level = 19 [ (gogoproto.jsontag) = "business_level", json_name = "business_level" ];
  // 企业授权函
  string business_auth = 20 [ (gogoproto.jsontag) = "business_auth", json_name = "business_auth" ];
  // 其他补充资料
  string supplement = 21 [ (gogoproto.jsontag) = "supplement", json_name = "supplement" ];
  // 专业资质
  string professional = 22 [ (gogoproto.jsontag) = "professional", json_name = "professional" ];
  // 身份证明
  string identification = 23 [ (gogoproto.jsontag) = "identification", json_name = "identification" ];
  // 提交来源
  string submit_source = 24 [ (gogoproto.jsontag) = "submit_source", json_name = "submit_source" ];
  // 官网地址
  string official_site = 25 [ (gogoproto.jsontag) = "official_site", json_name = "official_site" ];
  // 注册资本
  string registered_capital = 26 [ (gogoproto.jsontag) = "registered_capital", json_name = "registered_capital" ];
  // 操作类型
  string submit_type = 27 [ (gogoproto.jsontag) = "submit_type", json_name = "submit_type" ];
  // 取消认证原因
  string cancel_reason = 28 [ (gogoproto.jsontag) = "cancel_reason", json_name = "cancel_reason" ];
  // 企业自媒体确认函
  string enterprise_we_media_confirmation = 29
      [ (gogoproto.jsontag) = "enterprise_we_media_confirmation", json_name = "enterprise_we_media_confirmation" ];
  // 团队自媒体确认函
  string group_we_media_confirmation = 30
      [ (gogoproto.jsontag) = "group_we_media_confirmation", json_name = "group_we_media_confirmation" ];
}

message UpdateSexReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 性别
  int64 sex = 2 [ (gogoproto.jsontag) = "sex", json_name = "sex" ];
  // 远端ip
  string remote_IP = 3 [ (gogoproto.jsontag) = "remoteIP", json_name = "remoteIP" ];
}

message UpdateUnameReq {
  // mid
  int64 mid = 1 [
    (gogoproto.jsontag) = "mid", json_name = "mid",
    (gogoproto.moretags) = "form:\"mid\" validate:\"required\""
  ];
  // 名称
  string name = 2 [
    (gogoproto.jsontag) = "name", json_name = "name",
    (gogoproto.moretags) = "form:\"name\" validate:\"required\""
  ];
  // 远端ip
  string remote_IP = 3 [ (gogoproto.jsontag) = "remoteIP", json_name = "remoteIP" ];
  bool skip_name_check = 4 [
    (gogoproto.jsontag) = "skip_name_check", json_name = "skip_name_check",
    (gogoproto.moretags) = "form:\"skip_name_check\""
  ];
  bool skip_name_lock = 5 [
    (gogoproto.jsontag) = "skip_name_lock", json_name = "skip_name_lock",
    (gogoproto.moretags) = "form:\"skip_name_lock\""
  ];
  // 请求来源，目前有游戏注册，非游戏注册
  string source = 6 [
    (gogoproto.jsontag) = "source", json_name = "source",
    (gogoproto.moretags) = "form:\"source\""
  ];
}

message CanSetNameReq {
  int64 mid = 1
      [ (gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = "form:\"mid\"" ];
  string name = 2 [
    (gogoproto.jsontag) = "name", json_name = "name",
    (gogoproto.moretags) = "form:\"name\" validate:\"required\""
  ];
}

message UpdateBaseReq {
  int64 mid = 1 [
    (gogoproto.jsontag) = "mid", json_name = "mid",
    (gogoproto.moretags) = "form:\"mid\" validate:\"required\""
  ];
  string name = 2 [
    (gogoproto.jsontag) = "name", json_name = "name",
    (gogoproto.moretags) = "form:\"name\" validate:\"required\""
  ];
  string face = 3
      [ (gogoproto.jsontag) = "face", json_name = "face", (gogoproto.moretags) = "form:\"face\"" ];
  string sign = 4 [
    (gogoproto.jsontag) = "sign", json_name = "sign",
    (gogoproto.moretags) = "form:\"user_sign\""
  ];
  int64 rank = 5
      [ (gogoproto.jsontag) = "rank", json_name = "rank", (gogoproto.moretags) = "form:\"rank\"" ];
  int64 birthday = 6 [
    (gogoproto.jsontag) = "birthday", json_name = "birthday",
    (gogoproto.casttype) = "go-common/library/time.Time",
    (gogoproto.moretags) = "form:\"birthday\""
  ];
  int64 sex = 7
      [ (gogoproto.jsontag) = "sex", json_name = "sex", (gogoproto.moretags) = "form:\"sex\"" ];
  bool skip_name_check = 8 [
    (gogoproto.jsontag) = "skip_name_check", json_name = "skip_name_check",
    (gogoproto.moretags) = "form:\"skip_name_check\""
  ];
  bool skip_name_lock = 9 [
    (gogoproto.jsontag) = "skip_name_lock", json_name = "skip_name_lock",
    (gogoproto.moretags) = "form:\"skip_name_lock\""
  ];
}

message UpdateFaceReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 头像
  string face = 2 [ (gogoproto.jsontag) = "face", json_name = "face" ];
  // 远端ip
  string remote_IP = 3 [ (gogoproto.jsontag) = "remoteIP", json_name = "remoteIP" ];
}

message UpdateRankReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 排行
  int64 rank = 2 [ (gogoproto.jsontag) = "rank", json_name = "rank" ];
  // 远端ip
  string remote_IP = 3 [ (gogoproto.jsontag) = "remoteIP", json_name = "remoteIP" ];
}

message UpdateBirthdayReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 生日
  int64 birthday = 2 [
    (gogoproto.jsontag) = "birthday", json_name = "birthday",
    (gogoproto.casttype) = "go-common/library/time.Time"
  ];
  // 远端ip
  string remote_IP = 3 [ (gogoproto.jsontag) = "remoteIP", json_name = "remoteIP" ];
}

message UpdateSignReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 签名
  string sign = 2 [ (gogoproto.jsontag) = "sign", json_name = "sign" ];
  // 远端ip
  string remote_IP = 3 [ (gogoproto.jsontag) = "remoteIP", json_name = "remoteIP" ];
}

message SetSeniorMemberReq {
  // mid
  int64 mid = 1 [(gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
  // 来源
  string source = 2 [(gogoproto.moretags) = "form:\"source\" validate:\"required\""];
  // 远端ip
  string remote_IP = 3 [ (gogoproto.jsontag) = "remoteIP", json_name = "remoteIP" ];
}

message SeniorMemberCancelReq {
  // mid
  int64 mid = 1 [(gogoproto.moretags) = "form:\"mid\" validate:\"required\"", json_name = "mid"];
  // 来源
  string source = 2 [(gogoproto.moretags) = "form:\"source\" validate:\"required\"", json_name = "source"];
  // 远端ip
  string remote_IP = 3 [(gogoproto.jsontag) = "remoteIP", json_name = "remoteIP"];
}

message SeniorMemberInfo {
  // 过期时间
  int64 valid_to = 2 [ (gogoproto.jsontag) = "valid_to", json_name = "valid_to" ];
  // 成为硬核会员的时间
  int64 valid_from = 3 [ (gogoproto.jsontag) = "valid_from", json_name = "valid_from" ];
}

message OfficialDocInfoReply {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 名称
  string name = 2 [ (gogoproto.jsontag) = "name", json_name = "name" ];
  // 审核状态
  int32 state = 3 [ (gogoproto.jsontag) = "state", json_name = "state" ];
  // 角色类型
  int32 role = 4
      [ (gogoproto.jsontag) = "role", json_name = "role" ];
  // 认证称号
  string title = 5 [ (gogoproto.jsontag) = "title", json_name = "title" ];
  // 认证后缀
  string desc = 6 [ (gogoproto.jsontag) = "desc", json_name = "desc" ];
  // 被拒绝原因
  string reject_reason = 7 [ (gogoproto.jsontag) = "reject_reason", json_name = "reject_reason" ];
  // 真实姓名
  int32 realname = 8
      [ (gogoproto.jsontag) = "realname", json_name = "realname" ];
  // 经营人
  string operator = 9 [ (gogoproto.jsontag) = "operator", json_name = "operator" ];
  // 电话好吗
  string telephone = 10 [ (gogoproto.jsontag) = "telephone", json_name = "telephone" ];
  // 邮箱
  string email = 11 [ (gogoproto.jsontag) = "email", json_name = "email" ];
  // 地址
  string address = 12 [ (gogoproto.jsontag) = "address", json_name = "address" ];
  // 公司
  string company = 13 [ (gogoproto.jsontag) = "company", json_name = "company" ];
  // 社会信用代码
  string credit_code = 14 [ (gogoproto.jsontag) = "credit_code", json_name = "credit_code" ];
  // 政府或组织机构名称
  string organization = 15 [ (gogoproto.jsontag) = "organization", json_name = "organization" ];
  // 组织或机构类型
  string organization_type = 16 [ (gogoproto.jsontag) = "organization_type", json_name = "organization_type" ];
  // 营业执照
  string business_license = 17 [ (gogoproto.jsontag) = "business_license", json_name = "business_license" ];
  // 企业规模
  string business_scale = 18 [ (gogoproto.jsontag) = "business_scale", json_name = "business_scale" ];
  // 企业等级
  string business_level = 19 [ (gogoproto.jsontag) = "business_level", json_name = "business_level" ];
  // 企业授权函
  string business_auth = 20 [ (gogoproto.jsontag) = "business_auth", json_name = "business_auth" ];
  // 其他补充资料
  string supplement = 21 [ (gogoproto.jsontag) = "supplement", json_name = "supplement" ];
  // 专业资质
  string professional = 22 [ (gogoproto.jsontag) = "professional", json_name = "professional" ];
  // 身份证明
  string identification = 23 [ (gogoproto.jsontag) = "identification", json_name = "identification" ];
  // 官网地址
  string official_site = 24 [ (gogoproto.jsontag) = "official_site", json_name = "official_site" ];
  // 注册资本
  string registered_capital = 25 [ (gogoproto.jsontag) = "registered_capital", json_name = "registered_capital" ];
  // 申请操作
  string submit_type = 26 [ (gogoproto.jsontag) = "submit_type", json_name = "submit_type" ];
  // 企业自媒体确认函
  string enterprise_we_media_confirmation = 27
      [ (gogoproto.jsontag) = "enterprise_we_media_confirmation", json_name = "enterprise_we_media_confirmation" ];
  // 团队自媒体确认函
  string group_we_media_confirmation = 28
      [ (gogoproto.jsontag) = "group_we_media_confirmation", json_name = "group_we_media_confirmation" ];
}

message OfficialDocExternalInfoReply {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 名称
  string name = 2 [ (gogoproto.jsontag) = "name", json_name = "name" ];
  // 审核状态
  int32 state = 3 [ (gogoproto.jsontag) = "state", json_name = "state" ];
  // 角色类型
  int32 role = 4 [ (gogoproto.jsontag) = "role", json_name = "role" ];
  // 认证称号
  string title = 5 [ (gogoproto.jsontag) = "title", json_name = "title" ];
  // 认证后缀
  string desc = 6 [ (gogoproto.jsontag) = "desc", json_name = "desc" ];
  // 公司
  string company = 7 [ (gogoproto.jsontag) = "company", json_name = "company" ];
  // 社会信用代码
  string credit_code = 8 [ (gogoproto.jsontag) = "credit_code", json_name = "credit_code" ];
  // 运营人员联系手机
  string mobile           = 9 [(gogoproto.jsontag) = "mobile", json_name = "mobile"];
  // 运营人员联系邮箱
  string email            = 10 [(gogoproto.jsontag) = "email", json_name = "email"];
  // 营业执照
  string business_license = 11 [(gogoproto.jsontag) = "business_license", json_name = "business_license"];
  // 运营人员姓名
  string operator         = 12 [(gogoproto.jsontag) = "operator", json_name = "operator"];
}

// moral
message MoralReply {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 节操值
  int64 moral = 2 [ (gogoproto.jsontag) = "moral", json_name = "moral" ];
  // 增量
  int64 added = 3 [ (gogoproto.jsontag) = "added", json_name = "added" ];
  // 减量
  int64 deducted = 4 [ (gogoproto.jsontag) = "deducted", json_name = "deducted" ];
  // 上一次节操低于70时的恢复时间
  int64 last_recover_date = 5 [
    (gogoproto.jsontag) = "last_recover_date", json_name = "last_recover_date",
    (gogoproto.casttype) = "go-common/library/time.Time"
  ];
}

message UpdateMoralReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 增量
  int64 delta = 2 [ (gogoproto.jsontag) = "delta", json_name = "delta" ];
  // 类型
  int64 origin = 3 [ (gogoproto.jsontag) = "origin", json_name = "origin" ];
  // 操作原因
  string reason = 4 [ (gogoproto.jsontag) = "reason", json_name = "reason" ];
  // 操纵原因类型
  int64 reason_type = 5 [ (gogoproto.jsontag) = "reason_type", json_name = "reason_type" ];
  // 操作人
  string operator = 6 [ (gogoproto.jsontag) = "operator", json_name = "operator" ];
  // 备注
  string remark = 7 [ (gogoproto.jsontag) = "remark", json_name = "remark" ];
  // 状态
  int64 status = 8 [ (gogoproto.jsontag) = "status", json_name = "status" ];
  // 是否通知
  bool is_notify = 9 [ (gogoproto.jsontag) = "is_notify", json_name = "is_notify" ];
  // ip地址
  string ip = 10 [ (gogoproto.jsontag) = "ip", json_name = "ip" ];
}

message UpdateMoralsReq {
  // mid 列表
  repeated int64 mids = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 增量
  int64 delta = 2 [ (gogoproto.jsontag) = "delta", json_name = "delta" ];
  // 类型
  int64 origin = 3 [ (gogoproto.jsontag) = "origin", json_name = "origin" ];
  // 操作原因
  string reason = 4 [ (gogoproto.jsontag) = "reason", json_name = "reason" ];
  // 操纵原因类型
  int64 reason_type = 5 [ (gogoproto.jsontag) = "reason_type", json_name = "reason_type" ];
  // 操作人
  string operator = 6 [ (gogoproto.jsontag) = "operator", json_name = "operator" ];
  // 备注
  string remark = 7 [ (gogoproto.jsontag) = "remark", json_name = "remark" ];
  // 状态
  int64 status = 8 [ (gogoproto.jsontag) = "status", json_name = "status" ];
  // 是否通知
  bool is_notify = 9 [ (gogoproto.jsontag) = "is_notify", json_name = "is_notify" ];
  // ip
  string ip = 10 [ (gogoproto.jsontag) = "ip", json_name = "ip" ];
}

message UpdateMoralsReply {
  // 批量更新后节操值
  map<int64, int64> after_morals = 1 [ (gogoproto.jsontag) = "after_morals", json_name = "after_morals" ];
}

// property_review
message AddUserMonitorReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 操作人
  string operator = 2 [ (gogoproto.jsontag) = "operator", json_name = "operator" ];
  // 备注
  string remark = 3 [ (gogoproto.jsontag) = "remark", json_name = "remark" ];
  int32 name_audit_mode = 4
      [(gogoproto.jsontag) = "name_audit_mode", json_name = "name_audit_mode"];
  int32 face_audit_mode = 5
      [(gogoproto.jsontag) = "face_audit_mode", json_name = "face_audit_mode"];
  int32 sign_audit_mode = 6
      [(gogoproto.jsontag) = "sign_audit_mode", json_name = "sign_audit_mode"];
}

message PropertyReviewExtra { string json = 1 [ (gogoproto.jsontag) = "-" ]; }

// AddPropertyReview
message AddPropertyReviewReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 属性新的值
  string new = 2 [ (gogoproto.jsontag) = "new", json_name = "new" ];
  // 状态
  int32 state = 3
      [ (gogoproto.jsontag) = "state", json_name = "state" ];
  // 属性
  int32 property = 4
      [ (gogoproto.jsontag) = "property", json_name = "property" ];
  // 额外的一些信息
  PropertyReviewExtra extra = 5
      [ (gogoproto.jsontag) = "extra", json_name = "extra", (gogoproto.nullable) = false ];
  int32 filter_level = 6 [ (gogoproto.jsontag) = "filter_level", json_name = "filter_level" ];
  int32 audit_mode = 7
      [(gogoproto.jsontag) = "audit_mode", json_name = "audit_mode"];
  int32 monitor_mode = 8
      [(gogoproto.jsontag) = "monitor_mode", json_name = "monitor_mode"];
  bool is_monitor = 9
      [(gogoproto.jsontag) = "is_monitor", json_name = "is_monitor"];
  string source = 10
      [(gogoproto.jsontag) = "source", json_name = "source"];
}

// AddPropertyReviewReply
message AddPropertyReviewReply {
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  string old = 2 [ (gogoproto.jsontag) = "old", json_name = "old" ];
  string new = 3 [ (gogoproto.jsontag) = "new", json_name = "new" ];
  int32 state = 4
      [ (gogoproto.jsontag) = "state", json_name = "state" ];
  int32 property = 5
      [ (gogoproto.jsontag) = "property", json_name = "property" ];
  bool is_monitor = 6 [ (gogoproto.jsontag) = "is_monitor", json_name = "is_monitor" ];
  string extra = 7 [ (gogoproto.jsontag) = "extra", json_name = "extra" ];
  int32 audit_mode = 8
      [ (gogoproto.jsontag) = "audit_mode", json_name = "audit_mode" ];
  int32 monitor_mode = 9
      [ (gogoproto.jsontag) = "monitor_mode", json_name = "monitor_mode" ];
}

message IsInMonitorReply {
  // member是否处在受监控列表中
  bool is_in_monitor = 1 [ (gogoproto.jsontag) = "is_in_monitor", json_name = "is_in_monitor" ];
}

// realname
message RealnameStatusReply {
  // 实名认证状态
  int32 realname_status = 1 [
    (gogoproto.jsontag) = "realname_status", json_name = "realname_status"
  ];
}

message RealnameApplyInfoReply {
  // 认证流程状态
  int32 status = 1
      [ (gogoproto.jsontag) = "status", json_name = "status" ];
  // 备注
  string remark = 2 [ (gogoproto.jsontag) = "remark", json_name = "remark" ];
  // 真实姓名
  string realname = 3 [ (gogoproto.jsontag) = "realname", json_name = "realname" ];
  // 身份证号
  string card = 4 [ (gogoproto.jsontag) = "card", json_name = "card" ];
}

message ArgRealnameApplyReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 验证码
  int64 capture_code = 2 [ (gogoproto.jsontag) = "capture_code", json_name = "capture_code" ];
  // 真实的名字
  string realname = 3 [ (gogoproto.jsontag) = "realname", json_name = "realname" ];
  // 证件类型
  int32 card_type = 4
      [ (gogoproto.jsontag) = "card_type", json_name = "card_type" ];
  // 证件码
  string card_code = 5 [ (gogoproto.jsontag) = "card_code", json_name = "card_code" ];
  // 国家
  int32 country = 6
      [ (gogoproto.jsontag) = "country", json_name = "country" ];
  // 手持照的路径
  string handIMG_token = 7;
  // 证件照的前面
  string frontIMG_token = 8;
  // 证件照的后面
  string backIMG_token = 9;
  bool check_capture = 10;
  int32 source = 11 [(gogoproto.jsontag) = "source"];
}

message RealnameDetailReply {
  // 实名姓名
  string realname = 1 [ (gogoproto.jsontag) = "realname", json_name = "realname" ];
  // 证件码
  string card = 2 [ (gogoproto.jsontag) = "card", json_name = "card" ];
  // 证件类型
  int32 card_type = 3
      [ (gogoproto.jsontag) = "card_type", json_name = "card_type" ];
  // 实名状态
  int32 status = 4
      [ (gogoproto.jsontag) = "status", json_name = "status" ];
  // 性别
  string gender = 5 [ (gogoproto.jsontag) = "gender", json_name = "gender" ];
  // 手持照IMG User
  string hand_img = 6 [ (gogoproto.jsontag) = "hand_img", json_name = "hand_img" ];
}

message RealnameStrippedInfoReply {
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 1 已实名；其他 未实名
  int32 status = 2
      [ (gogoproto.jsontag) = "status", json_name = "status" ];
  int32 channel = 3
      [ (gogoproto.jsontag) = "channel", json_name = "channel" ];
  int32 country = 4
      [ (gogoproto.jsontag) = "country", json_name = "country" ];
  // 0 大陆身份证；其他 非大陆身份证
  int32 card_type = 5
      [ (gogoproto.jsontag) = "card_type", json_name = "card_type" ];
  // 0 未成年； 1 成年； 2 未知
  int32 adult_type = 6
      [ (gogoproto.jsontag) = "adult_type", json_name = "adult_type" ];
}

message RealnameAlipayApplyReq {
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  int64 capture_code = 2 [ (gogoproto.jsontag) = "capture_code", json_name = "capture_code" ];
  string realname = 3 [ (gogoproto.jsontag) = "realname", json_name = "realname" ];
  string card_code = 4 [ (gogoproto.jsontag) = "card_code", json_name = "card_code" ];
  string img_token = 5 [ (gogoproto.jsontag) = "img_token", json_name = "img_token" ];
  string bizno = 6 [ (gogoproto.jsontag) = "bizno", json_name = "bizno" ];
}

message RealnameAlipayBiznoReply {
  string bizno = 1 [ (gogoproto.jsontag) = "bizno", json_name = "bizno" ];
}

message RealnameAlipayConfirmReq {
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  bool pass = 2 [ (gogoproto.jsontag) = "pass", json_name = "pass" ];
  string reason = 3 [ (gogoproto.jsontag) = "reason", json_name = "reason" ];
}

message MidByRealnameCardReply {
  map<string, int64> code_to_mid = 5 [ (gogoproto.jsontag) = "code_to_mid", json_name = "code_to_mid" ];
}

message RealnameCheckReq {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid"  ];
  // 来源
  string source = 2 [ (gogoproto.jsontag) = "source", json_name = "source" ];
  // 真实的名字,rsa加密
  string realname = 3 [ (gogoproto.jsontag) = "realname", json_name = "realname"  ];
  // 证件类型
  int32 card_type = 4 [ (gogoproto.jsontag) = "card_type", json_name = "card_type" ];
  // 证件码,rsa加密
  string card_code = 5 [ (gogoproto.jsontag) = "card_code" , json_name = "card_code" ];
}

message RealnameCheckReply {
  // 0 未实名 1 一致 2 不一致
  int32 status = 1 [ (gogoproto.jsontag) = "status" , json_name = "status"];
}

message RealnameTeenAgeCheckReply {
  int32 realname = 1 [(gogoproto.jsontag) = "realname", json_name = "realname"];
  bool after14 = 2 [(gogoproto.jsontag) = "after14", json_name = "after14"];
  bool after16 = 3 [(gogoproto.jsontag) = "after16", json_name = "after16"];
  bool after18 = 4 [(gogoproto.jsontag) = "after18", json_name = "after18"];
  int64 rtime = 5  [(gogoproto.jsontag) = "rtime", json_name = "rtime"];
  bool is_faceid = 6  [(gogoproto.jsontag) = "is_faceid", json_name = "is_faceid"];
}

message RealnameLiveAgeCheckReply {
  // 实名状态：-1 未实名，0 已实名但非中国大陆身份证，1 已实名且中国大陆身份证。仅当realname为1时可判断用户年龄。
  int32 realname = 1 [(gogoproto.jsontag) = "realname", json_name = "realname"];
  // 是否满14岁
  bool after14 = 2 [(gogoproto.jsontag) = "after14", json_name = "after14"];
  // 是否满16岁
  bool after16 = 3 [(gogoproto.jsontag) = "after16", json_name = "after16"];
  // 是否满18岁
  bool after18 = 4 [(gogoproto.jsontag) = "after18", json_name = "after18"];
  // 是否满8岁
  bool after8  = 5 [(gogoproto.jsontag) = "after8", json_name = "after8"];
}

message FaceIdVerifyResReply {
  bool pass = 1 [(gogoproto.jsontag) = "pass", json_name = "pass"];
  int64 verify_time = 2 [(gogoproto.jsontag) = "verify_time", json_name = "verify_time"];
}

message BlockInfoReply {
  int64 MID = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  int32 BlockStatus = 2 [ (gogoproto.jsontag) = "status", json_name = "status" ];
  int64 StartTime = 3 [ (gogoproto.jsontag) = "start_time", json_name = "start_time" ];
  int64 EndTime = 4 [ (gogoproto.jsontag) = "end_time", json_name = "end_time" ];
}

message BlockDetailReply {
  int64 MID = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  int64 BlockCount = 2 [ (gogoproto.jsontag) = "block_count", json_name = "block_count" ];
}

message BlockBatchDetailReply {
  map<int64, BlockDetailReply> BlockDetails = 1
      [ (gogoproto.jsontag) = "block_details", json_name = "block_details" ];
}

message BlockBatchInfoReply {
  repeated BlockInfoReply BlockInfos = 1
      [ (gogoproto.jsontag) = "block_infos", json_name = "block_infos" ];
}

message OfficialInviteReq {
  int64 MID = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  int64 InviteRole = 2 [ (gogoproto.jsontag) = "invite_role", json_name = "invite_role" ];
}

message OfficialInviteReply {
  int64 MID = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  int64 InviteRole = 2 [ (gogoproto.jsontag) = "invite_role", json_name = "invite_role" ];
  string InviteTitle = 3 [ (gogoproto.jsontag) = "invite_title", json_name = "invite_title" ];
  string InviteDescription = 4 [ (gogoproto.jsontag) = "invite_description", json_name = "invite_description" ];
}

message IsInPropertyReviewReply {
  // member是否处在审核列表中
  bool is_in_review = 1 [ (gogoproto.jsontag) = "is_in_review", json_name = "is_in_review"];
}

message IsInPropertyReviewReq {
   // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", (gogoproto.moretags) = "form:\"mid\"", json_name = "mid"];
   // property
  int64 property = 2 [ (gogoproto.jsontag) = "property", (gogoproto.moretags) = "form:\"property\"", json_name = "property"];
  // 远端ip
  string remoteIP = 3 [ (gogoproto.jsontag) = "remoteIP", json_name = "remoteIP"];
}

message UserPropertyReviewLatestReq {
  // mid
  int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = "form:\"mid\"", json_name = "mid"];
  // property
  int64 property = 2 [(gogoproto.jsontag) = "property", (gogoproto.moretags) = "form:\"property\"", json_name = "property"];
  // 远端ip
  string remoteIP = 3 [(gogoproto.jsontag) = "remoteIP", json_name = "remoteIP"];
}

message UserPropertyReviewLatestReply {
  string new = 1 [(gogoproto.jsontag) = "new", json_name = "new"];
  int64 pass_time = 2 [(gogoproto.jsontag) = "pass_time", (gogoproto.casttype) = "go-common/library/time.Time", json_name = "pass_time"];
}

message FaceAuditStatusReq{
  string face = 1 [(gogoproto.moretags) = "form:\"face\" validate:\"required\"", json_name = "face"];
}

message FaceAuditStatusReply{
  string face = 1 [(gogoproto.jsontag) = "face", json_name = "face"];
  bool auditStatus = 2 [(gogoproto.jsontag) = "audit_status", json_name = "audit_status"];
}

message SetUserTagReq {
  int64 mid = 1 [ (gogoproto.moretags) = "form:\"mid\" validate:\"required\"", json_name = "mid"];
  repeated string tags = 2 [ (gogoproto.moretags) = "form:\"tags,split\"", json_name = "tags"];
}

message UserTagReply {
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  repeated string tags = 2 [(gogoproto.jsontag) = "tags", json_name = "tags"];
}

message OfficialRealnameReply {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 企业全称
  string company_name = 2 [ (gogoproto.jsontag) = "company_name", json_name = "company_name" ];
  // 社会信用代码
  string credit_code = 3 [ (gogoproto.jsontag) = "credit_code", json_name = "credit_code" ];
  // 注册资金
  string registered_capital = 4 [ (gogoproto.jsontag) = "registered_capital", json_name = "registered_capital" ];
  // 公司法人姓名
  string legal_person_name = 5 [ (gogoproto.jsontag) = "legal_person_name", json_name = "legal_person_name" ];
  // 公司法人电话
  string legal_person_tel = 6 [ (gogoproto.jsontag) = "legal_person_tel", json_name = "legal_person_tel" ];
  // 公司法人电话国家
  string legal_person_tel_country = 7 [ (gogoproto.jsontag) = "legal_person_tel_country", json_name = "legal_person_tel_country" ];
  // 企业营业执照
  string business_license = 8 [ (gogoproto.jsontag) = "business_license", json_name = "business_license" ];
  // 授权书
  string business_auth = 9 [ (gogoproto.jsontag) = "business_auth", json_name = "business_auth" ];
  // 状态
  int64 status = 10 [(gogoproto.jsontag) = "status", json_name = "status"];
  // 最大子账号个数
  int64 max_child_no = 11 [(gogoproto.jsontag) = "max_child_no", json_name = "max_child_no"];
}

//运营人员信息
message OfficialRealnameOperator {
  // 运营人员id
  int64 id = 1 [ (gogoproto.jsontag) = "id", json_name = "id" ];
  // 运营人姓名
  string operator_name = 2 [ (gogoproto.jsontag) = "operator_name", json_name = "operator_name" ];
  // 运营人电话
  string operator_tel = 3 [ (gogoproto.jsontag) = "operator_tel", json_name = "operator_tel" ];
  // 运营人邮箱
  string operator_email = 4 [ (gogoproto.jsontag) = "operator_email", json_name = "operator_email" ];
  // 归属父账号id
  int64 owner_id = 5 [ (gogoproto.jsontag) = "owner_id", json_name = "owner_id" ];
}

message OfficialRealnamesReply {
  map<int64, OfficialRealnameReply> official_realname_batch = 1 [(gogoproto.jsontag) = "official_realname_batch", json_name = "official_realname_batch"];
}

message ChildIdsParam {
  repeated int64 child_ids = 1 [(gogoproto.jsontag) = "child_ids", json_name = "child_ids"];
}

message ParentChildCheckReq {
  map<int64, ChildIdsParam> official_realname_check = 1 [(gogoproto.jsontag) = "official_realname_check", json_name = "official_realname_check"];
  string                    channel                 = 2 [(gogoproto.jsontag) = "channel", json_name = "channel"];
}

// 返回具有父子账号关系的child id
message ParentChildCheckReply {
  map<int64, ChildIdsParam> check = 1 [(gogoproto.jsontag) = "check", json_name = "check"];
}

// 子账号运营人员信息
message OfficialRealnamesSubAccountOperatorReply {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 父账号id
  int64 parent_id = 2 [ (gogoproto.jsontag) = "parent_id", json_name = "parent_id" ];
  // 运营人id
  int64 operator_id = 3 [ (gogoproto.jsontag) = "operator_id", json_name = "operator_id" ];
  // 运营人姓名
  string operator_name = 4 [ (gogoproto.jsontag) = "operator_name", json_name = "operator_name" ];
  // 运营人电话
  string operator_tel = 5 [ (gogoproto.jsontag) = "operator_tel", json_name = "operator_tel" ];
  // 运营人邮箱
  string operator_email = 6 [ (gogoproto.jsontag) = "operator_email", json_name = "operator_email" ];
}

message OfficialRealnameSubAccountReq {
  // 父账号id
  int64 parent_id = 1 [ (gogoproto.jsontag) = "parent_id", json_name = "parent_id" ];
  // 子账号id
  int64 child_id = 2 [ (gogoproto.jsontag) = "child_id", json_name = "child_id" ];
  // 运营人员id
  int64 operator_id = 3 [ (gogoproto.jsontag) = "operator_id", json_name = "operator_id" ];
  // 创建渠道
  string channel = 4  [(gogoproto.jsontag) = "channel", json_name = "channel"];

}

message OfficialRealnameSubaccountBasicReply {
  // mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid" ];
  // 创建时间
  int64 ctime = 2 [(gogoproto.jsontag) = "ctime", json_name = "ctime", (gogoproto.casttype) = "go-common/library/time.Time"];
  // 父账号id
  int64 parent_id = 3 [ (gogoproto.jsontag) = "parent_id", json_name = "parent_id" ];
  // 状态
  int64 status = 4 [(gogoproto.jsontag) = "status", json_name = "status"];
  // 来源
  string source = 5 [ (gogoproto.jsontag) = "source", json_name = "source" ];
}

message RealnameAgeCheckReq {
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = "form:\"mid\"  validate:\"required\""];
  string name = 2 [(gogoproto.jsontag) = "name", json_name = "name", (gogoproto.moretags) = "form:\"name\"  validate:\"required\""];
}

message RealnameAgeCheckReply {
  int32 realname = 1 [(gogoproto.jsontag) = "realname", json_name = "realname"];
  bool name_match = 2 [(gogoproto.jsontag) = "name_match", json_name = "name_match"];
  bool before30 = 3 [(gogoproto.jsontag) = "before30", json_name = "before30"];
  bool after15 = 4 [(gogoproto.jsontag) = "after15", json_name = "after15"];
  bool before25 = 5 [(gogoproto.jsontag) = "before25", json_name = "before25"];
}

message RealnameZxAuthReq {
  int64  mid          = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  string mini_game_id = 2 [(gogoproto.jsontag) = "mini_game_id", json_name = "mini_game_id"];
}

message RealnameZxAuthReply {
  int64                  platform_code = 1 [(gogoproto.jsontag) = "platform_code", json_name = "platform_code"];
  string                 platform_msg  = 2 [(gogoproto.jsontag) = "platform_msg", json_name = "platform_msg"];
  RealnameZxPlatformData platform_data = 3 [(gogoproto.jsontag) = "platform_data", json_name = "platform_data"];
}

message RealnameZxPlatformData {
  int32 is_adult  = 1 [(gogoproto.jsontag) = "is_adult", json_name = "is_adult"];
  int32 can_cache = 2 [(gogoproto.jsontag) = "can_cache", json_name = "can_cache"];
}

message OfficialRealnameOperatorInfoReq {
  // 运营人员id
  int64 operator_id = 1 [ (gogoproto.jsontag) = "operator_id", json_name = "operator_id" ];
}

message AuditStatusReply{
  bool inAudit = 1 [(gogoproto.jsontag) = "in_audit", json_name = "in_audit"];
  bool manualAudited = 2 [(gogoproto.jsontag) = "manual_audited", json_name = "manual_audited"];
}

message SchoolReply{
  int64 school_id = 1 [(gogoproto.jsontag) = "school_id", json_name = "school_id"];
  string name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
  string academy = 3 [(gogoproto.jsontag) = "academy", json_name = "academy"];
  int64 join_time = 4 [(gogoproto.jsontag) = "join_time", json_name = "join_time"];
  int64 ts = 5 [(gogoproto.jsontag) = "ts", json_name = "ts"];
  int32 status = 6 [(gogoproto.jsontag) = "status", json_name = "status"];
}

message SetSchoolReq {
  int64 mid = 1 [(gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
  int64 school_id = 2 [(gogoproto.moretags) = "form:\"school_id\""];
  string academy = 3 [(gogoproto.moretags) = "form:\"academy\""];
  int64 join_time = 4 [(gogoproto.moretags) = "form:\"join_time\""];
  int64 sid = 5 [(gogoproto.moretags) = "form:\"sid\""];
  int64 pool_id = 6 [(gogoproto.moretags) = "form:\"pool_id\""];
  map<int64, string> answer = 7 [(gogoproto.moretags) = "form:\"answer\""];
}

message PrivacySettingReply{
  // 是否展示学校
  bool school_switch = 1;
}

message UpdatePrivacySettingReq{
  int64 mid = 1 [(gogoproto.moretags)='validate:"required"'];
  // 要设置的隐私项，1 是否展示学校
  int64 switch_type = 2;
  // true 打开 false 关闭
  bool switch = 3;
}

message MidByNameReq {
  repeated string names = 1 [(gogoproto.jsontag) = "names",json_name = "names"];
}

message MidByNameReply {
  map<string, int64> nameMap = 1
  [ (gogoproto.jsontag) = "name_map", json_name = "name_map"];
}

message SupportMid64Req {
  string app_key = 1[(gogoproto.moretags) = "form:\"app_key\", validate:\"required\""];
  int64 build_no = 2[(gogoproto.moretags) = "form:\"build_no\", validate:\"gt=0,required\""];
}

message CheckSupportMid64Reply {
  bool is_support_mid64_device = 1 [(gogoproto.jsontag) = "is_support_mid64_device", json_name = "is_support_mid64_device"];
}

message GetMid64VersionMapReply {
  map<string, string> mid64_version_map = 1[(gogoproto.jsontag) = "mid64_version_map", json_name = "mid64_version_map"];
}

message NFTStatusReq {
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = "form:\"mid\""];
  string nft_id = 2 [(gogoproto.jsontag) = "nft_id", json_name = "nft_id", (gogoproto.moretags) = "form:\"nft_id\" validate:\"required\""];
}

message UninstallNFTfaceReq{
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
  string nft_id = 2 [(gogoproto.jsontag) = "nft_id", json_name = "nft_id", (gogoproto.moretags) = "form:\"nft_id\" validate:\"required\""];
  // 可不传，默认为0，卸载NFT头像
  int64 type = 3 [(gogoproto.jsontag) = "type",json_name = "type", (gogoproto.moretags) = "form:\"type\""];
}

message UninstallNFTWithRestoreFaceReq{
  int64  mid    = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
  string nft_id = 2 [(gogoproto.jsontag) = "nft_id", json_name = "nft_id", (gogoproto.moretags) = "form:\"nft_id\" validate:\"required\""];
  int64 type = 3 [(gogoproto.jsontag) = "type", json_name = "type",(gogoproto.moretags) = "form:\"type\""];
}

message NFTStatusReply {
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  string nft_id = 2 [(gogoproto.jsontag) = "nft_id", json_name = "nft_id"];
  // 0 未使用 1 使用中 2 已卸载
  int64 status = 3 [(gogoproto.jsontag) = "status", json_name = "status"];
}

message NFTBatchInfoReq {
  repeated int64 mids = 1 [(gogoproto.jsontag) = "mids", json_name = "mids"];
  string status = 2 [(gogoproto.jsontag) = "status", json_name = "status"];
  string source = 3 [(gogoproto.jsontag) = "source", json_name = "source"];
  int64 type = 4 [(gogoproto.jsontag) = "type", json_name = "type"];
}

message NFTBatchInfoReply {
  map<string, NFTBatchInfoData> nftInfos = 1 [ (gogoproto.jsontag) = "nft_infos", json_name = "nft_infos" ];
}

message NFTBatchInfoData {
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid" ];
  string nftId = 2 [(gogoproto.jsontag) = "nft_id", json_name = "nft_id" ];
  int32 status = 3 [(gogoproto.jsontag) = "status", json_name = "status" ];
  string source = 4 [(gogoproto.jsontag) = "source", json_name = "source" ];
  int64 type = 5 [(gogoproto.jsontag) = "type", json_name = "type"];
}

message UserProfessionReply {
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  int64 id = 2 [(gogoproto.jsontag) = "id", json_name = "id"];
  string name = 3 [(gogoproto.jsontag) = "name", json_name = "name"];
  string show_text = 4 [(gogoproto.jsontag) = "show_text", json_name = "show_text"];
  int32 is_show = 5 [(gogoproto.jsontag) = "is_show", json_name = "is_show"];
  string category_one = 6 [(gogoproto.jsontag) = "category_one", json_name = "category_one"];
  string realname = 7 [(gogoproto.jsontag) = "realname", json_name = "realname"];
  string title  = 8 [(gogoproto.jsontag) = "title", json_name = "title"];
  string department = 9 [(gogoproto.jsontag) = "department", json_name = "department"];
  int64 category_one_id = 10 [(gogoproto.jsontag) = "category_one_id", json_name = "category_one_id"];
}

message UserProfessionsReply {
  map<int64, UserProfessionReply> profession = 1 [(gogoproto.jsontag) = "profession", json_name = "profession"];
}

message AddUserExtraValueReq{
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
  string key = 2 [(gogoproto.jsontag) = "key", json_name = "key", (gogoproto.moretags) = "form:\"key\" validate:\"required\""];
  string value = 3 [(gogoproto.jsontag) = "value", json_name = "value", (gogoproto.moretags) = "form:\"value\""];
}

message UserExtraValueSingleKeyReq{
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
  string key = 2 [(gogoproto.jsontag) = "key", json_name = "key", (gogoproto.moretags) = "form:\"key\" validate:\"required\""];
}

message GetUserExtraBasedOnKeyReq{
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
  repeated string keys = 2 [(gogoproto.jsontag) = "keys", json_name = "keys", (gogoproto.moretags) = "form:\"keys\" validate:\"required\""];
}

message BatchAddUserExtraValueReq{
  string key = 1 [(gogoproto.jsontag) = "key", json_name = "key", (gogoproto.moretags) = "form:\"key\" validate:\"required\""];
  map<int64, string> mid_values = 2 [(gogoproto.jsontag) = "mid_values", json_name = "mid_values", (gogoproto.moretags) = "form:\"mid_values\" validate:\"required\""];
}

message RemoveUserExtraValueReq{
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = "form:\"mid\" validate:\"required\""];
  string key = 2 [(gogoproto.jsontag) = "key", json_name = "key", (gogoproto.moretags) = "form:\"key\" validate:\"required\""];
}

message BatchRemoveUserExtraValueReq{
  string key = 1 [(gogoproto.jsontag) = "key", json_name = "key", (gogoproto.moretags) = "form:\"key\" validate:\"required\""];
  repeated int64 mids = 2 [(gogoproto.jsontag) = "mids", json_name = "mids", (gogoproto.moretags) = "form:\"mids\" validate:\"required\""];
}

message BatchFailedUserExtraValueReply{
  repeated int64 failed_mids = 1 [(gogoproto.jsontag) = "failed_mids", json_name = "failed_mids"];
}

message UserExtraValueSingleKeyReply{
  string value = 1 [(gogoproto.jsontag) = "value", json_name = "value"];
}

message UserExtraValues{
  map<string, string> extra_info = 1 [ (gogoproto.jsontag) = "extra_info", json_name = "extra_info" ];
}

message BatchUserExtraValueReply{
  map<int64, UserExtraValues> reply = 1 [(gogoproto.jsontag) = "reply", json_name = "reply"];
}

// 是否登录过危险设备
message UserBlackDeviceReply {
  bool deviceStatus = 1 [(gogoproto.jsontag) = "device_status", json_name = "device_status"];
}

// 批量获取用户是否登录过危险设备
message UserBlackDevicesReply {
  map<int64, UserBlackDeviceReply> devices_status = 1[ (gogoproto.jsontag) = "devices_status", json_name = "devices_status" ];
}

message BlackDeviceCheckReq {
  // 设备localId
  string LocalId = 1 [(gogoproto.jsontag) = "local_id", json_name = "local_id"];
}

message BlackDevicesCheckReply {
  // 设备是否是危险设备
  bool flag = 1 [(gogoproto.jsontag) = "flag", json_name = "flag"];
  // 危险设备原始的Mid
  int64 mid = 2 [(gogoproto.jsontag) = "mid", json_name = "mid"];
}

// archive_extra
message ArchiveAidReq {
  int64 aid = 1 [(gogoproto.jsontag) = "aid", json_name = "aid", (gogoproto.moretags) = "form:\"aid\" validate:\"required\""];
}

message ArchiveAidsReq {
  repeated int64 aids = 1 [(gogoproto.jsontag) = "aids", json_name = "aids", (gogoproto.moretags) = "form:\"aids\" validate:\"required\""];
}

message AddArchiveExtraValueReq{
  int64 aid = 1 [(gogoproto.jsontag) = "aid", json_name = "aid", (gogoproto.moretags) = "form:\"aid\" validate:\"required\""];
  string key = 2 [(gogoproto.jsontag) = "key", json_name = "key", (gogoproto.moretags) = "form:\"key\" validate:\"required\""];
  string value = 3 [(gogoproto.jsontag) = "value", json_name = "value", (gogoproto.moretags) = "form:\"value\""];
}

message ArchiveExtraValueSingleKeyReq{
  int64 aid = 1 [(gogoproto.jsontag) = "aid", json_name = "aid", (gogoproto.moretags) = "form:\"aid\" validate:\"required\""];
  string key = 2 [(gogoproto.jsontag) = "key", json_name = "key", (gogoproto.moretags) = "form:\"key\" validate:\"required\""];
}

message GetArchiveExtraBasedOnKeyReq{
  int64 aid = 1 [(gogoproto.jsontag) = "aid", json_name = "aid", (gogoproto.moretags) = "form:\"aid\" validate:\"required\""];
  repeated string keys = 2 [(gogoproto.jsontag) = "keys", json_name = "keys", (gogoproto.moretags) = "form:\"keys\" validate:\"required\""];
}

message BatchAddArchiveExtraValueReq{
  string key = 1 [(gogoproto.jsontag) = "key", json_name = "key", (gogoproto.moretags) = "form:\"key\" validate:\"required\""];
  map<int64, string> aid_values = 2 [(gogoproto.jsontag) = "aid_values", json_name = "aid_values", (gogoproto.moretags) = "form:\"aid_values\" validate:\"required\""];
}

message RemoveArchiveExtraValueReq{
  int64 aid = 1 [(gogoproto.jsontag) = "aid", json_name = "aid", (gogoproto.moretags) = "form:\"aid\" validate:\"required\""];
  string key = 2 [(gogoproto.jsontag) = "key", json_name = "key", (gogoproto.moretags) = "form:\"key\" validate:\"required\""];
}

message BatchRemoveArchiveExtraValueReq{
  string key = 1 [(gogoproto.jsontag) = "key", json_name = "key", (gogoproto.moretags) = "form:\"key\" validate:\"required\""];
  repeated int64 aids = 2 [(gogoproto.jsontag) = "aids", json_name = "aids", (gogoproto.moretags) = "form:\"aids\" validate:\"required\""];
}

message BatchFailedArchiveExtraValueReply{
  repeated int64 failed_aids = 1 [(gogoproto.jsontag) = "failed_aids", json_name = "failed_aids"];
}

message ArchiveExtraValueSingleKeyReply{
  string value = 1 [(gogoproto.jsontag) = "value", json_name = "value"];
}

message ArchiveExtraValues{
  map<string, string> extra_info = 1 [ (gogoproto.jsontag) = "extra_info", json_name = "extra_info" ];
}

message BatchArchiveExtraValueReply{
  map<int64, ArchiveExtraValues> reply = 1 [(gogoproto.jsontag) = "reply", json_name = "reply"];
}

message IsUserBaseInPropertyReviewReq {
  // mid
  int64 Mid = 1;
}

message IsUserBaseInPropertyReviewReply {
  // 是否在有审核中的申请
  bool  IsInPropertyReview = 1;
  // 如果有在审核中的申请，该申请属于修改的什么属性,1 头像，2 签名，3 昵称
  int64 Property           = 2;
}

message UserBaseInfoLatestUpdateInfoReq {
  int64 Mid = 1;
}

message UserBaseInfoLatestUpdateInfoReply {
  int64 Mid        = 1;
  // 生效时间
  int64 ModifyTime = 2;
  // 生效时该申请属于修改的什么属性,1 头像，2 签名，3 昵称
  int64 Property   = 3;
  // 修改触发的审核类型
  int64 AuditMode  = 4;
}

message CheckReq {
  int64  mid   = 1;
  string token = 2;
  string scene = 3;
}

message CheckReply {
  // 是否验证通过
  bool pass = 1;
}

message RealnameInfoCheckReq {
  // mid 必传
  int64            mid      = 1;
  // 来源，业务属性描述：不超过16字符
  string           source   = 2;
  // 证件类型，只有证件类型对应时才继续做检查，否则直接检查不通过
  RealNameCardType cardType = 3;
  // 姓名，需rsa加密，参数不空时做检查
  string           realname = 4;
  // 证件码，需rsa加密，参数不空是做检查
  string           cardCode = 5;
  // 校验身份证倒数lastNum位，最大校验倒数8位，校验完整身份证号码可不传
  int32            lastNum  = 6;
}

message RealnameInfoCheckReply {
  // 用户当前是否已实名
  bool status          = 1;
  // 证件类型身份正确
  bool cardType        = 2;
  // 姓名是否检查通过
  bool nameCheckResult = 3;
  // 证件码是否检查通过
  bool codeCheckResult = 4;
}

enum RealNameCardType {
  ChinaIDCard      = 0; // 中国大陆身份证
  OverseasPassport = 1; // 护照(境外签发)
  HKG              = 2; // 港澳居民来往内地通行证
  PermitForTaiwan  = 3; //台湾居民来往大陆通行证
  ChinesePassport  = 4; // 护照(中国签发)
  PRC              = 5; // 外国人永久居留证
  OtherIDCard      = 6; //其他国家或地区身份证
  NoCareType       = -1; // 不关心证件类型，此时的对比不会在意用户实名时的证件类型；只会做业务需要的姓名或证件号校验
}

message InternalDefinedControlStatusReply {
  // mid => InternalDefinedControlStatus
  map<int64,InternalDefinedControlStatus> mid_status_map = 1 [ (gogoproto.jsontag) = "mid_status_map", json_name = "mid_status_map" ];
}

message InternalDefinedControlStatus {
  // 用户mid
  int64 mid = 1 [ (gogoproto.jsontag) = "mid", json_name = "mid"];
  // 是否为虚假账号
  bool is_fake_account = 2 [ (gogoproto.jsontag) = "is_fake_account", json_name = "is_fake_account" ];
}

message BaseInfoUpdateFlag {
  // 是否为默认生日且没有修改过
  bool birthday = 1;
}

message UserHonourReply {
  int64              mid    = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  UserHonourStyle    colour = 2 [(gogoproto.jsontag) = "colour", json_name = "colour"];
  repeated HonourTag tags   = 3 [(gogoproto.jsontag) = "tags", json_name = "tags"];
}

message UserHonourStyle {
  string dark   = 1 [(gogoproto.jsontag) = "dark", json_name = "dark"];
  string normal = 2 [(gogoproto.jsontag) = "normal", json_name = "normal"];
}

message HonourTag {
  string          name     = 1 [(gogoproto.jsontag) = "name", json_name = "name"];
  string          link     = 2 [(gogoproto.jsontag) = "link", json_name = "link"];
  string          web_link = 3 [(gogoproto.jsontag) = "web_link", json_name = "web_link"];
  int32           type     = 4 [(gogoproto.jsontag) = "type", json_name = "type"];
  repeated string scene    = 5 [(gogoproto.jsontag) = "scene", json_name = "scene"];
  int32           year     = 6 [(gogoproto.jsontag) = "year", json_name = "year"];
  string          icon     = 7 [(gogoproto.jsontag) = "icon", json_name = "icon"];
}

message UserHonoursReply {
  map<int64, UserHonourReply> honour = 1 [(gogoproto.jsontag) = "honour"];
}

// BatchRealnameStatusReply
message BatchRealnameStatusReply {
	map<int64, RealnameStatusReply> realname_status = 1 [(gogoproto.jsontag) = "realname_status"];
}

// morals
message MoralsReply {
	map<int64, MoralReply> morals = 1 [(gogoproto.jsontag) = "morals"];
}

message RealnamePrivacyInfo {
  int64  mid       = 1 [(gogoproto.jsontag) = "mid"];
  string realname  = 2 [(gogoproto.jsontag) = "realname"];
  string real_card = 3 [(gogoproto.jsontag) = "real_card"];
  string gender    = 4 [(gogoproto.jsontag) = "gender"];
  string hand_img  = 5 [(gogoproto.jsontag) = "hand_img"];
  int32  country   = 6 [(gogoproto.jsontag) = "country"];
  int32  card_type = 7 [(gogoproto.jsontag) = "card_type"];
  int32  status    = 8 [(gogoproto.jsontag) = "status"];
}

message RealnamePrivacyInfoReply {
  int64           mid = 1 [(gogoproto.jsontag) = "mid"];
  repeated string permitted_fields = 2 [(gogoproto.jsontag) = "permitted_fields"];
  string          payload = 3 [(gogoproto.jsontag) = "payload"];
}

message GetBusinessStorageInfoReq {
  // 为接入的团队分配的团队id，由账号团队维护；不同的business之间信息隔离，不可互相访问
  int32 business_id = 1;
  // 业务团队内自己衍生的活动或批次id，由业务自身维护
  int32 business_sub_id = 2;
  // 单次业务使用的唯一标识信息：可为mid、邮箱、手机号等
  string business_info_key = 3;
}

message BusinessStorageInfo {
  // 类别信息
  PrivacyCategoryEnum category = 1;
  // 存储的隐私信息：如果是明文要求则返回加密后的信息，如果非明文则为脱敏信息(脱敏方式根据类别一一对应，即手机号有自己的专有脱敏方式152*****456)
  string payload = 2;
}

message PhonePayload {
  // 手机号
  string info = 1;
  // 手机号的区号，例如86
  string info_extend = 2;
}

// 业务所存储的隐私信息归属类别
enum PrivacyCategoryEnum {
  // 无
  NONE = 0;
  // 手机号
  PHONE = 1;
}

enum InfoKeyCategoryEnum {
  // 无
  INFO_KEY_NONE = 0;
  // mid
  INFK_KEY_MID = 1;
}

message SaveMidCategoryInfoReq {
  // 为接入的团队分配的团队id，由账号团队维护；不同的business之间信息隔离，不可互相访问
  int32 business_id = 1;
  // 业务团队内自己衍生的活动或批次id，由业务自身维护
  int32 business_sub_id = 2;
  // 信息类别
  InfoKeyCategoryEnum info_key_category = 3;
  // 单次业务使用的类别唯一标识信息：可为mid、邮箱、手机号等，和info_key_category对应
  string business_info_key = 4;
  // 隐私信息对应的信息类别
  PrivacyCategoryEnum category = 5;
  // 验证码校验相关的参数
  CodeCheckReq code_check_req = 6;
}

message CodeCheckReq {
  // 发送验证码调用的接口返回的值
  string token = 1;
  // 用户输入的验证码
  string code = 2;
}

message SaveCategoryInfoReq {
  // 为接入的团队分配的团队id，由账号团队维护；不同的business之间信息隔离，不可互相访问
  int32 business_id = 1;
  // 业务团队内自己衍生的活动或批次id，由业务自身维护
  int32 business_sub_id = 2;
  // 信息类别
  InfoKeyCategoryEnum info_key_category = 3;
  // 单次业务使用的类别唯一标识信息：可为mid、邮箱、手机号等，和info_key_category对应
  string business_info_key = 4;
  // 隐私信息对应的信息类别
  PrivacyCategoryEnum category = 5;
  // rsa加密的info信息，例如手机号
  string info = 6;
  // rsa加密的info_extend信息，例如手机区号
  string info_extend = 7;
  // 验证码校验相关的参数
  CodeCheckReq code_check_req = 8;
}

message UserOfficialInfoReply {
  // mid
  int64 mid = 1;
  // 官方认证时的昵称
  string name = 2;
  // 认证类型，如果role=0，则代表其未认证，其他相关信息都会时默认空值
  int32 role = 3;
  // 认证称号
  string title = 4;
  // 认证后缀
  string desc = 5;
  // 认证资料加密后的payload，对应OfficialInfoPrivacyInfo
  string payload = 6;
}

message UserOfficialLatestAuditInfoReply {
  // mid
  int64 mid = 1;
  // 官方认证时的昵称
  string name = 2;
  // 认证类型，如果role=0，则代表其未认证，其他相关信息都会时默认空值
  int32 role = 3;
  // 认证称号
  string title = 4;
  // 认证后缀
  string desc = 5;
  // 该申请对应的审核状态
  UserOfficialAuditState state = 6;
  string submit_source = 7;
  int64 submit_time = 8 [(gogoproto.casttype) = "go-common/library/time.Time"];
  string submit_type = 9;
  // 认证资料加密后的payload， 对应OfficialInfoPrivacyInfo
  string payload = 10;
}

enum UserOfficialAuditState {
  // 无审核，即未发起过
  OFFICIAL_AUDIT_NONE = 0;
  // 审核中
  OFFICIAL_AUDIT_WAIT = 1;
  // 审核通过
  OFFICIAL_AUDIT_PASS = 2;
  // 审核拒绝
  OFFICIAL_AUDIT_REJECTED = 3;
  // 用户已撤销申请
  OFFICIAL_CANCEL = 4;
}

// OfficialInfoPrivacyInfo 官方认证用户隐私资料信息，用于json后做加密
message OfficialInfoPrivacyInfo {
  // 是否已实名
  int32  realname                         = 1 [(gogoproto.jsontag) = "realname"];
  // 经营人
  string operator                         = 2 [(gogoproto.jsontag) = "operator"];
  // 电话号码
  string telephone                        = 3 [(gogoproto.jsontag) = "telephone"];
  // 电话国家码
  int64  tel_country                      = 4 [(gogoproto.jsontag) = "tel_country"];
  // 邮箱
  string email                            = 5 [(gogoproto.jsontag) = "email"];
  // 地址
  string address                          = 6 [(gogoproto.jsontag) = "address"];
  // 公司
  string company                          = 7 [(gogoproto.jsontag) = "company"];
  // 社会信用代码
  string credit_code                      = 8 [(gogoproto.jsontag) = "credit_code"];
  // 一级行业名称
  string first_level_industry_name        = 9 [(gogoproto.jsontag) = "first_level_industry_name"];
  // 一级行业id
  int64  first_level_industry_id          = 10 [(gogoproto.jsontag) = "first_level_industry_id"];
  // 二级行业名称
  string second_level_industry_name       = 11 [(gogoproto.jsontag) = "second_level_industry_name"];
  // 二级行业id
  int64  second_level_industry_id         = 12 [(gogoproto.jsontag) = "second_level_industry_id"];
  // 政府或组织机构名称
  string organization                     = 13 [(gogoproto.jsontag) = "organization"];
  // 组织或机构类型
  string organization_type                = 14 [(gogoproto.jsontag) = "organization_type"];
  // 营业执照
  string business_license                 = 15 [(gogoproto.jsontag) = "business_license"];
  // 企业规模
  string business_scale                   = 16 [(gogoproto.jsontag) = "business_scale"];
  // 企业等级
  string business_level                   = 17 [(gogoproto.jsontag) = "business_level"];
  // 企业授权函
  string business_auth                    = 18 [(gogoproto.jsontag) = "business_auth"];
  // 其他补充资料
  string supplement                       = 19 [(gogoproto.jsontag) = "supplement"];
  // 特殊行业补充材料
  string special_industry_supply_material = 20 [(gogoproto.jsontag) = "special_industry_supply_material"];
  // 专业资质
  string professional                     = 21 [(gogoproto.jsontag) = "professional"];
  // 身份证明
  string identification                   = 22 [(gogoproto.jsontag) = "identification"];
  // 官网地址
  string official_site                    = 23 [(gogoproto.jsontag) = "official_site"];
  // 注册资本
  string registered_capital               = 24 [(gogoproto.jsontag) = "registered_capital"];
  // 企业自媒体确认函
  string enterprise_we_media_confirmation = 25 [(gogoproto.jsontag) = "enterprise_we_media_confirmation"];
  // 团队自媒体确认函
  string group_we_media_confirmation      = 26 [(gogoproto.jsontag) = "group_we_media_confirmation"];
  // 站外账号链接
  string external_site_link               = 27 [(gogoproto.jsontag) = "external_site_link"];
  // 站外平台
  string external_site_name               = 28 [(gogoproto.jsontag) = "external_site_name"];
  // 站外账号粉丝量
  int64  external_site_follower_count     = 29 [(gogoproto.jsontag) = "external_site_follower_count"];
  // 站外账号所属人（或公司)
  string external_site_owner              = 30 [(gogoproto.jsontag) = "external_site_owner"];
  // 站外平台主页后台截图
  string external_site_img                = 31 [(gogoproto.jsontag) = "external_site_img"];
  // 社会名人ID
  int64  public_figure_category_id        = 32 [(gogoproto.jsontag) = "public_figure_category_id"];
}

message UserOfficialRealnameInfosReply {
  map<int64, UserOfficialRealnameInfo> infos = 1;
}

message UserOfficialRealnameInfo {
  // mid
  int64 mid = 1;
  // 实名状态
  int32 status = 2;
  // 实名资料payload，结构对应UserOfficialRealnamePrivacyInfo
  string payload = 3;
}

message UserOfficialRealnamePrivacyInfo {
  // 企业全称
  string company_name = 1 [ (gogoproto.jsontag) = "company_name", json_name = "company_name" ];
  // 社会信用代码
  string credit_code = 2 [ (gogoproto.jsontag) = "credit_code", json_name = "credit_code" ];
  // 注册资金
  string registered_capital = 3 [ (gogoproto.jsontag) = "registered_capital", json_name = "registered_capital" ];
  // 公司法人姓名
  string legal_person_name = 4 [ (gogoproto.jsontag) = "legal_person_name", json_name = "legal_person_name" ];
  // 公司法人电话
  string legal_person_tel = 5 [ (gogoproto.jsontag) = "legal_person_tel", json_name = "legal_person_tel" ];
  // 公司法人电话国家
  string legal_person_tel_country = 6 [ (gogoproto.jsontag) = "legal_person_tel_country", json_name = "legal_person_tel_country" ];
  // 企业营业执照
  string business_license = 7 [ (gogoproto.jsontag) = "business_license", json_name = "business_license" ];
  // 授权书
  string business_auth = 8 [ (gogoproto.jsontag) = "business_auth", json_name = "business_auth" ];
  // 最大子账号个数
  int64 max_child_no = 9 [(gogoproto.jsontag) = "max_child_no", json_name = "max_child_no"];
}

message CancelUserOfficialApplyReq {
  int64 mid = 1;
  string reason = 2;
  string source = 3;
}

message UserAgeCheckReq {
  // 用户mid
  int64 mid = 1;
  // 要求校验用户年龄是否 大于等于该年龄；允许对比的值需要向账号团队申请
  int32 gte_age = 2;
  // 要求校验用户年龄是否 小于等于该年龄；允许对比的值需要向账号团队申请
  int32 lte_age = 3;
}

message UserAgeCheckResp {
  // 是否已实名，true已实名，只有实名的才有可能判断年龄
  bool status = 1;
  // 是否可以根据实名信息获取到年龄; 如果不可以则年龄判断全为默认值
  bool check_age_result = 2;
  // 大于等于的年龄校验结果, 是否大于等于该年龄
  bool gte_age = 3;
  // 小于等于的年龄校验结果, 是否小于等于该年龄
  bool lte_age = 4;
}

message GetFaceVerifyResultByDateReq {
  // 获取新的维度：0默认值为mid维度，1身份证号
  int32 category = 1;
  // 身份证号列表或mid列表
  repeated string ids = 2;
  // 日期值，格式20231012
  string date = 3;
}
message UserSameCardUidsRsp{
  repeated int64 uids = 1;
}

message GetFaceVerifyResultByDateResp {
  // 聚合结果
  map<string, FaceVerifyResultRecords> list = 1;
}

message FaceVerifyResultRecords {
  repeated FaceVerifyResultRecord records = 1;
}

message FaceVerifyResultRecord {
  // 是否认证通过
  bool verify_result = 1;
  // 认证的时间
  int64 verify_time =2;
}

message CNYNameNickNamesReq {
  map<int64,CNYNameNickName> cny_names = 1;
}

message CNYNameNickNamesRsp{
  map<int64, string> names = 1;
}

message CNYNameNickName {
  string name = 1;
  string ip = 2;
}


service Member {
  // 得到member的基本信息
  rpc Base(MemberMidReq) returns (BaseInfoReply);
  // 批量得到一批member的基本信息
  rpc Bases(MemberMidsReq) returns (BaseInfosReply);
  // 得到member的全量信息
  rpc Member(MemberMidReq) returns (MemberInfoReply);
  // 批量得到一批member的全量信息
  rpc Members(MemberMidsReq) returns (MemberInfosReply);
  // 查看member是否更新过昵称
  rpc NickUpdated(MemberMidReq) returns (NickUpdatedReply);
  // 标记member已经更新过昵称
  rpc SetNickUpdated(MemberMidReq) returns (EmptyStruct);
  // 设置官方文档资料
  rpc SetOfficialDoc(OfficialDocReq) returns (EmptyStruct);
  // 设置 base 信息
  rpc SetBase(UpdateBaseReq) returns (EmptyStruct);
  // 设置member的性别
  rpc SetSex(UpdateSexReq) returns (EmptyStruct);
  // 设置member的名字
  rpc SetName(UpdateUnameReq) returns (EmptyStruct);
  // 设置member的名字，走审核流程
  rpc SetNameReview(UpdateUnameReq) returns (EmptyStruct);
  // 查看member是否处理信息审核中
  rpc IsInPropertyReview(IsInPropertyReviewReq) returns (IsInPropertyReviewReply);
  // 查看用户基础信息是否有审核中的记录(包括：昵称、头像、签名)
  rpc IsUserBaseInPropertyReview(IsUserBaseInPropertyReviewReq) returns (IsUserBaseInPropertyReviewReply);
  // 查看用户最新人工审核通过的信息
  rpc UserPropertyReviewLatest (UserPropertyReviewLatestReq) returns (UserPropertyReviewLatestReply);
  // 获取用户基础信息最新更改信息(基于审核系统产出,包括：昵称、头像、签名)
  rpc UserBaseInfoLatestUpdateInfo(UserBaseInfoLatestUpdateInfoReq) returns (UserBaseInfoLatestUpdateInfoReply);
  rpc CanSetName(CanSetNameReq) returns (EmptyStruct);
  // 设置member的头像
  rpc SetFace(UpdateFaceReq) returns (EmptyStruct);
  // 设置member的头像
  rpc SetRank(UpdateRankReq) returns (EmptyStruct);
  // 设置member的生日
  rpc SetBirthday(UpdateBirthdayReq) returns (EmptyStruct);
  // 设置member的签名
  rpc SetSign(UpdateSignReq) returns (EmptyStruct);
  // 设置是资深会员
  rpc SetIsSeniorMember(SetSeniorMemberReq) returns (EmptyStruct);
  // 取消资深会员信息
  rpc SeniorMemberCancel(SeniorMemberCancelReq) returns (EmptyStruct);
  // 得到member的官方信息
  rpc OfficialDoc(MidReq) returns (OfficialDocInfoReply);
  // 得到当前生效的官方认证信息
  rpc Official(MidReq) returns (OfficialInfoReply);
  // 得到member对外可用的官方认证信息
  rpc OfficialDocExternalInfo (MidReq) returns (OfficialDocExternalInfoReply);
  // 获取官方认证邀请
  rpc OfficialInvite(OfficialInviteReq) returns (OfficialInviteReply);
  // 查询用户当前是否在审核中
  rpc AuditStatus(MemberMidReq) returns (AuditStatusReply);
  // 查询隐私设置
  rpc PrivacySetting(MidReq)  returns(PrivacySettingReply);
  // 更新隐私设置
  rpc UpdatePrivacySetting(UpdatePrivacySettingReq)returns(EmptyStruct);
  // 通过name查询mid
  rpc MidByName(MidByNameReq) returns (MidByNameReply);

  // 得到member的节操值
  rpc Moral(MemberMidReq) returns (MoralReply);
  // 得到member的节操日志
  rpc MoralLog(MemberMidReq) returns (UserLogsReply);
  // 给member添加节操值
  rpc AddMoral(UpdateMoralReq) returns (EmptyStruct);
  // 给一批member批量添加节操值
  rpc BatchAddMoral(UpdateMoralsReq) returns (UpdateMoralsReply);

  // 得到member的经验值
  rpc Exp(MidReq) returns (LevelInfoReply);
  // 得到member的等级
  rpc Level(MidReq) returns (LevelInfoReply);
  // 更新member的经验值
  rpc UpdateExp(AddExpReq) returns (EmptyStruct);
  // 得到membe的经验日志
  rpc ExpLog(MidReq) returns (UserLogsReply);
  // 得到member的经验统计
  rpc ExpStat(MidReq) returns (ExpStatReply);

  // 得到member的实名认证状态
  rpc RealnameStatus(MemberMidReq) returns (RealnameStatusReply);
  // 得到member 实名认证流程的状态
  rpc RealnameApplyStatus(MemberMidReq) returns (RealnameApplyInfoReply);
  // 手机号实名认证发送验证码
  rpc RealnameTelCapture(MemberMidReq) returns (EmptyStruct);
  // 进行实名认证
  rpc RealnameApply(ArgRealnameApplyReq) returns (EmptyStruct);
  // 实名详情
  rpc RealnameDetail(MemberMidReq) returns (RealnameDetailReply);
  // 所有非敏感实名认证信息
  rpc RealnameStrippedInfo(MemberMidReq) returns (RealnameStrippedInfoReply);
  // 通过身份证号查询 mid
  rpc MidByRealnameCard(MidByRealnameCardsReq) returns (MidByRealnameCardReply);
  // 提交芝麻认证请求
  rpc RealnameAlipayApply(RealnameAlipayApplyReq) returns (EmptyStruct);
  // 获取芝麻认证 bizno
  rpc RealnameAlipayBizno(MemberMidReq) returns (RealnameAlipayBiznoReply);
  rpc RealnamePrivacyInfo (MemberMidReq) returns (RealnamePrivacyInfoReply);
  // 添加用户为受监控
  rpc AddUserMonitor(AddUserMonitorReq) returns (EmptyStruct);
  // 查看member是否在监控状态
  rpc IsInMonitor(MidReq) returns (IsInMonitorReply);
  // 添加属性审核
  rpc AddPropertyReview(AddPropertyReviewReq) returns (AddPropertyReviewReply);
  rpc RealnameAlipayConfirm(RealnameAlipayConfirmReq) returns (EmptyStruct);
  // 实名校验
  rpc RealnameCheck(RealnameCheckReq) returns (RealnameCheckReply);
  // 三方业务用于向账号询问检查用户实名信息：姓名或证件号是否正确
  rpc RealnameInfoCheck (RealnameInfoCheckReq) returns (RealnameInfoCheckReply);
  rpc RealnameAgeCheck(RealnameAgeCheckReq) returns (RealnameAgeCheckReply);
  rpc RealnameTeenAgeCheck(MidReq) returns (RealnameTeenAgeCheckReply);
  rpc Realname14AgeCheck (MemberMidReq) returns (RealnameLiveAgeCheckReply);
  rpc FaceIdVerifyResult (MidReq) returns (FaceIdVerifyResReply);
  rpc FaceIdVerifyTokenCheck(CheckReq) returns (CheckReply);
  rpc RealnameZxAuth(RealnameZxAuthReq) returns (RealnameZxAuthReply);

  // block
  // 获取用户封禁信息数据
  rpc BlockInfo(MemberMidReq) returns (BlockInfoReply);
  // 批量获取用户封禁信息数据
  rpc BlockBatchInfo(MemberMidsReq) returns (BlockBatchInfoReply);
  //批量获取用户封禁详细数据
  rpc BlockBatchDetail(MemberMidsReq) returns (BlockBatchDetailReply);

  // 查询头像是否被人工审核通过
  rpc FaceAuditStatus(FaceAuditStatusReq) returns (FaceAuditStatusReply);

  //设置用户个人标签
  rpc SetUserTag(SetUserTagReq) returns (EmptyStruct);
  //获取用户个人标签
  rpc UserTag(MidReq) returns (UserTagReply);

  //企业实名认证
  //获取企业实名认证信息
  rpc OfficialRealname(MemberMidReq) returns (OfficialRealnameReply);
  //批量企业实名认证信息
  rpc OfficialRealnames(MemberMidsReq) returns (OfficialRealnamesReply);
  //获取企业实名认证子账号基本信息
  rpc OfficialRealnameSubaccountBasic(MemberMidReq) returns (OfficialRealnameSubaccountBasicReply);

  //创建企业实名认证子账号
  rpc OfficialRealnameSubAccountCreate(OfficialRealnameSubAccountReq) returns (EmptyStruct);
  //解绑企业实名认证子账号
  rpc OfficialRealnameSubAccountUnbind(OfficialRealnameSubAccountReq) returns (EmptyStruct);
  //修改企业实名认证子账号
  rpc OfficialRealnameSubAccountEdit(OfficialRealnameSubAccountReq) returns (EmptyStruct);
  //批量查询父账号和子账号是否存在父子关系
  rpc OfficialRealnameParentChildCheck(ParentChildCheckReq) returns (ParentChildCheckReply);
  //获取子账号运营人员信息
  rpc OfficialRealnameSubAccountOperator(MemberMidReq) returns (OfficialRealnamesSubAccountOperatorReply);
  //获取运营人员信息
  rpc OfficialRealnameOperatorInfo(OfficialRealnameOperatorInfoReq) returns (OfficialRealnameOperator);

  //获取用户学校信息
  rpc School(MidReq) returns (SchoolReply);
  //更新、删除学校
  rpc SetSchool (SetSchoolReq) returns (EmptyStruct);

  //判断当前设备是否支持int64
  rpc CheckSupportMid64(SupportMid64Req) returns (CheckSupportMid64Reply);
  
  //获取int64版本号map
  rpc GetMid64VersionMap(EmptyStruct) returns (GetMid64VersionMapReply) {
    option (google.api.http) = {
      get:"/x/account/version/getMid64VersionMap"
    };
  };

  // 获取用户nft状态
  rpc NFTStatus (NFTStatusReq) returns (NFTStatusReply);

  // 批量获取nft信息
  rpc NFTBatchInfo(NFTBatchInfoReq) returns (NFTBatchInfoReply);
  // 卸载nft或集卡头像
  rpc UninstallNFTface(UninstallNFTfaceReq) returns (EmptyStruct);
  // 卸载特殊头像（nft/集卡/直播虚拟）并恢复上一个头像
  rpc UninstallNFTWithRestoreFace(UninstallNFTWithRestoreFaceReq) returns (EmptyStruct);

  // 职业认证信息
  rpc UserProfession (MidReq) returns (UserProfessionReply);
  rpc UserProfessions (MemberMidsReq) returns (UserProfessionsReply);

  //user_extra信息相关接口
  rpc AddUserExtraValue(AddUserExtraValueReq) returns (EmptyStruct);
  rpc BatchAddUserExtraValue(BatchAddUserExtraValueReq) returns (BatchFailedUserExtraValueReply);
  rpc RemoveUserExtraValue(RemoveUserExtraValueReq) returns (EmptyStruct);
  rpc BatchRemoveUserExtraValue(BatchRemoveUserExtraValueReq) returns (BatchFailedUserExtraValueReply);
  rpc GetUserExtraValueSingleKey(UserExtraValueSingleKeyReq) returns (UserExtraValueSingleKeyReply);
  rpc GetUserExtraValue(MemberMidReq) returns (UserExtraValues);
  rpc GetUserExtraBasedOnKeys(GetUserExtraBasedOnKeyReq) returns (UserExtraValues);
  rpc BatchGetUserExtraValue(MemberMidsReq) returns (BatchUserExtraValueReply);

  //archive_extra信息相关接口
  rpc AddArchiveExtraValue(AddArchiveExtraValueReq) returns (EmptyStruct);
  rpc BatchAddArchiveExtraValue(BatchAddArchiveExtraValueReq) returns (BatchFailedArchiveExtraValueReply);
  rpc RemoveArchiveExtraValue(RemoveArchiveExtraValueReq) returns (EmptyStruct);
  rpc BatchRemoveArchiveExtraValue(BatchRemoveArchiveExtraValueReq) returns (BatchFailedArchiveExtraValueReply);
  rpc GetArchiveExtraValueSingleKey(ArchiveExtraValueSingleKeyReq) returns (ArchiveExtraValueSingleKeyReply);
  rpc GetArchiveExtraValue(ArchiveAidReq) returns (ArchiveExtraValues);
  rpc GetArchiveExtraBasedOnKeys(GetArchiveExtraBasedOnKeyReq) returns (ArchiveExtraValues);
  rpc BatchGetArchiveExtraValue(ArchiveAidsReq) returns (BatchArchiveExtraValueReply);


  // 获取用户登录设备中是否有危险设备
  rpc UserBlackDeviceStatus (MemberMidReq) returns (UserBlackDeviceReply);
  // 批量获取用户登录设备中是否有危险设备
  rpc BatchUserBlackDeviceStatus (MemberMidsReq) returns (UserBlackDevicesReply);
  // 查询设备是否是危险设备
  rpc BlackDevicesCheck(BlackDeviceCheckReq) returns (BlackDevicesCheckReply);

  // 管控打包接口(对外)
  // 获取内部定义管控状态(批量)
  rpc InternalDefinedControlStatus(MemberMidsReq) returns (InternalDefinedControlStatusReply);

  // 批量得到member的实名认证状态
  rpc BatchRealnameStatus (MemberMidsReq) returns (BatchRealnameStatusReply);

  rpc UserHonoursTags (MemberMidsReq) returns (UserHonoursReply);
  //获取用户个人荣誉列表
  rpc UserHonourTags (MidReq) returns (UserHonourReply);
  // 批量得到member的节操值
  rpc Morals (MemberMidsReq) returns (MoralsReply);
  /** 为业务统一提供的隐私信息收集方案系列接口 **/
  // 获取业务存储的隐私信息：已脱敏
  rpc GetBusinessStorageInfo(GetBusinessStorageInfoReq) returns (BusinessStorageInfo);
  // 获取业务存储的隐私信息：明文
  rpc GetBusinessStoragePrivacyInfo(GetBusinessStorageInfoReq) returns (BusinessStorageInfo);
  // 通过用户mid获取系统绑定信息后进行存储
  rpc SaveMidCategoryInfo(SaveMidCategoryInfoReq) returns (EmptyStruct);
  // 通过用户输入的信息进行存储
  rpc SaveCategoryInfo(SaveCategoryInfoReq) returns (EmptyStruct);

  // 获取用户已认证资料信息
  rpc UserOfficialInfo(MidReq) returns (UserOfficialInfoReply);
  // 获取用户最新申请的认证资料信息(可为审核中的)
  rpc UserOfficialLatestAuditInfo(MidReq) returns (UserOfficialLatestAuditInfoReply);
  // 取消申请中的官方认证，不存在申请中的则报错40120 -没有正在进行中的官方认证
  rpc CancelUserOfficialApply (CancelUserOfficialApplyReq) returns (EmptyStruct);
  // 获取官方认证实名信息，可批量
  rpc UserOfficialRealnameInfos (MemberMidsReq) returns (UserOfficialRealnameInfosReply);
  // 根据业务方提供的年龄值做判断，后续统一使用该接口，使用前请先申请调用
  rpc RealnameAgeCommonCheck(UserAgeCheckReq) returns (UserAgeCheckResp);
  // 根据批量身份证号或mid获取他们某个日期扫脸记录
  rpc GetFaceVerifyResultByDate(GetFaceVerifyResultByDateReq) returns (GetFaceVerifyResultByDateResp);
  // 根据用户ID,获取对应实名信息之前在哪些账号上认证成功过，限制只返回50条
  rpc UserSameCardUids(MemberMidReq) returns(UserSameCardUidsRsp);

  //批量获取cny直播昵称展示 批量长度不超过100个,如果没有找到合适的昵称，将不返回对应的mid
  rpc CNYNickNames(CNYNameNickNamesReq) returns (CNYNameNickNamesRsp);

  //批量增加和删除cny白名单
  rpc BatchUpdateWhiteNames(BatchUpdateWhiteNamesReq) returns (EmptyStruct);
}

message BatchUpdateWhiteNamesReq{
  map<int64, BatchUpdateWhiteNames> names = 1;
  int32 KeyType = 2;
}

message BatchUpdateWhiteNames {
  string name = 1;
  int32 status = 2;
}