syntax = "proto3";

// `buf.bilibili.co:"bapis/bapis-gen,prefergogo"`

package pangu.metadata.common;


option go_package = "buf.bilibili.co/bapis/bapis-gen/pangu/metadata.common;common";
option java_package = "com.bapis.pangu.metadata.common";
option java_multiple_files = true;

// 藏品属性值
message Attribute {
  string trait_type = 1;
  string value = 2;
  string percent = 3;
  int64 trait_count = 4;
}

// 陀螺仪数据实体
message GyroscopeEntity {
  string trait_type = 1;
  string value = 2;
  int64 trait_count = 3;
  string display_type = 4;
  repeated GyroscopeContent contents = 5;
}

message GyroscopeContent {
  string file_url = 1;
  float scale = 2;
  repeated PhysicalOrientation physical_orientation = 3;
}

message PhysicalOrientation {
  string type = 1;
  repeated int32 angle = 2;
  repeated PhysicalOrientationAnimation animations = 3;
}

message PhysicalOrientationAnimation {
  string type = 1;
  repeated float value = 2;
  string bezier = 3;
}
enum FaceOverflowType {
  FaceOverflowTypeDefault = 0;
  FaceOverflowTypeCustom = 1;
}
message FaceOverflow {
  string face_url = 1;
  repeated Overflow overflow = 2;
  FaceOverflowType overflow_type = 3;
}

message Overflow {
  int64 layer_index = 1;
  string layer_type = 2;
  string file_type = 3;
  string file_url = 4;
  float scale = 5;
  repeated PhysicalOrientation physical_orientation = 6;
}