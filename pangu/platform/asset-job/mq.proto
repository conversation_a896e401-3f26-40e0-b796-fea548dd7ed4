// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
// package 命名使用 bilibili.{discovery_id}.{version} 的方式, version 形如 v1,
// v2 ..
package bilibili.pangu.asset.job.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/pangu/platform.asset.job;api";
option java_package = "com.bapis.pangu.platform.asset.job";
option java_multiple_files = true;

message NFTInfo {
  string nft_id = 1 [(gogoproto.jsontag) = 'nft_id', json_name = "nft_id"];
  int64 item_id = 2 [(gogoproto.jsontag) = 'item_id', json_name = "item_id"];
  string token_id = 3 [(gogoproto.jsontag) = 'token_id', json_name = "token_id"];
  string nft_address = 4 [(gogoproto.jsontag) = 'nft_address', json_name = "nft_address"];
  string video_url = 5 [(gogoproto.jsontag) = 'video_url', json_name = "video_url"];
  string image = 6 [(gogoproto.jsontag) = 'image', json_name = "image"];
  string biz_type = 7 [(gogoproto.jsontag) = 'biz_type', json_name = "biz_type"];
  string interaction_url = 8 [(gogoproto.jsontag) = 'interaction_url', json_name = "interaction_url"];

}

message NFTInfoChangeEvent {
    int64 chain_id = 1 [(gogoproto.jsontag) = "chain_id", json_name = "chain_id"];
    EventType event_type = 2 [(gogoproto.jsontag) = "event_type", json_name = "event_type"];
    NFTInfo nft_info = 3 [(gogoproto.jsontag) = "nft_info", json_name = "nft_info"];
}

enum EventType {
  Create = 0 ;
  Update = 1;
  Delete = 2;
}