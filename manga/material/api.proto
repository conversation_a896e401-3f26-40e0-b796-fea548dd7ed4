// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
// import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package materials.service.v1;

// NOTE: 最后请删除这些无用的注释 (゜-゜)つロ

option go_package = "buf.bilibili.co/bapis/bapis-gen/manga/material;api";
option java_package = "com.bapis.manga.material";
option (gogoproto.goproto_getters_all) = false;
option java_multiple_files = true;
option (wdcli.appid) = "comic.infras.materials-service";


service Materials {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);
  // 获取天马物料
  rpc GetMaterial(GetMaterialReq) returns (GetMaterialResp);
}

// 获取天马物料请求
message GetMaterialReq {
  // 漫画id，一次最大500个，第二优先级，同时传ids时，以ids为准
  repeated int64 comic_ids = 1 [(gogoproto.moretags) = 'form:"comic_ids" validate:"max=500"'];
  // 物料类型, 不传时返回所有类型物料，否则返回指定类型物料
  // 2-天马图文物料
  // 3-漫画app首页竖封
  // 4-漫画app首页横封(预留)
  // 5-漫画app首页视频(预留)
  // 6-长评图文物料
  repeated int64 type = 2 ;
  // 是否需要兜底物料
  bool need_default = 3;
  // 物料id，一次最大500个，第一优先级，同时传comic_ids时，以ids为准
  repeated int64 ids = 4 [(gogoproto.moretags) = 'form:"ids" validate:"max=500"'];
}

// 获取天马物料响应
message GetMaterialResp {
  // 物料列表
  // 请求参数为comic_ids时，返回map[comic_id][]Material，返回所有comicID下的对应type物料
  // 请求参数为ids时，返回map[id][]Material, 返回对应id、type物料但最多只有一个
  map<int64, MaterialList> materials_list = 1;
}

message MaterialList {
  // 物料列表
  repeated Material materials = 1;
}
// 物料
message Material {
  // 物料id
  int32 id = 1;
  // 漫画id
  int32 comic_id = 2;
  // 物料类型
  int32 type = 3;
  // 物料文案
  string content = 4;
  // 物料链接
  string link = 5;
  // 是否兜底物料
  bool  is_default = 6;
  // 关联id
  int64 related_id = 7;
  // 弹幕信息
  repeated MaterialDM dm_infos = 8;
}

message MaterialDM {
  // 弹幕内容
  string content = 1;
  // 弹幕坐标
  int32 progress = 2;
}