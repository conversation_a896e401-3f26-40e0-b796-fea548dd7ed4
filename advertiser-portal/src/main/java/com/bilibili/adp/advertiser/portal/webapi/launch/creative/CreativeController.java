package com.bilibili.adp.advertiser.portal.webapi.launch.creative;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.account.service.IIndustryCategoryService;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.service.launch.WebCreativeService;
import com.bilibili.adp.advertiser.portal.webapi.launch.creative.vo.CreativeVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.creative.vo.ImageVo;
import com.bilibili.adp.advertiser.portal.webapi.resource.vo.TemplateVo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.cpc.biz.services.unit.api.ILauUnitService;
import com.bilibili.adp.cpc.dto.LauUnitBaseDto;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.launch.api.common.MaterialType;
import com.bilibili.adp.launch.api.creative.dto.Creative;
import com.bilibili.adp.launch.api.creative.dto.ImageDto;
import com.bilibili.adp.launch.api.creative.dto.ImageHash;
import com.bilibili.adp.launch.api.service.ILauCreativeService;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.location.common.CmMarkEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2016年9月22日
 */
@Controller
@RequestMapping("/web_api/v1/launch/creatives")
@Api(value = "/creatives", description = "素材相关")
public class CreativeController extends BasicController {

    @Autowired
    private ILauCreativeService lauCreativeService;
    @Autowired
    private ILauUnitService lauUnitService;
    @Autowired
    private IIndustryCategoryService industryCategoryService;
    @Autowired
    private WebCreativeService webCreativeService;
    @Autowired
    private IQueryTemplateService queryTemplateService;

    @Deprecated
    @ApiOperation(value = "根据创意ID查询创意")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<CreativeVo> queryCreative(
            @ApiIgnore Context context,
            @PathVariable("id")
            @ApiParam(required = true, value = "creativeId", defaultValue = "1")
                    Integer creativeId) throws ServiceException {
        Creative creativDto = lauCreativeService.getCreativeDtoById(creativeId);
        Assert.isTrue(context.getAccountId().equals(creativDto.getAccountId()), "不能操作不属于你的创意");

        LauUnitBaseDto unitDto = lauUnitService.getBaseDtoById(creativDto.getUnitId());
        TemplateDto template = queryTemplateService.getTemplateById(creativDto.getTemplateId());
        CmMarkEnum cmMark = CmMarkEnum.getByCode(creativDto.getCmMark());

        return Response.SUCCESS(creativeDto2Vo(creativDto, template, cmMark, PromotionPurposeType.getByCode(unitDto.getPromotionPurposeType())));
    }

    private CreativeVo creativeDto2Vo(Creative creative, TemplateDto template, CmMarkEnum cmMark, PromotionPurposeType launchType) throws ServiceException {
        Map<Integer, String> industryCategoryMap = industryCategoryService.getCategoryMapInIds(Lists.newArrayList(creative.getFirstCategoryId(), creative.getSecondCategoryId()));
        CreativeVo creativeVo = CreativeVo.builder()
                .attach_type(creative.getAttachType())
                .campaign_id(creative.getCampaignId())
                .unit_id(creative.getUnitId())
                .creative_id(creative.getCreativeId())
                .creative_name(creative.getCreativeName())
                .cm_mark(creative.getCmMark())
                .cm_mark_name(cmMark.getDesc())
                .promotion_purpose_content(creative.getPromotionPurposeContent())
                .customized_click_url(creative.getCustomizedClickUrl())
                .customized_imp_url(creative.getCustomizedImpUrl())
                .images(this.convertImageDtos2Vos(creative.getImageDtos(), creative.getTemplateId()))
                .video_url(creative.getVideoUrl())
                .video_id(creative.getVideoId())
                .ext_image_url(creative.getExtImageUrl())
                .ext_image_hash(this.buildHash(MaterialType.EXT_IMAGE, creative.getTemplateId(), creative.getExtImageUrl(), creative.getExtImageMd5()))
                .title(creative.getTitle())
                .description(creative.getDescription())
                .ext_description(creative.getExtDescription())
                .audit_status(creative.getAuditStatus())
                .template(templateDto2Vo(template, launchType))
                .status(creative.getStatus())
                .reason(creative.getReason())
                .button_copy(creative.getButtonCopy())
                .first_category_id(creative.getFirstCategoryId())
                .second_category_id(creative.getSecondCategoryId())
                .first_category_name(industryCategoryMap.getOrDefault(creative.getFirstCategoryId(), "--"))
                .second_category_name(industryCategoryMap.getOrDefault(creative.getSecondCategoryId(), "--"))
                .button_copy_id(creative.getButtonCopyId())
                .button_copy_url(creative.getButtonCopyUrl())
                .tags(creative.getTags())
                .is_history(creative.getIsHistory())
                .build();
        return creativeVo;
    }

    private List<ImageVo> convertImageDtos2Vos(List<ImageDto> imageDtos, Integer templateId) {
        if (CollectionUtils.isEmpty(imageDtos)) {
            return Collections.emptyList();
        }

        return imageDtos.stream().map(dto -> ImageVo.builder()
                .id(dto.getId())
                .image_url(dto.getUrl())
                .image_hash(this.buildHash(MaterialType.IMAGE, templateId, dto.getUrl(), dto.getMd5()))
                .build()).collect(Collectors.toList());
    }

    private String buildHash(MaterialType materialType, Integer templateId, String url, String md5) {
        if (Strings.isNullOrEmpty(url)) {
            return "";
        }
        ImageHash imageHash = ImageHash.builder()
                .url(url)
                .md5(md5)
                .templateId(templateId)
                .type(materialType)
                .build();
        return Base64.encodeBase64String(JSON.toJSONString(imageHash).getBytes());
    }

    private TemplateVo templateDto2Vo(TemplateDto template, PromotionPurposeType launchType) {
        return TemplateVo.builder()
                .id(template.getTemplateId())
                .name(template.getTemplateName())
                ._fill_title(template.getIsFillTitle())
                .title_max_length(template.getTitleMaxLength())
                .title_min_length(template.getTitleMinLength())
                ._fill_desc(template.getIsFillDesc())
                .desc_max_length(template.getDescMaxLength())
                .desc_min_length(template.getDescMinLength())
                ._support_image(template.getIsSupportImage())
                .image_width(template.getImageWidth())
                .image_height(template.getImageHeight())
                .image_kb_limit(template.getImageKbLimit())
                ._support_ext_image(template.getIsSupportExtImage())
                .ext_image_width(template.getExtImageWidth())
                .ext_image_height(template.getExtImageHeight())
                .ext_image_kb_limit(template.getExtImageKbLimit())
                ._support_video(template.getIsSupportVideo())
                .video_width(template.getVideoWidth())
                .video_height(template.getVideoHeight())
                .video_kb_limit(template.getVideoKbLimit())
                .video_duration_max(template.getVideoDurationMax())
                .video_duration_min(template.getVideoDurationMin())
                ._fill_ext_desc(template.getIsFillExtDesc())
                .ext_desc_min_length(template.getExtDescMinLength())
                .ext_desc_max_length(template.getExtDescMaxLength())
                ._support_video_id(template.getIsSupportVideoId())
                .html(template.getHtml())
                ._support_button(template.getIsSupportButton())
                .button_copy_min_length(template.getButtonCopyMinLength())
                .button_copy_max_length(template.getButtonCopyMaxLength())
                ._support_dynamic_layout(template.getIsSupportDynamicLayout())
                .review_examples(webCreativeService.buildTemplateReviewExample(template.getReviewExampleDtos()))
                .support_image_num(template.getImageNum())
                .button_copys(webCreativeService.buttonCopyDtos2Vos(template.getButtonCopyDtos(), launchType))
                .card_type(template.getCardType())
                .build();
    }

}