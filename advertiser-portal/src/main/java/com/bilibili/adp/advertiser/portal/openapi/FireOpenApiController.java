package com.bilibili.adp.advertiser.portal.openapi;

import com.bilibili.adp.account.dto.AccountDto;
import com.bilibili.adp.account.dto.LoginInfoDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.openapi.helper.FireOpenApiHelper;
import com.bilibili.adp.advertiser.portal.openapi.vo.*;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.enums.FireMidStatEnum;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.AccountAgentType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.cpc.biz.bos.account.FireAccountBo;
import com.bilibili.adp.cpc.enums.UserType;
import com.bilibili.adp.passport.api.dto.UserInfoDto;
import com.bilibili.adp.passport.biz.service.PassportService;
import com.bilibili.adp.util.common.DistributedLock;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.crm.platform.api.account.dto.AgentAuthDto;
import com.bilibili.crm.platform.api.enums.FinanceType;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.soa.ISoaAccountLabelService;
import com.bilibili.crm.platform.soa.ISoaAccountService;
import com.bilibili.crm.platform.soa.ISoaAgentService;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.crm.platform.soa.dto.*;
import com.bilibili.sycpb.acc.api.dict.common.BidRoleEnum;
import com.bilibili.sycpb.acc.api.dto.soa.ByMappingIdQueryDto;
import com.bilibili.sycpb.acc.api.dto.soa.SimpleBidInfoDto;
import com.bilibili.sycpb.acc.api.service.soa.ISoaBizAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.unidal.tuple.Triple;
import pleiades.component.http.client.response.BiliDataApiResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping(value = "/open_api/v1/fire")
@Api(value = "/fire", description = "花火跳转中间件【兼容】")
public class FireOpenApiController extends BasicController {
    @Autowired
    private PassportService passportService;
    @Autowired
    private FireOpenApiHelper fireOpenApiHelper;
    @Autowired
    private ISoaAccountLabelService soaAccountLabelService;
    @Autowired
    private ISoaAgentService soaAgentService;
    @Autowired
    private ISoaQueryAccountService soaQueryAccountService;
    @Autowired
    private ISoaAccountService soaAccountService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private ISoaBizAccountService soaBizAccountService;
    @Autowired
    private DistributedLock distributedLock;

    @Value("${fly.image.url:https://s1.hdslb.com/bfs/security/account/202108/3f7af1ecbfefd5964e065cdefbd35d55.jpg}")
    private String flyImageUrl;

    @Value("${qualification.label:219}")
    private Integer qualificationLabel;

    @Value("${business.center.label:169}")
    private Integer businessCenterLabel;

    private final static String FIRE_ACCOUNT_PROC_SUFFIX = ":huahuo:account:process:";

    /**
     * 1. fire_agent_account   fire_ad_account
     * 2. 带上灰度标还是前端调不同接口？？
     *
     *
     * @param request
     * @param fireAdAccountId
     * @return
     * @throws ServiceException
     */
    @ApiOperation(value = "花火中间页状态（1-无资质 2-无起飞代理商账号 3-无起飞广告主账号 4-有起飞广告主账号）")
    @RequestMapping(value = "/state", method = RequestMethod.GET)
    public BiliDataApiResponse<Integer> state(HttpServletRequest request,
                                   @RequestParam(value = "fire_agent_id", required = false) Integer fireAgentId,
                                   @RequestParam(value = "fire_ad_account_id") Integer fireAdAccountId) throws ServiceException {

        FireAccountBo fireAccountBo = fireOpenApiHelper.buildFireAccount(request, fireAgentId, fireAdAccountId);
        Boolean hasQualification = soaAccountLabelService.checkAccountWithLabel(fireAdAccountId,qualificationLabel);
        if(hasQualification!=null && hasQualification){
            return SUCCESS(FireMidStatEnum.NO_QUALIFICATION.getCode());
        }
        Pair<AccountDto,SoaFlyCustomerExistDto> pair = fireOpenApiHelper.getFireAccountInfo(fireAccountBo);
        SoaFlyCustomerExistDto soaFlyCustomerExistDto = pair.getRight();
        if(Boolean.FALSE.equals(soaFlyCustomerExistDto.getExistFlyAgent())){
            return SUCCESS(FireMidStatEnum.NO_FLY_AGENT.getCode());
        }
        if(Boolean.FALSE.equals(soaFlyCustomerExistDto.getExistFlyAccount())){
            return SUCCESS(FireMidStatEnum.NO_FLY_AD.getCode());
        }
        return SUCCESS(FireMidStatEnum.HAS_FLY_AD.getCode());
    }

    @ApiOperation(value = "起飞广告主账号列表")
    @RequestMapping(value = "/account_list", method = RequestMethod.GET)
    public BiliDataApiResponse<Pagination<List<AdAccountVo>>> adAccountList(
            HttpServletRequest request,
            @RequestParam(value = "fire_agent_id", required = false) Integer fireAgentId,
            @RequestParam(value = "mid", required = false, defaultValue = "") Long mid,
            @RequestParam(value = "account_name", required = false) String accountName,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
            @RequestParam(value = "fire_ad_account_id") Integer fireAdAccountId
    ) throws ServiceException{
        FireAccountBo fireAccountBo = fireOpenApiHelper.buildFireAccount(request, fireAgentId, fireAdAccountId);
        Pair<AccountDto,SoaFlyCustomerExistDto> pair = fireOpenApiHelper.getFireAccountInfo(fireAccountBo);
        PageResult<SoaAccountWithAgentDto> pr = soaQueryAccountService.queryPageProFlyAccountByAgentMid(SoaAccountWithAgentQueryDto.builder()
                .agentCustomerId(pair.getRight().getAgentCustomerId())
                .agentBid(fireAccountBo.getBid())
                .accountCustomerId(pair.getRight().getCustomerId())
                .huahuoAccountId(pair.getLeft().getAccountId())
                .usernameLike(accountName)
                .longMid(mid)
                .build(),page,size);
        if(CollectionUtils.isEmpty(pr.getRecords())) {
            return SUCCESS(new Pagination(page, pr.getTotal(), Collections.emptyList()));
        }
        List<AdAccountVo> vos = pr.getRecords().stream()
                .map(fireOpenApiHelper::accountWithAgentDto2Vo)
                .collect(Collectors.toList());
        return SUCCESS(new Pagination(page, pr.getTotal(), vos));
    }

    @ApiOperation(value = "开通代理账号")
    @RequestMapping(value = "/open_proxy_account", method = RequestMethod.POST)
    public BiliDataApiResponse<AgentAccountVo> openProxyAccount(HttpServletRequest request,HttpServletResponse response,
                                                        @RequestBody OpenProxyAccountVo vo) throws Exception {
        RLock lock = distributedLock.getLock(vo.getFire_ad_account_id(), FIRE_ACCOUNT_PROC_SUFFIX);
        try{
            FireAccountBo fireAccountBo = fireOpenApiHelper.buildFireAccount(request, vo.getFire_agent_id(), vo.getFire_ad_account_id());
            Triple<AccountDto,AccountDto,SoaFlyCustomerExistDto> triple = fireOpenApiHelper.getFireAgentAccountInfo(fireAccountBo);
            SoaFlyCustomerExistDto existDto = triple.getLast();
            Assert.isTrue(existDto!=null && Boolean.FALSE.equals(existDto.getExistFlyAgent()),"同一客户下无起飞代理商才可开通");
            //开通起飞代理商账号
            Integer agentAccountId = soaAccountService.createProFlyAgentAccount(Operator.SYSTEM, SoaProFlyCreateAgentAccountDto.builder()
                    .customerId(triple.getLast().getAgentCustomerId())
                    .agentType(AccountAgentType.AGENT_EFFECT.getCode())

                    .longFireAgentMid(fireAccountBo.getMid())//登录的mid--代理商mid
                    .fireAgentId(fireAccountBo.getFireAgentId())
                    .fireAgentBid(fireAccountBo.getBid())
                    .financeType(FinanceType.CASH.getCode())
                    .username(triple.getMiddle().getUsername()+"-商业起飞")
                    .longMid(vo.getMid())
                    .isSupportFly(1)
                    .accountType(1)
                    .agentAuthExpireDate(Timestamp.valueOf("2030-08-08 00:00:00"))
                    .agentAuthDtos(Collections.singletonList(AgentAuthDto.builder()
                            .imageUrl(flyImageUrl)
                            .build()))
                    .build());
            log.info("after create agent with agentAccountId={}", agentAccountId);
            if (fireAccountBo.getBid() != null){
                log.info("bindFlyAccount with bid={} accountId={}", fireAccountBo.getBid(), agentAccountId);
                soaBizAccountService.bindFlyAccount(fireAccountBo.getBid(), agentAccountId);
            }
            //开通起飞广告主账号
            SoaProFlyCreateAccountDto dto = SoaProFlyCreateAccountDto.builder()
                    .departmentId(businessCenterLabel)
                    .financeType(FinanceType.CASH.getCode())
                    .fireAccountId(vo.getFire_ad_account_id())
                    .accountCustomerId(triple.getLast().getCustomerId())
                    .agentCustomerId(triple.getLast().getAgentCustomerId())
                    .username(triple.getFirst().getUsername()+"-商业起飞")
                    .agentAuthExpireDate(Timestamp.valueOf("2030-08-08 00:00:00"))
                    .agentAuthDtos(Collections.singletonList(AgentAuthDto.builder()
                            .imageUrl(flyImageUrl)
                            .build()))
                    .build();
            SoaCreateAccountRepDto soaCreateAccountRepDto = soaAccountService.createProFlyAccount(Operator.SYSTEM, dto);
            log.info("after create account with soaCreateAccountRepDto={}", soaCreateAccountRepDto);
            if (fireAccountBo.getBid() != null){
                SimpleBidInfoDto fireAccountBidInfoDto = soaBizAccountService.queryBidInfoByMappingId(ByMappingIdQueryDto.builder()
                        .bidRole(BidRoleEnum.ADVERTISE.getCode())
                        .mappingId(vo.getFire_ad_account_id().longValue())
                        .build());
                log.info("try to check bind bid with fireAccountBidInfoDto={} and account={}", fireAccountBidInfoDto, soaCreateAccountRepDto.getAccountId());
                if (fireAccountBidInfoDto != null){
                    soaBizAccountService.bindFlyAccount(fireAccountBidInfoDto.getBid(), soaCreateAccountRepDto.getAccountId());
                }
            }
            Boolean ans = soaAgentService.accountBelongCheck(vo.getFire_ad_account_id(),soaCreateAccountRepDto.getAccountId());
            Assert.isTrue(ans!=null && ans,"登录起飞广告主id和火花广告主id不属于同一个客户");
            //代理商登录广告主
            fireOpenApiHelper.accountLogin4Fire(request, response, null, agentAccountId, soaCreateAccountRepDto.getAccountId());
            return SUCCESS(AgentAccountVo.builder()
                    .mid(soaCreateAccountRepDto.getLongAgentMid())
//                .agent_id()
                    .account_name(soaCreateAccountRepDto.getAgentAccountName())
                    .build());
        }finally {
            distributedLock.unlock(lock);
        }
    }



    @ApiOperation(value = "开通起飞账号")
    @RequestMapping(value = "/open_fly_account", method = RequestMethod.POST)
    public BiliDataApiResponse<AgentAccountVo> openProxyAccount(HttpServletRequest request, HttpServletResponse response,
                                                 @RequestBody OpenFlyAccount vo) throws Exception {
        RLock lock = distributedLock.getLock(vo.getFire_ad_account_id(), FIRE_ACCOUNT_PROC_SUFFIX);
        try{
            FireAccountBo fireAccountBo = fireOpenApiHelper.buildFireAccount(request, vo.getFire_agent_id(), vo.getFire_ad_account_id());
            Pair<AccountDto,SoaFlyCustomerExistDto> pair = fireOpenApiHelper.getFireAccountInfo(fireAccountBo);
            SoaFlyCustomerExistDto existDto = pair.getRight();
            Assert.isTrue(existDto!=null && Boolean.TRUE.equals(existDto.getExistFlyAgent())
                    && Boolean.FALSE.equals(existDto.getExistFlyAccount()),"同一客户下无起飞广告主才可开通");
            //开通起飞广告主账号
            SoaProFlyCreateAccountDto dto = new SoaProFlyCreateAccountDto();
            dto.setDepartmentId(businessCenterLabel);
            dto.setFinanceType(FinanceType.CASH.getCode());
            dto.setFireAccountId(vo.getFire_ad_account_id());
            dto.setAccountCustomerId(pair.getRight().getCustomerId());
            dto.setAgentCustomerId(pair.getRight().getAgentCustomerId());
            dto.setUsername(pair.getLeft().getUsername()+"-商业起飞");
            dto.setAgentAuthExpireDate(Timestamp.valueOf("2030-08-08 00:00:00"));
            dto.setAgentAuthDtos(Collections.singletonList(AgentAuthDto.builder()
                    .imageUrl(flyImageUrl)
                    .build()));
            SoaCreateAccountRepDto soaCreateAccountRepDto = soaAccountService.createProFlyAccount(Operator.SYSTEM, dto);
            log.info("after create account only with soaCreateAccountRepDto={}", soaCreateAccountRepDto);
            Boolean ans = soaAgentService.accountBelongCheck(vo.getFire_ad_account_id(),soaCreateAccountRepDto.getAccountId());
            Assert.isTrue(ans!=null && ans,"登录起飞广告主id和火花广告主id不属于同一个客户");
            if (fireAccountBo.getBid() != null){
                SimpleBidInfoDto fireAccountBidInfoDto = soaBizAccountService.queryBidInfoByMappingId(ByMappingIdQueryDto.builder()
                        .bidRole(BidRoleEnum.ADVERTISE.getCode())
                        .mappingId(vo.getFire_ad_account_id().longValue())
                        .build());
                log.info("try to check bind bid with fireAccountBidInfoDto={} and account={}", fireAccountBidInfoDto, soaCreateAccountRepDto.getAccountId());
                if (fireAccountBidInfoDto != null){
                    soaBizAccountService.bindFlyAccount(fireAccountBidInfoDto.getBid(), soaCreateAccountRepDto.getAccountId());
                }
            }
            //代理商登录广告主
            fireOpenApiHelper.accountLogin4Fire(request, response, null, soaCreateAccountRepDto.getDependencyAccountId(), soaCreateAccountRepDto.getAccountId());
            return SUCCESS(AgentAccountVo.builder()
                    .mid(soaCreateAccountRepDto.getLongAgentMid())
                    .account_name(soaCreateAccountRepDto.getAgentAccountName())
                    .build());
        }finally {
            distributedLock.unlock(lock);
        }
    }

    @ApiOperation(value = "花火代理商选择用户登录")
    @RequestMapping(value = "/fire_agent/login", method = RequestMethod.POST)
    public BiliDataApiResponse<LoginInfoDto> fireAgentLogin(HttpServletRequest request, HttpServletResponse response,
                                                    @RequestBody FireAgentLoginVo vo) throws Exception {
        FireAccountBo fireAccountBo = fireOpenApiHelper.buildFireAccount(request, vo.getFire_agent_id(), vo.getFire_account_id());
        Pair<AccountDto,SoaFlyCustomerExistDto> pair = fireOpenApiHelper.getFireAccountInfo(fireAccountBo);

        Boolean ans = soaAgentService.accountBelongCheck(vo.getFire_account_id(),vo.getAccount_id());
        Assert.isTrue(ans!=null && ans,"登录起飞广告主id和火花广告主id不属于同一个客户");
        //代理商登录广告主
        String accessToken = fireOpenApiHelper.accountLogin4Fire(request, response, vo.getDependency_agent_id(), null, vo.getAccount_id());
        return SUCCESS(LoginInfoDto.builder()
                .accessToken(accessToken)
                .accountId(vo.getAccount_id())
                .build());
    }

    @ApiOperation(value = "校验mid是否已绑定代理商账号(0-没绑过 1-已绑过）")
    @RequestMapping(value = "/check_mid_bind", method = RequestMethod.GET)
    public BiliDataApiResponse<Integer> checkMidBind(HttpServletRequest request,
                                          @RequestParam("mid") Long mid) throws ServiceException {
        UserInfoDto userInfoDto = passportService.getUserByMid(mid);
        Assert.notNull(userInfoDto,"该mid不存在");

        List<Integer> userTypes = Arrays.asList(UserType.PERSONAL_USER.getCode(), UserType.ORG_USER.getCode());
        List<AccountDto> list =  queryAccountService.getAccountDtosByMid4Fly(mid,userTypes);
        if(CollectionUtils.isEmpty(list)){
            return SUCCESS(IsValid.FALSE.getCode());
        }
        List<Boolean> isAgentList = list.stream().map(AccountDto::getIsAgent).collect(Collectors.toList());
        return SUCCESS(isAgentList.contains(Boolean.TRUE)?IsValid.TRUE.getCode():IsValid.FALSE.getCode());
    }

    @ApiOperation(value = "校验账号是否开通成功(0-未成功 1-成功)")
    @RequestMapping(value = "/check_open_success", method = RequestMethod.GET)
    public BiliDataApiResponse<Integer> checkOpenSuccess(
            HttpServletRequest request,
            @RequestParam(value = "fire_agent_id", required = false) Integer fireAgentId,
            @RequestParam(value = "fire_ad_account_id") Integer fireAdAccountId) throws ServiceException {
        FireAccountBo fireAccountBo = fireOpenApiHelper.buildFireAccount(request, fireAgentId, fireAdAccountId);
        SoaFlyCustomerExistDto existDto = fireOpenApiHelper.checkFlyAccountExist(fireAccountBo);
        return SUCCESS(existDto != null && Boolean.TRUE.equals(existDto.getExistFlyAgent())
                && Boolean.TRUE.equals(existDto.getExistFlyAccount())? IsValid.TRUE.getCode() : IsValid.FALSE.getCode());
    }


}
