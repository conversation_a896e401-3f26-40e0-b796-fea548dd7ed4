package com.bilibili.adp.advertiser.task.job;

import com.bilibili.adp.cpc.biz.services.archive.MainArcAtmosphereExecuteService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName RefreshUnitArcAtmosphereJob
 * <AUTHOR>
 * @Date 2024/1/19 6:30 下午
 * @Version 1.0
 **/
@Component
@Slf4j
@JobHandler("RefreshUnitArcAtmosphereJob")
public class RefreshUnitArcAtmosphereJob extends IJobHandler {

    @Autowired
    private MainArcAtmosphereExecuteService mainArcAtmosphereExecuteService;


    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Integer startUnitId = Integer.parseInt(param);
        mainArcAtmosphereExecuteService.refreshContentAndPersonFlyUnitArc(startUnitId);
        return ReturnT.SUCCESS;
    }
}
