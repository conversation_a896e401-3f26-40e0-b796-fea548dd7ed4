package com.bilibili.adp.advertiser.portal.service.launch;

import com.bilibili.adp.account.service.IQueryAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class AccountListener implements ApplicationListener {
    @Autowired
    private IQueryAccountService queryAccountService;
    private boolean initPersonFlyWhite = false;

    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        freshPersonFlyAccountWhite();
    }

    private void freshPersonFlyAccountWhite() {
        if(!initPersonFlyWhite) {
            queryAccountService.initPersonFlyAccountWhite();
            initPersonFlyWhite = true;
        }
    }
}
