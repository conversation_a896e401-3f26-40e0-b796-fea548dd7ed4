package com.bilibili.adp.advertiser.portal.webapi.resource.vo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChannelVo {

    private int id;

    private String name;
    
    private List<Integer> target_types;
    
    List<SlotGroupVo> childs;

}