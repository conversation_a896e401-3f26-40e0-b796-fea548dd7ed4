package com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter;

import com.bapis.ad.jupiter.archive.Stat;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.ArchiveAnalysisReportChartVo;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.ArchiveAnalysisReportDetailVo;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.ArchiveAnalysisReportVo;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveAnalysisDetailBo;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveAnalysisReportChartBo;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
public abstract class ArchiveAnalysisReportConverterDecorator implements ArchiveAnalysisReportConverter {
    private final BigDecimal HUNDRED = BigDecimal.valueOf(100);
    private final ArchiveAnalysisReportConverter delegate;

    public ArchiveAnalysisReportDetailVo bo2Vo(ArchiveAnalysisDetailBo detailBo) {
        ArchiveAnalysisReportDetailVo detailVo = delegate.bo2Vo(detailBo);
        //「率」前端展示百分比 需 * 100 保留2位小数
        detailVo.setClickThroughRate(detailBo.getClickThroughRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setPlay3SRate(detailBo.getPlay3SRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setPlay5SRate(detailBo.getPlay5SRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setPlay10SRate(detailBo.getPlay10SRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setVideoPlayRate(detailBo.getVideoPlayRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setVideoLikeRate(detailBo.getVideoLikeRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setFanIncreaseRate(detailBo.getFanIncreaseRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setFirstCommentCopyRate(detailBo.getFirstCommentCopyRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setUnderBoxLinkRate(detailBo.getUnderBoxLinkRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setCommentUrlShowRate(detailBo.getCommentUrlShowRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setCommentUrlShowClickRate(detailBo.getCommentUrlShowClickRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setFormSubmitRate(detailBo.getFormSubmitRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setFormUserCostRate(detailBo.getFormUserCostRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setClueValidCountRate(detailBo.getClueValidCountRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setApplyRate(detailBo.getApplyRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setCreditRate(detailBo.getCreditRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setWithdrawDepositsRate(detailBo.getWithdrawDepositsRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setGameSubscribeApiRate(detailBo.getGameSubscribeApiRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setDownloadSuccessRate(detailBo.getDownloadSuccessRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setInstallSuccessRate(detailBo.getInstallSuccessRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setAppActiveRate(detailBo.getAppActiveRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setGameActiveApiRate(detailBo.getGameActiveApiRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setUserRegisterRate(detailBo.getUserRegisterRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setUserFirstCostRate(detailBo.getUserFirstCostRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setGameUserFirstCostRate(detailBo.getGameUserFirstCostRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setRetentionRate(detailBo.getRetentionRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setAppCallUpRate(detailBo.getAppCallUpRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setLpCallUpSuccessRate(detailBo.getLpCallUpSuccessRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setLpCallUpSuccessStayRate(detailBo.getLpCallUpSuccessStayRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setOrderPlaceRate(detailBo.getOrderPlaceRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setOrderConversionRate(detailBo.getOrderConversionRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setFirstOrderPlaceRate(detailBo.getFirstOrderPlaceRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setActionValidRate(detailBo.getActionValidRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        detailVo.setCommentUrlClickRate(detailBo.getCommentUrlClickRate().multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        //「成本」保留2位小数
        detailVo.setCostPerClick(detailBo.getCostPerClick().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerMille(detailBo.getCostPerMille().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerFormSubmit(detailBo.getCostPerFormSubmit().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerFormUserCost(detailBo.getCostPerFormUserCost().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerClueValid(detailBo.getCostPerClueValid().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerApply(detailBo.getCostPerApply().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerCredit(detailBo.getCostPerCredit().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerWithdrawDeposits(detailBo.getCostPerWithdrawDeposits().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerGameSubscribeApi(detailBo.getCostPerGameSubscribeApi().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerDownloadSuccess(detailBo.getCostPerDownloadSuccess().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerInstallSuccess(detailBo.getCostPerInstallSuccess().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerAppActive(detailBo.getCostPerAppActive().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerGameActiveApi(detailBo.getCostPerGameActiveApi().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerUserRegister(detailBo.getCostPerUserRegister().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerUserFirstCost(detailBo.getCostPerUserFirstCost().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerGameUserFirstCost(detailBo.getCostPerGameUserFirstCost().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerRetention(detailBo.getCostPerRetention().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerAppCallUp(detailBo.getCostPerAppCallUp().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerLpCallUpSuccess(detailBo.getCostPerLpCallUpSuccess().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerLpCallUpSuccessStay(detailBo.getCostPerLpCallUpSuccessStay().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerOrderPlace(detailBo.getCostPerOrderPlace().setScale(2, RoundingMode.HALF_UP));
        detailVo.setOrderRoi(detailBo.getOrderRoi().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerFirstOrderPlace(detailBo.getCostPerFirstOrderPlace().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerActionValid(detailBo.getCostPerActionValid().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCostPerCommentUrlClick(detailBo.getCostPerCommentUrlClick().setScale(2, RoundingMode.HALF_UP));
        detailVo.setCost(detailBo.getCost().setScale(2, RoundingMode.HALF_UP));
        //「金额」是分 除以100 保留2位小数
        detailVo.setUserCostAmount(BigDecimal.valueOf(detailBo.getUserCostAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP));
        detailVo.setUserFirstCostAmount(BigDecimal.valueOf(detailBo.getUserFirstCostAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP));
        detailVo.setOrderPlaceAmount(BigDecimal.valueOf(detailBo.getOrderPlaceAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP));
        detailVo.setFirstOrderPlaceAmount(BigDecimal.valueOf(detailBo.getFirstOrderPlaceAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP));
        return detailVo;
    }

    @Override
    public ArchiveAnalysisReportChartVo bo2Vo(ArchiveAnalysisReportChartBo bo) {
        ArchiveAnalysisReportVo total = this.bo2Vo(bo.getTotal());
        List<ArchiveAnalysisReportVo> details = bo
                .getDetail()
                .stream()
                .map(this::bo2Vo)
                .collect(Collectors.toList());
        return ArchiveAnalysisReportChartVo.builder()
                .xaxis(bo.getXaxis())
                .total(total)
                .detail(details)
                .build();
    }

    public ArchiveAnalysisReportVo bo2Vo(Stat stat) {
        ArchiveAnalysisReportVo vo = delegate.bo2Vo(stat);
        //「率」前端展示百分比 需 * 100 保留2位小数
        vo.setClickThroughRate(BigDecimal.valueOf(stat.getClickThroughRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setPlay3SRate(BigDecimal.valueOf(stat.getPlay3SRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setPlay5SRate(BigDecimal.valueOf(stat.getPlay5SRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setPlay10SRate(BigDecimal.valueOf(stat.getPlay10SRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setVideoPlayRate(BigDecimal.valueOf(stat.getVideoPlayRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setVideoLikeRate(BigDecimal.valueOf(stat.getVideoLikeRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setFanIncreaseRate(BigDecimal.valueOf(stat.getFanIncreaseRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setFirstCommentCopyRate(BigDecimal.valueOf(stat.getFirstCommentCopyRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setUnderBoxLinkRate(BigDecimal.valueOf(stat.getUnderBoxLinkRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setCommentUrlShowRate(BigDecimal.valueOf(stat.getCommentUrlShowRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setCommentUrlShowClickRate(BigDecimal.valueOf(stat.getCommentUrlShowClickRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setFormSubmitRate(BigDecimal.valueOf(stat.getFormSubmitRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setFormUserCostRate(BigDecimal.valueOf(stat.getFormUserCostRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setClueValidCountRate(BigDecimal.valueOf(stat.getClueValidCountRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setApplyRate(BigDecimal.valueOf(stat.getApplyRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setCreditRate(BigDecimal.valueOf(stat.getCreditRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setWithdrawDepositsRate(BigDecimal.valueOf(stat.getWithdrawDepositsRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setGameSubscribeApiRate(BigDecimal.valueOf(stat.getGameSubscribeApiRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setDownloadSuccessRate(BigDecimal.valueOf(stat.getDownloadSuccessRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setInstallSuccessRate(BigDecimal.valueOf(stat.getInstallSuccessRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setAppActiveRate(BigDecimal.valueOf(stat.getAppActiveRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setGameActiveApiRate(BigDecimal.valueOf(stat.getGameActiveApiRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setUserRegisterRate(BigDecimal.valueOf(stat.getUserRegisterRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setUserFirstCostRate(BigDecimal.valueOf(stat.getUserFirstCostRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setGameUserFirstCostRate(BigDecimal.valueOf(stat.getGameUserFirstCostRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setRetentionRate(BigDecimal.valueOf(stat.getRetentionRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setAppCallUpRate(BigDecimal.valueOf(stat.getAppCallUpRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setLpCallUpSuccessRate(BigDecimal.valueOf(stat.getLpCallUpSuccessRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setLpCallUpSuccessStayRate(BigDecimal.valueOf(stat.getLpCallUpSuccessStayRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setOrderPlaceRate(BigDecimal.valueOf(stat.getOrderPlaceRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setOrderConversionRate(BigDecimal.valueOf(stat.getOrderConversionRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setFirstOrderPlaceRate(BigDecimal.valueOf(stat.getFirstOrderPlaceRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setActionValidRate(BigDecimal.valueOf(stat.getActionValidRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        vo.setCommentUrlClickRate(BigDecimal.valueOf(stat.getCommentUrlClickRate()).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP));
        //「成本」保留2位小数
        vo.setCostPerClick(BigDecimal.valueOf(stat.getCostPerClick()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerMille(BigDecimal.valueOf(stat.getCostPerMille()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerFormSubmit(BigDecimal.valueOf(stat.getCostPerFormSubmit()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerFormUserCost(BigDecimal.valueOf(stat.getCostPerFormUserCost()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerClueValid(BigDecimal.valueOf(stat.getCostPerClueValid()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerApply(BigDecimal.valueOf(stat.getCostPerApply()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerCredit(BigDecimal.valueOf(stat.getCostPerCredit()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerWithdrawDeposits(BigDecimal.valueOf(stat.getCostPerWithdrawDeposits()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerGameSubscribeApi(BigDecimal.valueOf(stat.getCostPerGameSubscribeApi()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerDownloadSuccess(BigDecimal.valueOf(stat.getCostPerDownloadSuccess()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerInstallSuccess(BigDecimal.valueOf(stat.getCostPerInstallSuccess()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerAppActive(BigDecimal.valueOf(stat.getCostPerAppActive()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerGameActiveApi(BigDecimal.valueOf(stat.getCostPerGameActiveApi()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerUserRegister(BigDecimal.valueOf(stat.getCostPerUserRegister()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerUserFirstCost(BigDecimal.valueOf(stat.getCostPerUserFirstCost()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerGameUserFirstCost(BigDecimal.valueOf(stat.getCostPerGameUserFirstCost()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerRetention(BigDecimal.valueOf(stat.getCostPerRetention()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerAppCallUp(BigDecimal.valueOf(stat.getCostPerAppCallUp()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerLpCallUpSuccess(BigDecimal.valueOf(stat.getCostPerLpCallUpSuccess()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerLpCallUpSuccessStay(BigDecimal.valueOf(stat.getCostPerLpCallUpSuccessStay()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerOrderPlace(BigDecimal.valueOf(stat.getCostPerOrderPlace()).setScale(2, RoundingMode.HALF_UP));
        vo.setOrderRoi(BigDecimal.valueOf(stat.getOrderRoi()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerFirstOrderPlace(BigDecimal.valueOf(stat.getCostPerFirstOrderPlace()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerActionValid(BigDecimal.valueOf(stat.getCostPerActionValid()).setScale(2, RoundingMode.HALF_UP));
        vo.setCostPerCommentUrlClick(BigDecimal.valueOf(stat.getCostPerCommentUrlClick()).setScale(2, RoundingMode.HALF_UP));
        vo.setCost(BigDecimal.valueOf(stat.getCost()).setScale(2, RoundingMode.HALF_UP));
        //「金额」是分 除以100 保留2位小数
        vo.setUserCostAmount(BigDecimal.valueOf(stat.getUserCostAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP));
        vo.setUserFirstCostAmount(BigDecimal.valueOf(stat.getUserFirstCostAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP));
        vo.setOrderPlaceAmount(BigDecimal.valueOf(stat.getOrderPlaceAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP));
        vo.setFirstOrderPlaceAmount(BigDecimal.valueOf(stat.getFirstOrderPlaceAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP));
        return vo;
    }

}
