package com.bilibili.adp.advertiser.portal.webapi.effect_ad.ad_product.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@ApiModel
public class SdpaProductLibraryVo {

    /**
     * 产品库id
     */
    private Long libraryId;

    /**
     * 产品库类型1-课程库, 2-借贷产品库
     */
    @ApiModelProperty(name = "产品库类型1-课程库, 2-借贷产品库")
    private Integer type;

    /**
     * 产品库名称
     */
    private String name;

    /**
     * 创建账户
     */
    private Integer createAccount;

    /**
     * 产品数量
     */
    private Integer productCount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间")
    private String ctime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "更新时间")
    private String mtime;

    /**
     * 归属类型,0：创建账户，1：被分享账户
     */
    @ApiModelProperty(name = "归属类型,0：创建账户，1：被分享账户")
    private Integer belongType;

}
