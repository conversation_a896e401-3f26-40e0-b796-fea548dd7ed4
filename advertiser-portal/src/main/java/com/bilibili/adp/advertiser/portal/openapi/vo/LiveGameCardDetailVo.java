package com.bilibili.adp.advertiser.portal.openapi.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2017年3月27日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LiveGameCardDetailVo {

    @ApiModelProperty("卡片类型：游戏-0，应用-1")
    private Integer cardType;

    @ApiModelProperty("投放平台: 1-ios, 2-安卓")
    private String launchPlatform;

    @ApiModelProperty("下载方式: 0-apk下载, 1-跳转h5, 2-从H5跳转到app store, 3-游戏卡直接跳转app store ")
    private Integer downloadType;

    @ApiModelProperty("应用包名称")
    private String apkName;

    @ApiModelProperty("应用包的名称")
    private String appPackageName;

    @ApiModelProperty("应用包url")
    private String apkUrl;

    @ApiModelProperty("apk按钮文案")
    private String apkButtonText;

    @ApiModelProperty("apk版本")
    private String apkVersion;

    @ApiModelProperty("apk开发商")
    private String apkDeveloperName;

    @ApiModelProperty("apk更新日期")
    private String apkUpdateTime;

    @ApiModelProperty("apk头像小图")
    private String apkIcon;

    @ApiModelProperty("应用包id")
    private String appStoreId;

    @ApiModelProperty("游戏id")
    private Integer gameId;

    @ApiModelProperty("游戏名称")
    private String gameName;

    @ApiModelProperty("游戏图标")
    private String gameIcon;

    @ApiModelProperty("游戏小图 需符合直播侧设计稿")
    private String gameCardIcon;

    @ApiModelProperty("h5背景图")
    private String h5BgImage;

    @ApiModelProperty("h5应用描述")
    private String h5ApkDescription;

    //隐私政策
    private String infoPrivacyName;

    //隐私政策url
    private String infoPrivacyUrl;

    //权限信息
    private String infoPolicyName;

    //权限信息url
    private String infoPolicyUrl;
}
