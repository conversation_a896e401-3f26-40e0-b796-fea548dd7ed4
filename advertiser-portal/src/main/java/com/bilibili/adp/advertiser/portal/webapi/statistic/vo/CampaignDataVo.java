package com.bilibili.adp.advertiser.portal.webapi.statistic.vo;

import com.bilibili.adp.web.framework.annotations.CsvHeader;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/9/22.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CampaignDataVo {

    @CsvHeader("日期")
    @ApiModelProperty(notes = "日期")
    private String date;

    @CsvHeader("计划id")
    @ApiModelProperty(notes = "计划id")
    private Integer campaign_id;

    @CsvHeader("计划名")
    @ApiModelProperty(notes = "计划名")
    private String campaign_name;

    @ApiModelProperty(notes = "展示数量")
    @CsvHeader("展示数量")
    private Integer show_count;

    @CsvHeader("点击次数")
    @ApiModelProperty(notes = "点击次数")
    private Integer click_count;

    @CsvHeader("点击率")
    @ApiModelProperty(notes = "点击率")
    private BigDecimal click_rate;

    @CsvHeader("平均千次展现费用(元)")
    @ApiModelProperty(notes = "平均千次展现费用(元)")
    private BigDecimal average_cost_per_thousand;

    @CsvHeader("平均点击费用(元)")
    @ApiModelProperty(notes = "平均点击费用(元)")
    private BigDecimal cost_per_click;

    @CsvHeader("消费(元)")
    @ApiModelProperty(notes = "消费(元)")
    private BigDecimal cost;

}
