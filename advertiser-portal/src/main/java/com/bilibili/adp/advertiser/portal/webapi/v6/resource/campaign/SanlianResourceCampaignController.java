package com.bilibili.adp.advertiser.portal.webapi.v6.resource.campaign;

import com.bapis.ad.pandora.resource.*;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.campaign.bos.SanlianGetCampaignContentRespBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.campaign.bos.SanlianGetPromotionPurposeTypeRespBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mapper.ResourceCampaignMapper;
import com.bilibili.adp.web.framework.core.Response;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import pleiades.venus.starter.rpc.client.RPCClient;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName SanlianCampaignResourceController
 * <AUTHOR>
 * @Date 2024/3/14 2:46 下午
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/web_api/v6/resource/sanlian/campaign")
@Api(value = "/v6/resource/sanlian/campaign", tags = {"计划", "v6"})
public class SanlianResourceCampaignController extends BasicController {
    @RPCClient("sycpb.platform.cpm-pandora")
    private CampaignResourceServiceGrpc.CampaignResourceServiceBlockingStub campaignResourceServiceBlockingStub;

    @ApiOperation("获取推广目的类型")
    @RequestMapping(value = "/get_promotion_purpose_type", method = RequestMethod.GET)
    public Response<List<SanlianGetPromotionPurposeTypeRespBo>> getPromotionPurposeType(@ApiIgnore Context context) {
        ListPromotionPurposeTypeReq req = ListPromotionPurposeTypeReq.newBuilder()
                .setAccountId(context.getAccountId())
                .build();
        ListPromotionPurposeTypeResp resp =
                campaignResourceServiceBlockingStub.listPromotionPurposeType(req);
        List<SanlianGetPromotionPurposeTypeRespBo> boList = resp.getEntitySetList().stream()
                .map(ResourceCampaignMapper.MAPPER::convertRpcInfo2Bo)
                .collect(Collectors.toList());
        return Response.SUCCESS(boList);
    }

    @ApiOperation("获取支持的广告类型")
    @RequestMapping(value = "/get_content", method = RequestMethod.GET)
    public Response<SanlianGetCampaignContentRespBo> getCampaignContent(
            @ApiIgnore Context context,
            @RequestParam("promotion_purpose_type") Integer promotionPurposeType) {
        GetCampaignContentReq req = GetCampaignContentReq.newBuilder()
                .setAccountId(context.getAccountId())
                .setPromotionPurposeTypeValue(promotionPurposeType)
                .build();
        GetCampaignContentResp resp = campaignResourceServiceBlockingStub.getCampaignContent(req);
        SanlianGetCampaignContentRespBo respBo =
                ResourceCampaignMapper.MAPPER.convertRpcInfo2Bo(resp.getContent());
        return Response.SUCCESS(respBo);
    }

}
