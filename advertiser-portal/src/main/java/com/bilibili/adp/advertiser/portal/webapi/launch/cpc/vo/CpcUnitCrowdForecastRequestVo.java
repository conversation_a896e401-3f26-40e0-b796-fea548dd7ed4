/** 
* <AUTHOR> 
* @date  2018年3月29日
*/ 

package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CpcUnitCrowdForecastRequestVo {

	private CpcUnitTargetVo target;

	private GameCardUnitTargetVo gameCardUnitTargets;

	private BigDecimal cost_price;

	private Integer sales_type;

	private Integer campaign_id;

	private Integer unit_id;

	private Integer frequency_limit;

	private List<List<String>> launch_time;

	private Integer app_id;
}
