package com.bilibili.adp.advertiser.portal.webapi.v6.resource.mapper;

import com.bapis.ad.pandora.resource.*;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.common.SanlianAccessibilityBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.unit.bos.*;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.sanlian.SanlianAccessibilityTypeEnum;
import com.bilibili.adp.cpc.enums.sanlian.SanlianBaseCpaTargetEnum;
import com.bilibili.adp.resource.api.target_lau.dto.TargetTreeDto;
import io.swagger.models.auth.In;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * @ClassName ResourceUnitMapper
 * <AUTHOR>
 * @Date 2024/3/15 2:35 下午
 * @Version 1.0
 **/
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ResourceUnitMapper {
    ResourceUnitMapper MAPPER = Mappers.getMapper(ResourceUnitMapper.class);

    List<Integer> SUPPORTED_CPA_TARGETS = Arrays.asList(
            CpaTarget.CPA_FIRST_DAY_ROI_VALUE,
            CpaTarget.CPA_PAID_IN_24H_ROI_VALUE,
            CpaTarget.CPA_IN_APP_CHARGE_24H_ROI_VALUE,
            CpaTarget.CPA_PAID_IN_7D_ROI_VALUE,
            CpaTarget.CPA_GAME_CHARGE_IN_24H_MIX_VALUE
    );

    SanlianGetPromotionPurposeContentRespBo convertRpcInfo2Bo(PromotionContentTypeInfo info);

    @Mapping(target = "deepCpaTarget", source = "deepCpaTargetInfoSet")
    @Mapping(target = "assistCpaTarget", source = "assistCpaTargetSet")
    SanlianGetCpaTargetRespBo convertRpcInfo2Bo(CpaTargetEntity entity);

    SanlianCpaTargetBo convertRpcInfo2Bo(CpaTargetInfo info);

    @Mapping(target = "cpaTargetMinBid", expression = "java(CONVERT_MIN_BID_VALUE.apply(info.getCpaTargetMinBid(), ocpxTarget))")
    @Mapping(target = "cpaTargetMaxBid", expression = "java(CONVERT_MAX_BID_VALUE.apply(info.getCpaTargetMaxBid(), ocpxTarget))")
    @Mapping(target = "ninetyPercentBid", expression = "java(CONVERT_BID_SECTION_VALUE.apply(info.getNinetyPercentBid(), ocpxTarget))")
    @Mapping(target = "fiftyPercentBid", expression = "java(CONVERT_BID_SECTION_VALUE.apply(info.getFiftyPercentBid(), ocpxTarget))")
    @Mapping(target = "thirtyPercentBid", expression = "java(CONVERT_BID_SECTION_VALUE.apply(info.getThirtyPercentBid(), ocpxTarget))")
    SanlianCpaTargetMinBidConfigBo convertRpcInfo2Bo(CpaTargetBidConfigInfo info, Integer ocpxTarget);

    @Mapping(target = "supportTaoTianGoodsId",
            source = "supportTaoTianGoodsIdValue")
    @Mapping(target = "supportAppPackageId",
            source = "supportAppPackageIdValue")
    @Mapping(target = "supportAppStoreDirectLaunch",
            source = "supportAppStoreDirectLaunchValue")
    @Mapping(target = "supportGameBaseId",
            source = "supportGameBaseIdValue")
    @Mapping(target = "supportLiveRoomId",
            source = "supportLiveRoomIdValue")
    @Mapping(target = "supportUpMid",
            source = "supportUpMidValue")
    @Mapping(target = "supportSuggestBid",
            source = "supportSuggestBidValue")
    @Mapping(target = "supportBiliGame",
            source = "supportBiliGameValue")
    @Mapping(target = "baseCpaTargetList", source = "baseCpaTarget")
    @Mapping(target = "supportShopGoodsId",
            source = "supportShopGoodsIdValue")
    @Mapping(target = "supportWxMiniGame",
            source = "supportWxMiniGameValue")
    SanlianGetUnitContentRespBo convertRpcInfo2Bo(UnitContentConfigInfo info);

    @Mapping(target = "desc", expression = "java(CONVERT_ACCESSIBILITY_DESC.apply(num))")
    @Mapping(target = "code", expression = "java(new Integer(num))")
    SanlianAccessibilityBo convertNum2AccessibilityBo(Integer num);

    @Mapping(target = "otherAreaShowFlag",
            source = "other_area_show_flag")
    SanlianUnitTargetItemBo convertTargetTreeDto2ItemBo(TargetTreeDto treeDto);

    BiFunction<Integer, Integer, String> CONVERT_MIN_BID_VALUE = (minBid, ocpxTarget) -> {
        if (!Utils.isPositive(minBid)) {
            return "0";
        }
        if (SUPPORTED_CPA_TARGETS.contains(ocpxTarget)) {
            return BigDecimal.valueOf(minBid.longValue()).divide(BigDecimal.valueOf(10000L), 4, RoundingMode.HALF_UP).toPlainString();
        }
        return BigDecimal.valueOf(minBid.longValue()).divide(BigDecimal.valueOf(100L), 2, RoundingMode.HALF_UP).toPlainString();
    };

    BiFunction<Integer, Integer, String> CONVERT_MAX_BID_VALUE = (maxBid, ocpxTarget) -> {
        BigDecimal divide ;
        int scale = 2;
        if (OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(ocpxTarget)) {
            divide = new BigDecimal(10000L);
            scale = 4;
        } else {
            divide = new BigDecimal(100L);
        }
        return Utils.isPositive(maxBid) ?
                BigDecimal.valueOf(maxBid.longValue()).divide(divide, scale, RoundingMode.HALF_UP).toPlainString() :
                BigDecimal.valueOf(Integer.MAX_VALUE).divide(divide, scale, RoundingMode.HALF_UP).toPlainString();
    };

    BiFunction<Integer, Integer, String> CONVERT_BID_SECTION_VALUE = (bidSection, ocpxTarget) -> {
        BigDecimal divide ;
        int scale = 2;
        if (OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(ocpxTarget)) {
            divide = new BigDecimal(10000L);
            scale = 4;
        } else {
            divide = new BigDecimal(100L);
        }
        return Utils.isPositive(bidSection) ?
                BigDecimal.valueOf(bidSection.longValue()).divide(divide, scale, RoundingMode.HALF_UP).toPlainString() :
                "";
    };

    Function<Integer, String> CONVERT_BASE_CPA_TARGET_NAME =
            num -> SanlianBaseCpaTargetEnum.getByCode(num).getName();

    Function<Integer, String> CONVERT_ACCESSIBILITY_DESC =
            num -> SanlianAccessibilityTypeEnum.getByCode(num).getName();
}
