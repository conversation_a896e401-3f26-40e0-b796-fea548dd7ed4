package com.bilibili.adp.advertiser.task.job;

import com.bilibili.adp.cpc.biz.services.campaign.CpcCampaignService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
@JobHandler("RefreshCampaignNextdayBudgetJob")
public class RefreshCampaignNextdayBudgetJob extends IJobHandler {

    private final Logger JOB_LOGGER = LoggerFactory.getLogger(getClass());

    @Autowired
    private CpcCampaignService cpcCampaignService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        JOB_LOGGER.info("********  start job: RefreshCampaignNextdayBudgetJob....  ******");

        // 指定的计划 ids
        List<Integer> campaignIds = new ArrayList<>();
        if (!StringUtils.isEmpty(s)) {
            campaignIds = Arrays.stream(s.split(",")).map(t -> Integer.parseInt(t)).collect(Collectors.toList());
        }

        cpcCampaignService.refreshCampaignNextdayBudget(campaignIds);

        JOB_LOGGER.info("********  finish job: RefreshCampaignNextdayBudgetJob!!!  ******\n\n");
        return SUCCESS;
    }
}
