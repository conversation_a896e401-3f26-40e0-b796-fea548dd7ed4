package com.bilibili.adp.advertiser.portal.webapi.middleground;

import cn.hutool.json.JSONUtil;
import com.bapis.ad.cmc.up.GetReserveSidByAvidReq;
import com.bapis.ad.cmc.up.GetReserveSidByAvidResp;
import com.bapis.ad.cmc.up.UpInfoServiceGrpc;
import com.bapis.ad.component.*;
import com.bapis.ad.pandora.core.list.*;
import com.bapis.ad.pandora.core.v6.*;
import com.bapis.ad.pandora.resource.GetUnitBidConfigReq;
import com.bapis.ad.pandora.resource.GetUnitBidConfigResp;
import com.bapis.ad.pandora.resource.UnitResourceServiceGrpc;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.ICrmCompanyGroupService;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.advertiser.portal.common.*;
import com.bilibili.adp.advertiser.portal.converter.UnitKeywordConverter;
import com.bilibili.adp.advertiser.portal.service.openapi.OpenApiFlyValidator;
import com.bilibili.adp.advertiser.portal.service.unit.TargetLowestBidProc;
import com.bilibili.adp.advertiser.portal.service.unit.UnitOcpxTargetComponent;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.FlyProCampaignController;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.conveter.FlyCampaignConverter;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.conveter.FlyUnitConverter;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.AccountLaunchTargetVo;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.DynamicVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.CpcResourceContorller;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.CpcUnitContorller;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.common.UpdateStatusRequestBean;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.*;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.converter.UnitConverter;
import com.bilibili.adp.advertiser.portal.webapi.launch.unit.vo.UnitListInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.unit.vo.UnitRemainingVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.unit.vo.accelerate.*;
import com.bilibili.adp.advertiser.portal.webapi.middleground.converter.MiddleUnitConverter;
import com.bilibili.adp.advertiser.portal.webapi.middleground.service.MiddleArchiveService;
import com.bilibili.adp.advertiser.portal.webapi.middleground.vo.cpc.*;
import com.bilibili.adp.advertiser.portal.webapi.middleground.vo.fly.MidFlyVideoVo;
import com.bilibili.adp.advertiser.portal.webapi.middleground.vo.fly.VideoTypeEnum;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mapper.ResourceUnitMapper;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.unit.bos.SanlianCpaTargetMinBidConfigBo;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.LaunchVideoType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.fly.FlyIsSetInviteLinkEnum;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.goods.GoodsArchiveHintProc;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.services.account.LaunchAccountV1Service;
import com.bilibili.adp.cpc.biz.services.campaign.CpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.MiddleFlyCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.creative.FlyCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.config.AccLabelConfig;
import com.bilibili.adp.cpc.biz.services.dynamic.FlyDynamicService;
import com.bilibili.adp.cpc.biz.services.dynamic.GoodsArchiveDynamicProc;
import com.bilibili.adp.cpc.biz.services.goods.PromotionPurposeDescProc;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveReserveService;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveRoomService;
import com.bilibili.adp.cpc.biz.services.live.LiveReserveValidator;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveReservationInfoBo;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveRoomInfoBo;
import com.bilibili.adp.cpc.biz.services.recommend.bos.AdStatSearchWordBo;
import com.bilibili.adp.cpc.biz.services.search_ad_unit.EffectAdSearchAdUnitService;
import com.bilibili.adp.cpc.biz.services.search_ad_unit.dto.UnitKeywordsBo;
import com.bilibili.adp.cpc.biz.services.target_package.ResTargetItemService;
import com.bilibili.adp.cpc.biz.services.unit.*;
import com.bilibili.adp.cpc.biz.services.unit.bos.*;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.ParseAreaDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.UnitPriceListDto;
import com.bilibili.adp.cpc.biz.validator.CpcUnitValidator;
import com.bilibili.adp.cpc.converter.FlyInvitationConverter;
import com.bilibili.adp.cpc.core.LaunchUnitAssistSearchService;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitExtraPo;
import com.bilibili.adp.cpc.dto.*;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.YesOrNoEnum;
import com.bilibili.adp.cpc.enums.ad.CampaignAdType;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.target_package.AreaTypeEnum;
import com.bilibili.adp.launch.api.ad.dto.AdvertiserAvidMapingDto;
import com.bilibili.adp.launch.api.common.UnitStatus;
import com.bilibili.adp.launch.api.common.UnitUpdateBidPriceValueTypeEnum;
import com.bilibili.adp.launch.api.creative.dto.FlyInvitationDto;
import com.bilibili.adp.launch.api.flyPro.dto.v2.AccountLaunchTargetDto;
import com.bilibili.adp.launch.api.launch.dto.GetBidCostParam;
import com.bilibili.adp.launch.api.unit.dto.UnitUpdateBidPriceQueryDto;
import com.bilibili.adp.launch.biz.service.AdvertiserAvidMappingService;
import com.bilibili.adp.launch.biz.service.account.LaunchAccountGroupService;
import com.bilibili.adp.launch.biz.service.unit.LaunchUnitLowestBidService;
import com.bilibili.adp.launch.biz.service.unit.LaunchUnitService;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.api.dto.UserInfoDto;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.adp.passport.biz.common.ArchiveState;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.passport.biz.manager.bean.LiveBroadcastRoomInfo;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.QueryTemplateLaunchTypeMappingDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupTemplateMappingDto;
import com.bilibili.adp.resource.api.target_lau.dto.TargetTreeDto;
import com.bilibili.adp.resource.api.targetmeta.TargetType;
import com.bilibili.adp.v6.account.AccountV6Service;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.google.common.base.Joiner;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import pleiades.venus.starter.rpc.client.RPCClient;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.bapis.ad.crm.account.ListAccountIdsByTypeReq.Type.SAME_CUSTOMER_SAME_PRODUCT_VALUE;
import static com.bilibili.adp.common.enums.SalesType.PLATFORM_SALES_TYPES;
import static com.bilibili.adp.cpc.constants.AccountLabelConstants.oldSanlianAllowCreate;
import static com.bilibili.adp.cpc.constants.AccountLabelConstants.oldSanlianAllowEdit;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_TM;


@RestController
@RequestMapping("/web_api/v1/middle/unit")
@Api(value = "/middle/unit", description = "广告中台【单元】")
@Slf4j
@RequiredArgsConstructor
public class MiddleUnitController extends BasicController {

    @Autowired
    private FlyCampaignConverter flyCampaignConverter;
    @Autowired
    private FlyProCampaignController flyProCampaignController;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private ICrmCompanyGroupService crmCompanyGroupService;
    @Autowired
    private CpcResourceContorller cpcResourceContorller;
    @Autowired
    private AdvertiserAvidMappingService advertiserAvidMappingService;
    @Resource
    private FlyDynamicService flyDynamicService;
    @Autowired
    private CpcUnitContorller cpcUnitController;

    @RPCClient("sycpb.platform.cpm-pandora")
    private UnitCreativeServiceGrpc.UnitCreativeServiceBlockingStub unitCreativeServiceBlockingStub;

    @Autowired
    private CpcUnitService cpcUnitService;
    @Autowired
    private CpcCampaignService cpcCampaignService;
    @Autowired
    private PromotionPurposeDescProc promotionPurposeDescProc;
    @Autowired
    private FlyUnitConverter flyUnitConverter;
    @Autowired
    private UnitConverter converter;
    @Autowired
    private TargetLowestBidProc targetLowestBidProc;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private FlyUnitService flyUnitService;
    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private MiddleFlyUnitService middleFlyUnitService;
    @Autowired
    private MiddleArchiveService middleArchiveService;
    @Autowired
    private ResTargetItemService resTargetItemService;
    @Autowired
    private MiddleFlyCampaignService middleFlyCampaignService;
    @Autowired
    private MiddleUnitConverter middleUnitConverter;

    @Autowired
    private LaunchAccountGroupService accountGroupService;
    @Autowired
    private LaunchUnitLowestBidService lowestBidService;
    @Autowired
    private AdpCpcUnitService adpCpcUnitService;
    @Resource
    private DepthOptDataService depthOptDataService;
    @Autowired
    private IResSlotGroupService resSlotGroupService;
    @Autowired
    private AdpCpcLiveReserveService adpCpcLiveReserveService;
    @Resource
    private LiveReserveValidator liveReserveValidator;
    @Autowired
    private AdpCpcLiveRoomService adpCpcLiveRoomService;
    @Autowired
    private IPassportService passportService;

    @Autowired
    private UnitOcpxTargetComponent unitOcpxTargetComponent;

    @Autowired
    private FlyCreativeService flyCreativeService;
    @Autowired
    private GoodsArchiveHintProc goodsArchiveHintProc;
    @Autowired
    private OpenApiFlyValidator openApiFlyValidator;
    @Value("${middle.ground.label.id:342}")
    private Integer middleGroundLabelId;
    @Autowired
    private EffectAdSearchAdUnitService effectAdSearchAdUnitService;
    @Autowired
    private LaunchUnitAssistSearchService launchUnitAssistSearchService;
    @Autowired
    private AccLabelConfig accLabelConfig;
    @Autowired
    private CpcUnitValidator cpcUnitValidator;
    @Autowired
    private UnitAccelerateService unitAccelerateService;
    @Autowired
    private CpcUnitServiceDelegate cpcUnitServiceDelegate;
    @Autowired
    private LauUnitExtraService unitExtraService;
    private final LaunchUnitV1Service launchUnitV1Service;
    private final GoodsArchiveDynamicProc goodsDynamicProc;
    @Autowired
    private LaunchAccountV1Service launchAccountV1Service;
    @Autowired
    private UnitArchiveService unitArchiveService;
    @Value("${old.sanlian.allow.create.switch:0}")
    private Integer oldSanlianAllowCreateSwitch;
    @RPCClient(value = "sycpb.cpm.tavern-platform")
    private UpInfoServiceGrpc.UpInfoServiceBlockingStub upInfoServiceBlockingStub;
    @RPCClient("sycpb.platform.cpm-pandora")
    private UnitResourceServiceGrpc.UnitResourceServiceBlockingStub unitResourceServiceBlockingStub;

    @RPCClient("sycpb.platform.cpm-pandora")
    private ListServiceGrpc.ListServiceBlockingStub listServiceBlockingStub;

    @Autowired
    private final CommentComponentServiceGrpc.CommentComponentServiceBlockingStub commentComponentServiceBlockingStub;

    private final AccountV6Service accountV6Service;

    @ApiOperation(value = "【起飞】账号可选投放目标")
    @RequestMapping(value = "/fly/account/launch_target", method = RequestMethod.GET)
    public Response<List<AccountLaunchTargetVo>> launchTarget(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id") Integer campaignId) throws ServiceException {
        CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(campaignId);
        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        Assert.notNull(accountBaseDto, "账号信息不存在");

        if (accountBaseDto.getIsSupportContent() > 0) {
            // https://www.tapd.cn/********/prong/stories/view/11********004159242
            final List<AccountLaunchTargetVo> vos = new ArrayList<>();
            if (PromotionPurposeType.isBrandSpread(campaign.getPromotionPurposeType())) {
                vos.add(new AccountLaunchTargetVo(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), "稿件"));
                vos.add(new AccountLaunchTargetVo(PromotionPurposeType.DYNAMIC.getCode(), "动态"));
            } else if (PromotionPurposeType.isEnterprisePromotion(campaign.getPromotionPurposeType())) {
                vos.add(new AccountLaunchTargetVo(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), "稿件"));
            } else if (PromotionPurposeType.isLiveRoom(campaign.getPromotionPurposeType())) {
                vos.add(new AccountLaunchTargetVo(PromotionPurposeType.LIVE_ROOM.getCode(), "直播中"));
            }
            return Response.SUCCESS(vos);
        }

        // 品牌传播
        if (campaign.getPromotionPurposeType() == PromotionPurposeType.BRAND_SPREAD.getCode()) {
            List<AccountLaunchTargetDto> dtos = middleFlyCampaignService.getFlyAccountLaunchTarget(context.getAccountId());
            return Response.SUCCESS(flyCampaignConverter.accountLaunchTargetDtos2Vos(dtos));
        }
        // 账号推广
        if (campaign.getPromotionPurposeType() == PromotionPurposeType.ENTERPRISE_PROMOTION.getCode()) {
            List<AccountLaunchTargetDto> dtos = middleFlyCampaignService.getFlyEnterpriseAccountLaunchTarget(context.getAccountId(), campaign.getAdType());
            return Response.SUCCESS(flyCampaignConverter.accountLaunchTargetDtos2Vos(dtos));
        }
        return Response.SUCCESS(Collections.emptyList());
    }

    @Deprecated
    @ApiOperation(value = "账号可选稿件【带货三联】")
    @RequestMapping(value = "/fly/account/videos", method = RequestMethod.GET)
    public Response<Pagination<List<MidFlyVideoVo>>> getUpVideos(
            @ApiIgnore Context context,
            @ApiParam("计划id") @RequestParam(value = "campaign_id", required = false) Integer campaignId,
            @ApiParam("类型 1-bilibili视频（非商单） 2-商单视频") @RequestParam(value = "type", required = true) Integer type,
            @ApiParam("key word") @RequestParam(value = "kw", required = false) String kw,
            @ApiParam("页号") @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(value = "page_size", required = false, defaultValue = "20") Integer pageSize) throws ServiceException, ExecutionException {

        Assert.isTrue(VideoTypeEnum.getByCode(type) != null, "type传值错误");
        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        Assert.notNull(accountBaseDto, "当前用户不存在");

        // 稿件账号标签的账号才能获取稿件列表
        Assert.isTrue(accountGroupService.isAccountWithLabel(accountBaseDto.getAccountId(), middleGroundLabelId),
                "账号没有稿件相关权限");
        log.info("accountBaseDto.accountId ---> {}", accountBaseDto.getAccountId());

        // 稿件状态列表: 开放浏览,橙色通过,修复待审,会员可见,延迟发布,UP主定时发布
        List<Integer> archiveStates = Arrays.asList(ArchiveState.OPEN_BROWSE.getCode(), ArchiveState.ORANGE_THROUGH.getCode(),
                ArchiveState.REPAIR_PENDING.getCode(), ArchiveState.VIP_ACCESS.getCode(), ArchiveState.DELAY_RELEASE.getCode(), ArchiveState.UP_TIMED_RELEASE.getCode());
        kw = kw == null ? null : kw.trim();

        // 账号的商业稿件 map<avid, 稿件>
        Map<Long, ArchiveDetail> resultArchiveMap = new HashMap<>();
        // 稿件投放类型
        Map<Long, Integer> avIdSourceMap = new HashMap<>();

        // 用来存放投放账户一个公司组的账户 ids
        List<Integer> accountIds = new ArrayList<>();
        accountIds.add(accountBaseDto.getAccountId());

        // 根据账号 id 获取一个公司组下的所有的账户 ids(crm_company_account_mapping)
        List<Integer> ids = crmCompanyGroupService.getAccountIdsWithSameCompanyGroupByAccountId(accountBaseDto.getAccountId());
        accountIds.addAll(ids);
        accountIds = accountIds.stream().distinct().collect(Collectors.toList());

        log.info("with same company group account size is {}", CollectionHelper.getSize(ids));
        // 获取一批账号的商业稿件
        cpcResourceContorller.getAndPutBusinessVideos(kw, accountIds, archiveStates, resultArchiveMap);

        // 继续填充获取相同客户id和产品id的账号的商业稿件
        fillCustomerIdsAndVideosIfNecessary(kw, accountBaseDto, archiveStates, resultArchiveMap);
        // 设置为商业内容
        resultArchiveMap.forEach((k, v) -> avIdSourceMap.put(k, LaunchVideoType.BUSINESS.getCode()));

        // 获取一批账号绑定的视频信息(广告主-视频关系绑定表)
        List<AdvertiserAvidMapingDto> dtos = advertiserAvidMappingService.getAdvertiserAvIdMappingsByAccountIds(accountIds);
        log.info("AdvertiserAvidMapingDto list.size is {}", CollectionHelper.getSize(dtos));

        if (!CollectionUtils.isEmpty(dtos)) {
            dtos.forEach(dto -> {
                // 广告主-视频关系绑定表的稿件添加到稿件 map
                resultArchiveMap.put(dto.getAvid(), dto.getArchiveDetail());
                // 广告主-视频关系绑定表的稿件类型设置为其他
                avIdSourceMap.put(dto.getAvid(), LaunchVideoType.OTHER.getCode());
            });
        }
        // 稿件列表内存分页
        Pagination<List<MidFlyVideoVo>> result = middleArchiveService.filterBusiness(resultArchiveMap, kw, archiveStates, avIdSourceMap,
                pageNum, pageSize, VideoTypeEnum.getByCode(type), context.getAccountId());
        // 拼接带货信息
        middleUnitConverter.decorateVideoWithGoodsSupportSuit(campaignId, context.getAccountId(), result.getData());
        return Response.SUCCESS(result);
    }

    /**
     * 获取相同客户id和产品id的账号的商业稿件
     *
     * @param keyWord
     * @param accountInfo
     * @param archiveStates
     * @param resultArchiveMap
     * @throws ServiceException
     */
    private void fillCustomerIdsAndVideosIfNecessary(String keyWord, AccountBaseDto accountInfo,
                                                     List<Integer> archiveStates,
                                                     Map<Long, ArchiveDetail> resultArchiveMap) throws ServiceException {

        // 获取相同客户id和产品id的账号
        //List<Integer> accountIds = crmCompanyGroupService.getAccountIdsByAccountCustomerIdAndProductId(accountInfo.getAccountId());
        List<Integer> accountIds = accountV6Service.listAccountIdsByType(accountInfo.getAccountId(), SAME_CUSTOMER_SAME_PRODUCT_VALUE);
        if (!CollectionUtils.isEmpty(accountIds)) {
            cpcResourceContorller.getAndPutBusinessVideos(keyWord, accountIds, archiveStates, resultArchiveMap);
        }
    }


    @ApiOperation(value = "账号可选动态(20条)")
    @RequestMapping(value = "/account/dynamics", method = RequestMethod.GET)
    public Response<List<DynamicVo>> accountDynamics(
            @ApiIgnore Context context,
            @RequestParam(value = "kw", required = false) @ApiParam("key word") String kw) throws ServiceException {

        List<DynamicVo> resultDynamicList = flyProCampaignController.accountDynamics(context, kw).getResult();
        processHasCommentComponent(resultDynamicList);

        return Response.SUCCESS(resultDynamicList);
    }

    @ApiOperation(value = "获取 mid 下动态列表")
    @RequestMapping(value = "/mid/dynamics", method = RequestMethod.GET)
    public Response<Pagination<List<DynamicVo>>> midDynamics(@ApiIgnore Context context,
                                                             @RequestParam(value = "mid") @ApiParam("mid") Long mid,
                                                             @RequestParam(value = "keywords", required = false) @ApiParam("keywords") String keywords,
                                                             @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                             @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize) throws ServiceException {
        MidDynamicQueryDto requestDto = MidDynamicQueryDto.builder()
                .accountId(context.getAccountId())
                .mid(mid)
                .keywords(keywords)
                .needGoodsContent(true)
                .page(page)
                .pageSize(pageSize)
                .build();
        PageResult<NewDynamicDto> dynamicDtoPageResult = flyDynamicService.midDynamics(requestDto);
        List<DynamicVo> dynamicVos = DynamicVo.convertNewDynamicDtos2Vos(dynamicDtoPageResult.getRecords());
        processHasCommentComponent(dynamicVos);
        return Response.SUCCESS(new Pagination<>(page, dynamicDtoPageResult.getTotal(), dynamicVos));
    }

    @ApiOperation(value = "根据动态id获取动态信息")
    @RequestMapping(value = "/dynamicById", method = RequestMethod.GET)
    public Response<DynamicVo> dynamicById(@ApiIgnore Context context,
                                           @RequestParam(value = "dynamic_id") @ApiParam("dynamic_id") Long dynamicId) throws ServiceException {
        NewDynamicDto dynamicDto = flyDynamicService.fetchDynamicInfo(dynamicId);
        DynamicVo dynamicVo = DynamicVo.convertNewDynamicDto2Vo(dynamicDto);
        return Response.SUCCESS(dynamicVo);
    }

    /**
     * 获取稿件的 mid
     *
     * @param aid
     * @return
     * @throws ServiceException
     */
    private Long generateMidByAid(Long aid) throws ServiceException {
        ArchiveDetail archiveDetail = archiveManager.queryArchivesByAid(aid);
        Long archiveMid = Optional.ofNullable(archiveDetail)
                .map(o -> o.getArchive())
                .map(o -> o.getMid())
                .orElse(-1L);
        return archiveMid;
    }

    @ApiOperation(value = "根据计划ID获取单元数")
    @RequestMapping(value = "/count", method = RequestMethod.GET)
    public Response<Long> getUnitCountInCampaignIds(
            @ApiIgnore Context context,
            @RequestParam("campaign_ids") List<Integer> campaignIds) throws ServiceException {
        return cpcUnitController.getUnitCountInCampaignIds(context, campaignIds);
    }

    @ApiOperation(value = "根据批量计划ID获取单元数")
    @RequestMapping(value = "/batch", method = RequestMethod.GET)
    public Response<List<DpaCampaignVo>> getCampaignInfoInCampaignIds(@ApiIgnore Context context,
                                                                      @RequestParam("campaign_ids") List<Integer> campaignIds) throws ServiceException {
        return cpcUnitController.getCampaignInfoInCampaignIds(context, campaignIds);
    }

    @ApiOperation(value = "批量修改单元")
    @RequestMapping(value = "/batch", method = RequestMethod.PUT)
    public Response<Integer> batchUpdateUnit(
            @ApiIgnore Context context,
            @RequestBody BatchUpdateUnitVo vo) throws ServiceException {
        return cpcUnitController.batchUpdateUnit(context, vo);
    }

    @ApiOperation(value = "批量修改单元")
    @RequestMapping(value = "/direct/launch", method = RequestMethod.PUT)
    public Response<Integer> updateDirectLaunch(
            @ApiIgnore Context context,
            @RequestBody UpdateCpcUnitVo vo) throws ServiceException {

        return Response.SUCCESS(cpcUnitService.updateDirectLaunch(getOperator(context), vo.getUnit_id()));
    }

    @ApiOperation(value = "分页查询单元基本信息")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public Response<Pagination<List<MiddleCpcUnitBaseVo>>> queryUnitByPage(
            @ApiIgnore Context context,
            @ApiParam(value = "计划id") @RequestParam(value = "campaign_ids", required = false) List<Integer> campaignIds,
            @ApiParam(value = "单元id") @RequestParam(value = "unit_ids", required = false) List<Integer> unitIds,
            @ApiParam(value = "单元名称") @RequestParam(value = "unit_name", required = false) String unitName,
            @ApiParam(value = "定向包id") @RequestParam(value = "target_package_id", required = false) Integer targetPackageId,
            @ApiParam(value = "分页") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @ApiParam(value = "分页大小") @RequestParam(value = "page_size", required = false, defaultValue = "20") Integer pageSize) {
        Integer accountId = context.getAccountId();
        QueryUnitBo queryUnitBo = QueryUnitBo.builder()
                .accountIds(Collections.singletonList(accountId))
                .campaignIds(campaignIds)
                .unitIds(unitIds)
                .likeUnitName(unitName)
                .targetPackageId(targetPackageId)
                .adpVersions(AdpVersion.MIDDLE_AND_MERGE_LIST)
                .salesTypes(PLATFORM_SALES_TYPES)
                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
                .orderBy(Constants.MTIME_DESC)
                .page(Page.valueOf(page, pageSize))
                .build();
        PageResult<CpcUnitDto> unitDtoPageResult = launchUnitV1Service.getUnitPage(queryUnitBo);
        List<MiddleCpcUnitBaseVo> middleCpcUnitBaseVos = converter.convertCpcUnitDtos2MiddleBaseVos(unitDtoPageResult.getRecords());
        return Response.SUCCESS(new Pagination<>(page, unitDtoPageResult.getTotal(), middleCpcUnitBaseVos));
    }

    @ApiOperation(value = "查询单元")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public Response<MiddleCpcUnitVo> queryUnit(
            @ApiIgnore Context context,
            @PathVariable("id")
            @ApiParam(required = true, value = "unitId", defaultValue = "1")
            Integer unitId) throws ServiceException {
        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        Assert.notNull(accountBaseDto, "当前用户不存在");

        MiddleCpcUnitVo middleVo = new MiddleCpcUnitVo();
        CpcUnitVo cpcUnitVo = cpcUnitController.queryUnit(context, unitId).getResult();
        CpcCampaignDto cpcCampaignDto = cpcCampaignService.loadCpcCampaignDto(cpcUnitVo.getCampaign_id());

        Integer cppt = cpcCampaignDto.getPromotionPurposeType();
        Integer uppt = cpcUnitVo.getPromotion_purpose_type();
        BeanUtils.copyProperties(cpcUnitVo, middleVo);
        // cpcUnitVo的promotion_purpose_type是uppt，cpcCampaignDto的promotionPurposeType是cppt
        middleVo.setPromotion_purpose_type(cppt);
        String promotionPurposeDesc = promotionPurposeDescProc.getPromotionPurposeDesc(cppt, accountBaseDto.getDepartmentId());
        middleVo.setPromotion_purpose_type_desc(promotionPurposeDesc);

        middleVo.setUnit_promotion_purpose_type(uppt);
        middleVo.setSub_pkg(cpcUnitVo.getSubPkg());
        // 单元上的稿件信息
        Pair<Integer, Integer> pair = Pair.of(FlyIsSetInviteLinkEnum.NOT_SUPPORT.getCode(), IsValid.FALSE.getCode());
        if (!StringUtils.isEmpty(cpcUnitVo.getVideo_id())
                && !cpcUnitVo.getVideo_id().equals("0")) {
            try {
                Long avid = BVIDUtils.bvToAv(cpcUnitVo.getVideo_id());
                pair = flyUnitService.getIsSetInviteLinkAndShowJumpBasicBubble(cpcUnitVo.getOcpc_target(), avid);
                middleVo.setVideo_id(String.valueOf(avid));
            } catch (Exception e) {
                log.error("bvid failure, bvid:{}, e:{}", cpcUnitVo.getVideo_id(), e);
            }
        }

        // 直播信息
        if (Objects.equals(uppt, PromotionPurposeType.LIVE_RESERVE.getCode()) || Objects.equals(uppt, PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
            final LiveReservationInfoBo liveReservationInfoBo = adpCpcLiveReserveService.fetchUnitLiveReserveInfo(unitId);
            middleVo.setLiveReserve(liveReservationInfoBo);
        } else if (Objects.equals(uppt, PromotionPurposeType.LIVE_ROOM.getCode()) || Objects.equals(uppt, PromotionPurposeType.GOODS_LIVE.getCode())) {
            final LiveRoomInfoBo liveRoomInfoBo = adpCpcLiveRoomService.fetchInHouseLiveInfo(cpcUnitVo.getMaterial_id());
            middleVo.setLiveRoom(liveRoomInfoBo);
        }

        // 起飞单元其他信息
        CpcUnitDto cpcUnitDto = cpcUnitService.loadCpcUnit(unitId);
        MiddleFlyUnitBo middleFlyUnitBo = middleFlyUnitService.getUnitDetail(GsonUtils.toJson(cpcUnitDto), context.getAccountId(), cpcCampaignDto.getAdType());

        middleVo.setIs_set_invite_link(pair.getFirst());
        middleVo.setShow_jump_basic_bubble(pair.getSecond());
        middleVo.setDynamic_id(middleFlyUnitBo.getDynamicId() == null ? 0L : middleFlyUnitBo.getDynamicId());
        middleVo.setDynamic_type(middleFlyUnitBo.getDynamicType() == null ? 0 : middleFlyUnitBo.getDynamicType());
        middleVo.setDynamic_content(middleFlyUnitBo.getDynamicContent() == null ? "" : middleFlyUnitBo.getDynamicContent());
        middleVo.setDynamic_link(middleFlyUnitBo.getDynamicLink() == null ? "" : middleFlyUnitBo.getDynamicLink());
        middleVo.setDynamic_skip_link(middleFlyUnitBo.getDynamicSkipLink() == null ? "" : middleFlyUnitBo.getDynamicSkipLink());
        middleVo.setActivity_link(middleFlyUnitBo.getActivityLink() == null ? "" : middleFlyUnitBo.getActivityLink());
        // 动态信息
        middleVo.setDynamic_info(middleUnitConverter.middleDto2Vo(middleFlyUnitBo, middleFlyUnitBo.getMiddleDynamicBo()));
        if (Objects.equals(PromotionPurposeType.LIVE_ROOM.getCode(), uppt)) {
            Response<LiveMateriaInfolVo> response = this.getLiveBroadcastRoomInfoById(cpcUnitVo.getMaterial_id());
            middleVo.setLive_room_title(Optional.ofNullable(response.getResult()).map(o -> o.getTitle()).orElse(""));
        }
        // 邀约组件信息?为什么单元也要有，不是创意维度的信息吗？（说是新建的时候请求了这个接口）
        List<FlyInvitationDto> FlyInvitationDtos = flyCreativeService.processFlyInvitationInfoByUnitId(unitId);
        middleVo.setFly_invitation_infos(FlyInvitationConverter.flyInvitationDtos2Vos(FlyInvitationDtos));
        // 搜索广告关键词
        UnitKeywordConverter.MAPPER.updateVo(middleVo, middleFlyUnitBo.getUnitKeywordsBo());

        // 辅助价格正常传值
        middleVo.setAssist_price(Utils.fromFenToYuan(cpcUnitDto.getAssistPrice()));
        // 这里强制处理老数据，必须展示辅助目标为应用唤起
        if (accLabelConfig.supportFlyDingdanyouhuaAssist(context.getAccountId()) &&
                OcpcTargetEnum.GOODS_TRANSACTION.getCode().equals(cpcUnitDto.getOcpcTarget())) {
            middleVo.setAssist_target(OcpcTargetEnum.LP_CALL_UP_SUCCESS.getCode());
            middleVo.setAssist_target_desc(OcpcTargetEnum.LP_CALL_UP_SUCCESS.getDesc());
        } else {
            middleVo.setAssist_target(0);
            middleVo.setAssist_target_desc("-");
        }
        LauUnitExtraPo unitExtraPo = unitExtraService.getByUnitId(unitId);
        if (!Objects.isNull(unitExtraPo)) {
            String deviceAppStore = unitExtraPo.getDeviceAppStore();
            if (!StringUtils.isEmpty(deviceAppStore)) {
                List<Integer> stores = Arrays.stream(deviceAppStore.split(",")).map(x -> {
                    Integer i = Integer.valueOf(x);
                    if (i < 0) {
                        return -i;
                    }
                    return i;
                }).collect(Collectors.toList());
                deviceAppStore = Joiner.on(",").join(stores);
            }
            middleVo.setDeviceAppStore(Optional.ofNullable(deviceAppStore).orElse(""));
        } else {
            middleVo.setDeviceAppStore("");
        }

        if (middleVo.getAdp_version() == 6) {
            GetUnitCreativesReq req = GetUnitCreativesReq.newBuilder()
                    .setUnitId(unitId)
                    .build();
            GetUnitCreativesResp resp = unitCreativeServiceBlockingStub.getUnitCreatives(req);
            if (resp != null) {
                middleVo.setGeneralVersion(resp.getUnitCreative().getUnit().getGeneralVersion());
                // 如果一个单元下的创意最大个数有变更，则需要更改这里的数字
                if (middleVo.getGeneralVersion().equals(2) && resp.getUnitCreative().getCreativeSetList().size() < 10) {
                    middleVo.setIsAbleToCreateProgrammatic(true);
                }
            }

        }
        return Response.SUCCESS(middleVo);
    }


    // targets.installed_user_filter的字段表示已安装用户类型
    // app_package_id字段表示安装包id
    @ApiOperation(value = "创建单元")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Response<Integer> createUnit(
            @ApiIgnore Context context,
            @RequestBody @Validated MiddleNewCpcUnitVo vo) throws ServiceException {
        log.info("createUnit vo:{}", JSONUtil.toJsonStr(vo));
        final int aid = context.getAccountId();
        boolean isPersonalFly = launchAccountV1Service.isPersonalFly(aid);
        // 【三连下线投放频次】
        vo.setFrequency_unit(1);
        vo.setFrequency_limit(Integer.MAX_VALUE);

        if(!isPersonalFly) {
            if (Utils.isPositive(oldSanlianAllowCreateSwitch)) {
                Assert.isTrue(accountLabelService.isAccountIdInLabel(context.getAccountId(), oldSanlianAllowCreate), "不支持新建旧版广告");
            }
        }


        CpcCampaignDto cpcCampaignDto = cpcCampaignService.loadCpcCampaignDto(vo.getCampaign_id());

        Assert.isTrue(!AdpVersion.isCpcFlyMerge(cpcCampaignDto.getAdpVersion()), "不允许使用当前接口创建新三连单元");

        openApiFlyValidator.validateCreateUnitHasUppt(context,
                cpcCampaignDto.getPromotionPurposeType(),
                vo.getUnit_promotion_purpose_type());
        // PAID_IN_24H_ROI 出价校验
        if (OcpcTargetEnum.PAID_IN_24H_ROI.getCode().equals(vo.getOcpc_target())) {
            BigDecimal bid = new BigDecimal(cpcUnitServiceDelegate.getBrandSpreadPaidIn24HROILostBid()).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
            Assert.isTrue(vo.getTwo_stage_bid().compareTo(bid) >= 0,
                    "24小时ROI转化目标下，目标转化出价必须大于等于 " + bid);
        }
        // 如果有单元独有推广目的，用单元上的，否则用计划的
        // 计划上推广目的
        PromotionPurposeType cppt = PromotionPurposeType.getByCode(cpcCampaignDto.getPromotionPurposeType());
        // 单元上的推广目的
        PromotionPurposeType uppt = null;
        if (vo.getUnit_promotion_purpose_type() != null) {
            uppt = PromotionPurposeType.getByCode(vo.getUnit_promotion_purpose_type());
        } else {
            uppt = cppt;
        }
        // 稿件校验
        boolean needTryInitAssistData = this.archiveCheck(cppt, uppt, context, vo);
        // 账号推广校验
        this.enterprisePromotionCheck(cppt, uppt);
        // 安卓游戏校验
        this.androidGameCheck(cppt, context, vo);
        // 设置默认地域定向
        this.setDefaultAreaTypeIfNecessary(vo);

        // 起飞原生落地页，20240410，排除图文动态
        if (!PromotionPurposeType.DYNAMIC.equals(uppt) && !PromotionPurposeType.OGV.equals(uppt) && LaunchUnitService.isFlyNativeLandingPage(vo.getSales_type(), vo.getOcpc_target())) {
            final CpcUnitTargetVo targets = vo.getTargets();
            if (Objects.nonNull(targets) && Objects.nonNull(targets.getIntelligentMass())) {
                Assert.isTrue(CollectionUtils.isEmpty(targets.getIntelligentMass().getTargets()) && CollectionUtils.isEmpty(targets.getIntelligentMass().getExtraCrowdPackIds()), "起飞原生落地页暂时不支持智能放量");
            }
            // 起飞没有一阶段出价, 默认给一个5块
            vo.setCost_price(BigDecimal.valueOf(5));
            uppt = PromotionPurposeType.ARCHIVE_CONTENT;
        }

        boolean isArcCpc = uppt == PromotionPurposeType.ARCHIVE_CONTENT && Objects.equals(vo.getSales_type(), SalesType.CPC.getCode());
        Assert.isTrue(!isArcCpc, "该版本广告单元已不再支持新建或复制");

        // 直播间的预约id校验
        LiveReservationInfoBo liveReservationInfoBo = null;

        // 必选计划层级直播推广
        if (Objects.equals(PromotionPurposeType.LIVE_ROOM, cppt)) {
            // 计划上推广目的是直播, 看单元
            if (Utils.isPositive(vo.getSid())) {
                // 给预约id，单元层级保存为直播预约
                uppt = PromotionPurposeType.LIVE_RESERVE;
                liveReservationInfoBo = adpCpcLiveReserveService.queryBiliLiveReserveInfo(vo.getSid());
                liveReserveValidator.validateLiveReserve(liveReservationInfoBo, vo.getBegin_date(), vo.getEnd_date(), vo.getSales_type(), null, null, null);
            } else if (StringUtils.hasText(vo.getMaterial_id())) {
                // 给直播间id，单元层级保存为直播间
                uppt = PromotionPurposeType.LIVE_ROOM;
            } else {
                throw new ServiceRuntimeException("直播推广必须指定直播间id/预约id");
            }
        }
        // 品牌传播下的动态
        Long sidOfDynamic = 0L;
        if (Objects.equals(PromotionPurposeType.BRAND_SPREAD, cppt) && Objects.equals(PromotionPurposeType.DYNAMIC, uppt)) {
            if (Utils.isPositive(vo.getDynamic_id())) {
                NewDynamicDto newDynamicDto = flyDynamicService.fetchDynamicInfo(vo.getDynamic_id());
                if (newDynamicDto != null && Utils.isPositive(newDynamicDto.getSid())) {
                    sidOfDynamic = newDynamicDto.getSid();
                    liveReservationInfoBo = adpCpcLiveReserveService.queryBiliLiveReserveInfo(newDynamicDto.getSid());
                    liveReserveValidator.validateLiveReserveForDynamic(liveReservationInfoBo, vo.getBegin_date(), vo.getEnd_date(),
                            null, null, null);
                }

            }
        }
        // 品牌传播下的稿件
        if (Objects.equals(PromotionPurposeType.BRAND_SPREAD, cppt) && Objects.equals(PromotionPurposeType.ARCHIVE_CONTENT, uppt) && OcpcTargetEnum.LIVE_RESERVE.getCode().equals(vo.getOcpc_target())) {
            Long sid = vo.getSid();
            if (!Utils.isPositive(sid)) {
                GetReserveSidByAvidResp reserveSidsByAvids = upInfoServiceBlockingStub.getReserveSidsByAvids(GetReserveSidByAvidReq.newBuilder().addAvids(Long.parseLong(vo.getVideo_id())).build());
                if (reserveSidsByAvids != null && !CollectionUtils.isEmpty(reserveSidsByAvids.getReserveInfosList())) {
                    sid = reserveSidsByAvids.getReserveInfos(0).getSid();
                }
            }

            if (Utils.isPositive(sid)) {
                liveReservationInfoBo = adpCpcLiveReserveService.queryBiliLiveReserveInfo(sid);
                liveReserveValidator.validateLiveReserveForDynamic(liveReservationInfoBo, vo.getBegin_date(), vo.getEnd_date(), null, null, null);
            }
        }

        Integer negTermKeywordsCount = vo.getSearchAdNegativeTermKeywords() == null ? 0 : vo.getSearchAdNegativeTermKeywords().size();
        Integer negPreciseKeywordsCount = vo.getSearchAdNegativePreciseKeywords() == null ? 0 : vo.getSearchAdNegativePreciseKeywords().size();
        Assert.isTrue(negTermKeywordsCount + negPreciseKeywordsCount <= 500, "最多支持500个否词");
        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        NewCpcUnitVo newCpcUnitVo = new NewCpcUnitVo();
        BeanUtils.copyProperties(vo, newCpcUnitVo);
        //
        newCpcUnitVo.setIs_middle_ad(1);

        // 稿件内容情况 mid 处理
        newCpcUnitVo.setMid(this.generateUnitArchiveMid(accountBaseDto, vo));
        // 如果有单元独有推广目的，用单元上的，否则用计划的
        newCpcUnitVo.setUnit_promotion_purpose_type(uppt.getCode());
        //是否`所有广告`搜索词单元
        buildCreateAllAdSearchUnit(newCpcUnitVo, vo, cpcCampaignDto);
        // 创建单元
        log.info("cpcUnitController createUnit:{}", JSONUtil.toJsonStr(newCpcUnitVo));
        Integer unitId = cpcUnitController.createUnit(context, newCpcUnitVo).getResult();

        // 直播预约信息保存
        if (Objects.nonNull(liveReservationInfoBo)) {
            // 保存直播预约信息到本地
            adpCpcLiveReserveService.insertOrUpdateReserveInfo(liveReservationInfoBo);
            // 保存直播预约和单元的绑定关系
            adpCpcLiveReserveService.saveUnitReserve(unitId, liveReservationInfoBo.getSid());
        }

        // 保存中台起飞单元信息(动态和活动的才需要)
        middleFlyUnitService.insertLauUnitFlyMiddleInfo(MiddleFlyUnitCreateDto.builder()
                .accountId(context.getAccountId())
                .unitId(unitId)
                .promotionPurposeType(uppt.getCode())
                .dynamicType(vo.getDynamic_type())
                .dynamicId(vo.getDynamic_id())
                .dynamicLink(vo.getDynamic_link())
                .sid(sidOfDynamic)
                .activityLink(vo.getActivity_link())
                .build());
        // 所有广告类型 保存关键
        flyDynamicService.putDynamic2Redis(vo.getDynamic_id());
        // 搜索广告，保存关键词
        if (CampaignAdType.SEARCH.getCode().equals(cpcCampaignDto.getAdType()) || CampaignAdType.ALL.getCode().equals(cpcCampaignDto.getAdType())) {
            if (!CollectionUtils.isEmpty(vo.getSearch_ad_keywords_tag())) {
                // 前端传 search_ad_keywords_tag，为了适配以前的逻辑，需要对 search_ad_keywords进行赋值
                vo.setSearch_ad_keywords(vo.getSearch_ad_keywords_tag().stream()
                        .map(AdStatSearchWordBo::getKeyword).collect(Collectors.toList()));

                vo.getSearch_ad_keywords_tag().forEach(x -> {
                    x.setUnitId(unitId);
                    x.setAccountId(context.getAccountId());
                });
            } else if (!CollectionUtils.isEmpty(vo.getSearch_ad_keywords())) {
                // search_ad_keywords_tag 为空时，使用 search_ad_keywords 为 search_ad_keywords_tag 赋值
                vo.setSearch_ad_keywords_tag(vo.getSearch_ad_keywords().stream()
                        .map(x -> AdStatSearchWordBo.builder().keyword(x)
                                .unitId(unitId)
                                .accountId(context.getAccountId()).build())
                        .collect(Collectors.toList()));
            }
            UnitKeywordsBo unitKeywordsBo = UnitKeywordConverter.MAPPER.fromVo(vo);
            unitKeywordsBo.setAccountId(context.getAccountId());
            unitKeywordsBo.setOperateType(0);
            unitKeywordsBo.setAdType(cpcCampaignDto.getAdType());
            effectAdSearchAdUnitService.save(unitId, unitKeywordsBo);
        }

        // 辅助出价数据保存
        if (needTryInitAssistData) {
            launchUnitAssistSearchService.insertInitDataIfNotExist(unitId);
        }

        if (isPersonalFly && StringUtils.hasText(vo.getVideo_id()) && Utils.isPositive(vo.getSeason_id())) {
            unitArchiveService.updateSeasonId(unitId, vo.getSeason_id());
        }
        return Response.SUCCESS(unitId);
    }

    /**
     * 辅助出价校验
     *
     * @param cppt
     * @param uppt
     * @param context
     * @param vo
     * @return
     */
    private boolean archiveCheck(PromotionPurposeType cppt, PromotionPurposeType uppt, Context context, MiddleNewCpcUnitVo vo) {
        boolean needTryInitAssistData = false;
        // 计划是品牌传播
        if (PromotionPurposeType.BRAND_SPREAD.equals(cppt)) {
            // 计划层级品牌传播，单元层级推广内容范围
            Assert.isTrue(PromotionPurposeType.BRAND_SPREAD_SUPPORT_UNIT_PPT_SET.contains(uppt), "unit_promotion_purpose_type传参错误");
            // 单元是投稿内容
            if (PromotionPurposeType.ARCHIVE_CONTENT.equals(uppt)) {
                goodsArchiveHintProc.checkAvidSupportGoodsOcpcTarget(context.getAccountId(), vo.getVideo_id(),
                        vo.getOcpc_target());
                // 如果是订单提交优化目标，需要校验2个assist属性
                if (accLabelConfig.supportFlyDingdanyouhuaAssist(context.getAccountId())
                        && ObjectUtils.equals(OcpcTargetEnum.GOODS_TRANSACTION.getCode(), vo.getOcpc_target())) {
                    goodsArchiveHintProc.checkAssistTargetAndAssistPrice(vo.getAssist_target(), vo.getAssist_price());
                    needTryInitAssistData = true;
                } else {
                    // 如果不是，给这2个属性默认值
                    vo.setAssist_target(0);
                    vo.setAssist_price(BigDecimal.ZERO);
                }
            }
            if (PromotionPurposeType.DYNAMIC.equals(uppt)) {
                // 如果是订单提交优化目标 & cid动态，需要校验2个assist属性
                if (accLabelConfig.supportFlyDingdanyouhuaAssist(context.getAccountId())
                        && ObjectUtils.equals(OcpcTargetEnum.GOODS_TRANSACTION.getCode(), vo.getOcpc_target()) && goodsDynamicProc.isCidDynamic(vo.getDynamic_id())) {
                    goodsArchiveHintProc.checkAssistTargetAndAssistPrice(vo.getAssist_target(), vo.getAssist_price());
                    needTryInitAssistData = true;
                } else {
                    // 如果不是，给这2个属性默认值
                    vo.setAssist_target(0);
                    vo.setAssist_price(BigDecimal.ZERO);
                }
            }
        }
        return needTryInitAssistData;
    }


    /**
     * 计划账号推广，单元层级推广目的范围
     *
     * @param cppt
     * @param uppt
     */
    private void enterprisePromotionCheck(PromotionPurposeType cppt, PromotionPurposeType uppt) {
        if (PromotionPurposeType.ENTERPRISE_PROMOTION.equals(cppt)) {
            Assert.isTrue(PromotionPurposeType.ENTERPRISE_PROMOTION_SUPPORT_UNIT_PPT_SET.contains(uppt), "unit_promotion_purpose_type传参错误");
        }
    }

    private void androidGameCheck(PromotionPurposeType cppt, Context context, MiddleNewCpcUnitVo vo) {
        // 计划是安卓游戏 or 品牌传播且选了游戏包
        if (PromotionPurposeType.ON_SHELF_GAME.equals(cppt) ||
                (PromotionPurposeType.BRAND_SPREAD.equals(cppt) && Utils.isPositive(vo.getGame_base_id()))) {
            // 设备定向必须为"安卓"
            Assert.notNull(vo.getTargets(), "目标不能为空");
            cpcUnitValidator.validateOsMustOnlyAndroid(vo.getTargets().getOs());
            // 智能放量不能突破"设备
            List<Integer> IntelligentMass = Optional.ofNullable(vo).map(o -> o.getTargets())
                    .map(o -> o.getIntelligentMass())
                    .map(o -> o.getTargets()).orElse(new ArrayList<>());
            cpcUnitValidator.validateIntelligentMassNotDevice(IntelligentMass);
        }
    }

    private void setDefaultAreaTypeIfNecessary(MiddleNewCpcUnitVo vo) {
        if (vo.getTargets() != null && CollectionUtils.isEmpty(vo.getTargets().getArea_type())) {
            vo.getTargets().setArea_type(Arrays.asList(AreaTypeEnum.ALL.getCode()));
        }
    }

    /**
     * 稿件内容情况 mid 处理
     * 1. 蓝v mids 包含稿件mid，归因在稿件 mid
     * 2. 否则，归因在 account上的 mid 上
     *
     * @param accountBaseDto
     * @param vo
     * @return
     * @throws ServiceException
     */
    private Long generateUnitArchiveMid(AccountBaseDto accountBaseDto, MiddleNewCpcUnitVo vo) throws ServiceException {
        //计划类型是稿件
        if (vo.getUnit_promotion_purpose_type() != null && PromotionPurposeType.ARCHIVE_CONTENT.getCode() == vo.getUnit_promotion_purpose_type()) {
            List<Long> blueMids = middleArchiveService.getBlueMidsByAccountId(accountBaseDto.getAccountId());

            // 获取稿件的 mid
            Long archiveMid = this.generateMidByAid(Long.valueOf(vo.getVideo_id()));
            //是蓝V稿件,建单元时mid传稿件mid（让数据正确归因）
            if (blueMids.contains(archiveMid)) {
                return archiveMid;
            } else {
                //否则传账号mid（不让数据归因上）
                return accountBaseDto.getMid();
            }
        }
        return accountBaseDto.getMid();
    }

    // targets.installed_user_filter的字段表示已安装用户类型
    // app_package_id字段表示安装包id
    @ApiOperation(value = "编辑单元")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    public Response<Object> updateUnit(
            @ApiIgnore Context context,
            @Validated @RequestBody UpdateCpcUnitVo vo) throws ServiceException {

        return cpcUnitController.updateUnit(context, vo);
    }


    @ApiOperation(value = "启用单元")
    @RequestMapping(params = {"status=1"}, value = "/status", method = RequestMethod.PUT)
    public Response<Object> enableUnit(
            @ApiIgnore Context context,
            @RequestBody UpdateStatusRequestBean usrb,
            @RequestParam("status") int status) throws ServiceException {
        return cpcUnitController.enableUnit(context, usrb, status);
    }

    @ApiOperation(value = "暂停单元")
    @RequestMapping(params = {"status=2"}, value = "/status", method = RequestMethod.PUT)
    public Response<Object> pauseUnit(@ApiIgnore Context context, @RequestBody UpdateStatusRequestBean usrb,
                                      @RequestParam("status") int status) throws ServiceException {
        return cpcUnitController.pauseUnit(context, usrb, status);
    }

    @ApiOperation(value = "结束单元")
    @RequestMapping(params = {"status=3"}, value = "/status", method = RequestMethod.PUT)
    public Response<Object> finishUnit(
            @ApiIgnore Context context,
            @RequestBody UpdateStatusRequestBean usrb,
            @RequestParam("status") int status) throws ServiceException {

        return cpcUnitController.finishUnit(context, usrb, status);
    }

    @ApiOperation(value = "删除单元")
    @RequestMapping(params = {"status=4"}, value = "/{uids}", method = RequestMethod.PUT)
    public Response<Object> deleteUnit(
            @ApiIgnore Context context,
            @RequestBody UpdateStatusRequestBean usrb,
            @RequestParam("status") int status) throws ServiceException {

        return cpcUnitController.deleteUnit(context, usrb, status);
    }

    @ApiOperation(value = "修改单元预算")
    @RequestMapping(value = "/budget", method = RequestMethod.PUT)
    public Response<Object> updateBudget(@ApiIgnore Context context,
                                         @RequestParam("unit_id") List<Integer> unitIds,
                                         @RequestParam("budget") BigDecimal budget,
                                         @RequestParam Integer daily_budget_type,
                                         @RequestParam("effect_type") Integer effectType,
                                         @RequestParam(value = "is_repeat", required = false, defaultValue = "0") Integer isRepeat) throws ServiceException {
        Assert.notNull(unitIds, "单元id不能为空");

        if (!accountLabelService.isAccountIdInLabel(context.getAccountId(), oldSanlianAllowEdit)) {
            Assert.isTrue(adpCpcUnitService.getAdp5UnitBos(unitIds).isEmpty(), "不支持编辑旧版广告单元");
        }
        cpcUnitService.batchUpdateCpcUnitBudget(NewBatchUpdateCpcUnitBudgetDto.builder()
                .budget(budget)
                .unitIds(unitIds)
                .dailyBudgetType(daily_budget_type)
                .effectType(effectType)
                .isRepeat(isRepeat)
                .build(), getOperator(context));
        //cpcUnitService.batchUpdateBudget(unitIds, budget, daily_budget_type, getOperator(context));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "修改单元名称")
    @RequestMapping(value = "/unit_name", method = RequestMethod.PUT)
    public Response<Integer> updateUnitName(@ApiIgnore Context context, @RequestBody UnitNameUpdateVo unitNameUpdateVo) throws ServiceException {
        if (!accountLabelService.isAccountIdInLabel(context.getAccountId(), oldSanlianAllowEdit)) {
            Assert.isTrue(adpCpcUnitService.getAdp5UnitBos(Collections.singletonList(unitNameUpdateVo.getUnitId())).isEmpty(), "不支持编辑旧版广告计划");
        }
        UpdateUnitNameDto updateUnitNameDto = UpdateUnitNameDto.builder()
                .unitId(unitNameUpdateVo.getUnitId())
                .unitName(unitNameUpdateVo.getUnitName())
                .build();
        Integer count = cpcUnitService.updateUnitName(updateUnitNameDto, getOperator(context));
        return Response.SUCCESS(count);
    }

    @ApiOperation(value = "删除单元次日预算")
    @RequestMapping(value = "/nextday_budget/{unitId}", method = RequestMethod.DELETE)
    public Response<Object> deleteNextdayBudget(@ApiIgnore Context context, @PathVariable("unitId") Integer unitId) throws ServiceException {
        return cpcUnitController.deleteNextdayBudget(context, unitId);
    }

    @ApiOperation(value = "单元批量绑定定向包")
    @RequestMapping(value = "/batch/target_package", method = RequestMethod.PUT)
    public Response<Object> batchBindUnit(@ApiIgnore Context context,
                                          @RequestBody MiddleTargetPackageBatchBindVo bindVo) {
        UnitBatchBindTargetPackageUpgradeBo batchBindDto = UnitBatchBindTargetPackageUpgradeBo.builder()
                .targetPackageId(bindVo.getTarget_package_id())
                .operator(getOperator(context))
                .bindUnitIdList(bindVo.getBind_unit_id_list())
                .unbindUnitIdList(bindVo.getUnbind_unit_id_list())
                .build();
        cpcUnitService.batchBindTargetPackageUpgrade(batchBindDto);
        return Response.SUCCESS();
    }

    @Deprecated
    @ApiOperation(value = "修改出价")
    @RequestMapping(value = "/cost_price", method = RequestMethod.PUT)
    public Response<Object> updateCostPrice(@ApiIgnore Context context,
                                            @RequestParam("unit_id") List<Integer> unitIds,
                                            @RequestParam("cost_price") BigDecimal costPrice) throws ServiceException {
        return cpcUnitController.updateCostPrice(context, unitIds, costPrice);
    }

    @ApiOperation(value = "批量修改出价")
    @RequestMapping(value = "/batch/bid_price", method = RequestMethod.PUT)
    public Response<Object> batchUpdateBidPrice(@ApiIgnore Context context,
                                                @RequestParam("unit_ids") List<Integer> unitIds,
                                                @RequestParam("bid_price") BigDecimal bidPrice,
                                                @RequestParam(value = "two_stage_bid", defaultValue = "") BigDecimal twoStageBid) throws ServiceException {
        return cpcUnitController.batchUpdateBidPrice(context, unitIds, bidPrice, twoStageBid);
    }

    @ApiOperation(value = "批量修改出价计算更新结果")
    @RequestMapping(value = "/batch/bid_price", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<UnitPriceListVo> getBatchUpdateBidPrice(@ApiIgnore Context context,
                                                     @ApiParam("unit_ids") @RequestParam @Valid List<Integer> unit_ids,
                                                     @ApiParam("出价方式 11-CPM 12-CPC") @RequestParam @Valid Integer sales_type,
                                                     @ApiParam("修改类型 0-CPX出价 1-OCPX一阶段出价 2-OCPX优化目标出价 3-OCPC深度优化目标出价 4-辅助探索出价") @Valid @RequestParam Integer update_type,
                                                     @ApiParam("修改值类型 0-修改至指定价格 1-修改差值 2-修改差值百分比") @Valid @RequestParam Integer update_value_type,
                                                     @ApiParam("更新值 正负") @Valid @RequestParam(value = "update_value") BigDecimal update_value,
                                                     @ApiParam("限制值 上限/下限") @RequestParam(value = "limit", required = false) BigDecimal limit) {
        // 百分比处理
        if (UnitUpdateBidPriceValueTypeEnum.DIFF_VALUE_PERCENT.getCode().equals(update_value_type)) {
            update_value = update_value.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_DOWN);
        }
        UnitUpdateBidPriceQueryDto queryDto = UnitUpdateBidPriceQueryDto.builder()
                .unitIds(unit_ids)
                .salesType(sales_type)
                .updateType(update_type)
                .updateValueType(update_value_type)
                .updateValue(update_value)
                .limit(limit)
                .build();
        UnitPriceListDto result = cpcUnitService.getBatchUpdateBidPrice(super.getOperator(context), queryDto);
        return Response.SUCCESS(UnitPriceListVo.convertFromDto(result));
    }

    @ApiOperation(value = "批量修改出价新版")
    @RequestMapping(value = "/batch/bid_price/new", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> batchUpdateBidPriceNew(@ApiIgnore Context context,
                                            @RequestBody UnitUpdateBidPriceVo vo) throws ServiceException {
        vo.validVo();
        if (!accountLabelService.isAccountIdInLabel(context.getAccountId(), oldSanlianAllowEdit)) {
            Assert.isTrue(adpCpcUnitService.getAdp5UnitBos(
                    vo.getUpdatePriceVoList().stream().map(UnitPriceVo::getUnitId).collect(Collectors.toList())
                    ).isEmpty(),
                    "不支持编辑旧版广告计划");
        }
        cpcUnitService.batchUpdateBidPrice(super.getOperator(context), UnitUpdateBidPriceVo.convertToDto(vo));
        return Response.SUCCESS();
    }

    @ApiOperation(value = "校验预算修改是否合法")
    @RequestMapping(value = "/validate/budget", method = RequestMethod.POST)
    public Response<Object> validateBudget(@ApiIgnore Context context,
                                           @RequestParam("unit_id") Integer unitId,
                                           @RequestParam("budget") BigDecimal budget) throws ServiceException {
        return cpcUnitController.validateBudget(context, unitId, budget);
    }

    @ApiOperation(value = "校验预算修改是否合法")
    @RequestMapping(value = "/mock/update_app_id/{id}", method = RequestMethod.GET)
    public String updateAppId(@ApiIgnore Context context,
                              @PathVariable("id") Integer appId) throws ServiceException {
        return cpcUnitController.updateAppId(context, appId);
    }

    @ApiOperation(value = "查询单元详情")
    @GetMapping(value = "/detail/{id}")
    public Response<CpcUnitDetailVo> getUnitDetail(
            @ApiIgnore Context context,
            @PathVariable("id") @ApiParam(required = true, value = "unitId", defaultValue = "1") Integer unitId
    ) throws ServiceException {
        return cpcUnitController.getUnitDetail(context, unitId);
    }

    @ApiOperation(value = "查询推广单元花费")
    @RequestMapping(value = "/{uid}/cost", method = RequestMethod.GET)
    public Response<Object> queryCampaignCost(@ApiIgnore Context context,
                                              @PathVariable("uid") Integer unitId,
                                              @RequestParam("from_time") Long fromTime,
                                              @RequestParam("to_time") Long toTime) throws ServiceException {

        return cpcUnitController.queryCampaignCost(context, unitId, fromTime, toTime);
    }

    // APP包的已安装用户不在target里，前端根据推广类型=4,6,8,9支持
    @ApiOperation(value = "竞价广告查询定向条件")
    @RequestMapping(value = "/target", method = RequestMethod.GET)
    public Response<Object> queryTargetListForPrice(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id", required = false) Integer campaignId,
            @RequestParam(value = "promotion_purpose_type", required = false) Integer ppt) throws InterruptedException, ServiceException {
        // 1. 不传计划id&推广目的 返回效果定向
        if (campaignId == null || campaignId <= 0 && !Utils.isPositive(ppt)) {
            Map<TargetType, List<TargetTreeDto>> target2ItemMap = resTargetItemService.getAllValidTarget2ItemTreeMap();
            Map<String, Object> ans = converter.buildTargetMap(context.getAccountId(), target2ItemMap, campaignId, ppt, getOperator(context));
            return Response.SUCCESS(ans);
        }

        // 2. 传了计划 id 或者推广目的
        PromotionPurposeType promotionPurposeType = null;
        Integer campaignAdType = null;
        if (Utils.isPositive(campaignId)) {
            CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(campaignId);
            promotionPurposeType = PromotionPurposeType.getByCode(campaign.getPromotionPurposeType());
            campaignAdType = campaign.getAdType();
        }
        if (Utils.isPositive(ppt)) {
            promotionPurposeType = PromotionPurposeType.getByCode(ppt);
        }

        // 2.1 如果品牌传播，账号推广是起飞定向
        //【三连推广】直播推广定向优化
        //https://www.tapd.bilibili.co/********/prong/stories/view/11********003022354
        if (PromotionPurposeType.ENTERPRISE_PROMOTION.equals(promotionPurposeType)
                || PromotionPurposeType.BRAND_SPREAD.equals(promotionPurposeType)) {
//        if (PromotionPurposeType.LIVE_ROOM.equals(promotionPurposeType)
//                || PromotionPurposeType.ENTERPRISE_PROMOTION.equals(promotionPurposeType)
//                || PromotionPurposeType.BRAND_SPREAD.equals(promotionPurposeType)) {
            // 获取定向树
            Map<TargetType, List<TargetTreeDto>> target2ItemMap = resTargetItemService.getAllValidTarget2ItemTreeMap();

            // 根据账号等信息处理定向
            Map<String, Object> ans = flyUnitConverter.buildTargetMap(context.getAccountId(), target2ItemMap, campaignId, promotionPurposeType.getCode(), getOperator(context), true);

            // 获取视频二级分区
            Response response = cpcResourceContorller.getVideoSecondPartitions();
            List<SelectDropBoxItemVo> second = (List) response.getResult();
            ans.put("video_interest", second);
            // 计划是搜索计划，最多只能返回5个特定定向
            if (CampaignAdType.SEARCH.getCode().equals(campaignAdType)) {
                return Response.SUCCESS(this.handleSearchCampaignTarget(ans));
            }

            //搜索广告/闪屏广告对行业优选不生效
            boolean adSupportProfessionAuto = true;
            boolean adSupportProfessionInterest = true;
            if (null == campaignAdType || CampaignAdType.SPLASH_SCREEN.getCode().equals(campaignAdType) || CampaignAdType.SEARCH.getCode().equals(campaignAdType)) {
                adSupportProfessionAuto = false;
                adSupportProfessionInterest = false;
            }
            ans.put("adSupportProfessionAuto", adSupportProfessionAuto);
            ans.put("adSupportProfessionInterest", adSupportProfessionInterest);

            return Response.SUCCESS(ans);
        }

        // 2.2 否则，其它推广目的是效果定向
        Map<TargetType, List<TargetTreeDto>> target2ItemMap = resTargetItemService.getAllValidTarget2ItemTreeMap();
        Map<String, Object> ans = converter.buildTargetMap(context.getAccountId(), target2ItemMap, campaignId, ppt, getOperator(context));
        // 计划是搜索计划，最多只能返回5个特定定向
        if (CampaignAdType.SEARCH.getCode().equals(campaignAdType)) {
            return Response.SUCCESS(this.handleSearchCampaignTarget(ans));
        }
        return Response.SUCCESS(ans);
    }

    private Map<String, Object> handleSearchCampaignTarget(Map<String, Object> ans) {
        Map<String, Object> res = new HashMap<>();
        // 用名字过滤
        ans.forEach((k, v) -> {
            if (TargetType.SEARCH_CAMPAIGN_SUPPORT_TARGET_BY_NAME.contains(k)) {
                res.put(k, v);
            }
        });
        return res;
    }

    @ApiOperation(value = "最低出价")
    @RequestMapping(value = "/lowest_bid", method = RequestMethod.GET)
    public Response<BigDecimal> getLowestBidCost(
            @ApiIgnore Context context,
            @RequestParam(value = "slot_group_id", required = false) Integer slotGroupId,
            @RequestParam(value = "promotion_purpose_type", required = false) Integer promotionPurposeType,
            @RequestParam(value = "unit_promotion_purpose_type", required = false) Integer unitPromotionPurposeType,
            @RequestParam(value = "sales_type") Integer salesType,
            @RequestParam(value = "adp_version", required = false, defaultValue = "0") Integer adpVersion,
            @ApiParam("目标状态 9-稿件播放 10-用户关注 25-动态详情页浏览 26-活动页浮层拉起 27-首条评论复制 28-框下链接点击 30-评论点击 31-小黄车点击")
            @RequestParam(value = "ocpm_target", required = false) Integer ocpmTarget,
            @RequestParam(value = "ocpc_target", required = false) Integer ocpcTarget
    ) throws ServiceException {
        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        Assert.notNull(accountBaseDto, "账号信息不存在");
        if (accountBaseDto.getIsSupportContent() > 0) {
            // https://www.tapd.cn/********/prong/stories/view/11********004159242
            Assert.isTrue(Utils.isPositive(promotionPurposeType), "计划推广目的不能为空");

            return Response.SUCCESS(Utils.fromFenToYuan(cpcUnitService.getContentFlyLowestBid(promotionPurposeType, unitPromotionPurposeType, ocpmTarget)));
        }
        List<Integer> accountLabelIdList = accountLabelService.getLabelIdsByAccountId(context.getAccountId());
        //品牌传播
        if (ObjectUtils.equals(PromotionPurposeType.BRAND_SPREAD.getCode(), promotionPurposeType)) {
            return Response.SUCCESS(Utils.fromFenToYuan(cpcUnitService.getFlyBanner4LowestCost4Unit(GetBidCostParam.builder()
                    .accountId(context.getAccountId())
                    .slotGroupId(slotGroupId)
                    .launchType(unitPromotionPurposeType)
                    .salesType(salesType)
                    .accountLabelIdList(accountLabelIdList)
                    .build(), ocpmTarget)));
        }
        //直播间
        if (ObjectUtils.equals(PromotionPurposeType.LIVE_ROOM.getCode(), promotionPurposeType) &&
                ObjectUtils.equals(SalesType.CPM.getCode(), salesType)) {
            return Response.SUCCESS(Utils.fromFenToYuan(cpcUnitService.getFlyBanner4LowestCost4Unit(GetBidCostParam.builder()
                    .accountId(context.getAccountId())
                    .slotGroupId(slotGroupId)
                    .launchType(promotionPurposeType)
                    .salesType(salesType)
                    .accountLabelIdList(accountLabelIdList)
                    .build(), ocpmTarget)));
        }
        //企业号下投稿内容
        if (ObjectUtils.equals(PromotionPurposeType.ENTERPRISE_PROMOTION.getCode(), promotionPurposeType)) {
            if (unitPromotionPurposeType != null && PromotionPurposeType.ARCHIVE_CONTENT.getCode() == unitPromotionPurposeType) {
                return Response.SUCCESS(Utils.fromFenToYuan(cpcUnitService.getFlyBanner4LowestCost4Unit(GetBidCostParam.builder()
                        .accountId(context.getAccountId())
                        .slotGroupId(slotGroupId)
                        .launchType(unitPromotionPurposeType)
                        .salesType(salesType)
                        .accountLabelIdList(accountLabelIdList)
                        .build(), ocpmTarget)));
            }
        }
        Integer lowestBidFen = null;
        if (ocpmTarget == null || ocpmTarget == 0) {
            lowestBidFen = cpcUnitServiceDelegate.getOcpcLowesBid(ocpcTarget, accountBaseDto.getAccountId(), accountLabelIdList);
        }
        //效果查询
        if (lowestBidFen == null) {
            lowestBidFen = lowestBidService.getLowestBidFen(context.getAccountId(), salesType);
        }
        return Response.SUCCESS(Utils.fromFenToYuan(lowestBidFen));
    }

    @ApiOperation(value = "最低出价")
    @RequestMapping(value = "/bid_section", method = RequestMethod.GET)
    public Response<SanlianCpaTargetMinBidConfigBo> getBidSection(
            @ApiIgnore Context context,
            @RequestParam(value = "slot_group_id", required = false) Integer slotGroupId,
            @RequestParam(value = "promotion_purpose_type", required = false) Integer promotionPurposeType,
            @RequestParam(value = "unit_promotion_purpose_type", required = false) Integer unitPromotionPurposeType,
            @RequestParam(value = "sales_type") Integer salesType,
            @RequestParam(value = "adp_version", required = false, defaultValue = "0") Integer adpVersion,
            @ApiParam("目标状态 9-稿件播放 10-用户关注 25-动态详情页浏览 26-活动页浮层拉起 27-首条评论复制 28-框下链接点击 30-评论点击 31-小黄车点击")
            @RequestParam(value = "ocpm_target", required = false) Integer ocpmTarget,
            @RequestParam(value = "ocpc_target", required = false) Integer ocpcTarget
    ) throws ServiceException {
        Integer cpaTarget = ocpmTarget == null || ocpmTarget == 0 ? ocpcTarget : ocpmTarget;
        SanlianCpaTargetMinBidConfigBo resultBo = null;
        if (cpaTarget != null && cpaTarget != 0) {
            GetUnitBidConfigReq req = GetUnitBidConfigReq.newBuilder()
                    .setAccountId(context.getAccountId())
                    .setCpaTargetValue(cpaTarget)
                    .build();
            GetUnitBidConfigResp resp = unitResourceServiceBlockingStub.getUnitBidConfig(req);
            resultBo = ResourceUnitMapper.MAPPER.convertRpcInfo2Bo(resp.getBidConfig(), cpaTarget);
        }
        if (!Objects.isNull(salesType) && SalesType.CPM.getCode() == salesType) {
            ocpmTarget = cpaTarget;
            ocpcTarget = null;
        } else if (!Objects.isNull(salesType) && SalesType.CPC.getCode() == salesType) {
            ocpmTarget = null;
            ocpcTarget = cpaTarget;
        }
        Response<BigDecimal> lowestBidCost = this.getLowestBidCost(context, slotGroupId, promotionPurposeType, unitPromotionPurposeType, salesType, adpVersion, ocpmTarget, ocpcTarget);

        if (Objects.isNull(resultBo)) {
            resultBo = new SanlianCpaTargetMinBidConfigBo();
            resultBo.setCpaTargetMaxBid("");
        }
        resultBo.setCpaTargetMinBid(lowestBidCost.getResult().toString());
        return Response.SUCCESS(resultBo);
    }

    @ApiOperation(value = "目标转化出价最低价")
    @RequestMapping(value = "/target_lowest_bid", method = RequestMethod.GET)
    public Response<BigDecimal> getLowestBidCost(
            @ApiIgnore Context context,
            @RequestParam(value = "promotion_purpose_type", required = false) Integer promotionPurposeType,
            @ApiParam("目标状态 9-稿件播放 10-用户关注 25-动态详情页浏览 26-活动页浮层拉起 27-首条评论复制 28-框下链接点击 30-评论点击 31-小黄车点击")
            @RequestParam(value = "ocpm_target", required = false) Integer ocpmTarget) throws ServiceException {
        return Response.SUCCESS(targetLowestBidProc.fetchLowestBid(context.getAccountId(), promotionPurposeType, ocpmTarget));
    }

    @ApiOperation(value = "列表最低出价展示")
    @RequestMapping(value = "/list_lowest_bid_show", method = RequestMethod.GET)
    public Response<BigDecimal> ListLowestBidShow(@ApiIgnore Context context,
                                                  @RequestParam(value = "unit_id", required = false) Integer unitId) throws ServiceException {
        CpcUnitDto cpcUnitDto = cpcUnitService.loadCpcUnit(unitId);
        CpcCampaignDto cpcCampaignDto = cpcCampaignService.loadCpcCampaignDto(cpcUnitDto.getCampaignId());
        if (ObjectUtils.equals(cpcUnitDto.getPromotionPurposeType(), PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
            if (ObjectUtils.equals(cpcUnitDto.getSalesType(), SalesType.CPM.getCode())) {
                if (ObjectUtils.equals(cpcUnitDto.getOcpcTarget(), 0)) {
                    return Response.SUCCESS(new BigDecimal(8.00));
                }
            }
        }
        return this.getLowestBidCost(context, cpcUnitDto.getSlotGroup(), cpcCampaignDto.getPromotionPurposeType(),
                cpcUnitDto.getPromotionPurposeType(),
                cpcUnitDto.getSalesType(), cpcUnitDto.getAdpVersion(), cpcUnitDto.getOcpcTarget(), 0);
    }

    @ApiOperation(value = "最高出价")
    @RequestMapping(value = "/highest_bid", method = RequestMethod.GET)
    public Response<Map<Integer, Integer>> getHighestBidCost(@ApiIgnore Context context) throws ServiceException {
        return cpcUnitController.getHighestBidCost(context);
    }

    @ApiOperation(value = "人群预估")
    @RequestMapping(value = "/forecast", method = RequestMethod.POST)
    public Response<CpcUnitCrowdForecastResponseVo> crowdForecast(@ApiIgnore Context context,
                                                                  @RequestBody CpcUnitCrowdForecastRequestVo vo) throws ServiceException {

        return cpcUnitController.crowdForecast(context, vo);
    }

    @ApiOperation(value = "创意复制检查")
    @RequestMapping(value = "/check_copy", method = RequestMethod.GET)
    public Response<CpcUnitCreativeCheckCopyResponseVo> checkCreativeCopy(@ApiIgnore Context context,
                                                                          @RequestParam Integer unit_id,
                                                                          @RequestParam(required = false) Integer new_unit_id,
                                                                          @RequestParam(required = false) Integer slot_group_id) throws ServiceException {
        return cpcUnitController.checkCreativeCopy(context, unit_id, new_unit_id, slot_group_id);
    }

    @ApiOperation(value = "获取单元下拉列表")
    @RequestMapping(value = "/drop_box", method = RequestMethod.GET)
    public Response<List<SimpleUnitVo>> getUnitDropBox(
            @ApiIgnore Context context,
            @RequestParam(required = true, value = "campaign_id") Integer campaignId,
            @RequestParam(required = false, value = "template_id") Integer templateId,
            @RequestParam(required = false, value = "no_programmatic", defaultValue = "false") boolean noProgrammatic) throws ServiceException {
//        QueryCpcUnitDto query = QueryCpcUnitDto
//                .builder()
//                .accountId(context.getAccountId())
//                .campaignId(campaignId)
//                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
//                .orderBy(Constants.MTIME_DESC)
//                .isNewFly(0)
//                .isMiddleAd(1)
//                .build();
//
//        List<CpcUnitDto> dtos = cpcUnitService.queryCpcUnit(query);
        QueryUnitBo query = QueryUnitBo
                .builder()
                .accountIds(Collections.singletonList(context.getAccountId()))
                .campaignIds(Collections.singletonList(campaignId))
                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
                .orderBy(Constants.MTIME_DESC)
                .isNewFly(0)
                .isMiddleAd(1)
                .build();
        List<CpcUnitDto> dtos = launchUnitV1Service.listUnits(query);
        if (noProgrammatic) {
            dtos = dtos.stream()
                    .filter(x -> !adpCpcUnitService.isProgrammatic(x.getUnitId()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(dtos)) {
            Response.SUCCESS(Collections.emptyList());
        }

        if (!Utils.isPositive(templateId)) {
            final List<SimpleUnitVo> dropboxItems = dtos.stream()
                    .map(x -> {
                        return SimpleUnitVo.builder()
                                .id(x.getUnitId())
                                .name(x.getUnitName())
                                .adpVersion(x.getAdpVersion())
                                .build();
                    }).collect(Collectors.toList());
            return Response.SUCCESS(dropboxItems);
        }

        Map<Integer, List<SimpleUnitVo>> unitPerSlotGroupId = dtos.stream()
                .collect(Collectors.groupingBy(CpcUnitDto::getSlotGroup,
                        Collectors.mapping(dto -> SimpleUnitVo.builder()
                                .id(dto.getUnitId())
                                .name(dto.getUnitName())
                                .adpVersion(dto.getAdpVersion())
                                .build(), Collectors.toList())));

        List<Integer> slotGroupIds = dtos
                .stream()
                .map(CpcUnitDto::getSlotGroup)
                .distinct()
                .collect(Collectors.toList());

        QueryTemplateLaunchTypeMappingDto qtlm = QueryTemplateLaunchTypeMappingDto
                .builder()
                .slotGroupIds(slotGroupIds)
                .build();

        if (campaignId != null && campaignId.compareTo(0) > 0) {
            CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(campaignId);
            Assert.isTrue(context.getAccountId().equals(campaign.getAccountId()), "不能操作不属于你的计划");

            qtlm.setPromotionPurposeType(campaign.getPromotionPurposeType());
        }

        List<ResSlotGroupTemplateMappingDto> validSlotGroups = resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(qtlm);

        if (CollectionUtils.isEmpty(validSlotGroups)) {
            return Response.SUCCESS(Collections.emptyList());
        }

        List<Integer> validSlotGroupIds = validSlotGroups
                .stream()
                .filter(sg -> !CollectionUtils.isEmpty(sg.getTemplates()) && sg.getTemplates().stream().filter(t -> templateId.equals(t.getTemplateId())).count() > 0)
                .map(ResSlotGroupTemplateMappingDto::getSlotGroupId)
                .collect(Collectors.toList());

        return Response.SUCCESS(
                unitPerSlotGroupId
                        .entrySet()
                        .stream()
                        .filter(e -> validSlotGroupIds.contains(e.getKey()))
                        .flatMap(e -> e.getValue().stream())
                        .collect(Collectors.toList()));
    }

    @ApiOperation("获取直播间信息")
    @RequestMapping(value = "/live_room/{live_id}", method = RequestMethod.GET)
    public Response<LiveMateriaInfolVo> getLiveBroadcastRoomInfoById(
            @ApiParam("直播间id") @PathVariable(value = "live_id") String liveId) {
        return cpcUnitController.getLiveBroadcastRoomInfoById(liveId);
    }

    @ApiOperation("获取直播间信息")
    @RequestMapping(value = "/live_room/by_mid/{mid}", method = RequestMethod.GET)
    public Response<LiveMateriaInfolVo> getLiveBroadcastRoomInfoByMid(
            @ApiParam("mid") @PathVariable(value = "mid") String mid) {
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(mid) && org.apache.commons.lang3.StringUtils.isNumeric(mid), "mid不能为空");

        LiveBroadcastRoomInfo roomInfo = adpCpcLiveRoomService.fetchLiveRoomInfoById(Long.valueOf(mid));
        Assert.isTrue(roomInfo != null, "直播间不存在");

        // 获取直播间的 up 信息
        String nickname = "";
        if (roomInfo.getUid() != null) {
            try {
                UserInfoDto userInfoDto = passportService.getUserByMid(roomInfo.getUid());
                nickname = userInfoDto == null ? "" : userInfoDto.getName();
            } catch (Exception e) {
                log.error("getLiveBroadcastRoomInfoByMid request user's nickname occur error! {}", e);
            }
        }
        return Response.SUCCESS(LiveMateriaInfolVo.builder()
                .description(nickname)
                .live_id(roomInfo.getRoomId())
                .uid(roomInfo.getUid())
                .title(roomInfo.getTitle())
                .cover(roomInfo.getCover())
                .status(roomInfo.getIsShow())
                .status_desc((roomInfo.getIsShow() == null || roomInfo.getIsShow() == 0) ? "未开播" : "直播中")
                .link_url(roomInfo.getLink())
                .area_v2_id(roomInfo.getAreaV2Id())
                .area_v2_name(roomInfo.getAreaV2Name())
                .area_v2_parent_id(roomInfo.getAreaV2ParentId())
                .area_v2_parent_name(roomInfo.getAreaV2ParentName())
                .build());
    }

    @ApiOperation("应用商店直投优化目标")
    @GetMapping(value = "/support_store_direct_launch")
    public Response<List<UnitSupportStoreDirectLaunchVo>> supportStoreDirectLaunch(@ApiIgnore Context context) {
        return cpcUnitController.supportStoreDirectLaunch(context);
    }


    //todo 下个版本移除接口
    @ApiOperation(value = "新建单元时查询广告位组模板")
    @RequestMapping(value = "/slot_group/drop_box", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<DropBoxItemVo>> getSlotGroupDropBox(@ApiIgnore Context context,
                                                      @RequestParam(value = "campaign_id", required = false) Integer campaignId) throws ServiceException {
        return cpcUnitController.getSlotGroupDropBox(context, campaignId);
    }

    @ApiOperation(value = "新建单元时查询广告位组模板")
    @RequestMapping(value = "/slot_group", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<UnitSlotGroupInfo>> querySlotGroupByCampaignId(@ApiIgnore Context context,
                                                                 @RequestParam("campaign_id") Integer campaignId) throws ServiceException {
        return cpcUnitController.querySlotGroupByCampaignId(context, campaignId);
    }

    @ApiOperation("获取投放方式及oCPX目标")
    @GetMapping("/sales_type_options")
    public Response<List<SalesTypeOptionVo>> getSalesTypeOptions(
            @ApiIgnore Context context,
            @RequestParam(value = "os", required = false) List<Integer> osList,
            @RequestParam(value = "slot_group_id", required = false) Integer slotGroupId,
            @RequestParam(value = "campaign_id", required = false) Integer campaignId,
            @RequestParam(value = "unit_id", required = false) Integer unitId,
            @RequestParam(value = "dynamic_id", required = false) String dynamicId,
            @RequestParam(value = "game_status", required = false) Integer gameStatus,
            @RequestParam(value = "unit_promotion_purpose_type", required = false) Integer unitPromotionPurposeType,
            @RequestParam(value = "avid", required = false) Long avid,
            @RequestParam(value = "promotion_purpose_type", required = false) Integer promotionPurposeType
    ) {
        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        Assert.notNull(accountBaseDto, "账号信息不存在");
        if (accountBaseDto.getIsSupportContent() > 0) {
            // https://www.tapd.cn/********/prong/stories/view/11********004159242
            return Response.SUCCESS(unitOcpxTargetComponent.getContentFlyTargets(campaignId, unitId, unitPromotionPurposeType, context.getAccountId()));
        }

        Long dynamicIdLong = StringUtils.isEmpty(dynamicId) ? null : Long.parseLong(dynamicId);

        List<SalesTypeOptionVo> result =
                unitOcpxTargetComponent.getUnitSalesTypeListWithOcpxTargets(context.getAccountId(), context.isOpenApi(),
                        osList, slotGroupId, campaignId,
                        unitId, gameStatus, unitPromotionPurposeType,
                        avid, promotionPurposeType, dynamicIdLong);

        return Response.SUCCESS(result);
    }

    @ApiOperation("获取深度优化 data")
    @GetMapping("/depth_opt_data")
    public Response<DepthOptDataVo> getDepthOptData(@ApiIgnore Context context) throws ServiceException {
        DepthOptDataDto dataDto = depthOptDataService.getDepthOptData(context.getAccountId());
        return Response.SUCCESS(DepthOptDataVo.convert(dataDto));
    }

    @ApiOperation("获取可筛选的单元目标")
    @GetMapping("/can_select_unit_target")
    public Response<List<CanSelectUnitTargetVo>> canSelectUnitTarget(@ApiIgnore Context context) throws ServiceException {
        List<CanSelectUnitTargetVo> res = new ArrayList<>();
//        // 1、cpm 普通
//        Map map1 = buildOcpxTargets(aid, campaignPromotionPurposeType, osList, gameStatus, SalesType.CPC.getCode()));
//        // 2、cpc 普通
//        Map map2 = buildOcpxTargets(aid, campaignPromotionPurposeType, osList, gameStatus, SalesType.CPM.getCode()));
//        // 3、nobid 优化目标
//        Map map3 =nobidOcpxTargetComponent.spliceNobidOcpxTargets(osList, campaignId, gameStatus, aid, accountLabelList, campaignAdType, result);
//        // 4、起飞ocpm
//        Map map4 = null;
//
//        // map key:target value:{名称，二级}
//
//        // 同时4个map，发现相同的key，需要找他们二级的并集，得到一个新map key:target value:{名称，二级}
//
//        // 新map，转成给前端的list

        return Response.SUCCESS(res);
    }


    private Integer buildCreateAllAdSearchUnit(NewCpcUnitVo newCpcUnitVo, MiddleNewCpcUnitVo vo, CpcCampaignDto cpcCampaignDto) {
        Integer isAllAdSearch = YesOrNoEnum.NO.getCode();
        if (CampaignAdType.ALL.getCode().equals(cpcCampaignDto.getAdType())) {
            isAllAdSearch = !CollectionUtils.isEmpty(vo.getSearch_ad_keywords()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
        }

        //不支持搜索明投设置默认值
        if (YesOrNoEnum.NO.getCode().equals(isAllAdSearch)) {
            newCpcUnitVo.setTarget_expand(IsValid.FALSE.getCode());
            newCpcUnitVo.setSearch_price_coefficient(BigDecimal.ZERO);
        }
        newCpcUnitVo.setIs_all_ad_search_unit(isAllAdSearch);
        return isAllAdSearch;
    }

    @ApiOperation("起量详情")
    @PostMapping("/accelerate/detail")
    public Response<UnitAccelerateDetailResponseVo> accelerateDetail(@ApiIgnore Context context, @Valid @RequestBody UnitAccelerateDetailRequestVo vo) {
        LauUnitAccelerateDetailBo unitAccelerateDetail = unitAccelerateService.getUnitAccelerateDetail(vo.getUnitId());
        return Response.SUCCESS(UnitAccelerateDetailResponseVo.fromBo(unitAccelerateDetail));
    }

    @ApiOperation("起量设置校验")
    @PostMapping("/accelerate/validate")
    public Response<UnitAccelerateValidateResponseVo> accelerateDetail(@ApiIgnore Context context, @Valid @RequestBody UnitAccelerateValidRequestVo vo) {
        LauUnitAccelerateValidateBo lauUnitAccelerateValidateBo = unitAccelerateService.validateUnitAccelerate(vo.getUnitIds(), vo.getStatus(), getOperator(context));
        return Response.SUCCESS(UnitAccelerateValidateResponseVo.fromBo(lauUnitAccelerateValidateBo));
    }

    @ApiOperation("设置单元起量")
    @PostMapping("/accelerate")
    public Response<Object> accelerateUnit(@ApiIgnore Context context, @Valid @RequestBody UnitAccelerateRequestVo vo) throws ServiceException {
        if (!accountLabelService.isAccountIdInLabel(context.getAccountId(), oldSanlianAllowEdit)) {
            Assert.isTrue(adpCpcUnitService.getAdp5UnitBos(Collections.singletonList(vo.getUnitId())).isEmpty(), "不支持编辑旧版广告单元");
        }
        List<LauUnitAccelerateValidateResultBo> resultBos = unitAccelerateService.accelerateUnit(vo.toBo(), getOperator(context), vo.getFromAccountDiagnosis());
        if (!CollectionUtils.isEmpty(resultBos)) {
            throw new ServiceException("开启起量失败，" + resultBos.get(0).getIllegalReason());
        }
        return Response.SUCCESS();
    }

    @ApiOperation("手动结束单元起量")
    @PostMapping("/accelerate/finish")
    public Response<Object> finishAccelerateUnit(@ApiIgnore Context context, @Valid @RequestBody UnitAccelerateFinishRequestVo vo) {
        if (!accountLabelService.isAccountIdInLabel(context.getAccountId(), oldSanlianAllowEdit)) {
            Assert.isTrue(adpCpcUnitService.getAdp5UnitBos(vo.getUnitIds()).isEmpty(), "不支持编辑旧版广告单元");
        }
        unitAccelerateService.finishUnitAccelerate(vo.getUnitIds(), getOperator(context));
        return Response.SUCCESS();
    }

    @ApiOperation("单元剩余预算")
    @PostMapping("/budget/remaining")
    public Response<UnitRemainingVo> budgetRemaining(@ApiIgnore Context context, @Valid @RequestBody UnitRemainingRequestVo vo) {
        Map<Integer, Long> unitRemainingBudgetMap = unitAccelerateService.getUnitRemainingBudgetMap(context.getAccountId(), vo.getUnitIds());

        List<UnitRemainingVo.Remaining> remainingList = unitRemainingBudgetMap.entrySet().stream().map(entry -> {
            UnitRemainingVo.Remaining remaining = new UnitRemainingVo.Remaining();
            remaining.setUnitId(entry.getKey());
            remaining.setRemainingBudget(Utils.fromFenToYuan(entry.getValue()));
            return remaining;
        }).collect(Collectors.toList());

        UnitRemainingVo unitRemainingVo = new UnitRemainingVo();
        unitRemainingVo.setRemainingList(remainingList);

        return Response.SUCCESS(unitRemainingVo);
    }

    @RequestMapping(value = "/parse-area", method = RequestMethod.POST)
    @ResponseBody
    public Response<ParseAreaDto> parseArea(
            @ApiIgnore Context context,
            @RequestParam("file") MultipartFile multipartFile
    ) throws IOException {
        String fileName = multipartFile.getOriginalFilename();
        Assert.isTrue(multipartFile.getSize() / 1000 <= 1024, "文件大小不能超过1M");
        Assert.isTrue(fileName != null || fileName.endsWith(".xls") || fileName.endsWith(".xlsx") || fileName.endsWith(".csv"), "请上传指定格式文件");
        return Response.SUCCESS(resTargetItemService.parseArea(multipartFile, fileName, context.getAccountId()));

    }

    @ApiOperation(value = "批量获取单元的投放模式")
    @PostMapping(value = "/list/speed_mode")
    @ResponseBody
    public Response<List<UnitListInfoVo>> queryUnitSpeedMode(
            @ApiIgnore Context context,
            @RequestBody BatchQueryUnitInfoVos vo) throws ServiceException {

        List<UnitListInfoVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(vo.getUnit_ids())) {
            return Response.SUCCESS(result);
        }

        final ListUnitResp resp = listServiceBlockingStub.listUnit(ListUnitReq.newBuilder().addAllUnitId(vo.getUnit_ids()).build());
        vo.getUnit_ids().forEach(
                unitid -> {
                    UnitListInfoVo v = new UnitListInfoVo();
                    v.setUnitId(String.valueOf(unitid));
                    ListUnitInfo info = resp.getUnitInfoMap().get(unitid);

                    if (Objects.nonNull(info) && context.getAccountId() == info.getAccountId()) {
                        v.setSpeedMode(info.getSpeedModeValue());
                    }
                    result.add(v);
                }
        );

        return Response.SUCCESS(result);
    }


    public void processHasCommentComponent(List<DynamicVo> dynamicVoList) {

        if (CollectionUtils.isEmpty(dynamicVoList)) {
            return;
        }

        List<Long> dynamicList = dynamicVoList.stream().map(DynamicVo::getDynamic_id).map(Long::parseLong).collect(Collectors.toList());

        ComponentsReq componentsReq = ComponentsReq.newBuilder()
                .addAllDynamicIdSet(dynamicList)
                .setPn(1)
                .setPs(20)
                .build();

        ComponentsReply components = commentComponentServiceBlockingStub.components(componentsReq);
        Set<Long> hasCommentComponentDynamic = components.getComponentsList().stream()
                .filter(commentComponent -> !Objects.equals(commentComponent.getStatus(), CommentComponentStatus.DELETED))
                .map(CommentComponent::getDynamicId)
                .collect(Collectors.toSet());
        for (DynamicVo dynamicVo : dynamicVoList) {
            String dynamicIdStr = dynamicVo.getDynamic_id();
            long dynamicId = Long.parseLong(dynamicIdStr);
            if (hasCommentComponentDynamic.contains(dynamicId)) {
                dynamicVo.setHas_bind_comment(IsValid.TRUE.getCode());
            } else {
                dynamicVo.setHas_bind_comment(IsValid.FALSE.getCode());
            }
        }
    }

}
