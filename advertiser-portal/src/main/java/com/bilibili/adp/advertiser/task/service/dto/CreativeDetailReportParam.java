package com.bilibili.adp.advertiser.task.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by ca<PERSON><PERSON><PERSON><PERSON> on 18/10/1.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreativeDetailReportParam {

    private List<Integer> accountIdList;
    private List<Integer> campaignIdList;
    private List<Integer> unitIdList;
    private List<Integer> creativeIdList;
    private Integer isDpa;
    private Integer isInner;
    private Integer isGame;
    private Integer isGoods;
    private Integer isContent;
    private Integer isLocalAd;
    private Integer dataType;
    private Timestamp fromTime;
    private Timestamp toTime;

}
