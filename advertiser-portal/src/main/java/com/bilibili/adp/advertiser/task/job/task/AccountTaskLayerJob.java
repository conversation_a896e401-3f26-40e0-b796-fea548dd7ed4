package com.bilibili.adp.advertiser.task.job.task;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.adp.task.TaskStartTypeEnum;
import com.bilibili.adp.advertiser.helper.CatMonitorPointcut;
import com.bilibili.adp.common.util.TimeUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.task.AdTaskHelper;
import com.bilibili.adp.cpc.biz.services.task.condition.TaskRuleConditionQuerierFactory;
import com.bilibili.adp.cpc.biz.services.task.dtos.AccountTaskRuleExeResultDetailDto;
import com.bilibili.adp.cpc.biz.services.task.dtos.TaskRuleConditionQueryDto;
import com.bilibili.adp.cpc.biz.services.task.dtos.TaskRuleConditionResultDto;
import com.bilibili.adp.cpc.biz.services.task.dtos.TaskStartEventCalculateQueryDto;
import com.bilibili.adp.cpc.biz.services.task.other.BsiMessageSender;
import com.bilibili.adp.cpc.biz.services.task.start_event.StartEventCalculatorFactory;
import com.bilibili.adp.cpc.dao.querydsl.pos.*;
import com.bilibili.adp.cpc.enums.task.*;
import com.bilibili.adp.cpc.repo.AdTaskRepo;
import com.bilibili.adp.cpc.utils.metrics.CustomMetrics;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant.*;

/**
 * 账户任务规则执行 job
 * 5分钟一次
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@JobHandler("AccountTaskLayerJob")
public class AccountTaskLayerJob extends IJobHandler {

    @Value("${refresh.task.size:300}")
    private Integer taskBatchSize;

    @Value("${coupon.task.notStartDaysLimit:90}")
    private Integer notStartDaysLimit;

    private final AdTaskRepo adTaskRepo;
    private final TaskRuleConditionQuerierFactory taskRuleConditionQuerierFactory;
    private final StartEventCalculatorFactory startEventCalculatorFactory;
    private final BsiMessageSender bsiMessageSender;
    private final AdTaskHelper adTaskHelper;
    private final CustomMetrics customMetrics;

    /**
     * 将某个时间之前的日志删除
     * 异步
     *
     * @return
     */
    public String deleteTaskExecLogAsync(Long maxTimestamp) {

        log.info("deleteTaskExecLogAsync, maxTimestamp={}", maxTimestamp);
        new Thread(() -> {

            // 小于等于某个时间戳的最大 id
            Long maxId = adTaskRepo.queryTaskExecMaxId(new Timestamp(maxTimestamp));
            log.info("deleteTaskExecLogAsync, maxTimestamp={}, maxId={}", maxTimestamp, maxId);

            // 分批删除
            Long fromId = 0L;
            Long toId = 0L;
            Integer batchSize = 500;
            Integer batchIndex = 1;
            Long totalDeleteCount = 0L;
            while (true) {
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                }

                toId = fromId + batchSize;
                Long deleteCount = adTaskRepo.deleteAccountTaskExecRecords(fromId, toId);
                totalDeleteCount += deleteCount;
                log.info("deleteAccountTaskExecRecords, fromId={},toId={},deleteCount={},batchIndex={},MaxId={}",
                        fromId, toId, deleteCount, batchIndex, maxId);
                if (toId >= maxId) {
                    log.info("deleteAccountTaskExecRecords 结束, fromId={},toId={},deleteCount={}," +
                                    "batchIndex={}MaxId={},totalDeleteCount={}",
                            fromId, toId, deleteCount, batchIndex, maxId, totalDeleteCount);
                    break;
                }

                fromId = toId;
                batchIndex++;
            }
        }).start();
        return "异步执行中...";
    }


    /**
     * 1. 根据 id 分批扫描任务
     * 2. 根据任务扫描账户任务关系
     * 3. 扫描这个任务这个账户的每个规则
     * 4. 根据规则执行情况处理
     *
     * @param s
     * @return
     * @throws Exception
     */
    @CatMonitorPointcut(transactionName = "AccountTaskJob")
    @Override
    public ReturnT<String> execute(String s) throws Exception {

        List<TaskExecStatDto> resultList = new ArrayList<>();

        for (int layer = 0; layer < 4; layer++) {
            resultList.add(doExecute(s, layer));
        }

        return new ReturnT<>(JSON.toJSONString(resultList));
    }

    private TaskExecStatDto doExecute(String s, Integer layer) throws InterruptedException {
        log.info("AccountTaskJob execute start...");

        Param param = parseParam(s);

        // 删除30天之前的日志
        deleteTaskExecLogAsync(Utils.getSomeDayAgo(Utils.getNow(), 30).getTime());

        // 获取最大 id
        Long maxId = adTaskRepo.queryMaxTaskId();
        XxlJobLogger.log("queryMaxTaskId, maxId={}", maxId);

        Long fromId = maxId;
        Long toId = maxId;
        Integer batchSize = taskBatchSize;
        Integer batchIndex = 1;

        TaskExecStatDto taskExecStatDto = TaskExecStatDto.builder().build();
        List<SingleTaskExecStatDto> taskExecStatDtos = new ArrayList<>();
        Integer totalTaskCount = 0;
        while (true) {
            Thread.sleep(10);
            fromId = toId - batchSize;
            // 获取一批任务
            List<AdTaskConfigPo> taskConfigPos = adTaskRepo.queryTaskListWithLayer(fromId, toId, param.getTaskIds(), layer);
            log.info("AccountTaskJob, queryTaskList, batchIndex:{},fromId:{},进度({}/{}),size:{}", batchIndex, fromId, toId, maxId, taskConfigPos.size());
            XxlJobLogger.log("queryTaskList, batchIndex:{},fromId:{},进度({}/{}),size:{}", batchIndex, fromId, toId, maxId, taskConfigPos.size());

            if (CollectionUtils.isEmpty(taskConfigPos)) {
                if (fromId <= 0) {
                    log.info("AccountTaskJob, queryTaskList 已处理完, batchIndex:{},fromId:{},进度({}/{})", batchIndex, fromId, toId, maxId);
                    XxlJobLogger.log("已处理完, batchIndex:{},fromId:{},进度({}/{})", batchIndex, fromId, toId, maxId);
                    break;
                }
                toId = fromId;
                batchIndex++;
                continue;
            }

            List<Long> taskIds = taskConfigPos.stream().map(taskConfigPo -> taskConfigPo.getId()).collect(Collectors.toList());
            List<AdTaskRuleConfigPo> allTaskRuleConfigPos = adTaskRepo.queryTaskRules(taskIds);
            Map<Long, List<AdTaskRuleConfigPo>> taskRuleConfigPosMap = allTaskRuleConfigPos.stream().collect(Collectors.groupingBy(t -> t.getTaskId()));

            toId = fromId;
            // 遍历任务
            for (AdTaskConfigPo taskConfigPo : taskConfigPos) {
                log.info("start process task,taskId={}, taskStatus={}", taskConfigPo.getId(), taskConfigPo.getStatus());
                XxlJobLogger.log("start process task,taskId={}", taskConfigPo.getId());
                totalTaskCount++;
                SingleTaskExecStatDto singleTaskExecStatDto = SingleTaskExecStatDto.builder().taskId(taskConfigPo.getId()).accountCount(0).build();
                taskExecStatDtos.add(singleTaskExecStatDto);

                AdAccountTaskRuleExecRecordPo recordPo = new AdAccountTaskRuleExecRecordPo();
                recordPo.setTaskId(taskConfigPo.getId());
                recordPo.setExecuteReslult(TaskExecuteResultTypeEnum.SUCCESS.getCode());

                // 获取任务的账户关系
                List<AdAccountTaskRelPo> accountTaskRelPos = adTaskRepo.queryAccountTaskRels(taskConfigPo.getId(),
                        param.getAccountIds());
                if (CollectionUtils.isEmpty(accountTaskRelPos)) {
                    singleTaskExecStatDto.setAccountCount(0);
                    continue;
                }
                singleTaskExecStatDto.setAccountCount(accountTaskRelPos.size());
                // 禁用的任务不需要跑，默认都是启用的
                if (Objects.equals(taskConfigPo.getStatus(), YesNoEnum.NO.getCode())) {
                    recordPo.setExecuteReslultDetail("任务status=0，禁用，跳过");
                    adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                    continue;
                }
                // 任务所有的账户的rel都完成了，则跳过; 更新任务的时候，粗暴更新为 no(减少扫描数据量)
                if (Objects.equals(taskConfigPo.getIsAccountRelsRunCompleted(), YesNoEnum.YES.getCode())) {
                    recordPo.setExecuteReslultDetail("任务isAccountRelsRunCompleted=1，跳过");
                    recordPo.setIsCompleted(YesNoEnum.YES.getCode());
                    adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                    continue;
                }

                List<AdTaskRuleConfigPo> taskRuleConfigPosOfTask = taskRuleConfigPosMap.get(taskConfigPo.getId());
                if (CollectionUtils.isEmpty(taskRuleConfigPosOfTask)) {
                    continue;
                }
                Map<Long, AdTaskRuleConfigPo> taskRulePoMapOfTask = taskRuleConfigPosOfTask.stream().collect(Collectors.toMap(t -> t.getId(), t -> t));

                // 获取任务的账户规则关系
                List<AdAccountTaskRuleRelPo> accountTaskRuleRelPosOfTask = adTaskRepo.queryAccountRuleRels(taskConfigPo.getId());
                if (CollectionUtils.isEmpty(accountTaskRuleRelPosOfTask)) {
                    continue;
                }

                //虽然根据accountId分组，但是其实已经根据objectId分组了
                Map<Integer, List<AdAccountTaskRuleRelPo>> objectTaskRuleRelMap = accountTaskRuleRelPosOfTask.stream().collect(Collectors.groupingBy(AdAccountTaskRuleRelPo::getAccountId));

                // 不关心项目的状态，哪怕是废弃，超预算
                Boolean isAllAccountRelsCompleted = true;
                // 处理任务的每个账户
                // 处理任务的每个对象
                Map<Integer, List<AdAccountTaskRelPo>> customerIdMap = accountTaskRelPos.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getCustomerId));
                Map<Integer, List<AdAccountTaskRelPo>> agentIdMap = accountTaskRelPos.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getAgentId));
                Map<Integer, List<AdAccountTaskRelPo>> productIdMap = accountTaskRelPos.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getProductId));
                Map<Integer, List<AdAccountTaskRelPo>> adAccountIdMap = accountTaskRelPos.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getAccountId));

                if (Objects.equals(taskConfigPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode())) {
                    isAllAccountRelsCompleted = calculateTask(taskConfigPo, customerIdMap, objectTaskRuleRelMap, recordPo, isAllAccountRelsCompleted, taskRulePoMapOfTask);
                } else if (Objects.equals(taskConfigPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode())) {
                    isAllAccountRelsCompleted = calculateTask(taskConfigPo, agentIdMap, objectTaskRuleRelMap, recordPo, isAllAccountRelsCompleted, taskRulePoMapOfTask);
                } else if (Objects.equals(taskConfigPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode())) {
                    isAllAccountRelsCompleted = calculateTask(taskConfigPo, productIdMap, objectTaskRuleRelMap, recordPo, isAllAccountRelsCompleted, taskRulePoMapOfTask);
                } else if (Objects.equals(taskConfigPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.ACCOUNT_DIMENSION.getCode())) {
                    isAllAccountRelsCompleted = calculateTask(taskConfigPo, adAccountIdMap, objectTaskRuleRelMap, recordPo, isAllAccountRelsCompleted, taskRulePoMapOfTask);

                }


                // 任务下所有的账户的rel都完成了，更新标记
                if (isAllAccountRelsCompleted && (CollectionUtils.isEmpty(param.getTaskIds()) && CollectionUtils.isEmpty(param.getAccountIds()))) {
                    adTaskRepo.updateTaskIsAccountRelsRunCompleted(taskConfigPo.getId(), YesNoEnum.YES.getCode());
                }

            }
        }

        taskExecStatDto.setTaskExecStatDtos(taskExecStatDtos);
        taskExecStatDto.setTaskCount(totalTaskCount);
        log.info("AccountTaskJob execute end...");
        XxlJobLogger.log("AccountTaskJob execute end...");
        return taskExecStatDto;
    }

    private Boolean calculateTask(AdTaskConfigPo taskConfigPo, Map<Integer, List<AdAccountTaskRelPo>> objectAccountMap, Map<Integer, List<AdAccountTaskRuleRelPo>> objectTaskRuleRelMap, AdAccountTaskRuleExecRecordPo recordPo, Boolean isAllAccountRelsCompleted, Map<Long, AdTaskRuleConfigPo> taskRulePoMapOfTask) {
        Integer completeDimension = taskConfigPo.getCompleteDimension();

        for (Map.Entry<Integer, List<AdAccountTaskRelPo>> customerAccountEntry : objectAccountMap.entrySet()) {
            Integer objectId = customerAccountEntry.getKey();

            List<AdAccountTaskRelPo> adAccountTaskRelPos = customerAccountEntry.getValue();
            List<Integer> adAccountIdList = adAccountTaskRelPos.stream().map(AdAccountTaskRelPo::getAccountId).collect(Collectors.toList());
            AdAccountTaskRelPo sampleAdAccountTaskRelPo = adAccountTaskRelPos.get(0);


            log.info("start process task account rel,taskId={}, objectId={}, isCompleted={}",
                    taskConfigPo.getId(), objectId, sampleAdAccountTaskRelPo.getIsCompleted());
            XxlJobLogger.log("start process task account rel,taskId={}, objectId={}, isCompleted={}", taskConfigPo.getId(),
                    objectId, sampleAdAccountTaskRelPo.getIsCompleted());

            List<AdAccountTaskRuleRelPo> accountTaskRuleRelPosOfAccount = objectTaskRuleRelMap.get(getObjectIdFromTaskRel(sampleAdAccountTaskRelPo));
            recordPo.setTaskId(taskConfigPo.getId());
            recordPo.setAccountId(getObjectIdFromTaskRel(sampleAdAccountTaskRelPo));
            recordPo.setExecuteReslult(TaskExecuteResultTypeEnum.SUCCESS.getCode());

            if (CollectionUtils.isEmpty(accountTaskRuleRelPosOfAccount)) {
                // 不合法数据，不会出现
                recordPo.setExecuteReslultDetail("没有AdAccountTaskRuleRelPo");
                adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                continue;
            }
            // 任务已完成，跳过
            if (Utils.isPositive(sampleAdAccountTaskRelPo.getIsCompleted())) {
                recordPo.setExecuteReslultDetail("accountTaskRelPo.getIsCompleted()=1");
                recordPo.setIsCompleted(YesNoEnum.YES.getCode());
                adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                continue;
            }
            //任务已废弃 跳过
            if (Objects.equals(sampleAdAccountTaskRelPo.getStatus(), TaskStatusEnum.DISCARD.getCode())) {
                recordPo.setExecuteReslultDetail("accountTaskRelPo is discard");
                adTaskRepo.addAccountTaskRelExecRecord(recordPo);

                continue;
            }

            // 是否需要计算条件
            Boolean needCalculateCondition = false;
            // 按时间
            if (Objects.equals(taskConfigPo.getStartType(), TaskStartTypeEnum.BY_TIME_VALUE)) {
                // todo 优化
                TaskStatusEnum taskStatusByAccountTask = adTaskHelper.getParentTaskStatusByAccountTask(sampleAdAccountTaskRelPo);
                if (Objects.nonNull(taskStatusByAccountTask) && (!taskStatusByAccountTask.getCode().equals(TaskStatusEnum.COMPLETED_RECEIVED.getCode()) && !taskStatusByAccountTask.getCode().equals(TaskStatusEnum.COMPLETED_NOT_RECEIVE.getCode()))) {
                    log.info("start process task account rel[任务未开始-按时间-父任务未完成],taskId={}, objectId={},isCompleted={}",
                            taskConfigPo.getId(), objectId, sampleAdAccountTaskRelPo.getIsCompleted());
                    recordPo.setExecuteReslultDetail("按事件-未开始-父任务未完成");
                    isAllAccountRelsCompleted = false;

                    continue;
                }

                if (Objects.nonNull(taskStatusByAccountTask) && taskStatusByAccountTask.getCode().equals(TaskStatusEnum.EXPIRED.getCode())) {
                    log.info("start process task account rel[任务未开始-按时间-父任务已过期],taskId={}, objectId={},isCompleted={}",
                            taskConfigPo.getId(), objectId, sampleAdAccountTaskRelPo.getIsCompleted());

                    adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setStatus(TaskStatusEnum.EXPIRED.getCode()));

                    adTaskRepo.updateAccountTaskRels(adAccountTaskRelPos);
                    recordPo.setExecuteReslultDetail("按事件-未开始-父任务已过期");
                    isAllAccountRelsCompleted = false;

                    continue;
                }

                // 任务已过期
                if (taskConfigPo.getEndTime() == null || taskConfigPo.getBeginTime() == null) {
                    adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setStatus(TaskStatusEnum.EXPIRED.getCode()));
                    adTaskRepo.updateAccountTaskRels(adAccountTaskRelPos);

                    log.error("start process task account rel[按时间,时间为空], taskId={}, objectId={}", taskConfigPo.getId(), sampleAdAccountTaskRelPo.getCustomerId());
                    continue;
                }
                if (taskConfigPo.getEndTime().before(Utils.getNow())) {
                    log.info("start process task account rel[任务过期-按时间],taskId={}, objectId={}, isCompleted={}",
                            taskConfigPo.getId(), objectId, sampleAdAccountTaskRelPo.getIsCompleted());
                    adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setStatus(TaskStatusEnum.EXPIRED.getCode()));

                    isAllAccountRelsCompleted = false;
                    recordPo.setExecuteReslultDetail("按时间-已过期");
                    adTaskRepo.updateAccountTaskRels(adAccountTaskRelPos);
                    adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                    continue;
                }

                // 任务未开始
                if (taskConfigPo.getBeginTime().after(Utils.getNow())) {
                    log.info("start process task account rel[任务未开始-按时间],taskId={}, accountId={}, isCompleted={}",
                            taskConfigPo.getId(), objectId, sampleAdAccountTaskRelPo.getIsCompleted());
                    isAllAccountRelsCompleted = false;
                    recordPo.setExecuteReslultDetail("按时间-未开始");
                    adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                    continue;
                }

                String parentTaskIds = taskConfigPo.getParentTaskId();
                if (StringUtils.isNotBlank(parentTaskIds)) {
                    List<Long> parentTaskIdList = Arrays.stream(parentTaskIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
                    //2024-05-21 激励券二期只有一个父任务
                    Long parentTaskId = parentTaskIdList.get(0);
                    List<AdAccountTaskRelPo> parentTaskRelList = adTaskRepo.queryAccountTaskRelsByCustomerId(parentTaskId, sampleAdAccountTaskRelPo.getCustomerId());
                    if (CollectionUtils.isEmpty(parentTaskRelList)) {
                        continue;
                    }
                    AdAccountTaskRelPo parentTaskRel = parentTaskRelList.get(0);
                    Integer parentTaskStatus = adTaskHelper.getTaskStatusByAccountTask(parentTaskRel).getCode();
                    if (Objects.equals(parentTaskStatus, TaskStatusEnum.NOT_START.getCode()) || Objects.equals(parentTaskStatus, TaskStatusEnum.EXPIRED.getCode())) {
                        continue;
                    }

                }
                // 计算指标
                needCalculateCondition = true;
            }

            // 按事件
            if (Objects.equals(taskConfigPo.getStartType(), TaskStartTypeEnum.BY_EVENT_VALUE)) {


                TaskStatusEnum parentTaskStatusByAccountTask = adTaskHelper.getParentTaskStatusByAccountTask(sampleAdAccountTaskRelPo);
                if (Objects.nonNull(parentTaskStatusByAccountTask) && (!parentTaskStatusByAccountTask.getCode().equals(TaskStatusEnum.COMPLETED_RECEIVED.getCode()) && !parentTaskStatusByAccountTask.getCode().equals(TaskStatusEnum.COMPLETED_NOT_RECEIVE.getCode()))) {
                    log.info("start process task account rel[任务未开始-按时间-父任务未完成],taskId={}, objectId={},isCompleted={}",
                            taskConfigPo.getId(), sampleAdAccountTaskRelPo.getCustomerId(), sampleAdAccountTaskRelPo.getIsCompleted());
                    recordPo.setExecuteReslultDetail("按事件-未开始-父任务未完成");
                    isAllAccountRelsCompleted = false;

                    continue;
                }

                if (Objects.nonNull(parentTaskStatusByAccountTask) && parentTaskStatusByAccountTask.getCode().equals(TaskStatusEnum.EXPIRED.getCode())) {
                    adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setStatus(TaskStatusEnum.EXPIRED.getCode()));
                    adTaskRepo.updateAccountTaskRels(adAccountTaskRelPos);
                    log.info("start process task account rel[任务未开始-按时间-父任务已过期],taskId={}, objectId={},isCompleted={}",
                            taskConfigPo.getId(), sampleAdAccountTaskRelPo.getCustomerId(), sampleAdAccountTaskRelPo.getIsCompleted());
                    recordPo.setExecuteReslultDetail("按事件-未开始-父任务已过期");
                    isAllAccountRelsCompleted = false;

                    continue;
                }

                // 任务已过期
                if (Objects.nonNull(sampleAdAccountTaskRelPo.getEndTime()) && sampleAdAccountTaskRelPo.getEndTime().before(Utils.getNow())) {
                    log.info("start process task account rel[任务过期-按时间],taskId={}, objectId={}, isCompleted={}",
                            taskConfigPo.getId(), objectId, sampleAdAccountTaskRelPo.getIsCompleted());
                    adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setStatus(TaskStatusEnum.EXPIRED.getCode()));

                    isAllAccountRelsCompleted = false;
                    recordPo.setExecuteReslultDetail("按时间-已过期");
                    adTaskRepo.updateAccountTaskRels(adAccountTaskRelPos);
                    adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                    continue;
                }

                // 任务开始事件未完成，重新计算开始事件
                if (!Utils.isPositive(sampleAdAccountTaskRelPo.getStartEventIsCompleted())) {
                    // 任务未开始，是否超过90天
                    if (Utils.getSomeDayAfter(sampleAdAccountTaskRelPo.getCtime(), notStartDaysLimit).before(Utils.getNow())) {
                        log.info("start process task account rel[任务未开始-按事件-超过90天],taskId={}, objectId={},isCompleted={}",
                                taskConfigPo.getId(), sampleAdAccountTaskRelPo.getAccountId(), sampleAdAccountTaskRelPo.getIsCompleted());
                        recordPo.setExecuteReslultDetail("按事件-未开始-超过90天");
                        adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setStatus(TaskStatusEnum.EXPIRED.getCode()));
                        adTaskRepo.updateAccountTaskRels(adAccountTaskRelPos);
                        adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                        continue;
                    }

                    TaskStartEventEnum taskStartEventEnum = TaskStartEventEnum.UNKNOWN;
                    if (Objects.equals(taskConfigPo.getStartEvent(), 0) || Objects.equals(taskConfigPo.getStartEvent(), 2) || Objects.equals(taskConfigPo.getStartEvent(), 4) || Objects.equals(taskConfigPo.getStartEvent(), 6)) {
                        taskStartEventEnum = TaskStartEventEnum.ACCOUNT_FIRST_CHARGE_CONDITION;
                    } else if (Objects.equals(taskConfigPo.getStartEvent(), 1) || Objects.equals(taskConfigPo.getStartEvent(), 3) || Objects.equals(taskConfigPo.getStartEvent(), 5) || Objects.equals(taskConfigPo.getStartEvent(), 7)) {
                        taskStartEventEnum = TaskStartEventEnum.ACCOUNT_COST;
                    }

                    TaskStartEventCalculateQueryDto taskStartEventCalculateQueryDto = TaskStartEventCalculateQueryDto.builder()
                            .taskId(taskConfigPo.getId())
                            .accountIds(adAccountIdList)
                            .taskCreateTime(sampleAdAccountTaskRelPo.getCtime())
                            .taskCompleteDimension(completeDimension)
                            .build();

                    TaskRuleConditionResultDto taskRuleConditionResultDto = startEventCalculatorFactory.queryConditionResult(taskStartEventCalculateQueryDto, taskStartEventEnum);
                    // 重新计算的开始事件仍未完成
                    if (taskRuleConditionResultDto == null || !taskRuleConditionResultDto.getIsCompleted()) {
                        isAllAccountRelsCompleted = false;
                        recordPo.setExecuteReslultDetail("按事件-重新计算仍未开始");
                        adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                        continue;
                    }

                    String parentTaskIds = taskConfigPo.getParentTaskId();
                    if (StringUtils.isNotBlank(parentTaskIds)) {
                        List<Long> parentTaskIdList = Arrays.stream(parentTaskIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
                        //2024-05-21 激励券二期只有一个父任务
                        Long parentTaskId = parentTaskIdList.get(0);
                        List<AdAccountTaskRelPo> parentTaskRelPoList = adTaskRepo.queryAccountTaskRelsByCustomerId(parentTaskId, objectId);
                        if (CollectionUtils.isEmpty(parentTaskRelPoList)) {
                            continue;
                        }
                        AdAccountTaskRelPo parentTaskRel = parentTaskRelPoList.get(0);
                        Integer parentTaskStatus = adTaskHelper.getTaskStatusByAccountTask(parentTaskRel).getCode();
                        if (Objects.equals(parentTaskStatus, TaskStatusEnum.NOT_START.getCode()) || Objects.equals(parentTaskStatus, TaskStatusEnum.EXPIRED.getCode())) {
                            continue;
                        }

                    }

                    // 记录开始时间和结束时间
                    adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setBeginTime(TimeUtils.getDayStart(Utils.getNow())));
                    adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setEndTime(calculateEndTimeForByEvent(Utils.getNow(), taskConfigPo.getPeriod())));
                    adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setStartEventIsCompleted(YesNoEnum.YES.getCode()));
                    adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setStartEventComplatedTime(Utils.getNow()));

                    adTaskRepo.updateAccountTaskRels(adAccountTaskRelPos);
                    log.info("start process task account rel[任务开始事件刚完成],taskId={}, objectId={},isCompleted={}",
                            taskConfigPo.getId(), sampleAdAccountTaskRelPo.getCustomerId(), sampleAdAccountTaskRelPo.getIsCompleted());
                }

                // 开始事件已完成，计算指标
                needCalculateCondition = true;
            }

            if (!needCalculateCondition) {
                recordPo.setExecuteReslultDetail("不需要计算指标");
                adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                continue;
            }

            // 遍历任务下账户的规则
            // 记录任务下的所有规则是否完成
            for (AdAccountTaskRuleRelPo adAccountTaskRuleRelPo : accountTaskRuleRelPosOfAccount) {
                log.info("start process rule account rel,taskId={}, objectId={}, ruleId={},isCompleted={}",
                        taskConfigPo.getId(), sampleAdAccountTaskRelPo.getCustomerId(), adAccountTaskRuleRelPo.getRuleId(), adAccountTaskRuleRelPo.getIsCompleted());
                XxlJobLogger.log("start process rule account rel,taskId={}, objectId={}, ruleId={},isCompleted={}",
                        taskConfigPo.getId(), sampleAdAccountTaskRelPo.getAccountId(), adAccountTaskRuleRelPo.getRuleId(), adAccountTaskRuleRelPo.getIsCompleted());

                recordPo.setTaskId(taskConfigPo.getId());
                recordPo.setAccountId(sampleAdAccountTaskRelPo.getCustomerId());
                recordPo.setRuleId(adAccountTaskRuleRelPo.getRuleId());

                AdTaskRuleConfigPo adTaskRuleConfigPo = taskRulePoMapOfTask.get(adAccountTaskRuleRelPo.getRuleId());
                if (adTaskRuleConfigPo == null) {
                    continue;
                }
                // 指标已经完成
                if (Utils.isPositive(adAccountTaskRuleRelPo.getIsCompleted())) {
                    recordPo.setExecuteReslultDetail("该规则已经完成");
                    adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                    continue;
                }

                // 计算指标(策略模式)
                TaskRuleConditionEnum taskRuleConditionEnum = TaskRuleConditionEnum.getByCode(adTaskRuleConfigPo.getConditionCode());

                Timestamp parentTaskCompletedTime = sampleAdAccountTaskRelPo.getParentTaskCompletedTime();

                Timestamp realBeginTime = sampleAdAccountTaskRelPo.getBeginTime();
                if (Objects.nonNull(parentTaskCompletedTime) && parentTaskCompletedTime.after(sampleAdAccountTaskRelPo.getBeginTime())) {
                    realBeginTime = parentTaskCompletedTime;
                }

                TaskRuleConditionQueryDto taskRuleConditionQueryDto = TaskRuleConditionQueryDto.builder()
                        .taskId(taskConfigPo.getId())
                        .ruleId(adAccountTaskRuleRelPo.getRuleId())
                        .value(adTaskRuleConfigPo.getValue())
                        .hasCompleteCount(adAccountTaskRuleRelPo.getCompletedValue())
                        .beginTime(Utils.getBeginOfDay(realBeginTime))
                        .endTime(Utils.getEndOfDay(sampleAdAccountTaskRelPo.getEndTime()))
                        .taskRuleConditionEnum(taskRuleConditionEnum)
                        .ocpcTarget(adTaskRuleConfigPo.getConvTarget())
                        .accountIds(adAccountIdList)
                        .completeDimension(completeDimension)
                        .build();
                log.info("queryConditionResult, param={}", JSON.toJSONString(taskRuleConditionQueryDto));
                TaskRuleConditionResultDto taskRuleConditionResultDto =
                        TaskRuleConditionResultDto.builder().isCompleted(false).completeCount(0L).build();

                recordPo.setExecuteParam(JSON.toJSONString(taskRuleConditionQueryDto));
                try {
                    taskRuleConditionResultDto = taskRuleConditionQuerierFactory.queryConditionResult(taskRuleConditionQueryDto, taskRuleConditionEnum);
                    adAccountTaskRuleRelPo.setExecuteReslult(TaskExecuteResultTypeEnum.SUCCESS.getCode());
                } catch (Exception e) {
                    log.error("queryConditionResult, error, param={}, e={}", JSON.toJSONString(taskRuleConditionQueryDto), e);
                    // 记录是否执行成功
                    recordPo.setExecuteReslult(TaskExecuteResultTypeEnum.FAIL.getCode());
                    adAccountTaskRuleRelPo.setExecuteReslult(TaskExecuteResultTypeEnum.FAIL.getCode());
                    taskRuleConditionResultDto.setErrorMsg(e.getMessage());
                }

                recordPo.setAccountId(adAccountTaskRuleRelPo.getAccountId());
                recordPo.setTaskId(adAccountTaskRuleRelPo.getTaskId());
                recordPo.setRuleId(adAccountTaskRuleRelPo.getRuleId());
                recordPo.setValue(adAccountTaskRuleRelPo.getValue());

                // 任务规则完成了
                if (Objects.nonNull(taskRuleConditionResultDto) && taskRuleConditionResultDto.getIsCompleted()) {
                    // 更新结果
                    adAccountTaskRuleRelPo.setIsCompleted(YesNoEnum.YES.getCode());
                    adAccountTaskRuleRelPo.setMtime(Utils.getNow());
                    adAccountTaskRuleRelPo.setCompletedValue(String.valueOf(taskRuleConditionResultDto.getCompleteCount()));

                    // 执行结果 detail
                    AccountTaskRuleExeResultDetailDto detailDto = AccountTaskRuleExeResultDetailDto.builder().completeTime(Utils.getTimestamp2String(Utils.getNow())).resultDto(taskRuleConditionResultDto).build();
                    adAccountTaskRuleRelPo.setExecuteReslultDetail(JSON.toJSONString(detailDto));
                    adTaskRepo.updateAccountTaskRuleRel(adAccountTaskRuleRelPo);

                    Attributes attributesTotal = Attributes.of(AttributeKey.stringKey(count_type), count_type_api_coupon,
                            AttributeKey.stringKey(count_type_APIName), count_type_api_coupon,
                            AttributeKey.stringKey(a_operator), count_type_op_coupon_job_rule_complete
                    );
                    customMetrics.count(1, attributesTotal);
                }
                // 未完成
                else {
                    isAllAccountRelsCompleted = false;

                    // 更新结果
                    adAccountTaskRuleRelPo.setIsCompleted(YesNoEnum.NO.getCode());
                    adAccountTaskRuleRelPo.setCompletedValue(String.valueOf(taskRuleConditionResultDto.getCompleteCount()));

                    // 执行结果 detail
                    AccountTaskRuleExeResultDetailDto detailDto = AccountTaskRuleExeResultDetailDto.builder().completeTime(Utils.getTimestamp2StringBySecond(Utils.getNow())).resultDto(taskRuleConditionResultDto).build();
                    adAccountTaskRuleRelPo.setExecuteReslultDetail(JSON.toJSONString(detailDto));
                    adTaskRepo.updateAccountTaskRuleRel(adAccountTaskRuleRelPo);
                }

                recordPo.setIsCompleted(adAccountTaskRuleRelPo.getIsCompleted());
                recordPo.setCompletedValue(adAccountTaskRuleRelPo.getCompletedValue());
                recordPo.setExecuteReslult(adAccountTaskRuleRelPo.getExecuteReslult());
                recordPo.setExecuteReslultDetail(JSON.toJSONString(taskRuleConditionResultDto));
                // 记录执行历史
                adTaskRepo.addAccountTaskRelExecRecord(recordPo);
                adTaskRepo.updateAccountTaskRels(adAccountTaskRelPos);
            }

            List<AdAccountTaskRuleRelPo> accountTaskRuleRelPos = adTaskRepo.queryAccountTaskRuleRel(objectId, sampleAdAccountTaskRelPo.getTaskId());
            // 账户任务下所有规则完成，更新账户任务层级 不为空
            if (!CollectionUtils.isEmpty(accountTaskRuleRelPos) && accountTaskRuleRelPos.stream().allMatch(t -> Objects.equals(t.getIsCompleted(), YesNoEnum.YES.getCode()))) {
                adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setIsCompleted(YesNoEnum.YES.getCode()));
                adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setMtime(Utils.getNow()));

                if (Objects.equals(sampleAdAccountTaskRelPo.getIsCouponReceived(), 0)) {
                    adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setStatus(TaskStatusEnum.COMPLETED_NOT_RECEIVE.getCode()));
                    adTaskRepo.updateAccountTaskRels(adAccountTaskRelPos);
                }
                adTaskHelper.updateParentCompletedTime(sampleAdAccountTaskRelPo);
                // 发送站内信通知
                try {
                    log.info("sendTaskCompleteMessage, 完成, taskId={}, objectId={}", sampleAdAccountTaskRelPo.getTaskId(), sampleAdAccountTaskRelPo.getCustomerId());
                    bsiMessageSender.batchSendTaskCompleteMessage(taskConfigPo.getId(), adAccountIdList);
                } catch (Exception e) {
                    log.error("sendTaskCompleteMessage, error, taskId={}, objectId={}",
                            sampleAdAccountTaskRelPo.getTaskId(), sampleAdAccountTaskRelPo.getAccountId(), e);
                }

                Attributes attributesTotal = Attributes.of(AttributeKey.stringKey(count_type), count_type_api_coupon,
                        AttributeKey.stringKey(count_type_APIName), count_type_api_coupon,
                        AttributeKey.stringKey(a_operator), count_type_op_coupon_job_task_complete,
                        AttributeKey.stringKey("taskId"), taskConfigPo.getId() + ""
                );
                customMetrics.count(1, attributesTotal);
            } else {
                adAccountTaskRelPos.forEach(adAccountTaskRelPo -> adAccountTaskRelPo.setStatus(TaskStatusEnum.PROCESSING.getCode()));
                adTaskRepo.updateAccountTaskRels(adAccountTaskRelPos);
            }


        }
        return isAllAccountRelsCompleted;
    }

    public static Param parseParam(String s) {
        Param param = new Param();
        param.setTaskIds(Collections.EMPTY_LIST);
        param.setAccountIds(Collections.EMPTY_LIST);
        if (StringUtils.isNotEmpty(s)) {
            param = JSON.parseObject(s, Param.class);
        }
        return param;
    }

    /**
     * 计算结束时间
     *
     * @param now
     * @param period
     * @return
     */
    private Timestamp calculateEndTimeForByEvent(Timestamp now, Integer period) {

        Integer days = period * 7;
        return Utils.getSomeDayAfter(now, days);
    }

    private Integer getObjectIdFromTaskRel(AdAccountTaskRelPo adAccountTaskRelPo) {
        Integer completeDimension = adAccountTaskRelPo.getCompleteDimension();
        if (Objects.equals(completeDimension, TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode())) {
            return adAccountTaskRelPo.getCustomerId();
        } else if (Objects.equals(completeDimension, TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode())) {
            return adAccountTaskRelPo.getProductId();
        } else if (Objects.equals(completeDimension, TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode())) {
            return adAccountTaskRelPo.getAgentId();
        } else {
            return adAccountTaskRelPo.getAccountId();
        }
    }
}



