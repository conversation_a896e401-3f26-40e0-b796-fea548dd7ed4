package com.bilibili.adp.advertiser.task.job.budget;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.account.LauAccountService;
import com.bilibili.adp.cpc.repo.NextBudgetRepo;
import com.bilibili.adp.launch.biz.pojo.LauAccountNextdayBudgetPo;
import com.bilibili.mas.common.utils.MasCatUtils;
import com.bilibili.report.platform.api.dto.MinAndMaxId;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/5 20:21
 */
@Slf4j
@Component
public class AccountNextBudgetRefresher {

    @Autowired
    private LauAccountService lauAccountService;
    @Autowired
    private NextBudgetRepo nextBudgetRepo;

    public void refreshAccountNextdayBudget(List<Integer> accountIds, Timestamp timestamp) {
        if (timestamp == null) {
            timestamp = Utils.getNow();
        }

        Timestamp finalTimestamp = timestamp;
        MasCatUtils.newTransaction("JOB", "refreshAccountNextdayBudget", transaction -> {
            // 获取设置了次日预算的最小和最大id
            MinAndMaxId minAndMaxId = nextBudgetRepo.queryAccountNextdayBudgetMaxIdInfo(accountIds, finalTimestamp);
            log.info("refreshAccountNextdayBudget, minAndMaxId:{}, timestamp:{}", JSON.toJSONString(minAndMaxId), finalTimestamp);

            if (minAndMaxId == null || minAndMaxId.getMinId() == null) {
                XxlJobLogger.log("refreshAccountNextdayBudget, minAndMaxId:{}, timestamp:{}", JSON.toJSONString(minAndMaxId), finalTimestamp);
                return;
            }

            Integer fromId = minAndMaxId.getMinId().intValue();
            Integer batchSize = LauAccountService.REFRESH_ACCOUNT_NEXTDAY_BUDGET_SIZE;
            Integer toId = fromId;
            Integer maxId = minAndMaxId.getMaxId().intValue();
            Integer batchIndex = 0;
            Integer totalUpdateCount = 0;
            while (true) {
                toId += batchSize;
                batchIndex++;
                // 根据 id 范围获取一批
                List<LauAccountNextdayBudgetPo> nextdayBudgetPos = nextBudgetRepo.queryTodayAccountNextBudgetList(accountIds, finalTimestamp, fromId, toId);
                log.info("refreshAccountNextdayBudget, queryList, batchIndex:{},fromId:{},toId:{},进度:({}/{}),size:{}",
                        batchIndex, fromId, toId, toId, maxId, nextdayBudgetPos.size());
                XxlJobLogger.log("refreshAccountNextdayBudget, queryList, batchIndex:{},fromId:{},toId:{},进度:({}/{}),size:{}",
                        batchIndex, fromId, toId, toId, maxId, nextdayBudgetPos.size());

                if (CollectionUtils.isEmpty(nextdayBudgetPos)) {
                    // 是否跳号
                    if (toId >= maxId) {
                        log.info("refreshAccountNextdayBudget, queryList break, batchIndex:{},fromId:{},toId:{},进度:" +
                                        "({}/{}),size:{}",
                                batchIndex, fromId, toId, toId, maxId, nextdayBudgetPos.size());
                        XxlJobLogger.log("refreshAccountNextdayBudget, queryList break, batchIndex:{},fromId:{},toId:{}," +
                                        "进度:({}/{}),size:{},totalUpdateCount:{}",
                                batchIndex, fromId, toId, toId, maxId, nextdayBudgetPos.size(), totalUpdateCount);
                        break;
                    }
                    fromId = toId;
                    continue;
                }
                fromId = toId;

                // 更新
                lauAccountService.batchUpdateAccountNextdayBudget(nextdayBudgetPos);
                totalUpdateCount += nextdayBudgetPos.size();
                log.info("refreshAccountNextdayBudget, batchUpdateAccountNextdayBudget, batchIndex:{},fromId:{},toId:{},进度:({}/{}),size:{}",
                        batchIndex, fromId, toId, toId, maxId, nextdayBudgetPos.size());
                XxlJobLogger.log("refreshAccountNextdayBudget, batchUpdateAccountNextdayBudget, batchIndex:{},fromId:{},toId:{},进度:({}/{}),size:{}",
                        batchIndex, fromId, toId, toId, maxId, nextdayBudgetPos.size());
            }
        });
    }
}
