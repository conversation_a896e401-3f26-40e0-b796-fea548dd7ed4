package com.bilibili.adp.advertiser.portal.webapi.v6.creative.bos.save;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SanlianSaveCreativeLandingPageBo
 * <AUTHOR>
 * @Date 2024/3/13 11:46 上午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SanlianCreativeLandingPageBo {
    private String url;
    private String mgkPageId;
    private String mgkPageGroupId;
    private String thirdPartyPageGroupId;
}
