package com.bilibili.adp.advertiser.helper.databus;

import java.util.Arrays;
import java.util.List;

public class DatabusConstant {
    public final static String ACTION_INSERT = "insert";
    public final static String ACTION_UPDATE = "update";
    public final static String ACCOUNT_ZHUANGXIAN_PREFIX = "account:zhuangxian:";


    public final static String ACCOUNT_BATCH_NO_DPA_PREFIX = "batch:no_dpa:";


    public final static String ACCOUNT_TARGET_PACKAGE_SHARE_PREFIX = "ad:target_package:share:";


    public final static String ACCOUNT_ID = "account_id";
    public final static String BUDGET = "budget";

    // 1-现金 2-返货 3-专项返货
    public final static String FINANCE_TYPE = "finance_type";

    public final static String TRADE_TYPE = "trade_type";
    public final static Integer COST = 0;

    public final static Integer CASH = 1;
    public final static Integer RED_PACKET = 2;
    public final static Integer SPECIAL_RED_PACKET = 3;
    public final static List<Integer> FINANCE_TYPE_LIST = Arrays.asList(CASH, RED_PACKET, SPECIAL_RED_PACKET);

    // 正负 1正，2负
    public final static String PLUS_MINUS = "plus_minus";
    public final static int PLUS = 1;
    public final static int MINUS = 2;

    public final static String BALANCE_AMOUNT = "balance_amount";
    public final static String AMOUNT = "amount";
    public final static String BELONG_DATE = "belong_date";

    public final static Integer IS_SEND = 1;
    public final static Integer NOT_SEND = 0;


    public static final String BALANCE_EMPTY_TITLE = "{0},您管理的账号{1}已无余额，请及时充值";
    public static final String BALANCE_EMPTY_MESSAGE = "{0},您管理的账号{1}已无余额，点击可进入账号查看详情";
    public static final String BUDGET_OVER_FLOW_TITLE = "{0},您管理的账号{1}消耗已达到账户日预算上限，请及时调整";
    public static final String BUDGET_OVER_FLOW_MESSAGE = "{0},您管理的账号{1}消耗已达到账户日预算上限，点击可进入账号查看详情";

    public final static String FLY_URL = "http://f.bilibili.com/#/login?account_id={0}&userid={1}";
    public final static String XIAOGUO_URL = "http://cm.bilibili.com/ad/#/login?account_id={0}&userid={1}";
    public final static String SANLIAN_URL = "http://ad.bilibili.com/#/login?account_id={0}&userid={1}";
}
