package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LauUpdateAccountBudgetReqVo {

    private BigDecimal budget;

    private Integer budget_limit_type;

    private Integer effect_type;

    @ApiModelProperty("次日预算是否每日重复，1:是,0:否")
    private Integer is_repeat;

}
