package com.bilibili.adp.advertiser.portal.webapi.v6.resource.dynamic;

import com.bapis.ad.mgk.audit.DynamicBlueLinkAuditServiceGrpc;
import com.bapis.ad.mgk.audit.DynamicBlueLinkDetails;
import com.bapis.ad.mgk.audit.DynamicReq;
import com.bapis.ad.pandora.resource.CreativeDynamicSource;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.account.converter.InfoConverter;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.account.vo.InfoVo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.archive.bos.SanlianArchiveCampaignUnitInfoInputBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.dynamic.bos.SanlianDynamicBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mapper.ResourceArchiveMapper;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mapper.ResourceDynamicMapper;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.account.InfoBo;
import com.bilibili.adp.cpc.biz.bos.dynamic.SanlianDynamicExtraBo;
import com.bilibili.adp.cpc.biz.services.account.EffectAdMidService;
import com.bilibili.adp.cpc.biz.services.account.bos.EffectAdMidQueryBo;
import com.bilibili.adp.cpc.biz.services.archive.bos.SanlianResHandleCampaignUnitInfoBo;
import com.bilibili.adp.cpc.biz.services.dynamic.DynamicQuerierProc;
import com.bilibili.adp.cpc.biz.services.dynamic.DynamicService;
import com.bilibili.adp.cpc.biz.services.dynamic.FlyDynamicService;
import com.bilibili.adp.cpc.biz.services.dynamic.bos.OpusCardBo;
import com.bilibili.adp.cpc.biz.services.goods.SanlianCreativeGoodsContentService;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveReserveService;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveReservationInfoBo;
import com.bilibili.adp.cpc.dto.MidDynamicQueryDto;
import com.bilibili.adp.cpc.dto.NewDynamicDto;
import com.bilibili.adp.cpc.enums.account.ContentType;
import com.bilibili.adp.cpc.enums.archive.ArchiveAuthStatusEnum;
import com.bilibili.adp.v6.resource.dynamic.GeneralDynamicService;
import com.bilibili.adp.v6.resource.dynamic.bos.GeneralDynamicAuthInfoBo;
import com.bilibili.adp.v6.resource.dynamic.constatns.DynamicType;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.google.common.collect.Lists;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.vavr.control.Try;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName SanlianDynamicController
 * <AUTHOR>
 * @Date 2024/4/2 4:58 下午
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/web_api/v6/resource/sanlian/dynamic")
@Api(value = "/v6/resource/sanlian/dynamic", tags = {"动态", "v6"})
public class SanlianDynamicController extends BasicController {

    private static final Logger logger = LoggerFactory.getLogger(SanlianDynamicController.class);

    @Autowired
    private SanlianCreativeGoodsContentService sanlianCreativeGoodsContentService;

    @Autowired
    private FlyDynamicService flyDynamicService;

    @Autowired
    private EffectAdMidService effectAdMidService;

    @Autowired
    private DynamicService dynamicService;

    @Autowired
    private GeneralDynamicService generalDynamicService;

    @Autowired
    private DynamicBlueLinkAuditServiceGrpc.DynamicBlueLinkAuditServiceBlockingStub blueLinkAuditServiceBlockingStub;

    @Autowired
    private DynamicQuerierProc dynamicQuerierProc;

    @Autowired
    private AdpCpcLiveReserveService adpCpcLiveReserveService;

    //todo 这个时间比较特殊，是动态上线时间，只能通过创建时间来区分历史数据，历史数据仅仅代表mid下稿件的授权，新增数据是稿件+动态的授权
    @Value("${dynamic.online.date.time:2023-10-17 00:00:00}")
    private String dynamicOnlineDateTimeString;

    @ApiOperation(value = "动态列表")
    @RequestMapping(value = "/general_dynamic/list_with_sharing")
    public Response<Pagination<List<SanlianDynamicBo>>> listWithSharingDynamic(
            @ApiIgnore Context context,
            @RequestParam(value = "auth_source", required = false) Integer authSource,
            @RequestParam(value = "unit_id", required = false) Integer unitId,
            @ApiParam("推广目的") @RequestParam(value = "promotion_purpose_type", required = false) Integer promotionPurposeType,
            @ApiParam("推广内容") @RequestParam(value = "promotion_content_type", required = false) Integer promotionContentType,
            @ApiParam("优化目标") @RequestParam(value = "cpa_target", required = false) Integer cpaTarget,
            @RequestParam(value = "dynamic_id", required = false) Long dynamicId,
            @RequestParam(value = "mid", required = false) Long mid,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "20") Integer size
    ) {
        Assert.isTrue(size <= 100, "分页大小超过阈值");
        PageResult<GeneralDynamicAuthInfoBo> generalDynamicAuthInfoBoPageResult = generalDynamicService.list(
                context.getAccountId(), authSource,
                ArchiveAuthStatusEnum.AUTHORIZED.getCode(), mid, page,
                size, dynamicId);
        List<Long> dynamicIds = generalDynamicAuthInfoBoPageResult.getRecords().stream()
                .map(r -> r.getDynamicInfo().getDynamicId())
                .collect(Collectors.toList());
        SanlianArchiveCampaignUnitInfoInputBo inputBo = SanlianArchiveCampaignUnitInfoInputBo.builder()
                .promotionPurposeType(promotionPurposeType)
                .promotionContentType(promotionContentType)
                .cpaTarget(cpaTarget)
                .build();
        SanlianResHandleCampaignUnitInfoBo handleCampaignUnitInfoBo =
                ResourceArchiveMapper.generateHandleCampaignUnitInfoBo(inputBo);
        List<SanlianDynamicExtraBo> goodsContentBoList =
                sanlianCreativeGoodsContentService.generateDynamicExtraBoList(
                        dynamicIds, unitId, handleCampaignUnitInfoBo, context.getAccountId(), 0);
        Map<Long, SanlianDynamicExtraBo> dynamicGoodsContentIdMap = goodsContentBoList.stream()
                .collect(Collectors.toMap(SanlianDynamicExtraBo::getDynamicId, Function.identity()));
        final Map<Long, OpusCardBo> opusCardMap = dynamicService.fetchOpusCardMap(dynamicIds);

        List<List<Long>> partitions = Lists.partition(dynamicIds, 20);
        Map<Long, Long> dynId2Sid = new HashMap<>();
        partitions.forEach(partition -> {
            // 该接口有预约 id，但是没有 content，需要动态升级接口
            List<NewDynamicDto> newDynamicDtos = dynamicQuerierProc.fetchDynamicInfoList(partition);
            dynId2Sid.putAll(newDynamicDtos.stream()
                    .filter(t -> t != null && t.getSid() != null)
                    .collect(Collectors.toMap(t -> Long.valueOf(t.getDynamicId()), NewDynamicDto::getSid)));
        });
        Map<Long, LiveReservationInfoBo> reservationInfoBoMap = adpCpcLiveReserveService.batchQueryBiliLiveReserveInfo(
                dynId2Sid.values().stream().filter(Utils::isPositive).collect(Collectors.toList()));
        List<SanlianDynamicBo> vos = generalDynamicAuthInfoBoPageResult.getRecords().stream()
                .map(dto -> {
                    long dyId = dto.getDynamicInfo().getDynamicId();
                    OpusCardBo opusCardBo = opusCardMap.get(dyId);
                    if (Objects.nonNull(opusCardBo) && Objects.equals(opusCardBo.getMusicId(), 0L)) {
                        opusCardBo = null;
                    }
                    SanlianDynamicExtraBo dynamicExtraBo = dynamicGoodsContentIdMap.getOrDefault(
                            dyId, new SanlianDynamicExtraBo());
                    Long sid = dynId2Sid.get(dyId);
                    return SanlianDynamicBo.builder()
                            .dynamicId(String.valueOf(dyId))
                            .dynamicContent(dto.getDynamicInfo().getContent())
                            .dynamicImages(dto.getDynamicInfo().getPictures())
                            .mid(String.valueOf(dto.getDynamicInfo().getMid()))
                            .face(dto.getDynamicInfo().getFace())
                            .nickname(dto.getDynamicInfo().getName())
                            .pubTime(new Timestamp(dto.getDynamicInfo().getCtime()))
                            .dynamicLink("https://t.bilibili.com/" + dyId)
                            .dynamicSkipLink(dto.getDynamicInfo().getJumpUri())
                            .sid(sid)
                            .isCidDynamicInvalid(dynamicExtraBo.getIsCidDynamicInvalid())
                            .isGoods(dynamicExtraBo.getIsGoodsContent())
                            .source(CreativeDynamicSource.DYNAMIC_SOURCE_ADHOC_DYNAMIC_VALUE)
                            .type(Math.toIntExact(dto.getDynamicInfo().getType()))
                            .canBeAdvertised(
                                    DynamicType.CAN_BE_ADVERTISED_INT_TYPES.contains(
                                            Math.toIntExact(dto.getDynamicInfo().getType())))
                            .liveReservationInfoBo(ResourceDynamicMapper.MAPPER.toDynamicLiveReserveInfoBo(
                                    reservationInfoBoMap.get(sid)))
                            .musicId(opusCardBo == null ? "" : String.valueOf(opusCardBo.getMusicId()))
                            .build();
                }).collect(Collectors.toList());
        vos = this.filterOutAuditNotPassDynamics(vos);

        return Response.SUCCESS(new Pagination<>(page, generalDynamicAuthInfoBoPageResult.getTotal(), vos));
    }

    @ApiOperation(value = "账号可选动态(20条)")
    @RequestMapping(value = "/account/dynamics", method = RequestMethod.GET)
    public Response<List<SanlianDynamicBo>> accountDynamics(
            @ApiIgnore Context context,
            @RequestParam(value = "unit_id", required = false) Integer unitId,
            @ApiParam("推广目的") @RequestParam(value = "promotion_purpose_type", required = false) Integer promotionPurposeType,
            @ApiParam("推广内容") @RequestParam(value = "promotion_content_type", required = false) Integer promotionContentType,
            @ApiParam("优化目标") @RequestParam(value = "cpa_target", required = false) Integer cpaTarget,
            @RequestParam(value = "kw", required = false) @ApiParam("key word") String kw,
            @RequestParam(value = "need_goods_info", required = false, defaultValue = "0") Integer needGoodsInfo) throws ServiceException {
        List<NewDynamicDto> dtos = flyDynamicService.getFirst20DynamicList(context.getAccountId(), kw, false);
        List<Long> dynamicIds = dtos.stream()
                .map(NewDynamicDto::getDynamicId)
                .filter(id -> !StringUtils.isEmpty(id))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        SanlianArchiveCampaignUnitInfoInputBo inputBo = SanlianArchiveCampaignUnitInfoInputBo.builder()
                .promotionPurposeType(promotionPurposeType)
                .promotionContentType(promotionContentType)
                .cpaTarget(cpaTarget)
                .build();
        SanlianResHandleCampaignUnitInfoBo handleCampaignUnitInfoBo =
                ResourceArchiveMapper.generateHandleCampaignUnitInfoBo(inputBo);
        List<SanlianDynamicExtraBo> goodsContentBoList =
                sanlianCreativeGoodsContentService.generateDynamicExtraBoList(
                        dynamicIds, unitId, handleCampaignUnitInfoBo, context.getAccountId(), needGoodsInfo);
        Map<Long, SanlianDynamicExtraBo> dynamicGoodsContentIdMap = goodsContentBoList.stream()
                .collect(Collectors.toMap(SanlianDynamicExtraBo::getDynamicId, Function.identity()));
        final Map<Long, OpusCardBo> opusCardMap = dynamicService.fetchOpusCardMap(dynamicIds);
        List<SanlianDynamicBo> vos = dtos.stream()
                .map(dto -> {
                    final long dynamicId = Long.parseLong(dto.getDynamicId());
                    OpusCardBo opusCardBo = opusCardMap.get(dynamicId);
                    if (Objects.nonNull(opusCardBo) && Objects.equals(opusCardBo.getMusicId(), 0L)) {
                        opusCardBo = null;
                    }
                    return ResourceDynamicMapper.MAPPER.toDynamicBo(dto, dynamicGoodsContentIdMap.get(dynamicId),
                            opusCardBo);
                }).collect(Collectors.toList());

        vos = this.filterOutAuditNotPassDynamics(vos);

        return Response.SUCCESS(vos);
    }

    @ApiOperation(value = "获取 动态可选mid")
    @GetMapping(value = "/infos")
    public Response<List<InfoVo>> infos(@ApiIgnore Context context,
            @ApiParam("page") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @ApiParam("page_size") @RequestParam(value = "page_size", required = false) Integer pageSize
    ) {
        final LocalDateTime dynamicOnlineDateTime = LocalDateTime.parse(dynamicOnlineDateTimeString,
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        EffectAdMidQueryBo queryBo = EffectAdMidQueryBo.builder()
                .accountId(context.getAccountId())
                .contentType(ContentType.Dynamic)
                .goeMtime(Timestamp.valueOf(dynamicOnlineDateTime))
                .page(page)
                .pageSize(pageSize)
                .build();
        final List<InfoBo> infos = effectAdMidService.infos(queryBo);
        return Response.SUCCESS(infos
                .stream()
                .map(InfoConverter.MAPPER::infoBo2InfoVo)
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "获取 mid 下动态列表")
    @RequestMapping(value = "/mid/dynamics", method = RequestMethod.GET)
    public Response<Pagination<List<SanlianDynamicBo>>> midDynamics(@ApiIgnore Context context,
                                                                    @RequestParam(value = "mid") @ApiParam("mid") Long mid,
                                                                    @RequestParam(value = "unit_id", required = false) @ApiParam("单元id") Integer unitId,
                                                                    @ApiParam("推广目的") @RequestParam(value = "promotion_purpose_type", required = false) Integer promotionPurposeType,
                                                                    @ApiParam("推广内容") @RequestParam(value = "promotion_content_type", required = false) Integer promotionContentType,
                                                                    @ApiParam("优化目标") @RequestParam(value = "cpa_target", required = false) Integer cpaTarget,
                                                                    @RequestParam(value = "keywords", required = false) @ApiParam("keywords") String keywords,
                                                                    @RequestParam(value = "need_goods_info", required = false, defaultValue = "0") Integer needGoodsInfo,
                                                                    @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                    @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize) {
        Assert.isTrue(pageSize <= 50, "单次最大查询50条");

        MidDynamicQueryDto requestDto = MidDynamicQueryDto.builder()
                .accountId(context.getAccountId())
                .mid(mid)
                .keywords(keywords)
                .needGoodsContent(false)
                .page(page)
                .pageSize(pageSize)
                .build();
        PageResult<NewDynamicDto> dynamicDtoPageResult = flyDynamicService.midDynamics(requestDto);
        List<NewDynamicDto> records = dynamicDtoPageResult.getRecords();

        List<Long> dynamicIds = records.stream()
                .map(NewDynamicDto::getDynamicId)
                .filter(id -> !StringUtils.isEmpty(id))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        SanlianArchiveCampaignUnitInfoInputBo inputBo = SanlianArchiveCampaignUnitInfoInputBo.builder()
                .promotionPurposeType(promotionPurposeType)
                .promotionContentType(promotionContentType)
                .cpaTarget(cpaTarget)
                .build();
        SanlianResHandleCampaignUnitInfoBo handleCampaignUnitInfoBo =
                ResourceArchiveMapper.generateHandleCampaignUnitInfoBo(inputBo);
        List<SanlianDynamicExtraBo> goodsContentBoList =
                sanlianCreativeGoodsContentService.generateDynamicExtraBoList(
                        dynamicIds, unitId, handleCampaignUnitInfoBo, context.getAccountId(), needGoodsInfo);
        Map<Long, SanlianDynamicExtraBo> dynamicGoodsContentIdMap = goodsContentBoList.stream()
                .collect(Collectors.toMap(SanlianDynamicExtraBo::getDynamicId, Function.identity()));
        final Map<Long, OpusCardBo> opusCardMap = dynamicService.fetchOpusCardMap(dynamicIds);
        List<SanlianDynamicBo> resultBoList = records.stream()
                .map(record -> {
                    final long dynamicId = Long.parseLong(record.getDynamicId());
                    OpusCardBo opusCardBo = opusCardMap.get(dynamicId);
                    if (Objects.nonNull(opusCardBo) && Objects.equals(opusCardBo.getMusicId(), 0L)) {
                        opusCardBo = null;
                    }
                    return ResourceDynamicMapper.MAPPER.toDynamicBo(record, dynamicGoodsContentIdMap.get(dynamicId),
                            opusCardBo);
                }).collect(Collectors.toList());

        resultBoList = filterOutAuditNotPassDynamics(resultBoList);

        return Response.SUCCESS(new Pagination<>(page, dynamicDtoPageResult.getTotal(), resultBoList));
    }


    private List<SanlianDynamicBo> filterOutAuditNotPassDynamics(List<SanlianDynamicBo> dynamics) {

        return Try.of(() -> {
            Map<String, DynamicBlueLinkDetails> auditMap = Optional.ofNullable(
                            blueLinkAuditServiceBlockingStub.getDynamicBlueLinks(
                                    DynamicReq.newBuilder()
                                            .addAllDynamicIds(
                                                    dynamics.stream()
                                                            .map(SanlianDynamicBo::getDynamicId)
                                                            .collect(Collectors.toList())
                                            )
                                            .setPage(1)
                                            .setSize(dynamics.size())
                                            .build()
                            ).getBlueLinkDetailsList()).orElse(new ArrayList<>()).stream()
                    .collect(Collectors.toMap(item -> item.getDynamicId(), item -> item, (a, b) -> b));

            return dynamics.stream()
                    .filter(dynamic -> {
                        return Optional.ofNullable(auditMap.get(dynamic.getDynamicId()))
                                .map(audit -> audit.getStatus())
                                .map(status -> {
                                    if (status.getNumber() == 1) {
                                        return true;
                                    } else {
                                        return false;
                                    }
                                }).orElse(true);
                    })
                    .collect(Collectors.toList());
        }).onFailure(t -> {
            logger.error("Fail to filterOutAuditNotPassDynamics, ", t);
        }).getOrElse(dynamics);


    }
}
