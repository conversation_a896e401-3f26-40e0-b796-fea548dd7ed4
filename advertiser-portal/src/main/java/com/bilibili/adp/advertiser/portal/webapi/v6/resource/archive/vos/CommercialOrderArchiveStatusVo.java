package com.bilibili.adp.advertiser.portal.webapi.v6.resource.archive.vos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommercialOrderArchiveStatusVo {

    @ApiModelProperty("授权次数")
    private Integer authTimes;

    @ApiModelProperty("续期次数")
    private Integer renewalTimes;

    @ApiModelProperty("稿件信息")
    private CommercialOrderArchiveInfoVo archiveInfo;
}
