package com.bilibili.adp.advertiser.portal.webapi.v6.resource.image.bos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SanlianImageUploadBo
 * <AUTHOR>
 * @Date 2024/4/5 3:05 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SanlianImageUploadBo {
    private String url;
}
