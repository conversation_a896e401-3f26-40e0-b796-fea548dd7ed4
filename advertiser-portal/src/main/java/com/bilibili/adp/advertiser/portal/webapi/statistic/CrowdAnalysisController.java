package com.bilibili.adp.advertiser.portal.webapi.statistic;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.statistic.vo.*;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.StatQueryType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.QueryCpcUnitDto;
import com.bilibili.adp.cpc.core.LaunchPersonAnalysisService;
import com.bilibili.adp.cpc.core.bos.AllStatTargetParamBo;
import com.bilibili.adp.cpc.core.bos.PersonAnalysisStatBo;
import com.bilibili.adp.cpc.core.bos.PersonAnalysisTargetStatBo;
import com.bilibili.adp.cpc.dto.CpcLightUnitDto;
import com.bilibili.adp.cpc.enums.*;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.campaign.dto.QueryCpcCampaignDto;
import com.bilibili.adp.v6.report.CrowdAnalysisExportService;
import com.bilibili.adp.v6.report.bo.ExportAnalysisExcelBo;
import com.bilibili.adp.v6.report.bo.ExportProgramedCreativeAnalysisBo;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.adp.web.framework.exception.WebApiExceptionCode;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人群分析控制器。
 *
 * <AUTHOR>
 * @since 2018-01-10
 **/
@Slf4j
@Controller
@RequestMapping("/web_api/v1/analysis/crowd")
@Api(value = "/crowd/analysis", description = "ES人群分析")
public class CrowdAnalysisController extends BasicController {
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private ICpcUnitService cpcUnitService;
    @Autowired
    private LaunchPersonAnalysisService launchPersonAnalysisService;
    @Autowired
    private CrowdAnalysisExportService crowdAnalysisExportService;

    @ApiOperation(value = "基本人群分析图表")
    @RequestMapping(value = "/chart", method = RequestMethod.GET)
    @ResponseBody
    public Response<BasicAllCrowdAnalysisVo> getBasicCrowdAnalysisVo(
            @RequestParam("from_time") long fromTime,
            @RequestParam("to_time") long toTime,
            @RequestParam(value = "campaign_id", required = false) List<Integer> campaignIdList,
            @RequestParam(value = "unit_id", required = false) List<Integer> unitIdList,
            @RequestParam(value = "creative_id", required = false) List<Integer> creativeIdList,
            @ApiIgnore Context context) throws ServiceException {
        List<PersonAnalysisTargetStatBo> statTargetBoList = launchPersonAnalysisService.getAllPercentStatTargetsFromES(AllStatTargetParamBo.builder()
                .accountIds(Lists.newArrayList(context.getAccountId()))
                .fromDate(new Timestamp(fromTime))
                .toDate(Utils.getEndOfDay(new Timestamp(toTime)))
                .campaignIds(campaignIdList)
                .unitIds(unitIdList)
                .creativeIds(creativeIdList)
                .salesTypes(SalesType.PLATFORM_SALES_TYPES)
                .type(StatQueryType.ACCOUNT.getCode())
                .build());

        return Response.SUCCESS(buildCrowdTargetVo(statTargetBoList));
    }

    private BasicAllCrowdAnalysisVo buildCrowdTargetVo(List<PersonAnalysisTargetStatBo> statTargetBoList) {
        BasicAllCrowdAnalysisVo crowdAnalysisVo = new BasicAllCrowdAnalysisVo();
        Map<String, List<PersonAnalysisTargetStatBo>> statTargetMap = statTargetBoList.stream()
                .collect(Collectors.groupingBy(PersonAnalysisTargetStatBo::getTargetType, Collectors.toList()));

        // 年龄
        List<PersonAnalysisTargetStatBo> ageTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.AGE.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setAge(getAgeTargetStatVos(ageTargetBoList));

        // 性别
        List<PersonAnalysisTargetStatBo> genderTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.GENDER.getTargetType(), Collections.emptyList());

        crowdAnalysisVo.setGender(getGenderTargetStatVos(genderTargetBoList));

        // 设备
        List<PersonAnalysisTargetStatBo> deviceTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.DEVICE.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setDevice(getDeviceTargetStatVos(deviceTargetBoList));

        // 省份
        crowdAnalysisVo.setProvince(statTargetMap.getOrDefault(StatTargetMappings.PROVINCE.getTargetType(), Collections.emptyList())
                .stream().map(this::convertStatTargetBo2Vo).sorted(Comparator.comparing(AllTargetStatVo::getShowCount).reversed()).collect(Collectors.toList()));

        // 城市
        crowdAnalysisVo.setCity(statTargetMap.getOrDefault(StatTargetMappings.CITY.getTargetType(), Collections.emptyList())
                .stream().map(this::convertStatTargetBo2Vo).sorted(Comparator.comparing(AllTargetStatVo::getShowCount).reversed()).collect(Collectors.toList()));
        return crowdAnalysisVo;
    }

    private List<AllTargetStatVo> getAgeTargetStatVos(List<PersonAnalysisTargetStatBo> targetBoList) {
        List<AllTargetStatVo> targetStatVos = new ArrayList<>(StatTargetAge.values().length);
        for (StatTargetAge value : StatTargetAge.values()) {
            targetStatVos.add(targetBoList.stream()
                    .filter(bo -> value.getTargetValue().equals(bo.getTargetValue()))
                    .map(this::convertStatTargetBo2Vo).findFirst()
                    .orElseGet(() -> AllTargetStatVo.empty(StatTargetMappings.AGE.getTargetType(), value.getTargetName())));
        }
        return targetStatVos;
    }

    private List<AllTargetStatVo> getGenderTargetStatVos(List<PersonAnalysisTargetStatBo> targetBoList) {
        List<AllTargetStatVo> targetStatVos = new ArrayList<>(StatTargetAge.values().length);
        for (StatTargetGender value : StatTargetGender.values()) {
            targetStatVos.add(targetBoList.stream()
                    .filter(bo -> value.getTargetValue().equals(bo.getTargetValue()))
                    .map(this::convertStatTargetBo2Vo).findFirst()
                    .orElseGet(() -> AllTargetStatVo.empty(StatTargetMappings.GENDER.getTargetType(), value.getTargetName())));
        }
        return targetStatVos;
    }

    private List<AllTargetStatVo> getDeviceTargetStatVos(List<PersonAnalysisTargetStatBo> targetBoList) {
        List<AllTargetStatVo> targetStatVos = new ArrayList<>(StatTargetAge.values().length);
        for (StatTargetDevice value : StatTargetDevice.values()) {
            targetStatVos.add(targetBoList.stream()
                    .filter(bo -> value.getTargetValue().equals(bo.getTargetValue()))
                    .map(this::convertStatTargetBo2Vo).findFirst()
                    .orElseGet(() -> AllTargetStatVo.empty(StatTargetMappings.DEVICE.getTargetType(), value.getTargetName())));
        }
        return targetStatVos;
    }

    private AllTargetStatVo convertStatTargetBo2Vo(PersonAnalysisTargetStatBo targetStatBo) {
        AllTargetStatVo statVo = new AllTargetStatVo();
        BeanUtils.copyProperties(targetStatBo.getStatBo(), statVo);
        BeanUtils.copyProperties(targetStatBo.getStatPercentBo(), statVo);
        BeanUtils.copyProperties(targetStatBo, statVo);

        statVo.setTargetName(Strings.isNullOrEmpty(targetStatBo.getTargetValueDesc()) ? "其他" : targetStatBo.getTargetValueDesc());
        statVo.setClickRate(BigDecimal.ZERO);
        statVo.setShowPercent(statVo.getShowCountPercent());
        statVo.setClickPercent(statVo.getClickCountPercent());

        return statVo;
    }

    @ApiOperation(value = "基本人群分析报告导出")
    @RequestMapping(value = "/export_excel", method = RequestMethod.GET)
    public void exportExcelAnalysisVo(
            HttpServletResponse response,
            @RequestParam("from_time") long fromTime,
            @RequestParam("to_time") long toTime,
            @RequestParam(value = "campaign_id", required = false) List<Integer> campaignIdList,
            @RequestParam(value = "unit_id", required = false) List<Integer> unitIdList,
            @RequestParam(value = "creative_id", required = false) List<Integer> creativeIdList,
            @ApiIgnore Context context) throws Exception {


        try (ServletOutputStream outputStream = response.getOutputStream()) {

            ExportAnalysisExcelBo export = ExportAnalysisExcelBo.builder()
                    .accountId(context.getAccountId())
                    .fromTime(fromTime)
                    .toTime(toTime)
                    .campaignIdList(campaignIdList)
                    .unitIdList(unitIdList)
                    .creativeIdList(creativeIdList)
                    .build();

            crowdAnalysisExportService.exportExcelAnalysis(export,outputStream);

            String dateRange = StringDateParser.getDateString(new Timestamp(fromTime))
                    + "~" + StringDateParser.getDateString(Utils.getEndOfDay(new Timestamp(toTime)));
            final String fileName = "人群分析报告" + dateRange + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
        } catch (Exception e) {
            log.error("exportCrowdAnalysisReport error", e);
            throw new ServiceException(WebApiExceptionCode.SYSTEM_ERROR);
        }

        //
        //
        //final String LOG_PREFIX = "导出人群分析";
        //log.info(MessageFormat.format("{0}: 调用开始", LOG_PREFIX));
        //AllStatTargetParamBo statTargetParam = AllStatTargetParamBo.builder()
        //        .accountIds(Lists.newArrayList(context.getAccountId()))
        //        .fromDate(new Timestamp(fromTime))
        //        .toDate(Utils.getEndOfDay(new Timestamp(toTime)))
        //        .campaignIds(campaignIdList)
        //        .unitIds(unitIdList)
        //        .creativeIds(creativeIdList)
        //        .salesTypes(SalesType.PLATFORM_SALES_TYPES)
        //        .type(StatQueryType.CREATIVE.getCode())
        //        .build();
        //
        //log.info(MessageFormat.format("{0}: 调用报表准备结束", LOG_PREFIX));
        //try {
        //    List<PersonAnalysisTargetStatBo> statTargetBoList = launchPersonAnalysisService.getAllPercentStatTargetsFromES(statTargetParam, true);
        //    log.info(MessageFormat.format("{0}: 调用报表结束 -> {1}", LOG_PREFIX, statTargetBoList.size()));
        //    String dateRange = StringDateParser.getDateString(statTargetParam.getFromDate())
        //            + "~" + StringDateParser.getDateString(statTargetParam.getToDate());
        //
        //    ExportAllCrowdAnalysisVo exportAnalysisStatVo = buildAdTargetStatVo(statTargetBoList, dateRange);
        //
        //    log.info(MessageFormat.format("{0}: 组装报表结束", LOG_PREFIX));
        //
        //    ExcelWriter writer = null;
        //    final String fileName = "人群分析报告" + dateRange + ".xlsx";
        //    response.setCharacterEncoding("utf-8");
        //    try {
        //        writer = EasyExcel.write(response.getOutputStream(),ExportAllTargetStatVo.class)
        //                .build();
        //        final WriteSheet ageSheet = EasyExcel.writerSheet("年龄")
        //                .build();
        //        writer.write(exportAnalysisStatVo.getAge(), ageSheet);
        //        final WriteSheet genderSheet = EasyExcel.writerSheet("性别")
        //                .build();
        //        writer.write(exportAnalysisStatVo.getGender(), genderSheet);
        //        final WriteSheet deviceSheet = EasyExcel.writerSheet("设备")
        //                .build();
        //        writer.write(exportAnalysisStatVo.getDevice(), deviceSheet);
        //        final WriteSheet provinceSheet = EasyExcel.writerSheet("省级地域")
        //                .build();
        //        writer.write(exportAnalysisStatVo.getProvince(), provinceSheet);
        //        final WriteSheet citySheet = EasyExcel.writerSheet("市级地域")
        //                .build();
        //        writer.write(exportAnalysisStatVo.getCity(), citySheet);
        //    } finally {
        //        if (Objects.nonNull(writer)) writer.finish();
        //    }
        //    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        //    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
        //} catch (Exception ex) {
        //    response.setContentType("application/json;charset=UTF-8");
        //    log.error("人群分析报表下载发生异常", ex);
        //    String errorMsg = "生成报表错误";
        //    if (ex instanceof IllegalArgumentException){
        //        if (StringUtils.isNotBlank(ex.getMessage())) {
        //            errorMsg = ex.getMessage();
        //        }
        //    }
        //    throw new ServiceException(500, errorMsg);
        //} finally {
        //    log.info(MessageFormat.format("{0}: 调用结束", LOG_PREFIX));
        //}
    }

    private ExportAllCrowdAnalysisVo buildAdTargetStatVo(List<PersonAnalysisTargetStatBo> statTargetBoList, String dateRang) {
        if (CollectionUtils.isEmpty(statTargetBoList)) {
            return ExportAllCrowdAnalysisVo.empty();
        }

        List<Integer> campaginIds = statTargetBoList.stream().map(PersonAnalysisTargetStatBo::getCampaignId).distinct().collect(Collectors.toList());
        List<Integer> untiIds = statTargetBoList.stream().map(PersonAnalysisTargetStatBo::getUnitId).distinct().collect(Collectors.toList());

        List<CpcCampaignDto> cpcCampaignDtos = cpcCampaignService.queryCpcCampaign(QueryCpcCampaignDto.builder().campaignIds(campaginIds).build());
        Map<Integer, String> campaginNameMap = cpcCampaignDtos.stream().collect(Collectors.toMap(CpcCampaignDto::getCampaignId, CpcCampaignDto::getCampaignName));

        List<CpcLightUnitDto> cpcLightUnitDtos = cpcUnitService.queryLightUnits(QueryCpcUnitDto.builder().unitIds(untiIds).build());
        Map<Integer, String> unitNameMap = cpcLightUnitDtos.stream().collect(Collectors.toMap(CpcLightUnitDto::getUnitId, CpcLightUnitDto::getUnitName));

        ExportAllCrowdAnalysisVo crowdAnalysisVo = new ExportAllCrowdAnalysisVo();
        Map<String, List<PersonAnalysisTargetStatBo>> statTargetMap = statTargetBoList.stream()
                .collect(Collectors.groupingBy(PersonAnalysisTargetStatBo::getTargetType, Collectors.toList()));

        // 年龄
        List<PersonAnalysisTargetStatBo> ageTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.AGE.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setAge(getAdTargetStatVos(ageTargetBoList, campaginNameMap, unitNameMap, dateRang));

        // 性别
        List<PersonAnalysisTargetStatBo> genderTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.GENDER.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setGender(getAdTargetStatVos(genderTargetBoList, campaginNameMap, unitNameMap, dateRang));

        // 设备
        List<PersonAnalysisTargetStatBo> deviceTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.DEVICE.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setDevice(getAdTargetStatVos(deviceTargetBoList, campaginNameMap, unitNameMap, dateRang));

        // 省份
        List<PersonAnalysisTargetStatBo> provinceTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.PROVINCE.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setProvince(getAdTargetStatVos(provinceTargetBoList, campaginNameMap, unitNameMap, dateRang));

        // 城市
        List<PersonAnalysisTargetStatBo> cityTargetBoList = statTargetMap.getOrDefault(StatTargetMappings.CITY.getTargetType(), Collections.emptyList());
        crowdAnalysisVo.setCity(getAdTargetStatVos(cityTargetBoList, campaginNameMap, unitNameMap, dateRang));
        return crowdAnalysisVo;
    }

    private List<ExportAllTargetStatVo> getAdTargetStatVos(List<PersonAnalysisTargetStatBo> targetBoList,
                                                    Map<Integer, String> campaginNameMap,
                                                    Map<Integer, String> unitNameMap, String dateRang) {
        List<ExportAllTargetStatVo> targetVoList = new ArrayList<>(targetBoList.size());
        for (PersonAnalysisTargetStatBo bo : targetBoList) {
            ExportAllTargetStatVo statVo = new ExportAllTargetStatVo();
            statVo.setTargetName(bo.getTargetValueDesc());
            statVo.setCampaignId(bo.getCampaignId());
            statVo.setCampaignName(campaginNameMap.get(bo.getCampaignId()));
            statVo.setUnitId(bo.getUnitId());
            statVo.setUnitName(unitNameMap.get(bo.getUnitId()));
            statVo.setCreativeId(bo.getCreativeId());
            statVo.setDateRang(dateRang);

            PersonAnalysisStatBo pureBo = bo.getStatBo();
            statVo.setShowCount(pureBo.getShowCount());
            statVo.setClickCount(pureBo.getClickCount());
            statVo.setCost(pureBo.getCost());
            statVo.setOrderPlaceCount(pureBo.getOrderPlaceCount());
            statVo.setOrderPlaceAmount(pureBo.getOrderPlaceAmount());
            statVo.setUserCostCount(pureBo.getUserCostCount());
            statVo.setUserCostAmount(pureBo.getUserCostAmount());
            statVo.setGameSubscribeApiCount(pureBo.getGameSubscribeApiCount());
            statVo.setFanIncreaseCount(pureBo.getFanIncreaseCount());
            statVo.setIosAppActiveCount(pureBo.getIosAppActiveCount());
            statVo.setAndroidAppActiveCount(pureBo.getAndroidAppActiveCount());
            statVo.setAppActiveCount(pureBo.getAppActiveCount());
            statVo.setUserRegisterCount(pureBo.getUserRegisterCount());
            statVo.setFormSubmitCount(pureBo.getFormSubmitCount());
            statVo.setAndroidDownloadSuccessCount(pureBo.getAndroidDownloadSuccessCount());
            statVo.setAndroidInstallSuccessCount(pureBo.getAndroidInstallSuccessCount());
            statVo.setClueValidCount(pureBo.getClueValidCount());
            statVo.setRetentionCount(pureBo.getRetentionCount());
            statVo.setAppCallUpCount(pureBo.getAppCallUpCount());
            statVo.setLpCallUpCount(pureBo.getLpCallUpCount());
            statVo.setLpCallUpSuccessCount(pureBo.getLpCallUpSuccessCount());
            statVo.setLpCallUpSuccessStayCount(pureBo.getLpCallUpSuccessStayCount());
            statVo.setFormPaidCount(pureBo.getFormPaidCount());
            statVo.setAndroidGameCenterActivationCount(pureBo.getAndroidGameCenterActivationCount());
            statVo.setAccountSubscribeCount(pureBo.getAccountSubscribeCount());
            statVo.setPlayCount(pureBo.getPlayCount());
            statVo.setUnderBoxLinkCount(pureBo.getUnderBoxLinkCount());
            statVo.setDynamicDetailPageBrowseCount(pureBo.getDynamicDetailPageBrowseCount());
            statVo.setCommentUrlClickCount(pureBo.getCommentUrlClickCount());
            statVo.setCommentCallUpSuccessCount(pureBo.getCommentCallUpSuccessCount());

            targetVoList.add(statVo);
        }

        return targetVoList;
    }

    @ApiOperation(value = "人群分析指标列表")
    @RequestMapping(value = "/chart/columns", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<CrowdAnalysisColumnVo>> getCrowdAnalysisColumns() throws ServiceException {
        List<CrowdAnalysisColumnVo> columnVoList = Arrays.stream(CrowdAnalysisColumn.values())
                .map(column -> CrowdAnalysisColumnVo.builder()
                        .key(column.getKey())
                        .percent_key(column.getPercentKey())
                        .title(column.getTitle())
                        .build())
                .collect(Collectors.toList());

        return Response.SUCCESS(columnVoList);
    }
}
