package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo;

import com.bilibili.adp.advertiser.portal.webapi.v2.ocpx.vos.OcpxTargetVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OcpcTargetVo {
	private Integer id;
	private String name;
	private Boolean enable;
	private List<OcpxTargetVo> descendants;
}
