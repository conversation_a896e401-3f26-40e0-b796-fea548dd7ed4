package com.bilibili.adp.advertiser.portal.webapi.v6.resource.mid;

import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mid.converters.SanlianMidAuthorizedControllerConverter;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mid.vos.AuthorizedMidInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mid.vos.MidBindInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.mid.vos.MidBindReqVo;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.SystemException;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.adp.v6.enums.mid.AuthSourceEnum;
import com.bilibili.adp.v6.resource.mid.MidAuthorizeService;
import com.bilibili.adp.v6.resource.mid.bos.MidAuthInfoBo;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Pagination;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@Api(tags = {"bilibili账号授权"})
@RestController
@RequestMapping("/web_api/v6/bilibili_account")
public class SanlianMidAuthorizedController extends BaseController {
    @Autowired
    private MidAuthorizeService midAuthorizeService;

    @ApiOperation("获取授权记录列表")
    @GetMapping("/list")
    public Response<Pagination<List<AuthorizedMidInfoVo>>> list(@ApiIgnore Context context,
            @ApiParam("授权来源") @RequestParam(value = "source", required = false) Integer source,
            @ApiParam("授权范围") @RequestParam(value = "auth_mode", required = false) Integer authMode,
            @ApiParam("授权状态") @RequestParam(value = "auth_status", required = false) Integer authStatus,
            @ApiParam("页码") @RequestParam(value = "page", required = false) Integer page,
            @ApiParam("页面大小") @RequestParam(value = "page_size", required = false) Integer pageSize,
            @ApiParam("up主mid") @RequestParam(value = "mid", required = false) Long mid) {
        Integer accountId = context.getAccountId();
        PageResult<MidAuthInfoBo> result = midAuthorizeService.list(accountId, source, authMode, authStatus, page,
                pageSize, mid);
        return Response.ok(new Pagination<>(page, result.getTotal(),
                SanlianMidAuthorizedControllerConverter.MAPPER.toVos(result.getRecords())));
    }

    @ApiOperation("获取当前mid授权情况")
    @GetMapping("/state")
    public Response<MidBindInfoVo> state(@ApiIgnore Context context,
            @ApiParam("账号id") @RequestParam(value = "mid") Long mid) {
        Integer accountId = context.getAccountId();
        MidBindInfoVo minBindInfoVo = SanlianMidAuthorizedControllerConverter.MAPPER.toVo(
                midAuthorizeService.state(accountId, mid));
        return Response.ok(minBindInfoVo);
    }

    @ApiOperation("新增/再次发起授权")
    @PostMapping("/bind")
    public Response<Integer> bind(@ApiIgnore Context context,
            @RequestBody MidBindReqVo midBindVo) throws ServiceException, SystemException {
        Integer accountId = context.getAccountId();
        String userName = context.getUsername();
        Integer bound = midAuthorizeService.bind(
                SanlianMidAuthorizedControllerConverter.MAPPER.toBo(midBindVo, accountId, AuthSourceEnum.APPLY_FOR,
                        userName));
        return Response.ok(bound);
    }

    @ApiOperation("账号授权续期")
    @PostMapping("/renewal")
    public Response<Void> renewal(@ApiIgnore Context context,
            @RequestBody MidBindReqVo midRenewalReqVo) throws ServiceException, SystemException {
        Integer accountId = context.getAccountId();
        String userName = context.getUsername();
        midAuthorizeService.renewal(SanlianMidAuthorizedControllerConverter.MAPPER.toBo(midRenewalReqVo, accountId,
                AuthSourceEnum.APPLY_FOR, userName));
        return Response.ok();
    }

    @ApiOperation("账号授权编辑")
    @PostMapping("/update")
    public Response<Void> update(@ApiIgnore Context context,
            @RequestBody MidBindReqVo midUpdateReqVo) throws ServiceException, SystemException {
        Integer accountId = context.getAccountId();
        String userName = context.getUsername();
        midAuthorizeService.update(SanlianMidAuthorizedControllerConverter.MAPPER.toBo(midUpdateReqVo, accountId,
                AuthSourceEnum.APPLY_FOR, userName));
        return Response.ok();
    }

    @ApiOperation("账号授权解绑")
    @PostMapping("/unbind")
    public Response<Void> unbind(@ApiIgnore Context context,
            @ApiParam @RequestParam() Integer id) throws ServiceException, SystemException {
        Integer accountId = context.getAccountId();
        midAuthorizeService.unBind(id, accountId);
        return Response.ok();
    }

    @ApiOperation("账号授权删除")
    @DeleteMapping("")
    public Response<Void> delete(@ApiIgnore Context context,
            @ApiParam("记录id") @RequestParam(value = "id") Integer id) {
        Integer accountId = context.getAccountId();
        String userName = context.getUsername();
        midAuthorizeService.delete(id, accountId, userName);
        return Response.ok();
    }
}
