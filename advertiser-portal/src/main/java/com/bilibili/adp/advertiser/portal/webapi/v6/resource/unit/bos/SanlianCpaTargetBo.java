package com.bilibili.adp.advertiser.portal.webapi.v6.resource.unit.bos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SanlianCpaTargetBo
 * <AUTHOR>
 * @Date 2024/3/15 2:42 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SanlianCpaTargetBo {
    private Integer id;
    private String name;
    private String desc;
    private String code;
}
