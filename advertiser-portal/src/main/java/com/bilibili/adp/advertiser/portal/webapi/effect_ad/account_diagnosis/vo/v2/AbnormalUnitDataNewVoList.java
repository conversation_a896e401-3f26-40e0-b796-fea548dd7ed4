package com.bilibili.adp.advertiser.portal.webapi.effect_ad.account_diagnosis.vo.v2;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AbnormalUnitDataNewVoList {
    /*
     *异常单元列表
     */
    private List<AbnormalUnitDataNewVo> data;

}
