package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 该对象内还没计算出复合指标，比如首日付费成本，首日付费率
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CpcCreativeReportVo {
    @ApiModelProperty("必选版本 0-老版 1-新版")
    private Integer adpVersion;
    private int campaignId;
    private String campaignName;
    private int unitId;
    private String unitName;
    private int creativeId;
    private String creativeName;
    private int advertisingMode;
    private String advertisingModeDesc;
    private String title;
    private int status;
    private String statusDesc;
    private List<String> imageUrls;
    private long videoId;
    private int mgkVideoId;
    private String mgkVideoUrl;
    private String mgkPageId;
    private String promotionPurposeContent;
    private String accessUrl;
    private String reason;
    private Integer extraStatus;
    private String extraStatusDesc;

    private String createTime;

    // 游戏分包过期状态, 如果过期, 前端会做提示
    private Integer subPkgStatus;
    @ApiModelProperty(notes = "流量降权状态 1-正常，2-即将降权，3-已降权")
    private Integer flowWeightState;

    //
    private BilibiliVideoVo bilibiliVideo;

    @ApiModelProperty(notes = "展示数量")
    private Integer showCount = 0;
    @ApiModelProperty(notes = "点击次数")
    private Integer clickCount = 0;
    @ApiModelProperty(notes = "点击率")
    private String clickRate;
    @ApiModelProperty(notes = "平均点击费用(元)")
    private BigDecimal costPerClick = BigDecimal.ZERO;
    @ApiModelProperty(notes = "平均千次展现费用(元)")
    private String averageCostPerThousand = "--";
    @ApiModelProperty(notes = "消费(元)")
    private BigDecimal cost = BigDecimal.ZERO;

    @ApiModelProperty(value = "之前的预算")
    private BigDecimal beforeBudget;

    @ApiModelProperty(value = "单元预算")
    private BigDecimal budget;

    @ApiModelProperty("当日单元预算剩余修改次数")
    private long budgetRemainingModifyTimes;


    @ApiModelProperty(value = "预览时间（时间戳，单位毫秒）")
    private long previewTime;

    @ApiModelProperty(value = "预览状态: 1-不可预览 2-可预览 3-预览中")
    private int previewStatus;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "是否是历史创意：0-否 1-是")
    private int isHistory;

    @ApiModelProperty(value = "是否可修改：0-否 1-是")
    private int isModify;

    @ApiModelProperty("是否程序化创意")
    private boolean isProgrammatic;

    @ApiModelProperty("程序化创意组图URL")
    private List<String> programmaticImageUrls;

    @ApiModelProperty(notes = "提交订单数")
    private Integer orderAddCount;
    @ApiModelProperty(notes = "提交订单金额")
    private BigDecimal orderAddPrice;

    @ApiModelProperty(notes = "注册数")
    private Integer registerCount;

    private String goodsConversionRate;

    private BigDecimal goodsRoi;

    @ApiModelProperty(notes = "游戏激活数")
    private Integer activateCount;

    @ApiModelProperty(notes = "游戏预约数")
    private Integer reserveCount;

    private Integer fanFollowCount;

    private Integer fanWhisperCount;

    private Integer iosActivateCount;

    private Integer androidActivateCount;

    private Integer playCount;

    @ApiModelProperty(notes = "涨粉成本(元)")
    private String costPerIncreaseFans = "--";
    @ApiModelProperty(notes = "激活成本(元)")
    private String costPerAppActivate = "--";
    @ApiModelProperty(notes = "游戏预约成本(元)")
    private String costPerGameReserve = "--";
    @ApiModelProperty(notes = "游戏激活成本(元)")
    private String costPerGameActivate = "--";
    @ApiModelProperty(notes = "注册成本(元)")
    private String costPerRegister = "--";
    @ApiModelProperty(notes = "播放成本(元)")
    private String costPerPlayCount = "--";
    @ApiModelProperty(notes = "播放率")
    private String playRate = "--";

    private Integer under_box_link_click_count;
    @ApiModelProperty(notes = "框下链接点击成本(元)")
    private String cost_per_under_box_link_click = "--";
    @ApiModelProperty(notes = "框下链接点击率")
    private String under_box_link_click_rate;

    private Integer first_comment_copy_count;
    @ApiModelProperty(notes = "首条评论复制成本(元)")
    private String cost_per_first_comment_copy = "--";
    @ApiModelProperty(notes = "首条评论复制率")
    private String first_comment_copy_rate;

    @ApiModelProperty(notes = "涨粉率")
    private String fansFollowRate;
    @ApiModelProperty(notes = "播转粉率")
    private String play2FansRate;
    @ApiModelProperty(notes = "激活率")
    private String appActivateRate;
    @ApiModelProperty(notes = "游戏预约率")
    private String gameReserveRate;
    @ApiModelProperty(notes = "游戏激活率")
    private String gameActivateRate;
    @ApiModelProperty(notes = "注册率")
    private String registerRate;
    @ApiModelProperty(notes = "表单提交数")
    private Integer formSubmitCount;
    @ApiModelProperty(notes = "表单提交成本(元)")
    private String costPerFormSubmit = "--";
    @ApiModelProperty(notes = "表单提交率")
    private String formSubmitRate;
    private List<Integer> cardTypes;

    @ApiModelProperty(notes = "安卓下载数")
    private Integer androidDownloadCount;
    @ApiModelProperty(notes = "安卓下载成本(元)")
    private String costPerAndroidDownload = "--";
    @ApiModelProperty(notes = "安卓下载率")
    private String androidDownloadRate;

    @ApiModelProperty(notes = "安卓安装数")
    private Integer androidInstallCount;
    @ApiModelProperty(notes = "安卓安装成本(元)")
    private String costPerAndroidInstall = "--";
    @ApiModelProperty(notes = "安卓安装率")
    private String androidInstallRate;

    @ApiModelProperty(notes = "成功调起数")
    private Integer appCallupCount;
    @ApiModelProperty(notes = "调起成功成本")
    private BigDecimal appCallupCost;
    @ApiModelProperty(notes = "调起成功率")
    private String appCallupRatio;

    @ApiModelProperty(notes = "落地页调起店铺数")
    private Integer lpCallupCount;
    @ApiModelProperty(notes = "落地页调起店铺成本")
    private BigDecimal lpCallupCost;
    @ApiModelProperty(notes = "落地页调起店铺率")
    private String lpCallupRatio;
    @ApiModelProperty(notes = "提交订单成本")
    private BigDecimal orderSubmitCost;
    @ApiModelProperty(notes = "提交订单率")
    private String orderSubmitRate;


    @ApiModelProperty(notes = "起飞创意形式")
    private String flyCreativeStyle;

    private Integer dynamicDetailPageBrowseCount;
    private BigDecimal dynamicDetailPageBrowseCost;
    private BigDecimal dynamicDetailPageBrowseRate;

    @ApiModelProperty(notes = "应用内付费数")
    private Integer orderCount;
    @ApiModelProperty(notes = "应用内付费金额")
    private BigDecimal orderPayment;

    @ApiModelProperty(notes = "应用内首次付费数")
    private Integer orderFirstCount;
    @ApiModelProperty(notes = "应用内首次付费金额")
    private BigDecimal orderFirstPayment;
    /**
     * 首日付费相关
     */
    @ApiModelProperty(notes = "首日付费数")
    private Integer firstDayPayCount;
    @ApiModelProperty(notes = "首日付费金额")
    private BigDecimal firstDayPayAmount;

    @ApiModelProperty(notes = "创意形式")
    private String creativeStyle;
    @ApiModelProperty(notes = "素材描述")
    private String materialDesc;

    @ApiModelProperty(notes = "活动页浮层拉起数")
    private Integer activityPagePullUpCount;

    @ApiModelProperty(notes = "活动页浮层拉起成本(元)")
    private BigDecimal activityPagePullUpCost;

    private BigDecimal activityPagePullUpRate;

    @ApiModelProperty(notes = "是否是托管")
    private Boolean isManaged;

    private CpcReportCommonColumnVo cpcReportCommonColumnVo;
}
