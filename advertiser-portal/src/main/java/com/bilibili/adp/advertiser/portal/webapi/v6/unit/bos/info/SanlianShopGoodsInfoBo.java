package com.bilibili.adp.advertiser.portal.webapi.v6.unit.bos.info;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName SanlianShopGoodsInfoBo
 * <AUTHOR>
 * @Date 2024/12/27 2:40 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SanlianShopGoodsInfoBo {

    private Integer id;

    private String name;

    private Integer status;

    private String statusDesc;

    private BigDecimal price;

    private String imageUrl;

    private String jumpUrl;

    private String categoryName;

    private String productTypeName;

}
