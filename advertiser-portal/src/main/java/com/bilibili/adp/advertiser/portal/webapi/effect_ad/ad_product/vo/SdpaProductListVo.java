package com.bilibili.adp.advertiser.portal.webapi.effect_ad.ad_product.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@ApiModel
public class SdpaProductListVo {

    @ApiModelProperty(name = "产品id")
    private String productId;

    @ApiModelProperty(name = "产品名称")
    private String name;

    /**
     * 产品类型1-课程库, 2-借贷产品库
     */
    @ApiModelProperty(name = "产品库类型1-课程库, 2-借贷产品库")
    private Integer type;

    @ApiModelProperty(name = "一级行业名称")
    private String firstCategoryName;

    @ApiModelProperty(name = "二级行业名称")
    private String secondCategoryName;

    @ApiModelProperty(name = "三级行业名称")
    private String thirdCategoryName;

    /**
     * 状态,1：有效，0：无效
     */
    @ApiModelProperty(name = "状态,1：有效，0：无效")
    private Integer bizStatus;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "更新时间")
    private String mtime;

    @ApiModelProperty(name = "商品主图")
    private String mainImgUrl;
}
