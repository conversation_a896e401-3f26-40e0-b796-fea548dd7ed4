package com.bilibili.adp.advertiser.portal.common;

import com.bilibili.adp.advertiser.portal.filter.FlyProSecurityFilter;
import com.bilibili.adp.common.util.TokenUtil;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.crypto.MACVerifier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

public class MacVerifierUtils {

    private final static Logger LOGGER = LoggerFactory.getLogger(MacVerifierUtils.class);

    private static MACVerifier verifier = null;

    static {
        SecretKey secretKeySpec = new SecretKeySpec(TokenUtil.getSecretKey(), "AES");
        try {
            verifier = new MACVerifier(secretKeySpec);
        } catch (JOSEException e) {
            LOGGER.error("new MACVerifier.error", e);
            System.exit(0);
        }
    }

    public static MACVerifier getVerifier(){
        return verifier;
    }
}
