package com.bilibili.adp.advertiser.portal.webapi.middleground.vo.cpc.game;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MiddleLauMiniGameUpdateVo
 * <AUTHOR>
 * @Date 2022/5/15 10:10 下午
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MiddleLauMiniGameUpdateVo {

    @ApiModelProperty("微信小游戏id")
    private Integer id;

    @ApiModelProperty("微信小游戏名称")
    private String name;

    @ApiModelProperty("微信小游戏地址")
    private String gameUrl;

}
