package com.bilibili.adp.advertiser.portal.webapi.finance.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CouponOverviewVo {

    @ApiModelProperty("可用激励券总额")
    private BigDecimal availableCouponValue;

    @ApiModelProperty("使用中激励券")
    private BigDecimal usingCouponValue;

    @ApiModelProperty("72小时内即将过期激励券")
    private BigDecimal couponValueIn72Hours;
}
