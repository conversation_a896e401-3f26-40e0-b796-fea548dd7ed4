package com.bilibili.adp.advertiser.portal.webapi.v6.campaign;

import com.bilibili.adp.v6.operator.AdpOperator;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.service.openapi.OpenAPIValidator;
import com.bilibili.adp.advertiser.portal.webapi.v6.campaign.bos.SanlianGetCampaignRespBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.campaign.bos.SanlianSaveCampaignReqBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.campaign.bos.SanlianSaveCampaignRespBo;
import com.bilibili.adp.advertiser.portal.webapi.v6.campaign.mapper.CampaignMapper;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.v6.campaign.CampaignV6Service;
import com.bilibili.adp.v6.campaign.bo.CampaignBo;
import com.bilibili.adp.cpc.databus.platform_ad_elements.pub.PlatformAdElementsPub;
import com.bilibili.adp.web.framework.core.Response;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Collections;
import java.util.Objects;


@Slf4j
@RestController
@RequestMapping("/web_api/v6/sanlian/campaign")
@Api(value = "/sanlian/campaign", tags = {"计划", "v6"})
@RequiredArgsConstructor
public class SanlianCampaignController extends BasicController {
    private final CampaignV6Service campaignV6Service;
    private final OpenAPIValidator openAPIValidator;

    private final PlatformAdElementsPub platformAdElementsPub;

    @ApiOperation("保存计划")
    @RequestMapping(value = "/save_campaign", method = RequestMethod.POST)
    public Response<SanlianSaveCampaignRespBo> save(@ApiIgnore Context context,
                                                    @RequestBody SanlianSaveCampaignReqBo reqBo) {
        AdpOperator operator = getAdpOperator(context);
        boolean hasCreate = !Utils.isPositive(reqBo.getCampaign().getCampaignId());

        final Integer campaignId = campaignV6Service.save(reqBo.getCampaign(), operator);
        platformAdElementsPub.pub(context.getAccountId(), PlatformAdElementsPub.DIMENSION_CAMPAIGN, Collections.singletonList(campaignId));
        return Response.SUCCESS(new SanlianSaveCampaignRespBo(campaignId));
    }

    @ApiOperation("获取计划")
    @RequestMapping(value = "/get_campaign", method = RequestMethod.GET)
    public Response<SanlianGetCampaignRespBo> get(@ApiIgnore Context context,
                                                  @RequestParam("campaign_id") Integer campaignId) {
        final CampaignBo campaignBo = campaignV6Service.get(campaignId);
        Assert.notNull(campaignBo, "未找到对应的计划");
        Assert.isTrue(Objects.equals(context.getAccountId(),  campaignBo.getAccountId()), "禁止操作不属于自己的计划");
        return Response.SUCCESS(CampaignMapper.MAPPER.toCompoundBo(campaignBo));
    }
}
