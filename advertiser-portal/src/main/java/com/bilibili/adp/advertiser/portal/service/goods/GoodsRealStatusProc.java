package com.bilibili.adp.advertiser.portal.service.goods;

import com.bapis.ad.cmc.cidgoods.GoodsDetail;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.CreativeReportVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.UnitReportVo;
import com.bilibili.adp.cpc.biz.services.goods.GoodsManagerService;
import com.bilibili.adp.cpc.biz.services.goods.GoodsRealStatusJudger;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitGoodsPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.repo.OuterLauUnitRepo;
import com.bilibili.adp.cpc.repo.UnitGoodsRepo;
import com.bilibili.adp.legacy.goods.GoodsRealStatusService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品真实状态查询处理器
 *
 * <AUTHOR>
 * @date 2023/9/20 16:53
 *
 * @see GoodsRealStatusService
 */
@RequiredArgsConstructor
@Component
@Deprecated
public class GoodsRealStatusProc {

    private final GoodsManagerService goodsManagerService;
    private final UnitGoodsRepo unitGoodsRepo;
    private final OuterLauUnitRepo outerLauUnitRepo;

    public void procUnitGoodsStatus(List<UnitReportVo> unitReportVos, Integer accountId) {

        // 商品投放商品状态
        List<Integer> goodsUnitIds = unitReportVos.stream().filter(t -> Objects.equals(PromotionPurposeType.GOODS.getCode(),
                t.getPromotionPurposeType())).map(t -> t.getUnitId()).collect(Collectors.toList());
        List<LauUnitGoodsPo> unitGoodsPos = unitGoodsRepo.queryListByUnitIds(goodsUnitIds);

        List<Long> itemIds = unitGoodsPos.stream().map(t -> t.getItemId()).distinct().collect(Collectors.toList());
        Map<Integer, Long> unitItemIdMap = unitGoodsPos.stream().collect(Collectors.toMap(t -> t.getUnitId(), t -> t.getItemId(), (t1, t2) -> t2));
        Map<Long, GoodsDetail> goodsDetailMap = goodsManagerService.queryMapByItemIds(itemIds, accountId);

        unitReportVos.stream().forEach(unitReportVo -> {
            if (!Objects.equals(PromotionPurposeType.GOODS.getCode(), unitReportVo.getPromotionPurposeType())) {
                return;
            }
            Long itemId = unitItemIdMap.getOrDefault(unitReportVo.getUnitId(), 0L);
            GoodsDetail goodsDetail = goodsDetailMap.get(itemId);
            if (goodsDetail != null) {
                // 商品实际状态
                Integer goodsIsValid = GoodsRealStatusJudger.judgeGoodsRealStatus(goodsDetail.getAuditStatus(),
                        goodsDetail.getOnShelves());
                unitReportVo.setGoodsIsValid(goodsIsValid);
            } else {
                unitReportVo.setGoodsIsValid(0);
            }
        });
    }

    public void procCreativeGoodsStatus(List<CreativeReportVo> vos, Integer accountId) {
        List<Integer> resultUnitIds = vos.stream().map(t -> t.getUnitId()).distinct().collect(Collectors.toList());
        List<LauUnitPo> unitPos = outerLauUnitRepo.queryAdp6UnitListByRange(resultUnitIds);
        List<Integer> goodsUnitIds = unitPos.stream().filter(t -> Objects.equals(PromotionPurposeType.GOODS.getCode(),
                t.getPromotionPurposeType())).map(t -> t.getUnitId()).collect(Collectors.toList());
        Map<Integer, LauUnitPo> unitPoMap = unitPos.stream().collect(Collectors.toMap(t -> t.getUnitId(), t -> t));

        List<LauUnitGoodsPo> unitGoodsPos = unitGoodsRepo.queryListByUnitIds(goodsUnitIds);

        List<Long> itemIds = unitGoodsPos.stream().map(t -> t.getItemId()).distinct().collect(Collectors.toList());
        Map<Integer, Long> unitItemIdMap = unitGoodsPos.stream().collect(Collectors.toMap(t -> t.getUnitId(), t -> t.getItemId(), (t1, t2) -> t2));
        Map<Long, GoodsDetail> goodsDetailMap = goodsManagerService.queryMapByItemIds(itemIds, accountId);

        vos.stream().forEach(vo -> {
            LauUnitPo lauUnitPo = unitPoMap.get(vo.getUnitId());
            if (lauUnitPo == null) {
                return;
            }
            vo.setUnitPromotionPurposeType(lauUnitPo.getPromotionPurposeType());
            if (!Objects.equals(PromotionPurposeType.GOODS.getCode(), lauUnitPo.getPromotionPurposeType())) {
                return;
            }

            Long itemId = unitItemIdMap.getOrDefault(vo.getUnitId(), 0L);
            GoodsDetail goodsDetail = goodsDetailMap.get(itemId);
            if (goodsDetail != null) {
                // 商品实际状态
                Integer goodsIsValid = GoodsRealStatusJudger.judgeGoodsRealStatus(goodsDetail.getAuditStatus(),
                        goodsDetail.getOnShelves());
                vo.setGoodsIsValid(goodsIsValid);
            } else {
                vo.setGoodsIsValid(0);
            }
        });
    }

}
