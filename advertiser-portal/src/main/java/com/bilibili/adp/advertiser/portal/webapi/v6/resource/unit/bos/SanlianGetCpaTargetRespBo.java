package com.bilibili.adp.advertiser.portal.webapi.v6.resource.unit.bos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName SanlianGetCpaTargetRespBo
 * <AUTHOR>
 * @Date 2024/3/15 2:41 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SanlianGetCpaTargetRespBo {

    @ApiModelProperty("浅层优化目标")
    private SanlianCpaTargetBo cpaTarget;
    @ApiModelProperty("深层优化目标")
    private List<SanlianDeepCpaTargetBo> deepCpaTarget;
    @ApiModelProperty("出价类型")
    private List<SanlianCpaTargetBo> assistCpaTarget;
    @ApiModelProperty("是否为nobid")
    private Integer isNoBid;


}
