package com.bilibili.adp.advertiser.portal.webapi.resource.vo;

import com.bilibili.location.api.template.dto.ButtonCopyDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TemplateVo {
	
	private int id;

    private String name;

    private boolean _fill_title;

    private int title_min_length;

    private int title_max_length;

    private boolean _fill_desc;

    private int desc_min_length;

    private int desc_max_length;
    
    private boolean _fill_ext_desc;

    private int ext_desc_min_length;

    private int ext_desc_max_length;

    private boolean _support_image;

    private int image_width;

    private int image_height;

    private int image_kb_limit;

    private boolean _support_ext_image;

    private int ext_image_width;

    private int ext_image_height;

    private int ext_image_kb_limit;
    
    private boolean _support_video_id;
    
    private boolean _support_video;
    
    private int video_width;
    
    private int video_height;
    
    private int video_kb_limit;
    
    private int video_duration_min;
    
    private int video_duration_max;
    
    private String html;
    
    private boolean _support_button;
    
    private Integer button_copy_min_length;
    
    private Integer button_copy_max_length;

    @ApiModelProperty("是否支持动态布局")
    private boolean _support_dynamic_layout;

    @ApiModelProperty("背景—顶图素材")
    private List<TemplateReviewExampleVo> review_examples;

    @ApiModelProperty("支持的图片数目")
    private Integer support_image_num;

    @ApiModelProperty("按钮文案列表")
    private List<ButtonCopyVo> button_copys;

    @ApiModelProperty("卡片类型")
    private int card_type;
}
