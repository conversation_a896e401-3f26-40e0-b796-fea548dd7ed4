package com.bilibili.adp.advertiser.portal.advice;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.advertiser.grpc.holder.RequestSourceHolder;
import com.bilibili.adp.advertiser.portal.common.EnhancedResponse;
import com.bilibili.adp.advertiser.portal.common.annotation.BusinessDomain;
import com.bilibili.adp.cpc.enums.ErrorCodeEnum;
import com.bilibili.adp.cpc.utils.metrics.CustomMetrics;
import com.bilibili.adp.web.framework.core.Response;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashSet;
import java.util.Set;

/**
 * HTTP响应拦截器，捕获Response中的错误信息
 * 并进行指标数据上报
 */
@Order(1)
@Slf4j
@ControllerAdvice
public class MetricsResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    public static final String REQUEST_START_TIME = "request_start_time";

    // 指标名称
    private static final String HTTP_REQUEST_COUNTER = "http_request_count";
    private static final String HTTP_REQUEST_LATENCY = "http_request_latency";
    private static final String METRIC_DATA_UNKNOWN = "UNKNOWN";

    // 指标标签Key
    private static final AttributeKey<String> URI_KEY = AttributeKey.stringKey("method");
    private static final AttributeKey<String> ERROR_CODE_KEY = AttributeKey.stringKey("error_code");
    private static final AttributeKey<String> ERROR_MSG_KEY = AttributeKey.stringKey("error_msg"); // 错误基础描述
    private static final AttributeKey<String> REQUEST_SOURCE_KEY = AttributeKey.stringKey("request_source");
    private static final AttributeKey<String> INSTANCE_NAME_KEY = AttributeKey.stringKey("instance_name");
    private static final AttributeKey<String> ENV_KEY = AttributeKey.stringKey("env");
    private static final AttributeKey<String> TARGET_SERVICE_KEY = AttributeKey.stringKey("target_service");
    private static final AttributeKey<String> TARGET_SERVICE_ERR_CODE_KEY = AttributeKey.stringKey("target_service_err_code");

    private static final String RESPONSE_SUCCESS_STATUS = "success";

    @Autowired
    private CustomMetrics customMetrics;
    
    /**
     * 需要监控的URI列表，只有这些URI会被上报指标
     */
    private final Set<String> monitoredUris = new HashSet<>();

    /**
     * 初始化需要监控的URI列表
     */
    @PostConstruct
    public void init() {
        monitoredUris.add("/web_api/v6/sanlian/campaign/get_campaign");
        monitoredUris.add("/web_api/v6/sanlian/campaign/save_campaign");
        monitoredUris.add("/web_api/v6/sanlian/unit/get_unit");
        monitoredUris.add("/web_api/v6/sanlian/unit/save_unit");
        monitoredUris.add("/web_api/v6/sanlian/creative/get_creative");
        monitoredUris.add("/web_api/v6/sanlian/creative/save_creative");

        monitoredUris.add("/web_api/v6/sanlian/report/list/campaign");
        monitoredUris.add("/web_api/v6/sanlian/report/list/unit");
        monitoredUris.add("/web_api/v6/sanlian/report/list/creative");

        // 元数据
        monitoredUris.add("/web_api/v6/resource/sanlian/campaign/get_promotion_purpose_type");
        monitoredUris.add("/web_api/v6/resource/sanlian/unit/get_promotion_purpose_content");
        monitoredUris.add("/web_api/v6/resource/sanlian/unit/get_target");
        monitoredUris.add("/web_api/v6/resource/sanlian/unit/get_cpa_target_list");
        monitoredUris.add("/web_api/v6/resource/sanlian/creative/get_location");

        // 首页
        monitoredUris.add("/web_api/v1/middle/index/cash_info");
        monitoredUris.add("/web_api/v6/sanlian/home/<USER>");
        monitoredUris.add("/web_api/v6/sanlian/home/<USER>");
        monitoredUris.add("/web_api/v6/sanlian/home/<USER>");
        monitoredUris.add("/web_api/v6/sanlian/home/<USER>");
        monitoredUris.add("/web_api/v1/effect_ad/statistic/data/chart");

    }

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        // 支持处理所有类型的响应
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, 
                                  Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        try {
            if (!(request instanceof ServletServerHttpRequest)) {
                return body;
            }
            
            HttpServletRequest servletRequest = ((ServletServerHttpRequest) request).getServletRequest();
            String uri = servletRequest.getRequestURI();

            // 判断是否需要监控该URI
            if (!monitoredUris.contains(uri)) {
                return body;
            }

            Method method = returnType.getMethod();
            BusinessDomain businessDomainAnnotation = method.getAnnotation(BusinessDomain.class);
            String domainType = ErrorCodeEnum.DomainType.OTHER.name();
            if (businessDomainAnnotation != null) {
                domainType = businessDomainAnnotation.value().name();
            }
            
            String errorCode = ErrorCodeEnum.SubCode.SUCCESS.getCode();
            String errorMsg = ErrorCodeEnum.SubCode.SUCCESS.getDesc();
            String targetServiceErrCode = ErrorCodeEnum.SubCode.UNKNOWN.getCode();


            // 处理EnhancedResponse
            if (body instanceof EnhancedResponse) {
                EnhancedResponse<?> enhancedResponse = (EnhancedResponse<?>) body;
                if (!RESPONSE_SUCCESS_STATUS.equals(enhancedResponse.getStatus())) {
                    if (enhancedResponse.getDetailErrCode() != null) {
                        errorCode = domainType + "-" + ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
                        errorMsg = ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
                        targetServiceErrCode = enhancedResponse.getDetailErrCode();
                    }
                }
                log.info("MetricsResponseBodyAdvice uri:{}, enhancedResponse:{}", uri, JSON.toJSONString(enhancedResponse));
            }
            // 处理普通的Response
            else if (body instanceof Response) {
                Response<?> response1 = (Response<?>) body;
                // 当状态不是success时，使用错误码和错误信息
                if (!RESPONSE_SUCCESS_STATUS.equals(response1.getStatus())) {
                    if (response1.getError_code() != null) {
                        errorCode = String.valueOf(response1.getError_code());
                    }
                    if (response1.getError_msg() != null) {
                        errorMsg = response1.getError_msg();
                    }
                    if (errorCode.equals(String.valueOf(HttpStatus.BAD_REQUEST.value()))) {
                        errorCode = domainType + "-" + ErrorCodeEnum.SubCode.PARAM_INVALID.getCode();
                        errorMsg = ErrorCodeEnum.SubCode.PARAM_INVALID.getDesc();
                    } else if (errorCode.equals(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()))) {
                        errorCode = domainType + "-" + ErrorCodeEnum.SubCode.SYSTEM_ERROR.getCode();
                        errorMsg =  ErrorCodeEnum.SubCode.SYSTEM_ERROR.getDesc();
                    } else {
                        log.info("MetricsResponseBodyAdvice unknown error code, uri:{}, errorCode:{}, errorMsg:{}", uri, errorCode, errorMsg);
                        errorCode = domainType + "-" + ErrorCodeEnum.SubCode.UNKNOWN.getCode();
                        errorMsg = ErrorCodeEnum.SubCode.UNKNOWN.getDesc();
                    }
                }
                log.info("MetricsResponseBodyAdvice uri:{}, response:{}", uri, JSON.toJSONString(response1));
            } else {
                log.info("MetricsResponseBodyAdvice unknown response, uri:{}", uri);
                return body;
            }
            
            // 获取请求来源
            String requestSource = "0"; // 默认值
            String targetService = "UNKNOWN"; // 默认值
            RequestSourceHolder.RequestInfo requestInfo = RequestSourceHolder.getRequestInfo();
            if (requestInfo != null) {
                if (requestInfo.getRequestSource() != null) {
                    requestSource = String.valueOf(requestInfo.getRequestSource());
                }
                if (requestInfo.getServiceName() != null && !requestInfo.getServiceName().isEmpty()) {
                    targetService = requestInfo.getServiceName();
                }
            }
            
            String instanceName = getInstanceName();
            String env = getEnv();
            
            // 构建指标属性，使用Attributes.of直接构建，避免使用Builder
            Attributes attributes = Attributes.builder()
                    .put(URI_KEY, uri)
                    .put(ERROR_CODE_KEY, errorCode)
                    .put(ERROR_MSG_KEY, errorMsg)
                    .put(REQUEST_SOURCE_KEY, requestSource)
                    .put(INSTANCE_NAME_KEY, instanceName)
                    .put(ENV_KEY, env)
                    .put(TARGET_SERVICE_KEY, targetService)
                    .put(TARGET_SERVICE_ERR_CODE_KEY, targetServiceErrCode)
                    .build();

            // 上报请求计数
            customMetrics.count(HTTP_REQUEST_COUNTER, 1, attributes);
            
            // 计算请求延迟并上报
            long latency = 0L;
            Long startTime = (Long) servletRequest.getAttribute(REQUEST_START_TIME);
            if (startTime != null) {
                latency = System.currentTimeMillis() - startTime;
                
                // 延迟指标: uri、request_source、instance_name、env
                Attributes latencyAttributes = Attributes.of(
                    URI_KEY, uri,
                    REQUEST_SOURCE_KEY, requestSource,
                    INSTANCE_NAME_KEY, instanceName,
                    ENV_KEY, env
                );
                
                customMetrics.longHistogram(HTTP_REQUEST_LATENCY, latency, latencyAttributes);
            }
            
            log.info("MetricsResponseBodyAdvice report metrics, uri:{}, requestSource:{}, targetService:{}, errorCode:{}, errorMsg:{}, latency:{}, instanceName:{}, env:{}",
                     uri, requestSource, targetService, errorCode, errorMsg, latency, instanceName, env);
            
        } catch (Exception e) {
            log.error("MetricsResponseBodyAdvice error", e);
        }
        
        return body;
    }
    
    /**
     * 获取当前服务实例的主机名
     * @return 主机名，获取失败时返回 "UNKNOWN"
     */
    private String getInstanceName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            log.error("获取主机名失败", e);
            return METRIC_DATA_UNKNOWN;
        }
    }

    /**
     * 获取当前部署环境标识 (从环境变量 DEPLOY_ENV 读取)
     * @return 环境标识，未设置时返回 "UNKNOWN"
     */
    public String getEnv() {
        String env = System.getenv("DEPLOY_ENV");
        return env == null ? METRIC_DATA_UNKNOWN : env;
    }
}