package com.bilibili.adp.advertiser.portal.webapi.effect_ad.ad_product.vo;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SdpaProductMappingDarkVo {

    private Long adProductId;

    private Integer mappingId;

    private Integer type;

    private String uniqueKey;
}
