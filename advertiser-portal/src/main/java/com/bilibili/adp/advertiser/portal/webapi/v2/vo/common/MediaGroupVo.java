package com.bilibili.adp.advertiser.portal.webapi.v2.vo.common;

import com.bilibili.adp.cpc.biz.services.creative.bos.programmatic.MediaGroupBo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MediaGroupVo {
    // 每一个媒体组都属于同一个模板组
    @ApiModelProperty("模板组ID")
    private Integer templateGroupId;
    @ApiModelProperty("建站视频列表")
    private List<MgkVideoMediaVo> mgkVideoMediaList;
    @ApiModelProperty("单图/GIF列表")
    private List<MonoMediaVo> monoMediaList;
    @ApiModelProperty("图集列表(三图)")
    private List<ImageSetMediaVo> imageSetMediaList;
    @ApiModelProperty("稿件列表")
    private List<BiliVideoMediaVo> biliVideoMediaList;

    public MediaGroupBo toBo() {
        final MediaGroupBo.MediaGroupBoBuilder builder = MediaGroupBo.builder()
                .templateGroupId(templateGroupId);
        if (!CollectionUtils.isEmpty(mgkVideoMediaList)) {
            builder.mgkVideoMediaList(mgkVideoMediaList.stream().map(MgkVideoMediaVo::toBo).collect(Collectors.toList()));
        }

        // 稿件类表转换
        if (!CollectionUtils.isEmpty(biliVideoMediaList)) {
            builder.biliVideoMediaList(biliVideoMediaList.stream().map(BiliVideoMediaVo::toBo).collect(Collectors.toList()));
        }
        if (Objects.nonNull(monoMediaList)) {
            builder.monoMediaList(monoMediaList.stream().map(MonoMediaVo::toBo).collect(Collectors.toList()));
        }
        if (Objects.nonNull(imageSetMediaList)) {
            builder.imageSetMediaList(imageSetMediaList.stream().map(ImageSetMediaVo::toBo).collect(Collectors.toList()));
        }
        return builder.build();
    }

    public static MediaGroupVo fromBo(MediaGroupBo bo) {
        final MediaGroupVo.MediaGroupVoBuilder builder = MediaGroupVo.builder()
                .templateGroupId(bo.getTemplateGroupId());
        if (!CollectionUtils.isEmpty(bo.getMgkVideoMediaList())) {
            builder.mgkVideoMediaList(bo.getMgkVideoMediaList()
                    .stream()
                    .map(MgkVideoMediaVo::fromBo)
                    .collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(bo.getMonoMediaList())) {
            builder.monoMediaList(bo.getMonoMediaList()
                    .stream()
                    .map(MonoMediaVo::fromBo)
                    .collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(bo.getImageSetMediaList())) {
            builder.imageSetMediaList(bo.getImageSetMediaList()
                    .stream()
                    .map(ImageSetMediaVo::fromBo)
                    .collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(bo.getBiliVideoMediaList())) {
            builder.biliVideoMediaList(bo.getBiliVideoMediaList()
                    .stream()
                    .map(BiliVideoMediaVo::fromBo)
                    .collect(Collectors.toList()));
        }
        return builder.build();
    }
}
