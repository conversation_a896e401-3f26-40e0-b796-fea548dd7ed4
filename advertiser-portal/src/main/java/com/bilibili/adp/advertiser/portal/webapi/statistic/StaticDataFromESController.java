package com.bilibili.adp.advertiser.portal.webapi.statistic;

import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.service.stat.HystrixStatisticService;
import com.bilibili.adp.advertiser.portal.service.stat.StatService;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.conveter.FlyProStaticDataConverter;
import com.bilibili.adp.advertiser.portal.webapi.statistic.common.StatPortalUtils;
import com.bilibili.adp.advertiser.portal.webapi.statistic.vo.DailyStatVo;
import com.bilibili.adp.advertiser.portal.webapi.statistic.vo.LaunchChartVo;
import com.bilibili.adp.advertiser.portal.webapi.statistic.vo.LaunchDataDetailVo;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.StatQueryTimeType;
import com.bilibili.adp.common.enums.StatQueryType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.TimeUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.report.platform.api.dto.StatChartDto;
import com.bilibili.report.platform.api.dto.StatLaunchDetailDto;
import com.bilibili.report.platform.api.dto.StatQueryDto;
import com.bilibili.report.platform.api.enums.OcpcTimeCaliberEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-10-29
 **/
@Controller
@RequestMapping("/web_api/v1/statistic/data")
@Api(value = "/data/statistic", description = "统计报表 from ES")
@Slf4j
public class StaticDataFromESController extends BasicController {
    @Autowired
    private HystrixStatisticService hystrixStatisticService;
    @Autowired
    private StatPortalUtils statPortalUtils;

    @ApiOperation(value = "日常实时统计")
    @GetMapping(value = "/daily")
    @ResponseBody
    public Response<DailyStatVo> getDailyStatistics(@ApiIgnore Context context) throws ServiceException {
        StatQueryDto query = StatQueryDto.builder()
                .accountId(context.getAccountId())
                .startDate(TimeUtils.yesterdayStart())
                .endDate(TimeUtils.nowTimestamp())
                .type(StatQueryType.ACCOUNT)
                .salesTypes(SalesType.PLATFORM_SALES_TYPES)
                .timeType(StatQueryTimeType.HOUR)
                .isNewFly(0)
                .build();
        StatChartDto statChartDto = hystrixStatisticService.generateChartDtoFromES(query);
        return Response.SUCCESS(chartDtoToDailyStatVo(query, statChartDto));
    }

    private static DailyStatVo chartDtoToDailyStatVo(StatQueryDto query, StatChartDto statChartDto) {
        List<String> xAxis = new ArrayList<>(9);
        List<BigDecimal> yesterdayCost = new ArrayList<>(9);
        List<BigDecimal> todayCost = new ArrayList<>(9);

        // 从原点开始，后面的往后移动1小时
        xAxis.add("00:00");
        yesterdayCost.add(BigDecimal.ZERO);
        todayCost.add(BigDecimal.ZERO);

        int yesterday = TimeUtils.getDay(query.getStartDate());
        Iterator<String> xAxisIterator = statChartDto.getXAxis().iterator();
        Iterator<BigDecimal> costIterator = statChartDto.getCost().iterator();

        while (xAxisIterator.hasNext()) {
            Date date = StringDateParser.stringToDate(xAxisIterator.next());
            if (date.after(query.getEndDate())) {
                break;
            }

            BigDecimal cost = costIterator.hasNext() ? costIterator.next() : BigDecimal.ZERO;

            if (yesterday == TimeUtils.getDay(date)) {
                // 统计是0~23小时，显示是1~24小时，所以加1小时
                int hour = TimeUtils.getHour(date) + 1;
                xAxis.add((hour < 10 ? "0" + hour : hour) + ":00");
                yesterdayCost.add(yesterdayCost.get(yesterdayCost.size() - 1).add(cost));
            } else {
                todayCost.add(todayCost.get(todayCost.size() - 1).add(cost));
            }
        }
        return DailyStatVo.builder().xAxis(xAxis).yesterday_cost(yesterdayCost).today_cost(todayCost).build();
    }

    @ApiOperation(value = "报表明细")
    @GetMapping(value = "/detail")
    public
    @ResponseBody
    Response<Pagination<List<LaunchDataDetailVo>>> getLaunchStatisticsDetail(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id", required = false) Integer campaignId,
            @RequestParam(value = "unit_id", required = false) Integer unitId,
            @RequestParam(value = "creative_id", required = false) Integer creativeId,
            @RequestParam(value = "time_type", defaultValue = "0") Integer timeType,
            @RequestParam(value = "launch_type", defaultValue = "0") Integer launchType,
            @RequestParam(value = "promotion_type", required = false) Integer promotionType,
            @RequestParam(value = "slot_group_id", required = false) Integer slotGroupId,
            @RequestParam(value = "time_caliber", required = false) Integer timeCaliber,
            @RequestParam("from_time") long fromTime,
            @RequestParam("to_time") long toTime,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "page_size", defaultValue = "20") Integer pageSize) throws ServiceException {
        StatQueryDto query = StatQueryDto
                .builder()
                .accountId(context.getAccountId())
                .campaignId(campaignId)
                .unitId(unitId)
                .creativeId(creativeId)
                .timeCaliber(OcpcTimeCaliberEnum.getByCode(timeCaliber))
                .startDate(Utils.getBeginOfDay(new Timestamp(fromTime)))
                .endDate(Utils.getEndOfDay(new Timestamp(toTime)))
                .type(StatQueryType.getByCode(launchType))
                .timeType(StatQueryTimeType.getByCode(timeType))
                .ocpxSalesTypes(StatService.ocpxSalesTypes())
                .salesTypes(SalesType.PLATFORM_SALES_TYPES)
                .sourceId(slotGroupId)
                .promotionType(promotionType)
                .isNewFly(0)
                .page(Page.valueOf(page, pageSize))
                .build();
        PageResult<StatLaunchDetailDto> pageResult = hystrixStatisticService.pageGenStatDetailFromES(query);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return Response.SUCCESS(new Pagination<>(page, 0, Collections.emptyList()));
        }

        List<StatLaunchDetailDto> result = pageResult.getRecords();
        final List<LaunchDataDetailVo> pagedVos = statPortalUtils.detailDtosToVos(result, query);
        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), pagedVos));
    }

    @ApiOperation(value = "数据报表")
    @GetMapping(value = "/chart")
    public
    @ResponseBody
    Response<LaunchChartVo> getLaunchStatistics(@ApiIgnore Context context,
                                                @RequestParam(value = "campaign_id", required = false) Integer campaignId,
                                                @RequestParam(value = "unit_id", required = false) Integer unitId,
                                                @RequestParam(value = "creative_id", required = false) Integer creativeId,
                                                @RequestParam(value = "time_caliber", required = false) Integer timeCaliber,
                                                @RequestParam("from_time") Long fromTime,
                                                @RequestParam("to_time") Long toTime) throws ServiceException {
        StatQueryDto query = StatQueryDto
                .builder()
                .accountId(context.getAccountId())
                .campaignId(campaignId)
                .unitId(unitId)
                .creativeId(creativeId)
                .timeCaliber(OcpcTimeCaliberEnum.getByCode(timeCaliber))
                .startDate(Utils.getBeginOfDay(new Timestamp(fromTime)))
                .endDate(Utils.getEndOfDay(new Timestamp(toTime)))
                .salesTypes(SalesType.PLATFORM_SALES_TYPES)
                .isNewFly(0)
                .build();
        setTimeType(query);
        setStatQueryType(query);
        StatChartDto statChartDto = hystrixStatisticService.generateChartDtoFromES(query);
        return Response.SUCCESS(FlyProStaticDataConverter.chartDtoToVo(statChartDto));
    }

    public static void setTimeType(StatQueryDto query) {
        boolean oneDay = TimeUtils.isOneDay(query.getStartDate(), query.getEndDate());
        if (oneDay) {
            query.setTimeType(StatQueryTimeType.HOUR);
        } else {
            query.setTimeType(StatQueryTimeType.DAY);
        }
    }

    public static void setStatQueryType(StatQueryDto query) {
        if (query.getCampaignId() == null) {
            query.setType(StatQueryType.ACCOUNT);
        } else if (query.getUnitId() == null) {
            query.setType(StatQueryType.CAMPAIGN);
        } else if (query.getCreativeId() == null) {
            query.setType(StatQueryType.UNIT);
        } else {
            query.setType(StatQueryType.CREATIVE);
        }
    }
}
