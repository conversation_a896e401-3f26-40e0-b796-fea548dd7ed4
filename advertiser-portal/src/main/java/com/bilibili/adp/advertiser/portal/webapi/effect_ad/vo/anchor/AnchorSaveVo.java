package com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.anchor;

import com.bilibili.adp.cpc.biz.bos.anchor.AnchorImgBo;
import com.bilibili.adp.cpc.enums.JumpType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AnchorSaveVo {

    @ApiModelProperty(value = "锚点id")
    private Long id;

    @ApiModelProperty(value = "锚点id")
    private List<Long> ids;

    // 编辑仍然用的单数形式
    @ApiModelProperty(value = "aid")
    private Long aid;

    // 新建用复数形式
    @ApiModelProperty(value = "aids")
    private List<Long> aids;

    @ApiModelProperty(value = "场景")
    private List<Integer> scenes;

    @ApiModelProperty(value = "锚点类型 1应用推广 2游戏下载 3线索收集 4微信小游戏")
    private Integer type;
    @ApiModelProperty(value = "线索类型 0-落地页")
    private Integer clueType;

    @ApiModelProperty(value = "锚点名称")
    private String name;
    @ApiModelProperty(value = "主标题")
    private String mainTitle;
    @ApiModelProperty(value = "副标题")
    private String subTitle;
    @ApiModelProperty(value = "按钮文案")
    private String buttonText;

    @ApiModelProperty(value = "游戏id")
    private Integer gameBaseId;
    @ApiModelProperty(value = "b站小游戏mid")
    private Long biliMiniGameMid;

    @ApiModelProperty(value = "b站小程序Url")
    private String biliAppletUrl;

    @ApiModelProperty(value = "游戏平台类型1-安卓 2-IOS")
    private Integer gamePlatformType;
    @ApiModelProperty(value = "微信小程序id")
    private Integer miniGameId;
    @ApiModelProperty(value = "是否开启微信小程序")
    private Integer isOpenMiniGame;

    @ApiModelProperty(value = "ios链接类型，1-三方落地页 2-高能建站落地页")
    private Integer iosUrlType;
    @ApiModelProperty(value = "ios链接类型高能建站落地页page_id, 针对type = 2")
    private Long iosUrlPageId;
    @ApiModelProperty(value = "ios链接")
    private String iosUrl;

    @ApiModelProperty(value = "android链接类型，1-三方落地页 2-高能建站落地页")
    private Integer androidUrlType;
    @ApiModelProperty(value = "android链接高能建站落地页page_id, 针对type = 2")
    private Long androidUrlPageId;
    @ApiModelProperty(value = "android链接")
    private String androidUrl;

    /**
     * @see JumpType
     */
    @ApiModelProperty(value = "转化链链接(兜底链接)类型，1-三方落地页 5-高能建站落地页 9-三方落地页组 10-建站落地页组")
    private Integer conversionUrlType;
    @ApiModelProperty(value = "转化链链接(兜底链接)高能建站落地页page_id, 针对type = 2")
    private Long conversionUrlPageId;
    @ApiModelProperty(value = "转化链连接(兜底链接)")
    private String conversionUrl;

    @ApiModelProperty(value = "ios app 包id")
    private Integer iosAppPackageId;
    @ApiModelProperty(value = "android app 包id")
    private Integer androidAppPackageId;
    @ApiModelProperty(value = "ios 唤起链接")
    private String iosSchemaUrl;
    @ApiModelProperty(value = "android 唤起链接")
    private String androidSchemaUrl;
    @ApiModelProperty(value = "ios按钮唤起链接")
    private String iosButtonSchemaUrl;
    @ApiModelProperty(value = "android按钮唤起链接")
    private String androidButtonSchemaUrl;

    @ApiModelProperty(value = "clueData")
    private String clueData;

    @ApiModelProperty("是否广告包: 0-联运包, 1-广告包")
    private Integer subPkg;

    @ApiModelProperty("展示监控")
    private String customizedImpUrl;
    @ApiModelProperty("点击监控")
    private String customizedClickUrl;

    @ApiModelProperty("是否安卓应用商店直投: 0-不直投, 1-直投")
    private Integer isAndroidAppDirect;
    private String guideText;
    private String appDetailText;
    // APP标签
    private List<String> appLabels;
    // app详情类型: 1横图2竖图
    private Integer appDetailType;
    // 顶图
    private List<AnchorImgBo> topImgUrls;
    // app 图片列表
    private List<AnchorImgBo> appImgUrls;

    @ApiModelProperty("是否是mapi请求")
    private Boolean isMapiRequest;

    @ApiModelProperty("分组id")
    private Integer groupId;

    @ApiModelProperty("自然流量是否不可见 1 不可见 , 默认可见")
    private Integer notShowInNatureFlow;
}
