package com.bilibili.adp.advertiser.consumer;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.advertiser.consumer.dto.CoverClusterMsg;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.dao.ad_data_write.CreativeCoverClusteringBDao;
import com.bilibili.adp.cpc.dao.ad_data_write.CreativeCoverClusteringDao;
import com.bilibili.adp.cpc.enums.CoverClusterType;
import com.bilibili.adp.cpc.po.ad_data_write.CreativeCoverClusteringBPo;
import com.bilibili.adp.cpc.po.ad_data_write.CreativeCoverClusteringBPoExample;
import com.bilibili.adp.cpc.po.ad_data_write.CreativeCoverClusteringPo;
import com.bilibili.adp.cpc.po.ad_data_write.CreativeCoverClusteringPoExample;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/12
 * @description 稿件封面相似图片订阅算法消费者
 */
@Slf4j
@Service
public class CreativeCoverClusterConsumer implements MessageListener {
    public static final String IMAGE_CLUSTER = "image-cluster";

    @Value("${platform.image.cluster.updateA:false}")
    private Boolean updateA;

    @Value("${platform.image.cluster.updateB:false}")
    private Boolean updateB;

    @Value("${databus.image.cluster.enabled:true}")
    private Boolean enabled;
    private final String topic;
    private final String group;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    CreativeCoverClusteringDao clusteringDao;

    @Resource
    CreativeCoverClusteringBDao bClusteringDao;

    public CreativeCoverClusterConsumer(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();

        DatabusProperty property = properties.get(IMAGE_CLUSTER);
        log.info("CreativeCoverClusterConsumer, property={}", JSONObject.toJSONString(property));

        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }

    @Override
    public boolean autoCommit() {
        return true;
    }

    @Override
    public String topic() {
        return topic;
    }

    @Override
    public String group() {
        return group;
    }

    @Override
    public void onMessage(AckableMessage ackableMessage) {
        if (!enabled) {
            return;
        }

        /**
         * 目前，b表是78的阈值生成的相似id
         */
        AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, IMAGE_CLUSTER  + ":sub", transaction -> {
            try {
                CoverClusterMsg coverClusterMsg = objectMapper.readValue(ackableMessage.payload(), CoverClusterMsg.class);
                if (Objects.isNull(coverClusterMsg)) {
                    return;
                }
                log.info("CreativeCoverClusterConsumer msg={}", coverClusterMsg);
                String md5 = coverClusterMsg.getMd5();
                Long clusterId = coverClusterMsg.getClusterId();
                String type = coverClusterMsg.getType();

                if (StringUtils.isEmpty(md5) || Objects.isNull(clusterId) || StringUtils.isEmpty(type)) {
                    log.warn("CreativeCoverClusterConsumer clusterId=null or md5=null , or type= null msg={}", coverClusterMsg);
                    return;
                }

                //消息处理
                deal(coverClusterMsg);
            } catch (Exception e) {
                log.warn("CreativeCoverClusterConsumer err msg={}", new String(ackableMessage.payload()), e);
            }
        });
    }


    /**
     * 目前，b表是78的阈值生成的相似id
     */
    private void deal(CoverClusterMsg coverClusterMsg){

        String md5 = coverClusterMsg.getMd5();
        Long clusterId = coverClusterMsg.getClusterId();
        Long materialId = coverClusterMsg.getMaterialId();
        String url = coverClusterMsg.getUrl();
        Long imageId = coverClusterMsg.getImageId();

        String type = coverClusterMsg.getType();
        CoverClusterType msgType = CoverClusterType.getByCode(type);

        switch (msgType){
            case A:
                CreativeCoverClusteringPoExample example = new CreativeCoverClusteringPoExample();
                example.createCriteria().andImageMd5EqualTo(md5);
                List<CreativeCoverClusteringPo> clusteringPos = clusteringDao.selectByExample(example);
                CreativeCoverClusteringPo po = new CreativeCoverClusteringPo();
                po.setImageMd5(md5);
                po.setClusterId(clusterId);
                po.setMaterialId(materialId);
                po.setImageUrl(url);
                po.setImageId(imageId);

                //不存在，新增 || 存在判断开关是否开启
                if (CollectionUtils.isEmpty(clusteringPos) || (!CollectionUtils.isEmpty(clusteringPos) && updateA)) {
                    //新增
//                    log.info("CreativeCoverClusterConsumer A={}",po);
                    clusteringDao.insertUpdateSelective(po);

                }
                break;
            case B:
                CreativeCoverClusteringBPoExample bExample = new CreativeCoverClusteringBPoExample();
                bExample.createCriteria().andImageMd5EqualTo(md5);
                List<CreativeCoverClusteringBPo> bClusters = bClusteringDao.selectByExample(bExample);
                CreativeCoverClusteringBPo bCluster = new CreativeCoverClusteringBPo();
                bCluster.setImageMd5(md5);
                bCluster.setClusterId(clusterId);
                bCluster.setMaterialId(materialId);
                bCluster.setImageUrl(url);
                bCluster.setImageId(imageId);

                //不存在，新增 || 存在判断开关是否开启
                if (CollectionUtils.isEmpty(bClusters) || (!CollectionUtils.isEmpty(bClusters) && updateB)) {
                    //新增
                    bClusteringDao.insertUpdateSelective(bCluster);
                }
                break;
        }
    }
}
