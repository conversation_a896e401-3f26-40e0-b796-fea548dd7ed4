package com.bilibili.adp.advertiser.portal.webapi.v6.creative.bos.save;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SanlianSaveCreativeArcBo
 * <AUTHOR>
 * @Date 2024/3/12 9:05 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SanlianCreativeArcBo {
    @ApiModelProperty("稿件来源 1-子账号 2-普通稿件 3-花火商单 4-普通内容")
    private Integer source;
    private String avid;
    private String cid;
    private SanlianCreativeImageBo cover;

}
