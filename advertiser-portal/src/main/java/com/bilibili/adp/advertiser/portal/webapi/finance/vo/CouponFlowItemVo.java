package com.bilibili.adp.advertiser.portal.webapi.finance.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CouponFlowItemVo {

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("项目名")
    private String projectName;
    @ApiModelProperty("券id")
    private Integer couponId;

    @ApiModelProperty("券面额(元)")
    private BigDecimal couponValue;
    @ApiModelProperty("冻结金额(元)")
    private BigDecimal couponLockValue;
    // 实际消耗冻结金额
    private BigDecimal lockConsume;
    // 实际消耗激励金额
    private BigDecimal couponConsume;
    // 实际过期金额
    private BigDecimal unConsume;

    @ApiModelProperty("开始时间")
    private String beginTime;
    @ApiModelProperty("结束时间")
    private String endTime;

    private Integer crmCouponType;
    private Long parentCouponId;

}
