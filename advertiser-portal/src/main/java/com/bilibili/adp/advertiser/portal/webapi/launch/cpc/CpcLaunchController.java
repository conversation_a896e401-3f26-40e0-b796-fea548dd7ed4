package com.bilibili.adp.advertiser.portal.webapi.launch.cpc;

import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.launchVo.MgkVideoVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.QueryCollageMediaVo;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.api.launch.dto.MgkVideoDto;
import com.bilibili.adp.launch.api.launch.dto.QueryMgkVideoParam;
import com.bilibili.adp.launch.api.service.ICpcLaunchService;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.collage.api.dto.CollageMediaDto;
import com.bilibili.collage.api.dto.QueryCollageMediaDto;
import com.bilibili.collage.api.soa.ISoaCollageMediaService;
import com.bilibili.ssa.platform.common.enums.DefinitionTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.bilibili.adp.web.framework.exception.ExceptionUtils.errorMsgBuild;
import static com.bilibili.adp.web.framework.exception.enums.ErrorCodeEnum.PARAM_ERROR;

/**
 * CPC投放端公用入口。
 *
 * <AUTHOR>
 * @date 2019年3月12日
 */
@RestController
@RequestMapping("/web_api/v1/cpc/launch")
@Api(value = "/cpc/launch", description = "CPC投放端公用入口")
public class CpcLaunchController extends BasicController {
    private final Logger LOGGER = LoggerFactory.getLogger(CpcLaunchController.class);

    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private ICpcLaunchService cpcLaunchService;
    @Resource
    private ISoaCollageMediaService iSoaCollageMediaService;

    @ApiOperation("获取账户下的视频素材")
    @ResponseBody
    @GetMapping(value = "/videos")
    public Response<Pagination<List<MgkVideoVo>>> videos(
            @ApiIgnore Context context,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "width") Integer width,
            @RequestParam(value = "height") Integer height,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "10") int size) {

        PageResult<MgkVideoDto> pageResult = cpcLaunchService.queryMgkVideoByPage(QueryMgkVideoParam.builder()
                .accountId(context.getAccountId())
                .page(page)
                .size(size)
                .name(name)
                .width(width)
                .height(height)
                .definitionType(DefinitionTypeEnum.P480.getCode())
                .build());

        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(),
                CollectionUtils.isEmpty(pageResult.getRecords()) ? Collections.emptyList() :
                        pageResult.getRecords().stream()
                                .map(this::convertToMgkVideoVo)
                                .collect(Collectors.toList())));
    }

    private MgkVideoVo convertToMgkVideoVo(MgkVideoDto mgkVideoDto) {
        return mgkVideoDto == null ? null : MgkVideoVo.builder()
                .cover(mgkVideoDto.getCover())
                .height(Utils.zeroIfNull(mgkVideoDto.getHeight()))
                .id(mgkVideoDto.getId())
                .name(mgkVideoDto.getName())
                .url(mgkVideoDto.getVideoUrl())
                .width(Utils.zeroIfNull(mgkVideoDto.getWidth()))
                .build();
    }

    @ApiOperation("获取账户下的素材")
    @ResponseBody
    @RequestMapping(value = "/medias", method = RequestMethod.GET)
    public Response<PageResult<CollageMediaDto>> medias(@ApiIgnore Context context, QueryCollageMediaVo vo) {
        Page page = new Page();
        page.setPageSize(vo.getSize());
        page.setPage(vo.getPage());
        QueryCollageMediaDto queryCollageMediaDto = buildQueryCollageMediaDto(vo);
        queryCollageMediaDto.setAccountIds(Collections.singletonList(context.getAccountId()));
        queryCollageMediaDto.setFilterNoMd5(true);
        queryCollageMediaDto.setRequestType(context.getRequestType().getId());
        return Response.SUCCESS(iSoaCollageMediaService.getMediaList(queryCollageMediaDto));
    }

    private QueryCollageMediaDto buildQueryCollageMediaDto(QueryCollageMediaVo vo) {
        Assert.isTrue(vo.getSize() <= 100, errorMsgBuild(PARAM_ERROR, "页面size不能大于100 "));
        Long startMtime = vo.getStart_mtime(), endMtime = vo.getEnd_mtime();
        return QueryCollageMediaDto.builder()
                .ids(vo.getIds())
                .mediaName(vo.getMedia_name())
                .mediaMd5(vo.getMedia_md5())
                .mediaTypes(vo.getMedia_types())
                .mediaOrigins(vo.getMedia_origins())
                .mediaRadios(vo.getMedia_radios())
                .width(vo.getWidth())
                .height(vo.getHeight())
                .patternIds(vo.getPattern_ids())
                .worksIds(vo.getWorks_ids())
                .pageInfo(Page.valueOf(
                        vo.getPage() == null ? 1 : vo.getPage(),
                        vo.getSize() == null ? 10 : vo.getSize()))
                .filterNoMd5(true)
                .startMtime(Utils.isPositive(startMtime) ? new Timestamp(startMtime) : null)
                .endMtime(Utils.isPositive(endMtime) ? new Timestamp(endMtime) : null)
                .build();
    }

}
