package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo;

import com.bilibili.adp.advertiser.portal.openapi.valid.annotation.SerializeFilter;
import com.bilibili.adp.advertiser.portal.webapi.v2.ocpx.vos.OcpxTargetVo;
import com.bilibili.adp.cpc.dto.DepthOptDataDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DepthOptDataVo {

    private Integer accountId;

    private BigDecimal target24HourRoi;
    private BigDecimal target7DayPayCost;

    /**
     * 24 小时 roi 限制
     */
    private BigDecimal target24HourRoiLimit;

    /**
     * 首日roi建议出价
     */
    private BigDecimal targetFirstDayRoi;

    /**
     * 首日 roi 限制
     */
    private BigDecimal targetFirstDayRoiLimit;

    public static DepthOptDataVo convert(DepthOptDataDto dto) {
        DepthOptDataVo dataVo = new DepthOptDataVo();
        BeanUtils.copyProperties(dto, dataVo);
        return dataVo;
    }

}
