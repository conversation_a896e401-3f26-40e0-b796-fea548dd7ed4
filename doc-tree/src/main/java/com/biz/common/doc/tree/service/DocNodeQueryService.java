package com.biz.common.doc.tree.service;

import com.biz.common.doc.tree.common.Pagination;
import com.biz.common.doc.tree.model.DocFlattenNode;
import com.biz.common.doc.tree.model.DocTreeNode;
import com.biz.common.doc.tree.service.vo.NodeBatchGetReq;
import com.biz.common.doc.tree.service.vo.NodeGetReq;
import com.biz.common.doc.tree.service.vo.NodeListRecursiveReq;
import com.biz.common.doc.tree.service.vo.NodeListReq;
import com.biz.common.doc.tree.service.vo.NodePageReq;
import com.biz.common.doc.tree.service.vo.NodeSearchReq;
import com.biz.common.doc.tree.service.vo.NodeSearchResult;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/7
 */
public interface DocNodeQueryService {


    /**
     * 获取以nodeId为root的子树
     *
     *
     * @param req
     * @param req {@link NodeListRecursiveReq#getDepth()} req.depth 从查询节点开始的深度，默认为1，即遍历一层； 如果是空，支持深度遍历所有, 是否对外待定
     * @param req {@link NodeListRecursiveReq#getNodeId()} req.nodeId 查询节点的id
     * @return 返回结果为从查询节点开始的树形结构
     */
    DocTreeNode subTree(NodeListRecursiveReq req);


    List<DocFlattenNode> listChildren(NodeListReq req);


    /**
     * 分页查询节点下的文档目录，对于分页的场景，不应该支持深度
     *
     * @param req {@link NodePageReq#getNodeId()} req.nodeId 查询节点的id
     * @return
     */
    Pagination<List<DocFlattenNode>> pageChildren(NodePageReq req);


    /**
     * @return
     * @throws IllegalArgumentException 如果节点不存在时抛出异常
     */
    DocFlattenNode getNode(NodeGetReq req);


    List<DocFlattenNode> batchGetNode(NodeBatchGetReq req);

    Pagination<List<NodeSearchResult>> searchDoc(NodeSearchReq req);


    List<NodeSearchResult> searchSuggest(NodeSearchReq req);

}
