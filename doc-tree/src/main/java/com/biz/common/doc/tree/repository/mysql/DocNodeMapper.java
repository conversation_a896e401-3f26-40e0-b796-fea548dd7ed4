package com.biz.common.doc.tree.repository.mysql;

import com.biz.common.doc.tree.model.DocFlattenNode;
import java.util.List;
import org.apache.ibatis.annotations.Param;


public interface DocNodeMapper {

    int deleteByPrimaryKey(Long nodeId);


    @Deprecated
    int insert(DocFlattenNode record);

    int insertSelective(DocFlattenNode record);

    DocFlattenNode selectByPrimaryKey(Long nodeId);

    int updateByPrimaryKeySelective(DocFlattenNode record);

    int updateByPrimaryKey(DocFlattenNode record);


    DocFlattenNode selectByNodeId(@Param("nodeId") Long nodeId);


    List<DocFlattenNode> selectByNodeIdIn(@Param("nodeIds") List<Long> nodeIds);


    List<DocFlattenNode> selectByPathLikeAndDepthLteEq(
            @Param("parentId") Long parentId,
            @Param("pathLike") String pathLike,
            @Param("depthLte") Integer depthLte,
            @Param("depthEq") Integer depthEq,
            @Param("docType") List<String> docType,
            @Param("nodeType") List<String> nodeType,
            @Param("isDeleted") Boolean isDeleted,
            @Param("isShow") Boolean isShow,
            @Param("excludeNodeIds") List<Long> excludeNodeIds,
            @Param("rootIds") List<Long> rootIds,
            @Param("orderBy") String sortClause,
            @Param("limit") Integer limit
    );


    List<DocFlattenNode> selectAllByIdGtAndLimit(
            @Param("nodeId") Long nodeId,
            @Param("limit") Integer limit
    );


    /**
     * 本质是计算child的数量
     *
     * @param path
     * @return
     */
    int countByPathEq(@Param("path") String path,

            @Param("isDeleted") Boolean isDeleted,
            @Param("isShow") Boolean isShow
    );

    int countByPathLike(@Param("path") String path,

            @Param("isDeleted") Boolean isDeleted,
            @Param("isShow") Boolean isShow

    );


}