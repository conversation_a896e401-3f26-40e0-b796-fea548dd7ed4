syntax = "proto3";

// use {app_id}.{version} as package name
package esports.service.v1;

import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "esports.service";

// specify golang package name
option go_package = "buf.bilibili.co/bapis/bapis-gen/esports/service;v1";
option java_multiple_files = true;
option java_package = "com.bapis.esports.service";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

// The greeting service definition.
service Esports {
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用，替换为operational/esportsservice/api.proto/AddContestFav
  //添加预约
  rpc LiveAddFav(FavRequest) returns (NoArgRequest);
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用，替换为operational/esportsservice/api.proto/DelContestFav
  //删除预约
  rpc LiveDelFav(FavRequest) returns (NoArgRequest);
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用，替换为operational/esportsservice/api.proto/GetContests
  //赛程数据 没有数据返回 -404
  rpc LiveContests(LiveContestsRequest) returns (LiveContestsReply);
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用，替换为operational/esportsservice/api.proto/GetContestSubscribers
  //赛程订阅用户列表
  rpc SubContestUsers(SubContestsRequest) returns (FavedUsersReply);
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用，替换为operational/esportsservice/api.proto/GetContestsByTime
  //按时间赛程列表
  rpc StimeContests(StimeContestsRequest) returns (LiveContestsReply);
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用，替换为operational/esportsservice/api.proto/GetGames
  //游戏数据
  rpc Games(GamesRequest) returns (GamesReply);
  // 基于赛程id更新关联赛季的预测版本
  rpc UpdateSeasonGuessVersion(UpdateSeasonGuessVersionRequest) returns (UpdateSeasonGuessVersionReply);
  //赛程列表
  rpc ContestList(ContestListRequest) returns (ContestListReply);
  //赛程游戏
  rpc GameMap(GameMapRequest) returns (GameMapReply);
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用，替换为operational/esportsservice/api.proto/GetContests
  // OTT赛程数据无缓存
  rpc OttContests(OttContestsRequest) returns (OttContestsReply);
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用，替换为operational/esportsservice/api.proto/GetContestSubscribers
  //赛程订阅用户列表新接口
  rpc SubContestUsersV2(SubContestUsersV2Request) returns (SubContestUsersV2Reply);
  // 获取比赛支持率信息
  rpc GetSupportRate(GetSupportRateReq) returns (GetSupportRateResp);
}

message  GetSupportRateReq {
  // 平台
  string platform = 1 [(gogoproto.jsontag) = "platform"];
  // 赛事ID eg. MSI
  string match_id = 2 [(gogoproto.jsontag) = "match_id"];
  // 赛事日期 eg. 20220525
  string match_date = 3 [(gogoproto.jsontag) = "match_date"];
  // 开赛时间 eg. 14:00
  string start_time = 4 [(gogoproto.jsontag) = "start_time"];
}

message GetSupportRateResp {
  // code
  int32 code = 1 [(gogoproto.jsontag) = "code"];
  // message
  string message = 2 [(gogoproto.jsontag) = "message"];

  repeated TeamRatioInfo team_ratios = 3 [(gogoproto.jsontag) = "team_ratios"];
}

message  TeamRatioInfo {
  // 队伍名称 eg. RNG
  string vote_team = 1 [(gogoproto.jsontag) = "vote_team"];
  // 队伍支持率 eg. 71.25
  string vote_ratio = 2 [(gogoproto.jsontag) = "vote_ratio"];
  // 赛事ID eg. MSI
  string match_id = 3 [(gogoproto.jsontag) = "match_id"];
  // 赛事日期 eg. 20220525
  string match_date = 4 [(gogoproto.jsontag) = "match_date"];
  // 开赛时间 eg. 14:00
  string start_time = 5 [(gogoproto.jsontag) = "start_time"];
}


//赛程订阅用户列表新接口请求参数
message SubContestUsersV2Request {
  int64 cid = 1 [(gogoproto.moretags) = 'validate:"required"'];
  int64 cursor = 2;
  int32 cursor_size = 3;
}

//赛程订阅用户列表新接口返回
message SubContestUsersV2Reply {
  int64 cursor = 1;
  repeated User user = 2;
}

message UpdateSeasonGuessVersionRequest {
  int64 match_id = 1;
}

message UpdateSeasonGuessVersionReply {
  int64 status = 1;
}

message FavedUsersReply {
  ModelPage page = 1;
  repeated User user = 2;
}

message ModelPage {
  int32 num = 1;
  int32 size = 2;
  int32 count = 3;
}

message User {
  int64 id = 1;
  int64 oid = 2;
  int64 mid = 3;
  int32 typ = 4;
  int32 state = 5;
  int64 ctime = 6;
  int64 mtime = 7;
}

// NoArgReq
message NoArgRequest {}

//收藏请求参数
message FavRequest {
  int64 mid = 1;
  int64 cid = 2;
}

//赛程请求参数
message LiveContestsRequest {
  int64 mid = 1;
  repeated int64 cids = 2;
}

//订阅赛程用户请求参数
message SubContestsRequest {
  int64 cid = 1 [(gogoproto.moretags) = 'validate:"required"'];
  int32 pn = 2 [(gogoproto.moretags) = 'validate:"min=1"'];
  int32 ps = 3 [(gogoproto.moretags) = 'validate:"min=1,max=5000"'];
}

//开始时间赛程请求参数
message StimeContestsRequest {
  string stime = 1 [(gogoproto.moretags) = 'validate:"required"'];
  string etime = 2 [(gogoproto.moretags) = 'validate:"required"'];
  repeated int64 roomids = 3 [(gogoproto.moretags) = 'validate:"required"'];
  int64 mid = 4;
}

//赛程请求返回
message LiveContestsReply {
  repeated Contest Contests = 1 [(gogoproto.jsontag) = "-"];
}

//游戏请求参数
message GamesRequest {
  repeated int64 gids = 1;
}

//游戏请求返回
message GamesReply {
  repeated Game Games = 1 [(gogoproto.jsontag) = "-"];
}

//赛程列表请求参数
message ContestListRequest {
  int64 mid = 1;
  int64 sort = 2;
  int64 match_id = 3;
  int64 tid = 4;
  string stime = 5;
  string etime = 6;
  repeated int64 sids = 7;
  repeated int64 cids = 8;
  int64 guess_type = 9;
  int64 pn = 10 [(gogoproto.moretags) = 'validate:"min=1"'];
  int64 ps = 11 [(gogoproto.moretags) = 'validate:"min=1,max=100"'];
}

//赛程列表请求返回
message ContestListReply {
  ModelPage page = 1;
  repeated Contest Contests = 2 [(gogoproto.jsontag) = "-"];
}

//赛程数据
message Contest {
  int64 ID = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  //比赛阶段
  string GameStage = 2
      [(gogoproto.jsontag) = "game_stage", json_name = "game_stage"];
  //比赛开始时间
  int64 Stime = 3 [(gogoproto.jsontag) = "stime", json_name = "stime"];
  //比赛结束时间
  int64 Etime = 4 [(gogoproto.jsontag) = "etime", json_name = "etime"];
  //主场队伍id
  int64 HomeID = 5 [(gogoproto.jsontag) = "home_id", json_name = "home_id"];
  //客场队伍id
  int64 AwayID = 6 [(gogoproto.jsontag) = "away_id", json_name = "away_id"];
  //主场分数
  int64 HomeScore = 7
      [(gogoproto.jsontag) = "home_score", json_name = "home_score"];
  //客场分数
  int64 AwayScore = 8
      [(gogoproto.jsontag) = "away_score", json_name = "away_score"];
  //直播房间号
  int64 LiveRoom = 9
      [(gogoproto.jsontag) = "live_room", json_name = "live_room"];
  //回播房间号
  int64 Aid = 10 [(gogoproto.jsontag) = "aid", json_name = "aid"];
  //集锦房间号
  int64 Collection = 11
      [(gogoproto.jsontag) = "collection", json_name = "collection"];
  //订阅状态 3-已订阅  其它-未订阅
  int64 GameState = 12
      [(gogoproto.jsontag) = "game_state", json_name = "game_state"];
  // 0 启用 1 冻结
  string Dic = 13 [(gogoproto.jsontag) = "dic", json_name = "dic"];
  //创建时间
  int64 Status = 14 [(gogoproto.jsontag) = "status", json_name = "status"];
  //季度id
  int64 Sid = 15 [(gogoproto.jsontag) = "sid", json_name = "sid"];
  //赛事id
  int64 Mid = 16 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  //赛程类型：0普通1特殊
  int64 Special = 17 [(gogoproto.jsontag) = "special", json_name = "special"];
  //胜利战队
  int64 SuccessTeam = 18
      [(gogoproto.jsontag) = "success_team", json_name = "success_team"];
  //赛程名称
  string SpecialName = 19
      [(gogoproto.jsontag) = "special_name", json_name = "special_name"];
  //胜利文案
  string SpecialTips = 20
      [(gogoproto.jsontag) = "special_tips", json_name = "special_tips"];
  //赛程图片
  string SpecialImage = 21
      [(gogoproto.jsontag) = "special_image", json_name = "special_image"];
  //回播房间号url
  string Playback = 22
      [(gogoproto.jsontag) = "playback", json_name = "playback"];
  //集锦房间号url
  string CollectionURL = 23
      [(gogoproto.jsontag) = "collection_url", json_name = "collection_url"];
  //集锦房间号url
  string LiveURL = 24
      [(gogoproto.jsontag) = "live_url", json_name = "live_url"];
  //比赛数据页类型 0：无 1：LOL 2:DATA2
  int64 DataType = 25
      [(gogoproto.jsontag) = "data_type", json_name = "data_type"];
  //雷达数据match_id
  int64 MatchID = 26 [(gogoproto.jsontag) = "match_id", json_name = "match_id"];
  //赛季
  Season season = 27 [(gogoproto.jsontag) = "season", json_name = "season"];
  //主场战队信息
  Team HomeTeam = 28
      [(gogoproto.jsontag) = "home_team", json_name = "home_team"];
  //客场战队信息
  Team AwayTeam = 29
      [(gogoproto.jsontag) = "away_team", json_name = "away_team"];
  //特殊赛程胜利队信息
  Team SuccessTeaminfo = 30 [
    (gogoproto.jsontag) = "success_teaminfo",
    json_name = "success_teaminfo"
  ];
  //是否有竞猜
  int64 GuessShow = 31
      [(gogoproto.jsontag) = "guess_show", json_name = "guess_show"];
  //比赛阶段1
  string GameStage1 = 32 [(gogoproto.jsontag) = "game_stage1", json_name = "game_stage1"];
  //比赛阶段2
  string GameStage2 = 33 [(gogoproto.jsontag) = "game_stage2", json_name = "game_stage2"];
  //跳转地址
  string JumpURL = 34 [(gogoproto.jsontag) = "jump_url", json_name = "jump_url"];
  bool CanGuess = 35 [(gogoproto.jsontag) = "can_guess", json_name = "can_guess"];
  string GuessLink = 36 [(gogoproto.jsontag) = "guess_link", json_name = "guess_link"];
  bool IsOlympic = 37;
  // 仅奥运赛程使用，其他场景无需关注； 0不展示集锦及敬请期待按钮；1按照比赛状态及配置信息正常展示
  int32 OlympicShowRule = 38;
  // 赛程的冻结状态，此状态为冻结时不吐出详情
  int64  ContestFreeze   = 39 [(gogoproto.jsontag) = "contest_freeze"];
  // 比赛状态，枚举：1未开始，2进行中，3已结束
  int64  ContestStatus   = 40 [(gogoproto.jsontag) = "contest_status"];
  // 前瞻url
  string Prospect = 41 [(gogoproto.jsontag) = "prospect", json_name = "prospect"];
  // 赛后视频url
  string AfterContestVideo = 42 [(gogoproto.jsontag) = "after_contest_video", json_name = "after_contest_video"];
  //主场总分
  int64  HomeTotalScore       = 43 [(gogoproto.jsontag) = "home_total_score"];
  //客场总分
  int64  AwayTotalScore       = 44 [(gogoproto.jsontag) = "away_total_score"];
    // 比赛最热选手
  PlayerGradeCard hottest_player = 45 [(gogoproto.jsontag) = "hottest_player"];
}

message PlayerGradeCard {
  // 昵称
  string nickname = 1;
  // 选手id
  int64 player_id = 2;
  // 头像
  string portrait = 3;
  // 辣评
  string hot_remark = 4;
  // 总评(参与打分的人数)
  int64 grade_users = 5;
  // 评分均值
  string avg_grade = 6;
   // 选手位置(上单/打野/中单/射手/辅助)
  string position = 7;
   // 打分id
  string grade_id = 8;
  // 当前bo
  int64 bo = 9;
  // 跳转地址
  string jump_url = 10;
}

//赛季数据
message Season {
  int64 ID = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  //赛事id
  int64 Mid = 2 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  //简称
  string Title = 3 [(gogoproto.jsontag) = "title", json_name = "title"];
  //全称
  string SubTitle = 4
      [(gogoproto.jsontag) = "sub_title", json_name = "sub_title"];
  //开始时间
  int64 Stime = 5 [(gogoproto.jsontag) = "stime", json_name = "stime"];
  //结束时间
  int64 Etime = 6 [(gogoproto.jsontag) = "etime", json_name = "etime"];
  //主办方
  string Sponsor = 7 [(gogoproto.jsontag) = "sponsor", json_name = "sponsor"];
  // logo
  string Logo = 8 [(gogoproto.jsontag) = "logo", json_name = "logo"];
  //备注
  string Dic = 9 [(gogoproto.jsontag) = "dic", json_name = "dic"];
  // 0 启用  1 冻结
  int64 Status = 10 [(gogoproto.jsontag) = "status", json_name = "status"];
  // 0 启用  1 冻结
  int64 Rank = 11 [(gogoproto.jsontag) = "rank", json_name = "rank"];
  //是否在移动端展示: 0否1是
  int64 IsApp = 12 [(gogoproto.jsontag) = "is_app", json_name = "is_app"];
  //赛季URL
  string URL = 13 [(gogoproto.jsontag) = "url", json_name = "url"];
  //比赛数据页焦点图
  string DataFocus = 14
      [(gogoproto.jsontag) = "data_focus", json_name = "data_focus"];
  //比赛数据页焦点图url
  string FocusURL = 15
      [(gogoproto.jsontag) = "focus_url", json_name = "focus_url"];
  //搜索赛程卡标题底图
  string SearchImage = 16
      [(gogoproto.jsontag) = "search_image", json_name = "search_image"];
  // LOGO全
  string LogoFull = 17
      [(gogoproto.jsontag) = "logo_full", json_name = "logo_full"];
  //同步平台
  int64 SyncPlatform = 18
      [(gogoproto.jsontag) = "sync_platform", json_name = "sync_platform"];
}

//赛季数据
message Team {
  int64 ID = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  //简称
  string Title = 2 [(gogoproto.jsontag) = "title", json_name = "title"];
  //全称
  string SubTitle = 3
      [(gogoproto.jsontag) = "sub_title", json_name = "sub_title"];
  //英文全称
  string ETitle = 4 [(gogoproto.jsontag) = "e_title", json_name = "e_title"];
  //地区
  string Area = 5 [(gogoproto.jsontag) = "area", json_name = "area"];
  //英文全称
  string Logo = 7 [(gogoproto.jsontag) = "logo", json_name = "logo"];
  //地区
  int64 UID = 6 [(gogoproto.jsontag) = "uid", json_name = "uid"];
  //成员
  string Members = 8 [(gogoproto.jsontag) = "members", json_name = "members"];
  //备注
  string Dic = 9 [(gogoproto.jsontag) = "dic", json_name = "dic"];
  //战队类型
  int64 TeamType = 10
      [(gogoproto.jsontag) = "team_type", json_name = "team_type"];
  // LOGO全
  string LogoFull = 11
      [(gogoproto.jsontag) = "logo_full", json_name = "logo_full"];
}

//游戏数据
message Game {
  int64 ID = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  //中文全称
  string Title = 2 [(gogoproto.jsontag) = "title", json_name = "title"];
  //游戏简称
  string SubTitle = 3
      [(gogoproto.jsontag) = "sub_title", json_name = "sub_title"];
  //英文全称
  string ETitle = 4 [(gogoproto.jsontag) = "e_title", json_name = "e_title"];
  //平台
  string Plat = 5 [(gogoproto.jsontag) = "plat", json_name = "plat"];
  //游戏类型
  int64 GameType = 6
      [(gogoproto.jsontag) = "game_type", json_name = "game_type"];
  //游戏 logo
  string Logo = 7 [(gogoproto.jsontag) = "logo", json_name = "logo"];
  //发行商
  string Publisher = 8
      [(gogoproto.jsontag) = "publisher", json_name = "publisher"];
  //运行商
  string Operations = 9
      [(gogoproto.jsontag) = "operations", json_name = "operations"];
  //发布时间
  int64 PbTime = 10 [(gogoproto.jsontag) = "pb_time", json_name = "pb_time"];
  //备注
  string Dic = 11 [(gogoproto.jsontag) = "dic", json_name = "dic"];
  // LOGO全
  string LogoFull = 12
      [(gogoproto.jsontag) = "logo_full", json_name = "logo_full"];
}

// 赛程游戏请求参数
message GameMapRequest {
  repeated int64 cids = 1;
}

// 赛程游戏请求返回
message GameMapReply {
  map<int64, Game> games = 1;
}

// Ott赛程请求参数
message OttContestsRequest {
  int64 mid = 1;
  repeated int64 cids = 2;
}

// Ott赛程请求返回
message OttContestsReply {
  repeated Contest Contests = 1;
}