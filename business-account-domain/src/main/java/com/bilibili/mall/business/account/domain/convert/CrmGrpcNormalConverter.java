package com.bilibili.mall.business.account.domain.convert;

import com.bapis.ad.crm.account.AccountType;
import com.bapis.ad.crm.account.CreateAccountReq;
import com.bapis.ad.crm.account.FinanceType;
import com.bapis.ad.crm.account.UserType;
import com.bapis.ad.crm.common.*;
import com.bapis.ad.crm.customer.*;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.dto.*;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class CrmGrpcNormalConverter {
    public static CrmBihuoQualificationDto convertToCrmBihuoQualificationDto(com.bapis.ad.crm.audit.QualificationInfo qualificationInfo) {
        if (qualificationInfo == null) {
            return null;
        }
        if (Objects.equals(qualificationInfo.getIsExists(), 0)) {
            return null;
        }

        CrmBihuoQualificationDto dto = new CrmBihuoQualificationDto();

        // 设置必火企业资质信息
        if (Objects.nonNull(qualificationInfo.getOperationName())) {
            dto.setOperationName(qualificationInfo.getOperationName());
        }
        if (Objects.nonNull(qualificationInfo.getOperationNumberAddr())) {
            dto.setOperationNumberAddr(qualificationInfo.getOperationNumberAddr());
        }
        if (Objects.nonNull(qualificationInfo.getOperationNumber())) {
            dto.setOperationNumber(qualificationInfo.getOperationNumber());
        }
        if (Objects.nonNull(qualificationInfo.getEmail())) {
            dto.setEmail(qualificationInfo.getEmail());
        }
        if (Objects.nonNull(qualificationInfo.getAddress())) {
            dto.setAddress(qualificationInfo.getAddress());
        }
        if (Objects.nonNull(qualificationInfo.getEnterpriseName())) {
            dto.setEnterpriseName(qualificationInfo.getEnterpriseName());
        }
        if (Objects.nonNull(qualificationInfo.getEnterpriseFirstIndustry())) {
            dto.setEnterpriseFirstIndustry(qualificationInfo.getEnterpriseFirstIndustry());
        }
        if (Objects.nonNull(qualificationInfo.getEnterpriseSecondIndustry())) {
            dto.setEnterpriseSecondIndustry(qualificationInfo.getEnterpriseSecondIndustry());
        }
        if (Objects.nonNull(qualificationInfo.getOrganizationType())) {
            dto.setOrganizationType(qualificationInfo.getOrganizationType());
        }
        if (Objects.nonNull(qualificationInfo.getEnterpriseScale())) {
            dto.setEnterpriseScale(qualificationInfo.getEnterpriseScale());
        }
        if (Objects.nonNull(qualificationInfo.getRegisteredCapital())) {
            dto.setRegisteredCapital(qualificationInfo.getRegisteredCapital());
        }
        if (Objects.nonNull(qualificationInfo.getBusinessLicenceCode())) {
            dto.setBusinessLicenceCode(qualificationInfo.getBusinessLicenceCode());
        }
        if (Objects.nonNull(qualificationInfo.getOfficialWebsite())) {
            dto.setOfficialWebsite(qualificationInfo.getOfficialWebsite());
        }
        if (qualificationInfo.getBusinessLicencePictureCount() > 0) {
            dto.setBusinessLicencePicture(qualificationInfo.getBusinessLicencePictureList());
        }
        if (qualificationInfo.getOrganizationRegisterPictureCount() > 0) {
            dto.setOrganizationRegisterPicture(qualificationInfo.getOrganizationRegisterPictureList());
        }
        if (qualificationInfo.getSupplementPictureCount() > 0) {
            dto.setSupplementPicture(qualificationInfo.getSupplementPictureList());
        }
        if (Objects.nonNull(qualificationInfo.getQualificationTypeId())) {
            dto.setQualificationTypeId(qualificationInfo.getQualificationTypeId());
        }
        if (Objects.nonNull(qualificationInfo.getBusinessLicenceExpireDate())) {
            dto.setBusinessLicenceExpireDate(qualificationInfo.getBusinessLicenceExpireDate());
        }
        if (Objects.nonNull(qualificationInfo.getIsExists())) {
            dto.setIsExists(qualificationInfo.getIsExists());
        }

        return dto;
    }

    public static CustomerInfoDetailDto convertToCustomerInfoDetailDto(CustomerInfoDetail customerInfoDetail) {
        if (customerInfoDetail == null) {
            return null;
        }

        CustomerInfoDetailDto dto = new CustomerInfoDetailDto();
        dto.setId(customerInfoDetail.getId());
        dto.setCustomerCategory(customerInfoDetail.getCustomerCategory());
        dto.setCustomerCategoryDesc(customerInfoDetail.getCustomerCategoryDesc());
        dto.setUserName(customerInfoDetail.getUserName());
        dto.setBusinessLicenceCode(customerInfoDetail.getBusinessLicenceCode());
        dto.setIsBusinessLicenceIndefinite(customerInfoDetail.getIsBusinessLicenceIndefinite());
        dto.setIsBusinessLicenceIndefiniteDesc(customerInfoDetail.getIsBusinessLicenceIndefiniteDesc());
        dto.setBusinessLicenceExpireDate(customerInfoDetail.getBusinessLicenceExpireDate());
        dto.setQualificationTypeId(customerInfoDetail.getQualificationTypeId());
        dto.setQualificationTypeIdDesc(customerInfoDetail.getQualificationTypeIdDesc());
        dto.setUnitedFirstIndustryId(customerInfoDetail.getUnitedFirstIndustryId());
        dto.setUnitedSecondIndustryId(customerInfoDetail.getUnitedSecondIndustryId());
        dto.setUnitedThirdIndustryId(customerInfoDetail.getUnitedThirdIndustryId());
        dto.setUnitedFirstIndustryName(customerInfoDetail.getUnitedFirstIndustryName());
        dto.setUnitedSecondIndustryName(customerInfoDetail.getUnitedSecondIndustryName());
        dto.setUnitedThirdIndustryName(customerInfoDetail.getUnitedThirdIndustryName());
        dto.setLinkmanName(customerInfoDetail.getInternalLinkman());
        dto.setLinkmanPhone(customerInfoDetail.getLinkmanPhone());
        dto.setLinkmanEmail(customerInfoDetail.getLinkmanEmail());
        dto.setLinkmanAddress(customerInfoDetail.getLinkmanAddress());
        dto.setIcpRecordNumber(customerInfoDetail.getIcpRecordNumber());
        dto.setPersonalIdCardType(customerInfoDetail.getPersonalIdCardType());
        dto.setPersonalIdCardTypeDesc(customerInfoDetail.getPersonalIdCardTypeDesc());
        dto.setPersonalIdCardNumber(customerInfoDetail.getPersonalIdCardNumber());
        dto.setIsPersonalIdCardIndefinite(customerInfoDetail.getIsPersonalIdCardIndefinite());
        dto.setIsPersonalIdCardIndefiniteDesc(customerInfoDetail.getIsPersonalIdCardIndefiniteDesc());
        dto.setPersonalIdCardExpireDate(customerInfoDetail.getPersonalIdCardExpireDate());
        dto.setPersonalPhone(customerInfoDetail.getPersonalPhone());
        dto.setIsAgent(customerInfoDetail.getIsAgent());
        dto.setIsAgentDesc(customerInfoDetail.getIsAgentDesc());
        dto.setIsInner(customerInfoDetail.getIsInner());
        dto.setIsInnerDesc(customerInfoDetail.getIsInnerDesc());
        dto.setDepartmentId(customerInfoDetail.getDepartmentId());
        dto.setDepartmentName(customerInfoDetail.getDepartmentName());
        dto.setAreaId(customerInfoDetail.getAreaId());
//        dto.setAreaName(customerInfoDetail.getAreaName());
//        dto.setCreateTime(customerInfoDetail.getCreateTime());
//        dto.setUpdateTime(customerInfoDetail.getUpdateTime());

        return dto;
    }

    public static CreateCustomerReq convertToCreateCustomerReq(RegisterEnterpriseAccountDto registerEnterpriseAccountDto) {
        if (registerEnterpriseAccountDto == null) {
            return null;
        }

        CreateCustomerReq.Builder builder = CreateCustomerReq.newBuilder();

        // 设置基本信息
        if (registerEnterpriseAccountDto.getIsAgent() != null) {
            builder.setIsAgent(integerToYesOrNo(registerEnterpriseAccountDto.getIsAgent()));
        }

        if (registerEnterpriseAccountDto.getIsInner() != null) {
            builder.setIsInner(integerToYesOrNo(registerEnterpriseAccountDto.getIsInner()));
        }

        if (registerEnterpriseAccountDto.getCustomerCategory() != null) {
            builder.setCustomerCategory(integerToCustomerCategory(registerEnterpriseAccountDto.getCustomerCategory()));
        }

        if (registerEnterpriseAccountDto.getIsBusinessLicenceIndefinite() != null) {
            builder.setIsBusinessLicenceIndefinite(integerToYesOrNo(registerEnterpriseAccountDto.getIsBusinessLicenceIndefinite()));
        }

        // 设置企业信息
        if (registerEnterpriseAccountDto.getBusinessLicenseCode() != null) {
            builder.setBusinessLicenceCode(registerEnterpriseAccountDto.getBusinessLicenseCode());
        }
        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getBusinessLicensePics())) {
            List<CustomerAttachInfo> businessLicensePics = registerEnterpriseAccountDto.getBusinessLicensePics().stream().map(pic -> CustomerAttachInfo.newBuilder().setUrl(pic).build()).collect(Collectors.toList());
            builder.addAllBusinessLicencePics(businessLicensePics);

        }
        if (Objects.nonNull(registerEnterpriseAccountDto.getBusinessLicenseExpireDate())) {
            builder.setBusinessLicenceExpireDate(registerEnterpriseAccountDto.getBusinessLicenseExpireDate());
        }

        if (registerEnterpriseAccountDto.getUserName() != null) {
            builder.setUserName(registerEnterpriseAccountDto.getUserName());
        }

        if (registerEnterpriseAccountDto.getLinkmanPhone() != null) {
            builder.setLinkmanPhone(registerEnterpriseAccountDto.getLinkmanPhone());
        }

        if (registerEnterpriseAccountDto.getLinkmanEmail() != null) {
            builder.setLinkmanEmail(registerEnterpriseAccountDto.getLinkmanEmail());
        }

        if (registerEnterpriseAccountDto.getLinkmanAddress() != null) {
            builder.setLinkmanAddress(registerEnterpriseAccountDto.getLinkmanAddress());
        }

        if (registerEnterpriseAccountDto.getQualificationTypeId() != null) {
            builder.setQualificationTypeId(registerEnterpriseAccountDto.getQualificationTypeId());
        }

        if (registerEnterpriseAccountDto.getIcpRecordNumber() != null) {
            builder.setIcpRecordNumber(registerEnterpriseAccountDto.getIcpRecordNumber());
        } else {
            builder.setIcpRecordNumber("-");
        }

        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getIcpRecordPics())) {
            List<CustomerAttachInfo> icpPicList = registerEnterpriseAccountDto.getIcpRecordPics()
                    .stream()
                    .map(pic -> CustomerAttachInfo.newBuilder()
                            .setUrl(pic)
                            .build())
                    .collect(Collectors.toList());
            builder.addAllIcpPics(icpPicList);
        } else {
            builder.addAllIcpPics(Collections.singletonList(CustomerAttachInfo.newBuilder().setUrl("https://i0.hdslb.com/bfs/kfptfe/floor/58fa0b09468231834ca3baed3862bf83d2b1bc41.png").build()));
        }

        // 设置行业信息
        if (registerEnterpriseAccountDto.getUnitedFirstIndustryId() != null) {
            builder.setUnitedFirstIndustryId(registerEnterpriseAccountDto.getUnitedFirstIndustryId());
        }

        if (registerEnterpriseAccountDto.getUnitedSecondIndustryId() != null) {
            builder.setUnitedSecondIndustryId(registerEnterpriseAccountDto.getUnitedSecondIndustryId());
        }

        if (registerEnterpriseAccountDto.getUnitedThirdIndustryId() != null) {
            builder.setUnitedThirdIndustryId(registerEnterpriseAccountDto.getUnitedThirdIndustryId());
        }

        // 设置部门和区域信息
        if (registerEnterpriseAccountDto.getDepartmentId() != null) {
            builder.setDepartmentId(registerEnterpriseAccountDto.getDepartmentId());
        }

        if (registerEnterpriseAccountDto.getAreaId() != null) {
            builder.setAreaId(registerEnterpriseAccountDto.getAreaId());
        }
        if (Objects.nonNull(registerEnterpriseAccountDto.getGroupId())) {
            builder.setGroupId(registerEnterpriseAccountDto.getGroupId());
        }
        if (Objects.nonNull(registerEnterpriseAccountDto.getDomain())) {
            builder.addAllDomains(Collections.singletonList(registerEnterpriseAccountDto.getDomain()));
        } else {
            builder.addAllDomains(Collections.singletonList("https://www.bilibili.com"));
        }
        List<CustomerAdditionalQualificationInfo> customerAdditionalQualificationInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getQualifications())) {
            List<CustomerAdditionalQualificationInfo> originalCustomerAdditionalQualificationInfoList = registerEnterpriseAccountDto.getQualifications()
                    .stream()
                    .map(qualification -> CustomerAdditionalQualificationInfo.newBuilder()
                            .setUrl(qualification.getUrl())
                            .setSpecialInfoTypeId(qualification.getSpecialInfoTypeId())
                            .setExpireDate(qualification.getExpireDate())
                            .build())
                    .collect(Collectors.toList());
            customerAdditionalQualificationInfoList.addAll(originalCustomerAdditionalQualificationInfoList);

        }

        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getBusinessAuthPics())) {
            List<CustomerAdditionalQualificationInfo> businessAuthPics = registerEnterpriseAccountDto.getBusinessAuthPics()
                    .stream()
                    .map(qualification -> CustomerAdditionalQualificationInfo.newBuilder()
                            .setUrl(qualification.getUrl())
                            .setSpecialInfoTypeId(qualification.getSpecialInfoTypeId())
                            .setExpireDate(qualification.getExpireDate())
                            .build())
                    .collect(Collectors.toList());
            customerAdditionalQualificationInfoList.addAll(businessAuthPics);
        }
        if (!customerAdditionalQualificationInfoList.isEmpty()) {
            builder.addAllQualifications(customerAdditionalQualificationInfoList);
        }
        builder.setOperator(getDefaultOperator());

        if (Objects.nonNull(registerEnterpriseAccountDto.getPromotionType())) {
            builder.setPromotionTypeValue(registerEnterpriseAccountDto.getPromotionType());
        }
        builder.setInternalLinkman(registerEnterpriseAccountDto.getLinkmanName());
        if (Objects.nonNull(registerEnterpriseAccountDto.getStoreAddress())) {
            builder.setStoreAddress(registerEnterpriseAccountDto.getStoreAddress());
        }
        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getStoreEntityPics())) {
            List<CustomerAttachInfo> storeEntityPics = registerEnterpriseAccountDto.getStoreEntityPics()
                    .stream()
                    .map(pic -> CustomerAttachInfo.newBuilder()
                            .setUrl(pic)
                            .build())
                    .collect(Collectors.toList());
            builder.addAllStoreEntityPics(storeEntityPics);
        }
        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getStoreItemPics())) {
            List<CustomerAttachInfo> storeItemPics = registerEnterpriseAccountDto.getStoreItemPics()
                    .stream()
                    .map(pic -> CustomerAttachInfo.newBuilder()
                            .setUrl(pic)
                            .build())
                    .collect(Collectors.toList());
            builder.addAllStoreItemPics(storeItemPics);
        }

        return builder.build();
    }

    public static CreateCustomerReq convertToCreateCustomerReq(RegisterPersonalAccountDto registerPersonalAccountDto) {
        if (registerPersonalAccountDto == null) {
            return null;
        }

        CreateCustomerReq.Builder builder = CreateCustomerReq.newBuilder();

        // 设置基本信息
        if (registerPersonalAccountDto.getIsAgent() != null) {
            builder.setIsAgent(integerToYesOrNo(registerPersonalAccountDto.getIsAgent()));
        }

        if (registerPersonalAccountDto.getIsInner() != null) {
            builder.setIsInner(integerToYesOrNo(registerPersonalAccountDto.getIsInner()));
        }

        if (registerPersonalAccountDto.getCustomerCategory() != null) {
            builder.setCustomerCategory(integerToCustomerCategory(registerPersonalAccountDto.getCustomerCategory()));
        }

        // 设置个人证件信息
        if (registerPersonalAccountDto.getIsPersonalIdCardIndefinite() != null) {
            builder.setIsPersonalIdCardIndefinite(integerToYesOrNo(registerPersonalAccountDto.getIsPersonalIdCardIndefinite()));
        }

        if (registerPersonalAccountDto.getPersonalIdCardType() != null) {
            builder.setPersonalIdCardType(integerToPersonalIdCardType(registerPersonalAccountDto.getPersonalIdCardType()));
        }

        if (registerPersonalAccountDto.getPersonalPhone() != null) {
            builder.setPersonalPhone(registerPersonalAccountDto.getPersonalPhone());
        }

        if (registerPersonalAccountDto.getPersonalIdCardNumber() != null) {
            builder.setPersonalIdCardNumber(registerPersonalAccountDto.getPersonalIdCardNumber());
        }

        if (registerPersonalAccountDto.getPersonalIdCardExpireDate() != null) {
            builder.setPersonalIdCardExpireDate(registerPersonalAccountDto.getPersonalIdCardExpireDate());
        }

        // 设置行业信息
        if (registerPersonalAccountDto.getUnitedFirstIndustryId() != null) {
            builder.setUnitedFirstIndustryId(registerPersonalAccountDto.getUnitedFirstIndustryId());
        }

        if (registerPersonalAccountDto.getUnitedSecondIndustryId() != null) {
            builder.setUnitedSecondIndustryId(registerPersonalAccountDto.getUnitedSecondIndustryId());
        }

        if (registerPersonalAccountDto.getUnitedThirdIndustryId() != null) {
            builder.setUnitedThirdIndustryId(registerPersonalAccountDto.getUnitedThirdIndustryId());
        }

        // 设置部门和区域信息
        if (registerPersonalAccountDto.getDepartmentId() != null) {
            builder.setDepartmentId(registerPersonalAccountDto.getDepartmentId());
        }

        if (registerPersonalAccountDto.getAreaId() != null) {
            builder.setAreaId(registerPersonalAccountDto.getAreaId());
        }
        if (CollectionUtils.isNotEmpty(registerPersonalAccountDto.getQualifications())) {
            List<CustomerAdditionalQualificationInfo> customerAdditionalQualificationInfoList = registerPersonalAccountDto.getQualifications()
                    .stream()
                    .map(qualification -> CustomerAdditionalQualificationInfo.newBuilder()
                            .setUrl(qualification.getUrl())
                            .setSpecialInfoTypeId(qualification.getSpecialInfoTypeId())
                            .setExpireDate(qualification.getExpireDate())
                            .build())
                    .collect(Collectors.toList());
            builder.addAllQualifications(customerAdditionalQualificationInfoList);
        }
        if (Objects.nonNull(registerPersonalAccountDto.getUserName())) {
            builder.setUserName(registerPersonalAccountDto.getUserName());
        }
        if (CollectionUtils.isNotEmpty(registerPersonalAccountDto.getPersonalIdCardPics())) {
            List<CustomerAttachInfo> personalIdCardPicList = registerPersonalAccountDto.getPersonalIdCardPics().stream()
                    .map(customerAttachInfoDto -> CustomerAttachInfo.newBuilder()
                            .setUrl(customerAttachInfoDto.getUrl())
                            .build())
                    .collect(Collectors.toList());
            builder.addAllPersonIdCardPics(personalIdCardPicList);
        }
        if (Objects.nonNull(registerPersonalAccountDto.getDomain())) {
            builder.addAllDomains(Collections.singletonList(registerPersonalAccountDto.getDomain()));
        }
        builder.setOperator(getDefaultOperator());

        return builder.build();
    }

    public static UpdateCustomerReq convertToUpdateCustomerReq(RegisterPersonalAccountDto registerPersonalAccountDto) {
        if (registerPersonalAccountDto == null) {
            return null;
        }

        UpdateCustomerReq.Builder builder = UpdateCustomerReq.newBuilder();

        // 设置基本信息
        if (registerPersonalAccountDto.getIsAgent() != null) {
            builder.setIsAgent(integerToYesOrNo(registerPersonalAccountDto.getIsAgent()));
        }

        if (registerPersonalAccountDto.getIsInner() != null) {
            builder.setIsInner(integerToYesOrNo(registerPersonalAccountDto.getIsInner()));
        }

        if (registerPersonalAccountDto.getCustomerCategory() != null) {
            builder.setCustomerCategory(integerToCustomerCategory(registerPersonalAccountDto.getCustomerCategory()));
        }

        // 设置个人证件信息
        if (registerPersonalAccountDto.getIsPersonalIdCardIndefinite() != null) {
            builder.setIsPersonalIdCardIndefinite(integerToYesOrNo(registerPersonalAccountDto.getIsPersonalIdCardIndefinite()));
        }

        if (registerPersonalAccountDto.getPersonalIdCardType() != null) {
            builder.setPersonalIdCardType(integerToPersonalIdCardType(registerPersonalAccountDto.getPersonalIdCardType()));
        }

        if (registerPersonalAccountDto.getPersonalPhone() != null) {
            builder.setPersonalPhone(registerPersonalAccountDto.getPersonalPhone());
        }

        if (registerPersonalAccountDto.getPersonalIdCardNumber() != null) {
            builder.setPersonalIdCardNumber(registerPersonalAccountDto.getPersonalIdCardNumber());
        }

        if (registerPersonalAccountDto.getPersonalIdCardExpireDate() != null) {
            builder.setPersonalIdCardExpireDate(registerPersonalAccountDto.getPersonalIdCardExpireDate());
        }

        // 设置行业信息
        if (registerPersonalAccountDto.getUnitedFirstIndustryId() != null) {
            builder.setUnitedFirstIndustryId(registerPersonalAccountDto.getUnitedFirstIndustryId());
        }

        if (registerPersonalAccountDto.getUnitedSecondIndustryId() != null) {
            builder.setUnitedSecondIndustryId(registerPersonalAccountDto.getUnitedSecondIndustryId());
        }

        if (registerPersonalAccountDto.getUnitedThirdIndustryId() != null) {
            builder.setUnitedThirdIndustryId(registerPersonalAccountDto.getUnitedThirdIndustryId());
        }

        // 设置部门和区域信息
        if (registerPersonalAccountDto.getDepartmentId() != null) {
            builder.setDepartmentId(registerPersonalAccountDto.getDepartmentId());
        }

        if (registerPersonalAccountDto.getAreaId() != null) {
            builder.setAreaId(registerPersonalAccountDto.getAreaId());
        }
        if (CollectionUtils.isNotEmpty(registerPersonalAccountDto.getQualifications())) {
            List<CustomerAdditionalQualificationInfo> customerAdditionalQualificationInfoList = registerPersonalAccountDto.getQualifications()
                    .stream()
                    .map(qualification -> CustomerAdditionalQualificationInfo.newBuilder()
                            .setUrl(qualification.getUrl())
                            .setSpecialInfoTypeId(qualification.getSpecialInfoTypeId())
                            .setExpireDate(qualification.getExpireDate())
                            .build())
                    .collect(Collectors.toList());
            builder.addAllQualifications(customerAdditionalQualificationInfoList);
        }
        if (Objects.nonNull(registerPersonalAccountDto.getUserName())) {
            builder.setUserName(registerPersonalAccountDto.getUserName());
        }
        if (CollectionUtils.isNotEmpty(registerPersonalAccountDto.getPersonalIdCardPics())) {
            List<CustomerAttachInfo> personalIdCardPicList = registerPersonalAccountDto.getPersonalIdCardPics().stream()
                    .map(customerAttachInfoDto -> CustomerAttachInfo.newBuilder()
                            .setUrl(customerAttachInfoDto.getUrl())
                            .build())
                    .collect(Collectors.toList());
            builder.addAllPersonIdCardPics(personalIdCardPicList);
        }
        if (Objects.nonNull(registerPersonalAccountDto.getDomain())) {
            builder.addAllDomains(Collections.singletonList(registerPersonalAccountDto.getDomain()));
        }
        if (Objects.nonNull(registerPersonalAccountDto.getCustomerId()) && registerPersonalAccountDto.getCustomerId() > 0) {
            builder.setId(registerPersonalAccountDto.getCustomerId());
        }
        builder.setOperator(getDefaultOperator());

        return builder.build();
    }

    public static UpdateCustomerReq convertToUpdateCustomerReq(RegisterEnterpriseAccountDto registerEnterpriseAccountDto) {
        if (registerEnterpriseAccountDto == null) {
            return null;
        }

        UpdateCustomerReq.Builder builder = UpdateCustomerReq.newBuilder();

        // 设置基本信息
        if (registerEnterpriseAccountDto.getIsAgent() != null) {
            builder.setIsAgent(integerToYesOrNo(registerEnterpriseAccountDto.getIsAgent()));
        }

        if (registerEnterpriseAccountDto.getIsInner() != null) {
            builder.setIsInner(integerToYesOrNo(registerEnterpriseAccountDto.getIsInner()));
        }

        if (registerEnterpriseAccountDto.getCustomerCategory() != null) {
            builder.setCustomerCategory(integerToCustomerCategory(registerEnterpriseAccountDto.getCustomerCategory()));
        }

        if (registerEnterpriseAccountDto.getIsBusinessLicenceIndefinite() != null) {
            builder.setIsBusinessLicenceIndefinite(integerToYesOrNo(registerEnterpriseAccountDto.getIsBusinessLicenceIndefinite()));
        }

        // 设置企业信息
        if (registerEnterpriseAccountDto.getBusinessLicenseCode() != null) {
            builder.setBusinessLicenceCode(registerEnterpriseAccountDto.getBusinessLicenseCode());
        }
        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getBusinessLicensePics())) {
            List<CustomerAttachInfo> businessLicensePics = registerEnterpriseAccountDto.getBusinessLicensePics().stream().map(pic -> CustomerAttachInfo.newBuilder().setUrl(pic).build()).collect(Collectors.toList());
            builder.addAllBusinessLicencePics(businessLicensePics);

        }
        if (Objects.nonNull(registerEnterpriseAccountDto.getBusinessLicenseExpireDate())) {
            builder.setBusinessLicenceExpireDate(registerEnterpriseAccountDto.getBusinessLicenseExpireDate());
        }

        if (registerEnterpriseAccountDto.getUserName() != null) {
            builder.setUserName(registerEnterpriseAccountDto.getUserName());
        }

        if (registerEnterpriseAccountDto.getLinkmanPhone() != null) {
            builder.setLinkmanPhone(registerEnterpriseAccountDto.getLinkmanPhone());
        }

        if (registerEnterpriseAccountDto.getLinkmanEmail() != null) {
            builder.setLinkmanEmail(registerEnterpriseAccountDto.getLinkmanEmail());
        }

        if (registerEnterpriseAccountDto.getLinkmanAddress() != null) {
            builder.setLinkmanAddress(registerEnterpriseAccountDto.getLinkmanAddress());
        }

        if (registerEnterpriseAccountDto.getQualificationTypeId() != null) {
            builder.setQualificationTypeId(registerEnterpriseAccountDto.getQualificationTypeId());
        }

        if (registerEnterpriseAccountDto.getIcpRecordNumber() != null) {
            builder.setIcpRecordNumber(registerEnterpriseAccountDto.getIcpRecordNumber());
        } else {
            builder.setIcpRecordNumber(registerEnterpriseAccountDto.getBusinessLicenseCode());
        }

        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getIcpRecordPics())) {
            List<CustomerAttachInfo> icpPicList = registerEnterpriseAccountDto.getIcpRecordPics()
                    .stream()
                    .map(pic -> CustomerAttachInfo.newBuilder()
                            .setUrl(pic)
                            .build())
                    .collect(Collectors.toList());
            builder.addAllIcpPics(icpPicList);
        } else {
            builder.addAllIcpPics(Collections.singletonList(CustomerAttachInfo.newBuilder().setUrl("https://i0.hdslb.com/bfs/kfptfe/floor/58fa0b09468231834ca3baed3862bf83d2b1bc41.png").build()));
        }

        // 设置行业信息
        if (registerEnterpriseAccountDto.getUnitedFirstIndustryId() != null) {
            builder.setUnitedFirstIndustryId(registerEnterpriseAccountDto.getUnitedFirstIndustryId());
        }

        if (registerEnterpriseAccountDto.getUnitedSecondIndustryId() != null) {
            builder.setUnitedSecondIndustryId(registerEnterpriseAccountDto.getUnitedSecondIndustryId());
        }

        if (registerEnterpriseAccountDto.getUnitedThirdIndustryId() != null) {
            builder.setUnitedThirdIndustryId(registerEnterpriseAccountDto.getUnitedThirdIndustryId());
        }

        // 设置部门和区域信息
        if (registerEnterpriseAccountDto.getDepartmentId() != null) {
            builder.setDepartmentId(registerEnterpriseAccountDto.getDepartmentId());
        }

        if (registerEnterpriseAccountDto.getAreaId() != null) {
            builder.setAreaId(registerEnterpriseAccountDto.getAreaId());
        }
        if (Objects.nonNull(registerEnterpriseAccountDto.getGroupId())) {
            builder.setGroupId(registerEnterpriseAccountDto.getGroupId());
        }
        if (Objects.nonNull(registerEnterpriseAccountDto.getDomain())) {
            builder.addAllDomains(Collections.singletonList(registerEnterpriseAccountDto.getDomain()));
        } else {
            builder.addAllDomains(Collections.singletonList("https://www.bilibili.com"));
        }

        List<CustomerAdditionalQualificationInfo> customerAdditionalQualificationInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getQualifications())) {
            List<CustomerAdditionalQualificationInfo> originalCustomerAdditionalQualificationInfoList = registerEnterpriseAccountDto.getQualifications()
                    .stream()
                    .map(qualification -> CustomerAdditionalQualificationInfo.newBuilder()
                            .setUrl(qualification.getUrl())
                            .setSpecialInfoTypeId(qualification.getSpecialInfoTypeId())
                            .setExpireDate(qualification.getExpireDate())
                            .build())
                    .collect(Collectors.toList());
            customerAdditionalQualificationInfoList.addAll(originalCustomerAdditionalQualificationInfoList);

        }

        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getBusinessAuthPics())) {
            List<CustomerAdditionalQualificationInfo> businessAuthPics = registerEnterpriseAccountDto.getBusinessAuthPics()
                    .stream()
                    .map(qualification -> CustomerAdditionalQualificationInfo.newBuilder()
                            .setUrl(qualification.getUrl())
                            .setSpecialInfoTypeId(qualification.getSpecialInfoTypeId())
                            .setExpireDate(qualification.getExpireDate())
                            .build())
                    .collect(Collectors.toList());
            customerAdditionalQualificationInfoList.addAll(businessAuthPics);
        }
        if (!customerAdditionalQualificationInfoList.isEmpty()) {
            builder.addAllQualifications(customerAdditionalQualificationInfoList);
        }
        builder.setOperator(getDefaultOperator());

        if (Objects.nonNull(registerEnterpriseAccountDto.getPromotionType())) {
            builder.setPromotionTypeValue(registerEnterpriseAccountDto.getPromotionType());
        }
        builder.setInternalLinkman(registerEnterpriseAccountDto.getLinkmanName());
        if (Objects.nonNull(registerEnterpriseAccountDto.getStoreAddress())) {
            builder.setStoreAddress(registerEnterpriseAccountDto.getStoreAddress());
        }
        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getStoreEntityPics())) {
            List<CustomerAttachInfo> storeEntityPics = registerEnterpriseAccountDto.getStoreEntityPics()
                    .stream()
                    .map(pic -> CustomerAttachInfo.newBuilder()
                            .setUrl(pic)
                            .build())
                    .collect(Collectors.toList());
            builder.addAllStoreEntityPics(storeEntityPics);
        }
        if (CollectionUtils.isNotEmpty(registerEnterpriseAccountDto.getStoreItemPics())) {
            List<CustomerAttachInfo> storeItemPics = registerEnterpriseAccountDto.getStoreItemPics()
                    .stream()
                    .map(pic -> CustomerAttachInfo.newBuilder()
                            .setUrl(pic)
                            .build())
                    .collect(Collectors.toList());
            builder.addAllStoreItemPics(storeItemPics);
        }

        if (Objects.nonNull(registerEnterpriseAccountDto.getCustomerId()) && registerEnterpriseAccountDto.getCustomerId() > 0) {
            builder.setId(registerEnterpriseAccountDto.getCustomerId());
        }

        return builder.build();
    }


    public static CreateAccountReq convertToCreateAccountReq(CreateAccountAndAuditDto createAccountAndAuditDto) {
        if (createAccountAndAuditDto == null) {
            return null;
        }

        CreateAccountReq.Builder builder = CreateAccountReq.newBuilder();

        // 设置客户ID和账户信息
        builder.setCustomerId(createAccountAndAuditDto.getCustomerId());

        if (createAccountAndAuditDto.getUserName() != null) {
            builder.setUserName(createAccountAndAuditDto.getUserName());
        }

        builder.setAccountType(AccountType.forNumber(createAccountAndAuditDto.getAccountType()));

        // 设置行业信息
        builder.setUnitedFirstIndustryId(createAccountAndAuditDto.getUnitedFirstIndustryId());
        builder.setUnitedSecondIndustryId(createAccountAndAuditDto.getUnitedSecondIndustryId());
        builder.setUnitedThirdIndustryId(createAccountAndAuditDto.getUnitedThirdIndustryId());

        // 设置其他必要的字段
        builder.setDependencyAgentId(createAccountAndAuditDto.getDependencyAgentId());
        builder.setAuthExpireDate(createAccountAndAuditDto.getAuthExpireDate());
        if (createAccountAndAuditDto.getAuthImages() != null) {
            builder.addAllAuthImages(createAccountAndAuditDto.getAuthImages());
        }
        builder.setMid(createAccountAndAuditDto.getMid());
        builder.setBlueMid(createAccountAndAuditDto.getBlueMid());
        builder.setProductLineId(createAccountAndAuditDto.getProductLineId());
        builder.setProductId(createAccountAndAuditDto.getProductId());
        builder.setDepartmentId(createAccountAndAuditDto.getDepartmentId());
        builder.setIsSupportFly(createAccountAndAuditDto.getIsSupportFly());
        builder.setIsSupportPickup(createAccountAndAuditDto.getIsSupportPickup());
        builder.setAdStatus(createAccountAndAuditDto.getAdStatus());
        builder.setGdStatus(createAccountAndAuditDto.getGdStatus());
        builder.setIsSupportContent(createAccountAndAuditDto.getIsSupportContent());
        builder.setIsSupportGame(createAccountAndAuditDto.getIsSupportGame());
        builder.setIsSupportSeller(createAccountAndAuditDto.getIsSupportSeller());
        builder.setIsSupportDpa(createAccountAndAuditDto.getIsSupportDpa());
        builder.setIsSupportMas(createAccountAndAuditDto.getIsSupportMas());
        builder.setIsSupportCluePass(createAccountAndAuditDto.getIsSupportCluePass());

        if (createAccountAndAuditDto.getSaleIds() != null) {
            builder.addAllSaleIds(createAccountAndAuditDto.getSaleIds());
        }

        if (createAccountAndAuditDto.getRemark() != null) {
            builder.setRemark(createAccountAndAuditDto.getRemark());
        }

        builder.setIsAgent(integerToYesOrNo(createAccountAndAuditDto.getIsAgent()));
        builder.setAgentType(createAccountAndAuditDto.getAgentType());
        builder.setIsInner(integerToYesOrNo(createAccountAndAuditDto.getIsInner()));
        builder.setUserType(UserType.forNumber(createAccountAndAuditDto.getUserType()));
        builder.setGroupId(createAccountAndAuditDto.getGroupId());
        builder.setPickupMid(createAccountAndAuditDto.getPickupMid());

        if (createAccountAndAuditDto.getBrandDomain() != null) {
            builder.setBrandDomain(createAccountAndAuditDto.getBrandDomain());
        }

        builder.setFinanceType(FinanceType.forNumber(createAccountAndAuditDto.getFinanceType()));

        if (createAccountAndAuditDto.getOperator() != null) {
            builder.setOperator(createAccountAndAuditDto.getOperator());
        }

        builder.setOperator(getDefaultOperator());
        return builder.build();
    }

    public static CustomerInfoDetailDto convertToCustomerInfoDetailDtoWithSetter(CustomerInfoDetail customerInfoDetail) {
        if (customerInfoDetail == null) {
            return null;
        }

        CustomerInfoDetailDto dto = new CustomerInfoDetailDto();

        // 基本信息
        dto.setId(customerInfoDetail.getId());
        dto.setIsInner(customerInfoDetail.getIsInner());
        dto.setIsInnerDesc(customerInfoDetail.getIsInnerDesc());
        dto.setCustomerCategory(customerInfoDetail.getCustomerCategory());
        dto.setCustomerCategoryDesc(customerInfoDetail.getCustomerCategoryDesc());
        dto.setNickName(customerInfoDetail.getNickName());
        dto.setUserName(customerInfoDetail.getUserName());

        // 集团和部门信息
        dto.setGroupId(customerInfoDetail.getGroupId());
        dto.setCompanyGroupName(customerInfoDetail.getCompanyGroupName());
        dto.setParentGroupId(customerInfoDetail.getParentGroupId());
        dto.setParentGroupName(customerInfoDetail.getParentGroupName());
        dto.setDepartmentId(customerInfoDetail.getDepartmentId());
        dto.setDepartmentName(customerInfoDetail.getDepartmentName());
        if (customerInfoDetail.getDepartmentIdsList() != null) {
            dto.setDepartmentIds(customerInfoDetail.getDepartmentIdsList());
        }
        dto.setDepartmentNames(customerInfoDetail.getDepartmentNames());

        // 蓝v信息


        // 客户状态
        dto.setCustomerStatus(customerInfoDetail.getCustomerStatus());
        dto.setCustomerStatusName(customerInfoDetail.getCustomerStatusName());

        // 域名信息
        if (customerInfoDetail.getDomainsList() != null) {
            dto.setDomains(customerInfoDetail.getDomainsList());
        }

        // 区域信息
        dto.setAreaId(customerInfoDetail.getAreaId());
        dto.setAreaIdDesc(customerInfoDetail.getAreaIdDesc());

        // 网站和社交信息
        dto.setWebsiteName(customerInfoDetail.getWebsiteName());
        dto.setWeibo(customerInfoDetail.getWeibo());

        // 联系人信息
        dto.setLinkmanName(customerInfoDetail.getInternalLinkman());
        dto.setLinkmanPhone(customerInfoDetail.getLinkmanPhone());
        dto.setLinkmanEmail(customerInfoDetail.getLinkmanEmail());
        dto.setLinkmanAddress(customerInfoDetail.getLinkmanAddress());
        dto.setBank(customerInfoDetail.getBank());

        // 代理商信息
        dto.setIsAgent(customerInfoDetail.getIsAgent());
        dto.setIsAgentDesc(customerInfoDetail.getIsAgentDesc());
        dto.setAgentType(customerInfoDetail.getAgentType());
        dto.setAgentTypeDesc(customerInfoDetail.getAgentTypeDesc());

        // 行业分类信息
        dto.setCategoryFirstId(customerInfoDetail.getCategoryFirstId());
        dto.setCategoryFirstName(customerInfoDetail.getCategoryFirstName());
        dto.setCategorySecondId(customerInfoDetail.getCategorySecondId());
        dto.setCategorySecondName(customerInfoDetail.getCategorySecondName());
        dto.setBizCategoryFirstId(customerInfoDetail.getBizCategoryFirstId());
        dto.setBizCategoryFirstName(customerInfoDetail.getBizCategoryFirstName());
        dto.setBizCategorySecondId(customerInfoDetail.getBizCategorySecondId());
        dto.setBizCategorySecondName(customerInfoDetail.getBizCategorySecondName());

        // 个人信息
        dto.setPersonalPhone(customerInfoDetail.getPersonalPhone());
        dto.setPersonalAddress(customerInfoDetail.getPersonalAddress());
        dto.setRemark(customerInfoDetail.getRemark());

        // 账号行业信息


        // 资质信息
        dto.setQualificationTypeId(customerInfoDetail.getQualificationTypeId());
        dto.setQualificationTypeIdDesc(customerInfoDetail.getQualificationTypeIdDesc());

        // 营业执照信息
        dto.setBusinessLicenceCode(customerInfoDetail.getBusinessLicenceCode());
        if (customerInfoDetail.getBusinessLicencePicsList() != null) {
            List<CustomerAttachInfoDto> businessLicencePics = customerInfoDetail.getBusinessLicencePicsList().stream()
                    .map(pic -> {
                        CustomerAttachInfoDto picDto = new CustomerAttachInfoDto();
                        picDto.setId(pic.getId());
                        picDto.setCustomerId(pic.getCustomerId());
                        picDto.setUrl(pic.getUrl());
                        picDto.setHash(pic.getHash());
                        picDto.setToken(pic.getToken());
                        picDto.setIsFrontSide(pic.getIsFrontSide());
                        return picDto;
                    })
                    .collect(Collectors.toList());
            dto.setBusinessLicencePics(businessLicencePics);
        }
        dto.setIsBusinessLicenceIndefinite(customerInfoDetail.getIsBusinessLicenceIndefinite());
        dto.setIsBusinessLicenceIndefiniteDesc(customerInfoDetail.getIsBusinessLicenceIndefiniteDesc());
        dto.setBusinessLicenceExpireDate(customerInfoDetail.getBusinessLicenceExpireDate());

        // 法人信息
        dto.setLegalPersonName(customerInfoDetail.getLegalPersonName());
        if (customerInfoDetail.getLegalPersonIdCardPicsList() != null) {
            List<CustomerAttachInfoDto> legalPersonIdCardPics = customerInfoDetail.getLegalPersonIdCardPicsList().stream()
                    .map(pic -> {
                        CustomerAttachInfoDto picDto = new CustomerAttachInfoDto();
                        picDto.setId(pic.getId());
                        picDto.setCustomerId(pic.getCustomerId());
                        picDto.setUrl(pic.getUrl());
                        picDto.setHash(pic.getHash());
                        picDto.setToken(pic.getToken());
                        picDto.setIsFrontSide(pic.getIsFrontSide());
                        return picDto;
                    })
                    .collect(Collectors.toList());
            dto.setLegalPersonIdCardPics(legalPersonIdCardPics);
        }
        dto.setIsLegalPersonIdCardIndefinite(customerInfoDetail.getIsLegalPersonIdCardIndefinite());
        dto.setIsLegalPersonIdCardIndefiniteDesc(customerInfoDetail.getIsLegalPersonIdCardIndefiniteDesc());
        dto.setLegalPersonIdCardExpireDate(customerInfoDetail.getLegalPersonIdCardExpireDate());

        // ICP信息
        dto.setIcpRecordNumber(customerInfoDetail.getIcpRecordNumber());
        if (customerInfoDetail.getIcpPicsList() != null) {
            List<CustomerAttachInfoDto> icpPics = customerInfoDetail.getIcpPicsList().stream()
                    .map(pic -> {
                        CustomerAttachInfoDto picDto = new CustomerAttachInfoDto();
                        picDto.setId(pic.getId());
                        picDto.setCustomerId(pic.getCustomerId());
                        picDto.setUrl(pic.getUrl());
                        picDto.setHash(pic.getHash());
                        picDto.setToken(pic.getToken());
                        picDto.setIsFrontSide(pic.getIsFrontSide());
                        return picDto;
                    })
                    .collect(Collectors.toList());
            dto.setIcpPics(icpPics);
        }

        // 个人证件信息
        dto.setPersonalIdCardType(customerInfoDetail.getPersonalIdCardType());
        dto.setPersonalIdCardTypeDesc(customerInfoDetail.getPersonalIdCardTypeDesc());
        dto.setPersonalIdCardNumber(customerInfoDetail.getPersonalIdCardNumber());
        dto.setIsPersonalIdCardIndefinite(customerInfoDetail.getIsPersonalIdCardIndefinite());
        dto.setIsPersonalIdCardIndefiniteDesc(customerInfoDetail.getIsPersonalIdCardIndefiniteDesc());
        dto.setPersonalIdCardExpireDate(customerInfoDetail.getPersonalIdCardExpireDate());
        if (customerInfoDetail.getPersonIdCardPicsList() != null) {
            List<CustomerAttachInfoDto> personIdCardPics = customerInfoDetail.getPersonIdCardPicsList().stream()
                    .map(pic -> {
                        CustomerAttachInfoDto picDto = new CustomerAttachInfoDto();
                        picDto.setId(pic.getId());
                        picDto.setCustomerId(pic.getCustomerId());
                        picDto.setUrl(pic.getUrl());
                        picDto.setHash(pic.getHash());
                        picDto.setToken(pic.getToken());
                        picDto.setIsFrontSide(pic.getIsFrontSide());
                        return picDto;
                    })
                    .collect(Collectors.toList());
            dto.setPersonIdCardPics(personIdCardPics);
        }

        // 额外资质信息
        if (CollectionUtils.isNotEmpty(customerInfoDetail.getQualificationsList())) {
            List<CustomerAdditionalQualificationInfoDto> qualificationList = new ArrayList<>();
            List<CustomerAdditionalQualificationInfoDto> businessAuthList = new ArrayList<>();
            for (CustomerAdditionalQualificationInfo qualification : customerInfoDetail.getQualificationsList()) {

                CustomerAdditionalQualificationInfoDto qualificationDto = new CustomerAdditionalQualificationInfoDto();
                qualificationDto.setId(qualification.getId());
                qualificationDto.setCustomerId(qualification.getCustomerId());
                qualificationDto.setUrl(qualification.getUrl());
                qualificationDto.setHash(qualification.getHash());
                qualificationDto.setToken(qualification.getToken());
                qualificationDto.setSpecialInfoTypeId(qualification.getSpecialInfoTypeId());
                qualificationDto.setSpecialInfoTypeName(qualification.getSpecialInfoTypeName());
                qualificationDto.setExpireDate(qualification.getExpireDate());

                if (Objects.equals(qualification.getSpecialInfoTypeId(), 155)) {
                    businessAuthList.add(qualificationDto);
                } else {
                    qualificationList.add(qualificationDto);
                }
            }
            dto.setQualifications(qualificationList);
            dto.setBusinessAuth(businessAuthList);
        }

        // 开票信息


        // 创建时间
        dto.setCtime(customerInfoDetail.getCtime());

        // 线下店铺信息
        if (customerInfoDetail.getStoreItemPicsList() != null) {
            List<CustomerAttachInfoDto> storeItemPics = customerInfoDetail.getStoreItemPicsList().stream()
                    .map(pic -> {
                        CustomerAttachInfoDto picDto = new CustomerAttachInfoDto();
                        picDto.setId(pic.getId());
                        picDto.setCustomerId(pic.getCustomerId());
                        picDto.setUrl(pic.getUrl());
                        picDto.setHash(pic.getHash());
                        picDto.setToken(pic.getToken());
                        picDto.setIsFrontSide(pic.getIsFrontSide());
                        return picDto;
                    })
                    .collect(Collectors.toList());
            dto.setStoreItemPics(storeItemPics);
        }

        if (customerInfoDetail.getStoreEntityPicsList() != null) {
            List<CustomerAttachInfoDto> storeEntityPics = customerInfoDetail.getStoreEntityPicsList().stream()
                    .map(pic -> {
                        CustomerAttachInfoDto picDto = new CustomerAttachInfoDto();
                        picDto.setId(pic.getId());
                        picDto.setCustomerId(pic.getCustomerId());
                        picDto.setUrl(pic.getUrl());
                        picDto.setHash(pic.getHash());
                        picDto.setToken(pic.getToken());
                        picDto.setIsFrontSide(pic.getIsFrontSide());
                        return picDto;
                    })
                    .collect(Collectors.toList());
            dto.setStoreEntityPics(storeEntityPics);
        }

        dto.setStoreAddress(customerInfoDetail.getStoreAddress());

        // 统一行业分类
        dto.setUnitedFirstIndustryId(customerInfoDetail.getUnitedFirstIndustryId());
        dto.setUnitedSecondIndustryId(customerInfoDetail.getUnitedSecondIndustryId());
        dto.setUnitedThirdIndustryId(customerInfoDetail.getUnitedThirdIndustryId());
        dto.setUnitedFirstIndustryName(customerInfoDetail.getUnitedFirstIndustryName());
        dto.setUnitedSecondIndustryName(customerInfoDetail.getUnitedSecondIndustryName());
        dto.setUnitedThirdIndustryName(customerInfoDetail.getUnitedThirdIndustryName());

        // 客户运营标签
        dto.setCustomerOperateLabelType(customerInfoDetail.getCustomerOperateLabelType());
        dto.setPromotionType(customerInfoDetail.getPromotionType());

        return dto;
    }

    static YesOrNo integerToYesOrNo(Integer value) {
        if (value == null) {
            return YesOrNo.NO;
        }
        return value == 1 ? YesOrNo.YES : YesOrNo.NO;
    }

    /**
     * 将Integer类型转换为CustomerCategory枚举
     *
     * @param value Integer值 (0表示个人，1表示机构)
     * @return CustomerCategory枚举值
     */
    static CustomerCategory integerToCustomerCategory(Integer value) {
        if (value == null) {
            return CustomerCategory.PERSONAL_CUSTOMER; // 默认为个人
        }
        switch (value) {
            case 0:
                return CustomerCategory.PERSONAL_CUSTOMER;
            case 1:
                return CustomerCategory.ORG_CUSTOMER;
            default:
                return CustomerCategory.PERSONAL_CUSTOMER;
        }
    }

    /**
     * 将Integer类型转换为PersonalIdCardType枚举
     *
     * @param value Integer值 (0表示身份证，1表示护照)
     * @return PersonalIdCardType枚举值
     */
    static PersonalIdCardType integerToPersonalIdCardType(Integer value) {
        if (value == null) {
            return PersonalIdCardType.ID_CARD;
        }
        switch (value) {
            case 1:
                return PersonalIdCardType.ID_CARD;
            default:
                return PersonalIdCardType.ID_CARD;
        }
    }

    private static GrpcOperator getDefaultOperator() {
        return GrpcOperator.newBuilder()
                .setOperatorId(0)
                .setOperatorName("经营号")
                .setOperatorType(OperatorType.SYSTEM)
                .setSystemType(SystemType.BUSINESS)
                .build();
    }
}
