package com.bilibili.mall.business.account.domain.databus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/2 5:17 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ToolRemoveMsg {

    /**
     * 1.删除员工 传uid+unBindStaff
     * 2.解除授权 传uid+unBindStaff+typeCodes
     * 3.删除转化组件 传toolId
     *
     * */

    /** 需要解绑的用户 */
    private Long uid;
    /**
     * 是否是解绑用户
     */
    private Boolean unBindStaff;
    /**
     * 需要删除的转化组件ID
     */
    private String toolId;
    /**
     * 需要删除的转化组件类型code
     * RESERVE:在线预约、CONTACT_QW:联系企微、MINI_APP:微信小程序
     */
    private List<String> typeCodes;
}
