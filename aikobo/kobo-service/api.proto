syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

package aikobo.kobo.service.v1;

option (wdcli.appid) = "community.aikobo.kobo-service";
option go_package = "buf.bilibili.co/bapis/bapis-gen/aikobo/kobo.service;v1";
option java_package = "com.bapis.aikobo.kobo.service";
option java_multiple_files = true;

service Kobo {
  rpc AegisPushImage(AegisPushImageReq) returns (AegisPushImageResp);
}

message AegisPushImageReq {
  string oid = 1 [(gogoproto.moretags) = 'form:"oid" validate:"required"', (gogoproto.jsontag) = 'oid', json_name = "oid"];
  int64 uid = 2 [(gogoproto.moretags) = 'form:"uid" validate:"required"', (gogoproto.jsontag) = 'uid', json_name = "uid"];
  string pic = 3 [(gogoproto.moretags) = 'form:"pic" validate:"required,url"', (gogoproto.jsontag) = 'pic', json_name = "pic"];
  int64 ctime = 4 [(gogoproto.moretags) = 'form:"ctime" validate:"required"', (gogoproto.jsontag) = 'ctime', json_name = "ctime"];
  string from = 5 [(gogoproto.moretags) = 'form:"from"', (gogoproto.jsontag) = 'from', json_name = "from"];
  string description = 6 [(gogoproto.moretags) = 'form:"description"', (gogoproto.jsontag) = 'description', json_name = "description"];
}

message AegisPushImageResp {}