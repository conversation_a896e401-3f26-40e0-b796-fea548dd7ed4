package com.bilibili.sycp.personal.mgk.portal.controller.open_api.login;

import com.alibaba.fastjson.JSONObject;
import com.bapis.ad.crm.account.AccountReadServiceGrpc;
import com.bilibili.sycp.personal.mgk.biz.api.account.dto.BizAccountStautsDto;
import com.bilibili.sycp.personal.mgk.biz.common.enums.BusinessAccountStatusEnum;
import com.bilibili.sycp.personal.mgk.biz.common.exception.ServiceException;
import com.bilibili.sycp.personal.mgk.biz.config.MgkPaladinConfig;
import com.bilibili.sycp.personal.mgk.biz.impl.account.AccountService;
import com.bilibili.sycp.personal.mgk.portal.controller.common.BaseController;
import com.bilibili.sycp.personal.mgk.portal.controller.common.Context;
import com.bilibili.sycp.personal.mgk.portal.controller.common.Response;
import com.bilibili.sycp.personal.mgk.portal.controller.exception.BusinessExceptionCode;
import com.bilibili.sycp.personal.mgk.portal.controller.open_api.login.vo.BusinessAccountStatusVo;
import com.bilibili.sycp.personal.mgk.portal.controller.open_api.login.vo.LoginInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.web.bind.annotation.*;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

@Slf4j
@RestController
@Tag(name = "LoginController-登录相关")
@RequestMapping("/open_api/v1/login")
@RequiredArgsConstructor
public class LoginController extends BaseController {

    private final AccountService accountService;

    private final MgkPaladinConfig paladinConfig;
    @RPCClient("sycpb.cpm.crm-portal")
    private AccountReadServiceGrpc.AccountReadServiceBlockingStub accountReadServiceBlockingStub;

    /**
     * 检测mid
     *
     * @return 登录信息
     */
    @ResponseBody
    @RequestMapping(value = "/account/check", method = RequestMethod.GET)
    public Response<LoginInfoVo> isCorpMid(@Parameter(hidden = true) Context context) throws ServiceException {
        Long mid = context.getMid();
        var resp = accountService.queryCorpInfo(mid);
        if (resp == null || resp.getData() == null) {
            throw new ServiceException(BusinessExceptionCode.SYSTEM_BUSY);
        }
        accountService.checkAndCreate(mid);
        return Response.SUCCESS(LoginInfoVo.builder().mid(mid)
                .user_name(resp.getData().getName())
                .face_url(resp.getData().getFace()).build());
    }

    @Operation(summary = "登出营销系统")
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Boolean> logout(@Parameter(hidden = true) Context context,
                             @Parameter(hidden = true) HttpServletRequest request,
                             @Parameter(hidden = true) HttpServletResponse response) throws ServiceException {
        log.info("logout context: [{}], cookie:[{}]", JSONObject.toJSONString(context),
                JSONObject.toJSONString(request.getCookies()));

        this.removeAccessTokenCookie(request, response);

        return Response.SUCCESS(true);
    }

    private void removeAccessTokenCookie(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        log.info("removeMasterStationCookie");
        try {
            var cookies = request.getCookies();
            for (Cookie cookie : cookies) {
                cookie.setPath("/");
                cookie.setMaxAge(0);
                cookie.setValue(null);
                cookie.setDomain(".bilibili.com");
                response.addCookie(cookie);
            }
            response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
        } catch (Exception e) {
//            throw new ServiceException(BusinessExceptionCode.SYSTEM_BUSY);
        }
    }


    /**
     * @return 内网登录信息
     */
    @ResponseBody
    @RequestMapping(value = "/inner/login", method = RequestMethod.POST)
    public Response<LoginInfoVo> innerLogin(@Parameter(hidden = true) Context context,
                                            @RequestBody LoginInfoVo loginInfoVo) throws ServiceException {
        Long mid = context.getMid();
        var resp = accountService.queryCorpInfo(mid);
        if (resp == null || resp.getData() == null) {
            throw new ServiceException(BusinessExceptionCode.SYSTEM_BUSY);
        }
        //sso 校验

        //种cookie

        return Response.SUCCESS(LoginInfoVo.builder().mid(mid)
                .user_name(resp.getData().getName())
                .face_url(resp.getData().getFace()).build());
    }

    @Operation(summary = "检查经营号状态")
    @RequestMapping(value = "/business/account/status", method = RequestMethod.GET)
    public Response<BusinessAccountStatusVo> checkBusinessAccountStatus(@Parameter(hidden = true) Context context) {
        Long mid = context.getMid();

        BusinessAccountStatusVo businessAccountStatusVo = getBusinessAccountStatusVo(mid);


        return Response.SUCCESS(businessAccountStatusVo);

    }

    private BusinessAccountStatusVo getBusinessAccountStatusVo(Long mid) {
        BizAccountStautsDto bizAccountStautsDto = accountService.checkBusinessAccount(mid);
        Integer businessAccountId = bizAccountStautsDto.getAccountId();
        Boolean bizHasAssistant = bizAccountStautsDto.getHasAssistant();

        BusinessAccountStatusVo businessAccountStatusVo = new BusinessAccountStatusVo();

//        if(Objects.isNull(accountId)){
//            businessAccountStatusVo.setStatus(BusinessAccountStatusEnum.HAS_ASSISTANT_NO_BIZ_ACCOUNT.getCode());
//        }

        Integer assistantAccount = accountService.checkPersonalFlyAccess(mid);
        Boolean assistantHasSubmit = accountService.hasSubmitForm(mid);
        Boolean hasAssistant = Objects.nonNull(assistantAccount) && assistantHasSubmit;

        if (Objects.nonNull(businessAccountId) && BooleanUtils.isTrue(hasAssistant) && !Objects.equals(businessAccountId, assistantAccount) && BooleanUtils.isTrue(bizHasAssistant)) {
            businessAccountStatusVo.setStatus(BusinessAccountStatusEnum.HAS_OLD_ASSISTANT_HAS_BIZ_ACCOUNT.getCode());
        } else if (Objects.isNull(businessAccountId) && BooleanUtils.isNotTrue(hasAssistant)) {
            businessAccountStatusVo.setStatus(BusinessAccountStatusEnum.NO_ASSISTANT_NO_BIZ_ACCOUNT.getCode());
        } else if (Objects.isNull(businessAccountId) && BooleanUtils.isTrue(hasAssistant)) {
            businessAccountStatusVo.setStatus(BusinessAccountStatusEnum.HAS_ASSISTANT_NO_BIZ_ACCOUNT.getCode());
        } else if (Objects.nonNull(businessAccountId) && BooleanUtils.isNotTrue(bizHasAssistant)) {
            businessAccountStatusVo.setStatus(BusinessAccountStatusEnum.ASSISTANT_INDUSTRY_ERROR.getCode());
        } else if (Objects.equals(businessAccountId, assistantAccount) && hasAssistant) {
            businessAccountStatusVo.setStatus(BusinessAccountStatusEnum.HAS_NEW_ASSISTANT_HAS_BIZ_ACCOUNT.getCode());
        }
        return businessAccountStatusVo;
    }

}
