package com.bilibili.adp.cpc.enums.ad_product;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 投放产品行业级别 1,2,3
 */
@Getter
@AllArgsConstructor
public enum AdProductCategoryLevelEnum {

    ONE(1, "一级"),
    TWO(2, "二级"),
    THREE(3, "三级"),
    ;

    public static AdProductCategoryLevelEnum getByCode(Integer code) {
        for (AdProductCategoryLevelEnum bean : values()) {
            if (code.equals(bean.getCode())) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown category level" + code);
    }

    private final Integer code;
    private final String name;

}
