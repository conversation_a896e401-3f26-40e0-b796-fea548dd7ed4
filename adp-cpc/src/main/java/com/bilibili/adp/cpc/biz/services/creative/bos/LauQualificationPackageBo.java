package com.bilibili.adp.cpc.biz.services.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LauQualificationPackageBo {

    private Integer accountId;

    private Integer id;

    private Integer isDeleted;

    private String name;

    private java.sql.Timestamp ctime;

    /**
     * 共享范围：1-同代理同集团同产品，2-仅本账户
     */
    private Integer shareScope;

    /**
     * 资质列表
     */
    private List<QualificationSimpleBo> qualificationList;

}

