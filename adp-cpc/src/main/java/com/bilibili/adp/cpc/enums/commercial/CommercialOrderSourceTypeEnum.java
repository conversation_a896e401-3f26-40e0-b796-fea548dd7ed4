package com.bilibili.adp.cpc.enums.commercial;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 花火code映射
 * 由于花火bapis定义没有设定枚举所以只能直接写magic value，花火代码 @克劳德 <br>
 *
 * <a href="https://git.bilibili.co/bapis/bapis/-/blob/feature/2024-06/cloud_adauth_dyn/ad/commercialorder/adauth/api.proto">接口定义</a>
 * <AUTHOR>
 * @date 2024/7/1
 */
@Getter
@AllArgsConstructor
public enum CommercialOrderSourceTypeEnum {

    DYNAMIC(1, 1, "动态"),
    ARCHIVE(2, 2, "视频"),
    ;

    private final int code;
    private final int commercialOrderCode;
    private final String desc;

    public static CommercialOrderSourceTypeEnum getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(mode -> Objects.equals(mode.getCode(), code))
                .findAny()
                .orElseThrow(() -> new RuntimeException("花火授权类型不存在，code：" + code));
    }

    public static CommercialOrderSourceTypeEnum getByCommercialCode(Integer code) {
        return Arrays.stream(values())
                .filter(mode -> Objects.equals(mode.getCommercialOrderCode(), code))
                .findAny()
                .orElseThrow(() -> new RuntimeException("花火授权类型不存在，CommercialCode：" + code));
    }
}
