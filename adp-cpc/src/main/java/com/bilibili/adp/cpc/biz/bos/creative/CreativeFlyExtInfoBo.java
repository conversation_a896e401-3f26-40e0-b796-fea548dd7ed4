package com.bilibili.adp.cpc.biz.bos.creative;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreativeFlyExtInfoBo {
    private Integer scenesType;
    private String specificScenes;
    private Integer creativeStyle;
    private Integer banner4CoverType;
    private String storyUrl;
    private Integer preferDirectCallUp;
    private Integer isYellowCar;
    private String yellowCarTitle;
    private Integer yellowCarIcon;
    public static CreativeFlyExtInfoBo getDefaultInstance() {
        return CreativeFlyExtInfoBo
                .builder()
                .scenesType(0)
                .specificScenes("")
                .creativeStyle(0)
                .banner4CoverType(0)
                .storyUrl("")
                .preferDirectCallUp(0)
                .isYellowCar(0)
                .yellowCarTitle("")
                .yellowCarIcon(0)
                .build();
    }
}
