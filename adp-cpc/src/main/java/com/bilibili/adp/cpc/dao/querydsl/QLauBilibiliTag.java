package com.bilibili.adp.cpc.dao.querydsl;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauBilibiliTagPo;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.querydsl.sql.ColumnMetadata;

import javax.annotation.Generated;
import java.sql.Types;

import static com.querydsl.core.types.PathMetadataFactory.forVariable;




/**
 * QLauBilibiliTag is a Querydsl query type for LauBilibiliTagPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauBilibiliTag extends com.querydsl.sql.RelationalPathBase<LauBilibiliTagPo> {

    private static final long serialVersionUID = 1721637398;

    public static final QLauBilibiliTag lauBilibiliTag = new QLauBilibiliTag("lau_bilibili_tag");

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> state = createNumber("state", Integer.class);

    public final NumberPath<Long> tagId = createNumber("tagId", Long.class);

    public final StringPath tagName = createString("tagName");

    public final NumberPath<Integer> type = createNumber("type", Integer.class);

    public final com.querydsl.sql.PrimaryKey<LauBilibiliTagPo> primary = createPrimaryKey(id);

    public QLauBilibiliTag(String variable) {
        super(LauBilibiliTagPo.class, forVariable(variable), "null", "lau_bilibili_tag");
        addMetadata();
    }

    public QLauBilibiliTag(String variable, String schema, String table) {
        super(LauBilibiliTagPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauBilibiliTag(String variable, String schema) {
        super(LauBilibiliTagPo.class, forVariable(variable), schema, "lau_bilibili_tag");
        addMetadata();
    }

    public QLauBilibiliTag(Path<? extends LauBilibiliTagPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_bilibili_tag");
        addMetadata();
    }

    public QLauBilibiliTag(PathMetadata metadata) {
        super(LauBilibiliTagPo.class, metadata, "null", "lau_bilibili_tag");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(7).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(state, ColumnMetadata.named("state").withIndex(5).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(tagId, ColumnMetadata.named("tag_id").withIndex(2).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(tagName, ColumnMetadata.named("tag_name").withIndex(3).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(type, ColumnMetadata.named("type").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
    }

}

