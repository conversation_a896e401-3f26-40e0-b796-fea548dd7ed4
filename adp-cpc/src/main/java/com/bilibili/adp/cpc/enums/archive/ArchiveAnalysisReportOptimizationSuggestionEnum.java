package com.bilibili.adp.cpc.enums.archive;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ArchiveAnalysisReportOptimizationSuggestionEnum {
    /**
     * 未知
     */
    UNKNOWN(-404, "数据积累中，建议持续观察"),
    /**
     * 优质视频
     */
    HIGH_QUALITY_ARCHIVE(1, "播放率优异，建议加大投放"),
    /**
     * 满足门槛
     */
    MEET_THE_THRESHOLD(2, "播放率略低，建议持续优化"),
    /**
     * 未满足门槛
     */
    NOT_MEET_THE_THRESHOLD(3, "数据积累中，建议持续观察"),
    ;
    private final int code;

    private final String desc;

    public static ArchiveAnalysisReportOptimizationSuggestionEnum getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(t -> Objects.equals(t.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
