package com.bilibili.adp.cpc.dao.ad;

import com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestUpgradePo;
import com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestUpgradePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface ResTargetPackageProfessionInterestUpgradeDao {
    long countByExample(ResTargetPackageProfessionInterestUpgradePoExample example);

    int deleteByExample(ResTargetPackageProfessionInterestUpgradePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(ResTargetPackageProfessionInterestUpgradePo record);

    int insertBatch(List<ResTargetPackageProfessionInterestUpgradePo> records);

    int insertUpdateBatch(List<ResTargetPackageProfessionInterestUpgradePo> records);

    int insert(ResTargetPackageProfessionInterestUpgradePo record);

    int insertUpdateSelective(ResTargetPackageProfessionInterestUpgradePo record);

    int insertSelective(ResTargetPackageProfessionInterestUpgradePo record);

    List<ResTargetPackageProfessionInterestUpgradePo> selectByExample(ResTargetPackageProfessionInterestUpgradePoExample example);

    ResTargetPackageProfessionInterestUpgradePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ResTargetPackageProfessionInterestUpgradePo record, @Param("example") ResTargetPackageProfessionInterestUpgradePoExample example);

    int updateByExample(@Param("record") ResTargetPackageProfessionInterestUpgradePo record, @Param("example") ResTargetPackageProfessionInterestUpgradePoExample example);

    int updateByPrimaryKeySelective(ResTargetPackageProfessionInterestUpgradePo record);

    int updateByPrimaryKey(ResTargetPackageProfessionInterestUpgradePo record);
}