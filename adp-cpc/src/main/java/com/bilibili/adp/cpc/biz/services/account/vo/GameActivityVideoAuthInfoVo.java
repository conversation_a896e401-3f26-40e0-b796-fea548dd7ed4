package com.bilibili.adp.cpc.biz.services.account.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.impl.TableDocumentImpl;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GameActivityVideoAuthInfoVo {

    @ApiModelProperty("请求类型 1 授权 2 续期")
    private Integer requestType;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("代理商名称")
    private String agentName;

    @ApiModelProperty("稿件是否联合投稿,0：否，1：是")
    private Integer isCooperation;
    @ApiModelProperty("视频作者列表")
    private List<GeneralMidInfoVo> midInfoVoList;

//    @ApiModelProperty("视频授权模式， 1:原视频+UP主空间头像，2:原视频")
//    private Integer authModel;

    @ApiModelProperty("视频avid")
    private Long avid;

    @ApiModelProperty("视频bvid")
    private String bvid;

    @ApiModelProperty("视频封面")
    private String cover;

    @ApiModelProperty("视频标题")
    private String title;

    /**
     * 宽度
     */
    @ApiModelProperty(value = "宽度")
    private Long width;
    /**
     * 高度
     */
    @ApiModelProperty(value = "高度")
    private Long height;

    @ApiModelProperty(value = "Up主mid")
    private Long mid;

    @ApiModelProperty(value = "Up主名称")
    private String name;

    @ApiModelProperty(value = "Up主头像")
    private String avatar;

    @ApiModelProperty(value = "稿件简介")
    private String desc;

    @ApiModelProperty(value = "稿件状态")
    private Integer state;

    @ApiModelProperty(value = "稿件状态详情")
    private String stateDesc;

    @ApiModelProperty(value = "稿件总时长")
    private Long duration;

    @ApiModelProperty("生效时间")
    private Timestamp authEffectiveTime;

    @ApiModelProperty("新的授权时间类型，1:指定起始时间 2:不限")
    private Integer authTimeType;


    @ApiModelProperty("新的失效时间")
    private Timestamp authExpireTime;

    @ApiModelProperty("授权状态,1：授权待确认，2：授权待生效，3：授权中，4：授权失效，5：授权已拒绝，6：授权已撤回")
    private Integer authStatus;

    /**
     * 联合投稿触发
     * 1：授权待确认 文案：授权待确认 UP 主：xxx、xxx
     * 5：授权已拒绝 文案：授权拒绝 UP 主：xxx
     */
    @ApiModelProperty("授权状态描述")
    private String authStateDesc;

    /**
     * 授权状态（2：授权待生效，3：授权中）触发
     */
    @ApiModelProperty("续期状态，1：续期待确认，2：续期已拒绝，3:续约成功")
    private Integer renewalState;

    /**
     * 联合投稿触发
     * 文案：续期待确认 UP 主：xxx、xxx
     * 文案：续期拒绝 UP 主：xxx
     */
    @ApiModelProperty("续期状态描述")
    private String renewalStateDesc;

    @ApiModelProperty("稿件是否支持投放,true:支持，false:不支持")
    private Boolean isSupportAdvertising;


    private String failReason;

    private Long id;

    @ApiModelProperty("稿件原生状态")
    private Integer nativeStatus;
    @ApiModelProperty("稿件原生状态描述")
    private String nativeStatusDesc;
    @ApiModelProperty("绑定评论组件状态 0-未绑定 1-已绑定")
    private Integer hasBindComment;

}
