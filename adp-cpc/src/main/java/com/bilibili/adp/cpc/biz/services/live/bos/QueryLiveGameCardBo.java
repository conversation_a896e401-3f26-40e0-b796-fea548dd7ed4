package com.bilibili.adp.cpc.biz.services.live.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryLiveGameCardBo {

    private String nameLike;

    private Timestamp createBeginTime;

    private Timestamp createEndTime;

    //卡片类型：游戏-0，应用-1
    private Integer cardType;

    private String orderBy;

    private Long roomId;
}
