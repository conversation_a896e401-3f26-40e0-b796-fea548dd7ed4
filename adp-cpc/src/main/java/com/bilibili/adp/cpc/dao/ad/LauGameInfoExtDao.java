package com.bilibili.adp.cpc.dao.ad;

import com.bilibili.adp.cpc.po.ad.LauGameInfoPo;
import com.bilibili.adp.cpc.po.ad.LauGameInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauGameInfoExtDao {
    long countByExample(LauGameInfoPoExample example);

    int deleteByExample(LauGameInfoPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(LauGameInfoPo record);

    int insertBatch(List<LauGameInfoPo> records);

    //此处已被人工改动，请勿随便改动
    int insertUpdateBatch(List<LauGameInfoPo> records);

    int insert(LauGameInfoPo record);

    int insertUpdateSelective(LauGameInfoPo record);

    int insertSelective(LauGameInfoPo record);

    List<LauGameInfoPo> selectByExample(LauGameInfoPoExample example);

    LauGameInfoPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") LauGameInfoPo record, @Param("example") LauGameInfoPoExample example);

    int updateByExample(@Param("record") LauGameInfoPo record, @Param("example") LauGameInfoPoExample example);

    int updateByPrimaryKeySelective(LauGameInfoPo record);

    int updateByPrimaryKey(LauGameInfoPo record);
}