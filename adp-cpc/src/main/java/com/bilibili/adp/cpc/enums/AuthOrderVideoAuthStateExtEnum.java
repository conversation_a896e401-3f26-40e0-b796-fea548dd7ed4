package com.bilibili.adp.cpc.enums;

import java.util.Arrays;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description 授权商单视频-授权状态枚举
 * <AUTHOR>
 * @date 2022-01-01 4:58 下午
 */
@AllArgsConstructor
public enum AuthOrderVideoAuthStateExtEnum {
    /**
     * 待授权MCN-UP主确认
     */
    WAIT_CONFIRM_UP_AND_MCN(0,  "待授权MCN-UP主确认"),
    /**
     * 授权待UP主确认
     */
    WAIT_CONFIRM_UP(1,  "授权待UP主确认"),
    /**
     * 授权待MCN确认
     */
    WAIT_CONFIRM_MCN(2,  "授权待MCN确认"),
    /**
     * 授权已接受
     */
    CONFIRM(3,  "授权已接受"),
    /**
     * 授权已生效
     */
    CONFIRM_AND_ACTIVE(4,  "授权已生效"),
    /**
     * 授权已过期
     */
    CONFIRM_AND_TIME_OUT(5,  "授权已过期"),
    /**
     * MCN已拒绝授权
     */
    REJECT_BY_MCN(6,  "MCN已拒绝授权"),
    /**
     * UP主已拒绝授权
     */
    REJECT_BY_UP(7,  "UP主已拒绝授权"),
    /**
     * 授权已撤回
     */
    CANCEL(8,  "授权已撤回"),
    /**
     * 授权已失效
     */
    INVALID(9,  "授权已失效"),
    /**
     * 状态异常
     */
    ERROR(-1,  "状态异常"),
    ;
    @Getter
    private int code;

    @Getter
    private String desc;

    public static AuthOrderVideoAuthStateExtEnum getByCode(Integer code) {
        return Arrays.stream(values())
            .filter(authOrderVideoAuthStateEnum -> Objects.equals(code,
                authOrderVideoAuthStateEnum.getCode())).findFirst()
            .orElseThrow(() -> new IllegalArgumentException("invalid auth state code"));
    }
}
