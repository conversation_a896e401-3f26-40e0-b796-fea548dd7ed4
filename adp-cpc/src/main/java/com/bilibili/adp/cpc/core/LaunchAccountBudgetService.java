package com.bilibili.adp.cpc.core;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauAccountBudgetPo;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.mysema.commons.lang.Assert;
import edu.emory.mathcs.backport.java.util.Collections;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauAccountBudget.lauAccountBudget;

@Service
public class LaunchAccountBudgetService {

    @Value("${account.budget.listQueury.sizeLimit:50}")
    private Integer accountBudgetQuerySizeLimit;

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    public LauAccountBudgetPo get(Integer accountId) {
        return adBqf.selectFrom(lauAccountBudget)
                .where(lauAccountBudget.accountId.eq(accountId))
                .where(lauAccountBudget.isDeleted.eq(0))
                .fetchFirst();
    }

    public List<LauAccountBudgetPo> queryAccountBudgetList(List<Integer> accountIds) {

        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }
        Assert.isTrue(accountIds.size() <= accountBudgetQuerySizeLimit, "账户数量不能超过" + accountBudgetQuerySizeLimit);

        List<LauAccountBudgetPo> accountBudgetPos = adBqf.selectFrom(lauAccountBudget)
                .where(lauAccountBudget.accountId.in(accountIds))
                .where(lauAccountBudget.isDeleted.eq(0)).fetch(LauAccountBudgetPo.class);
        return accountBudgetPos;
    }

}
