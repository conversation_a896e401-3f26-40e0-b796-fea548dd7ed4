package com.bilibili.adp.cpc.core;

import com.bilibili.adp.cpc.biz.bos.creative.ShadowCreativeBo;
import com.bilibili.adp.cpc.biz.converter.creative.ProgrammaticCreativeDetailConverter;
import com.bilibili.adp.cpc.biz.services.creative.bos.programmatic.ProgrammaticInfoBo;
import com.bilibili.adp.cpc.biz.services.material.AdpCpcLauProgrammaticCreativeDetailService;
import com.bilibili.adp.cpc.core.bos.CreativeImageBo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeArchivePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeImagePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeTitlePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauProgrammaticCreativeDetailPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauMaterialBilibiliVideoWithCoverPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauMaterialPo;
import com.bilibili.adp.cpc.enums.CreativeDetailBizStatus;
import com.bilibili.adp.cpc.enums.MaterialType;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeArchive.lauCreativeArchive;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeComponent.lauCreativeComponent;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeImage.lauCreativeImage;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeTitle.lauCreativeTitle;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauProgrammaticCreativeDetail.lauProgrammaticCreativeDetail;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauMaterial.lauMaterial;
import static com.bilibili.adp.cpc.dao.querydsl.QLauMaterialBilibiliVideoWithCover.lauMaterialBilibiliVideoWithCover;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LaunchProgrammaticCreativeDetailService {
    private static final int LIMIT = 4;
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    @Autowired
    private AdpCpcLauProgrammaticCreativeDetailService adpCpcLauProgrammaticCreativeDetailService;

    @Autowired
    private LaunchShadowCreativeService launchShadowCreativeService;

    public Map<Integer, ProgrammaticInfoBo> getProgrammaticInfoMap(Collection<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return new HashMap<>();
        }
        final Map<Integer, List<LauProgrammaticCreativeDetailPo>> creativeDetailMap = adCoreBqf
                .selectFrom(lauProgrammaticCreativeDetail).where(lauProgrammaticCreativeDetail.creativeId.in(creativeIds))
                .fetch()
                .stream()
                .collect(Collectors.groupingBy(LauProgrammaticCreativeDetailPo::getCreativeId));

        final List<Integer> shadowCreativeAuditStatus = Collections.singletonList(com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING);
        final List<Integer> shadowCreativeCreativeStatus = Arrays.asList(com.bilibili.adp.cpc.core.constants.CreativeStatus.AUDITING, com.bilibili.adp.cpc.core.constants.CreativeStatus.LANDING_PAGE_AUDITING);
        Map<Integer, ShadowCreativeBo> shadowCreativeBoMap = launchShadowCreativeService.shadowCreatives(creativeIds, shadowCreativeAuditStatus, shadowCreativeCreativeStatus);

        // 影子替换
        for (Map.Entry<Integer, List<LauProgrammaticCreativeDetailPo>> entry : creativeDetailMap.entrySet()) {
            Integer creativeId = entry.getKey();
            ShadowCreativeBo shadowCreativeBo = shadowCreativeBoMap.get(creativeId);
            if (shadowCreativeBo != null && !CollectionUtils.isEmpty(shadowCreativeBo.getCreativeDetailBos())) {
                List<LauProgrammaticCreativeDetailPo> creativeDetailPos = shadowCreativeBo.getCreativeDetailBos()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(detailBo -> ProgrammaticCreativeDetailConverter.MAPPER.bo2po(detailBo)).collect(Collectors.toList());
                entry.setValue(creativeDetailPos);
            }
        }

        final Map<Integer, List<String>> creativeIdSampleImageUrlsMap = getCreativeIdSampleImageUrlsMap(creativeDetailMap);
        final List<Long> titleIds = creativeDetailMap
                .values()
                .stream()
                .flatMap(List::stream)
                .filter(detail -> Objects.equals(detail.getMaterialType(), MaterialType.TITLE.getCode()))
                .map(LauProgrammaticCreativeDetailPo::getMaterialId)
                .distinct()
                .collect(Collectors.toList());
        final Map<Long, String> materialIdContentMap = adBqf.select(lauMaterial.id, lauMaterial.materialContent)
                .from(lauMaterial)
                .where(lauMaterial.id.in(titleIds))
                .fetch(LauMaterialPo.class)
                .stream()
                .collect(Collectors.toMap(LauMaterialPo::getId, LauMaterialPo::getMaterialContent));
        return creativeDetailMap
                .entrySet()
                .stream()
                .map(entry -> {
                    final int creativeId = entry.getKey();
                    final List<LauProgrammaticCreativeDetailPo> details = entry
                            .getValue();
                    final List<String> sampleUrls = creativeIdSampleImageUrlsMap.get(creativeId);
                    if (CollectionUtils.isEmpty(sampleUrls)) {
                        return null;
                    }
                    final List<Long> titleMaterialIds = details
                            .stream()
                            .filter(detail -> Objects.equals(detail.getMaterialType(), MaterialType.TITLE.getCode()))
                            .map(LauProgrammaticCreativeDetailPo::getMaterialId)
                            .distinct()
                            .collect(Collectors.toList());
                    final Long sampleTitleMaterialId = titleMaterialIds
                            .stream()
                            .findFirst()
                            .orElse(0L);
                    final String sampleTitle = materialIdContentMap.getOrDefault(sampleTitleMaterialId, StringUtils.EMPTY);
                    // 获取全部标题
                    final List<String> allTitles = titleMaterialIds.stream()
                            .map(materialId -> materialIdContentMap.getOrDefault(materialId, StringUtils.EMPTY))
                            .filter(StringUtils::isNotEmpty)
                            .collect(Collectors.toList());

                    final long titleCount = titleMaterialIds.size();
                    final long mediaCount = details.size() - titleCount;
                    final long rejectedCount = entry.getValue().stream().filter(detail -> Objects.equals(detail.getBizStatus(), CreativeDetailBizStatus.REJECTED.getCode())).count();
                    final long waitingCount = entry.getValue().stream().filter(detail -> Objects.equals(detail.getBizStatus(), CreativeDetailBizStatus.WAITING.getCode())).count();
                    return ProgrammaticInfoBo.builder()
                            .creativeId(creativeId)
                            .sampleImageUrls(sampleUrls)
                            .sampleTitle(sampleTitle)
                            .mediaCount((int) mediaCount)
                            .titleCount((int) titleCount)
                            .auditRejectedCount((int) rejectedCount)
                            .auditWaitingCount((int) waitingCount)
                            .allTitles(allTitles)
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ProgrammaticInfoBo::getCreativeId, Function.identity()));
    }

    public Map<Integer, ProgrammaticInfoBo> getMergedProgrammaticInfoMap(Collection<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return new HashMap<>();
        }

        final Map<Integer, List<LauProgrammaticCreativeDetailPo>> creativeDetailMap = adCoreBqf
                .selectFrom(lauProgrammaticCreativeDetail).where(lauProgrammaticCreativeDetail.creativeId.in(creativeIds))
                .fetch()
                .stream()
                .collect(Collectors.groupingBy(LauProgrammaticCreativeDetailPo::getCreativeId));

        final List<Integer> shadowCreativeAuditStatus = Collections.singletonList(com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING);
        final List<Integer> shadowCreativeCreativeStatus = Arrays.asList(com.bilibili.adp.cpc.core.constants.CreativeStatus.AUDITING, com.bilibili.adp.cpc.core.constants.CreativeStatus.LANDING_PAGE_AUDITING);
        Map<Integer, ShadowCreativeBo> shadowCreativeBoMap = launchShadowCreativeService.shadowCreatives(creativeIds, shadowCreativeAuditStatus, shadowCreativeCreativeStatus);
        // 影子替换
        for (Map.Entry<Integer, List<LauProgrammaticCreativeDetailPo>> entry : creativeDetailMap.entrySet()) {
            ShadowCreativeBo shadowCreativeBo = shadowCreativeBoMap.get(entry.getKey());
            if (shadowCreativeBo != null && !CollectionUtils.isEmpty(shadowCreativeBo.getCreativeDetailBos())) {
                List<LauProgrammaticCreativeDetailPo> creativeDetailPos = shadowCreativeBo.getCreativeDetailBos()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(detailBo -> ProgrammaticCreativeDetailConverter.MAPPER.bo2po(detailBo)).collect(Collectors.toList());
                entry.setValue(creativeDetailPos);
            }
        }

        // 视频模板组用图片在稿件表上
        Map<Integer, List<String>> creativeArchiveCoverUrlMap = adCoreBqf.selectFrom(lauCreativeArchive)
                .where(lauCreativeArchive.creativeId.in(creativeIds))
                .fetch()
                .stream()
                .collect(Collectors.groupingBy(LauCreativeArchivePo::getCreativeId,
                        Collectors.mapping(LauCreativeArchivePo::getCoverUrl, Collectors.toList())));

        // 标题
        Map<Integer, List<String>> creativeTitleMap = adCoreBqf.selectFrom(lauCreativeTitle)
                .where(lauCreativeTitle.creativeId.in(creativeIds))
                .fetch()
                .stream()
                .collect(Collectors.groupingBy(LauCreativeTitlePo::getCreativeId,
                        Collectors.mapping(LauCreativeTitlePo::getTitle, Collectors.toList())));
        // 影子替换
        for (Map.Entry<Integer, List<String>> entry : creativeTitleMap.entrySet()) {
            ShadowCreativeBo shadowCreativeBo = shadowCreativeBoMap.get(entry.getKey());
            if (shadowCreativeBo != null && !CollectionUtils.isEmpty(shadowCreativeBo.getCreativeDetailBos())) {
                List<String> titles = shadowCreativeBo.getCreativeDetailBos()
                        .stream()
                        .filter(Objects::nonNull)
                        .filter(t -> Objects.equals(MaterialType.TITLE.getCode(), t.getMaterialType()))
                        .map(detailBo -> detailBo.getContent()).collect(Collectors.toList());
                entry.setValue(titles);
            }
        }

        // 图片
        Map<Integer, List<String>> creativeImageUrlMap = adCoreBqf.selectFrom(lauCreativeImage)
                .where(lauCreativeImage.creativeId.in(creativeIds))
                .fetch()
                .stream()
                .collect(Collectors.groupingBy(LauCreativeImagePo::getCreativeId,
                        Collectors.mapping(LauCreativeImagePo::getImageUrl, Collectors.toList())));
        // 影子替换
        for (Map.Entry<Integer, List<String>> entry : creativeImageUrlMap.entrySet()) {
            ShadowCreativeBo shadowCreativeBo = shadowCreativeBoMap.get(entry.getKey());
            if (shadowCreativeBo != null && !CollectionUtils.isEmpty(shadowCreativeBo.getImages())) {
                entry.setValue(shadowCreativeBo.getImages().stream().map(CreativeImageBo::getImageUrl).collect(Collectors.toList()));
            }
        }

        return creativeIds.stream().map(creativeId -> {
            List<LauProgrammaticCreativeDetailPo> detailPos = creativeDetailMap.getOrDefault(creativeId, Collections.emptyList());
            if (CollectionUtils.isEmpty(detailPos)) {
                return null;
            }

            // 视频模板组
            boolean hasVideo = detailPos.stream()
                    .anyMatch(detailPo -> detailPo.getMaterialType().equals(MaterialType.AVID_AND_COVER.getCode()));

            Integer titleCount = (int) detailPos.stream()
                    .filter(detailPo -> detailPo.getMaterialType().equals(MaterialType.TITLE.getCode()))
                    .count();
            Integer mediaCount = detailPos.size() - titleCount;

            List<String> videoCoverUrls = hasVideo ?
                    creativeArchiveCoverUrlMap.getOrDefault(creativeId, Collections.emptyList()) : Collections.emptyList();
            List<String> imageUrls = creativeImageUrlMap.getOrDefault(creativeId, Collections.emptyList());
            String sampleTitle =
                    creativeTitleMap.getOrDefault(creativeId, Collections.emptyList()).stream().findFirst().orElse("");
            List<String> sampleMediaUrls = Stream.of(videoCoverUrls, imageUrls)
                    .filter(urlList -> !CollectionUtils.isEmpty(urlList))
                    .flatMap(Collection::stream)
                    .limit(4L)
                    .collect(Collectors.toList());

            final long rejectedCount = detailPos.stream()
                    .filter(detail -> Objects.equals(detail.getBizStatus(), CreativeDetailBizStatus.REJECTED.getCode()))
                    .count();
            final long waitingCount = detailPos.stream()
                    .filter(detail -> Objects.equals(detail.getBizStatus(), CreativeDetailBizStatus.WAITING.getCode()))
                    .count();

            return ProgrammaticInfoBo.builder()
                    .creativeId(creativeId)
                    .sampleImageUrls(sampleMediaUrls)
                    .sampleTitle(sampleTitle)
                    .mediaCount(mediaCount)
                    .titleCount(titleCount)
                    .auditRejectedCount((int) rejectedCount)
                    .auditWaitingCount((int) waitingCount)
                    .allTitles(creativeTitleMap.getOrDefault(creativeId, Collections.emptyList()))
                    .build();


        }).filter(Objects::nonNull)
                .collect(Collectors.toMap(ProgrammaticInfoBo::getCreativeId, Function.identity()));
    }

    private Map<Integer, List<String>> getCreativeIdSampleImageUrlsMap(Map<Integer, List<LauProgrammaticCreativeDetailPo>> creativeDetailsMap) {
        final Map<Integer, List<String>> creativeIdImageGifCoverUrlsMap = creativeIdImageGifCoverUrlsMap(creativeDetailsMap);
        final Map<Integer, List<String>> creativeIdVideoCoverUrlsMap = creativeIdVideoCoverUrlsMap(creativeDetailsMap);
        return creativeDetailsMap
                .keySet()
                .stream()
                .collect(Collectors.toMap(creativeId -> creativeId, creativeId -> {
                    final List<String> imageGifCoverUrls = creativeIdImageGifCoverUrlsMap.getOrDefault(creativeId, Collections.emptyList());
                    final List<String> videoCoverUrls = creativeIdVideoCoverUrlsMap.getOrDefault(creativeId, Collections.emptyList());
                    return Stream.concat(imageGifCoverUrls.stream(), videoCoverUrls.stream())
                            .distinct()
                            .limit(LIMIT)
                            .collect(Collectors.toList());
                }));
    }
    private Map<Integer, List<String>> creativeIdImageGifCoverUrlsMap(Map<Integer, List<LauProgrammaticCreativeDetailPo>> creativeDetailsMap) {
        final Map<Integer, List<Long>> creativeIdImageGifMaterialIdsMap = creativeDetailsMap
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                    final List<LauProgrammaticCreativeDetailPo> details = entry.getValue();
                    return details
                            .stream()
                            .filter(detail -> Arrays.asList(MaterialType.IMAGE.getCode(), MaterialType.GIF.getCode()).contains(detail.getMaterialType()))
                            .map(LauProgrammaticCreativeDetailPo::getMaterialId)
                            .distinct()
                            .limit(LIMIT)
                            .collect(Collectors.toList());
                }));
        final List<Long> imageGifMaterialIds = creativeIdImageGifMaterialIdsMap
                .values()
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(imageGifMaterialIds)) {
            return Collections.emptyMap();
        }
        final Map<Long, String> materialIdUrlsMap = adBqf
                .select(lauMaterial.id, lauMaterial.materialContent)
                .from(lauMaterial)
                .where(lauMaterial.id.in(imageGifMaterialIds))
                .fetch(LauMaterialPo.class)
                .stream()
                .collect(Collectors.toMap(LauMaterialPo::getId, LauMaterialPo::getMaterialContent));
        return creativeIdImageGifMaterialIdsMap
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry
                        .getValue()
                        .stream()
                        .map(materialIdUrlsMap::get)
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList())));
    }

    private Map<Integer, List<String>> creativeIdVideoCoverUrlsMap(Map<Integer, List<LauProgrammaticCreativeDetailPo>> creativeDetailsMap) {
        final Map<Integer, List<String>> creativeIdMd5sMap = creativeDetailsMap
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                    final List<LauProgrammaticCreativeDetailPo> details = entry.getValue();
                    return details
                            .stream()
                            .filter(detail -> Objects.equals(MaterialType.AVID_AND_COVER.getCode(), detail.getMaterialType()))
                            .map(LauProgrammaticCreativeDetailPo::getMaterialMd5)
                            .distinct()
                            .limit(LIMIT)
                            .collect(Collectors.toList());
                }));
        final List<String> md5s = creativeIdMd5sMap
                .values()
                .stream()
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(md5s)) return Collections.emptyMap();

        final Map<String, String> bilibiliVideoMd5CoverUrlMap = adBqf
                .select(lauMaterialBilibiliVideoWithCover.materialMd5, lauMaterialBilibiliVideoWithCover.coverUrl)
                .from(lauMaterialBilibiliVideoWithCover)
                .where(lauMaterialBilibiliVideoWithCover.materialMd5.in(md5s))
                .fetch(LauMaterialBilibiliVideoWithCoverPo.class)
                .stream()
                .collect(Collectors.toMap(LauMaterialBilibiliVideoWithCoverPo::getMaterialMd5, LauMaterialBilibiliVideoWithCoverPo::getCoverUrl));
        if (CollectionUtils.isEmpty(bilibiliVideoMd5CoverUrlMap)) return Collections.emptyMap();

        return creativeDetailsMap
                .keySet()
                .stream()
                .collect(Collectors.toMap(creativeId -> creativeId, creativeId -> {
                    final List<String> creativeMd5s = creativeIdMd5sMap.get(creativeId);
                    if (CollectionUtils.isEmpty(creativeMd5s)) return Collections.emptyList();

                    return creativeMd5s.stream()
                            .map(bilibiliVideoMd5CoverUrlMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                }));
    }

    public void saveCreativeDetails(Integer creativeId, List<LauProgrammaticCreativeDetailPo> newVersions) {

        List<LauProgrammaticCreativeDetailPo> curVersions = adCoreBqf.selectFrom(lauProgrammaticCreativeDetail).where(lauProgrammaticCreativeDetail.creativeId.eq(creativeId))
                .fetch();

        final RecDiffResult<LauProgrammaticCreativeDetailPo, Long> result = CommonFuncs.recDiff(curVersions, newVersions, this::uk, LauProgrammaticCreativeDetailPo::getId, LauProgrammaticCreativeDetailPo::setId);
        CommonFuncs.handleRecDiff(result, adCoreBqf, lauCreativeComponent, lauCreativeComponent.id::in);

        // total md5
        adpCpcLauProgrammaticCreativeDetailService.doSaveTotalMd5(creativeId, ProgrammaticCreativeDetailConverter.MAPPER.pos2Bos(newVersions));
    }

    private String uk(LauProgrammaticCreativeDetailPo po) {
        return po.getCreativeId() + "-" + po.getMaterialId() + "-" + po.getMaterialType();
    }
}