package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauSelfDanmakuPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauSelfDanmaku is a Querydsl query type for LauSelfDanmakuPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauSelfDanmaku extends com.querydsl.sql.RelationalPathBase<LauSelfDanmakuPo> {

    private static final long serialVersionUID = *********;

    public static final QLauSelfDanmaku lauSelfDanmaku = new QLauSelfDanmaku("lau_self_danmaku");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> danmakuAuditStatus = createNumber("danmakuAuditStatus", Integer.class);

    public final StringPath danmakuColor = createString("danmakuColor");

    public final NumberPath<Long> danmakuIconId = createNumber("danmakuIconId", Long.class);

    public final NumberPath<Long> danmakuId = createNumber("danmakuId", Long.class);

    public final StringPath danmakuMd5 = createString("danmakuMd5");

    public final StringPath danmakuPicUrl = createString("danmakuPicUrl");

    public final NumberPath<Integer> danmakuStatus = createNumber("danmakuStatus", Integer.class);

    public final StringPath danmakuText = createString("danmakuText");

    public final NumberPath<Integer> danmakuType = createNumber("danmakuType", Integer.class);

    public final NumberPath<Long> groupId = createNumber("groupId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final StringPath reason = createString("reason");

    public final com.querydsl.sql.PrimaryKey<LauSelfDanmakuPo> primary = createPrimaryKey(id);

    public QLauSelfDanmaku(String variable) {
        super(LauSelfDanmakuPo.class, forVariable(variable), "null", "lau_self_danmaku");
        addMetadata();
    }

    public QLauSelfDanmaku(String variable, String schema, String table) {
        super(LauSelfDanmakuPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauSelfDanmaku(String variable, String schema) {
        super(LauSelfDanmakuPo.class, forVariable(variable), schema, "lau_self_danmaku");
        addMetadata();
    }

    public QLauSelfDanmaku(Path<? extends LauSelfDanmakuPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_self_danmaku");
        addMetadata();
    }

    public QLauSelfDanmaku(PathMetadata metadata) {
        super(LauSelfDanmakuPo.class, metadata, "null", "lau_self_danmaku");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(11).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(danmakuAuditStatus, ColumnMetadata.named("danmaku_audit_status").withIndex(13).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(danmakuColor, ColumnMetadata.named("danmaku_color").withIndex(6).ofType(Types.VARCHAR).withSize(16).notNull());
        addMetadata(danmakuIconId, ColumnMetadata.named("danmaku_icon_id").withIndex(7).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(danmakuId, ColumnMetadata.named("danmaku_id").withIndex(3).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(danmakuMd5, ColumnMetadata.named("danmaku_md5").withIndex(16).ofType(Types.VARCHAR).withSize(64).notNull());
        addMetadata(danmakuPicUrl, ColumnMetadata.named("danmaku_pic_url").withIndex(8).ofType(Types.VARCHAR).withSize(255).notNull());
        addMetadata(danmakuStatus, ColumnMetadata.named("danmaku_status").withIndex(9).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(danmakuText, ColumnMetadata.named("danmaku_text").withIndex(5).ofType(Types.VARCHAR).withSize(128).notNull());
        addMetadata(danmakuType, ColumnMetadata.named("danmaku_type").withIndex(4).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(groupId, ColumnMetadata.named("group_id").withIndex(15).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(10).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(12).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(reason, ColumnMetadata.named("reason").withIndex(14).ofType(Types.VARCHAR).withSize(128).notNull());
    }

}

