package com.bilibili.adp.cpc.biz.bos.archive;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArchiveAnalysisReportBo {
    /**
     * date
     */
    private String date;
    /**
     * aid
     */
    private Long aid;
    /**
     * 累计播放
     */
    private Long playTotal;
    /**
     * 累计评论
     */
    private Long replyTotal;
    /**
     * 累计收藏
     */
    private Long favTotal;
    /**
     * 累计投币
     */
    private Long coinTotal;
    /**
     * 累计弹幕
     */
    private Long danmuTotal;
    /**
     * 累计分享
     */
    private Long shareTotal;
    /**
     * 累计点赞
     */
    private Long likesTotal;
    /**
     * 当日播放
     */
    private Long playIncrement;
    /**
     * 当日评论
     */
    private Long replyIncrement;
    /**
     * 当日收藏
     */
    private Long favIncrement;
    /**
     * 当日投币
     */
    private Long coinIncrement;
    /**
     * 当日弹幕
     */
    private Long danmuIncrement;
    /**
     * 当日分享
     */
    private Long shareIncrement;
    /**
     * 当日点赞
     */
    private Long likesIncrement;

    private BigDecimal cost;

    private Long playCnt;

    private Long play3sCnt;

    private Long play5sCnt;

    private Long play10sCnt;

    private BigDecimal play3sRate;

    private BigDecimal play5sRate;

    private BigDecimal play10sRate;

    private Long pv;

    private Long click;

    private BigDecimal ctr;

    private BigDecimal cpc;

    private BigDecimal cpm;

    //region 销售线索
    //region 表单提交
    /**
     * 表单提交数
     */
    private Long formSubmitCount;
    /**
     * 表单提交成本
     */
    private BigDecimal formSubmitCost;
    /**
     * 表单提交率
     */
    private BigDecimal formSubmitRate;
    //endregion
    //region 表单付费
    /**
     * 表单付费数
     */
    private Long formUserCostCount;
    /**
     * 表单付费成本
     */
    private BigDecimal formUserCostCost;
    /**
     * 表单付费率
     */
    private BigDecimal formUserCostRate;
    //endregion
    //region 有效线索
    /**
     * 有效线索数
     */
    private Long clueCount;
    /**
     * 有效线索成本
     */
    private BigDecimal clueCost;
    /**
     * 有效线索率
     */
    private BigDecimal clueRate;
    //endregion
    //endregion
    //region 应用与游戏
    //region 游戏预约
    /**
     * 游戏预约数
     */
    private Long gameSubscribeApiCount;
    /**
     * 游戏预约成本
     */
    private BigDecimal gameSubscribeApiCost;
    /**
     * 游戏预约率
     */
    private BigDecimal gameSubscribeApiRate;
    //endregion
    //region 安卓下载
    /**
     * 安卓下载数
     */
    private Long downloadSuccessCount;
    /**
     * 安卓下载成本
     */
    private BigDecimal downloadSuccessCost;
    /**
     * 安卓下载率
     */
    private BigDecimal downloadSuccessRate;
    //endregion
    //region 安卓安装
    /**
     * 安卓安装数
     */
    private Long installSuccessCount;
    /**
     * 安卓安装成本
     */
    private BigDecimal installSuccessCost;
    /**
     * 安卓安装率
     */
    private BigDecimal installSuccessRate;
    //endregion
    //region 应用接活
    /**
     * ios激活数
     */
    private Long iosAppFirstActiveCount;
    /**
     * 安卓激活数
     */
    private Long androidAppFirstActiveCount;
    /**
     * 应用激活数
     */
    private Long appFirstActiveCount;
    /**
     * 应用激活成本
     */
    private BigDecimal appFirstActiveCost;
    /**
     * 应用激活率
     */
    private BigDecimal appFirstActiveRate;
    //endregion
    //region 安卓游戏中心激活
    /**
     * 安卓游戏中心激活数
     */
    private Long gameActiveApiCount;
    /**
     * 安卓游戏中心激活成本
     */
    private BigDecimal gameActiveApiCost;
    /**
     * 安卓游戏中心激活率
     */
    private BigDecimal gameActiveApiRate;
    //endregion 安卓游戏中心接活
    //region 用户注册
    /**
     * 用户注册数
     */
    private Long userRegisterCount;
    /**
     * 用户注册成本
     */
    private BigDecimal userRegisterCost;
    /**
     * 用户注册率
     */
    private BigDecimal userRegisterRate;
    //endregion
    //region 应用内付费
    /**
     * 应用内付费
     */
    private Long userCostCount;
    /**
     * 应用内付费金额
     */
    private BigDecimal userCostValue;
    /**
     * 应用内首次付费数
     */
    private Long userFirstCostCount;
    /**
     * 应用内首次付费金额
     */
    private BigDecimal userFirstCostValue;
    /**
     * 应用内首次付费成本
     */
    private BigDecimal userFirstCostCost;
    /**
     * 应用内首次付费率
     */
    private BigDecimal userFirstCostRate;
    //endregion
    //region 安卓游戏中心应用内付费次数
    /**
     *  安卓游戏中心应用内付费次数
     */
    private Long gameUserCostCount;
    /**
     *  安卓游戏中心应用内付费金额
     */
    private BigDecimal gameUserCostValue;
    /**
     *  安卓游戏中心应用内首次付费次数
     */
    private Long gameUserFirstCostCount;
    /**
     *  安卓游戏中心应用内首次付费金额
     */
    private BigDecimal gameUserFirstCostValue;
    /**
     *  安卓游戏中心应用内首次付费成本
     */
    private BigDecimal gameUserFirstCostCost;
    /**
     *  安卓游戏中心应用内首次付费率
     */
    private BigDecimal gameUserFirstCostRate;
    //endregion
    //region 次日留存
    /**
     * 次日留存数
     */
    private Long retentionCount;
    /**
     * 次日留存成本
     */
    private BigDecimal retentionCost;
    /**
     * 次日留存率
     */
    private BigDecimal retentionRate;
    //endregion
    //region 应用唤起
    /**
     * 应用内访问数
     */
    private Long appCallupCount;
    /**
     * 应用内访问数成本
     */
    private BigDecimal appCallupCost;
    /**
     * 应用内访问率
     */
    private BigDecimal appCallupRate;
    //endregion
    //endregion
    //region 电商店铺
    //region 落地页店铺调起
//    /**
//     * 落地页调起店铺数
//     */
//    private Long lpCallupCount;
    /**
     * 应用唤起数
     */
    private Long callupSucCount;
    /**
     * 应用唤起成本
     */
    private BigDecimal callupSucCost;
    /**
     * 应用唤起率
     */
    private BigDecimal callupSucRate;
    //endregion
    //region 店铺停留
    /**
     * 店铺停留数
     */
    private Long lpCallupSuccStayCount;
    /**
     * 店铺停留率
     */
    private BigDecimal lpCallupSuccStayRate;
    /**
     * 店铺停留成本
     */
    private BigDecimal lpCallupSuccStayCost;
    //endregion
    //region 订单提交
    /**
     * 订单提交数
     */
    private Long orderPlaceCount;
    /**
     * 订单提交金额
     */
    private BigDecimal orderPlaceValue;
    /**
     * 订单提交率
     */
    private BigDecimal orderPlaceRate;
    /**
     * 订单提交成本
     */
    private BigDecimal orderPlaceCost;
    /**
     * 订单ROI
     */
    private BigDecimal orderRoi;
    /**
     * 订单转化率
     */
    private BigDecimal orderConversionRate;
    /**
     * 唤起成单率
     */
    private BigDecimal callUpOrderSuccessRate;

    /**
     * 用户首次付费数
     */
    private Long firstOrderPlaceCount;

    /**
     * 用户首次付费数
     */
    private BigDecimal firstOrderPlaceValue;
    /**
     * 用户首次付费成本
     */
    private BigDecimal firstOrderPlaceCost;
    /**
     * 用户首次付费率
     */
    private BigDecimal firstOrderPlaceRate;
    //endregion
    //region 关键行为
    /**
     * 关键行为数
     */
    private Long actionValidCount;
    /**
     * 关键行为率
     */
    private BigDecimal actionValidRate;
    /**
     * 关键行为成本
     */
    private BigDecimal actionValidCost;
    //endregion
    //endregion

}
