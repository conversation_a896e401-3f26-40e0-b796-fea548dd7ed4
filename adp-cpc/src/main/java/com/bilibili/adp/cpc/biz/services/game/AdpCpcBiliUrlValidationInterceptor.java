package com.bilibili.adp.cpc.biz.services.game;

import com.google.common.base.Joiner;
import okhttp3.HttpUrl;
import okhttp3.Request;
import pleiades.component.utility.common.Signatures;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public class AdpCpcBiliUrlValidationInterceptor {
    private static final Joiner valueJoiner = Joiner.on(",").skipNulls();
    private static final Joiner.MapJoiner queryJoiner = Joiner.on("&").withKeyValueSeparator("=");

    public static Request intercept(Request origin, String appKey, String appSecret) {
        HttpUrl httpUrl = origin.url();
        Map<String, String> params = new TreeMap<>();
        params.put("appkey", appKey);
        params.put("ts", String.valueOf(System.currentTimeMillis()));

        for(int i = 0; i < httpUrl.querySize(); ++i) {
            String name = httpUrl.queryParameterName(i);
            List<String> values = httpUrl.queryParameterValues(name);
            if (!params.containsKey(name)) {
                params.put(name, valueJoiner.join(values));
            }
        }

        String signature = Signatures.getSignature(params, appSecret);
        params.put("sign", signature);
        return origin.newBuilder()
                .url(httpUrl.newBuilder()
                        .query(queryJoiner.join(params))
                        .build())
                .build();
    }
}
