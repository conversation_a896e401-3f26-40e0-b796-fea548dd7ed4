package com.bilibili.adp.cpc.core.shadow;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.creative.ShadowCreativeBo;
import com.bilibili.adp.cpc.biz.converter.creative.ShadowCreativeConverter;
import com.bilibili.adp.cpc.biz.services.creative.CpcSaveCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.core.LaunchShadowCreativeService;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauShadowCreativePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.repo.LauUnitCreativeRepo;
import com.bilibili.adp.cpc.repo.OuterLauUnitRepo;
import com.bilibili.adp.launch.biz.pojo.LauUnitCreativePo;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_TM;

/**
 * <AUTHOR>
 * @date 2024/1/4 01:55
 */
@Component
@Slf4j
public class PersonalFlyCreativeShadowProc {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private LaunchShadowCreativeService launchShadowCreativeService;
    @Autowired
    private LauUnitCreativeRepo lauUnitCreativeRepo;
    @Autowired
    private OuterLauUnitRepo outerLauUnitRepo;
    @Autowired
    private CpcSaveCreativeService cpcSaveCreativeService;

    /**
     * 保存创意的影子，目前给个人起飞用
     */
    @SneakyThrows
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Integer savePersonalFlyCreativeShadow(Integer creativeId) {
        log.info("saveCreativeShadow exec, creativeId={}", creativeId);
        if (!Utils.isPositive(creativeId)) {
            return 0;
        }

        // 获取创意
        LauUnitCreativePo creativePo = lauUnitCreativeRepo.queryCreativesByCreativeId(creativeId);
        if (creativePo == null) {
            log.error("saveCreativeShadow, creativePo 不存在, creativeId={}", creativeId);
            return 0;
        }

        Map<Integer, CpcCreativeDto> oldCreativeDtoMap = cpcSaveCreativeService.getMapInIds(Arrays.asList(creativeId));
        CpcCreativeDto oldCpcCreativeDto = oldCreativeDtoMap.get(creativeId);
        if (oldCpcCreativeDto == null) {
            log.error("saveCreativeShadow, oldCpcCreativeDto 不存在, creativeId={}", creativeId);
            return 0;
        }

        LauUnitPo lauUnitPo = outerLauUnitRepo.queryUnitById(creativePo.getUnitId());
        if (lauUnitPo == null) {
            log.error("saveCreativeShadow, lauUnitPo 不存在, creativeId={}", creativeId);
            return 0;
        }

        // 获取创意影子
        ShadowCreativeBo oldShadowCreativeBo = launchShadowCreativeService.shadowCreative(creativeId);

        // 已经存在创意影子则跳过
        if (oldShadowCreativeBo != null) {
            log.info("saveCreativeShadow, 影子已经存在, creativeId={}", creativeId);
            return 0;
        }

        //保存影子创意
        ShadowCreativeBo.ShadowCreativeBoBuilder shadowCreativeBuilder = ShadowCreativeBo.builder();
        shadowCreativeBuilder.accountId(creativePo.getAccountId());
        shadowCreativeBuilder.campaignId(creativePo.getCampaignId());
        shadowCreativeBuilder.unitId(creativePo.getUnitId());
        shadowCreativeBuilder.creativeId(creativeId);
        shadowCreativeBuilder.promotionPurposeType(lauUnitPo.getPromotionPurposeType());
        shadowCreativeBuilder.advertisingMode(creativePo.getAdvertisingMode());
        shadowCreativeBuilder.jumpType(creativePo.getJumpType());
        shadowCreativeBuilder.jumpUrl(creativePo.getPromotionPurposeContent());
        shadowCreativeBuilder.jumpUrlSecondary(creativePo.getPromotionPurposeContentSecondary());
        shadowCreativeBuilder.title(creativePo.getTitle());
        shadowCreativeBuilder.description(creativePo.getDescription());
        shadowCreativeBuilder.extDescription(creativePo.getExtDescription());
        shadowCreativeBuilder.mgkPageId(creativePo.getMgkPageId());
        shadowCreativeBuilder.videoId(creativePo.getVideoId());
        shadowCreativeBuilder.cmMark(creativePo.getCmMark());
        shadowCreativeBuilder.busMark(creativePo.getBusMarkId());
        shadowCreativeBuilder.button(null);
        shadowCreativeBuilder.images(null);
        shadowCreativeBuilder.components(null);
        shadowCreativeBuilder.tab(null);
        shadowCreativeBuilder.landingPage(null);
        shadowCreativeBuilder.flyExtInfo(null); // 回填时未用到
        shadowCreativeBuilder.trackadf(creativePo.getTrackadf());
        shadowCreativeBuilder.auditStatus(com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING);
        shadowCreativeBuilder.creativeStatus(com.bilibili.adp.cpc.core.constants.CreativeStatus.AUDITING);
        shadowCreativeBuilder.pageGroupId(null);
        shadowCreativeBuilder.isPageGroup(null);
        shadowCreativeBuilder.canJointLaunchPageGroup(null);
        ShadowCreativeBo shadowCreativeBo = shadowCreativeBuilder.build();

        String shadowCreative = objectMapper.writeValueAsString(shadowCreativeBo);
        final List<LauShadowCreativePo> shadowCreatives = Stream.of(shadowCreativeBo).filter(Objects::nonNull).map(bo -> ShadowCreativeConverter.MAPPER.bo2Po(bo, shadowCreative)).collect(Collectors.toList());
        launchShadowCreativeService.saveByCreativeId(creativeId, shadowCreatives);
        return 1;
    }

    public Long deletePersonalFlyCreativeShadows(List<Integer> creativeIds) {
        return launchShadowCreativeService.deleteByCreativeIds(creativeIds);
    }
}
