package com.bilibili.adp.cpc.biz.services.business_message.delegate;


import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.cpc.biz.services.business_message.dto.CreateMessageComponentDto;
import com.bilibili.adp.cpc.dao.ad.MessageComponentDao;
import com.bilibili.adp.cpc.po.ad.MessageComponentPo;
import com.bilibili.adp.cpc.po.ad.MessageComponentPoExample;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.List;

import static com.bilibili.adp.cpc.biz.services.archive.BusinessToolArchiveConversionComponentService.MESSAGE_COMPONENT_MACRO_PARAM;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;
import static com.bilibili.adp.cpc.dao.querydsl.QMessageComponent.messageComponent;

@Service
@Slf4j
public class BusinessMessageServiceDelegate {
    @Resource
    private MessageComponentDao messageComponentDao;

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    public MessageComponentPo selectByPrimaryKey(Long id){
        return messageComponentDao.selectByPrimaryKey(id);
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public MessageComponentPo doCreateComponent(CreateMessageComponentDto createMessageComponentDto, Integer adCreativeId) {
        Assert.isTrue(StringUtils.isNotBlank(createMessageComponentDto.getJumpUrl()), "跳转链接不能为空");
        MessageComponentPo newMessageComponentPo = new MessageComponentPo();
        newMessageComponentPo.setAccountId(createMessageComponentDto.getAccountId());
        newMessageComponentPo.setContent(createMessageComponentDto.getContent());
        newMessageComponentPo.setImageUrl(createMessageComponentDto.getImageUrl());
        String url = UriComponentsBuilder.fromUriString(createMessageComponentDto.getJumpUrl())
                // 宏替换
                .query(MESSAGE_COMPONENT_MACRO_PARAM)
                .build(false)
                .toUriString();
        newMessageComponentPo.setJumpUrl(url);
        newMessageComponentPo.setIsDeleted((byte) 0);
        newMessageComponentPo.setMessageType(createMessageComponentDto.getMessageType());
        newMessageComponentPo.setAdCreativeId(adCreativeId.longValue());
        newMessageComponentPo.setRelatePageId(createMessageComponentDto.getRelatePageId());
        newMessageComponentPo.setIconUrl(createMessageComponentDto.getIconUrl());
        newMessageComponentPo.setName(createMessageComponentDto.getName());
        log.info("newMessageComponentPo:{}", JSON.toJSONString(newMessageComponentPo));
        messageComponentDao.insertSelective(newMessageComponentPo);
        return newMessageComponentPo;

    }

    public MessageComponentPo newDoCreateComponent(CreateMessageComponentDto createMessageComponentDto, Integer adCreativeId) {
        Assert.isTrue(StringUtils.isNotBlank(createMessageComponentDto.getJumpUrl()), "跳转链接不能为空");
        MessageComponentPo newMessageComponentPo = new MessageComponentPo();
        newMessageComponentPo.setAccountId(createMessageComponentDto.getAccountId());
        newMessageComponentPo.setContent(createMessageComponentDto.getContent());
        newMessageComponentPo.setImageUrl(createMessageComponentDto.getImageUrl());
        String url = UriComponentsBuilder.fromUriString(createMessageComponentDto.getJumpUrl())
                // 宏替换
                .query(MESSAGE_COMPONENT_MACRO_PARAM)
                .build(false)
                .toUriString();
        newMessageComponentPo.setJumpUrl(url);
        newMessageComponentPo.setIsDeleted((byte) 0);
        newMessageComponentPo.setMessageType(createMessageComponentDto.getMessageType());
        newMessageComponentPo.setAdCreativeId(adCreativeId.longValue());
        newMessageComponentPo.setRelatePageId(createMessageComponentDto.getRelatePageId());
        newMessageComponentPo.setIconUrl(createMessageComponentDto.getIconUrl());
        newMessageComponentPo.setName(createMessageComponentDto.getName());
        log.info("newMessageComponentPo:{}", JSON.toJSONString(newMessageComponentPo));
        messageComponentDao.insertSelective(newMessageComponentPo);
        return newMessageComponentPo;

    }

    public List<MessageComponentPo> selectByAccountId(Long accountId){
        MessageComponentPoExample example = new MessageComponentPoExample();
        example.createCriteria().andAccountIdEqualTo(accountId).andIsDeletedEqualTo((byte) 0);
        return messageComponentDao.selectByExample(example);
    }

    public List<com.bilibili.adp.cpc.dao.querydsl.pos.MessageComponentPo> bqfGetMessageComponentPoList(Long accountId, Byte messageType, Integer pageIndex, Integer pageSize, Boolean includeDeleted, Long relatePageId){
        if(includeDeleted){
            return adBqf.from(messageComponent)
                    .where(messageComponent.accountId.eq(accountId))
                    .whereIfNotNull(messageType,  x -> messageComponent.messageType.eq(messageType == null?null:Integer.valueOf(messageType)))
                    .whereIfNotNull(relatePageId, x->messageComponent.relatePageId.eq(relatePageId))
                    .limit(pageSize)
                    .offset(pageSize*(pageIndex-1))
                    .select(messageComponent)
                    .fetch(com.bilibili.adp.cpc.dao.querydsl.pos.MessageComponentPo.class);
        }
        return adBqf.from(messageComponent)
                .where(messageComponent.accountId.eq(accountId))
                .whereIfNotNull(messageType,  x -> messageComponent.messageType.eq(messageType == null?null:Integer.valueOf(messageType)))
                .whereIfNotNull(relatePageId, x->messageComponent.relatePageId.eq(relatePageId))
                .where(messageComponent.isDeleted.eq(0))
                .limit(pageSize)
                .offset(pageSize*(pageIndex-1))
                .select(messageComponent)
                .fetch(com.bilibili.adp.cpc.dao.querydsl.pos.MessageComponentPo.class);
    }

    public long getMessageComponentPoSize(Long accountId, Byte messageType, Boolean includeDeleted, Long relatePageId) {
        if(includeDeleted){
            return adBqf.from(messageComponent)
                    .where(messageComponent.accountId.eq(accountId))
                    .whereIfNotNull(messageType,  x -> messageComponent.messageType.eq(messageType == null?null:Integer.valueOf(messageType)))
                    .where(messageComponent.isDeleted.in(0,1))
                    .whereIfNotNull(relatePageId, x->messageComponent.relatePageId.eq(relatePageId))
                    .select(messageComponent)
                    .fetchCount();
        }
        return adBqf.from(messageComponent)
                .where(messageComponent.accountId.eq(accountId))
                .whereIfNotNull(messageType,  x -> messageComponent.messageType.eq(messageType == null?null:Integer.valueOf(messageType)))
                .where(messageComponent.isDeleted.eq(0))
                .whereIfNotNull(relatePageId, x->messageComponent.relatePageId.eq(relatePageId))
                .select(messageComponent)
                .fetchCount();
    }

    public Long deleteByAccountIdAndCreativeId(Long accountId, Long creativeId) {
        log.info("accountId={},creativeId={}",accountId,creativeId);
        return adBqf.update(messageComponent)
                .where(messageComponent.accountId.eq(accountId))
                .where(messageComponent.adCreativeId.eq(creativeId))
                .set(messageComponent.isDeleted, IsDeleted.DELETED.getCode())
                .execute();
    }

    public List<com.bilibili.adp.cpc.dao.querydsl.pos.MessageComponentPo> bqfGetMessageComponentPoList(Long accountId, Long creativeId){
        log.info("accountId={},creativeId={}",accountId,creativeId);
        return adBqf.from(messageComponent)
                .where(messageComponent.accountId.eq(accountId))
                .where(messageComponent.adCreativeId.eq(creativeId))
                .select(messageComponent)
                .fetch(com.bilibili.adp.cpc.dao.querydsl.pos.MessageComponentPo.class);
    }

    public Long updateMessageComponentTitle(long pageId, String title) {
        return adBqf.update(messageComponent)
                .where(messageComponent.relatePageId.eq(pageId))
                .set(messageComponent.content, title)
                .execute();
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public MessageComponentPo updateCreateMessageComponent(Long accountId, Long creativeId, CreateMessageComponentDto createMessageComponentDto, Integer newCreativeId) {
        MessageComponentPoExample messageComponentPoExample = new MessageComponentPoExample();
        messageComponentPoExample.createCriteria().andAccountIdEqualTo(accountId).andAdCreativeIdEqualTo(creativeId);
        MessageComponentPo update = new MessageComponentPo();
        update.setAccountId(accountId);
        update.setAdCreativeId(creativeId);
        update.setIsDeleted((byte)1);
        messageComponentDao.updateByExampleSelective(update,messageComponentPoExample);
        return newDoCreateComponent(createMessageComponentDto,newCreativeId.intValue());
    }
}
