package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauPreviewMidPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauPreviewMid is a Querydsl query type for LauPreviewMidPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauPreviewMid extends com.querydsl.sql.RelationalPathBase<LauPreviewMidPo> {

    private static final long serialVersionUID = *********;

    public static final QLauPreviewMid lauPreviewMid = new QLauPreviewMid("lau_preview_mid");

    public final NumberPath<Long> accountId = createNumber("accountId", Long.class);

    public final NumberPath<Long> creativeId = createNumber("creativeId", Long.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> mid = createNumber("mid", Long.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Long> previewId = createNumber("previewId", Long.class);

    public final NumberPath<Integer> previewStatus = createNumber("previewStatus", Integer.class);

    public final com.querydsl.sql.PrimaryKey<LauPreviewMidPo> primary = createPrimaryKey(id);

    public QLauPreviewMid(String variable) {
        super(LauPreviewMidPo.class, forVariable(variable), "null", "lau_preview_mid");
        addMetadata();
    }

    public QLauPreviewMid(String variable, String schema, String table) {
        super(LauPreviewMidPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauPreviewMid(String variable, String schema) {
        super(LauPreviewMidPo.class, forVariable(variable), schema, "lau_preview_mid");
        addMetadata();
    }

    public QLauPreviewMid(Path<? extends LauPreviewMidPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_preview_mid");
        addMetadata();
    }

    public QLauPreviewMid(PathMetadata metadata) {
        super(LauPreviewMidPo.class, metadata, "null", "lau_preview_mid");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(8).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(creativeId, ColumnMetadata.named("creative_id").withIndex(3).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(mid, ColumnMetadata.named("mid").withIndex(5).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(7).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(previewId, ColumnMetadata.named("preview_id").withIndex(2).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(previewStatus, ColumnMetadata.named("preview_status").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
    }

}

