package com.bilibili.adp.cpc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewLauCampaignNextdayBudgetDto {

    private Integer id;
    private Integer campaignId;
    private Long nextdayBudget;
    private Integer nextdayBudgetLimitType;

    /**
     * 是否每次重复
     * 仅仅针对次日预算生效
     */
    private Integer isRepeat;
}
