package com.bilibili.adp.cpc.biz.services.unit.strategy.game_card;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.cpc.biz.services.unit.dto.NewCpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.UpdateCpcUnitDto;
import com.bilibili.adp.cpc.dto.GameCardUnitTargetDto;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.biz.converter.game.IGameConverter;
import com.bilibili.adp.cpc.biz.converter.unit.UnitGameConverter;
import com.bilibili.adp.cpc.biz.services.tag.TagService;
import com.bilibili.adp.cpc.dto.UnitTargetRuleTagDto;
import com.bilibili.adp.cpc.dto.UnitTargetRuleArchiveDto;
import com.bilibili.adp.cpc.biz.services.unit.strategy.UnitExtraProcessStrategy;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauBilibiliGamePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitGamePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitTargetArchivePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitTargetTagPo;
import com.bilibili.adp.launch.api.unit.dto.UnitGameDto;
import com.bilibili.adp.passport.api.dto.GameDto;
import com.bilibili.adp.passport.api.service.IGameCenterService;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.querydsl.QLauBilibiliGame.lauBilibiliGame;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitGame.lauUnitGame;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitTargetArchive.lauUnitTargetArchive;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitTargetTag.lauUnitTargetTag;

/**
 *
 * 游戏卡单元额外处理策略
 * <AUTHOR>
 * @date 2022-02-08 8:51 下午
 */
@Slf4j
@Service
public class GameCardIUnitExtraProcessStrategy extends UnitExtraProcessStrategy {
    private final IGameCenterService gameCenterService;

    private final TagService tagService;

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    public GameCardIUnitExtraProcessStrategy(
        IGameCenterService gameCenterService,
        TagService tagService) {
        this.gameCenterService = gameCenterService;
        this.tagService = tagService;
    }


    @Override
    public Integer getPromotionPurposeType() {
        return PromotionPurposeType.GAME_CARD.getCode();
    }

    @Override
    public void processNewUnit(Integer unitId, NewCpcUnitDto unit) {
        this.dealGameInfo(UnitGameDto.builder()
            .unitId(unitId)
            .gameBaseId(unit.getGameBaseId())
            .platformType(unit.getGamePlatformType())
            .build());
        // 由于游戏卡 和 游戏活动卡 可能不选择 「标签」 或 「视频」定向
        // 如果没传 直接返回
        // 保存定向信息
        GameCardUnitTargetDto targets = unit.getGameCardUnitTargets();
        if (Objects.isNull(targets)) {
            return;
        }
        // 处理稿件定向
        List<Long> avids = targets.getArchive().stream().map(UnitTargetRuleArchiveDto::getAvid)
            .collect(
                Collectors.toList());
        this.dealGameCardUnitArchiveTargets(unitId, unit.getGamePlatformType(), avids);
        //处理标签定向
        List<Long> tagIds = targets.getTag().stream().map(UnitTargetRuleTagDto::getTagId).collect(
            Collectors.toList());
        this.dealGameCardUnitTagTargets(unitId, unit.getGamePlatformType(), tagIds);
        // 保存tag信息
        List<String> tagNames = targets.getTag().stream().map(UnitTargetRuleTagDto::getTagName)
            .collect(Collectors.toList());
        tagService.saveTags(tagNames);
    }

    @Override
    public void processUpdateUnit(UpdateCpcUnitDto unit) {
        this.dealGameInfo(UnitGameDto.builder()
            .unitId(unit.getUnitId())
            .gameBaseId(unit.getGameBaseId())
            .platformType(unit.getGamePlatformType())
            .build());
        // 由于游戏卡 和 游戏活动卡 可能不选择 「标签」 或 「视频」定向
        // 如果没传 直接返回
        // 保存定向信息
        GameCardUnitTargetDto targets = unit.getGameCardUnitTargets();
        if (Objects.isNull(targets)) {
            return;
        }

        // 处理稿件定向
        List<Long> avids = targets.getArchive().stream().map(UnitTargetRuleArchiveDto::getAvid)
            .collect(
                Collectors.toList());
        this.dealGameCardUnitArchiveTargets(unit.getUnitId(), unit.getGamePlatformType(), avids);
        //处理标签定向
        List<Long> tagIds = targets.getTag().stream().map(UnitTargetRuleTagDto::getTagId).collect(
            Collectors.toList());
        this.dealGameCardUnitTagTargets(unit.getUnitId(), unit.getGamePlatformType(), tagIds);
        // 保存tag信息
        List<String> tagNames = targets.getTag().stream().map(UnitTargetRuleTagDto::getTagName)
            .collect(Collectors.toList());
        tagService.saveTags(tagNames);
    }

    @Override
    public void processDeleteUnit(List<Integer> unitIds) {
        super.processDeleteUnit(unitIds);
        adCoreBqf.update(lauUnitTargetArchive)
            .set(lauUnitTargetArchive.isDeleted, IsDeleted.DELETED.getCode())
            .where(lauUnitTargetArchive.unitId.in(unitIds)).execute();
        adCoreBqf.update(lauUnitTargetTag)
            .set(lauUnitTargetTag.isDeleted, IsDeleted.DELETED.getCode())
            .where(lauUnitTargetTag.unitId.in(unitIds)).execute();
    }

    /**
     * 处理游戏卡单元 游戏信息
     *
     * @param gameInfo 单元游戏信息
     * <AUTHOR>
     * @date 2022-01-30 4:57 下午
     */
    private void dealGameInfo(@NonNull UnitGameDto gameInfo) {
        // 保存单元游戏信息
        LauUnitGamePo existGame = adCoreBqf.selectFrom(lauUnitGame)
            .where(lauUnitGame.unitId.eq(gameInfo.getUnitId()))
            .where(lauUnitGame.gameBaseId.eq(gameInfo.getGameBaseId()))
            .where(lauUnitGame.platformType.eq(gameInfo.getPlatformType()))
            .fetchFirst();
        log.info(
            "start to deal unit game info, unit id :{}, game base id : {}, platform type : {}",
            gameInfo.getUnitId(), gameInfo.getGameBaseId(), gameInfo.getPlatformType()
        );

        LauUnitGamePo newGame = UnitGameConverter.MAPPER.dto2Po(gameInfo);
        if (Objects.nonNull(existGame)) {
            newGame.setId(existGame.getId());
            adCoreBqf.update(lauUnitGame).updateBean(newGame);
        } else {
            adCoreBqf.insert(lauUnitGame).insertBean(newGame);
        }
        // 保存游戏详情
        GameDto game = gameCenterService.getGameDtoByIdPlatform(gameInfo.getGameBaseId(), gameInfo.getPlatformType());
        LauBilibiliGamePo existGamePo = adBqf.selectFrom(lauBilibiliGame)
            .where(lauBilibiliGame.gameBaseId.eq(gameInfo.getGameBaseId()))
            .where(lauBilibiliGame.platformType.eq(gameInfo.getPlatformType()))
            .fetchFirst();
        LauBilibiliGamePo newGamePo = IGameConverter.MAPPER.gameDto2GamePo(gameInfo.getPlatformType(), game);
        if (Objects.nonNull(existGamePo)) {
            newGamePo.setId(existGamePo.getId());
            adBqf.update(lauBilibiliGame).updateBean(newGamePo);
        }else {
            adBqf.insert(lauBilibiliGame).insertBean(newGamePo);
        }
    }

    /**
     * 处理 游戏卡单元 稿件定向
     *
     * @param unitId           单元id
     * @param platformType 游戏平台 1-安卓 2-ios
     * @param avids            avid 列表
     * <AUTHOR>
     * @date 2022-01-30 4:26 下午
     */
    private void dealGameCardUnitArchiveTargets(@NonNull Integer unitId,
        @NonNull Integer platformType,
        List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            adCoreBqf.update(lauUnitTargetArchive)
                .set(lauUnitTargetArchive.isDeleted, IsDeleted.DELETED.getCode())
                .where(lauUnitTargetArchive.unitId.eq(unitId)).execute();
        }
        Set<Long> newAvids = new HashSet<>(avids);
        // 当前 单元 平台 对应的 有效 稿件定向
        Map<Long, Integer> avidMap = adCoreBqf
            .select(lauUnitTargetArchive.avid, lauUnitTargetArchive.id)
            .from(lauUnitTargetArchive)
            .where(lauUnitTargetArchive.unitId.eq(unitId))
            .where(lauUnitTargetArchive.platformType.eq(platformType))
            .where(lauUnitTargetArchive.isDeleted.eq(IsDeleted.VALID.getCode()))
            .fetch(LauUnitTargetArchivePo.class)
            .stream()
            .collect(
                Collectors.toMap(LauUnitTargetArchivePo::getAvid, LauUnitTargetArchivePo::getId));
        Set<Long> existAvids = new HashSet<>(avidMap.keySet());
        Set<Long> needInsertAvids = Sets.difference(newAvids, existAvids);
        Set<Long> needDeleteAvids = Sets.difference(existAvids, newAvids);

        adCoreBqf.insert(lauUnitTargetArchive).insertBeans(needInsertAvids.stream().map(
            avid -> {
                LauUnitTargetArchivePo po = new LauUnitTargetArchivePo();
                po.setUnitId(unitId);
                po.setPlatformType(platformType);
                po.setAvid(avid);
                return po;
            }).collect(Collectors.toList()));

        adCoreBqf.update(lauUnitTargetArchive).updateBeans(needDeleteAvids.stream().map(
            avid -> {
                Integer id = avidMap.get(avid);
                LauUnitTargetArchivePo po = new LauUnitTargetArchivePo();
                po.setId(id);
                po.setIsDeleted(IsDeleted.DELETED.getCode());
                return po;
            }
        ).collect(Collectors.toList()));
    }

    /**
     * 处理 游戏卡单元 标签定向
     *
     * @param unitId           单元id
     * @param platformType 游戏平台 1-安卓 2-ios
     * @param tagIds           标签id 列表
     * <AUTHOR>
     * @date 2022-01-30 4:26 下午
     */
    private void dealGameCardUnitTagTargets(@NonNull Integer unitId,
        @NonNull Integer platformType,
        List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            adCoreBqf.update(lauUnitTargetTag)
                .set(lauUnitTargetTag.isDeleted, IsDeleted.DELETED.getCode())
                .where(lauUnitTargetTag.unitId.eq(unitId)).execute();
        }
        Set<Long> newTagIds = new HashSet<>(tagIds);
        Map<Long, Integer> tagIdMap = adCoreBqf
            .select(lauUnitTargetTag.tagId, lauUnitTargetTag.id)
            .from(lauUnitTargetTag)
            .where(lauUnitTargetTag.unitId.eq(unitId))
            .where(lauUnitTargetTag.platformType.eq(platformType))
            .where(lauUnitTargetTag.isDeleted.eq(IsDeleted.VALID.getCode()))
            .fetch(LauUnitTargetTagPo.class)
            .stream()
            .collect(Collectors.toMap(LauUnitTargetTagPo::getTagId, LauUnitTargetTagPo::getId));

        Set<Long> existTagIds = new HashSet<>(tagIdMap.keySet());
        Set<Long> needInsertTagIds = Sets.difference(newTagIds, existTagIds);
        Set<Long> needDeleteTagIds = Sets.difference(existTagIds, newTagIds);

        adCoreBqf.insert(lauUnitTargetTag).insertBeans(needInsertTagIds.stream().map(
            tagId -> {
                LauUnitTargetTagPo po = new LauUnitTargetTagPo();
                po.setUnitId(unitId);
                po.setPlatformType(platformType);
                po.setTagId(tagId);
                return po;
            }).collect(Collectors.toList()));

        adCoreBqf.update(lauUnitTargetTag).updateBeans(needDeleteTagIds.stream().map(
            tagId -> {
                Integer id = tagIdMap.get(tagId);
                LauUnitTargetTagPo po = new LauUnitTargetTagPo();
                po.setId(id);
                po.setIsDeleted(IsDeleted.DELETED.getCode());
                return po;
            }
        ).collect(Collectors.toList()));
    }
}
