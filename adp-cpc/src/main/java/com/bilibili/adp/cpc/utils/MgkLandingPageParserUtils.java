package com.bilibili.adp.cpc.utils;


import com.bilibili.adp.cpc.biz.services.creative.dto.AdpCpcMgkLandingPageBo;
import com.bilibili.mgk.platform.common.LandingPageTypeEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/3/17 19:15
 */
public class MgkLandingPageParserUtils {
    public static final Map<String, String> env2PrefixMap = new HashMap();
    public static final String MINI_APP_SUBSTR = "miniapp.bilibili.com";
    public static final String NEW_H5_SUBSTR = "gaoneng.bilibili.com/tetris/page";
    public static final String LEGACY_H5_SUBSTR = "cm.bilibili.com/mgk/page";
    public static final String MGK_TEST_H5_SUBSTR = "cm-mng.bilibili.co/tetris/page";
    private static final Pattern rawPageIdPattern;
    private static final Pattern pageIdSnakeQueryPattern;
    private static final Pattern pageIdCamelQueryPattern;
    private static final Pattern pageIdPathPattern;

    public MgkLandingPageParserUtils() {
    }

    public static Long getRawPageId(String url) {
        Matcher matcher = rawPageIdPattern.matcher(url.trim());
        return matcher.matches() ? Long.valueOf(matcher.group()) : null;
    }

    public static boolean isSchemaKnown(String url) {
        return isNative(url) || isH5(url) || isMiniApp(url);
    }

    public static AdpCpcMgkLandingPageBo parse(String url) {
        long pageId;
        int pageType;
        boolean useNewH5Preview;
        if (isLegacyH5(url)) {
            Matcher matcher = pageIdPathPattern.matcher(url);
            if (!matcher.find()) {
                throw new IllegalArgumentException("解析旧版H5建站链接失败");
            }

            pageId = Long.parseLong(matcher.group(1));
            pageType = LandingPageTypeEnum.H5.getCode();
            useNewH5Preview = false;
        } else if (isMiniApp(url)) {
            Matcher matcher = pageIdSnakeQueryPattern.matcher(url);
            if (!matcher.find()) {
                throw new IllegalArgumentException("解析小程序建站链接失败");
            }

            pageId = Long.parseLong(matcher.group(1));
            pageType = LandingPageTypeEnum.APPLETS.getCode();
            useNewH5Preview = true;
        } else if (isNewH5(url)) {
            Matcher matcher = pageIdCamelQueryPattern.matcher(url);
            if (!matcher.find()) {
                throw new IllegalArgumentException("解析新版H5建站链接失败");
            }

            pageId = Long.parseLong(matcher.group(1));
            pageType = LandingPageTypeEnum.H5.getCode();
            useNewH5Preview = true;
        } else if (isNative(url)) {
            Matcher matcher = pageIdSnakeQueryPattern.matcher(url);
            if (!matcher.find()) {
                throw new IllegalArgumentException("解析原生建站链接失败");
            }

            pageId = Long.parseLong(matcher.group(1));
            pageType = LandingPageTypeEnum.NATIVE.getCode();
            useNewH5Preview = false;
        } else {
            if (!isTestNewH5(url)) {
                throw new IllegalArgumentException("未知的建站链接schema: " + url);
            }

            Matcher matcher = pageIdCamelQueryPattern.matcher(url);
            if (!matcher.find()) {
                throw new IllegalArgumentException("解析新版H5建站链接失败");
            }

            pageId = Long.parseLong(matcher.group(1));
            pageType = LandingPageTypeEnum.H5.getCode();
            useNewH5Preview = true;
        }

        return AdpCpcMgkLandingPageBo.builder()
                .pageId(pageId)
                .pageType(pageType)
                .pageUrl(url)
                .useNewH5Preview(useNewH5Preview)
                .build();
    }

    private static String genLegacyH5Preview(Long pageId) {
        return "https://cm.bilibili.com/mgk/page/" + pageId;
    }

    private static String genNewH5Preview(Long pageId, String env) {
        String prefix = (String)env2PrefixMap.get(env);
        return "https://" + prefix + "gaoneng.bilibili.com/tetris/page/?pageId=" + pageId;
    }

    private static String genNewConsultH5Preview(Long pageId, String env) {
        String prefix = (String)env2PrefixMap.get(env);
        return "https://" + prefix + "gaoneng.bilibili.com/tetris/consult/?pageId=" + pageId;
    }


    public static String getPreview(AdpCpcMgkLandingPageBo bo, String env) {
        if (bo.getPageType() == 6){
            return genNewConsultH5Preview(bo.getPageId(), env);
        }
        return bo.isUseNewH5Preview() ? genNewH5Preview(bo.getPageId(), env) : genLegacyH5Preview(bo.getPageId());
    }

    public static boolean isH5(String url) {
        return isNewH5(url) || isLegacyH5(url);
    }

    public static boolean isNewH5(String url) {
        return url.contains("gaoneng.bilibili.com/tetris/page") || url.contains("gaoneng.bilibili.com/tetris/consult");
    }

    public static boolean isLegacyH5(String url) {
        return url.contains("cm.bilibili.com/mgk/page");
    }

    public static boolean isMiniApp(String url) {
        return url.contains("miniapp.bilibili.com");
    }

    public static boolean isNative(String url) {
        return url.contains("bilibili://ad/page/imax");
    }

    public static boolean isTestNewH5(String url) {
        return url.contains("gaoneng.bilibili.com/tetris/page") || url.contains("gaoneng.bilibili.com/tetris/consult");
    }

    static {
        env2PrefixMap.put("prod", "");
        env2PrefixMap.put("pre", "pre-");
        env2PrefixMap.put("uat", "uat-");
        rawPageIdPattern = Pattern.compile("\\d+");
        pageIdSnakeQueryPattern = Pattern.compile("page_id=(\\d+)");
        pageIdCamelQueryPattern = Pattern.compile("pageId=(\\d+)");
        pageIdPathPattern = Pattern.compile("page/(\\d+)");
    }
}
