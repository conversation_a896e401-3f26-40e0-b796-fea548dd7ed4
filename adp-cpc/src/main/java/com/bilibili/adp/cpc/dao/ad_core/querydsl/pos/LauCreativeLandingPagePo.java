package com.bilibili.adp.cpc.dao.ad_core.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauCreativeLandingPagePo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauCreativeLandingPagePo {

    private Integer accountId;

    private Integer campaignId;

    private Long containerPageId;

    private String containerSecondaryUrl;

    private String containerUrl;

    private Integer creativeId;

    private java.sql.Timestamp ctime;

    private Long id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Integer unitId;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public Long getContainerPageId() {
        return containerPageId;
    }

    public void setContainerPageId(Long containerPageId) {
        this.containerPageId = containerPageId;
    }

    public String getContainerSecondaryUrl() {
        return containerSecondaryUrl;
    }

    public void setContainerSecondaryUrl(String containerSecondaryUrl) {
        this.containerSecondaryUrl = containerSecondaryUrl;
    }

    public String getContainerUrl() {
        return containerUrl;
    }

    public void setContainerUrl(String containerUrl) {
        this.containerUrl = containerUrl;
    }

    public Integer getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", campaignId = " + campaignId + ", containerPageId = " + containerPageId + ", containerSecondaryUrl = " + containerSecondaryUrl + ", containerUrl = " + containerUrl + ", creativeId = " + creativeId + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", unitId = " + unitId;
    }

}

