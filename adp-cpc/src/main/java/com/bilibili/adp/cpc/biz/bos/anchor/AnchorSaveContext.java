package com.bilibili.adp.cpc.biz.bos.anchor;

import com.bilibili.adp.common.annotation.DatabaseColumnEnum;
import com.bilibili.adp.common.annotation.DatabaseColumnName;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.cpc.biz.services.archive.bos.BatchCreateAdThreeElementsBo;
import com.bilibili.adp.cpc.biz.services.archive.bos.CreateAdThreeElementsBo;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnchorSaveContext {

    private AnchorSaveBo anchorSaveBo;
    private Operator operator;
    private boolean isUpdate;
    private AccountBaseDto accountBaseDto;
    private List<Integer> accountLabelIds;

    // 修改时才有值
    private AnchorSaveBo oldAnchorSaveBo;
    // 修改时有值
    private CreateAdThreeElementsBo createAdThreeElementsBo;
    // 新建时有值，批量的
    private BatchCreateAdThreeElementsBo batchCreateAdThreeElementsBo;

    public static AnchorSaveContext init(AnchorSaveBo anchorSaveBo, Operator operator, boolean isUpdate) {
        return AnchorSaveContext.builder()
                .anchorSaveBo(anchorSaveBo)
                .operator(operator)
                .isUpdate(isUpdate)
                .accountLabelIds(new ArrayList<>())
                .build();
    }
}
