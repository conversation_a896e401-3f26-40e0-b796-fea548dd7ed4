package com.bilibili.adp.cpc.biz.services.ad_product.strategy;

import com.bilibili.adp.account.dto.AccountAllInfoDto;
import com.bilibili.adp.account.dto.AccountDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.ad_product.AdProductHelper;
import com.bilibili.adp.cpc.biz.services.ad_product.AdProductUtils;
import com.bilibili.adp.cpc.biz.services.ad_product.dto.ImportAdProductNovelDto;
import com.bilibili.adp.cpc.biz.services.ad_product.dto.ImportAdProductResultDto;
import com.bilibili.adp.cpc.biz.services.log.DbTable;
import com.bilibili.adp.cpc.biz.services.log.ILogOperateNewService;
import com.bilibili.adp.cpc.enums.ad_product.AdProductCategoryLevelEnum;
import com.bilibili.adp.cpc.enums.ad_product.AdProductItemTypeEnum;
import com.bilibili.adp.cpc.po.ad.AdProductCategoryPo;
import com.bilibili.adp.cpc.po.ad.AdProductItemPo;
import com.bilibili.adp.cpc.repo.AdProductRepo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.biz.services.ad_product.AdProductUtils.*;

/**
 * 投放产品-小说产品导入实现类
 *
 * <AUTHOR>
 * @date 2023/11/4 17:20
 */
@Slf4j
@Component
public class AdProductImportForNovel extends AdProductImportBase<ImportAdProductNovelDto> {

    @Resource
    private IQueryAccountService queryAccountService;
    @Autowired
    private ILogOperateNewService logOperateService;
    @Autowired
    private AdProductHelper adProductHelper;
    
    public AdProductImportForNovel(AdProductRepo adProductRepo, RedissonClient redissonClient) {
        super(adProductRepo, redissonClient);
    }

    @Override
    protected void validateImportData(List<ImportAdProductNovelDto> importDtos) {
    }

    @Override
    protected void importDataBizProc(List<ImportAdProductNovelDto> importDtos) {

        Integer accountId = importDtos.stream().map(ImportAdProductNovelDto::getAccountId)
                .findFirst().orElse(0);
        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(accountId);
        AccountDto accountDto = accountAllInfoDto.getAccountDto();
        // 行业产品中心小说库权限: 一级行业=文化娱乐&二级行业=小说
        Assert.isTrue(Integer.valueOf(768).equals(accountDto.getCommerceCategoryFirstId()), "账号:" + accountDto.getAccountId() + "行业不符合");


//        Map<String, String> categoryNameMap = queryCategoryLevelMap();
        List<AdProductCategoryPo> firstCategoryPos =
                adProductRepo.getAllProductCategoryListByLevel(AdProductCategoryLevelEnum.ONE.getCode());
        Map<String, AdProductCategoryPo> firstCategoryNameMap =
                firstCategoryPos.stream().collect(Collectors.toMap(AdProductCategoryPo::getName, Function.identity(), adProductHelper::compareCategoryRule));

        List<AdProductCategoryPo> secondCategoryPos =
                adProductRepo.getAllProductCategoryListByLevel(AdProductCategoryLevelEnum.TWO.getCode());
        Map<String, AdProductCategoryPo> secondCategoryNameMap = secondCategoryPos.stream().collect(Collectors.toMap(AdProductCategoryPo::getName, Function.identity(), adProductHelper::compareCategoryRule));

        List<AdProductCategoryPo> thirdCategoryPos =
                adProductRepo.getAllProductCategoryListByLevel(AdProductCategoryLevelEnum.THREE.getCode());
        Map<String, AdProductCategoryPo> thirdCategoryNameMap = thirdCategoryPos.stream().collect(Collectors.toMap(AdProductCategoryPo::getName, Function.identity(), adProductHelper::compareCategoryRule));


        List<AdProductItemPo> allItems = adProductRepo.getAllItems();
        Map<String, Long> productItemMap =
                allItems.stream().collect(Collectors.toMap(t -> generateMapKey(t.getContent(), t.getType()), AdProductItemPo::getId, (t1, t2) -> t1));
        // 处理数据
        for (ImportAdProductNovelDto novelDto : importDtos) {
            int cur = novelDto.getLineNum() + 3;
            Long channelItemId = productItemMap.getOrDefault(generateMapKey(AdProductUtils.replaceSpecialChar(novelDto.getChannelName()),
                    AdProductItemTypeEnum.CHANNEL.getCode()), 0L);
            novelDto.setChannel(convertId2ArrStr(channelItemId));
            novelDto.setTheme(convertName2IdArrStrForLong(productItemMap, AdProductUtils
                            .replaceSpecialChar(novelDto.getThemeName()), AdProductItemTypeEnum.THEME.getCode(),
                    itemIdList -> Assert.isTrue(itemIdList.size() <= 3, "第" + cur + "行题材最多支持选择3个")));
            Long wordNumId = productItemMap.getOrDefault(generateMapKey(AdProductUtils.replaceSpecialChar(novelDto.getWordsNumName()),
                    AdProductItemTypeEnum.WORDS_NUM_V2.getCode()), 0L);
            novelDto.setWordsNum(convertId2ArrStr(wordNumId));
            Long updateStatusId = productItemMap.getOrDefault(generateMapKey(AdProductUtils.replaceSpecialChar(novelDto.getUpdateStatusName()),
                    AdProductItemTypeEnum.UPDATE_STATUS.getCode()), 0L);
            novelDto.setUpdateStatus(convertId2ArrStr(updateStatusId));
            novelDto.setRealizationMethod(convertName2IdArrStrForLong(productItemMap, AdProductUtils
                    .replaceSpecialChar(novelDto.getRealizationMethodName()), AdProductItemTypeEnum.REALIZATION_METHOD.getCode()));


            // 一级，二级，三级是否都存在
            AdProductCategoryPo firstCategoryPo = firstCategoryNameMap.getOrDefault(novelDto.getFirstCategoryName(),
                    AdProductCategoryPo.builder().code(0L).build());
            novelDto.setFirstCategoryCode(firstCategoryPo.getCode());
            Assert.isTrue(Utils.isPositive(novelDto.getFirstCategoryCode()), "第" + cur + "行" + "一级行业不存在");

            AdProductCategoryPo secondCategoryPo = secondCategoryNameMap.getOrDefault(novelDto.getSecondCategoryName(), AdProductCategoryPo.builder().code(0L).pCode(0L).build());
            novelDto.setSecondCategoryCode(secondCategoryPo.getCode());
            Assert.isTrue(Utils.isPositive(novelDto.getSecondCategoryCode()), "第" + cur + "行" + "二级行业不存在");

            AdProductCategoryPo thirdCategoryPo = thirdCategoryNameMap.getOrDefault(novelDto.getThirdCategoryName(), AdProductCategoryPo.builder().code(0L).pCode(0L).build());
            novelDto.setThirdCategoryCode(thirdCategoryPo.getCode());
            Assert.isTrue(Utils.isPositive(novelDto.getThirdCategoryCode()), "第" + cur + "行" + "三级行业不存在");
            
            
            Assert.isTrue(thirdCategoryPo.getPCode().equals(novelDto.getSecondCategoryCode()),
                    "第" + cur + "行" + "二级行业没有该三级行业");
            Assert.isTrue(secondCategoryPo.getPCode().equals(novelDto.getFirstCategoryCode()),
                    "第" + cur + "行" + "一级行业没有该二级行业");


            Assert.isTrue(StringUtils.hasText(novelDto.getChannel()), "第" + cur + "行"
                    + "频道不存在");
            Assert.isTrue(StringUtils.hasText(novelDto.getTheme()), "第" + cur + "行"
                    + "主题不存在");
            Assert.isTrue(StringUtils.hasText(novelDto.getRealizationMethod()), "第" + cur + "行"
                    + "变现方式不存在");
            Assert.isTrue(StringUtils.hasText(novelDto.getWordsNum()), "第" + cur + "行"
                    + "总字数不存在");
            Assert.isTrue(StringUtils.hasText(novelDto.getUpdateStatus()), "第" + cur + "行"
                    + "更新状态不存在");
        }
    }


    @Override
    protected List<ImportAdProductResultDto> convertImportDtos2ResultDtos(List<ImportAdProductNovelDto> importDtos) {
        return importDtos.stream().map(t -> ImportAdProductResultDto.builder()
                .name(t.getName())
                .lineNum(t.getLineNum())
                .success(t.getSuccess())
                .msg(t.getMsg())
                .build()).collect(Collectors.toList());
    }

    @Override
    protected void batchInsertProduct(List<ImportAdProductNovelDto> importDtos, Operator operator) {
        List<Long> idList = adProductRepo.insertImportNovelV2(importDtos);
        List<Integer> idIntList = idList.stream().map(Long::intValue).collect(Collectors.toList());
        logOperateService.addBatchInsertLog(DbTable.AD_PRODUCT_TOTAL_INFO, operator, importDtos, idIntList, true);
    }
}
