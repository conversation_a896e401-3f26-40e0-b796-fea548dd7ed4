package com.bilibili.adp.cpc.biz.services.target_package;

import com.bilibili.adp.cpc.biz.services.target_package.api.IResProfessionInterestService;
import com.bilibili.adp.cpc.dao.ad.ResProfessionInterestCrowdsDao;
import com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo;
import com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPoExample;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/23
 * @description 行业兴趣人群（新）持久层实现类
 */
@Service
@Slf4j
public class ResProfessionInterestServiceImpl implements IResProfessionInterestService {


    @Resource
    private ResProfessionInterestCrowdsDao professionInterestDao;
    @Override
    public List<ResProfessionInterestCrowdsPo> allValidProfessionInterest() {
        ResProfessionInterestCrowdsPoExample example = new ResProfessionInterestCrowdsPoExample();
        example.createCriteria().andIsDeleteEqualTo(0);//0为有效，1为无效

        List<ResProfessionInterestCrowdsPo> pos = professionInterestDao.selectByExample(example);
        return CollectionUtils.isEmpty(pos) ? Collections.emptyList() : pos;
    }

    @Override
    public Integer batchInsertProfessionInterestPo(List<ResProfessionInterestCrowdsPo> pos) {

        return professionInterestDao.insertBatch(pos);
    }
}
