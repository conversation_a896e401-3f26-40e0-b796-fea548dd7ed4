package com.bilibili.adp.cpc.utils;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.HttpUtils;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.adp.util.entity.ApkInfo;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import net.dongliu.apk.parser.ApkFile;
import net.dongliu.apk.parser.bean.Icon;
import net.dongliu.apk.parser.bean.IconFace;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;


@Slf4j
public class AdpApkWithIconUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdpApkWithIconUtil.class);

    public AdpApkWithIconUtil() {
    }

    private static InputStream extractIconFileFromApk(String apkpath, String fileName) {
        try {
            ZipFile zFile = new ZipFile(apkpath);
            ZipEntry entry = zFile.getEntry(fileName);
            entry.getComment();
            entry.getCompressedSize();
            entry.getCrc();
            entry.isDirectory();
            entry.getSize();
            entry.getMethod();
            return zFile.getInputStream(entry);
        } catch (IOException var4) {
            LOGGER.error("extractIconFileFromApk failed", var4);
            return null;
        }
    }

    public static String parseApkIconPath(String apkPath){
        String iconPath = "";
        ApkFile apkFile = ApkFileParseUtil.getApkFile(new File(apkPath));
        if (Objects.isNull(apkFile)) {
            return iconPath;
        }
        try {
            List<IconFace> allIcons = apkFile.getAllIcons();
            if (CollectionUtils.isEmpty(allIcons)) {
                return iconPath;
            }
            Icon icon = null;
            for (IconFace iconFace: allIcons) {
               if (!(iconFace instanceof Icon)) {
                   // AdaptiveIcon 这种格式的icon，分为 foreground、background 两个icon。不做考虑
                   continue;
               }
                Icon tmp = (Icon)iconFace;
                if (icon == null || icon.getDensity() < tmp.getDensity()) {
                    // 获取最清晰的
                    icon = tmp;
                }
            }
            iconPath = Optional.ofNullable(icon).map(x -> x.getPath()).orElse("");
            log.info("parseApkIconPath iconPath={}, appName={}", icon, apkFile.getApkMeta().getName());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        try {
            apkFile.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return iconPath;
    }

    public static ApkInfo getApkInfoAndroid(String apkpath) {
        ApkInfo apkInfo = null;
        try {
            String icon320 = parseApkIconPath(apkpath);
            apkInfo = AdpApkUtil.getInstance().getApkInfo(apkpath);
            Map<String, String> applicationIcons = apkInfo.getApplicationIcons();
            if (StringUtils.isEmpty(icon320) && applicationIcons != null) {
                icon320 = applicationIcons.get("application-icon-320");
            }

            InputStream iconStream = extractIconFileFromApk(apkpath, icon320);
            apkInfo.setIconInputStream(iconStream);
            apkInfo.setIconImageSuffix(getIconImageSuffix(icon320));
        } catch (Exception var5) {
            LOGGER.error("getApkInfoAndroidWithIconInputStream failed", var5);
        }

        return apkInfo;
    }

    public static ApkInfo getApkInfoAndroid(String apkpath, String outputPath) {
        String icon320 = "";
        InputStream is = null;
        BufferedOutputStream bos = null;
        BufferedInputStream bis = null;
        ApkInfo apkInfo = null;

        try {
            apkInfo = AdpApkUtil.getInstance().getApkInfo(apkpath);
            Map<String, String> applicationIcons = apkInfo.getApplicationIcons();
            if (applicationIcons != null) {
                icon320 = (String) applicationIcons.get("application-icon-320");
            }

            is = extractIconFileFromApk(apkpath, icon320);
            File file = new File(outputPath);
            bos = new BufferedOutputStream(new FileOutputStream(file), 1024);
            byte[] b = new byte[1024];
            bis = new BufferedInputStream(is, 1024);

            while (bis.read(b) != -1) {
                bos.write(b);
            }

            bos.flush();
        } catch (Exception var26) {
            var26.printStackTrace();
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(bis);
            IOUtils.closeQuietly(bos);
        }

        return apkInfo;
    }

    public static ApkInfo getApkInfoIOS(String apkUrl) {
        ApkInfo apkInfo = null;
        String iOSIcon = "";

        try {
            apkInfo = AdpApkUtil.getInstance().getIOSInfo(apkUrl);
            if (apkInfo != null) {
                Map<String, String> applicationIcons = apkInfo.getApplicationIcons();

                // 获取转icon的文件流，后续上传至bfs上
                if (applicationIcons != null) {
                    iOSIcon = (String) applicationIcons.get("application-icon-320");
                }

                apkInfo.setIconInputStream(AdpHttpHelper.getInputStreamV2(iOSIcon));
                apkInfo.setIconImageSuffix(getIconImageSuffix(iOSIcon));

                // 获取screenshot的文件流
                List<String> iphoneScreenshotUrls = apkInfo.getIphoneScreenshotUrls();
                Map<String, InputStream> iphoneScreenshotStreamMap = new HashMap<>(iphoneScreenshotUrls.size());
                iphoneScreenshotUrls.forEach(url -> {
                    try {
                        iphoneScreenshotStreamMap.put(url, AdpHttpHelper.getInputStreamV2(url));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
                apkInfo.setIphoneScreenshotStreamMap(iphoneScreenshotStreamMap);
            }
        } catch (Exception var4) {
            LOGGER.error("getApkInfoIOS fail [{}]", Throwables.getStackTraceAsString(var4));
        }

        return apkInfo;
    }

    private static String getIconImageSuffix(String iconUrl) {
        int i = iconUrl.lastIndexOf(".");
        return i > 0 ? iconUrl.substring(i) : "";
    }

    public static void main(String[] args) throws Exception {
        String icon = HttpUtils.TEMP_IMAGE_FILE_DIR + System.currentTimeMillis() + ".png";
        String apk = HttpUtils.TEMP_IMAGE_FILE_DIR + System.currentTimeMillis() + ".apk";
        new File(HttpUtils.TEMP_IMAGE_FILE_DIR).mkdirs();
        IOUtils.copy(OkHttpUtils.get("http://dl.hdslb.com/cm/online/17501547040886229-1600138876.apk")
                .callForStream(), new FileOutputStream(apk));
        ApkInfo apkInfo = getApkInfoAndroid(apk, icon);
        System.out.println(JSON.toJSON(apkInfo));
    }
}
