package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauPreviewMidPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauPreviewMidPo {

    private Long accountId;

    private Long creativeId;

    private java.sql.Timestamp ctime;

    private Long id;

    private Long mid;

    private java.sql.Timestamp mtime;

    private Long previewId;

    private Integer previewStatus;

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(Long creativeId) {
        this.creativeId = creativeId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMid() {
        return mid;
    }

    public void setMid(Long mid) {
        this.mid = mid;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Long getPreviewId() {
        return previewId;
    }

    public void setPreviewId(Long previewId) {
        this.previewId = previewId;
    }

    public Integer getPreviewStatus() {
        return previewStatus;
    }

    public void setPreviewStatus(Integer previewStatus) {
        this.previewStatus = previewStatus;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", creativeId = " + creativeId + ", ctime = " + ctime + ", id = " + id + ", mid = " + mid + ", mtime = " + mtime + ", previewId = " + previewId + ", previewStatus = " + previewStatus;
    }

}

