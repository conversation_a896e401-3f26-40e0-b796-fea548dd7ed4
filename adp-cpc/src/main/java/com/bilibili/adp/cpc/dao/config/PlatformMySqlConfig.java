package com.bilibili.adp.cpc.dao.config;

import com.bilibili.adp.launch.biz.config.mysql.AdPlatformMysqlConfig;

public class PlatformMySqlConfig {
    public static final String AD_CORE_BQF = AdPlatformMysqlConfig.AD_CORE_DYNAMIC_BQF;
    public static final String AD_CORE_SF = AdPlatformMysqlConfig.AD_CORE_DYNAMIC_SF;
    public static final String AD_CORE_TM = AdPlatformMysqlConfig.AD_CORE_DYNAMIC_TM;

    public static final String AD_BQF = AdPlatformMysqlConfig.AD_DYNAMIC_BQF;
    public static final String AD_SF = AdPlatformMysqlConfig.AD_DYNAMIC_SF;
    public static final String AD_TM = AdPlatformMysqlConfig.AD_DYNAMIC_TM;
}
