package com.bilibili.adp.cpc.biz.services.task.condition;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.task.dtos.TaskRuleConditionQueryDto;
import com.bilibili.adp.cpc.biz.services.task.dtos.TaskRuleConditionResultDto;
import com.bilibili.adp.cpc.biz.services.task.dtos.TaskRuleFormatParamDto;
import com.bilibili.adp.cpc.biz.services.task.other.CostQuerier;
import com.bilibili.adp.cpc.enums.task.TaskRuleConditionEnum;
import com.bilibili.adp.cpc.enums.task.TaskRuleConditionExpressionEnum;
import com.bilibili.adp.cpc.repo.OuterLauUnitRepo;
import com.bilibili.report.platform.api.dto.StatUnitDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * 任务规则指标查询器（原生单元消耗【新三连】）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskRuleConditionQuerierOfNativeUnitCost implements ITaskRuleConditionQuerier {

    private final CostQuerier costQuerier;
    private final OuterLauUnitRepo outerLauUnitRepo;

    @Override
    public TaskRuleConditionEnum getTaskRuleConditionEnum() {
        return TaskRuleConditionEnum.NATIVE_UNIT_COST;
    }


    @Override
    public TaskRuleConditionResultDto queryConditionResult(TaskRuleConditionQueryDto queryDto) {

        Assert.isTrue(CollectionUtils.isNotEmpty(queryDto.getAccountIds()), "account ids 不能为空");
        Assert.isTrue(queryDto.getBeginTime() != null, "begin time 不能为空");
        Assert.isTrue(queryDto.getEndTime() != null, "end time 不能为空");

        // 全量方式查询
        // 开始时间天的0点
        Timestamp startTime = Utils.getBeginOfDay(queryDto.getBeginTime());
        // 结束时间为开始时间所在天的23点
        Timestamp endTime = Utils.getEndOfDay(queryDto.getEndTime());

        // 获取账户最近30天的原生单元
        List<Integer> nativeUnitIds = outerLauUnitRepo.queryNativeUnitIdsOfAccount(queryDto.getAccountIds());

        // 查询原生单元的消耗（单位元）
        List<StatUnitDto> statUnitDtos = costQuerier.queryUnitCost(startTime, endTime, nativeUnitIds);
        log.info("queryUnitCost, accountIds={},unit.size={}", JSON.toJSONString(queryDto.getAccountIds()), nativeUnitIds.size());
        // 求和
        BigDecimal cost = statUnitDtos.stream().map(statUnitDto -> statUnitDto.getCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        Boolean isCompleted = cost.longValue() >= Long.parseLong(queryDto.getValue());
        return TaskRuleConditionResultDto.builder()
                .completeCount(cost.longValue())
                .isCompleted(isCompleted)
                .build();
    }


    @Override
    public String formatRuleContent(TaskRuleFormatParamDto taskRuleFormatParamDto) {
        // 示例: 原生单元消耗 >= 1000元
        TaskRuleConditionEnum taskRuleConditionEnum = this.getTaskRuleConditionEnum();
        Integer expressionCode = taskRuleConditionEnum.getCandidateExpressionList().get(0);
        String expressionDesc = TaskRuleConditionExpressionEnum.getByCode(expressionCode).getDesc();

        return taskRuleConditionEnum.getName() + " " + expressionDesc + " " + taskRuleFormatParamDto.getNeedCompletedValue() + " 元";
    }

    @Override
    public String formatRuleProcessingContent(TaskRuleFormatParamDto taskRuleFormatParamDto) {
        return "新增原生单元消耗" + taskRuleFormatParamDto.getRealCompletedValue();
    }
}
