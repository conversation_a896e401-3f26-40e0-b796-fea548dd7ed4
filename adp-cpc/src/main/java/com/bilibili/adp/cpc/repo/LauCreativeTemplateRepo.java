package com.bilibili.adp.cpc.repo;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.adp.cpc.dao.ad_core.mybatis.LauCreativeTemplateDao;
import com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePo;
import com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePoExample;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class LauCreativeTemplateRepo {

    @Autowired
    private LauCreativeTemplateDao lauCreativeTemplateDao;

    public List<LauCreativeTemplatePo> queryListByUnitId (Integer unitId) {
        if (!Utils.isPositive(unitId)) {
            return Collections.EMPTY_LIST;
        }

        LauCreativeTemplatePoExample example = new LauCreativeTemplatePoExample();
        example.or().andUnitIdEqualTo(unitId).andIsDeletedEqualTo(IsDeleted.VALID);
        List<LauCreativeTemplatePo> templatePos = lauCreativeTemplateDao.selectByExample(example);
        return templatePos;
    }

    public List<LauCreativeTemplatePo> queryListByCreativeIds(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.EMPTY_LIST;
        }

        LauCreativeTemplatePoExample example = new LauCreativeTemplatePoExample();
        example.or().andCreativeIdIn(creativeIds.stream().map(t -> new Long(t)).collect(Collectors.toList())).andIsDeletedEqualTo(IsDeleted.VALID);
        List<LauCreativeTemplatePo> templatePos = lauCreativeTemplateDao.selectByExample(example);
        return templatePos;
    }

    public List<LauCreativeTemplatePo> queryListByCreativeIds(List<Integer> creativeIds, List<Integer> templateIds, List<Integer> slotGroupIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.EMPTY_LIST;
        }

        LauCreativeTemplatePoExample example = new LauCreativeTemplatePoExample();
        LauCreativeTemplatePoExample.Criteria criteria = example.or().andCreativeIdIn(creativeIds.stream().map(t -> new Long(t)).collect(Collectors.toList())).andIsDeletedEqualTo(IsDeleted.VALID);
        if (!CollectionUtils.isEmpty(templateIds)) {
            criteria.andTemplateIdIn(templateIds);
        }
        if (!CollectionUtils.isEmpty(slotGroupIds)) {
            criteria.andSlotGroupIdIn(slotGroupIds);
        }
        List<LauCreativeTemplatePo> templatePos = lauCreativeTemplateDao.selectByExample(example);
        return templatePos;
    }

    public List<LauCreativeTemplatePo> queryListByCreativeId(Integer creativeId) {
        Map<Integer, List<LauCreativeTemplatePo>> map = this.queryMapByCreativeIds(Arrays.asList(creativeId));
        return map.get(creativeId);
    }

    public Map<Integer, List<LauCreativeTemplatePo>> queryMapByUnitId (Integer unitId) {
        List<LauCreativeTemplatePo> templatePos = this.queryListByUnitId(unitId);
        return templatePos.stream().collect(Collectors.groupingBy(t -> t.getCreativeId().intValue()));
    }

    public Map<Integer, List<LauCreativeTemplatePo>> queryMapByCreativeIds(List<Integer> creativeIds) {
        List<LauCreativeTemplatePo> templatePos = this.queryListByCreativeIds(creativeIds);
        return templatePos.stream().collect(Collectors.groupingBy(t -> t.getCreativeId().intValue()));
    }

    public void deleteCreativeTemplateById(Long id) {
        log.info("deleteCreativeTemplateById, id:{}", id);
        lauCreativeTemplateDao.deleteByPrimaryKey(id);
    }

    public int insertUpdateSelective(LauCreativeTemplatePo creativeTemplatePo) {

        return lauCreativeTemplateDao.insertUpdateSelective(creativeTemplatePo);
    }

    public int batchUpdate(List<LauCreativeTemplatePo> creativeTemplatePos) {

        if (CollectionUtils.isEmpty(creativeTemplatePos)) {
            return 0;
        }

        List<List<LauCreativeTemplatePo>> partitions = Lists.partition(creativeTemplatePos, 50);
        Integer count = 0;
        for (List<LauCreativeTemplatePo> partition : partitions) {
            count += lauCreativeTemplateDao.insertUpdateBatch(partition);
        }

        return count;
    }
}
