package com.bilibili.adp.cpc.grpc.mapper;

import com.bapis.ad.adp.archive.*;
import com.bilibili.adp.cpc.biz.bos.account.InfoBo;
import com.bilibili.adp.cpc.biz.services.account.bos.EffectAdMidQueryBo;
import com.bilibili.adp.v6.creative.ArchiveCreativeFlyInfoBo;
import com.bilibili.adp.v6.resource.archive.bos.GeneralVideoArcAuthInfoQueryBo;
import com.bilibili.adp.v6.resource.archive.bos.LauArchiveAuthorizedRelationBaseBo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @ClassName AdArchiveServiceMapper
 * <AUTHOR>
 * @Date 2025/4/15 8:52 下午
 * @Version 1.0
 **/
@Mapper(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface AdArchiveServiceMapper {

    AdArchiveServiceMapper MAPPER = Mappers.getMapper(AdArchiveServiceMapper.class);

    AdpArcFlyInfoReply toRo(ArchiveCreativeFlyInfoBo x);

    @Mapping(target = "goeMtime", ignore = true)
    @Mapping(target = "goeMtimeLongValue", source = "goeMtime")
    @Mapping(target = "originAuthAccountId")
    @Mapping(target = "contentType",
            expression = "java(com.bilibili.adp.cpc.enums.account.ContentType.getByCode(x.getContentType()))")
    @Mapping(target = "page", source = "pn")
    @Mapping(target = "pageSize", source = "ps")
    EffectAdMidQueryBo fromRo(AdpBiliAuthedMidInfoReq x);

    List<AdpBiliAuthedMidInfoEntity> toRoList(List<InfoBo> x);

    AdpBiliAuthedMidInfoEntity toRo(InfoBo x);

    @Mapping(target = "status", expression = "java(com.bilibili.adp.cpc.enums.archive.ArchiveAuthStatusEnum.AUTHORIZED.getCode())")
    GeneralVideoArcAuthInfoQueryBo fromRo(GetAdpGeneralArcReq x);

    @Mapping(target = "avid", source = "avid")
    AdpGeneralArcEntity toRo(Long avid);

    List<AdpGeneralArcEntity> toGeneralRoList(List<LauArchiveAuthorizedRelationBaseBo> x);

    AdpGeneralArcEntity toGeneralRo(LauArchiveAuthorizedRelationBaseBo x);
}
