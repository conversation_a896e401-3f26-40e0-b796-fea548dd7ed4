package com.bilibili.adp.cpc.biz.services.target_package.api.upgrade;

import com.bilibili.adp.resource.api.target_package.upgrade.dto.TargetPackageMinigameTargetDto;

import java.util.List;
import java.util.Map;

/**
 * @ClassName IResTargetPackageMinigameService
 * <AUTHOR>
 * @Date 2023/1/16 3:52 下午
 * @Version 1.0
 *
 * copy class
 * @see com.bilibili.adp.resource.api.target_package.upgrade.IResTargetPackageMinigameService
 **/
public interface IResTargetPackageMinigameService {
    /**
     * 保存新版定向包小游戏定向
     */
    void saveTargetPackageMinigameTarget(Integer targetPackageId, List<String> includeMiniGameIds, List<String> excludeMiniGameIds);

    /**
     * 查询新版定向包小游戏定向
     */
    TargetPackageMinigameTargetDto getTargetPackageMinigameTarget(Integer targetPackageId);

    Map<Integer, TargetPackageMinigameTargetDto> getTargetPackageMinigameTargetMap(List<Integer> targetPackageIds);
}
