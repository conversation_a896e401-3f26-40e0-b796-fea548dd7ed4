package com.bilibili.adp.cpc.repo;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.account.dao.AccProductDao;
import com.bilibili.adp.account.po.AccProductPo;
import com.bilibili.adp.account.po.AccProductPoExample;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.ad_product.dto.*;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.QueryCpcUnitDto;
import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.adp.cpc.dao.ad.*;
import com.bilibili.adp.cpc.dao.ad_core.mybatis.AdProductMappingDao;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.QAdProductMapping;
import com.bilibili.adp.cpc.dao.querydsl.QAdProductTotalInfo;
import com.bilibili.adp.cpc.dao.querydsl.pos.AdProductShortFilmPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.AdProductTotalInfoPo;
import com.bilibili.adp.cpc.dao.querydsl_cpc.QCPCAdProductMapping;
import com.bilibili.adp.cpc.dao.querydsl_cpc.QCPCAdProductTotalInfo;
import com.bilibili.adp.cpc.dao.querydsl_cpc.pos.AdProductMappingCPCPo;
import com.bilibili.adp.cpc.dao.querydsl_cpc.pos.AdProductTotalInfoCPCPo;
import com.bilibili.adp.cpc.enums.ad_product.*;
import com.bilibili.adp.cpc.po.ad.*;
import com.bilibili.adp.cpc.utils.ExampleUtils;
import com.bilibili.adp.resource.biz.pojo.ResTargetItemPo;
import com.bilibili.adp.resource.biz.pojo.ResTargetItemPoExample;
import com.bilibili.adp.resource.biz.res_dao.ResTargetItemDao;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.crm.platform.common.IsValid;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.config.AppConfig.AD_BUSINESS_CPC_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.*;
import static com.bilibili.adp.cpc.dao.querydsl.QAdProductShortFilm.adProductShortFilm;

/**
 * <AUTHOR>
 * @date 2023/10/30 11:49
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class AdProductRepo {

    private final AdProductCategoryDao adProductCategoryDao;
    private final AdProductSkuDao adProductSkuDao;
    private final AdProductSpuDao adProductSpuDao;
    private final AdProductItemDao adProductItemDao;

    private final AdProductLibraryDao adProductLibraryDao;
    private final AdProductLibraryShareDao adProductLibraryShareDao;
    private final AdProductEducationDao adProductEducationDao;
    private final AdProductBorrowLoanDao adProductBorrowLoanDao;
    private final AdProductNovelDao adProductNovelDao;
    private final AdProductCarDao adProductCarDao;
    private final AdProductInfoShareDao adProductInfoShareDao;

    private final AdProductMappingDao adProductMappingDao;
    private final ResTargetItemDao resTargetItemDao;
    private final AccProductDao accProductDao;
    private final ICpcUnitService cpcUnitService;

    private static final Long DEFAULT_SKU_CODE = 0L;

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;


    @Resource
    @Qualifier(AD_BUSINESS_CPC_BQF)
    private BaseQueryFactory cpcBqf;

    public AdProductCategoryPo getProductCategoryByCode(Long code) {

        AdProductCategoryPoExample example = new AdProductCategoryPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID).andCodeEqualTo(code);
        List<AdProductCategoryPo> categoryPos = adProductCategoryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(categoryPos)) {
            return null;
        }
        return categoryPos.get(0);
    }

    public AdProductCategoryPo getProductCategoryByCode(Long code, Long pCode) {

        AdProductCategoryPoExample example = new AdProductCategoryPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID).andCodeEqualTo(code).andPCodeEqualTo(pCode);
        List<AdProductCategoryPo> categoryPos = adProductCategoryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(categoryPos)) {
            return null;
        }
        return categoryPos.get(0);
    }

    public List<AdProductCategoryPo> getProductCategoryList(Long pCode, int level) {

        AdProductCategoryPoExample example = new AdProductCategoryPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID)
                .andLevelEqualTo(level).andPCodeEqualTo(pCode);
        return adProductCategoryDao.selectByExample(example);
    }

    public List<AdProductCategoryPo> getProductCategoryListByPCodeList(List<Long> pCodeList, int level) {

        AdProductCategoryPoExample example = new AdProductCategoryPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID)
                .andLevelEqualTo(level).andPCodeIn(pCodeList);
        return adProductCategoryDao.selectByExample(example);
    }

    public List<AdProductCategoryPo> getAllProductCategoryList() {

        AdProductCategoryPoExample example = new AdProductCategoryPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID);
        return adProductCategoryDao.selectByExample(example);
    }

    public Map<Long, String> getCategoryCode2Name(List<Long> categoryCodes) {
        AdProductCategoryPoExample example = new AdProductCategoryPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID).andCodeIn(categoryCodes);
        var categorys = adProductCategoryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(categorys)) {
            return new HashMap<>();
        }
        Map<Long, String> code2Name = new HashMap<>();
        categorys.forEach(category -> code2Name.put(category.getCode(), category.getName()));
        return code2Name;
    }

    public List<AdProductCategoryPo> getAllProductCategoryListByLevel(Integer level) {

        AdProductCategoryPoExample example = new AdProductCategoryPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID).andLevelEqualTo(level);
        return adProductCategoryDao.selectByExample(example);
    }


    public List<AdProductSkuPo> querySku(Long skuCode, List<Long> spuCodes) {

        AdProductSkuPoExample example = new AdProductSkuPoExample();
        AdProductSkuPoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID).andBizStatusEqualTo(1);
        ExampleUtils.notNull(skuCode, criteria::andCodeEqualTo);
        ExampleUtils.notEmpty(spuCodes, criteria::andSpuCodeIn);

        return adProductSkuDao.selectByExample(example);
    }


    public List<AdProductSpuPo> querySpu(Long spuCode, Long thirdCategoryCode) {

        AdProductSpuPoExample example = new AdProductSpuPoExample();
        AdProductSpuPoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID).andBizStatusEqualTo(1);
        ExampleUtils.notNull(spuCode, criteria::andCodeEqualTo);
        ExampleUtils.notNull(thirdCategoryCode, criteria::andThirdCategoryCodeEqualTo);

        return adProductSpuDao.selectByExample(example);
    }

    public List<AdProductSpuPo> querySpuList(List<Long> spuCodes) {

        AdProductSpuPoExample example = new AdProductSpuPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andCodeIn(spuCodes);
        return adProductSpuDao.selectByExample(example);
    }

    public List<AdProductItemPo> getItemsByType(List<Integer> types) {

        AdProductItemPoExample example = new AdProductItemPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andTypeIn(types);
        return adProductItemDao.selectByExample(example);
    }

    public List<AdProductItemPo> getAllItems() {

        AdProductItemPoExample example = new AdProductItemPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID);
        return adProductItemDao.selectByExample(example);
    }

    public AdProductMappingPo getProductMappingByMappingId(Integer mappingId, Integer type) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andMappingIdEqualTo(mappingId).andTypeEqualTo(type);
        List<AdProductMappingPo> adProductMappingPos = adProductMappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(adProductMappingPos)) {
            return null;
        }
        return adProductMappingPos.get(0);
    }


    public List<AdProductBaseDto> getProductsByMappingIdsV2(Integer mappingId, Integer type) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andMappingIdEqualTo(mappingId).andTypeEqualTo(type);
        List<AdProductMappingPo> mappingPos = adProductMappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(mappingPos)) {
            return null;
        }

        List<AdProductTotalInfoPo> adProductTotalInfoPoList = adBqf.selectFrom(QAdProductTotalInfo.adProductTotalInfo)
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.in(mappingPos.stream().map(AdProductMappingPo::getAdProductId).collect(Collectors.toList()))
                        .and(QAdProductTotalInfo.adProductTotalInfo.isDeleted.eq(IsDeleted.VALID)))
                .fetch();
        return adProductTotalInfoPoList.stream().map(adProductTotalInfoPo -> AdProductBaseDto.builder()
                .productId(adProductTotalInfoPo.getAdProductId())
                .name(adProductTotalInfoPo.getAdProductName())
                .libraryId(adProductTotalInfoPo.getLibraryId())
                .adMainImgUrl(adProductTotalInfoPo.getAdMainImgUrl())
                .build()).collect(Collectors.toList());

    }

    public List<AdProductMappingPo> getProductMappingByProductIdsLimit(List<Long> productIds,
                                                                       Integer type, Integer limit) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAdProductIdIn(productIds)
                .andTypeEqualTo(type);
        example.setLimit(limit);
        return adProductMappingDao.selectByExample(example);
    }

    public List<AdProductMappingPo> getProductMappingByProductIdsLimit(List<Long> productIds, Integer type) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAdProductIdIn(productIds)
                .andTypeEqualTo(type);
        example.setLimit(10);
        return adProductMappingDao.selectByExample(example);
    }

    public List<AdProductMappingPo> getProductMappingByProductIds(List<Long> productIds, Integer type) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAdProductIdIn(productIds)
                .andTypeEqualTo(type);
        return adProductMappingDao.selectByExample(example);
    }

    public List<AdProductLibraryPo> getLibraries(Integer accountId, List<Long> libraryIds) {
        AdProductLibraryPoExample example = new AdProductLibraryPoExample();
        var criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID);
        Assert.isTrue(Utils.isPositive(accountId) || !CollectionUtils.isEmpty(libraryIds),
                "产品库id和账户id必须传一个");
        ExampleUtils.notNull(accountId, criteria::andAccountIdEqualTo);
        ExampleUtils.notEmpty(libraryIds, criteria::andIdIn);
        return adProductLibraryDao.selectByExample(example);
    }

    public List<AdProductLibrarySharePo> getShareLibrariesByAccountId(Integer accountId, Long libraryId) {
        AdProductLibrarySharePoExample example = new AdProductLibrarySharePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID)
                .andAccountIdEqualTo(accountId).andLibraryIdEqualTo(libraryId);
        return adProductLibraryShareDao.selectByExample(example);
    }

    public void updateLibrary(Long libraryId, String name) {
        AdProductLibraryPoExample example = new AdProductLibraryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andIdEqualTo(libraryId);
        adProductLibraryDao.updateByExampleSelective(AdProductLibraryPo.builder()
                .name(name).build(), example);

        AdProductLibrarySharePoExample sharePoExample = new AdProductLibrarySharePoExample();
        sharePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID).andLibraryIdEqualTo(libraryId);
        adProductLibraryShareDao.updateByExampleSelective(AdProductLibrarySharePo.builder()
                .name(name).build(), sharePoExample);
    }

    public List<Integer> queryAuthorityAccountList(Long libraryId, Integer curAccountId) {
        AdProductLibrarySharePoExample example = new AdProductLibrarySharePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID)
                .andAccountIdNotEqualTo(curAccountId)
                .andLibraryIdEqualTo(libraryId);
        var librarySharePos = adProductLibraryShareDao.selectByExample(example);
        if (CollectionUtils.isEmpty(librarySharePos)) {
            return new ArrayList<>();
        }
        return librarySharePos.stream().map(AdProductLibrarySharePo::getAccountId)
                .collect(Collectors.toList());
    }

    public void shareLibrary(Long libraryId, List<Integer> shareAccountIds) {
        AdProductLibraryPoExample example = new AdProductLibraryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andIdEqualTo(libraryId);
        var libraryPo = adProductLibraryDao.selectByExample(example)
                .stream().findFirst().orElse(null);
        Assert.notNull(libraryPo, "查询不到被分享的商品库");

        List<AdProductTotalInfoPo> adProductTotalInfoPoList = getProductsByLibraryIdV2(Lists.newArrayList(libraryId));
        List<Long> productIds = adProductTotalInfoPoList.stream().map(AdProductTotalInfoPo::getAdProductId).collect(Collectors.toList());
        Map<Long, String> idNameMap = adProductTotalInfoPoList.stream().collect(Collectors.toMap(AdProductTotalInfoPo::getAdProductId, AdProductTotalInfoPo::getAdProductName));

        for (Integer accountId : shareAccountIds) {
            List<AdProductMappingPo> mappingPoList = new ArrayList<>();
            List<AdProductLibrarySharePo> librarySharePoList = new ArrayList<>();
            List<AdProductInfoSharePo> infoSharePoList = new ArrayList<>();
            AdProductLibrarySharePo sharePo = new AdProductLibrarySharePo();
            BeanUtils.copyProperties(libraryPo, sharePo);
            sharePo.setId(null);
            sharePo.setBelongType(AdProductLibraryBelongTypeEnum.SHARED.getCode());
            sharePo.setLibraryId(libraryId);
            sharePo.setAccountId(accountId);
            librarySharePoList.add(sharePo);
            if (!CollectionUtils.isEmpty(productIds)) {
                for (Long productId : productIds) {
                    AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
                    adProductMappingPo.setAdProductId(productId);
                    adProductMappingPo.setMappingId(accountId);
                    adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
                    adProductMappingPo.setIsDeleted(IsDeleted.DELETED);
                    adProductMappingPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
                    adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
                    adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
                    mappingPoList.add(adProductMappingPo);

                    AdProductInfoSharePo adProductInfoSharePo = new AdProductInfoSharePo();
                    adProductInfoSharePo.setAdProductId(productId);
                    adProductInfoSharePo.setLibraryId(libraryId);
                    adProductInfoSharePo.setShareAccountId(accountId);
                    adProductInfoSharePo.setBizStatus(AdProductStatusEnum.DISABLE.getCode());
                    adProductInfoSharePo.setAdProductName(idNameMap.getOrDefault(productId, ""));
                    adProductInfoSharePo.setIsDeleted(IsDeleted.VALID);
                    adProductInfoSharePo.setCtime(new Timestamp(System.currentTimeMillis()));
                    adProductInfoSharePo.setMtime(new Timestamp(System.currentTimeMillis()));
                    infoSharePoList.add(adProductInfoSharePo);
                }
            }
            if (!CollectionUtils.isEmpty(mappingPoList)) {
                adProductMappingDao.insertBatch(mappingPoList);
            }

            if (!CollectionUtils.isEmpty(librarySharePoList)) {
                adProductLibraryShareDao.insertUpdateBatch(librarySharePoList);
            }
            if (!CollectionUtils.isEmpty(infoSharePoList)) {
                adProductInfoShareDao.insertBatch(infoSharePoList);
            }
        }
    }

    public void deleteAdProductInfoShare(List<Integer> accountIdList, List<Long> libraryIdList) {
        if (CollectionUtils.isEmpty(accountIdList) && CollectionUtils.isEmpty(libraryIdList)) {
            return;
        }
        AdProductInfoSharePoExample example = new AdProductInfoSharePoExample();
        AdProductInfoSharePoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID);
        if (!CollectionUtils.isEmpty(accountIdList)) {
            criteria.andShareAccountIdIn(accountIdList);
        }
        if (!CollectionUtils.isEmpty(libraryIdList)) {
            criteria.andLibraryIdIn(libraryIdList);
        }
        AdProductInfoSharePo record = AdProductInfoSharePo.builder().isDeleted(IsDeleted.DELETED).build();
        adProductInfoShareDao.updateByExampleSelective(record, example);
    }

    public void deleteAdProductInfoByProductId(List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }
        AdProductInfoSharePoExample example = new AdProductInfoSharePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAdProductIdIn(productIdList);
        AdProductInfoSharePo record = AdProductInfoSharePo.builder().isDeleted(IsDeleted.DELETED).build();
        adProductInfoShareDao.updateByExampleSelective(record, example);
    }


    public PageResult<AdProductLibrarySharePo> queryAllLibraries(AdProductLibraryQueryDto queryDto) {
        AdProductLibrarySharePoExample example = new AdProductLibrarySharePoExample();
        AdProductLibrarySharePoExample.Criteria criteria = example.or()
                .andAccountIdEqualTo(queryDto.getAccountId()).andIsDeletedEqualTo(IsDeleted.VALID);
        ExampleUtils.notNull(queryDto.getBelongType(), criteria::andBelongTypeEqualTo);
        ExampleUtils.notNull(queryDto.getType(), criteria::andTypeEqualTo);
        ExampleUtils.notNull(queryDto.getLibraryId(), criteria::andLibraryIdEqualTo);
        if (StringUtils.hasText(queryDto.getNameLike())) {
            criteria.andNameLike("%" + queryDto.getNameLike() + "%");
        }

        long count = adProductLibraryShareDao.countByExample(example);
        if (count == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        Page page = Page.valueOf(queryDto.getPage(), queryDto.getSize());
        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());

        example.setOrderByClause("mtime desc");

        List<AdProductLibrarySharePo> libraryPos = adProductLibraryShareDao.selectByExample(example);
        return PageResult.<AdProductLibrarySharePo>builder().total(Math.toIntExact(count))
                .records(libraryPos).build();
    }

    public List<AdProductLibrarySharePo> queryAccountAllLibraryIds(Integer accountId) {
        AdProductLibrarySharePoExample example = new AdProductLibrarySharePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAccountIdEqualTo(accountId);
        return adProductLibraryShareDao.selectByExample(example);
    }


    public PageResult<AdProductTotalInfoPo> queryProductBaseV2(AdProductQueryDto queryDto) {
        long count = adBqf.selectFrom(QAdProductTotalInfo.adProductTotalInfo)
                .where(QAdProductTotalInfo.adProductTotalInfo.libraryId.eq(queryDto.getLibraryId()))
                .where(QAdProductTotalInfo.adProductTotalInfo.isDeleted.eq(IsDeleted.VALID))
                .whereIfHasText(queryDto.getNameLike(), QAdProductTotalInfo.adProductTotalInfo.adProductName::contains)
                .whereIfNotNull(queryDto.getBizStatus(), QAdProductTotalInfo.adProductTotalInfo.bizStatus::eq)
                .whereIfNotNull(queryDto.getProductId(), QAdProductTotalInfo.adProductTotalInfo.adProductId::eq)
                .orderBy("mtime desc")
                .fetchCount();
        if (count == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }
        Page page = Page.valueOf(queryDto.getPage(), queryDto.getSize());

        List<AdProductTotalInfoPo> adProductTotalInfoPoList = adBqf.selectFrom(QAdProductTotalInfo.adProductTotalInfo)
                .where(QAdProductTotalInfo.adProductTotalInfo.libraryId.eq(queryDto.getLibraryId()))
                .where(QAdProductTotalInfo.adProductTotalInfo.isDeleted.eq(IsDeleted.VALID))
                .whereIfHasText(queryDto.getNameLike(), QAdProductTotalInfo.adProductTotalInfo.adProductName::contains)
                .whereIfNotNull(queryDto.getBizStatus(), QAdProductTotalInfo.adProductTotalInfo.bizStatus::eq)
                .whereIfNotNull(queryDto.getProductId(), QAdProductTotalInfo.adProductTotalInfo.adProductId::eq)
                .orderBy("mtime desc")
                .limit(page.getLimit())
                .offset(page.getOffset())
                .fetch();
        return PageResult.<AdProductTotalInfoPo>builder().total(Math.toIntExact(count))
                .records(adProductTotalInfoPoList).build();
    }

    public PageResult<AdProductTotalInfoPo> queryShareProductBaseV2(AdProductQueryDto queryDto) {
        AdProductInfoSharePoExample example = new AdProductInfoSharePoExample();
        AdProductInfoSharePoExample.Criteria criteria = example.or()
                .andLibraryIdEqualTo(queryDto.getLibraryId())
                .andShareAccountIdEqualTo(queryDto.getAccountId())
                .andIsDeletedEqualTo(IsDeleted.VALID);

        if (StringUtils.hasText(queryDto.getNameLike())) {
            criteria.andAdProductNameLike("%" + queryDto.getNameLike() + "%");
        }
        if (queryDto.getBizStatus() != null) {
            criteria.andBizStatusEqualTo(queryDto.getBizStatus());
        }
        if (queryDto.getProductId() != null) {
            criteria.andAdProductIdEqualTo(queryDto.getProductId());
        }

        example.setOrderByClause("mtime desc");

        long count = adProductInfoShareDao.countByExample(example);
        if (count == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        Page page = Page.valueOf(queryDto.getPage(), queryDto.getSize());
        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());

        List<AdProductInfoSharePo> adProductInfoSharePoList = adProductInfoShareDao.selectByExample(example);
        Map<Long, AdProductInfoSharePo> shareProductIdMap = adProductInfoSharePoList.stream().collect(Collectors.toMap(AdProductInfoSharePo::getAdProductId, Function.identity()));


        List<Long> adProductIdList = adProductInfoSharePoList.stream().map(AdProductInfoSharePo::getAdProductId).collect(Collectors.toList());
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = adBqf.selectFrom(QAdProductTotalInfo.adProductTotalInfo)
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.in(adProductIdList))
                .fetch();
        for (AdProductTotalInfoPo adProductTotalInfoPo : adProductTotalInfoPoList) {
            AdProductInfoSharePo adProductInfoSharePo = shareProductIdMap.get(adProductTotalInfoPo.getAdProductId());
            if (adProductInfoSharePo != null) {
                adProductTotalInfoPo.setBizStatus(adProductInfoSharePo.getBizStatus());
            } else {
                adProductTotalInfoPo.setBizStatus(AdProductStatusEnum.DISABLE.getCode());
            }
        }

        return PageResult.<AdProductTotalInfoPo>builder().total(Math.toIntExact(count))
                .records(adProductTotalInfoPoList).build();
    }

    public AdProductTotalInfoPo getProductBaseByIdV2(Long productId) {
        return adBqf.selectFrom(QAdProductTotalInfo.adProductTotalInfo)
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.eq(productId))
                .where(QAdProductTotalInfo.adProductTotalInfo.isDeleted.eq(IsDeleted.VALID))
                .fetchOne();
    }

    public List<AdProductTotalInfoPo> getProductBaseByAccountIdV2(Integer accountId) {
        return adBqf.selectFrom(QAdProductTotalInfo.adProductTotalInfo)
                .where(QAdProductTotalInfo.adProductTotalInfo.accountId.eq(accountId))
                .where(QAdProductTotalInfo.adProductTotalInfo.isDeleted.eq(IsDeleted.VALID))
                .fetch();
    }

    public List<AdProductTotalInfoCPCPo> queryAdProductTotalInfoCPCPoList(List<String> uniqueKeys) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = cpcBqf.selectFrom(QCPCAdProductTotalInfo.adProductTotalInfo)
                .where(QCPCAdProductTotalInfo.adProductTotalInfo.uniqueKey.in(uniqueKeys))
                .where(QCPCAdProductTotalInfo.adProductTotalInfo.isDeleted.eq(IsDeleted.VALID))
                .fetch();
        return adProductTotalInfoCPCPoList;
    }

    public List<AdProductTotalInfoCPCPo> queryAdProductTotalInfoCPCPoListWithDeleted(List<Long> adProductIds) {
        return cpcBqf.selectFrom(QCPCAdProductTotalInfo.adProductTotalInfo)
                .where(QCPCAdProductTotalInfo.adProductTotalInfo.adProductId.in(adProductIds))
                .fetch();
    }

    public List<AdProductMappingCPCPo> queryAdProductMappingCpcPoList(List<SdpaProductMappingDarkDto> sdpaProductMappingDarkDtoList) {
        return cpcBqf.selectFrom(QCPCAdProductMapping.adProductMapping)
                .where(QCPCAdProductMapping.adProductMapping.adProductId.in(sdpaProductMappingDarkDtoList.stream().map(SdpaProductMappingDarkDto::getAdProductId).collect(Collectors.toList())))
                .where(QCPCAdProductMapping.adProductMapping.mappingId.in(sdpaProductMappingDarkDtoList.stream().map(SdpaProductMappingDarkDto::getMappingId).collect(Collectors.toList())))
                .where(QCPCAdProductMapping.adProductMapping.isDeleted.eq(IsDeleted.VALID))
                .where(QCPCAdProductMapping.adProductMapping.belongType.eq(AdProductBelongTypeEnum.DARK.getCode()))
                .fetch();
    }

    public List<AdProductMappingCPCPo> queryAdProductMappingCpcPoList(Long adProductId, Integer mappingId) {
        return cpcBqf.selectFrom(QCPCAdProductMapping.adProductMapping)
                .where(QCPCAdProductMapping.adProductMapping.adProductId.eq(adProductId))
                .where(QCPCAdProductMapping.adProductMapping.mappingId.eq(mappingId))
                .where(QCPCAdProductMapping.adProductMapping.isDeleted.eq(IsDeleted.VALID))
                .where(QCPCAdProductMapping.adProductMapping.belongType.eq(AdProductBelongTypeEnum.DARK.getCode()))
                .fetch();
    }

    public List<AdProductMappingCPCPo> queryAdProductMappingCpcPoList(Integer mappingId, Integer type, Integer belongType) {
        return cpcBqf.selectFrom(QCPCAdProductMapping.adProductMapping)
                .where(QCPCAdProductMapping.adProductMapping.type.eq(type))
                .where(QCPCAdProductMapping.adProductMapping.mappingId.eq(mappingId))
                .where(QCPCAdProductMapping.adProductMapping.isDeleted.eq(IsDeleted.VALID))
                .where(QCPCAdProductMapping.adProductMapping.belongType.eq(belongType))
                .fetch();
    }

    public List<AdProductMappingCPCPo> queryAdProductMappingCpcPoListByMappingIds(List<Integer> mappingIds) {
        return cpcBqf.selectFrom(QCPCAdProductMapping.adProductMapping)
                .where(QCPCAdProductMapping.adProductMapping.mappingId.in(mappingIds))
                .where(QCPCAdProductMapping.adProductMapping.isDeleted.eq(IsDeleted.VALID))
                .where(QCPCAdProductMapping.adProductMapping.belongType.eq(AdProductBelongTypeEnum.DARK.getCode()))
                .fetch();
    }


    public List<AdProductTotalInfoPo> getProductsByLibraryIdV2(List<Long> libraryIds) {
        return adBqf.selectFrom(QAdProductTotalInfo.adProductTotalInfo)
                .where(QAdProductTotalInfo.adProductTotalInfo.libraryId.in(libraryIds))
                .where(QAdProductTotalInfo.adProductTotalInfo.isDeleted.eq(IsDeleted.VALID))
                .fetch();
    }


    public Integer saveProductMapping(Long adProductId, Integer mappingId, Integer type) {
        AdProductMappingPo mappingPo = new AdProductMappingPo();
        mappingPo.setType(type);
        mappingPo.setAdProductId(adProductId);
        mappingPo.setMappingId(mappingId);
        return adProductMappingDao.insertSelective(mappingPo);
    }

    public void deleteProductMapping(List<Long> adProductIds, Integer mappingId, Integer type) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andMappingIdEqualTo(mappingId)
                .andTypeEqualTo(type)
                .andAdProductIdIn(adProductIds);
        adProductMappingDao.updateByExampleSelective(AdProductMappingPo.builder().isDeleted(IsDeleted.DELETED).build(),
                example);
    }

    public List<Long> selectProductMapping(List<Long> adProductIds, Integer mappingId, Integer type) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andMappingIdEqualTo(mappingId)
                .andTypeEqualTo(type)
                .andAdProductIdIn(adProductIds);
        List<AdProductMappingPo> adProductMappingPoList = adProductMappingDao.selectByExample(example);
        return adProductMappingPoList.stream().map(AdProductMappingPo::getId).collect(Collectors.toList());
    }

    public void deleteProductMapping(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andIdIn(idList);
        adProductMappingDao.updateByExampleSelective(AdProductMappingPo.builder().isDeleted(IsDeleted.DELETED).build(),
                example);
    }

    public void deleteProductMapping(List<Integer> mappingIds, Integer type) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andMappingIdIn(mappingIds)
                .andTypeEqualTo(type);
        adProductMappingDao.updateByExampleSelective(AdProductMappingPo.builder().isDeleted(IsDeleted.DELETED).build(),
                example);
    }

    public void deleteProductMappingByProductId(Long adProductId) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAdProductIdEqualTo(adProductId);
        adProductMappingDao.updateByExampleSelective(AdProductMappingPo.builder().isDeleted(IsDeleted.DELETED).build(),
                example);
    }

    public void deleteProductMappingByProductIdAndAccountId(Long adProductId, Integer accountId) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAdProductIdEqualTo(adProductId)
                .andMappingIdEqualTo(accountId).andTypeEqualTo(AdProductBindTypeEnum.ACCOUNT.getCode());
        adProductMappingDao.updateByExampleSelective(AdProductMappingPo.builder().isDeleted(IsDeleted.DELETED).build(),
                example);
    }

    public void addProductMappingByProductIdAndAccountId(Long adProductId, Integer accountId) {
        AdProductMappingPo adProductMappingPo = AdProductMappingPo.builder()
                .adProductId(adProductId)
                .mappingId(accountId)
                .type(AdProductBindTypeEnum.ACCOUNT.getCode())
                .belongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode())
                .build();
        adProductMappingDao.insertSelective(adProductMappingPo);
    }

    public void deleteProductMappingByProductId(List<Long> adProductIdList) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAdProductIdIn(adProductIdList);
        adProductMappingDao.updateByExampleSelective(AdProductMappingPo.builder().isDeleted(IsDeleted.DELETED).build(),
                example);
    }

    public void deleteProductMappingByIds(List<Long> idList) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andIdIn(idList);
        adProductMappingDao.updateByExampleSelective(AdProductMappingPo.builder().isDeleted(IsDeleted.DELETED).build(),
                example);
    }

    public void recoverProductMappingByIds(List<Long> idList) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.DELETED).andIdIn(idList);
        adProductMappingDao.updateByExampleSelective(AdProductMappingPo.builder().isDeleted(IsDeleted.VALID).build(),
                example);
    }

    public void batchUpdateProductMapping(List<Long> adProductIds, Integer mappingId,
                                          Integer type, boolean forAdd) {
        if (forAdd && CollectionUtils.isEmpty(adProductIds)) {
            return;
        }
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andMappingIdEqualTo(mappingId);
        var mappingPos = adProductMappingDao.selectByExample(example);

        List<Long> inc = new ArrayList<>();
        List<Long> dec = new ArrayList<>();

        if (CollectionUtils.isEmpty(mappingPos)) {
            if (CollectionUtils.isEmpty(adProductIds)) {
                return;
            }
            inc.addAll(adProductIds);
        } else {
            mappingPos.forEach(mappingPo -> {
                if (!adProductIds.contains(mappingPo.getAdProductId())) {
                    dec.add(mappingPo.getAdProductId());
                }
            });
            var oldProductIds = mappingPos.stream().map(AdProductMappingPo::getAdProductId)
                    .collect(Collectors.toList());
            mappingPos.forEach(mappingPo -> {
                if (!adProductIds.contains(mappingPo.getAdProductId())) {
                    dec.add(mappingPo.getAdProductId());
                }
            });

            adProductIds.forEach(productId -> {
                if (!oldProductIds.contains(productId)) {
                    inc.add(productId);
                }
            });
        }
        if (!CollectionUtils.isEmpty(inc)) {
            inc.forEach(adProductId -> saveProductMapping(adProductId, mappingId, type));
        }
        if (!CollectionUtils.isEmpty(dec)) {
            dec.forEach(adProductId -> deleteProductMapping(Lists.newArrayList(adProductId), mappingId, type));
        }
    }

    public Long insertCategory(Long code, String name, Long pCode, int level) {
        AdProductCategoryPo categoryPo = AdProductCategoryPo.builder()
                .code(code)
                .name(name)
                .pCode(pCode)
                .level(level)
                .build();
        adProductCategoryDao.insertSelective(categoryPo);
        return code;
    }

    public Long insertSku(Long spuCode, Long skuCode,
                          String skuName) {
        AdProductSkuPo skuPo = AdProductSkuPo.builder()
                .code(skuCode)
                .spuCode(spuCode)
                .name(skuName)
                .build();
        adProductSkuDao.insertSelective(skuPo);
        return skuCode;
    }

    public Long insertSpu(Long spuCode, String spuName, Long thirdCategoryCode) {
        AdProductSpuPo spuPo = AdProductSpuPo.builder()
                .code(spuCode)
                .name(spuName)
                .thirdCategoryCode(thirdCategoryCode)
                .build();
        adProductSpuDao.insertSelective(spuPo);
        return spuCode;
    }


    public List<Long> insertImportLoanV2(List<ImportAdProductLoanDto> importAdProductLoanDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPos = convertImportLoanToAdProductTotalInfoPo(importAdProductLoanDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        Map<String, AdProductTotalInfoPo> productNameMap = adProductTotalInfoPos.stream().collect(Collectors.toMap(AdProductTotalInfoPo::getAdProductName, Function.identity()));

        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();

        for (ImportAdProductLoanDto importAdProductLoanDto : importAdProductLoanDtoList) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            AdProductTotalInfoPo adProductTotalInfoPo = productNameMap.get(importAdProductLoanDto.getName());
            if (Objects.isNull(adProductTotalInfoPo)) {
                continue;
            }

            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(importAdProductLoanDto.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setBelongType(importAdProductLoanDto.getBelongType());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPoList.add(adProductMappingPo);
            if (Utils.isPositive(importAdProductLoanDto.getUnitId())) {
                AdProductMappingPo adProductMappingPoUnit = new AdProductMappingPo();
                adProductMappingPoUnit.setAdProductId(adProductTotalInfoPo.getAdProductId());
                adProductMappingPoUnit.setMappingId(importAdProductLoanDto.getUnitId());
                adProductMappingPoUnit.setType(AdProductBindTypeEnum.UNIT.getCode());
                adProductMappingPoUnit.setBelongType(importAdProductLoanDto.getBelongType());
                adProductMappingPoUnit.setIsDeleted(IsDeleted.VALID);
                adProductMappingPoUnit.setCtime(new Timestamp(System.currentTimeMillis()));
                adProductMappingPoUnit.setMtime(new Timestamp(System.currentTimeMillis()));
                adProductMappingPoList.add(adProductMappingPoUnit);
            }
        }
        adCoreBqf.insert(QAdProductMapping.adProductMapping).insertBeans(adProductMappingPoList);
        return idList;
    }


    public void updateProductStatusV2(Long productId, Integer bizStatus) {
        adBqf.update(QAdProductTotalInfo.adProductTotalInfo)
                .set(QAdProductTotalInfo.adProductTotalInfo.bizStatus, bizStatus)
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.eq(productId))
                .execute();
    }

    public void updateShareProductStatus(Integer accountId, Long productId, Integer shareStatus) {
        AdProductInfoSharePoExample example = new AdProductInfoSharePoExample();
        example.or()
                .andShareAccountIdEqualTo(accountId)
                .andAdProductIdEqualTo(productId)
                .andIsDeletedEqualTo(IsDeleted.VALID);

        AdProductInfoSharePo record = AdProductInfoSharePo.builder()
                .bizStatus(shareStatus)
                .build();

        adProductInfoShareDao.updateByExampleSelective(record, example);
    }


    public List<Long> insertLoanV2(List<SdpaProductLoanDto> sdpaProductLoanDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPos = convertLoanToAdProductTotalInfoPo(sdpaProductLoanDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();
        for (AdProductTotalInfoPo adProductTotalInfoPo : adProductTotalInfoPos) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(adProductTotalInfoPo.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            adProductMappingPoList.add(adProductMappingPo);
        }
        adProductMappingDao.insertBatch(adProductMappingPoList);
        return idList;
    }

    public List<Long> insertLoanDarkV2(List<SdpaProductLoanDto> sdpaProductLoanDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPos = convertLoanToAdProductTotalInfoCpcPo(sdpaProductLoanDtoList);
        List<Long> idList = cpcBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        return idList;
    }


    public List<Long> insertNovelV2(List<SdpaProductNovelDto> sdpaProductNovelDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPos = convertNovelToAdProductTotalInfoPo(sdpaProductNovelDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);
        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();
        for (AdProductTotalInfoPo adProductTotalInfoPo : adProductTotalInfoPos) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(adProductTotalInfoPo.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());

            adProductMappingPoList.add(adProductMappingPo);
        }
        adProductMappingDao.insertBatch(adProductMappingPoList);
        return idList;
    }

    public List<Long> insertNovelDarkV2(List<SdpaProductNovelDto> sdpaProductNovelDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPos = convertNovelToAdProductTotalInfoCpcPo(sdpaProductNovelDtoList);
        List<Long> idList = cpcBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        return idList;
    }


    public List<Long> insertShortFilmV2(List<SdpaShortFilmDto> sdpaShortFilmDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPos = convertShortFilmToAdProductTotalInfoPo(sdpaShortFilmDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);
        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();
        for (AdProductTotalInfoPo adProductTotalInfoPo : adProductTotalInfoPos) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(adProductTotalInfoPo.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());

            adProductMappingPoList.add(adProductMappingPo);
        }
        adProductMappingDao.insertBatch(adProductMappingPoList);
        return idList;
    }

    public List<Long> insertShortFilmDarkV2(List<SdpaShortFilmDto> sdpaShortFilmDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPos = convertShortFilmToAdProductTotalInfoCpcPo(sdpaShortFilmDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        return idList;
    }

    public List<Long> insertImportShortFilmV2(List<ImportAdProductShortFilmDto> importAdProductShortFilmDtos) {
        List<AdProductTotalInfoPo> adProductTotalInfoPos = convertImportShortFilmToAdProductTotalInfoPo(importAdProductShortFilmDtos);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        Map<String, AdProductTotalInfoPo> productNameMap = adProductTotalInfoPos.stream().collect(Collectors.toMap(AdProductTotalInfoPo::getAdProductName, Function.identity()));

        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();

        for (ImportAdProductShortFilmDto importAdProductShortFilmDto : importAdProductShortFilmDtos) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            AdProductTotalInfoPo adProductTotalInfoPo = productNameMap.get(importAdProductShortFilmDto.getName());
            if (Objects.isNull(adProductTotalInfoPo)) {
                continue;
            }

            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(importAdProductShortFilmDto.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setBelongType(importAdProductShortFilmDto.getBelongType());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPoList.add(adProductMappingPo);
            if (Utils.isPositive(importAdProductShortFilmDto.getUnitId())) {
                AdProductMappingPo adProductMappingPoUnit = new AdProductMappingPo();
                adProductMappingPoUnit.setAdProductId(adProductTotalInfoPo.getAdProductId());
                adProductMappingPoUnit.setMappingId(importAdProductShortFilmDto.getUnitId());
                adProductMappingPoUnit.setType(AdProductBindTypeEnum.UNIT.getCode());
                adProductMappingPoUnit.setBelongType(importAdProductShortFilmDto.getBelongType());
                adProductMappingPoUnit.setIsDeleted(IsDeleted.VALID);
                adProductMappingPoUnit.setCtime(new Timestamp(System.currentTimeMillis()));
                adProductMappingPoUnit.setMtime(new Timestamp(System.currentTimeMillis()));
                adProductMappingPoList.add(adProductMappingPoUnit);
            }
        }
        adCoreBqf.insert(QAdProductMapping.adProductMapping).insertBeans(adProductMappingPoList);
        return idList;
    }


    public List<Long> insertImportNovelV2(List<ImportAdProductNovelDto> importAdProductNovelDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPos = convertImportNovelToAdProductTotalInfoPo(importAdProductNovelDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        Map<String, AdProductTotalInfoPo> productNameMap = adProductTotalInfoPos.stream().collect(Collectors.toMap(AdProductTotalInfoPo::getAdProductName, Function.identity()));

        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();

        for (ImportAdProductNovelDto importAdProductNovelDto : importAdProductNovelDtoList) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            AdProductTotalInfoPo adProductTotalInfoPo = productNameMap.get(importAdProductNovelDto.getName());
            if (Objects.isNull(adProductTotalInfoPo)) {
                continue;
            }

            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(importAdProductNovelDto.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setBelongType(importAdProductNovelDto.getBelongType());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPoList.add(adProductMappingPo);
            if (Utils.isPositive(importAdProductNovelDto.getUnitId())) {
                AdProductMappingPo adProductMappingPoUnit = new AdProductMappingPo();
                adProductMappingPoUnit.setAdProductId(adProductTotalInfoPo.getAdProductId());
                adProductMappingPoUnit.setMappingId(importAdProductNovelDto.getUnitId());
                adProductMappingPoUnit.setType(AdProductBindTypeEnum.UNIT.getCode());
                adProductMappingPoUnit.setBelongType(importAdProductNovelDto.getBelongType());
                adProductMappingPoUnit.setIsDeleted(IsDeleted.VALID);
                adProductMappingPoUnit.setCtime(new Timestamp(System.currentTimeMillis()));
                adProductMappingPoUnit.setMtime(new Timestamp(System.currentTimeMillis()));
                adProductMappingPoList.add(adProductMappingPoUnit);
            }
        }
        adCoreBqf.insert(QAdProductMapping.adProductMapping).insertBeans(adProductMappingPoList);
        return idList;
    }


    public void updateLoanV2(SdpaProductLoanDto sdpaProductLoanDto) {
        AdProductTotalInfoPo adProductTotalInfoPo = convertLoanToAdProductTotalInfoPo(Collections.singletonList(sdpaProductLoanDto)).get(0);
        adBqf.update(QAdProductTotalInfo.adProductTotalInfo)
                .populate(adProductTotalInfoPo)
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.eq(sdpaProductLoanDto.getProductId()))
                .execute();
    }

    public void updateLoanDarkV2(List<SdpaProductLoanDto> sdpaProductLoanDto) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = convertLoanToAdProductTotalInfoCpcPo(sdpaProductLoanDto);
        adBqf.update(QCPCAdProductTotalInfo.adProductTotalInfo).updateBeans(adProductTotalInfoCPCPoList);

    }


    public void updateNovelV2(SdpaProductNovelDto sdpaProductNovelDto) {
        AdProductTotalInfoPo adProductTotalInfoPo = convertNovelToAdProductTotalInfoPo(Collections.singletonList(sdpaProductNovelDto)).get(0);
        adBqf.update(QAdProductTotalInfo.adProductTotalInfo)
                .populate(adProductTotalInfoPo)
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.eq(sdpaProductNovelDto.getProductId()))
                .execute();
    }

    public void updateNovelDarkV2(List<SdpaProductNovelDto> sdpaProductNovelDto) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = convertNovelToAdProductTotalInfoCpcPo(sdpaProductNovelDto);
        adBqf.update(QCPCAdProductTotalInfo.adProductTotalInfo).updateBeans(adProductTotalInfoCPCPoList);
    }

    public void updateShortFilmV2(SdpaShortFilmDto sdpaShortFilmDto) {
        AdProductTotalInfoPo adProductTotalInfoPo = convertShortFilmToAdProductTotalInfoPo(Collections.singletonList(sdpaShortFilmDto)).get(0);
        adBqf.update(QAdProductTotalInfo.adProductTotalInfo)
                .populate(adProductTotalInfoPo)
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.eq(sdpaShortFilmDto.getProductId()))
                .execute();
    }

    public void updateShortFilmDarkV2(List<SdpaShortFilmDto> sdpaShortFilmDto) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = convertShortFilmToAdProductTotalInfoCpcPo(sdpaShortFilmDto);
        cpcBqf.update(QCPCAdProductTotalInfo.adProductTotalInfo).updateBeans(adProductTotalInfoCPCPoList);
    }

    public List<Long> insertImportCourseV2(List<ImportAdProductEducationDto> courseDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPos = this.convertImportEducationToAdProductTotalInfoPo(courseDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        Map<String, AdProductTotalInfoPo> productNameMap = adProductTotalInfoPos.stream().collect(Collectors.toMap(AdProductTotalInfoPo::getAdProductName, Function.identity()));

        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();

        for (ImportAdProductEducationDto importAdProducEducationDto : courseDtoList) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            AdProductTotalInfoPo adProductTotalInfoPo = productNameMap.get(importAdProducEducationDto.getName());
            if (Objects.isNull(adProductTotalInfoPo)) {
                continue;
            }

            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(importAdProducEducationDto.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setBelongType(importAdProducEducationDto.getBelongType());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPoList.add(adProductMappingPo);
            if (Utils.isPositive(importAdProducEducationDto.getUnitId())) {
                AdProductMappingPo adProductMappingPoUnit = new AdProductMappingPo();
                adProductMappingPoUnit.setAdProductId(adProductTotalInfoPo.getAdProductId());
                adProductMappingPoUnit.setMappingId(importAdProducEducationDto.getUnitId());
                adProductMappingPoUnit.setType(AdProductBindTypeEnum.UNIT.getCode());
                adProductMappingPoUnit.setBelongType(importAdProducEducationDto.getBelongType());
                adProductMappingPoUnit.setIsDeleted(IsDeleted.VALID);
                adProductMappingPoUnit.setCtime(new Timestamp(System.currentTimeMillis()));
                adProductMappingPoUnit.setMtime(new Timestamp(System.currentTimeMillis()));
                adProductMappingPoList.add(adProductMappingPoUnit);
            }
        }
        adCoreBqf.insert(QAdProductMapping.adProductMapping).insertBeans(adProductMappingPoList);
        return idList;
    }


    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public List<Long> insertCourseV2(List<SdpaProductEducationDto> educationDtoList) {

        List<AdProductTotalInfoPo> adProductTotalInfoPos = this.convertEducationToAdProductTotalInfoPo(educationDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();
        for (AdProductTotalInfoPo adProductTotalInfoPo : adProductTotalInfoPos) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(adProductTotalInfoPo.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            adProductMappingPoList.add(adProductMappingPo);
        }
        adProductMappingDao.insertBatch(adProductMappingPoList);

        return idList;
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<Long> insertCourseDarkV2(List<SdpaProductEducationDto> educationDtoList) {

        List<AdProductTotalInfoCPCPo> adProductTotalInfoPos = this.convertEducationToAdProductTotalInfoCpcPo(educationDtoList);
        List<Long> idList = cpcBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        return idList;
    }


    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public List<Long> insertCarV2(List<SdpaProductCarDto> sdpaProductCarDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPos = convertCarToAdProductTotalInfoPo(sdpaProductCarDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);
        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();
        for (AdProductTotalInfoPo adProductTotalInfoPo : adProductTotalInfoPos) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(adProductTotalInfoPo.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());

            adProductMappingPoList.add(adProductMappingPo);
        }
        adProductMappingDao.insertBatch(adProductMappingPoList);
        return idList;
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<Long> insertECommerceV2(List<SdpaProductECommerceDto> sdpaProductECommerceDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPos = convertECommerceToAdProductTotalInfoPo(sdpaProductECommerceDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);
        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();
        for (AdProductTotalInfoPo adProductTotalInfoPo : adProductTotalInfoPos) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(adProductTotalInfoPo.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());

            adProductMappingPoList.add(adProductMappingPo);
        }
        adProductMappingDao.insertBatch(adProductMappingPoList);
        return idList;
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<Long> insertCarDarkV2(List<SdpaProductCarDto> sdpaProductCarDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPos = convertCarToAdProductTotalInfoCpcPo(sdpaProductCarDtoList);
        List<Long> idList = cpcBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);
        return idList;
    }


    public List<Long> insertImportCarV2(List<ImportAdProductCarDto> importAdProductCarDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPos = convertImportCarToAdProductTotalInfoPo(importAdProductCarDtoList);
        List<Long> idList = adBqf.insert(QAdProductTotalInfo.adProductTotalInfo).insertGetKeys(adProductTotalInfoPos);

        Map<String, AdProductTotalInfoPo> productNameMap = adProductTotalInfoPos.stream().collect(Collectors.toMap(AdProductTotalInfoPo::getAdProductName, Function.identity()));

        List<AdProductMappingPo> adProductMappingPoList = new ArrayList<>();

        for (ImportAdProductCarDto importAdProductCarDto : importAdProductCarDtoList) {
            AdProductMappingPo adProductMappingPo = new AdProductMappingPo();
            AdProductTotalInfoPo adProductTotalInfoPo = productNameMap.get(importAdProductCarDto.getName());
            if (Objects.isNull(adProductTotalInfoPo)) {
                continue;
            }

            adProductMappingPo.setAdProductId(adProductTotalInfoPo.getAdProductId());
            adProductMappingPo.setMappingId(importAdProductCarDto.getAccountId());
            adProductMappingPo.setType(AdProductBindTypeEnum.ACCOUNT.getCode());
            adProductMappingPo.setBelongType(importAdProductCarDto.getBelongType());
            adProductMappingPo.setIsDeleted(IsDeleted.VALID);
            adProductMappingPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingPoList.add(adProductMappingPo);
            if (Utils.isPositive(importAdProductCarDto.getUnitId())) {
                AdProductMappingPo adProductMappingPoUnit = new AdProductMappingPo();
                adProductMappingPoUnit.setAdProductId(adProductTotalInfoPo.getAdProductId());
                adProductMappingPoUnit.setMappingId(importAdProductCarDto.getUnitId());
                adProductMappingPoUnit.setType(AdProductBindTypeEnum.UNIT.getCode());
                adProductMappingPoUnit.setBelongType(importAdProductCarDto.getBelongType());
                adProductMappingPoUnit.setIsDeleted(IsDeleted.VALID);
                adProductMappingPoUnit.setCtime(new Timestamp(System.currentTimeMillis()));
                adProductMappingPoUnit.setMtime(new Timestamp(System.currentTimeMillis()));
                adProductMappingPoList.add(adProductMappingPoUnit);
            }
        }
        adCoreBqf.insert(QAdProductMapping.adProductMapping).insertBeans(adProductMappingPoList);
        return idList;
    }

    public void insertGoodsDarkV2(List<SdpaProductGoodsDarkDto> sdpaProductGoodsDarkDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPos = convertGoodsToAdProductTotalInfoCpcPo(sdpaProductGoodsDarkDtoList);
        cpcBqf.insert(QCPCAdProductTotalInfo.adProductTotalInfo).insertBeans(adProductTotalInfoPos);
    }

    public void insertGameDarkV2(List<SdpaProductGameDto> sdpaProductGameDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPos = convertGameToAdProductTotalInfoCpcPo(sdpaProductGameDtoList);
        cpcBqf.insert(QCPCAdProductTotalInfo.adProductTotalInfo).insertBeans(adProductTotalInfoPos);
    }

    public void updateCarV2(SdpaProductCarDto sdpaProductCarDto) {
        AdProductTotalInfoPo adProductTotalInfoPo = convertCarToAdProductTotalInfoPo(Collections.singletonList(sdpaProductCarDto)).get(0);
        adBqf.update(QAdProductTotalInfo.adProductTotalInfo)
                .populate(adProductTotalInfoPo)
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.eq(sdpaProductCarDto.getProductId()))
                .execute();
    }

    public void updateECommerceV2(SdpaProductECommerceDto sdpaProductECommerceDto) {
        AdProductTotalInfoPo adProductTotalInfoPo = convertECommerceToAdProductTotalInfoPo(Collections.singletonList(sdpaProductECommerceDto)).get(0);
        adBqf.update(QAdProductTotalInfo.adProductTotalInfo)
                .populate(adProductTotalInfoPo)
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.eq(sdpaProductECommerceDto.getProductId()))
                .execute();
    }

    public void updateCarDarkV2(List<SdpaProductCarDto> sdpaProductCarDto) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = convertCarToAdProductTotalInfoCpcPo(sdpaProductCarDto);
        adBqf.update(QCPCAdProductTotalInfo.adProductTotalInfo).updateBeans(adProductTotalInfoCPCPoList);
    }

    public void updateGoodsDarkV2(List<SdpaProductGoodsDarkDto> sdpaProductGoodsDarkDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPos = convertGoodsToAdProductTotalInfoCpcPo(sdpaProductGoodsDarkDtoList);
        cpcBqf.update(QCPCAdProductTotalInfo.adProductTotalInfo).updateBeans(adProductTotalInfoPos);
    }

    public void updateMappingDarkV2(Long oldAdProductId, Long newAdProductId, Integer mappingId, Integer type) {
        cpcBqf.update(QCPCAdProductMapping.adProductMapping).set(QCPCAdProductMapping.adProductMapping.adProductId, newAdProductId)
                .whereIfNotNull(oldAdProductId, QCPCAdProductMapping.adProductMapping.adProductId::eq)
                .where(QCPCAdProductMapping.adProductMapping.mappingId.eq(mappingId))
                .where(QCPCAdProductMapping.adProductMapping.type.eq(type))
                .where(QCPCAdProductMapping.adProductMapping.belongType.eq(AdProductBelongTypeEnum.DARK.getCode()))
                .execute();
    }

    public AdProductEducationPo getCourseById(Long productId) {
        AdProductEducationPoExample poExample = new AdProductEducationPoExample();
        poExample.or().andAdProductIdEqualTo(productId).andIsDeletedEqualTo(IsDeleted.VALID);
        var educations = adProductEducationDao.selectByExample(poExample);
        return CollectionUtils.isEmpty(educations) ? null : educations.get(0);
    }

    public AdProductBorrowLoanPo getLoanById(Long productId) {
        AdProductBorrowLoanPoExample poExample = new AdProductBorrowLoanPoExample();
        poExample.or().andAdProductIdEqualTo(productId).andIsDeletedEqualTo(IsDeleted.VALID);
        var loans = adProductBorrowLoanDao.selectByExample(poExample);
        return CollectionUtils.isEmpty(loans) ? null : loans.get(0);
    }

    public AdProductNovelPo getNovelById(Long productId) {
        AdProductNovelPoExample poExample = new AdProductNovelPoExample();
        poExample.or().andAdProductIdEqualTo(productId).andIsDeletedEqualTo(IsDeleted.VALID);
        var novels = adProductNovelDao.selectByExample(poExample);
        return CollectionUtils.isEmpty(novels) ? null : novels.get(0);
    }

    public AdProductShortFilmPo getShortFilmById(Long productId) {
        return adBqf.selectFrom(adProductShortFilm)
                .where(adProductShortFilm.adProductId.eq(productId))
                .where(adProductShortFilm.isDeleted.eq(IsDeleted.VALID))
                .fetchOne();
    }

    public AdProductCarPo getCarById(Long productId) {
        AdProductCarPoExample example = new AdProductCarPoExample();
        example.or().andAdProductIdEqualTo(productId).andIsDeletedEqualTo(IsDeleted.VALID);
        List<AdProductCarPo> adProductCarPos = adProductCarDao.selectByExample(example);
        return CollectionUtils.isEmpty(adProductCarPos) ? null : adProductCarPos.get(0);
    }


    public void updateCourseV2(SdpaProductEducationDto sdpaProductEducationDto) {
        AdProductTotalInfoPo adProductTotalInfoPo = convertEducationToAdProductTotalInfoPo(Collections.singletonList(sdpaProductEducationDto)).get(0);
        adBqf.update(QAdProductTotalInfo.adProductTotalInfo)
                .populate(adProductTotalInfoPo)
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.eq(adProductTotalInfoPo.getAdProductId()))
                .execute();
    }

    public void updateCourseDarkV2(List<SdpaProductEducationDto> sdpaProductEducationDto) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = convertEducationToAdProductTotalInfoCpcPo(sdpaProductEducationDto);
        adBqf.update(QCPCAdProductTotalInfo.adProductTotalInfo).updateBeans(adProductTotalInfoCPCPoList);
    }

    public Long insertLibrary(Integer accountId, String libraryName,
                              Integer type, Integer createType) {

        AdProductLibraryPo libraryPo = AdProductLibraryPo.builder()
                .type(type)
                .name(libraryName)
                .accountId(accountId)
                .createType(createType)
                .bizStatus(AdProductStatusEnum.ENABLE.getCode())
                .build();
        adProductLibraryDao.insertSelective(libraryPo);

        AdProductLibrarySharePo librarySharePo = AdProductLibrarySharePo.builder()
                .type(type)
                .name(libraryName)
                .accountId(accountId)
                .belongType(AdProductLibraryBelongTypeEnum.OWNER.getCode())
                .bizStatus(AdProductStatusEnum.ENABLE.getCode())
                .libraryId(libraryPo.getId())
                .build();
        adProductLibraryShareDao.insertSelective(librarySharePo);
        return libraryPo.getId();
    }

    public Long manualLabelInsertLibrary(Integer accountId, String libraryName,
                                         Integer type, Integer createType, AdProductLibraryBelongTypeEnum belongTypeEnum) {

        AdProductLibraryPo libraryPo = AdProductLibraryPo.builder()
                .type(type)
                .name(libraryName)
                .accountId(accountId)
                .createType(createType)
                .bizStatus(AdProductStatusEnum.ENABLE.getCode())
                .build();
        adProductLibraryDao.insertSelective(libraryPo);

        AdProductLibrarySharePo librarySharePo = AdProductLibrarySharePo.builder()
                .type(type)
                .name(libraryName)
                .accountId(accountId)
                .belongType(belongTypeEnum.getCode())
                .bizStatus(AdProductStatusEnum.ENABLE.getCode())
                .libraryId(libraryPo.getId())
                .build();
        adProductLibraryShareDao.insertSelective(librarySharePo);
        return libraryPo.getId();
    }

    public List<ResTargetItemPo> getAllAreaList() {

        ResTargetItemPoExample example = new ResTargetItemPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andTypeEqualTo(1);
        return resTargetItemDao.selectByExample(example);
    }

    public void deleteAllSku() {
        AdProductSkuPoExample example = new AdProductSkuPoExample();
        int count = adProductSkuDao.deleteByExample(example);
        log.info("deleteAllSku, count={}", count);
    }

    public void deleteAllSpu() {
        AdProductSpuPoExample example = new AdProductSpuPoExample();
        int count = adProductSpuDao.deleteByExample(example);
        log.info("deleteAllSpu, count={}", count);
    }

    public void deleteAllCategory() {
        AdProductCategoryPoExample example = new AdProductCategoryPoExample();
        int count = adProductCategoryDao.deleteByExample(example);
        log.info("deleteAllCategory, count={}", count);
    }

    public void deleteProductLibrary(List<Long> libraryIds) {
        AdProductLibraryPoExample example = new AdProductLibraryPoExample();
        example.or().andIdIn(libraryIds);

        int count = adProductLibraryDao.updateByExampleSelective(AdProductLibraryPo.builder()
                .isDeleted(IsDeleted.DELETED).build(), example);
        log.info("deleteProductLibrary, count={}", count);

        deleteProductShareLibrary(libraryIds, null);
    }

    public void deleteProductShareLibrary(List<Long> libraryIds, Integer accountId) {
        AdProductLibrarySharePoExample example = new AdProductLibrarySharePoExample();
        AdProductLibrarySharePoExample.Criteria criteria = example.or()
                .andLibraryIdIn(libraryIds);
        ExampleUtils.notNull(accountId, criteria::andAccountIdEqualTo);
        adProductLibraryShareDao.updateByExampleSelective(AdProductLibrarySharePo.builder()
                .isDeleted(IsDeleted.DELETED).build(), example);
    }

    /**
     * 返回的是自增id 删除使用的 注意id的含义
     *
     * @param libraryIds
     * @param accountId
     * @return
     */
    public List<Long> selectProductShareLibrary(List<Long> libraryIds, Integer accountId) {
        AdProductLibrarySharePoExample example = new AdProductLibrarySharePoExample();
        AdProductLibrarySharePoExample.Criteria criteria = example.or()
                .andLibraryIdIn(libraryIds);
        ExampleUtils.notNull(accountId, criteria::andAccountIdEqualTo);
        List<AdProductLibrarySharePo> adProductLibrarySharePoList = adProductLibraryShareDao.selectByExample(example);
        return adProductLibrarySharePoList.stream().map(AdProductLibrarySharePo::getId).collect(Collectors.toList());
    }

    public void deleteProductShareLibrary(List<Long> idList) {
        AdProductLibrarySharePoExample example = new AdProductLibrarySharePoExample();
        example.or().andIdIn(idList);
        adProductLibraryShareDao.updateByExampleSelective(AdProductLibrarySharePo.builder()
                .isDeleted(IsDeleted.DELETED).build(), example);
    }

    public void deleteAdProductsV2(List<Long> libraryIds, List<Long> productIds) {
        Assert.isTrue(!CollectionUtils.isEmpty(libraryIds) || !CollectionUtils.isEmpty(productIds),
                "删除的时候商品库id和商品id最少要传一个");
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = adBqf.selectFrom(QAdProductTotalInfo.adProductTotalInfo)
                .whereIfTrue(!CollectionUtils.isEmpty(libraryIds), () -> QAdProductTotalInfo.adProductTotalInfo.libraryId.in(libraryIds))
                .whereIfTrue(!CollectionUtils.isEmpty(productIds), () -> QAdProductTotalInfo.adProductTotalInfo.adProductId.in(productIds))
                .fetch();
        if (CollectionUtils.isEmpty(adProductTotalInfoPoList)) {
            return;
        }
        List<Long> productIdList = adProductTotalInfoPoList.stream().map(AdProductTotalInfoPo::getAdProductId)
                .collect(Collectors.toList());
        checkDeleteOrDisableProduct(productIdList);

        long deleteCount = adBqf.update(QAdProductTotalInfo.adProductTotalInfo)
                .set(QAdProductTotalInfo.adProductTotalInfo.isDeleted, IsValid.TRUE.getCode())
                .set(QAdProductTotalInfo.adProductTotalInfo.deleteTime, new Timestamp(System.currentTimeMillis()))
                .where(QAdProductTotalInfo.adProductTotalInfo.adProductId.in(productIdList))
                .execute();
        log.info("deleteAdProductTotalInfo, count={}", deleteCount);

        deleteProductMappingByProductId(productIdList);
    }


    public void deleteAdProductDarkV2(List<Long> adProductIdList) {
        cpcBqf.update(QCPCAdProductTotalInfo.adProductTotalInfo)
                .where(QCPCAdProductTotalInfo.adProductTotalInfo.adProductId.in(adProductIdList))
                .set(QCPCAdProductTotalInfo.adProductTotalInfo.isDeleted, IsValid.TRUE.getCode())
                .set(QCPCAdProductTotalInfo.adProductTotalInfo.bizStatus, AdProductStatusEnum.DISABLE.getCode())
                .set(QCPCAdProductTotalInfo.adProductTotalInfo.deleteTime, new Timestamp(System.currentTimeMillis()))
                .execute();


        cpcBqf.update(QCPCAdProductMapping.adProductMapping)
                .where(QCPCAdProductMapping.adProductMapping.adProductId.in(adProductIdList))
                .set(QCPCAdProductMapping.adProductMapping.isDeleted, IsValid.TRUE.getCode())
                .execute();

    }

    public void deleteProductMappingByProductIdDark(Long adProductId, List<Integer> mappingIds, Integer type) {
        cpcBqf.update(QCPCAdProductMapping.adProductMapping)
                .where(QCPCAdProductMapping.adProductMapping.adProductId.eq(adProductId))
                .whereIfTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(mappingIds), () -> QCPCAdProductMapping.adProductMapping.mappingId.in(mappingIds))
                .where(QCPCAdProductMapping.adProductMapping.type.eq(type))
                .set(QCPCAdProductMapping.adProductMapping.isDeleted, IsValid.TRUE.getCode())
                .execute();
    }

    public void checkDeleteOrDisableProduct(List<Long> productIdList) {
        var mappingPos = getProductMappingByProductIdsLimit(productIdList,
                AdProductBindTypeEnum.UNIT.getCode());
        if (!CollectionUtils.isEmpty(mappingPos)) {
            var unitIds = mappingPos.stream().map(AdProductMappingPo::getMappingId)
                    .collect(Collectors.toList());
            var unitDtos = cpcUnitService.queryLightUnits(QueryCpcUnitDto.builder()
                    .unitIds(unitIds).build());
            StringBuilder sb = new StringBuilder();
            sb.append("删除或置为失效失败，请确保产品库内产品未关联单元,当前有关联单元的产品：产品id:")
                    .append(productIdList.stream().map(String::valueOf)
                            .collect(Collectors.joining(","))).append(";已关联单元:");
            unitDtos.forEach(unitDto -> sb.append("[单元id:").append(unitDto.getUnitId())
                    .append("(所属账号:")
                    .append(unitDto.getAccountId()).append(")]"));
            throw new IllegalArgumentException(sb.toString());
        }
    }

    public void checkDeleteOrDisableProductSingleStatus(List<Long> productIdList, Integer accountId) {
        var mappingPos = getProductMappingByProductIdsLimit(productIdList,
                AdProductBindTypeEnum.UNIT.getCode());
        if (!CollectionUtils.isEmpty(mappingPos)) {
            var unitIds = mappingPos.stream().map(AdProductMappingPo::getMappingId)
                    .collect(Collectors.toList());
            var unitDtos = cpcUnitService.queryLightUnits(QueryCpcUnitDto.builder()
                    .unitIds(unitIds).build());
            unitDtos = unitDtos.stream().filter(cpcLightUnitDto -> Objects.equals(cpcLightUnitDto.getAccountId(), accountId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(unitDtos)) {
                return;
            }

            StringBuilder sb = new StringBuilder();
            sb.append("删除或置为失效失败，请确保产品库内产品未关联单元,当前有关联单元的产品：产品id:")
                    .append(productIdList.stream().map(String::valueOf)
                            .collect(Collectors.joining(","))).append(";已关联单元:");
            unitDtos.forEach(unitDto -> sb.append("[单元id:").append(unitDto.getUnitId())
                    .append("(所属账号:")
                    .append(unitDto.getAccountId()).append(")]"));
            throw new IllegalArgumentException(sb.toString());
        }
    }


    public void updateCategoryNameByCode(Long code, String name) {

        AdProductCategoryPo record = new AdProductCategoryPo();
        record.setName(name);
        AdProductCategoryPoExample example = new AdProductCategoryPoExample();
        example.or().andCodeEqualTo(code);
        adProductCategoryDao.updateByExampleSelective(record, example);
    }

    public List<AdProductSkuPo> getAllCategorySkuList() {

        AdProductSkuPoExample example = new AdProductSkuPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andBizStatusEqualTo(AdProductStatusEnum.ENABLE.getCode());
        return adProductSkuDao.selectByExample(example);
    }

    public List<AdProductSpuPo> getAllSpuList() {

        AdProductSpuPoExample example = new AdProductSpuPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andBizStatusEqualTo(AdProductStatusEnum.ENABLE.getCode());
        return adProductSpuDao.selectByExample(example);
    }

    public List<AdProductSkuPo> getSkuListByNames(List<String> skuNames) {
        if (CollectionUtils.isEmpty(skuNames)) {
            return Collections.EMPTY_LIST;
        }

        AdProductSkuPoExample example = new AdProductSkuPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andBizStatusEqualTo(AdProductStatusEnum.ENABLE.getCode()).andNameIn(skuNames);
        return adProductSkuDao.selectByExample(example);
    }

    public List<AdProductSpuPo> getSpuListByNames(List<String> spuNames) {
        if (CollectionUtils.isEmpty(spuNames)) {
            return Collections.EMPTY_LIST;
        }

        AdProductSpuPoExample example = new AdProductSpuPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andBizStatusEqualTo(AdProductStatusEnum.ENABLE.getCode()).andNameIn(spuNames);
        return adProductSpuDao.selectByExample(example);
    }

    public void updateSkuNameByCode(Long skuCode, String skuName) {

        AdProductSkuPo skuPo = AdProductSkuPo.builder()
                .name(skuName)
                .build();
        AdProductSkuPoExample example = new AdProductSkuPoExample();
        example.or().andCodeEqualTo(skuCode);
        adProductSkuDao.updateByExampleSelective(skuPo, example);
    }

    public void updateSpuNameByCode(Long spuCode, String spuName) {

        AdProductSpuPo skuPo = AdProductSpuPo.builder()
                .name(spuName)
                .build();
        AdProductSpuPoExample example = new AdProductSpuPoExample();
        example.or().andCodeEqualTo(spuCode);
        adProductSpuDao.updateByExampleSelective(skuPo, example);
    }

    public AdProductLibraryPo getLibraryByName(String libraryName, Integer accountId) {

        AdProductLibraryPoExample example = new AdProductLibraryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAccountIdEqualTo(accountId).andNameEqualTo(libraryName);
        List<AdProductLibraryPo> libraryPos = adProductLibraryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(libraryPos)) {
            return null;
        } else {
            return libraryPos.get(0);
        }
    }

    public AdProductLibraryPo getLibraryById(Long libraryId, Integer accountId) {

        AdProductLibraryPoExample example = new AdProductLibraryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAccountIdEqualTo(accountId).andIdEqualTo(libraryId);
        List<AdProductLibraryPo> libraryPos = adProductLibraryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(libraryPos)) {
            return null;
        } else {
            return libraryPos.get(0);
        }
    }


    public List<AccProductPo> getBrandListByNames(List<String> brandNameList) {
        if (CollectionUtils.isEmpty(brandNameList)) {
            return Collections.EMPTY_LIST;
        }

        AccProductPoExample example = new AccProductPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andNameIn(brandNameList);
        return accProductDao.selectByExample(example);
    }

    public void dealHistory() {
        AdProductLibraryPoExample example = new AdProductLibraryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andAccountIdNotIn(Lists.newArrayList(3, 1645004));
        List<AdProductLibraryPo> libraryPos = adProductLibraryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(libraryPos)) {
            return;
        } else {
            libraryPos.forEach(po -> {
                        AdProductLibrarySharePo sharePo = new AdProductLibrarySharePo();
                        BeanUtils.copyProperties(po, sharePo);
                        sharePo.setLibraryId(po.getId());
                        sharePo.setId(null);
                        adProductLibraryShareDao.insertUpdateSelective(sharePo);
                    }
            );
        }
    }

    private List<AdProductTotalInfoPo> convertEducationToAdProductTotalInfoPo(List<SdpaProductEducationDto> sdpaProductEducationDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaProductEducationDto sdpaProductEducationDto : sdpaProductEducationDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductEducationDto);
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.COURSE.getCode());
            adProductTotalInfoPo.setAdOriginalPrice(Objects.isNull(sdpaProductEducationDto.getTrialPrice()) ? "0" : sdpaProductEducationDto.getTrialPrice());
            SdpaProductEducationExtraDto extraDto = convertToSdpaProductEducationExtra(sdpaProductEducationDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(extraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    /**
     * 暗投方法
     *
     * @param sdpaProductEducationDtoList
     * @return
     */
    private List<AdProductTotalInfoCPCPo> convertEducationToAdProductTotalInfoCpcPo(List<SdpaProductEducationDto> sdpaProductEducationDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaProductEducationDto sdpaProductEducationDto : sdpaProductEducationDtoList) {
            AdProductTotalInfoCPCPo adProductTotalInfoPo = new AdProductTotalInfoCPCPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductEducationDto);
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.DARK.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.COURSE.getCode());
            adProductTotalInfoPo.setAdOriginalPrice(Objects.isNull(sdpaProductEducationDto.getTrialPrice()) ? "0" : sdpaProductEducationDto.getTrialPrice());
            SdpaProductEducationExtraDto extraDto = convertToSdpaProductEducationExtra(sdpaProductEducationDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(extraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoPo> convertImportEducationToAdProductTotalInfoPo(List<ImportAdProductEducationDto> importAdProductEducationDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (ImportAdProductEducationDto importAdProductEducationDto : importAdProductEducationDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, importAdProductEducationDto);

            adProductTotalInfoPo.setBizStatus(AdProductStatusEnum.ENABLE.getCode());
            adProductTotalInfoPo.setBelongType(importAdProductEducationDto.getBelongType());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.COURSE.getCode());
            adProductTotalInfoPo.setIsDeleted(IsValid.FALSE.getCode());
            adProductTotalInfoPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductTotalInfoPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductTotalInfoPo.setAdOriginalPrice(Objects.isNull(importAdProductEducationDto.getTrialPrice()) ? "0" : importAdProductEducationDto.getTrialPrice());
            SdpaProductEducationExtraDto extraDto = convertToSdpaProductEducationExtra(importAdProductEducationDto);

            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(extraDto));

            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoPo> convertLoanToAdProductTotalInfoPo(List<SdpaProductLoanDto> sdpaProductLoanDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaProductLoanDto sdpaProductLoanDto : sdpaProductLoanDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            adProductTotalInfoPo.setLibraryId(sdpaProductLoanDto.getLibraryId());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.BORROW_LOAN.getCode());
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductLoanDto);
            SdpaProductLoanExtraDto sdpaProductLoanExtraDto = convertToSdpaProductLoanExtra(sdpaProductLoanDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductLoanExtraDto));

            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    /**
     * 暗投方法
     *
     * @param sdpaProductLoanDtoList
     * @return
     */
    private List<AdProductTotalInfoCPCPo> convertLoanToAdProductTotalInfoCpcPo(List<SdpaProductLoanDto> sdpaProductLoanDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaProductLoanDto sdpaProductLoanDto : sdpaProductLoanDtoList) {
            AdProductTotalInfoCPCPo adProductTotalInfoPo = new AdProductTotalInfoCPCPo();
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.DARK.getCode());
            adProductTotalInfoPo.setLibraryId(sdpaProductLoanDto.getLibraryId());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.BORROW_LOAN.getCode());
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductLoanDto);
            SdpaProductLoanExtraDto sdpaProductLoanExtraDto = convertToSdpaProductLoanExtra(sdpaProductLoanDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductLoanExtraDto));

            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoPo> convertImportLoanToAdProductTotalInfoPo(List<ImportAdProductLoanDto> sdpaProductLoanDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (ImportAdProductLoanDto sdpaProductLoanDto : sdpaProductLoanDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            adProductTotalInfoPo.setBizStatus(AdProductStatusEnum.ENABLE.getCode());
            adProductTotalInfoPo.setBelongType(sdpaProductLoanDto.getBelongType());
            adProductTotalInfoPo.setLibraryId(sdpaProductLoanDto.getLibraryId());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.BORROW_LOAN.getCode());

            adProductTotalInfoPo.setIsDeleted(IsValid.FALSE.getCode());
            adProductTotalInfoPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductTotalInfoPo.setMtime(new Timestamp(System.currentTimeMillis()));

            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductLoanDto);

            SdpaProductLoanExtraDto sdpaProductLoanExtraDto = convertToSdpaProductLoanExtra(sdpaProductLoanDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductLoanExtraDto));

            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoPo> convertNovelToAdProductTotalInfoPo(List<SdpaProductNovelDto> sdpaProductNovelDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaProductNovelDto sdpaProductNovelDto : sdpaProductNovelDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductNovelDto);
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.NOVEL.getCode());
            adProductTotalInfoPo.setAdOriginalPrice(Objects.isNull(sdpaProductNovelDto.getChapterPrice()) ? "0" : sdpaProductNovelDto.getChapterPrice());
            SdpaProductNovelExtraDto sdpaProductNovelExtraDto = convertToSdpaProductNovelExtra(sdpaProductNovelDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductNovelExtraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoCPCPo> convertNovelToAdProductTotalInfoCpcPo(List<SdpaProductNovelDto> sdpaProductNovelDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaProductNovelDto sdpaProductNovelDto : sdpaProductNovelDtoList) {
            AdProductTotalInfoCPCPo adProductTotalInfoPo = new AdProductTotalInfoCPCPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductNovelDto);
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.DARK.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.NOVEL.getCode());
            adProductTotalInfoPo.setAdOriginalPrice(Objects.isNull(sdpaProductNovelDto.getChapterPrice()) ? "0" : sdpaProductNovelDto.getChapterPrice());
            SdpaProductNovelExtraDto sdpaProductNovelExtraDto = convertToSdpaProductNovelExtra(sdpaProductNovelDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductNovelExtraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoPo> convertImportNovelToAdProductTotalInfoPo(List<ImportAdProductNovelDto> sdpaProductNovelDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (ImportAdProductNovelDto sdpaProductNovelDto : sdpaProductNovelDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductNovelDto);
            adProductTotalInfoPo.setBizStatus(AdProductStatusEnum.ENABLE.getCode());
            adProductTotalInfoPo.setBelongType(sdpaProductNovelDto.getBelongType());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.NOVEL.getCode());
            adProductTotalInfoPo.setIsDeleted(IsValid.FALSE.getCode());
            adProductTotalInfoPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductTotalInfoPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductTotalInfoPo.setAdOriginalPrice(sdpaProductNovelDto.getChapterPrice());
            SdpaProductNovelExtraDto sdpaProductNovelExtraDto = convertToSdpaProductNovelExtra(sdpaProductNovelDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductNovelExtraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoPo> convertShortFilmToAdProductTotalInfoPo(List<SdpaShortFilmDto> sdpaShortFilmDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaShortFilmDto sdpaShortFilmDto : sdpaShortFilmDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaShortFilmDto);
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.SHORT_FILM.getCode());
            adProductTotalInfoPo.setAdOriginalPrice(String.valueOf(sdpaShortFilmDto.getEpisodePrice()));
            SdpaProductShortFilmExtraDto sdpaShortFilmExtraDto = convertToSdpaProductShortFilmExtra(sdpaShortFilmDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaShortFilmExtraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoCPCPo> convertShortFilmToAdProductTotalInfoCpcPo(List<SdpaShortFilmDto> sdpaShortFilmDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaShortFilmDto sdpaShortFilmDto : sdpaShortFilmDtoList) {
            AdProductTotalInfoCPCPo adProductTotalInfoPo = new AdProductTotalInfoCPCPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaShortFilmDto);
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.DARK.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.SHORT_FILM.getCode());
            adProductTotalInfoPo.setAdOriginalPrice(String.valueOf(sdpaShortFilmDto.getEpisodePrice()));
            SdpaProductShortFilmExtraDto sdpaShortFilmExtraDto = convertToSdpaProductShortFilmExtra(sdpaShortFilmDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaShortFilmExtraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoPo> convertImportShortFilmToAdProductTotalInfoPo(List<ImportAdProductShortFilmDto> sdpaShortFilmDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (ImportAdProductShortFilmDto sdpaShortFilmDto : sdpaShortFilmDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaShortFilmDto);
            adProductTotalInfoPo.setBizStatus(AdProductStatusEnum.ENABLE.getCode());
            adProductTotalInfoPo.setBelongType(sdpaShortFilmDto.getBelongType());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.SHORT_FILM.getCode());
            adProductTotalInfoPo.setIsDeleted(IsValid.FALSE.getCode());
            adProductTotalInfoPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductTotalInfoPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductTotalInfoPo.setAdOriginalPrice(String.valueOf(sdpaShortFilmDto.getEpisodePrice()));
            SdpaProductShortFilmExtraDto sdpaShortFilmExtraDto = convertToSdpaProductShortFilmExtra(sdpaShortFilmDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaShortFilmExtraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoPo> convertCarToAdProductTotalInfoPo(List<SdpaProductCarDto> sdpaProductCarDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaProductCarDto sdpaProductCarDto : sdpaProductCarDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductCarDto);
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.CAR.getCode());
            SdpaProductCarExtraDto sdpaProductCarExtraDto = convertToSdpaProductCarExtra(sdpaProductCarDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductCarExtraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoCPCPo> convertCarToAdProductTotalInfoCpcPo(List<SdpaProductCarDto> sdpaProductCarDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaProductCarDto sdpaProductCarDto : sdpaProductCarDtoList) {
            AdProductTotalInfoCPCPo adProductTotalInfoPo = new AdProductTotalInfoCPCPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductCarDto);
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.DARK.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.CAR.getCode());

            SdpaProductCarExtraDto sdpaProductCarExtraDto = convertToSdpaProductCarExtra(sdpaProductCarDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductCarExtraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoPo> convertImportCarToAdProductTotalInfoPo(List<ImportAdProductCarDto> sdpaProductCarDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (ImportAdProductCarDto sdpaProductCarDto : sdpaProductCarDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            adProductTotalInfoPo.setBizStatus(AdProductStatusEnum.ENABLE.getCode());
            adProductTotalInfoPo.setBelongType(sdpaProductCarDto.getBelongType());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.CAR.getCode());
            adProductTotalInfoPo.setIsDeleted(IsValid.FALSE.getCode());
            adProductTotalInfoPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductTotalInfoPo.setMtime(new Timestamp(System.currentTimeMillis()));
            SdpaProductCarExtraDto sdpaProductCarExtraDto = convertToSdpaProductCarExtra(sdpaProductCarDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductCarExtraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoPo> convertECommerceToAdProductTotalInfoPo(List<SdpaProductECommerceDto> sdpaProductECommerceDtoList) {
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = new ArrayList<>();
        for (SdpaProductECommerceDto sdpaProductECommerceDto : sdpaProductECommerceDtoList) {
            AdProductTotalInfoPo adProductTotalInfoPo = new AdProductTotalInfoPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductECommerceDto);
            adProductTotalInfoPo.setBizStatus(AdProductStatusEnum.ENABLE.getCode());
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.E_COMMERCE.getCode());
            adProductTotalInfoPo.setIsDeleted(IsValid.FALSE.getCode());
            adProductTotalInfoPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductTotalInfoPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductTotalInfoPo.setAdOriginalPrice(Objects.isNull(sdpaProductECommerceDto.getAdOriginalPrice()) ? "0" : sdpaProductECommerceDto.getAdOriginalPrice());
            SdpaProductECommerceExtraDto sdpaProductECommerceExtraDto = convertToSdpaProductECommerceExtra(sdpaProductECommerceDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductECommerceExtraDto));
            adProductTotalInfoPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoPoList;
    }

    private List<AdProductTotalInfoCPCPo> convertGoodsToAdProductTotalInfoCpcPo(List<SdpaProductGoodsDarkDto> sdpaProductGoodsDarkDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = new ArrayList<>();
        for (SdpaProductGoodsDarkDto sdpaProductGoodsDarkDto : sdpaProductGoodsDarkDtoList) {
            AdProductTotalInfoCPCPo adProductTotalInfoPo = new AdProductTotalInfoCPCPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductGoodsDarkDto);
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.DARK.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.GOODS.getCode());
            adProductTotalInfoPo.setAdOriginalPrice(sdpaProductGoodsDarkDto.getAdOriginalPrice());
            adProductTotalInfoPo.setAdAttributes(sdpaProductGoodsDarkDto.getAdAttributes());
            adProductTotalInfoPo.setUniqueKey(sdpaProductGoodsDarkDto.getUniqueKey());
            adProductTotalInfoCPCPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoCPCPoList;
    }

    private List<AdProductTotalInfoCPCPo> convertGameToAdProductTotalInfoCpcPo(List<SdpaProductGameDto> sdpaProductGameDtoList) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = new ArrayList<>();
        for (SdpaProductGameDto sdpaProductGameDto : sdpaProductGameDtoList) {
            AdProductTotalInfoCPCPo adProductTotalInfoPo = new AdProductTotalInfoCPCPo();
            supplyAdProductCommonDto(adProductTotalInfoPo, sdpaProductGameDto);
            adProductTotalInfoPo.setBelongType(AdProductBelongTypeEnum.DARK.getCode());
            adProductTotalInfoPo.setLibraryType(AdProductLibraryTypeEnum.GAME.getCode());
            adProductTotalInfoPo.setUniqueKey(sdpaProductGameDto.getUniqueKey());
            SdpaProductGameExtraDto sdpaProductGameExtraDto = convertToSdpaProductGameExtra(sdpaProductGameDto);
            adProductTotalInfoPo.setAdAttributes(JSON.toJSONString(sdpaProductGameExtraDto));
            adProductTotalInfoCPCPoList.add(adProductTotalInfoPo);
        }
        return adProductTotalInfoCPCPoList;
    }


    private SdpaProductEducationExtraDto convertToSdpaProductEducationExtra(SdpaProductEducationDto sdpaProductEducationDto) {
        SdpaProductEducationExtraDto extraDto = new SdpaProductEducationExtraDto();
        extraDto.setIosUpUlinkUrl(sdpaProductEducationDto.getIosUpUlinkUrl());
        extraDto.setBrand(sdpaProductEducationDto.getBrand());
        extraDto.setBrandName(sdpaProductEducationDto.getBrandName());
        extraDto.setSpuCode(sdpaProductEducationDto.getSpuCode());
        extraDto.setSpuName(sdpaProductEducationDto.getSpuName());
        extraDto.setRemark(sdpaProductEducationDto.getRemark());
        extraDto.setArea(sdpaProductEducationDto.getArea());
        extraDto.setAge(sdpaProductEducationDto.getAge());
        extraDto.setTeachChannel(sdpaProductEducationDto.getTeachChannel());
        extraDto.setTeachType(sdpaProductEducationDto.getTeachType());
        extraDto.setTrialClass(sdpaProductEducationDto.getTrialClass());
        extraDto.setBrief(sdpaProductEducationDto.getBrief());
        extraDto.setPositivePrice(sdpaProductEducationDto.getPositivePrice());
        return extraDto;
    }

    private SdpaProductEducationExtraDto convertToSdpaProductEducationExtra(ImportAdProductEducationDto sdpaProductEducationDto) {
        SdpaProductEducationExtraDto extraDto = new SdpaProductEducationExtraDto();
        extraDto.setIosUpUlinkUrl(sdpaProductEducationDto.getIosUpUlinkUrl());
        extraDto.setBrand(sdpaProductEducationDto.getBrand());
        extraDto.setSpuCode(sdpaProductEducationDto.getSpuCode());
        extraDto.setSpuName(sdpaProductEducationDto.getSpuName());
        extraDto.setRemark(sdpaProductEducationDto.getRemark());
        extraDto.setArea(sdpaProductEducationDto.getArea());
        extraDto.setAge(sdpaProductEducationDto.getAge());
        extraDto.setTeachChannel(sdpaProductEducationDto.getTeachChannel());
        extraDto.setTeachType(sdpaProductEducationDto.getTeachType());
        extraDto.setTrialClass(sdpaProductEducationDto.getTrialClass());
        extraDto.setBrief(sdpaProductEducationDto.getBrief());
        extraDto.setPositivePrice(sdpaProductEducationDto.getPositivePrice());
        return extraDto;
    }

    private SdpaProductLoanExtraDto convertToSdpaProductLoanExtra(SdpaProductLoanDto sdpaProductLoanDto) {
        SdpaProductLoanExtraDto sdpaProductLoanExtraDto = new SdpaProductLoanExtraDto();
        sdpaProductLoanExtraDto.setMaxYearRate(sdpaProductLoanDto.getMaxYearRate());
        sdpaProductLoanExtraDto.setSpuCode(sdpaProductLoanDto.getSpuCode());
        sdpaProductLoanExtraDto.setSpuName(sdpaProductLoanDto.getSpuName());
        sdpaProductLoanExtraDto.setMaxCreditLimit(sdpaProductLoanDto.getMaxCreditLimit());
        sdpaProductLoanExtraDto.setAverageCreditLimit(sdpaProductLoanDto.getAverageCreditLimit());
        sdpaProductLoanExtraDto.setLoanTerm(sdpaProductLoanDto.getLoanTerm());
        sdpaProductLoanExtraDto.setLoanUsage(sdpaProductLoanDto.getLoanUsage());
        sdpaProductLoanExtraDto.setDailyInterest(sdpaProductLoanDto.getDailyInterest());
        sdpaProductLoanExtraDto.setInterestType(sdpaProductLoanDto.getInterestType());
        sdpaProductLoanExtraDto.setRapaymentType(sdpaProductLoanDto.getRapaymentType());
        sdpaProductLoanExtraDto.setEarlyRepayment(sdpaProductLoanDto.getEarlyRepayment());
        sdpaProductLoanExtraDto.setEarlyRepaymentPenalty(sdpaProductLoanDto.getEarlyRepaymentPenalty());
        return sdpaProductLoanExtraDto;
    }

    private SdpaProductLoanExtraDto convertToSdpaProductLoanExtra(ImportAdProductLoanDto sdpaProductLoanDto) {
        SdpaProductLoanExtraDto sdpaProductLoanExtraDto = new SdpaProductLoanExtraDto();
        sdpaProductLoanExtraDto.setMaxYearRate(sdpaProductLoanDto.getMaxYearRate());
        sdpaProductLoanExtraDto.setSpuCode(sdpaProductLoanDto.getSpuCode());
        sdpaProductLoanExtraDto.setSpuName(sdpaProductLoanDto.getSpuName());
        sdpaProductLoanExtraDto.setMaxCreditLimit(sdpaProductLoanDto.getMaxCreditLimit());
        sdpaProductLoanExtraDto.setAverageCreditLimit(sdpaProductLoanDto.getAverageCreditLimit());
        sdpaProductLoanExtraDto.setLoanTerm(sdpaProductLoanDto.getLoanTerm());
        sdpaProductLoanExtraDto.setLoanUsage(sdpaProductLoanDto.getLoanUsage());
        sdpaProductLoanExtraDto.setDailyInterest(sdpaProductLoanDto.getDailyInterest());
        sdpaProductLoanExtraDto.setInterestType(sdpaProductLoanDto.getInterestType());
        sdpaProductLoanExtraDto.setRapaymentType(sdpaProductLoanDto.getRapaymentType());
        sdpaProductLoanExtraDto.setEarlyRepayment(sdpaProductLoanDto.getEarlyRepayment());
        sdpaProductLoanExtraDto.setEarlyRepaymentPenalty(sdpaProductLoanDto.getEarlyRepaymentPenalty());
        return sdpaProductLoanExtraDto;
    }

    private SdpaProductNovelExtraDto convertToSdpaProductNovelExtra(SdpaProductNovelDto sdpaProductNovelDto) {
        SdpaProductNovelExtraDto sdpaProductNovelExtraDto = new SdpaProductNovelExtraDto();
        sdpaProductNovelExtraDto.setChannel(sdpaProductNovelDto.getChannel());
        sdpaProductNovelExtraDto.setBrief(sdpaProductNovelDto.getBrief());
        sdpaProductNovelExtraDto.setTheme(sdpaProductNovelDto.getTheme());
        sdpaProductNovelExtraDto.setChapterNum(sdpaProductNovelDto.getChapterNum());
        sdpaProductNovelExtraDto.setWordsNum(sdpaProductNovelDto.getWordsNum());
        sdpaProductNovelExtraDto.setUpdateStatus(sdpaProductNovelDto.getUpdateStatus());
        sdpaProductNovelExtraDto.setRealizationMethod(sdpaProductNovelDto.getRealizationMethod());
        sdpaProductNovelExtraDto.setFirstPayChapter(sdpaProductNovelDto.getFirstPayChapter());
        sdpaProductNovelExtraDto.setAuthor(sdpaProductNovelDto.getAuthor());
        return sdpaProductNovelExtraDto;
    }

    private SdpaProductNovelExtraDto convertToSdpaProductNovelExtra(ImportAdProductNovelDto sdpaProductNovelDto) {
        SdpaProductNovelExtraDto sdpaProductNovelExtraDto = new SdpaProductNovelExtraDto();
        sdpaProductNovelExtraDto.setChannel(sdpaProductNovelDto.getChannel());
        sdpaProductNovelExtraDto.setBrief(sdpaProductNovelDto.getBrief());
        sdpaProductNovelExtraDto.setTheme(sdpaProductNovelDto.getTheme());
        sdpaProductNovelExtraDto.setChapterNum(sdpaProductNovelDto.getChapterNum());
        sdpaProductNovelExtraDto.setWordsNum(sdpaProductNovelDto.getWordsNum());
        sdpaProductNovelExtraDto.setUpdateStatus(sdpaProductNovelDto.getUpdateStatus());
        sdpaProductNovelExtraDto.setRealizationMethod(sdpaProductNovelDto.getRealizationMethod());
        sdpaProductNovelExtraDto.setFirstPayChapter(sdpaProductNovelDto.getFirstPayChapter());
        sdpaProductNovelExtraDto.setAuthor(sdpaProductNovelDto.getAuthor());
        return sdpaProductNovelExtraDto;
    }

    private SdpaProductShortFilmExtraDto convertToSdpaProductShortFilmExtra(SdpaShortFilmDto shortFilmDto) {
        shortFilmDto.setLink(shortFilmDto.getLinkArray().stream().map(String::valueOf).collect(Collectors.joining(",")));
        SdpaProductShortFilmExtraDto sdpaProductShortFilmExtraDto = new SdpaProductShortFilmExtraDto();
        sdpaProductShortFilmExtraDto.setChannel(shortFilmDto.getChannel());
        sdpaProductShortFilmExtraDto.setBrief(shortFilmDto.getBrief());
        sdpaProductShortFilmExtraDto.setTheme(shortFilmDto.getTheme());
        sdpaProductShortFilmExtraDto.setEpisodeNum(shortFilmDto.getEpisodeNum());
        sdpaProductShortFilmExtraDto.setEpisodeDuration(shortFilmDto.getEpisodeDuration());
        sdpaProductShortFilmExtraDto.setUpdateStatus(shortFilmDto.getUpdateStatus());
        sdpaProductShortFilmExtraDto.setIsNew(shortFilmDto.getIsNew());
        sdpaProductShortFilmExtraDto.setLink(shortFilmDto.getLinkArray().stream().map(String::valueOf).collect(Collectors.joining(",")));
        sdpaProductShortFilmExtraDto.setLinkArray(shortFilmDto.getLinkArray().stream().map(String::valueOf).collect(Collectors.joining(",")));
        sdpaProductShortFilmExtraDto.setLeaderActor(shortFilmDto.getLeaderActor());
        sdpaProductShortFilmExtraDto.setDirector(shortFilmDto.getDirector());
        sdpaProductShortFilmExtraDto.setFirstPayEpisode(shortFilmDto.getFirstPayEpisode());
        sdpaProductShortFilmExtraDto.setMinTopUp(shortFilmDto.getMinTopUp());
        sdpaProductShortFilmExtraDto.setMaxTopUp(shortFilmDto.getMaxTopUp());
        sdpaProductShortFilmExtraDto.setMicroLandingPageUrl(shortFilmDto.getMicroLandingPageUrl());
        sdpaProductShortFilmExtraDto.setIsMotivationContent(shortFilmDto.getIsMotivationContent());
        sdpaProductShortFilmExtraDto.setOnlineTime(shortFilmDto.getOnlineTime());
        return sdpaProductShortFilmExtraDto;

    }

    private SdpaProductShortFilmExtraDto convertToSdpaProductShortFilmExtra(ImportAdProductShortFilmDto shortFilmDto) {
        SdpaProductShortFilmExtraDto sdpaProductShortFilmExtraDto = new SdpaProductShortFilmExtraDto();
        sdpaProductShortFilmExtraDto.setChannel(shortFilmDto.getChannel());
        sdpaProductShortFilmExtraDto.setBrief(shortFilmDto.getBrief());
        sdpaProductShortFilmExtraDto.setTheme(shortFilmDto.getTheme());
        sdpaProductShortFilmExtraDto.setEpisodeNum(shortFilmDto.getEpisodeNum());
        sdpaProductShortFilmExtraDto.setEpisodeDuration(shortFilmDto.getEpisodeDuration());
        sdpaProductShortFilmExtraDto.setUpdateStatus(shortFilmDto.getUpdateStatus());
        sdpaProductShortFilmExtraDto.setIsNew(shortFilmDto.getIsNew());
        sdpaProductShortFilmExtraDto.setLink(shortFilmDto.getLink());
        sdpaProductShortFilmExtraDto.setLinkArray(shortFilmDto.getLink());
        sdpaProductShortFilmExtraDto.setLeaderActor(shortFilmDto.getLeaderActor());
        sdpaProductShortFilmExtraDto.setDirector(shortFilmDto.getDirector());
        sdpaProductShortFilmExtraDto.setFirstPayEpisode(shortFilmDto.getFirstPayEpisode());
        sdpaProductShortFilmExtraDto.setMinTopUp(new BigDecimal(shortFilmDto.getMinTopUp()));
        sdpaProductShortFilmExtraDto.setMaxTopUp(new BigDecimal(shortFilmDto.getMaxTopUp()));
        sdpaProductShortFilmExtraDto.setMicroLandingPageUrl(shortFilmDto.getMicroLandingPageUrl());
        sdpaProductShortFilmExtraDto.setIsMotivationContent(shortFilmDto.getIsMotivationContent());
        sdpaProductShortFilmExtraDto.setOnlineTime(shortFilmDto.getOnlineTime());
        return sdpaProductShortFilmExtraDto;

    }

    private SdpaProductCarExtraDto convertToSdpaProductCarExtra(SdpaProductCarDto sdpaProductCarDto) {
        SdpaProductCarExtraDto sdpaProductCarExtraDto = new SdpaProductCarExtraDto();
        sdpaProductCarExtraDto.setBrand(sdpaProductCarDto.getBrand());
        sdpaProductCarExtraDto.setBrandName(sdpaProductCarDto.getBrandName());
        sdpaProductCarExtraDto.setModel(sdpaProductCarDto.getModel());
        sdpaProductCarExtraDto.setSpuCode(sdpaProductCarDto.getSpuCode());
        sdpaProductCarExtraDto.setSpuName(sdpaProductCarDto.getSpuName());
        sdpaProductCarExtraDto.setFuelType(sdpaProductCarDto.getFuelType());
        sdpaProductCarExtraDto.setPriceRange(sdpaProductCarDto.getPriceRange());
        return sdpaProductCarExtraDto;
    }

    private SdpaProductCarExtraDto convertToSdpaProductCarExtra(ImportAdProductCarDto sdpaProductCarDto) {
        SdpaProductCarExtraDto sdpaProductCarExtraDto = new SdpaProductCarExtraDto();
        sdpaProductCarExtraDto.setBrand(sdpaProductCarDto.getBrand());
        sdpaProductCarExtraDto.setModel(sdpaProductCarDto.getModel());
        sdpaProductCarExtraDto.setSpuCode(sdpaProductCarDto.getSpuCode());
        sdpaProductCarExtraDto.setSpuName(sdpaProductCarDto.getSpuName());
        return sdpaProductCarExtraDto;
    }

    private SdpaProductECommerceExtraDto convertToSdpaProductECommerceExtra(SdpaProductECommerceDto sdpaProductECommerceDto) {
        SdpaProductECommerceExtraDto sdpaProductECommerceExtraDto = new SdpaProductECommerceExtraDto();
        sdpaProductECommerceExtraDto.setSpuCode(sdpaProductECommerceDto.getSpuCode());
        sdpaProductECommerceExtraDto.setSpuName(sdpaProductECommerceDto.getSpuName());
        sdpaProductECommerceExtraDto.setPlatformName(sdpaProductECommerceDto.getPlatformName());
        sdpaProductECommerceExtraDto.setRemark(sdpaProductECommerceDto.getRemark());
        sdpaProductECommerceExtraDto.setStoreName(sdpaProductECommerceDto.getStoreName());
        sdpaProductECommerceExtraDto.setAge(sdpaProductECommerceDto.getAge());
        sdpaProductECommerceExtraDto.setOriginalPrice(sdpaProductECommerceDto.getOriginalPrice());
        sdpaProductECommerceExtraDto.setDiscount(sdpaProductECommerceDto.getDiscount());
        sdpaProductECommerceExtraDto.setVideoLink(sdpaProductECommerceDto.getVideoLink());
        sdpaProductECommerceExtraDto.setIosUpUlinkUrl(sdpaProductECommerceDto.getIosUpUlinkUrl());
        return sdpaProductECommerceExtraDto;
    }

    private SdpaProductGameExtraDto convertToSdpaProductGameExtra(SdpaProductGameDto sdpaProductGameDto) {
        SdpaProductGameExtraDto sdpaProductGameExtraDto = new SdpaProductGameExtraDto();
        sdpaProductGameExtraDto.setGameName(sdpaProductGameDto.getGameName());
        sdpaProductGameExtraDto.setPlatform(sdpaProductGameDto.getPlatform());
        sdpaProductGameExtraDto.setPlay1(sdpaProductGameDto.getPlay1());
        sdpaProductGameExtraDto.setPlay2(sdpaProductGameDto.getPlay2());
        sdpaProductGameExtraDto.setPlay3(sdpaProductGameDto.getPlay3());
        sdpaProductGameExtraDto.setIp(sdpaProductGameDto.getIp());
        sdpaProductGameExtraDto.setTheme1(sdpaProductGameDto.getTheme1());
        sdpaProductGameExtraDto.setTheme2(sdpaProductGameDto.getTheme2());
        sdpaProductGameExtraDto.setTheme3(sdpaProductGameDto.getTheme3());
        sdpaProductGameExtraDto.setStyle1(sdpaProductGameDto.getStyle1());
        sdpaProductGameExtraDto.setStyle2(sdpaProductGameDto.getStyle2());
        sdpaProductGameExtraDto.setElement(sdpaProductGameDto.getElement());
        sdpaProductGameExtraDto.setManufacturer(sdpaProductGameDto.getManufacturer());
        sdpaProductGameExtraDto.setRole(sdpaProductGameDto.getRole());
        sdpaProductGameExtraDto.setPayToPlay(sdpaProductGameDto.getPayToPlay());

        return sdpaProductGameExtraDto;
    }


    private <T extends SdpaProductCommonDto> void supplyAdProductCommonDto(AdProductTotalInfoPo adProductTotalInfoPo, T adProductCommonDto) {
        adProductTotalInfoPo.setAdProductId(adProductCommonDto.getProductId());
        adProductTotalInfoPo.setFirstCategoryId(adProductCommonDto.getFirstCategoryId());
        adProductTotalInfoPo.setSecondCategoryId(adProductCommonDto.getSecondCategoryId());
        adProductTotalInfoPo.setThirdCategoryId(adProductCommonDto.getThirdCategoryId());
        adProductTotalInfoPo.setFirstCategoryName(adProductCommonDto.getFirstCategoryName());
        adProductTotalInfoPo.setSecondCategoryName(adProductCommonDto.getSecondCategoryName());
        adProductTotalInfoPo.setThirdCategoryName(adProductCommonDto.getThirdCategoryName());
        adProductTotalInfoPo.setAdProductName(adProductCommonDto.getAdProductName());
        adProductTotalInfoPo.setAccountId(adProductCommonDto.getAccountId());
        adProductTotalInfoPo.setLibraryId(adProductCommonDto.getLibraryId());
        adProductTotalInfoPo.setAdMainImgUrl(Objects.isNull(adProductCommonDto.getAdMainImgUrl()) ? "" : adProductCommonDto.getAdMainImgUrl());
        adProductTotalInfoPo.setAdExtraImgUrl(Objects.isNull(adProductCommonDto.getAdSubImgUrl()) ? "" : adProductCommonDto.getAdSubImgUrl());
        adProductTotalInfoPo.setH5LandingPageUrl(Objects.isNull(adProductCommonDto.getH5LandingPageUrl()) ? "" : adProductCommonDto.getH5LandingPageUrl());
        adProductTotalInfoPo.setPcLandingPageUrl(Objects.isNull(adProductCommonDto.getPcLandingPageUrl()) ? "" : adProductCommonDto.getPcLandingPageUrl());
        adProductTotalInfoPo.setIosLandingPageUrl(Objects.isNull(adProductCommonDto.getIosLandingPageUrl()) ? "" : adProductCommonDto.getIosLandingPageUrl());
        adProductTotalInfoPo.setAndroidLandingPageUrl(Objects.isNull(adProductCommonDto.getAndroidLandingPageUrl()) ? "" : adProductCommonDto.getAndroidLandingPageUrl());

    }

    private <T extends SdpaProductCommonDto> void supplyAdProductCommonDto(AdProductTotalInfoCPCPo adProductTotalInfoPo, T adProductCommonDto) {
        adProductTotalInfoPo.setAdProductId(adProductCommonDto.getProductId());
        adProductTotalInfoPo.setFirstCategoryId(adProductCommonDto.getFirstCategoryId());
        adProductTotalInfoPo.setSecondCategoryId(adProductCommonDto.getSecondCategoryId());
        adProductTotalInfoPo.setThirdCategoryId(adProductCommonDto.getThirdCategoryId());
        adProductTotalInfoPo.setFirstCategoryName(adProductCommonDto.getFirstCategoryName());
        adProductTotalInfoPo.setSecondCategoryName(adProductCommonDto.getSecondCategoryName());
        adProductTotalInfoPo.setThirdCategoryName(adProductCommonDto.getThirdCategoryName());
        adProductTotalInfoPo.setAdProductName(adProductCommonDto.getAdProductName());
        adProductTotalInfoPo.setAccountId(adProductCommonDto.getAccountId());
        adProductTotalInfoPo.setLibraryId(adProductCommonDto.getLibraryId());
        adProductTotalInfoPo.setAdMainImgUrl(adProductCommonDto.getAdMainImgUrl());
        adProductTotalInfoPo.setAdExtraImgUrl(adProductCommonDto.getAdSubImgUrl());
        adProductTotalInfoPo.setH5LandingPageUrl(adProductCommonDto.getH5LandingPageUrl());
        adProductTotalInfoPo.setPcLandingPageUrl(adProductCommonDto.getPcLandingPageUrl());
        adProductTotalInfoPo.setIosLandingPageUrl(adProductCommonDto.getIosLandingPageUrl());
        adProductTotalInfoPo.setAndroidLandingPageUrl(adProductCommonDto.getAndroidLandingPageUrl());
        adProductTotalInfoPo.setUniqueKey(adProductCommonDto.getUniqueKey());
    }

    private <T extends ImportAdProductBaseDto> void supplyAdProductCommonDto(AdProductTotalInfoPo adProductTotalInfoPo, T importAdProductBaseDto) {
        adProductTotalInfoPo.setAdProductId(importAdProductBaseDto.getAdProductId());
        adProductTotalInfoPo.setFirstCategoryId(importAdProductBaseDto.getFirstCategoryCode());
        adProductTotalInfoPo.setSecondCategoryId(importAdProductBaseDto.getSecondCategoryCode());
        adProductTotalInfoPo.setThirdCategoryId(importAdProductBaseDto.getThirdCategoryCode());
        adProductTotalInfoPo.setFirstCategoryName(importAdProductBaseDto.getFirstCategoryName());
        adProductTotalInfoPo.setSecondCategoryName(importAdProductBaseDto.getSecondCategoryName());
        adProductTotalInfoPo.setThirdCategoryName(importAdProductBaseDto.getThirdCategoryName());
        adProductTotalInfoPo.setAdProductName(importAdProductBaseDto.getName());
        adProductTotalInfoPo.setAccountId(importAdProductBaseDto.getAccountId());
        adProductTotalInfoPo.setLibraryId(importAdProductBaseDto.getLibraryId());
        adProductTotalInfoPo.setAdMainImgUrl(importAdProductBaseDto.getMainImgUrl());
        adProductTotalInfoPo.setAdExtraImgUrl(importAdProductBaseDto.getSubImgUrl());
        adProductTotalInfoPo.setH5LandingPageUrl(importAdProductBaseDto.getH5LandingPageUrl());
        adProductTotalInfoPo.setPcLandingPageUrl(importAdProductBaseDto.getPcLandingPageUrl());
        adProductTotalInfoPo.setIosLandingPageUrl(importAdProductBaseDto.getIosLandingPageUrl());
        adProductTotalInfoPo.setAndroidLandingPageUrl(importAdProductBaseDto.getAndroidLandingPageUrl());

    }

    List<AdProductMappingCPCPo> convertSdpaProductMappingAddDto(List<SdpaProductMappingDarkDto> sdpaProductMappingList) {
        List<AdProductMappingCPCPo> adProductMappingCPCPoList = new ArrayList<>();

        for (SdpaProductMappingDarkDto sdpaProductMappingDto : sdpaProductMappingList) {

            AdProductMappingCPCPo adProductMappingCPCPo = new AdProductMappingCPCPo();
            adProductMappingCPCPo.setAdProductId(sdpaProductMappingDto.getAdProductId());
            adProductMappingCPCPo.setMappingId(sdpaProductMappingDto.getMappingId());
            adProductMappingCPCPo.setType(sdpaProductMappingDto.getType());
            adProductMappingCPCPo.setIsDeleted(IsValid.FALSE.getCode());
            adProductMappingCPCPo.setCtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingCPCPo.setMtime(new Timestamp(System.currentTimeMillis()));
            adProductMappingCPCPo.setBelongType(AdProductBelongTypeEnum.DARK.getCode());
            adProductMappingCPCPoList.add(adProductMappingCPCPo);

        }

        return adProductMappingCPCPoList;
    }


    public void insertAdProductMappingCPCPoList(List<SdpaProductMappingDarkDto> sdpaProductMappingList) {
        List<AdProductMappingCPCPo> adProductMappingCPCPoList = convertSdpaProductMappingAddDto(sdpaProductMappingList);
        cpcBqf.insert(QCPCAdProductMapping.adProductMapping).insertBeans(adProductMappingCPCPoList);
    }

    public void insertAdProductMappingCPCPo(AdProductMappingCPCPo adProductMappingCPCPo) {
        cpcBqf.insert(QCPCAdProductMapping.adProductMapping).insertBean(adProductMappingCPCPo);
    }

    public List<AdProductMappingCPCPo> queryAdProductMappingDark(List<Long> adProductIdList) {
        return cpcBqf.selectFrom(QCPCAdProductMapping.adProductMapping)
                .where(QCPCAdProductMapping.adProductMapping.adProductId.in(adProductIdList))
                .where(QCPCAdProductMapping.adProductMapping.belongType.eq(AdProductBelongTypeEnum.DARK.getCode()))
                .where(QCPCAdProductMapping.adProductMapping.isDeleted.eq(IsValid.FALSE.getCode()))
                .fetch();

    }

    public List<AdProductMappingCPCPo> queryAdProductUnitMappingByMappingIdDark(List<Integer> unitIdList) {
        return cpcBqf.selectFrom(QCPCAdProductMapping.adProductMapping)
                .where(QCPCAdProductMapping.adProductMapping.mappingId.in(unitIdList))
                .where(QCPCAdProductMapping.adProductMapping.belongType.eq(AdProductBelongTypeEnum.DARK.getCode()))
                .where(QCPCAdProductMapping.adProductMapping.type.eq(AdProductBindTypeEnum.UNIT.getCode()))
                .where(QCPCAdProductMapping.adProductMapping.isDeleted.eq(IsValid.FALSE.getCode()))
                .fetch();

    }


    public Long insertProductInfoByBinlog(List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList) {
        Long insertCount = cpcBqf.insert(QCPCAdProductTotalInfo.adProductTotalInfo).insertBeans(adProductTotalInfoCPCPoList);
        return insertCount;
    }

    public Long updateProductInfoByBinlog(List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList) {
        long updateCount = cpcBqf.update(QCPCAdProductTotalInfo.adProductTotalInfo).updateBeans(adProductTotalInfoCPCPoList);
        return updateCount;

    }

    public Long insertProductMappingByBinlog(List<AdProductMappingCPCPo> adProductMappingCPCPoList) {
        Long insertCount = cpcBqf.insert(QCPCAdProductMapping.adProductMapping).insertBeans(adProductMappingCPCPoList);
        return insertCount;
    }

    public Long updateProductUnitMappingByBinlog(List<AdProductMappingCPCPo> adProductMappingCPCPoList) {

        Long updateCount = 0L;
        for (AdProductMappingCPCPo adProductMappingCPCPo : adProductMappingCPCPoList) {
            long singleUpdateCount = cpcBqf.update(QCPCAdProductMapping.adProductMapping)
                    .set(QCPCAdProductMapping.adProductMapping.adProductId, adProductMappingCPCPo.getAdProductId())
                    .set(QCPCAdProductMapping.adProductMapping.isDeleted, adProductMappingCPCPo.getIsDeleted())
                    .where(QCPCAdProductMapping.adProductMapping.mappingId.eq(adProductMappingCPCPo.getMappingId()))
                    .where(QCPCAdProductMapping.adProductMapping.type.eq(adProductMappingCPCPo.getType()))
                    .where(QCPCAdProductMapping.adProductMapping.belongType.eq(adProductMappingCPCPo.getBelongType()))
                    .execute();

            updateCount += singleUpdateCount;
        }
        return updateCount;
    }

    public Long updateProductAccountMappingByBinlog(List<AdProductMappingCPCPo> adProductMappingCPCPoList) {

        Long updateCount = 0L;
        for (AdProductMappingCPCPo adProductMappingCPCPo : adProductMappingCPCPoList) {
            long singleUpdateCount = cpcBqf.update(QCPCAdProductMapping.adProductMapping)
                    .set(QCPCAdProductMapping.adProductMapping.isDeleted, adProductMappingCPCPo.getIsDeleted())
                    .where(QCPCAdProductMapping.adProductMapping.adProductId.eq(adProductMappingCPCPo.getAdProductId()))
                    .where(QCPCAdProductMapping.adProductMapping.mappingId.eq(adProductMappingCPCPo.getMappingId()))
                    .where(QCPCAdProductMapping.adProductMapping.type.eq(adProductMappingCPCPo.getType()))
                    .where(QCPCAdProductMapping.adProductMapping.belongType.eq(adProductMappingCPCPo.getBelongType()))
                    .execute();

            updateCount += singleUpdateCount;
        }
        return updateCount;
    }

    public Long updateProductMappingDark(SdpaProductMappingDarkDto sdpaProductMappingDarkDto) {
        long updateCount = cpcBqf.update(QCPCAdProductMapping.adProductMapping)
                .set(QCPCAdProductMapping.adProductMapping.adProductId, sdpaProductMappingDarkDto.getAdProductId())
                .where(QCPCAdProductMapping.adProductMapping.mappingId.eq(sdpaProductMappingDarkDto.getMappingId()))
                .where(QCPCAdProductMapping.adProductMapping.type.eq(sdpaProductMappingDarkDto.getType()))
                .where(QCPCAdProductMapping.adProductMapping.belongType.eq(AdProductBelongTypeEnum.DARK.getCode()))
                .execute();
        return updateCount;
    }

    public Long deleteProductMappingByBinlog(List<AdProductMappingCPCPo> adProductMappingCPCPoList) {
        long deleteCount = 0L;
        for (AdProductMappingCPCPo adProductMappingCPCPo : adProductMappingCPCPoList) {
            long singleDeleteCount = cpcBqf.delete(QCPCAdProductMapping.adProductMapping)
                    .where(QCPCAdProductMapping.adProductMapping.adProductId.eq(adProductMappingCPCPo.getAdProductId()))
                    .where(QCPCAdProductMapping.adProductMapping.mappingId.eq(adProductMappingCPCPo.getMappingId()))
                    .where(QCPCAdProductMapping.adProductMapping.type.eq(adProductMappingCPCPo.getType()))
                    .where(QCPCAdProductMapping.adProductMapping.belongType.eq(adProductMappingCPCPo.getBelongType()))
                    .execute();
            deleteCount += singleDeleteCount;
        }

        return deleteCount;
    }

    public void batchInsertCategory(List<AdProductCategoryPo> adProductCategoryPoList) {
        adProductCategoryDao.insertBatch(adProductCategoryPoList);
    }

    public void batchInsertSpuCode(List<AdProductSpuPo> adProductSpuPoList) {
        adProductSpuDao.insertBatch(adProductSpuPoList);
    }

    public List<AdProductMappingCPCPo> queryAdProductMappingCpcPoListById(List<Long> idList) {
        return cpcBqf.selectFrom(QCPCAdProductMapping.adProductMapping)
                .where(QCPCAdProductMapping.adProductMapping.adProductId.in(idList))
                .fetch();
    }

    public void updateAdProductTotalInfoPo(AdProductTotalInfoPo adProductTotalInfoPo) {
        adBqf.update(QAdProductTotalInfo.adProductTotalInfo).updateBean(adProductTotalInfoPo);
    }

    public void insertProductMapping(AdProductMappingPo adProductMappingPo) {
        adProductMappingDao.insertSelective(adProductMappingPo);
    }

    public List<AdProductMappingPo> queryProductMapping(Long adProductId, Integer mappingId, Integer type, Integer belongType) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andAdProductIdEqualTo(adProductId)
                .andMappingIdEqualTo(mappingId)
                .andTypeEqualTo(type)
                .andBelongTypeEqualTo(belongType);
        List<AdProductMappingPo> adProductMappingPoList = adProductMappingDao.selectByExample(example);
        return adProductMappingPoList;
    }

    public List<AdProductMappingPo> queryProductMapping(Long adProductId, Integer type) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andAdProductIdEqualTo(adProductId)
                .andTypeEqualTo(type);
        List<AdProductMappingPo> adProductMappingPoList = adProductMappingDao.selectByExample(example);
        return adProductMappingPoList;
    }

    public List<AdProductMappingPo> queryProductMapping(Integer mappingId, Integer type, Integer belongType) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andMappingIdEqualTo(mappingId)
                .andTypeEqualTo(type)
                .andBelongTypeEqualTo(belongType);
        List<AdProductMappingPo> adProductMappingPoList = adProductMappingDao.selectByExample(example);
        return adProductMappingPoList;
    }

    public List<AdProductMappingPo> queryProductMapping(Long adProductId) {
        AdProductMappingPoExample example = new AdProductMappingPoExample();
        example.or().andAdProductIdEqualTo(adProductId);
        List<AdProductMappingPo> adProductMappingPoList = adProductMappingDao.selectByExample(example);
        return adProductMappingPoList;
    }

    public List<AdProductTotalInfoCPCPo> queryProductInfoByNames(List<String> adProductNames) {
        return cpcBqf.selectFrom(QCPCAdProductTotalInfo.adProductTotalInfo)
                .where(QCPCAdProductTotalInfo.adProductTotalInfo.adProductName.in(adProductNames))
                .where(QCPCAdProductTotalInfo.adProductTotalInfo.isDeleted.eq(IsValid.FALSE.getCode()))
                .fetch();
    }

    public void batchUpdateProductCpcInfo(List<AdProductTotalInfoCPCPo> adProductTotalInfoPoList) {
        cpcBqf.update(QCPCAdProductTotalInfo.adProductTotalInfo).updateBeans(adProductTotalInfoPoList);
    }

    public List<AdProductTotalInfoPo> queryEnableProduct(Integer accountId) {
        return adBqf.selectFrom(QAdProductTotalInfo.adProductTotalInfo)
                .where(QAdProductTotalInfo.adProductTotalInfo.accountId.eq(accountId))
                .where(QAdProductTotalInfo.adProductTotalInfo.bizStatus.eq(AdProductStatusEnum.ENABLE.getCode()))
                .where(QAdProductTotalInfo.adProductTotalInfo.isDeleted.eq(IsDeleted.VALID))
                .limit(2)
                .fetch();
    }

    public List<AdProductInfoSharePo> queryEnableShareProduct(Integer accountId) {
        AdProductInfoSharePoExample example = new AdProductInfoSharePoExample();
        example.or()
                .andShareAccountIdEqualTo(accountId)
                .andBizStatusEqualTo(AdProductStatusEnum.ENABLE.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID);
        example.setLimit(2);
        return adProductInfoShareDao.selectByExample(example);
    }

    public List<Integer> queryShareLibraryAccountIdList(Long libraryId) {
        AdProductLibrarySharePoExample example = new AdProductLibrarySharePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID).andLibraryIdEqualTo(libraryId).andBelongTypeEqualTo(AdProductLibraryBelongTypeEnum.SHARED.getCode());
        List<AdProductLibrarySharePo> adProductLibrarySharePos = adProductLibraryShareDao.selectByExample(example);
        if (CollectionUtils.isEmpty(adProductLibrarySharePos)) {
            return new ArrayList<>();
        }
        return adProductLibrarySharePos.stream().map(AdProductLibrarySharePo::getAccountId).collect(Collectors.toList());
    }

    public Integer addShareInfo(Map<String, Long> nameIdMap, List<Integer> accountIdList, Long libraryId) {
        List<AdProductInfoSharePo> result = new ArrayList<>();

        for (Map.Entry<String, Long> nameIdEntry : nameIdMap.entrySet()) {
            for (Integer accountId : accountIdList) {
                AdProductInfoSharePo adProductInfoSharePo = new AdProductInfoSharePo();
                adProductInfoSharePo.setAdProductId(nameIdEntry.getValue());
                adProductInfoSharePo.setShareAccountId(accountId);
                adProductInfoSharePo.setBizStatus(AdProductStatusEnum.DISABLE.getCode());
                adProductInfoSharePo.setIsDeleted(IsDeleted.VALID);
                adProductInfoSharePo.setCtime(new Timestamp(System.currentTimeMillis()));
                adProductInfoSharePo.setMtime(new Timestamp(System.currentTimeMillis()));
                adProductInfoSharePo.setAdProductName(nameIdEntry.getKey());
                adProductInfoSharePo.setLibraryId(libraryId);
                result.add(adProductInfoSharePo);
            }
        }
        return adProductInfoShareDao.insertBatch(result);
    }
}
