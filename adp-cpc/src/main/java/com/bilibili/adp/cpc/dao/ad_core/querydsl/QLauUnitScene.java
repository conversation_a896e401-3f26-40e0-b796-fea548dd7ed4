package com.bilibili.adp.cpc.dao.ad_core.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitScenePo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauUnitScene is a Querydsl query type for LauUnitScenePo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauUnitScene extends com.querydsl.sql.RelationalPathBase<LauUnitScenePo> {

    private static final long serialVersionUID = -802353460;

    public static final QLauUnitScene lauUnitScene = new QLauUnitScene("lau_unit_scene");

    public final NumberPath<Integer> channelId = createNumber("channelId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final StringPath supportedScenes = createString("supportedScenes");

    public final NumberPath<Integer> unitId = createNumber("unitId", Integer.class);

    public final com.querydsl.sql.PrimaryKey<LauUnitScenePo> primary = createPrimaryKey(id);

    public QLauUnitScene(String variable) {
        super(LauUnitScenePo.class, forVariable(variable), "null", "lau_unit_scene");
        addMetadata();
    }

    public QLauUnitScene(String variable, String schema, String table) {
        super(LauUnitScenePo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauUnitScene(String variable, String schema) {
        super(LauUnitScenePo.class, forVariable(variable), schema, "lau_unit_scene");
        addMetadata();
    }

    public QLauUnitScene(Path<? extends LauUnitScenePo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_unit_scene");
        addMetadata();
    }

    public QLauUnitScene(PathMetadata metadata) {
        super(LauUnitScenePo.class, metadata, "null", "lau_unit_scene");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(channelId, ColumnMetadata.named("channel_id").withIndex(3).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(5).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(supportedScenes, ColumnMetadata.named("supported_scenes").withIndex(4).ofType(Types.VARCHAR).withSize(64).notNull());
        addMetadata(unitId, ColumnMetadata.named("unit_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
    }

}

