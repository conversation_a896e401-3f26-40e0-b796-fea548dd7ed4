package com.bilibili.adp.cpc.biz.bos.mapi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @ClassName MapiBindAccountInfoBo
 * <AUTHOR>
 * @Date 2023/11/3 8:38 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MapiBindAccountInfoBo {
    private String appKey;
    private String appKeyInfo;
    private String operator;
    private Timestamp ctime;
    private Timestamp mtime;
}
