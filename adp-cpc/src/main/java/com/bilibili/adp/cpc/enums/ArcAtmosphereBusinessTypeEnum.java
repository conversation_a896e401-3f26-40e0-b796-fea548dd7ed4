package com.bilibili.adp.cpc.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName ArcAtmosphereBusinessTypeEnum
 * <AUTHOR>
 * @Date 2024/1/16 5:09 下午
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum ArcAtmosphereBusinessTypeEnum {

    BLUE_V_CM_FLY(4, 4, "蓝v商业起飞", 2, true),
    NORMAL_CM_FLY(6, 5, "其他商业起飞", 2, false),
    BLUE_V_CONTENT_FLY(4,6, "蓝v内容起飞", 0, true),
    BLUE_V_CONTENT_FLY_MIDDLE(4,6, "蓝v内容起飞", 4, true),
    BLUE_V_PERSONAL_FLY(4,7, "蓝V个人起飞", 3, true),
    BLUE_V_MUST_CHOOSE(4,8, "蓝V必选", 1, true),
    NORMAL_CONTENT_FLY(6,9, "其他内容起飞", 0, false),
    NORMAL_CONTENT_FLY_MIDDLE(6,9, "其他内容起飞", 4, false),
    NORMAL_PERSONAL_FLY(6,10, "其他个人起飞", 3, false),
    NORMAL_MUST_CHOOSE(7,11, "其他必选", 1, false),
    NORMAL_MUST_CHOOSE_CM_SPACE(7,12, "其他必选小号", 1, false)
    ;
    private final Integer firstType;
    private final Integer secondType;
    private final String name;
    private final Integer businessDomain;
    private final Boolean isBlueV;

    public static ArcAtmosphereBusinessTypeEnum getByBusinessDomainAndIsBlueV(Integer businessDomain, boolean isBlueV) {
        for (ArcAtmosphereBusinessTypeEnum bean : values()) {
            if (bean.getIsBlueV().equals(isBlueV) && bean.getBusinessDomain().equals(businessDomain)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown ArcAtmosphereBusinessTypeEnum businessDomain" + businessDomain);
    }
}
