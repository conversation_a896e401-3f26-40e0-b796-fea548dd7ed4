package com.bilibili.adp.cpc.biz.services.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @ClassName LauUnitExtraBo
 * <AUTHOR>
 * @Date 2024/11/3 10:00 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LauUnitExtraBo {

    private Long id;

    private Integer accountId;

    private Integer unitId;

    private Integer isBiliNative;

    private Integer androidAppPackageId;

    private String deviceAppStore;

    private Integer iosAppPackageId;

    private Integer smartKeyWord;

    private String tags;

    private Timestamp ctime;

    private java.sql.Timestamp mtime;

}
