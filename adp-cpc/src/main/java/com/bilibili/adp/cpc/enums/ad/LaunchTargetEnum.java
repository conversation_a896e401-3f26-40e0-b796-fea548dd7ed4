package com.bilibili.adp.cpc.enums.ad;


/**
 * copy class
 * @see com.bilibili.adp.launch.api.flyPro.dto.enums.LaunchTargetEnum
 */
public enum LaunchTargetEnum {

    //提升粉丝数量，助力账户成长
    ACCOUNT_GROWTH(0, "粉丝成长",PromotionPurposeType.ARCHIVE_CONTENT),

    //增加商业稿件播放，提升品牌效应
    TRAFFIC_BOOST(1, "宣发推广",PromotionPurposeType.ARCHIVE_CONTENT),

    //加速直播间曝光，直接提升人气
    LIVE_PROMOTION(2, "直播间助推",PromotionPurposeType.LIVE_ROOM),

    //提升直播间人气,放大商单效果
    BUSINESS_LIVE_PROMOTION(3, "商业直播间助推",PromotionPurposeType.LIVE_ROOM),

    //提高动态曝光
    DYNAMIC(4, "动态推广",PromotionPurposeType.DYNAMIC),

    ACTIVITY(5, "活动推广",PromotionPurposeType.ACTIVITY);

    private Integer code;
    private String name;
    private PromotionPurposeType promotionPurposeType;

    LaunchTargetEnum(int code, String name, PromotionPurposeType promotionPurposeType) {

        this.code = code;
        this.name = name;
        this.promotionPurposeType = promotionPurposeType;
    }

    public static LaunchTargetEnum getByCode(Integer code) {
        for (LaunchTargetEnum auditStatus : values()) {
            if (auditStatus.code.equals(code)) {
                return auditStatus;
            }
        }
        return null;
    }

    public static LaunchTargetEnum getByOcpxTarget(Integer ocpxTarget) {
        return ocpxTarget != null && ocpxTarget == 10 ? ACCOUNT_GROWTH : TRAFFIC_BOOST;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public PromotionPurposeType getPromotionPurposeType() {
        return promotionPurposeType;
    }
}
