package com.bilibili.adp.cpc.enums.ocpx;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum OcpxManagedAutoPayPeriod {
    PERIOD1(1, "第一周期", 14),
    PERIOD2(2, "第二周期", 21),
    PERIOD3(3, "第三周期", 28),
    PERIOD4(4, "第四周期", 35),
    ;

    private final Integer code;
    private final String msg;
    private final Integer date;

    OcpxManagedAutoPayPeriod(Integer code, String msg, Integer date) {
        this.code = code;
        this.msg = msg;
        this.date = date;
    }

    /**
     * 获取上一个周期
     * @param period 当前周期
     * @return 上一个周期
     */
    public static OcpxManagedAutoPayPeriod getPrePeriod(OcpxManagedAutoPayPeriod period) {
        if (OcpxManagedAutoPayPeriod.PERIOD1.equals(period)) {
            return null;
        }
        int code = period.getCode() - 2;
        return OcpxManagedAutoPayPeriod.values()[code];
    }

    /**
     * 获取最后一个周期
     * @return 最后一个周期
     */
    public static OcpxManagedAutoPayPeriod getLastPeriod() {
        return OcpxManagedAutoPayPeriod.values()[OcpxManagedAutoPayPeriod.values().length - 1];
    }

    public static OcpxManagedAutoPayPeriod getPeriodByCode(Integer code) {
        for (OcpxManagedAutoPayPeriod period : OcpxManagedAutoPayPeriod.values()) {
            if (Objects.equals(code, period.getCode())) {
                return period;
            }
        }
        throw new IllegalArgumentException("unknown OcpxManagedAutoPayPeriod code");
    }

    public static final Integer DAYS_OF_PERIOD = 7;
}
