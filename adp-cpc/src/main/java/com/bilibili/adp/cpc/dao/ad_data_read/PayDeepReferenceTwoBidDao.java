package com.bilibili.adp.cpc.dao.ad_data_read;

import com.bilibili.adp.cpc.po.ad_data.PayDeepReferenceTwoBidPo;
import com.bilibili.adp.cpc.po.ad_data.PayDeepReferenceTwoBidPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface PayDeepReferenceTwoBidDao {
    long countByExample(PayDeepReferenceTwoBidPoExample example);

    int deleteByExample(PayDeepReferenceTwoBidPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(PayDeepReferenceTwoBidPo record);

    int insertBatch(List<PayDeepReferenceTwoBidPo> records);

    int insertUpdateBatch(List<PayDeepReferenceTwoBidPo> records);

    int insert(PayDeepReferenceTwoBidPo record);

    int insertUpdateSelective(PayDeepReferenceTwoBidPo record);

    int insertSelective(PayDeepReferenceTwoBidPo record);

    List<PayDeepReferenceTwoBidPo> selectByExample(PayDeepReferenceTwoBidPoExample example);

    PayDeepReferenceTwoBidPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") PayDeepReferenceTwoBidPo record, @Param("example") PayDeepReferenceTwoBidPoExample example);

    int updateByExample(@Param("record") PayDeepReferenceTwoBidPo record, @Param("example") PayDeepReferenceTwoBidPoExample example);

    int updateByPrimaryKeySelective(PayDeepReferenceTwoBidPo record);

    int updateByPrimaryKey(PayDeepReferenceTwoBidPo record);
}