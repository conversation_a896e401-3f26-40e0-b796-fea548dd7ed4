package com.bilibili.adp.cpc.biz.services.unit.status_flow;

import com.bilibili.adp.cpc.biz.services.unit.LauUnitExtraService;
import com.bilibili.adp.cpc.biz.services.unit.UnitAccelerateService;
import com.bilibili.adp.cpc.enums.ad.UnitStatusChangeEventType;
import com.bilibili.adp.cpc.utils.metrics.CustomMetrics;
import com.bilibili.adp.launch.api.common.LaunchStatus;
import com.bilibili.adp.launch.api.common.UnitStatus;
import com.bilibili.bsi.api.soa.IBsiSoaMessageService;
import com.bilibili.report.platform.api.soa.ISoaStatUnitService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class AtDeliveryStatusFlow extends DefaultStatusFlow {
    public AtDeliveryStatusFlow(ISoaStatUnitService statUnitService,
                                IBsiSoaMessageService bsiSoaMessageService,
                                LauUnitExtraService unitExtraService,
                                UnitAccelerateService unitAccelerateService,
        CustomMetrics customMetrics) {
            super(statUnitService, bsiSoaMessageService, unitExtraService, unitAccelerateService, customMetrics);
    }

    @Override
    public UnitStatusChangeEventType getStatusFlowTyp() {
        return UnitStatusChangeEventType.AtDelivery;
    }

    @Override
    public void refreshStatus(List<Long> objectIds) {
        List<Integer> unitIds = objectIds.stream().mapToInt(Long::intValue).boxed().collect(Collectors.toList());
        batchUpdateStatus(unitIds, LaunchStatus.START.getCode(), UnitStatus.VALID.getCode());

    }
}
