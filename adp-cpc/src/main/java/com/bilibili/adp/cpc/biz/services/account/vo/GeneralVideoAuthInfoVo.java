package com.bilibili.adp.cpc.biz.services.account.vo;

import com.bilibili.adp.cpc.biz.services.account.vo.GeneralMidInfoVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GeneralVideoAuthInfoVo {

    @ApiModelProperty("请求类型 1 授权 2 续期")
    private Integer requestType;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("代理商名称")
    private String agentName;

    @ApiModelProperty("稿件是否联合投稿,0：否，1：是")
    private Integer isCooperation;
    @ApiModelProperty("视频作者列表")
    private List<GeneralMidInfoVo> midInfoVoList;

    @ApiModelProperty("视频授权模式， 1:原视频+UP主空间头像，2:原视频")
    private Integer authModel;

    @ApiModelProperty("视频avid")
    private Long avid;

    @ApiModelProperty("视频bvid")
    private String bvid;

    @ApiModelProperty("视频封面")
    private String cover;

    @ApiModelProperty("视频标题")
    private String title;

    @ApiModelProperty("生效时间")
    private String effectiveTime;


    @ApiModelProperty("旧失效时间")
    private String oldExpireTime;

    @ApiModelProperty("新的授权时间类型，1:指定起始时间 2:不限")
    private Integer authTimeType;


    @ApiModelProperty("新的失效时间")
    private String expireTime;


    private String failReason;

}
