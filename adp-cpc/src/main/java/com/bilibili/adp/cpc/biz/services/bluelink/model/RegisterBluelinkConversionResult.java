package com.bilibili.adp.cpc.biz.services.bluelink.model;

import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/20
 */
@Data
@Accessors(chain = true)
public class RegisterBluelinkConversionResult  {


    private Integer customerId;

    private Integer agentId;

    private List<Integer> creativeIds;

    private Integer unitId;

    private Integer campaignId;

    private Long parentMid;

    private Long parentDynamicRid;

    private ConvLink convLink;

    private String gameMonitoringUrl;




}
