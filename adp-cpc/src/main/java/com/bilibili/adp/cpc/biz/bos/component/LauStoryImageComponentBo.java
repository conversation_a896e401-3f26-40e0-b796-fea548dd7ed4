/*
 * This file is generated by jOOQ.
 */
package com.bilibili.adp.cpc.biz.bos.component;


import com.bapis.ad.component.GoodsInfo;
import com.bapis.ad.component.Image;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Objects;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LauStoryImageComponentBo {
    private String imageUrl;
    private String imageMd5;
    private Integer isContainGoods;
    private String itemId;
    private Integer itemSource;
    private Integer jumpType;
    private String jumpUrl;
    private String schemaUrl;
    private String miniProgramId;
    private String miniProgramName;
    private String miniProgramPath;

    public static boolean isChanged(LauStoryImageComponentBo a, LauStoryImageComponentBo b) {
        if (Objects.isNull(a) || Objects.isNull(b)) return false;
        return !(Objects.equals(a.imageUrl, b.imageUrl)
                && Objects.equals(a.itemId, b.itemId)
                && Objects.equals(a.jumpUrl, b.jumpUrl)
        );
    }

    public static LauStoryImageComponentBo fromRpcBo(Image image) {
        final LauStoryImageComponentBo bo = new LauStoryImageComponentBo();
        bo.setImageUrl(image.getImageUrl());
        bo.setImageMd5(image.getImageMd5());
        if (image.hasGoodsInfo()) {
            GoodsInfo goodsInfo = image.getGoodsInfo();
            bo.setIsContainGoods(1);
            bo.setItemId(String.valueOf(goodsInfo.getItemId()));
            bo.setItemSource(goodsInfo.getItemSource());
            bo.setJumpType(goodsInfo.getJumpType());
            bo.setJumpUrl(goodsInfo.getJumpUrl());
            bo.setSchemaUrl(goodsInfo.getSchemaUrl());
            bo.setMiniProgramId(goodsInfo.getMiniProgramId());
            bo.setMiniProgramName(goodsInfo.getMiniProgramName());
            bo.setMiniProgramPath(goodsInfo.getMiniProgramPath());
        }
        return bo;
    }

    public static Image toRpcBo(LauStoryImageComponentBo bo) {
        Assert.isTrue(StringUtils.hasText(bo.getImageUrl()), "组件图片url不能为空");
        Assert.isTrue(StringUtils.hasText(bo.getImageMd5()), "组件图片md5不能为空");
        Image.Builder builder = Image.newBuilder();
        builder.setImageUrl(bo.getImageUrl());
        builder.setImageMd5(bo.getImageMd5());
        GoodsInfo.Builder goodsBuilder = GoodsInfo.newBuilder();
        if (!StringUtils.isEmpty(bo.getItemId())) {
            goodsBuilder.setItemId(Long.valueOf(bo.getItemId()))
                    .setItemSource(bo.getItemSource())
                    .setJumpType(bo.getJumpType())
                    .setJumpUrl(bo.getJumpUrl());
            if (!StringUtils.isEmpty(bo.getSchemaUrl())) {
                goodsBuilder.setSchemaUrl(bo.getSchemaUrl());
            }
            if (!StringUtils.isEmpty(bo.getMiniProgramId()) && !StringUtils.isEmpty(bo.getMiniProgramName()) && !StringUtils.isEmpty(bo.getMiniProgramPath())) {
                goodsBuilder.setMiniProgramId(bo.getMiniProgramId())
                        .setMiniProgramName(bo.getMiniProgramName())
                        .setMiniProgramPath(bo.getMiniProgramPath());
            }
            builder.setGoodsInfo(goodsBuilder.build());
        }
        return builder.build();
    }
}