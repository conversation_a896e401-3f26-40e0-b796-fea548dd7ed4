package com.bilibili.adp.cpc.biz.services.sales_type_option;

import com.bilibili.adp.common.enums.SalesType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SalesTypeOptionService {
    private SalesTypeOptionBo genCpcOption() {
        return SalesTypeOptionBo.builder()
                .salesType(SalesType.CPC.getCode())
                .name(SalesType.CPC.getName())
                .order(30)
                .build();
    }

}
