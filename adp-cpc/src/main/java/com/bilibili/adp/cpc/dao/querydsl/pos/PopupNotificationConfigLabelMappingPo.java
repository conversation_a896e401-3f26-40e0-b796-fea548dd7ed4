package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * PopupNotificationConfigLabelMappingPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class PopupNotificationConfigLabelMappingPo {

    private Integer configId;

    private java.sql.Timestamp ctime;

    private Integer id;

    private Integer isDeleted;

    private Integer labelId;

    private java.sql.Timestamp mtime;

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getLabelId() {
        return labelId;
    }

    public void setLabelId(Integer labelId) {
        this.labelId = labelId;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
         return "configId = " + configId + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", labelId = " + labelId + ", mtime = " + mtime;
    }

}

