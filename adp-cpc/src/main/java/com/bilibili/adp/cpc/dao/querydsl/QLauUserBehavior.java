package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauUserBehaviorPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauUserBehavior is a Querydsl query type for LauUserBehaviorPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauUserBehavior extends com.querydsl.sql.RelationalPathBase<LauUserBehaviorPo> {

    private static final long serialVersionUID = -**********;

    public static final QLauUserBehavior lauUserBehavior = new QLauUserBehavior("lau_user_behavior");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final StringPath behavior = createString("behavior");

    public final NumberPath<Integer> behaviorId = createNumber("behaviorId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final StringPath generalConfig = createString("generalConfig");

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> system = createNumber("system", Integer.class);

    public final com.querydsl.sql.PrimaryKey<LauUserBehaviorPo> primary = createPrimaryKey(id);

    public QLauUserBehavior(String variable) {
        super(LauUserBehaviorPo.class, forVariable(variable), "null", "lau_user_behavior");
        addMetadata();
    }

    public QLauUserBehavior(String variable, String schema, String table) {
        super(LauUserBehaviorPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauUserBehavior(String variable, String schema) {
        super(LauUserBehaviorPo.class, forVariable(variable), schema, "lau_user_behavior");
        addMetadata();
    }

    public QLauUserBehavior(Path<? extends LauUserBehaviorPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_user_behavior");
        addMetadata();
    }

    public QLauUserBehavior(PathMetadata metadata) {
        super(LauUserBehaviorPo.class, metadata, "null", "lau_user_behavior");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(4).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(behavior, ColumnMetadata.named("behavior").withIndex(6).ofType(Types.VARCHAR).withSize(2048).notNull());
        addMetadata(behaviorId, ColumnMetadata.named("behavior_id").withIndex(5).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(2).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(generalConfig, ColumnMetadata.named("general_config").withIndex(8).ofType(Types.VARCHAR).withSize(2048).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(3).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(system, ColumnMetadata.named("system").withIndex(7).ofType(Types.TINYINT).withSize(3).notNull());
    }

}

