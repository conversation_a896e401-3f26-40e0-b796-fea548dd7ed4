package com.bilibili.adp.cpc.biz.bos.dynamic;

import com.bilibili.adp.cpc.biz.bos.goods.SanlianCmcGoodsContentResultBo;
import com.bilibili.adp.cpc.biz.bos.goods.SanlianCmcPlaceGoodsContentInfoBo;
import com.bilibili.adp.cpc.biz.bos.goods.SanlianSupportYellowCarTypeBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName SanlianDynamicExtraBo
 * <AUTHOR>
 * @Date 2024/12/2 8:40 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SanlianDynamicExtraBo {

    private Long dynamicId;
    // cid动态非法
    private Integer isCidDynamicInvalid;
    // 是否带货内容
    private Integer isGoodsContent;
    // 支持的黄车组件类型
    private List<SanlianSupportYellowCarTypeBo> supportYellowCarTypeBoList;
    // 商品详情
    private List<SanlianCmcPlaceGoodsContentInfoBo> goodsInfoList;
    // 评论位置的商品详情
    private List<SanlianCmcGoodsContentResultBo> commentGoods;

    public static SanlianDynamicExtraBo getDefaultInstance(Long dynamicId) {
        return SanlianDynamicExtraBo.builder()
                .dynamicId(dynamicId)
                .isCidDynamicInvalid(0)
                .isGoodsContent(0)
                .supportYellowCarTypeBoList(Collections.emptyList())
                .goodsInfoList(Collections.emptyList())
                .build();
    }
}
