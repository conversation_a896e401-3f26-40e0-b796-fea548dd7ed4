package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.bilibili.adp.cpc.dao.querydsl.pos.ResTargetPackageCrowdPackageUpgradePo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QResTargetPackageCrowdPackageUpgrade is a Querydsl query type for ResTargetPackageCrowdPackageUpgradePo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QResTargetPackageCrowdPackageUpgrade extends com.querydsl.sql.RelationalPathBase<ResTargetPackageCrowdPackageUpgradePo> {

    private static final long serialVersionUID = -31654904;

    public static final QResTargetPackageCrowdPackageUpgrade resTargetPackageCrowdPackageUpgrade = new QResTargetPackageCrowdPackageUpgrade("res_target_package_crowd_package_upgrade");

    public final NumberPath<Integer> crowdPackId = createNumber("crowdPackId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> groupId = createNumber("groupId", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> targetPackageId = createNumber("targetPackageId", Integer.class);

    public final NumberPath<Integer> type = createNumber("type", Integer.class);

    public final com.querydsl.sql.PrimaryKey<ResTargetPackageCrowdPackageUpgradePo> primary = createPrimaryKey(id);

    public QResTargetPackageCrowdPackageUpgrade(String variable) {
        super(ResTargetPackageCrowdPackageUpgradePo.class, forVariable(variable), "null", "res_target_package_crowd_package_upgrade");
        addMetadata();
    }

    public QResTargetPackageCrowdPackageUpgrade(String variable, String schema, String table) {
        super(ResTargetPackageCrowdPackageUpgradePo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QResTargetPackageCrowdPackageUpgrade(String variable, String schema) {
        super(ResTargetPackageCrowdPackageUpgradePo.class, forVariable(variable), schema, "res_target_package_crowd_package_upgrade");
        addMetadata();
    }

    public QResTargetPackageCrowdPackageUpgrade(Path<? extends ResTargetPackageCrowdPackageUpgradePo> path) {
        super(path.getType(), path.getMetadata(), "null", "res_target_package_crowd_package_upgrade");
        addMetadata();
    }

    public QResTargetPackageCrowdPackageUpgrade(PathMetadata metadata) {
        super(ResTargetPackageCrowdPackageUpgradePo.class, metadata, "null", "res_target_package_crowd_package_upgrade");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(crowdPackId, ColumnMetadata.named("crowd_pack_id").withIndex(3).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(5).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(groupId, ColumnMetadata.named("group_id").withIndex(4).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(7).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(targetPackageId, ColumnMetadata.named("target_package_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(type, ColumnMetadata.named("type").withIndex(8).ofType(Types.TINYINT).withSize(3).notNull());
    }

}

