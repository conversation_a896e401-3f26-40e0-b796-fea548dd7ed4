package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.querydsl.pos.PopupNotificationConfigPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QPopupNotificationConfig is a Querydsl query type for PopupNotificationConfigPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QPopupNotificationConfig extends com.querydsl.sql.RelationalPathBase<PopupNotificationConfigPo> {

    private static final long serialVersionUID = 545664403;

    public static final QPopupNotificationConfig popupNotificationConfig = new QPopupNotificationConfig("popup_notification_config");

    public final NumberPath<Integer> button1 = createNumber("button1", Integer.class);

    public final NumberPath<Integer> button2 = createNumber("button2", Integer.class);

    public final StringPath buttonLearnMoreUrl = createString("buttonLearnMoreUrl");

    public final StringPath buttonTryNowUrl = createString("buttonTryNowUrl");

    public final StringPath content = createString("content");

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final DateTimePath<java.sql.Timestamp> effectiveEndTime = createDateTime("effectiveEndTime", java.sql.Timestamp.class);

    public final DateTimePath<java.sql.Timestamp> effectiveStartTime = createDateTime("effectiveStartTime", java.sql.Timestamp.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final StringPath imageRedirectUrl = createString("imageRedirectUrl");

    public final StringPath imageUrl = createString("imageUrl");

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final NumberPath<Integer> isPublicToAllUsers = createNumber("isPublicToAllUsers", Integer.class);

    public final NumberPath<Integer> isVisible = createNumber("isVisible", Integer.class);

    public final StringPath location = createString("location");

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> templateId = createNumber("templateId", Integer.class);

    public final NumberPath<Integer> templateType = createNumber("templateType", Integer.class);

    public final StringPath title = createString("title");

    public final com.querydsl.sql.PrimaryKey<PopupNotificationConfigPo> primary = createPrimaryKey(id);

    public QPopupNotificationConfig(String variable) {
        super(PopupNotificationConfigPo.class, forVariable(variable), "null", "popup_notification_config");
        addMetadata();
    }

    public QPopupNotificationConfig(String variable, String schema, String table) {
        super(PopupNotificationConfigPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QPopupNotificationConfig(String variable, String schema) {
        super(PopupNotificationConfigPo.class, forVariable(variable), schema, "popup_notification_config");
        addMetadata();
    }

    public QPopupNotificationConfig(Path<? extends PopupNotificationConfigPo> path) {
        super(path.getType(), path.getMetadata(), "null", "popup_notification_config");
        addMetadata();
    }

    public QPopupNotificationConfig(PathMetadata metadata) {
        super(PopupNotificationConfigPo.class, metadata, "null", "popup_notification_config");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(button1, ColumnMetadata.named("button1").withIndex(7).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(button2, ColumnMetadata.named("button2").withIndex(8).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(buttonLearnMoreUrl, ColumnMetadata.named("button_learn_more_url").withIndex(16).ofType(Types.VARCHAR).withSize(1024).notNull());
        addMetadata(buttonTryNowUrl, ColumnMetadata.named("button_try_now_url").withIndex(17).ofType(Types.VARCHAR).withSize(1024).notNull());
        addMetadata(content, ColumnMetadata.named("content").withIndex(4).ofType(Types.VARCHAR).withSize(8192).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(13).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(effectiveEndTime, ColumnMetadata.named("effective_end_time").withIndex(15).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(effectiveStartTime, ColumnMetadata.named("effective_start_time").withIndex(9).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(imageRedirectUrl, ColumnMetadata.named("image_redirect_url").withIndex(6).ofType(Types.VARCHAR).withSize(1024).notNull());
        addMetadata(imageUrl, ColumnMetadata.named("image_url").withIndex(5).ofType(Types.VARCHAR).withSize(1024).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(12).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(isPublicToAllUsers, ColumnMetadata.named("is_public_to_all_users").withIndex(18).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(isVisible, ColumnMetadata.named("is_visible").withIndex(11).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(location, ColumnMetadata.named("location").withIndex(10).ofType(Types.VARCHAR).withSize(1024).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(14).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(templateId, ColumnMetadata.named("template_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(templateType, ColumnMetadata.named("template_type").withIndex(19).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(title, ColumnMetadata.named("title").withIndex(3).ofType(Types.VARCHAR).withSize(1024).notNull());
    }

}

