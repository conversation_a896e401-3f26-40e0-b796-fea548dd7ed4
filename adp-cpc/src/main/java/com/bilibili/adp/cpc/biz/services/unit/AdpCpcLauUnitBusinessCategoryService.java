package com.bilibili.adp.cpc.biz.services.unit;

import com.bilibili.adp.cpc.biz.bos.unit.LauUnitBusinessCategoryBo;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitBusinessCategory.lauUnitBusinessCategory;

@Service
public class AdpCpcLauUnitBusinessCategoryService {
    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    public LauUnitBusinessCategoryBo queryByUnitId(int unitId) {
        return adCoreBqf.selectFrom(lauUnitBusinessCategory)
                .where(lauUnitBusinessCategory.bizStatus.eq(Constants.BIZ_STATUS_OK)
                        .and(lauUnitBusinessCategory.unitId.eq(unitId)))
                .fetchFirst(LauUnitBusinessCategoryBo.class);
    }

    public void insertOrUpdate(LauUnitBusinessCategoryBo bo) {
        adCoreBqf.insertMysql(lauUnitBusinessCategory)
                .insertDuplicateUpdate(bo);
    }

    public void deleteByUnitId(int unitId) {
        adCoreBqf.delete(lauUnitBusinessCategory)
                .where(lauUnitBusinessCategory.unitId.eq(unitId))
                .execute();
    }
}
