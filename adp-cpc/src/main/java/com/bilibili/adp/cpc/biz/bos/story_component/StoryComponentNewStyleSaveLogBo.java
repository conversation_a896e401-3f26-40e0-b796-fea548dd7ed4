/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.adp.cpc.biz.bos.story_component;

import com.bilibili.adp.common.annotation.DatabaseColumnEnum;
import com.bilibili.adp.common.annotation.DatabaseColumnName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoryComponentNewStyleSaveLogBo {

    @DatabaseColumnName(value = "id")
    private Long      id;

    @DatabaseColumnName(value = "名称")
    private String    name;

    @DatabaseColumnName(value = "类型")
    @DatabaseColumnEnum("{'1':'动效翻转卡'}")
    private Integer   type;
    @DatabaseColumnName(value = "样式场景")
    @DatabaseColumnEnum("{'1':'大促特权样式'}")
    private Integer   styleScene;

    @DatabaseColumnName(value = "前卡标题")
    private String    frontCardTitle;
    @DatabaseColumnName(value = "前卡描述")
    private String    frontCardDesc;

    @DatabaseColumnName(value = "翻转卡标题")
    private String    flipCardTitle;
    @DatabaseColumnName(value = "翻转卡图片")
    private String    flipCardImgUrl;

//    private String    flipCardImgMd5;

    @DatabaseColumnName(value = "优惠类型")
    @DatabaseColumnEnum("{'1':'价格','2':'自定义'}")
    private Integer    discountAmountType;
    @DatabaseColumnName(value = "优惠额度")
    private String    discountAmount;
    @DatabaseColumnName(value = "优惠说明")
    private String    discountDesc;
    @DatabaseColumnName(value = "按钮文案")
    private String    buttonText;

    @DatabaseColumnName(value = "生效维度")
    private List<Integer> mappingIds;

}
