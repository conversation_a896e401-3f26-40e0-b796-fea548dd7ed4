package com.bilibili.adp.cpc.dao.ad_core.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauUnitGoodsPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauUnitGoodsPo {

    private java.sql.Timestamp ctime;

    private Long id;

    private Integer isDeleted;

    private Long itemId;

    private java.sql.Timestamp mtime;

    private Integer unitId;

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Override
    public String toString() {
         return "ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", itemId = " + itemId + ", mtime = " + mtime + ", unitId = " + unitId;
    }

}

