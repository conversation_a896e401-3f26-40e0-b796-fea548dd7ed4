package com.bilibili.adp.cpc.biz.services.video_auth;

import com.bapis.ad.adp.video_auth.GeneralVideoAuthReq;
import com.bapis.archive.service.Arc;
import com.bilibili.adp.cpc.biz.services.account.GeneralVideoAuthDaoService;
import com.bilibili.adp.cpc.biz.services.account.dto.LauGeneralAvidAuthDto;
import com.bilibili.adp.cpc.biz.services.account.dto.LauGeneralAvidMappingDto;
import com.bilibili.adp.cpc.biz.services.account.dto.LauGeneralAvidRequestDto;
import com.bilibili.adp.cpc.enums.GeneralVideoAuthStatusEnum;
import com.bilibili.adp.cpc.enums.GeneralVideoEnum;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;

@Slf4j
@Service
@RequiredArgsConstructor
public class VideoAuthDaoService {
	private static int SOURCE_FROM_GAME = 1;
	private final GeneralVideoAuthDaoService generalVideoAuthDaoService;

	// 插入操作：mapping, request, auth, record表
	@Transactional(value = AD_TM, rollbackFor = Exception.class)
	public void processInsert(GeneralVideoAuthReq request, List<AccountBaseDto> accountBaseDtoList, Arc arc) {
		for (AccountBaseDto account : accountBaseDtoList) {
			// 插入 mapping 表
			LauGeneralAvidMappingDto avidMapping = LauGeneralAvidMappingDto.builder()
					.avid(request.getAvid())
					.customerId(account.getCustomerId())
					.agentId(account.getDependencyAgentId())
					.authStatus(GeneralVideoAuthStatusEnum.CONFIRM.getCode())
					.authMode(GeneralVideoEnum.AuthModelEnum.VIDEO_AND_MID.getCode())
					.authTimeType(GeneralVideoEnum.AuthTimeTypeEnum.LIMIT.getCode())
					.authEffectiveTime(new Timestamp(request.getAuthEffectiveTime()))
					.authExpireTime(new Timestamp(request.getAuthExpireTime()))
					.source(SOURCE_FROM_GAME).build();
			Integer mappingId = generalVideoAuthDaoService.avidMappingInsert(avidMapping);
			log.info("VideoAuthRpcService: generalVideoAuth insert avidMapping mappingId:{}, request:{}", mappingId, request);
			// 插入 request表
			LauGeneralAvidRequestDto avidRequest = LauGeneralAvidRequestDto.builder()
					.mappingId(mappingId)
					.requestType(GeneralVideoEnum.RequestEnum.AUTH.getCode())
					.authTimeType(GeneralVideoEnum.AuthTimeTypeEnum.LIMIT.getCode())
					.authEffectiveTime(new Timestamp(request.getAuthEffectiveTime()))
					.authExpireTime(new Timestamp(request.getAuthExpireTime())).build();
			Long requestId = generalVideoAuthDaoService.avidRequestInsert(avidRequest);
			log.info("VideoAuthRpcService: generalVideoAuth insert avidRequest requestId:{}, request:{}", requestId, request);
			avidMapping.setLastRequestId(requestId.intValue());
			generalVideoAuthDaoService.avidMappingUpdate(avidMapping);
			log.info("VideoAuthRpcService: generalVideoAuth update avidMapping mappingId:{}, requestId:{}, request:{}", mappingId, requestId, request);
			// 插入 auth 表
			LauGeneralAvidAuthDto avidAuth = LauGeneralAvidAuthDto.builder()
					.mappingId(mappingId) // mapping表的主键Id
					.mid(arc.getAuthor().getMid())
					.face(arc.getAuthor().getFace())
					.name(arc.getAuthor().getName())
					.midAuthStatus(GeneralVideoEnum.AuthEnum.ACCEPTED.getCode())
					.requestId(requestId.intValue()).build(); // request 表的主键Id
			Integer authId = generalVideoAuthDaoService.avidAuthInsert(avidAuth).intValue();
			log.info("VideoAuthRpcService: generalVideoAuth insert avidAuth authId:{}, request:{}", authId, request);
			// 插入 record表
			Long recordId = generalVideoAuthDaoService.avidOperateRecordInsert(mappingId, GeneralVideoEnum.OperatorEnum.FIRST_AUTH.getCode(), account.getAccountId());
			log.info("VideoAuthRpcService: generalVideoAuth insert avidRecord recordId:{}, request:{}", recordId, request);
		}
	}

	@Transactional(value = AD_TM, rollbackFor = Exception.class)
	public void processOverride(GeneralVideoAuthReq request, List<LauGeneralAvidMappingDto> mappingListForOverride) {
		for (LauGeneralAvidMappingDto mappingDto : mappingListForOverride) {
			int mappingId = mappingDto.getId();
			// 更新 mapping 表
			LauGeneralAvidMappingDto avidMapping = LauGeneralAvidMappingDto.builder()
					.id(mappingId)
					.authStatus(GeneralVideoAuthStatusEnum.CONFIRM.getCode())
					.renewalStatus(GeneralVideoEnum.RenewalEnum.CONFIRM.getCode())
					.authMode(GeneralVideoEnum.AuthModelEnum.VIDEO_AND_MID.getCode())
					.authTimeType(GeneralVideoEnum.AuthTimeTypeEnum.LIMIT.getCode())
					.authEffectiveTime(new Timestamp(request.getAuthEffectiveTime()))
					.authExpireTime(new Timestamp(request.getAuthExpireTime())).build();
			generalVideoAuthDaoService.avidMappingUpdate(avidMapping);
			log.info("VideoAuthRpcService: generalVideoAuth process override, update avidMapping mappingId:{}, request:{}", mappingId, request);
			// 插入 record表
			Long recordId = generalVideoAuthDaoService.avidOperateRecordInsert(mappingId, GeneralVideoEnum.OperatorEnum.RENEWAL.getCode(), 0);
			log.info("VideoAuthRpcService: generalVideoAuth process override, insert avidRecord recordId:{}, mappingId:{}, request:{}", recordId, mappingId, request);
		}
	}
}
