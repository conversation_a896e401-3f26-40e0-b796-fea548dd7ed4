package com.bilibili.adp.cpc.biz.services.campaign.bos;

import com.bilibili.adp.common.annotation.DatabaseColumnName;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.dto.TargetUpgradeBiliClientVersionDto;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.dto.TargetUpgradeOsVersionDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * copy class
 * @see com.bilibili.adp.launch.api.effectad.dto.ManagedCampaignTargetDto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ManagedCampaignTargetDto {
    /**
     * 年龄定向
     */
    private List<Integer> age;
    /**
     * 性别定向
     */
    private List<Integer> gender;
    /**
     * 地域定向
     */
    private List<Integer> area;
    /**
     * 设备定向 iPhone iPad 安卓
     */
    private List<Integer> os;
    /**
     * 兴趣定向定向
     */
    private List<String> interestTags;
    /**
     * 是否模糊匹配
     */
    private Integer isFuzzyInterestTags;
    /**
     * 感兴趣的二级分区
     */
    private List<Integer> videoSecondPartition;
    /**
     * 视频定向
     */
    private List<String> videoTags;
    /**
     * 是否模糊匹配
     */
    private Integer isFuzzyVideoTags;
    /**
     * 粉丝关系定向-人群推荐类型
     * 1.核心
     * 2.优选
     * 3.潜在
     */
    private Integer recommendType;
    /**
     * 粉丝关系定向-投放关注以下UP主的粉丝
     */
    private List<Long> includeTheirsFans;
    /**
     * 粉丝关系定向-排除关注以下UP主的粉丝
     */
    private List<Long> excludeTheirsFans;
    /**
     * 网络定向
     */
    private List<Integer> network;
    /**
     * 人群包定向-投放人群包
     */
    private List<Integer> includeCrowdPackIds;
    /**
     * 人群包定向-排除人群包
     */
    private List<Integer> excludeCrowdPackIds;

    private Integer targetPackageId;

    private ManagedCampaignTargetInstalledUserFilterDto installedUserFilter;

    private List<Integer> areaType;

    @DatabaseColumnName("操作系统版本")
    private TargetUpgradeOsVersionDto osVersion;
    @DatabaseColumnName("bili客户端版本")
    private TargetUpgradeBiliClientVersionDto biliClientVersion;

    private Integer professionAuto;
    private List<Integer> professionInterest;
    private List<Integer> areaLevel;
    private List<Integer> phonePrice;



    public static final ManagedCampaignTargetDto EMPTY_INSTANCE = ManagedCampaignTargetDto
            .builder()
            .age(Collections.emptyList())
            .gender(Collections.emptyList())
            .area(Collections.emptyList())
            .os(Collections.emptyList())
            .interestTags(Collections.emptyList())
            .isFuzzyInterestTags(0)
            .videoSecondPartition(Collections.emptyList())
            .videoTags(Collections.emptyList())
            .isFuzzyVideoTags(0)
            .recommendType(0)
            .includeTheirsFans(Collections.emptyList())
            .excludeTheirsFans(Collections.emptyList())
            .network(Collections.emptyList())
            .includeCrowdPackIds(Collections.emptyList())
            .excludeCrowdPackIds(Collections.emptyList())
            .targetPackageId(0)
            .installedUserFilter(ManagedCampaignTargetInstalledUserFilterDto.emptyDto())
            .areaType(Collections.emptyList())
            .osVersion(null)
            .biliClientVersion(null)
            .build();
}
