package com.bilibili.adp.cpc.config.databus.creative;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bapis.infra.service.taishan.*;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.archive.CreativeBodyAuditService;
import com.bilibili.adp.cpc.biz.services.archive.bos.CreativeBodyAuditBo;
import com.bilibili.adp.cpc.biz.services.creative.bos.CreativeEsBo;
import com.bilibili.adp.cpc.config.CreativeEsConfig;
import com.bilibili.adp.cpc.config.databus.AccountMacheLabelBo;
import com.bilibili.adp.cpc.config.databus.SimpleLauUnitCreativeBo;
import com.bilibili.adp.cpc.config.taishan.TaishanConfig;
import com.bilibili.adp.cpc.converter.CreativeMapper;
import com.bilibili.adp.cpc.core.constants.AuditStatus;
import com.bilibili.adp.cpc.core.constants.IsManaged;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import com.bilibili.adp.cpc.databus.GenCreativeEsPub;
import com.bilibili.adp.cpc.repo.LauUnitCreativeRepo;
import com.bilibili.adp.cpc.taishan.TaishanService;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.google.protobuf.ByteString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/16 11:38 上午
 * @Version 1.0
 **/
@Component
@Slf4j
public class CreativeBinlog2ESMessageListener implements MessageListener {

    private static final String CREATIVE_BASE_INFO_CACHE_KEY_PREFIX = "mng:creative:base:info:";
    public static final String ACCOUNT_WHITELIST_CACHE_KEY_PREFIX = "mng:account:whitelist:";
    public static final int ACCOUNT_WHITELIST_CACHE_KEY_TTL = 60 * 60 * 24;

    public static final String CREATIVE_BINLOG = "creative-binlog-es";
    public static final String LAU_CREATIVE_AUDIT_RECALL = "lau_creative_audit_recall";
    public static final int MACHINE_LABEL_ID = 726;
    private final String topic;
    private final String group;

    @Value("${databus.creative.binlog2.enabled:true}")
    private Boolean enabled;

    @Value("${databus.creative.binlog2.es.enabled:true}")
    private Boolean esEnabled;
    @Autowired
    private CreativeEsConfig creativeEsConfig;
    @Autowired
    private Creative2EsService creative2EsService;
    @Autowired
    private CreativeBodyAuditService creativeBodyAuditService;
    @Autowired
    private TaishanService taishanService;
    @Resource(name = TaishanConfig.TAISHAN_MAP)
    private Map<String, String> tokenMap;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private LauUnitCreativeRepo lauUnitCreativeRepo;

    private final static ThreadLocal<GameCreativeInfoContext> context = new ThreadLocal<>();

    @Autowired
    private GenCreativeEsPub esPub;

    @Value("${databus.creative.binlog2.delay:500}")
    private long delay;

    private static final String ACTION = "action";
    private static final String INSERT = "insert";
    private static final String UPDATE = "update";
    private static final String DELETE = "delete";
    private static final String NEW = "new";
    private static final String OLD = "old";

    public CreativeBinlog2ESMessageListener(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get(CREATIVE_BINLOG);
        log.info("CreativeBinlog2ESMessageListener, property={}", JSONObject.toJSONString(property));
        this.topic = Objects.isNull(property) ? "" : property.getTopic();
        this.group = Objects.isNull(property) ? "" : property.getSub().getGroup();
    }

    private void handleMsg(String value) {
        log.info("CreativeBinlog2ESMessageListener, json={}", value);

        long start = System.currentTimeMillis();
        JSONObject jsonObject = JSONObject.parseObject(value);
        String action = jsonObject.getString(ACTION);
        if (StringUtils.isEmpty(action)) {
            return;
        }

        // 推送生成创意 es，index别名: adp-creative
        handleGenEs(jsonObject, action);

        long cost = Timestamp.valueOf(LocalDateTime.now()).getTime() - start;
        if (cost > delay) {
            log.info("CreativeBinlog2ESMessageListener, cost = {}", cost);
        }
    }

    private void handleMsgs(List<String> values) {
        log.info("CreativeBinlog2ESMessageListener, handleMsgs size={}", values.size());

        long start = System.currentTimeMillis();

        List<JSONObject> jsonObjects = values.stream().map(JSONObject::parseObject)
                .filter(jsonObject -> {
                    String action = jsonObject.getString(ACTION);
                    if (StringUtils.isEmpty(action)) {
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());


        List<LauUnitCreativePo> creativePos = jsonObjects.stream().map(jsonObject -> {
            log.info("CreativeBinlog2ESMessageListener, handleMsgs jsonObject={}", jsonObject.toJSONString());
            JSONObject newObject = jsonObject.getJSONObject(NEW);
            LauUnitCreativePo newBo = deserializeCreativeBinlogDto(newObject);
            return newBo;
        }).filter(creativePo -> {
            // 正常的创意
            if (Objects.isNull(creativePo)
                    || !Utils.isPositive(creativePo.getAccountId())
                    || StringUtils.isEmpty(creativePo.getCtime())) {
                return false;
            }

            if (Objects.equals(creativePo.getIsManaged(), IsManaged.EXPLORE)
                    && !Utils.isPositive(creativePo.getParentCreativeId())) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        // 预发环境时，esEnabled=false，不推送 es
        if (esEnabled) {
            // 推送生成创意 es，index别名: adp-creative
            pub2Es(creativePos);
        }
        saveVideoAudit(jsonObjects);

        // save to taishan
        List<SimpleLauUnitCreativeBo> unitCreativeBos = CreativeMapper.MAPPER.toBo(creativePos);
        try {
            save2Taishan(unitCreativeBos);
        } catch (Exception e) {
            log.error("save2Taishan error", e);
        }

        long cost = Timestamp.valueOf(LocalDateTime.now()).getTime() - start;
        if (cost > delay) {
            log.info("CreativeBinlog2ESMessageListener, handleMsgs cost = {}", cost);
        }
    }

    public void save2TaishanByIds(List<Integer> creativeIds) {
        List<com.bilibili.adp.launch.biz.pojo.LauUnitCreativePo> creativePos = lauUnitCreativeRepo.queryCreativesByCreativeIds(creativeIds);
        save2Taishan(CreativeMapper.MAPPER.toBos(creativePos));
        log.info("save2TaishanByIds, creativeIds={},tokenMap={}", creativeIds, tokenMap);
    }

    public void save2Taishan(List<SimpleLauUnitCreativeBo> creativePos) {

        long start = System.currentTimeMillis();
        // 创意
        List<Record> records = creativePos.stream().map(t -> {
            String key = t.getCreativeId() + CREATIVE_BASE_INFO_CACHE_KEY_PREFIX;
            String value = JSON.toJSONString(t);
            Record.Builder builder = Record.newBuilder();
            builder.setKey(ByteString.copyFromUtf8(key));
            if (value != null) {
                builder.addColumns(Column.newBuilder()
                        .setValue(ByteString.copyFromUtf8(value))
                        .build());
            }
            return builder.build();
        }).collect(Collectors.toList());
        BatchPutReq batchPutReq = BatchPutReq.newBuilder().addAllRecords(records)
                .setAuth(Auth.newBuilder().setToken(tokenMap.get(LAU_CREATIVE_AUDIT_RECALL)).build())
                .setTable(LAU_CREATIVE_AUDIT_RECALL)
                .build();
        BatchPutResp batchPutResp = taishanService.batchPut(batchPutReq);
        log.info("save2Taishan, batchPutResp={}", batchPutResp);

        // 账户机审标签
        List<Integer> accountIds = creativePos.stream().map(t -> t.getAccountId()).distinct().collect(Collectors.toList());
        Set<Integer> machineLabelAccountSet = accountLabelService.queryInLabelAccountIds(accountIds, MACHINE_LABEL_ID);
        if (!CollectionUtils.isEmpty(machineLabelAccountSet)) {
            List<Record> labelRecords = machineLabelAccountSet.stream().map(t -> TaishanService.recordBuilderWithTTL.apply(t + ACCOUNT_WHITELIST_CACHE_KEY_PREFIX, JSON.toJSONString(AccountMacheLabelBo.builder().accountId(t).machineFlag(true).build()), ACCOUNT_WHITELIST_CACHE_KEY_TTL)).collect(Collectors.toList());
            BatchPutReq batchPutReq1
                    = BatchPutReq.newBuilder().addAllRecords(labelRecords)
                    .setAuth(Auth.newBuilder().setToken(tokenMap.get(LAU_CREATIVE_AUDIT_RECALL)).build())
                    .setTable(LAU_CREATIVE_AUDIT_RECALL)
                    .build();
            BatchPutResp batchPutResp1 = taishanService.batchPut(batchPutReq);
        }
        long end = System.currentTimeMillis();
        log.info("save2Taishan, cost={}", end - start);
    }

    private void saveVideoAudit(List<JSONObject> jsonObjects) {

        List<LauUnitCreativePo> newCreativePos = new ArrayList<>();
        List<LauUnitCreativePo> oldCreativePos = new ArrayList<>();
        jsonObjects.forEach(jsonObject -> {
            log.info("CreativeBinlog2ESMessageListener, handleMsgs jsonObject={}", jsonObject.toJSONString());
            JSONObject old = jsonObject.getJSONObject(OLD);
            if (Objects.isNull(old)) {
                //  刚创建的 creative，没有 old，不需要落表
                return;
            }
            LauUnitCreativePo oldBo = deserializeCreativeBinlogDto(old);
            if (Objects.isNull(oldBo)) {
                return;
            }
            JSONObject newObject = jsonObject.getJSONObject(NEW);
            if (Objects.isNull(newObject)) {
                return;
            }
            LauUnitCreativePo newBo = deserializeCreativeBinlogDto(newObject);
            if (Objects.isNull(newBo)) {
                return;
            }
            if (newBo.getAuditStatus().equals(oldBo.getAuditStatus())) {
                // 审核状态没有发生变化时，不更新
                return;
            }

            // 审核状态发生变化时，需要更新
            newCreativePos.add(newBo);
            oldCreativePos.add(oldBo);
        });

        if (CollectionUtils.isEmpty(newCreativePos)) {
            log.info("CreativeBinlog2ESMessageListener, handleMsgs newCreativePos is empty");
            return;
        }

        // 需要insert or update的 videoId
        List<Long> needUpsertVideoIds = newCreativePos.stream()
                .map(LauUnitCreativePo::getVideoId).collect(Collectors.toList());
        log.info("CreativeBinlog2ESMessageListener, handleMsgs needUpsertVideoIds={}", needUpsertVideoIds);
        List<CreativeBodyAuditBo> creativeBodyAuditBos = creativeBodyAuditService.queryListByObjIds(needUpsertVideoIds);
        log.info("CreativeBinlog2ESMessageListener, handleMsgs creativeBodyAuditBos={}", creativeBodyAuditBos);

        Map<Long, CreativeBodyAuditBo> map = creativeBodyAuditBos.stream().collect(Collectors.toMap(CreativeBodyAuditBo::getObjId, x -> x, (x, y) -> y));
        Long mtime = System.currentTimeMillis();
        newCreativePos.forEach(x -> {
            if (Objects.isNull(x.getVideoId()) || x.getVideoId() == 0) {
                return;
            }
            if (!needUpsertVideoIds.contains(x.getVideoId())) {
                // 不需要 insert or update的 Video
                log.info("CreativeBinlog2ESMessageListener, handleMsgs needUpsertVideoIds={} dont need upsert", x.getVideoId());
                return;
            }
            if (AuditStatus.isPassed(x.getAuditStatus()) || AuditStatus.isRejected(x.getAuditStatus())) {
                // 只有审核通过 or 审核驳回时，才需要同步稿件的审核状态
                Long videoId = x.getVideoId();
                if (map.containsKey(videoId)) {
                    // lau_creative_body_audit 已经有该稿件时，则更新稿件审核状态
                    CreativeBodyAuditBo creativeBodyAuditBo = map.get(videoId);
                    if (Objects.isNull(creativeBodyAuditBo.getId()) || creativeBodyAuditBo.getId() == 0L) {
                        return;
                    }
                    // 审核状态没有发生变化时，也更新
                    creativeBodyAuditBo.setAuditStatus(x.getAuditStatus());
                    creativeBodyAuditBo.setReason(Optional.ofNullable(x.getReason()).orElse(""));
                    creativeBodyAuditBo.setMtime(mtime);
                    // 更新
                    log.info("saveVideoAudit, update creativeBodyAuditBo={}", creativeBodyAuditBo);
                    creativeBodyAuditService.update(creativeBodyAuditBo);
                    return;
                }

                CreativeBodyAuditBo creativeBodyAuditBo = CreativeBodyAuditBo.builder()
                        .objId(videoId)
                        .auditStatus(x.getAuditStatus())
                        .reason(Optional.ofNullable(x.getReason()).orElse(""))
                        .type(1) // 稿件
                        .build();
                // 插入
                log.info("saveVideoAudit, insert creativeBodyAuditBo={}", creativeBodyAuditBo);
                try {
                    Long insert = creativeBodyAuditService.insert(creativeBodyAuditBo);
                    creativeBodyAuditBo.setId(insert);
                    map.put(creativeBodyAuditBo.getObjId(), creativeBodyAuditBo);
                } catch (Exception e) {
                    log.error("saveVideoAudit, insert creativeBodyAuditBo error", e);
                }
                return;
            }
        });
    }

    private List<CreativeEsBo> pub2Es(List<LauUnitCreativePo> creativePos) {
        if (CollectionUtils.isEmpty(creativePos)) {
            return Collections.emptyList();
        }
        // 推送生成创意 es，index别名: adp-creative
        List<CreativeEsBo> creativeEsBos = creative2EsService.decorateEsPo(creativePos);
        if (CollectionUtils.isEmpty(creativeEsBos)) {
            return Collections.emptyList();
        }

        esPub.batchPub(creativeEsBos);
        return creativeEsBos;
    }

    private void handleGenEs(JSONObject jsonObject, String action) {
        JSONObject newObject = jsonObject.getJSONObject(NEW);
        LauUnitCreativePo newBo = deserializeCreativeBinlogDto(newObject);
        // 正常的创意
        if (Objects.isNull(newBo)
                || !Utils.isPositive(newBo.getAccountId())
                || StringUtils.isEmpty(newBo.getCtime())) {
            return;
        }

        List<CreativeEsBo> creativeEsBos = creative2EsService.decorateEsPo(Collections.singletonList(newBo));
        if (CollectionUtils.isEmpty(creativeEsBos)) {
            return;
        }

        JSONObject newJson = new JSONObject();
        newJson.put("new", creativeEsBos.get(0));
        newJson.put("action", action);

        esPub.pub(newJson, newBo.getCreativeId());
    }

    private LauUnitCreativePo deserializeCreativeBinlogDto(JSONObject insertObject) {
        if (Objects.isNull(insertObject)) {
            return null;
        }
        LauUnitCreativePo LauUnitCreativePo = null;
        try {
            LauUnitCreativePo = insertObject.toJavaObject(LauUnitCreativePo.class);
        } catch (Exception e) {
            log.error("deserializeCreativeBinlogDto error, insertObject:{}", insertObject, e);
        }
        return LauUnitCreativePo;
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

//    @Override
//    public void onMessage(AckableMessage ackableMessage) {
//        log.info("wrap onMessage, CreativeBinlog2ESMessageListener ackableMessage={}", JSON.toJSONString(ackableMessage));
//        if (!Boolean.TRUE.equals(enabled)) {
//            return;
//        }
//
//        try {
//            AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, CREATIVE_BINLOG + ":sub", transaction -> {
//                String value = new String(ackableMessage.payload());
//                handleMsg(value);
//            });
//        } catch (Exception e) {
//            log.error("CreativeBinlogSubTask error", e);
//        }
//
//    }

    @Override
    public void onMessages(AckableMessages messages) {
        List<AckableMessage> messagess = messages.getMessages();
        log.info("wrap onMessage, CreativeBinlog2ESMessageListener ackableMessage={}", messagess.size());
        if (!Boolean.TRUE.equals(enabled)) {
            return;
        }
        try {
            AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, CREATIVE_BINLOG + ":batchSub", transaction -> {
                List<String> values = messagess.stream().map(x -> new String(x.payload())).collect(Collectors.toList());
                handleMsgs(values);
            });
        } catch (Exception e) {
            log.error("CreativeBinlogSubTask error", e);
        }
    }

    @Override
    public boolean autoCommit() {
        return true;
    }
}
