package com.bilibili.adp.cpc.biz.services.unit;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.campaign.api.ILauCampaignService;
import com.bilibili.adp.cpc.biz.services.unit.api.ILauUnitService;
import com.bilibili.adp.cpc.dao.ad.AdpLauCampaignNextdayBudgetDao;
import com.bilibili.adp.cpc.dao.ad.AdpLauUnitNextdayBudgetDao;
import com.bilibili.adp.cpc.dto.NewLauCampaignNextdayBudgetDto;
import com.bilibili.adp.cpc.dto.NewLauUnitNextdayBudgetDto;
import com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPo;
import com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPoExample;
import com.bilibili.adp.cpc.po.ad.LauUnitNextdayBudgetPo;
import com.bilibili.adp.cpc.po.ad.LauUnitNextdayBudgetPoExample;
import com.bilibili.adp.launch.api.campaign.dto.LauCampaignBudgetDto;
import com.bilibili.adp.launch.api.common.BudgetLimitType;
import com.bilibili.adp.launch.api.common.LaunchConstant;
import com.bilibili.adp.launch.api.unit.dto.LauUnitBudgetDto;
import com.bilibili.adp.launch.biz.config.UpOrderConfig;
import com.bilibili.adp.launch.biz.lau_dao.LauCampaignBudgetDao;
import com.bilibili.adp.launch.biz.lau_dao.LauUnitBudgetDao;
import com.bilibili.adp.launch.biz.pojo.LauCampaignBudgetPo;
import com.bilibili.adp.launch.biz.pojo.LauCampaignBudgetPoExample;
import com.bilibili.adp.launch.biz.pojo.LauUnitBudgetPo;
import com.bilibili.adp.launch.biz.pojo.LauUnitBudgetPoExample;
import com.bilibili.adp.launch.biz.repo.LauUnitRepo;
import com.bilibili.adp.util.common.DistributedLock;
import com.google.common.collect.Maps;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;

/**
 * copy class
 * @see com.bilibili.adp.launch.biz.service.LauBudgetService
 */
@Primary
@Service(value = "LaunchBudgetService")
public class LauBudgetService {

    @Autowired
    private LauCampaignBudgetDao lauCampaignBudgetDao;
    @Autowired
    private AdpLauCampaignNextdayBudgetDao lauCampaignNextdayBudgetDao;
    @Autowired
    private AdpLauUnitNextdayBudgetDao lauUnitNextdayBudgetDao;
    @Autowired
    private ILauCampaignService lauCampaignService;

    @Autowired
    private LauUnitBudgetDao lauUnitBudgetDao;
    @Autowired
    private ILauUnitService lauUnitService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private UpOrderConfig upOrderConfig;
    @Autowired
    private LauUnitRepo lauUnitRepo;

    @Autowired
    private LauBudgetServiceDelegate lauBudgetServiceDelegate;

    private static final Logger LOGGER = LoggerFactory.getLogger(LauBudgetService.class);

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void insertCampaignBudget(Operator operator, Integer campaignId, Long budget) throws ServiceException {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(campaignId, "计划ID不可为空");
        Assert.notNull(budget, "预算不可为空");

        LOGGER.info("insertCampaignBudget.param operator-{}, campaignId-{}, budget-{}", operator, campaignId, budget);

        BigDecimal campaignBudget = this.getCampaignBudget(campaignId, Utils.getNow());
        Assert.isTrue(BigDecimal.ZERO.compareTo(campaignBudget) == 0, "接口调用错误");

        long budgetLimit =  getCampaignBudgetLimit(operator);
        Assert.isTrue(budget.compareTo(budgetLimit) >= 0, "计划预算不可低于" + Utils.fromFenToYuan(budgetLimit) + "元");
        lauBudgetServiceDelegate.insertCampaignBudget(campaignId, budget);
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void insertCampaignBudgetWithBudgetLimitType(Operator operator, Integer campaignId, Long budget, Integer budgetLimitType) throws ServiceException {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(campaignId, "计划ID不可为空");

        LOGGER.info("insertCampaignBudgetWithBudgetLimitType.param operator-{}, campaignId-{}, budget-{}, budgetLimitType-{}", operator, campaignId, budget, budgetLimitType);

        BigDecimal campaignBudget = this.getCampaignBudget(campaignId, Utils.getNow());
        Assert.isTrue(BigDecimal.ZERO.compareTo(campaignBudget) == 0, "接口调用错误");

        BudgetLimitType budgetLimitTypeEnum = BudgetLimitType.getByCode(budgetLimitType) == null ? BudgetLimitType.DESIGNATE : BudgetLimitType.getByCode(budgetLimitType);

        // 日预算，需要检验预算最小金额
        if (BudgetLimitType.DESIGNATE == budgetLimitTypeEnum) {
            Assert.notNull(budget, "预算不可为空");
            // 获取计划最小预算(走的配置)
            long budgetLimit = getCampaignBudgetLimit(operator);
            Assert.isTrue(budget.compareTo(budgetLimit) >= 0, "计划预算不可低于" + Utils.fromFenToYuan(budgetLimit) + "元");

        }
        // 新建计划预算
        lauBudgetServiceDelegate.insertCampaignBudget(campaignId, budget);
    }

    public void updateCampaignBudget(Operator operator, Integer campaignId, Long budget,Integer budgetType) throws ServiceException {

        RLock lock = distributedLock.getLock(campaignId, LaunchConstant.CAMPAIGN_BUDGET_LOCK_SUFFIX);
        try {
            lauBudgetServiceDelegate.updateCampaignBudget(operator, campaignId, budget,budgetType);
        } finally {
            LOGGER.info("---updateCampaignBudget unLock: {}{}----", campaignId, LaunchConstant.CAMPAIGN_BUDGET_LOCK_SUFFIX);
            lock.unlock();
        }
    }

    /**
     * 插入单元预算
     *
     * @param operator
     * @param unitId
     * @param budget
     * @throws ServiceException
     */
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void insertUnitBudget(Operator operator, Integer unitId, Long budget) throws ServiceException {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(unitId, "单元ID不可为空");
        Assert.notNull(budget, "预算不可为空");

        LOGGER.info("insertUnitBudget.param operator-{}, unitId-{}, budget-{}", operator, unitId, budget);

        BigDecimal unitBudget = this.getUnitBudget(unitId, Utils.getNow());
        Assert.isTrue(BigDecimal.ZERO.compareTo(unitBudget) == 0, "接口调用错误");

        // 单元预算限制校验
        long budgetLimit = getUnitBudgetLimit(operator);
        Assert.isTrue(budget.compareTo(budgetLimit) >= 0, "单元预算不可低于" + Utils.fromFenToYuan(budgetLimit) + "元");

        // 插入新的单元预算
        lauBudgetServiceDelegate.insertUnitBudget(unitId, budget);
    }

    /**
     * 修改单元预算
     *
     * @param operator
     * @param unitId
     * @param budget
     * @throws ServiceException
     */
    public void updateUnitBudget(Operator operator, Integer unitId, Long budget) throws ServiceException {
        RLock lock = distributedLock.getLock(unitId, LaunchConstant.UNIT_BUDGET_LOCK_SUFFIX);
        try {
            lauBudgetServiceDelegate.updateUnitBudget(operator, unitId, budget);
        } finally {
            LOGGER.info("---updateUnitBudget unLock: {}{}----", unitId, LaunchConstant.UNIT_BUDGET_LOCK_SUFFIX);
            lock.unlock();
        }
    }

    public BigDecimal getUnitBudget(Integer unitId, Timestamp effectiveTime) {
        Assert.notNull(unitId, "单元ID不可为空");
        Assert.notNull(effectiveTime, "生效时间不可为空");

        lauUnitService.getBaseDtoById(unitId);

        LauUnitBudgetPoExample example = new LauUnitBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdEqualTo(unitId)
                .andEffectiveTimeLessThanOrEqualTo(Utils.getEndOfDay(effectiveTime));
        example.setOrderByClause("effective_time desc");
        example.setLimit(1);

        List<LauUnitBudgetPo> lauUnitBudgetPos = lauUnitBudgetDao.selectByExample(example);
        if (CollectionUtils.isEmpty(lauUnitBudgetPos)) {
            return BigDecimal.ZERO;
        }
        return Utils.fromFenToYuan(lauUnitBudgetPos.get(0).getBudget());
    }

    public BigDecimal getCampaignBudget(Integer campaignId, Timestamp effectiveTime) {
        Assert.notNull(campaignId, "计划ID不可为空");
        Assert.notNull(effectiveTime, "生效时间不可为空");

        lauCampaignService.getCampaignDtoById(campaignId);

        LauCampaignBudgetPoExample example = new LauCampaignBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignIdEqualTo(campaignId)
                .andEffectiveTimeLessThanOrEqualTo(Utils.getEndOfDay(effectiveTime));
        example.setOrderByClause("effective_time desc");
        example.setLimit(1);

        List<LauCampaignBudgetPo> lauCampaignBudgetPos = lauCampaignBudgetDao.selectByExample(example);
        if (CollectionUtils.isEmpty(lauCampaignBudgetPos)) {
            return BigDecimal.ZERO;
        }
        return Utils.fromFenToYuan(lauCampaignBudgetPos.get(0).getBudget());
    }

    public Map<Integer, BigDecimal> getCampaignBudgetMapInIdsAndTime(List<Integer> campaignIds, Timestamp effectiveTime) {
        if (CollectionUtils.isEmpty(campaignIds) || effectiveTime == null) {
            return Collections.emptyMap();
        }

        LauCampaignBudgetPoExample example = new LauCampaignBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignIdIn(campaignIds)
                .andEffectiveTimeLessThanOrEqualTo(Utils.getEndOfDay(effectiveTime));
        List<LauCampaignBudgetPo> campaignBudgets = lauCampaignBudgetDao.selectByExample(example);

        Map<Integer, Optional<Long>> campaignOptionalBudget = campaignBudgets.stream().collect(Collectors.groupingBy(LauCampaignBudgetPo::getCampaignId, Collectors.mapping(LauCampaignBudgetPo::getBudget, Collectors.maxBy(Comparator.comparing(Function.identity())))));
        if (CollectionUtils.isEmpty(campaignOptionalBudget)) {
            return Collections.emptyMap();
        }

        Map<Integer, BigDecimal> campaignBudgetMap = Maps.newHashMap();
        campaignOptionalBudget.keySet().forEach(key -> campaignBudgetMap.put(key, Utils.fromFenToYuan(campaignOptionalBudget.get(key).orElse(0L))));
        return campaignBudgetMap;
    }

    public Map<Integer, List<NewLauCampaignNextdayBudgetDto>> getCampaignNextdayBudgetMapInIds(List<Integer> campaignIds) {
        if (CollectionUtils.isEmpty(campaignIds)) {
            return Collections.emptyMap();
        }

        LauCampaignNextdayBudgetPoExample example = new LauCampaignNextdayBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignIdIn(campaignIds);
        List<LauCampaignNextdayBudgetPo> nextdayBudgetPoList = lauCampaignNextdayBudgetDao.selectByExample(example);

        if (CollectionUtils.isEmpty(nextdayBudgetPoList)) {
            return Collections.emptyMap();
        }

        return nextdayBudgetPoList
                .stream()
                .map(po -> campaignNextdayBudgetPoToDto(po))
                .collect(Collectors.groupingBy(NewLauCampaignNextdayBudgetDto::getCampaignId, Collectors.toList()));
    }

    private NewLauCampaignNextdayBudgetDto campaignNextdayBudgetPoToDto(LauCampaignNextdayBudgetPo po) {
        NewLauCampaignNextdayBudgetDto dto = NewLauCampaignNextdayBudgetDto.builder()
                .id(po.getId())
                .campaignId(po.getCampaignId())
                .nextdayBudget(po.getBudget())
                .nextdayBudgetLimitType(po.getBudgetLimitType())
                .isRepeat(po.getIsRepeat())
                .build();
        return dto;
    }

    public Map<Integer, List<NewLauUnitNextdayBudgetDto>> getUnitNextdayBudgetMapInIds(List<Integer> unitIds) {
        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.emptyMap();
        }

        LauUnitNextdayBudgetPoExample example = new LauUnitNextdayBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdIn(unitIds);
        List<LauUnitNextdayBudgetPo> nextdayBudgetPoList = lauUnitNextdayBudgetDao.selectByExample(example);

        if (CollectionUtils.isEmpty(nextdayBudgetPoList)) {
            return Collections.emptyMap();
        }

        return nextdayBudgetPoList
                .stream()
                .map(po -> unitNextdayBudgetPoToDto(po))
                .collect(Collectors.groupingBy(NewLauUnitNextdayBudgetDto::getUnitId, Collectors.toList()));
    }

    private NewLauUnitNextdayBudgetDto unitNextdayBudgetPoToDto(LauUnitNextdayBudgetPo po) {
        NewLauUnitNextdayBudgetDto dto = NewLauUnitNextdayBudgetDto.builder()
                .id(po.getId())
                .unitId(po.getUnitId())
                .nextdayBudget(po.getBudget())
                .isRepeat(po.getIsRepeat()).build();
        return dto;
    }

    public Map<Integer, BigDecimal> getUnitBudgetMapInIdsAndTime(List<Integer> unitIds, Timestamp effectiveTime) {
        if (CollectionUtils.isEmpty(unitIds) || effectiveTime == null) {
            return Collections.emptyMap();
        }

        List<LauUnitBudgetPo> unitBudgets = getLauUnitBudgetPos(unitIds, effectiveTime);

        Map<Integer, Optional<Long>> unitOptionalBudget = unitBudgets.stream().collect(Collectors.groupingBy(LauUnitBudgetPo::getUnitId, Collectors.mapping(LauUnitBudgetPo::getBudget, Collectors.maxBy(Comparator.comparing(Function.identity())))));
        if (CollectionUtils.isEmpty(unitOptionalBudget)) {
            return Collections.emptyMap();
        }

        Map<Integer, BigDecimal> unitBudgetMap = Maps.newHashMap();
        unitOptionalBudget.keySet().forEach(key -> unitBudgetMap.put(key, Utils.fromFenToYuan(unitOptionalBudget.get(key).orElse(0L))));
        return unitBudgetMap;
    }

    private List<LauUnitBudgetPo> getLauUnitBudgetPos(List<Integer> unitIds, Timestamp effectiveTime) {
        LauUnitBudgetPoExample example = new LauUnitBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdIn(unitIds)
                .andEffectiveTimeLessThanOrEqualTo(Utils.getEndOfDay(effectiveTime));
        return lauUnitBudgetDao.selectByExample(example);
    }

    /**
     * 获取计划修改次数
     *
     * @param campaignIds
     * @return
     */
    public Map<Integer, Long> getBudgetRecordCountMapInCampaignIds(List<Integer> campaignIds) {
        if (CollectionUtils.isEmpty(campaignIds)) {
            return Collections.emptyMap();
        }

        LauCampaignBudgetPoExample example = new LauCampaignBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignIdIn(campaignIds)
                .andEffectiveTimeGreaterThanOrEqualTo(Utils.getBeginOfDay(Utils.getNow()))
                .andEffectiveTimeLessThanOrEqualTo(Utils.getEndOfDay(Utils.getNow()));

        return lauCampaignBudgetDao.selectByExample(example)
                .stream()
                .collect(Collectors.groupingBy(LauCampaignBudgetPo::getCampaignId, Collectors.counting()));
    }

    public Map<Integer, List<LauUnitBudgetDto>> getUnitBudgetDtoMapGreaterTimestamp(List<Integer> unitIds, Timestamp timestamp) {
        LauUnitBudgetPoExample example = new LauUnitBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdIn(unitIds)
                .andEffectiveTimeGreaterThan(timestamp);

        List<LauUnitBudgetPo> pos = lauUnitBudgetDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        return pos
                .stream()
                .map(po -> unitPoToUnitDto(po))
                .collect(Collectors.groupingBy(LauUnitBudgetDto::getUnitId, Collectors.toList()));
    }

    public List<LauUnitBudgetDto> getUnitBudgetDtosGreaterTimestamp(Integer unitId, Timestamp timestamp) {
        LauUnitBudgetPoExample example = new LauUnitBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdEqualTo(unitId)
                .andEffectiveTimeGreaterThan(timestamp);

        List<LauUnitBudgetPo> pos = lauUnitBudgetDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return pos
                .stream()
                .map(po -> unitPoToUnitDto(po))
                .collect(Collectors.toList());
    }

    public Map<Integer, List<LauCampaignBudgetDto>> getCampaignBudgetDtoMapGreaterTimestamp(List<Integer> campaignIds, Timestamp timestamp) {

        LauCampaignBudgetPoExample example = new LauCampaignBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignIdIn(campaignIds)
                .andEffectiveTimeGreaterThan(timestamp);

        List<LauCampaignBudgetPo> pos = lauCampaignBudgetDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }
        return pos
                .stream()
                .map(po -> campaignPoToCampainDto(po))
                .collect(Collectors.groupingBy(LauCampaignBudgetDto::getCampaignId, Collectors.toList()));
    }

    public List<LauCampaignBudgetDto> getCampaignBudgetDtosGreaterTimestamp(Integer campaignId, Timestamp timestamp) {

        LauCampaignBudgetPoExample example = new LauCampaignBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignIdEqualTo(campaignId)
                .andEffectiveTimeGreaterThan(timestamp);

        List<LauCampaignBudgetPo> pos = lauCampaignBudgetDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos
                .stream()
                .map(po -> campaignPoToCampainDto(po))
                .collect(Collectors.toList());
    }

    private LauUnitBudgetDto unitPoToUnitDto(LauUnitBudgetPo po) {
        LauUnitBudgetDto dto = new LauUnitBudgetDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    private LauCampaignBudgetDto campaignPoToCampainDto(LauCampaignBudgetPo po) {
        LauCampaignBudgetDto dto = new LauCampaignBudgetDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * 获取今天单元预算修改次数 lau_unit_budget
     *
     * @param unitId
     * @return
     */
    public long getBudgetRecordCountByUnitId(Integer unitId) {
        return lauUnitRepo.getBudgetRecordCountByUnitId(unitId);
    }

    public void validateDpaCampaignBudgetByCampaignIds(List<Integer> campaignIds, Long modifiedBudget, Integer accountId) {
        lauBudgetServiceDelegate.validateDpaCampaignBudgetByCampaignIds(campaignIds, modifiedBudget,accountId);
    }

    public void validateNoDpaCampaignBudgetByCampaignIds(List<Integer> campaignIds, Long modifiedBudget, Integer accountId) {
        lauBudgetServiceDelegate.validateNoDpaCampaignBudgetByCampaignIds(campaignIds, modifiedBudget, accountId);
    }

    public NewLauCampaignNextdayBudgetDto getCampaignNextdayBudgetDto(Integer campaignId) throws ServiceException {
        LauCampaignNextdayBudgetPoExample example = new LauCampaignNextdayBudgetPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignIdEqualTo(campaignId);
        example.setLimit(1);
        List<LauCampaignNextdayBudgetPo> nextdayBudgetPoList = lauCampaignNextdayBudgetDao.selectByExample(example);

        if (CollectionUtils.isEmpty(nextdayBudgetPoList)) {
            return null;
        }
        return campaignNextdayBudgetPoToDto(nextdayBudgetPoList.get(0));

    }

    /**
     * 获取计划最小预算(走的配置)
     *
     * @param operator
     * @return
     */
    public long getCampaignBudgetLimit(Operator operator) {
        return lauBudgetServiceDelegate.getCampaignBudgetLimit(operator);
    }

    public long getUnitBudgetLimit(Operator operator) {
        return lauBudgetServiceDelegate.getUnitBudgetLimit(operator);
    }
}
