package com.bilibili.adp.cpc.biz.services.log;

import com.bilibili.adp.common.DbTableInterface;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.cpc.biz.bos.log.LogOperateForLongDto;
import com.bilibili.adp.cpc.biz.bos.log.QueryLogOperateForLongDto;
import com.bilibili.adp.log.bean.DiffItem;

import java.util.List;

/**
 * Created by many2023 on 17/06/08.
 */
public interface ILogOperateForLongService {
    
    public void addInsertLog(DbTableInterface table, Operator operator, Object value, Long id);
    
    public void addUpdateLog(DbTableInterface table, Operator operator, Object value, Long id);
    
    public void addInsertLog(DbTableInterface table, Operator operator, List<Object> values, Long id);
    
    public void addUpdateLog(List<DbTableInterface> tables, Operator operator, List<Object> values, List<Long> ids);
    
    public void addUpdateStatusLog(DbTableInterface table, Operator operator, Object value, Long id);
    
    public void addDeleteLog(DbTableInterface table, Operator operator, Object value, Long id);
    
    public void addDeleteLog(List<DbTableInterface> tables, Operator operator, List<Object> values, List<Long> ids);
    
    public void addBatchInsertLog(DbTableInterface table, Operator operator, List<? extends Object> values, List<Long> ids);
    
    public void addBatchDeleteLog(DbTableInterface table, Operator operator, Object value, List<Long> ids);
    
    public void addBatchUpdateLog(DbTableInterface table, Operator operator, Object value, List<Long> ids);
    
    public void addBatchUpdateStatusLog(DbTableInterface table, Operator operator, Object value, List<Long> ids);
    
    public PageResult<LogOperateForLongDto> queryByPage(QueryLogOperateForLongDto queryDto, Page page);

	void addUpdateLog(DbTableInterface table, Operator operator, Object oldObject, Object newObject, Long id);
	
	public void addBatchUpdateStatusLog(DbTableInterface table, Operator operator, Object value, List<Long> ids, List<Integer> accountIds);

	public void addBatchReCheckLog(DbTableInterface table, Operator operator, Object value, List<Long> ids, List<Integer> accountIds);

    public void addBatchUnderFrameAuditLog(DbTableInterface table, Operator operator, Object value, List<Long> ids, List<Integer> accountIds);

    public void addBatchComplexMarkLog(DbTableInterface table, Operator operator, Object value, List<Long> ids, List<Integer> accountIds);

    String genHtmlDiffs(List<DiffItem> diffs, Integer operationType);

	void addUpdateLog(DbTableInterface table, Operator operator, List<Object> oldObjects, List<Object> newObjects, Long id);

	void addBatchUpdateLog(DbTableInterface table, Operator operator, List<? extends Object> oldObjects, List<? extends Object> newObjects,
	        List<Long> ids);

    void addDeleteLog(DbTableInterface table, Operator operator, Object oldObject, Object newObject, Long id);

    //这个接口只实现了插入的父类diff 其他操作没有实现 慎用
    void addBatchInsertLogWithParentClass(DbTableInterface table, Operator operator, List<? extends Object> values, List<Long> ids);

    void addUpdateLogWithParentClass(DbTableInterface table, Operator operator, Object oldObject, Object newObject, Long id);

}
