package com.bilibili.adp.cpc.repo;

import com.bilibili.adp.cpc.dao.querydsl_es.ElasticsearchCloudConfig;
import com.bilibili.adp.cpc.dao.querydsl_es.ElasticsearchQueryUtil;
import com.bilibili.adp.cpc.dao.querydsl_es.pos.TitlePO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/7/15 下午2:25
 */
@Slf4j
@Repository
public class TitleAIRepo {
    @Value("${elasticsearch.cloud.config.titleSuggestAI.token:i-kcDj89TF8t3sMM1z-csZjs3Em-tFK_u02FSXBG4Co}")
    private String token;
    @Value("${elasticsearch.cloud.config.titleSuggestAI.index:sycpb-aigc-account-titles}")
    private String index;
    @Value("${elasticsearch.cloud.config.domain:http://olap-search.bilibili.co}")
    private String domain;
    private static final int zero = 0;

    private static ElasticsearchCloudConfig config;

    private ElasticsearchCloudConfig getESConfig() {
        if (config == null) {
            config = ElasticsearchCloudConfig.builder().token(token).index(index).domain(domain).build();
        }
        return config;
    }

    public List<TitlePO> getTitlesAI(Integer accountId, int size) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("account_id", accountId)); // 精准匹配查询
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.from(zero);
        searchSourceBuilder.size(size);
        searchSourceBuilder.sort("score", SortOrder.DESC)
                .size(size);
        return ElasticsearchQueryUtil.getResultFromEsCloud(searchSourceBuilder,
                getESConfig(), null, TitlePO.class);
    }
}
