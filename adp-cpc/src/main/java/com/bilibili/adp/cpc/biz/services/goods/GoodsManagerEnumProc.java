package com.bilibili.adp.cpc.biz.services.goods;

import com.bapis.ad.cmc.cidgoods.SourceType;
import com.bapis.ad.cmc.cidgoods.UrlType;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/4 11:19
 */
@Component
public class GoodsManagerEnumProc {

    // 根据中文描述翻译成枚举
    public static Map<String, UrlType> URL_TYPE_MAP = new HashMap<>();
    public static Map<String, SourceType> SOURCE_TYPE_NAME_MAP = new HashMap<>();
    public static Set<Integer> SOURCE_TYPE_CODES = new HashSet<>();

    static {
        URL_TYPE_MAP.put("跳转端内落地页", UrlType.H5);
        URL_TYPE_MAP.put("唤起第三方应用", UrlType.APP);
        URL_TYPE_MAP.put("唤起微信小程序", UrlType.WE_CHAT);

//        SOURCE_TYPE_MAP.put("淘宝", SourceType.TAO_BAO);
//        SOURCE_TYPE_MAP.put("会员购", SourceType.PROPRIETARY);
//        SOURCE_TYPE_MAP.put("京东", SourceType.JD);
//        SOURCE_TYPE_MAP.put("工房", SourceType.SMALL_SHOP);
//        SOURCE_TYPE_MAP.put("会员购", SourceType.PROPRIETARY_CPS);
//        SOURCE_TYPE_MAP.put("数字藏品", SourceType.VIRTUAL_GOODS_SHUZICANGPIN);
//        SOURCE_TYPE_MAP.put("个性装扮", SourceType.VIRTUAL_GOODS_GEXINGZHUANGBAN);
//        SOURCE_TYPE_MAP.put("up主小店", SourceType.PROPRIETARY_THIRD_PARTY);
//        SOURCE_TYPE_MAP.put("课程", SourceType.VIRTUAL_GOODS_COURSE);
//        SOURCE_TYPE_MAP.put("拼多多", SourceType.PDD);
//        SOURCE_TYPE_MAP.put("有赞", SourceType.YOUZAN);
//        SOURCE_TYPE_MAP.put("唯品会-临时", SourceType.TENTATIVE_VIP);
//        SOURCE_TYPE_MAP.put("STEAM", SourceType.TENTATIVE_GAME_STEAM);
//        SOURCE_TYPE_MAP.put("XGP", SourceType.TENTATIVE_GAME_XGP);

        // 只有8种
        SOURCE_TYPE_NAME_MAP.put("阿里健康链接", SourceType.TENTATIVE_ALI_HEALTH);
        SOURCE_TYPE_NAME_MAP.put("淘宝普通链接", SourceType.TENTATIVE_TAOBAO_UNIVERSAL);
        SOURCE_TYPE_NAME_MAP.put("淘宝cid链接", SourceType.TENTATIVE_TAOBAO_CID);
        SOURCE_TYPE_NAME_MAP.put("京东cid链接", SourceType.TENTATIVE_JD_CID);
        SOURCE_TYPE_NAME_MAP.put("京东普通链接", SourceType.TENTATIVE_JD_UNIVERSAL);
        SOURCE_TYPE_NAME_MAP.put("拼多多cid链接", SourceType.TENTATIVE_PDD_CID);
        SOURCE_TYPE_NAME_MAP.put("拼多多普通链接", SourceType.TENTATIVE_PDD_UNIVERSAL);
        SOURCE_TYPE_NAME_MAP.put("泛电商", SourceType.TENTATIVE_EXTENSIVE_EC);
        SOURCE_TYPE_NAME_MAP.put("京东会场链接",SourceType.TENTATIVE_JD_ACTIVITY);


        SOURCE_TYPE_CODES = SOURCE_TYPE_NAME_MAP.values().stream().map(t -> t.getNumber()).collect(Collectors.toSet());
    }

    public String getSourceTypeName(Integer sourceTypeCode) {
        Set<Map.Entry<String, SourceType>> entries = SOURCE_TYPE_NAME_MAP.entrySet();
        for (Map.Entry<String, SourceType> entry : entries) {
            SourceType sourceType = entry.getValue();
            if (Objects.equals(sourceTypeCode, sourceType.getNumber())) {
                return entry.getKey();
            }
        }
        return "未知";
    }

}
