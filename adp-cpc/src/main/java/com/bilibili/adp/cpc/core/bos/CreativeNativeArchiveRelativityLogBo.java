package com.bilibili.adp.cpc.core.bos;

import com.bilibili.adp.common.annotation.DatabaseColumnEnum;
import com.bilibili.adp.common.annotation.DatabaseColumnName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreativeNativeArchiveRelativityLogBo {

    @DatabaseColumnName(value = "审核状态")
    @DatabaseColumnEnum("{'1':'审核中', '2':'审核通过', '3':'审核拒绝'}")
    private Integer auditStatus;

    private Long avid;

    private Integer creativeId;

    private Long id;

    private Integer isRecheck;

    private java.sql.Timestamp mtime;

    @DatabaseColumnName(value = "拒审原因")
    private String reason;

    private Integer type;

    @DatabaseColumnName(value = "version")
    private Integer version;
}
