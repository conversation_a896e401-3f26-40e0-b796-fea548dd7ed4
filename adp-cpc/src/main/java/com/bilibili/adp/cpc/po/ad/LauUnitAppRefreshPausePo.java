package com.bilibili.adp.cpc.po.ad;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauUnitAppRefreshPausePo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 单元ID
     */
    private Integer unitId;

    /**
     * 应用包id
     */
    private Integer appPackageId;

    /**
     * 页面id
     */
    private Long pageId;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 处理状态 0-未处理 1-处理完成
     */
    private Integer dealStatus;

    /**
     * 是否删除0否 1是
     */
    private Integer isDeleted;

    private Timestamp ctime;

    private Timestamp mtime;

    /**
     * 旧的应用链接
     */
    private String oldApkUrl;

    /**
     * 新的应用链接
     */
    private String newApkUrl;

    private static final long serialVersionUID = 1L;
}