package com.bilibili.adp.cpc.biz.services.creative.bos.programmatic;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialBo {
    private Long materialId;
    private Integer templateGroupId;
    private String md5;
    private Integer bizStatus;
    private String rejectedReason;
    private TitleBo title;
    private MonoMediaBo monoMedia;
    private MgkVideoMediaBo mgkVideoMedia;
    private ImageSetMediaBo imageSetMedia;
    private BiliVideoMediaBo biliVideoMedia;
}
