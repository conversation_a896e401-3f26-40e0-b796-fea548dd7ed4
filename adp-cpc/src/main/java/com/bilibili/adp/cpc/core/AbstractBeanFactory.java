package com.bilibili.adp.cpc.core;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 实现类的bean工厂
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractBeanFactory<T> {

    public AbstractBeanFactory() {
        log.info("new bean factory:" + this.getClass().getName());
    }

    public final Function<Entry<String, Class<T>>, T> DEFAULT_BEAN_CREATOR = new DefaultBeanCreator();

    @Autowired
    protected BeanFactory beanFactory;

    @Autowired
    protected Map<String, T> beanNameMap;

    @Autowired
    protected List<T> beanList;

    public T getByName(String key) {
        return beanNameMap.get(key);
    }

    public T getThrowableByName(String key) {
        return this.getOptionalByName(key).orElseThrow(() -> new RuntimeException("bean is not found by name:" + key));
    }

    public Optional<T> getOptionalByName(String key) {
        return Optional.ofNullable(beanNameMap.get(key));
    }

    public <V extends T> V getByClass(Class<V> cls) {
        return beanFactory.getBean(cls);
    }

    public <V extends T> Optional<V> getOptionalByClass(Class<V> cls) {
        return Optional.ofNullable(beanFactory.getBean(cls));
    }

    public <V extends T> V getThrowableByClass(Class<V> cls) {
        return getOptionalByClass(cls).orElseThrow(() -> new RuntimeException("bean is not found by class:" + cls));
    }

    public T get(String name, Class<? extends T> cls) {
        return Optional.ofNullable(beanNameMap.get(name)).orElse(beanFactory.getBean(cls));
    }

    public List<T> getBeanList() {
        return Lists.newArrayList(this.beanList);
    }

    public Map<String, T> getBeanMap() {
        return new HashMap<>(this.beanNameMap);
    }

    public T getOrDefault(String name) {
        return Optional.ofNullable(beanNameMap.get(name)).orElse(getDefault());
    }

    public T getOrNew(String beanKey, String beanName, Predicate<String> predicate) {
        return this.getOrNew(beanKey, beanName, predicate, DEFAULT_BEAN_CREATOR);
    }

    public T getOrNew(String beanKey, String beanName, Predicate<String> predicate,
                      Function<Entry<String, Class<T>>, T> creator) {
        // 判断是否按默认值获取
        T bean = predicate.test(beanKey) ? this.getDefault() : null;
        // 根据name获取bean
        bean = Optional.ofNullable(bean).orElse(this.getByName(beanName));
        // 根据class获取bean
        bean = Optional.ofNullable(bean).orElseGet(() -> this.getByClass(beanKey, beanName, creator));
        // 返回默认bean
        return Optional.ofNullable(bean).orElseGet(() -> this.getDefault());
    }

    private T getByClass(String beanKey, String beanName, Function<Entry<String, Class<T>>, T> creator) {
        Class<T> beanClass = null;
        try {
            beanClass = (Class<T>) Class.forName(beanKey);
        } catch (ClassNotFoundException e) {
            log.warn("class={}, error=", beanKey, e);
            return null;
        }
        try {
            return creator.apply(new Entry<>(beanName, beanClass));
        } catch (Exception e) {
            log.warn("create bean... class={}, error=", beanClass, e);
            return null;
        }
    }

    public abstract T getDefault();

    class DefaultBeanCreator implements Function<Entry<String, Class<T>>, T> {

        @Override
        public T apply(Entry<String, Class<T>> t) {
            // 动态注入指定class的bean
            DefaultListableBeanFactory listableBeanFactory = (DefaultListableBeanFactory) beanFactory;
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(t.getValue());
            String beanName = Optional.ofNullable(t.getKey()).orElse(builder.toString());
            listableBeanFactory.registerBeanDefinition(beanName, builder.getBeanDefinition());
            return (T) listableBeanFactory.getBean(beanName);
        }

    }
}
