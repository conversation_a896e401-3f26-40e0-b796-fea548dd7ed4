package com.bilibili.adp.cpc.core.constants;

import java.util.Objects;

public class BatchUnitDimension {
    public static final String AGE = "age";
    public static final String AREA = "area";
    public static final String GENDER = "gender";
    public static final String CROWD_PACK = "crowd_pack";
    public static final String AREA_LEVEL = "area_level";

    public static boolean isAge(String x) {
        return Objects.equals(x, AGE);
    }

    public static boolean isAreaLevel(String x) {
        return Objects.equals(x, AREA_LEVEL);
    }

    public static boolean isArea(String x) {
        return Objects.equals(x, AREA);
    }

    public static boolean isGender(String x) {
        return Objects.equals(x, GENDER);
    }

    public static boolean isCrowdPack(String x) {
        return Objects.equals(x, CROWD_PACK);
    }
}
