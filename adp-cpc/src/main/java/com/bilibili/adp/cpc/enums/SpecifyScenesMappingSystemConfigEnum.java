package com.bilibili.adp.cpc.enums;

import com.bilibili.adp.common.enums.SystemConfig;
import lombok.Getter;

/**
 * 指定场景下与配置中广告位组的映射枚举
 *
 * <AUTHOR>
 * @date 2021/8/17
 */

/**
 * copy class
 * @see com.bilibili.adp.launch.api.flyPro.dto.enums.SpecifyScenesMappingSystemConfigEnum
 */
@Getter
public enum SpecifyScenesMappingSystemConfigEnum {

    MSG_FLOW(SpecificScenesEnum.MSG_FLOW, SystemConfig.FLY_MSG_FLOW_LIST),
    PLAY_PAGE(SpecificScenesEnum.PLAY_PAGE, SystemConfig.FLY_PLAY_PAGE_LIST),
    MSG_FLOW_BIG_CARD(SpecificScenesEnum.MSG_FLOW_BIG_CARD, SystemConfig.FLY_MSG_FLOW_BIG_CARD_LIST),
    DYNAMIC_FLOW(SpecificScenesEnum.DYNAMIC_FLOW, SystemConfig.FLY_DYNAMIC_FLOW_LIST),
    STORY(SpecificScenesEnum.STORY, SystemConfig.STORY),
    IPAD_RECOMMEND(SpecificScenesEnum.IPAD_RECOMMEND, SystemConfig.IPAD_RECOMMEND),
    PC_INDEX(SpecificScenesEnum.PC_INDEX, SystemConfig.PC_INDEX),
    PC_PLAY(SpecificScenesEnum.PC_PLAY, SystemConfig.PC_PAGE),
    ;


    private final SpecificScenesEnum scene;
    private final SystemConfig config;

    SpecifyScenesMappingSystemConfigEnum(SpecificScenesEnum scene, SystemConfig config) {
        this.scene = scene;
        this.config = config;
    }
}
