package com.bilibili.adp.cpc.biz.services.archive;

import com.bapis.ott.service.*;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.cpc.biz.services.creative.config.CreativePositionConfig;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2022-11-18 13:27:01
 * @description:
 **/
@Primary
@Slf4j
@Service("cpcOttService")
public class OttService {
    @Autowired
    private CreativePositionConfig creativePositionConfig;
    @Resource
    private IAccountLabelService accountLabelService;

    @Resource
    private OTTServiceGrpc.OTTServiceBlockingStub ottServiceBlockingStub;

    /**
     * 判断投放场景是否满足OTT
     * @param accountId
     * @param
     * @return
     */
    public boolean isOttScene(Integer accountId, Integer salestype, Integer epId) {

        // 单元所属账户在TV白名单内：379 起飞投放TVinline
        if (!accountIdInLabel(accountId, creativePositionConfig.getFlyTvInlineId())) {
            return false;
        }
        // 推广标的为稿件且稿件属于TV稿件（可调用TV接口查）
        if (!isOttFromPgc(epId)) {
            return false;
        }
        // 出价方式为CPM或oCPM播放
        if (!salestype.equals(SalesType.CPM.getCode())){
            return false;
        }
        return true;
    }

    /**
     * 判断ep 是否为ott
     * @param epIds
     * @return
     */
    public Map<Integer, Boolean> isOttFromPgc(List<Integer> epIds){
        Map<Integer, Boolean> epIdMap = new HashMap<>();
        if(CollectionUtils.isEmpty(epIds)){
            return Maps.newHashMap();
        }
        List<Long> ids = epIds.stream().map(Long::valueOf).collect(Collectors.toList());
        Map<Long, Boolean> idMap = isOttByMediaTp(ids, MediaTp.PgcVideo);
        idMap.keySet().forEach(id -> {
            epIdMap.put(id.intValue(), idMap.get(id));
        });
        return epIdMap;
    }

    /**
     * 判断ep 是否为ott
     * @param aIds
     * @return
     */
    public Map<Long, Boolean> isOttFromUgc(List<Long> aIds){
        if(CollectionUtils.isEmpty(aIds)){
            return Maps.newHashMap();
        }
        return isOttByMediaTp(aIds, MediaTp.Ugc);
    }

    public Boolean isOttFromPgc(Integer epId){
        if(epId == null || epId <= 0){
            return false;
        }
        Map<Integer, Boolean> retMap = isOttFromPgc(Lists.newArrayList(epId));
        return retMap.containsKey(epId) ? retMap.get(epId) : false;
    }

    private Boolean accountIdInLabel(Integer accountId, Integer labelId) {
        return accountLabelService.isAccountIdsInLabels(Collections.singletonList(accountId),
                Collections.singletonList(labelId));
    }

    /**
     *
     * @param ids
     * @param mediaTp 1 PGC,Id 对应 seasonId;2 UGC ,Id 对应 archiveId; 3 PGC Video, id对应episodeId; 4 UGC Video,id对应archive 的 cId
     * @return
     */
    private Map<Long, Boolean> isOttByMediaTp(List<Long> ids, MediaTp mediaTp) {
        log.info("isOttFrom, Ids :{}", ids);
        Map<Long, Boolean> validOttMap;
        try {
            List<Media> items = ids
                    .stream()
                    .map(id -> Media.newBuilder().setId(id).setType(mediaTp).build())
                    .collect(Collectors.toList());
            MediaStatusBatchReq req = MediaStatusBatchReq.newBuilder().addAllItem(items).build();
            MediaStatusBatchReply reply = ottServiceBlockingStub.mediaStatusBatch(req);
            validOttMap = reply.getResultMap()
                    .values()
                    .stream()
                    .collect(Collectors.toMap(mediaStatus -> mediaStatus.getId(), mediaStatus -> (mediaStatus.getStatus() == MediaStatusTp.ImportPass)));
        } catch (Exception e) {
            log.error("mediaStatusBatch error", e);
            validOttMap = new HashMap<>();
        }
        return validOttMap;
    }
}
