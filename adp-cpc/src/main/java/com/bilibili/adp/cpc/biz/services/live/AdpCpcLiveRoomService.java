package com.bilibili.adp.cpc.biz.services.live;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.bapis.ad.live.LiveRoomServiceGrpc;
import com.bapis.ad.live.LiveStatInfoResp;
import com.bapis.ad.live.LiveStatRequest;
import com.bapis.live.xroom.*;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveAdStatDto;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveAdStatQueryDto;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveRoomInfoBo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauSubjectPo;
import com.bilibili.adp.passport.biz.manager.LiveBroadcastHttpService;
import com.bilibili.adp.passport.biz.manager.bean.LiveBroadcastRoomInfo;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.server.RPCService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.bilibili.adp.cpc.dao.querydsl.QLauSubject.lauSubject;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;

@Slf4j
@Service
@RPCService
public class AdpCpcLiveRoomService extends LiveRoomServiceGrpc.LiveRoomServiceImplBase {

    @Autowired
    private RoomGrpc.RoomBlockingStub roomBlockingStub;
    @Autowired
    private LiveRoomAdStatQuerier liveRoomAdStatQuerier;
    @Autowired
    private LiveBroadcastHttpService liveBroadcastHttpService;

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    public void updateRoomMid(String roomId, Long mid) {
        adBqf.update(lauSubject)
                .where(lauSubject.materialId.eq(roomId).and(lauSubject.type.eq(1)))
                .set(lauSubject.mid, mid)
                .execute();
    }

    public List<String> fetchNextBatch(int batchSize) {
        return adBqf.from(lauSubject)
                .where(lauSubject.mid.eq(0L))
                .select(lauSubject.materialId)
                .orderBy(lauSubject.id.asc())
                .limit(batchSize)
                .fetch();
    }

    public LiveRoomInfoBo fetchInHouseLiveInfo(String roomId) {
        final LauSubjectPo po = adBqf.selectFrom(lauSubject)
                .where(lauSubject.materialId.eq(roomId))
                .where(lauSubject.isDeleted.eq(IsDeleted.VALID.getCode()))
                .fetchFirst();
        return LiveRoomInfoBo.builder()
                .mid(po.getMid())
                .roomId(roomId)
                .status(po.getLaunchable())
                .build();
    }

    public LiveBroadcastRoomInfo queryLiveBroadcastRoomInfoById(Integer roomId) {
        Assert.notNull(roomId);

        Map<Integer, LiveBroadcastRoomInfo> roomInfoMap = liveBroadcastHttpService.queryLiveBroadcastRoomInfoByIds(Arrays.asList(roomId));
        LiveBroadcastRoomInfo roomInfo = roomInfoMap.get(roomId);
        return roomInfo;
    }

    public LiveBroadcastRoomInfo fetchLiveRoomInfoById(Long mid) {
        UIDsReq uiDsReq = UIDsReq.newBuilder()
                .addUids(mid)
                .addAttrs("anchor")
                .addAttrs("status")
                .addAttrs("show")
                .addAttrs("area")
                .build();
        LiveBroadcastRoomInfo roomInfo = LiveBroadcastRoomInfo.builder()
                .uid(mid).build();
        UIDsInfosResp uiDsInfosResp = roomBlockingStub.getMultipleByUids(uiDsReq);
        if (uiDsInfosResp == null) {
            return roomInfo;
        }

        Map<Long, Infos> uidsInfoMap = uiDsInfosResp.getListMap();
        if (uidsInfoMap == null) {
            return roomInfo;
        }
        Map<Long, LivePlayUrlData> playUrlMap = uiDsInfosResp.getPlayUrlMap();
        Infos infos = uidsInfoMap.get(mid);
        LivePlayUrlData livePlayUrlData = playUrlMap.get(mid);
        if (infos == null) {
            return roomInfo;
        }

        RoomShowInfo roomShowInfo = infos.getShow();
        RoomAreaInfo roomAreaInfo = infos.getArea();
        RoomStatusInfo roomStatusInfo = infos.getStatus();

        roomInfo.setRoomId((int) infos.getRoomId());
        roomInfo.setUid(infos.getUid());

        if (livePlayUrlData != null) {
            roomInfo.setLink(livePlayUrlData.getLink());
        }
        if (roomShowInfo != null) {
            roomInfo.setCover(roomShowInfo.getCover());
            roomInfo.setTitle(roomShowInfo.getTitle());
        }
        if (roomStatusInfo != null) {
            roomInfo.setIsShow((int) roomStatusInfo.getLiveStatus());
        }
        if (roomAreaInfo != null) {
            roomInfo.setArea(roomAreaInfo.getAreaName());
            roomInfo.setAreaV2Id((int) roomAreaInfo.getAreaId());
            roomInfo.setAreaV2Name(roomAreaInfo.getAreaName());
            roomInfo.setAreaV2ParentId((int) roomAreaInfo.getParentAreaId());
            roomInfo.setAreaV2ParentName(roomAreaInfo.getParentAreaName());
        }
        return roomInfo;
    }

    @Override
    public void fetchUpLiveStatInfo(LiveStatRequest request, StreamObserver<LiveStatInfoResp> responseObserver) {
        log.info("fetchUpLiveStatInfo, mid:{}, startTime:{}, endTime:{}", request.getMid(), request.getStartTime(),
                request.getEndTime());
        try {

            LiveAdStatQueryDto upLiveStatQueryDto = new LiveAdStatQueryDto();
            upLiveStatQueryDto.setMid(request.getMid());
            upLiveStatQueryDto.setStartTimeStr(request.getStartTime());
            upLiveStatQueryDto.setEndTimeStr(request.getEndTime());
            LiveAdStatDto liveAdStatDto = liveRoomAdStatQuerier.fetchUpLiveStatInfo(upLiveStatQueryDto);
            log.info("fetchUpLiveStatInfo, result:{}", JSON.toJSONString(liveAdStatDto));
            LiveStatInfoResp liveStatInfoResp = LiveStatInfoResp.newBuilder().build();
            if (liveAdStatDto != null) {
                liveStatInfoResp = LiveStatInfoResp.newBuilder()
                        .setShow(liveAdStatDto.getShow())
                        .setCost(liveAdStatDto.getCost())
                        .setLiveEntryCount(liveAdStatDto.getLiveEntryCount())
                        .setCostLatestTime(liveAdStatDto.getCostLatestTime())
                        .setLiveEntryCountLatestTime(liveAdStatDto.getLiveEntryCountLatestTime())
                        .build();
            }

            responseObserver.onNext(liveStatInfoResp);
            responseObserver.onCompleted();
        } catch (Throwable t) {
            log.error("fetchUpLiveStatInfo, mid:{}, startTime:{}, endTime:{}, error:{}", request.getMid(), request.getStartTime(),
                    request.getEndTime(), t.getMessage());
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL).withDescription(t.getMessage())));
        }

    }
}
