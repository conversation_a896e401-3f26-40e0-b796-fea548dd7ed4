package com.bilibili.adp.cpc.biz.services.archive;

import com.bapis.ad.archive.ArchiveInfo;
import com.bapis.ad.archive.CmArchiveInfoResp;
import com.bapis.ad.archive.CmArchiveServiceGrpc;
import com.bapis.ad.archive.Conditions;
import com.bapis.ad.jupiter.archive.*;
import com.bapis.ad.jupiter.archive.Stat;
import com.bapis.ad.mgk.material.MaterialIdRegistry;
import com.bapis.archive.service.*;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveAnalysisDetailBo;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveAnalysisReportChartBo;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveAnalysisReportColumnBo;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveAnalysisReportQueryBo;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.converter.archive.ArchiveAnalysisReportConverter;
import com.bilibili.adp.cpc.biz.services.AdpCpcLauUserBehaviorService;
import com.bilibili.adp.cpc.biz.services.material.MaterialCenterGrpcService;
import com.bilibili.adp.cpc.biz.services.material.enums.MaterialIdTypeEnum;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauArchiveAnalysisReportColumnsPo;
import com.bilibili.adp.cpc.utils.ValidateUtil;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.mgk.platform.api.archive.soa.ISoaMgkCmSpaceService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauArchiveAnalysisReportColumns.lauArchiveAnalysisReportColumns;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class EffectAdArchiveAnalysisReportService {
    private final static DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;
    private final AdpCpcLauUserBehaviorService userBehaviorService;
    private final ISoaMgkCmSpaceService soaMgkCmSpaceService;

    @RPCClient(value = "archive.service")
    private ArchiveGrpc.ArchiveBlockingStub archiveBlockingStub;
    @RPCClient(value = "sycpb.platform.cpm-jupiter")
    private ArchiveAnalysisGrpc.ArchiveAnalysisBlockingStub archiveAnalysisBlockingStub;

    @RPCClient("sycpb.cpm.scv")
    private CmArchiveServiceGrpc.CmArchiveServiceBlockingStub cmArchiveServiceBlockingStub;

    @Autowired
    private MaterialCenterGrpcService materialCenterGrpcService;

    public List<LauArchiveAnalysisReportColumnsPo> allAvailableColumnPos() {
        return adBqf.select(lauArchiveAnalysisReportColumns)
                .from(lauArchiveAnalysisReportColumns)
                .where(lauArchiveAnalysisReportColumns.isDeleted.eq(0))
                .fetch();
    }

    public List<ArchiveAnalysisReportColumnBo> availableColumnBos(String type,Set<Integer> excludedColumnIds) {
        switch (type) {
            case "detail":
                return detailAvailableColumnBos(excludedColumnIds);
            case "chart":
                return chartAvailableColumnBos(excludedColumnIds);
            default:
                return Collections.emptyList();
        }
    }

    private List<ArchiveAnalysisReportColumnBo> detailAvailableColumnBos(Set<Integer> excludedColumnIds) {
        List<LauArchiveAnalysisReportColumnsPo> allColumns = allAvailableColumnPos();
        return buildColumnsBo(allColumns, 0,excludedColumnIds);
    }

    private List<ArchiveAnalysisReportColumnBo> chartAvailableColumnBos(Set<Integer> excludedColumnIds) {
        List<LauArchiveAnalysisReportColumnsPo> allColumns = allAvailableColumnPos();
        return allColumns
                .stream()
                .filter(po -> Objects.equals(1, po.getIsSelectable()))
                .filter(po -> !excludedColumnIds.contains(po.getCode()))
                .map(ArchiveAnalysisReportConverter.MAPPER::po2Bo)
                .collect(Collectors.toList());
    }

    private int getBehaviorIdByType(String type) {
        switch (type) {
            case "detail":
                return Constants.SUBSCRIBED_ARCHIVE_ANALYSIS_REPORT_DETAIL_COLUMNS;
            case "chart":
                return Constants.SUBSCRIBED_ARCHIVE_ANALYSIS_REPORT_CHART_COLUMNS;
            default:
                throw new IllegalArgumentException("invalid type");
        }
    }

    public List<ArchiveAnalysisReportColumnBo> subscribedColumns(String type, Integer accountId) {
        List<LauArchiveAnalysisReportColumnsPo> allColumns = allAvailableColumnPos();
        List<Integer> fixedSubscribedColumns;
        List<Integer> defaultSubscribedColumns;
        switch (type) {
            case "chart":
                fixedSubscribedColumns = Collections.emptyList();
                // 图标的情况下 默认展示 可选的 前8个
                defaultSubscribedColumns = allColumns
                        .stream()
                        .filter(po -> Objects.equals(1, po.getIsSelectable()))
                        .map(LauArchiveAnalysisReportColumnsPo::getCode)
                        .limit(8)
                        .collect(Collectors.toList());
                break;
            case "detail":
                fixedSubscribedColumns = allColumns
                        .stream()
                        .filter(po -> (Objects.equals(1, po.getFixed())) && !Objects.equals(0, po.getParentCode()))
                        .sorted(Comparator.comparing(LauArchiveAnalysisReportColumnsPo::getSort))
                        .map(LauArchiveAnalysisReportColumnsPo::getCode)
                        .collect(Collectors.toList());
                defaultSubscribedColumns = Collections.emptyList();
                break;
            default:
                fixedSubscribedColumns = Collections.emptyList();
                defaultSubscribedColumns = Collections.emptyList();
                break;
        }
        int behaviorId = getBehaviorIdByType(type);
        List<Integer> userSubscribedColumns = userBehaviorService.getSubscribedColumns(accountId, behaviorId, 1);
        final List<Integer> subscribedColumns = CollectionUtils.isEmpty(userSubscribedColumns) ? defaultSubscribedColumns : userSubscribedColumns;
        final List<Integer> columns = Stream.of(fixedSubscribedColumns, subscribedColumns)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        // code 有唯一键 所以可以放心的 to map
        Map<Integer, LauArchiveAnalysisReportColumnsPo> allColumnsMap = allColumns
                .stream()
                .collect(Collectors.toMap(LauArchiveAnalysisReportColumnsPo::getCode, Function.identity()));
        List<LauArchiveAnalysisReportColumnsPo> subscribedColumnPos = columns
                .stream()
                .map(allColumnsMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return subscribedColumnPos
                .stream()
                .map(ArchiveAnalysisReportConverter.MAPPER::po2Bo)
                .collect(Collectors.toList());
    }

    public void saveSubscribedColumns(String type, Integer accountId, List<Integer> codes) {
        int behaviorId = getBehaviorIdByType(type);
        List<LauArchiveAnalysisReportColumnsPo> allColumns = allAvailableColumnPos();
        List<Integer> fixedCodes = allColumns
                .stream()
                .filter(po -> Objects.equals(1, po.getFixed()))
                .map(LauArchiveAnalysisReportColumnsPo::getCode)
                .collect(Collectors.toList());
        List<Integer> subscribedCodes = codes
                .stream()
                .filter(code -> !fixedCodes.contains(code))
                .collect(Collectors.toList());
        userBehaviorService.saveUserReportBehavior(accountId, behaviorId, subscribedCodes, 1);
    }

    public List<ArchiveAnalysisReportColumnBo> buildColumnsBo(List<LauArchiveAnalysisReportColumnsPo> lauArchiveAnalysisReportColumnsPos,
                                                              Integer parentCode, Set<Integer> excludedColumnIds) {
        List<ArchiveAnalysisReportColumnBo> result = lauArchiveAnalysisReportColumnsPos
                .stream()
                .filter(po -> Objects.equals(po.getParentCode(), parentCode))
                .filter(po -> !excludedColumnIds.contains(po.getCode()))
                .map(ArchiveAnalysisReportConverter.MAPPER::po2Bo)
                .collect(Collectors.toList());
        result.forEach(columnsBo -> {
            List<ArchiveAnalysisReportColumnBo> children = buildColumnsBo(lauArchiveAnalysisReportColumnsPos, columnsBo.getCode(),excludedColumnIds);
            columnsBo.setChildren(children);
        });
        return result;
    }

    private static List<String> genDates(Long fromTime, Long toTime) {
        LocalDate from = LocalDateTime.ofInstant(Instant.ofEpochMilli(fromTime), ZoneId.systemDefault()).toLocalDate();
        LocalDate to = LocalDateTime.ofInstant(Instant.ofEpochMilli(toTime), ZoneId.systemDefault()).toLocalDate();
        long days = to.toEpochDay() - from.toEpochDay();
        List<String> dates = new ArrayList<>();
        for (long i = 0; i <= days; i++) {
            dates.add(from.plusDays(i).format(FORMATTER));
        }
        return dates;
    }

    public PageResult<ArchiveAnalysisDetailBo> detailAnalysisInfos(ArchiveAnalysisReportQueryBo queryBo) {
        Assert.notEmpty((queryBo.getAccountIds()), "账户id不可为空");
        Assert.isTrue(queryBo.getAccountIds().size() == 1, "should only query one account");
        ValidateUtil.checkLongAdIdSize(queryBo.getCampaignIds(), 100);
        ValidateUtil.checkLongAdIdSize(queryBo.getUnitIds(), 100);
        ValidateUtil.checkLongAdIdSize(queryBo.getCreativeIds(), 100);
        ValidateUtil.checkLongAdIdSize(queryBo.getAids(), 100);
        Assert.isTrue(Utils.isPositive(queryBo.getFromTime()), "查询稿件元素报表起始时间不可为空");
        Assert.isTrue(Utils.isPositive(queryBo.getToTime()), "查询稿件元素报表结束时间不可为空");
        Assert.notEmpty((queryBo.getAccountIds()), "账户id不可为空");
        Assert.isTrue(queryBo.getToTime() >= queryBo.getFromTime(), "查询稿件素材报表结束时间不可早于开始时间");
        java.sql.Date fromDate = new java.sql.Date(queryBo.getFromTime()),
                toDate = new java.sql.Date(queryBo.getToTime());

        if (!queryBo.isForExport()) {
            Assert.isTrue(queryBo.getSize() <= 100, "分页大小最大为100");
        }
        // 产品要求 间隔30天
        boolean isTimeRangeValid = toDate.toLocalDate().minusDays(61L).isBefore(fromDate.toLocalDate());
        Assert.isTrue(isTimeRangeValid, "查询稿件元素报表时间间隔最大为60天");

        final StatReq req = StatReq.newBuilder()
                .setTimeTypeValue(queryBo.getTimeType())
                .setStartTime(queryBo.getFromTime())
                .setEndTime(queryBo.getToTime())
                .setType(Type.ARC)
                .addAllAids(queryBo.getAids())
                .addAllAccountIds(queryBo.getAccountIds())
                .addAllCampaignIds(queryBo.getCampaignIds())
                .addAllUnitIds(queryBo.getUnitIds())
                .addAllCreativeIds(queryBo.getCreativeIds())
                .setSceneTypeValue(queryBo.getSceneType())
                .setCreativeTypeValue(queryBo.getCreativeType())
                .setSortField(queryBo.getSortField())
                .setSortOrderValue(queryBo.getSortOrder())
                .setPn(queryBo.getPage())
                .setPs(queryBo.getSize())
                .build();
        final StatResp statsResp = archiveAnalysisBlockingStub.withDeadlineAfter(5, TimeUnit.MINUTES).stats(req);
        if (statsResp.getTotal() == 0) {
            return PageResult.emptyPageResult();
        }
        final List<Long> aids = statsResp
                .getStatsList()
                .stream()
                .map(Stat::getAid)
                .distinct().
                collect(Collectors.toList());
        Map<Long, Arc> arcsMap = new HashMap<>();
        //稿件rpc接口 一次查100
        Lists.partition(aids, 100).forEach(ids -> {
            final ArcsRequest arcsRequest = ArcsRequest.newBuilder()
                    .addAllAids(ids)
                    .build();
            final ArcsReply arcsReply = archiveBlockingStub.arcs(arcsRequest);
            arcsMap.putAll(arcsReply.getArcsMap());
        });
        final List<Long> mids = arcsMap
                .values()
                .stream()
                .map(Arc::getAuthor)
                .map(Author::getMid)
                .distinct()
                .collect(Collectors.toList());

        List<Long> cmMids = new ArrayList<>();
        Lists.partition(mids, 100).forEach(ids -> {
            List<Long> tempMids = soaMgkCmSpaceService.getArcMidsInMgkCmSpace(new ArrayList<>(ids));
            cmMids.addAll(tempMids);
        });

        final long total = statsResp.getTotal();

        //素材中心id，根据avid查md5，查素材id
        Map<Long, String> materialCenterIdByAvid = getMaterialCenterIdByAvid(aids);

        final List<ArchiveAnalysisDetailBo> details = statsResp
                .getStatsList()
                .stream()
                .map(stat -> {
                    final long aid = stat.getAid();
                    return ArchiveAnalysisReportConverter.MAPPER.stat2Bo(arcsMap.getOrDefault(aid, Arc.getDefaultInstance()), stat);
                })
                .peek(detail -> {
                    //根据稿件列表的mid查询是否有小号投稿, 如果有小号投稿的稿件 则部分信息不展示
                    if (cmMids.contains(Long.valueOf(detail.getMid()))) {
                        detail.setMid(StringUtils.EMPTY);
                        detail.setName(StringUtils.EMPTY);
                    }
                    if (StringUtils.isEmpty(detail.getLogDate())) {
                        detail.setLogDate(time2String(req.getStartTime()) + "~" + time2String(req.getEndTime()));
                    }
                    detail.setMaterialCenterId(materialCenterIdByAvid.getOrDefault(detail.getAid(),""));
                    detail.setMaterialUrl(Constants.BILIBILI_ARC_PC_PREFIX + detail.getAid());
                })
                .collect(Collectors.toList());
        return new PageResult<>((int) total, details);
    }

    private static final String CHART_TOTAL_SORT_FIELD = "account_id";
    private static final String CHART_DETAIL_SORT_FIELD = "log_date";

    public ArchiveAnalysisReportChartBo chartAnalysisInfos(ArchiveAnalysisReportQueryBo queryBo) {
        final List<String> logDates = genDates(queryBo.getFromTime(), queryBo.getToTime());
        final StatReq totalReq = StatReq.newBuilder()
                .setTimeType(TimeType.ALL)
                .setStartTime(queryBo.getFromTime())
                .setEndTime(queryBo.getToTime())
                .setType(Type.ACCOUNT)
                .addAllAids(queryBo.getAids())
                .addAllAccountIds(queryBo.getAccountIds())
                .addAllCampaignIds(queryBo.getCampaignIds())
                .addAllUnitIds(queryBo.getUnitIds())
                .addAllCreativeIds(queryBo.getCreativeIds())
                .setSceneTypeValue(queryBo.getSceneType())
                .setCreativeTypeValue(queryBo.getCreativeType())
                .setSortField(CHART_TOTAL_SORT_FIELD)
                .setSortOrder(SortOrder.ASC)
                .setPn(1L)
                .setPs(1L)
                .build();
        final StatResp totalResp = archiveAnalysisBlockingStub.withDeadlineAfter(5, TimeUnit.MINUTES).stats(totalReq);
        final Stat total = org.springframework.util.CollectionUtils.isEmpty(totalResp.getStatsList()) ? Stat.getDefaultInstance() : totalResp.getStats(0);
        final StatReq detailReq = StatReq.newBuilder()
                .setTimeType(TimeType.DAY)
                .setStartTime(queryBo.getFromTime())
                .setEndTime(queryBo.getToTime())
                .setType(Type.ACCOUNT)
                .addAllAids(queryBo.getAids())
                .addAllAccountIds(queryBo.getAccountIds())
                .addAllCampaignIds(queryBo.getCampaignIds())
                .addAllUnitIds(queryBo.getUnitIds())
                .addAllCreativeIds(queryBo.getCreativeIds())
                .setSceneTypeValue(queryBo.getSceneType())
                .setCreativeTypeValue(queryBo.getCreativeType())
                .setSortField(CHART_DETAIL_SORT_FIELD)
                .setSortOrder(SortOrder.ASC)
                .setPn(1L)
                .setPs(logDates.size())
                .build();
        final StatResp detailResp = archiveAnalysisBlockingStub.withDeadlineAfter(5, TimeUnit.MINUTES).stats(detailReq);
        final Map<String, Stat> detailMap = detailResp.getStatsList()
                .stream()
                .collect(Collectors.toMap(Stat::getLogDate, Function.identity()));
        final List<Stat> details = logDates
                .stream()
                .map(logDate -> detailMap.getOrDefault(logDate, Stat.newBuilder().setLogDate(logDate).build()))
                .collect(Collectors.toList());
        return ArchiveAnalysisReportChartBo.builder()
                .xaxis(logDates)
                .total(total)
                .detail(details)
                .build();
    }
    private String time2String(long time) {
        LocalDateTime ldt = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        return ldt.toLocalDate().format(FORMATTER);
    }

    private Map<Long,String> getMaterialCenterIdByAvid(List<Long> avids){
        CmArchiveInfoResp cmArchiveInfoResp =
                cmArchiveServiceBlockingStub.queryCmArchiveByCondition(Conditions.newBuilder().addAllAvids(avids).build());
        Map<Long, String> avidAndMd5 = cmArchiveInfoResp.getArchiveInfoList().stream().collect(Collectors.toMap(ArchiveInfo::getAvid, ArchiveInfo::getMd5, (c1, c2) -> c2));
        ArrayList<String> videoMd5s = new ArrayList<>(avidAndMd5.values());
        Map<String, MaterialIdRegistry> byMd5s = CollectionUtils.isEmpty(videoMd5s) ? new HashMap<>()
                : materialCenterGrpcService.findByMd5s(videoMd5s, MaterialIdTypeEnum.VIDEO.getName());


        HashMap<Long, String> avidAndMaterialCenterId = new HashMap<>();
        avidAndMd5.keySet().forEach(avid->{
            MaterialIdRegistry materialIdRegistry = byMd5s.getOrDefault(avidAndMd5.getOrDefault(avid,""), null);
            String materialCenterId = Objects.nonNull(materialIdRegistry) ? materialIdRegistry.getMaterialId() : "";
            avidAndMaterialCenterId.put(avid,materialCenterId);
        });
        return avidAndMaterialCenterId;
    }

}
