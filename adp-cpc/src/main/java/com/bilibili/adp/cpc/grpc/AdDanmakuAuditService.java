package com.bilibili.adp.cpc.grpc;

import com.bapis.ad.audit.danmaku.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.danmaku.SelfDanmakuGroupAuditRecordService;
import com.bilibili.adp.cpc.biz.services.danmaku.SelfDanmakuGroupAuditService;
import com.bilibili.adp.cpc.biz.services.danmaku.SelfDanmakuIconConfigService;
import com.bilibili.adp.cpc.biz.services.danmaku.api.ISelfDanmakuGroupService;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.*;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.audit.DanmakuGroupAuditInfoBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.audit.DanmakuGroupAuditRecordBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.audit.QueryDanmakuGroupAuditInfoBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.audit.QueryDanmakuGroupAuditRecordBo;
import com.bilibili.adp.cpc.enums.danmaku.SelfDanmakuAuditStatusEnum;
import com.bilibili.adp.cpc.enums.danmaku.SelfDanmakuGroupTypeEnum;
import com.bilibili.adp.cpc.enums.danmaku.SelfDanmakuTypeEnum;
import com.google.protobuf.Empty;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.server.RPCService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName AdDanmakuAuditService
 * <AUTHOR>
 * @Date 2024/1/8 4:09 下午
 * @Version 1.0
 **/
@Service
@Slf4j
@RPCService
public class AdDanmakuAuditService extends AdAuditDanmakuServiceGrpc.AdAuditDanmakuServiceImplBase {

    public static final String ID = "AD_DANMAKU_AUDIT_SERVICE";

    @Value("${ad.danmaku.audit.batch.limit:10}")
    private Integer adDanmakuAuditBatchLimit;

    @Resource
    private SelfDanmakuGroupAuditService selfDanmakuGroupAuditService;

    @Resource
    private SelfDanmakuGroupAuditRecordService selfDanmakuGroupAuditRecordService;

    @Resource
    private ISelfDanmakuGroupService selfDanmakuGroupService;

    @Resource
    private SelfDanmakuIconConfigService selfDanmakuIconConfigService;

    @Override
    public void auditPage(AuditPageRequest request, StreamObserver<AuditPageReply> responseObserver) {
        try {
            QueryDanmakuGroupAuditInfoBo queryBo = convertAuditPageRequest2QueryBo(request);
            PageResult<DanmakuGroupAuditInfoBo> pageResult = selfDanmakuGroupAuditService.queryForPage(queryBo);
            AuditPageReply reply = convertAuditInfoPageResult2Reply(pageResult);
            responseObserver.onNext(reply);
            responseObserver.onCompleted();
        } catch (IllegalArgumentException e) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditPage 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditPage 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        }
    }


    @Override
    public void getDetail(DanmakuGroupDetailRequest request, StreamObserver<DanmakuGroupDetailReply> responseObserver) {
        try {
            long groupId = request.getGroupId();
            int accountId = request.getAccountId();
            SelfDanmakuGroupBo detail = selfDanmakuGroupService.getDetail(accountId, groupId);
            DanmakuGroupDetailReply reply = convertDetailBo2Reply(detail);
            responseObserver.onNext(reply);
            responseObserver.onCompleted();
        } catch (IllegalArgumentException e) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:getDetail 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:getDetail 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        }
    }

    @Override
    public void auditPass(AuditTotalRequest request, StreamObserver<Empty> responseObserver) {
        try {
            List<AuditRequest> requestList = request.getRequestList();
            Assert.isTrue(requestList.size() <= adDanmakuAuditBatchLimit,
                    "目前单次批量审核最多允许" + adDanmakuAuditBatchLimit + "个弹幕组");
            requestList.forEach(singleRequest -> {
                SelfDanmakuGroupAuditBo singleAuditBo = convertAuditRequest2Bo(singleRequest);
                selfDanmakuGroupService.auditPass(singleAuditBo);
            });
            responseObserver.onNext(Empty.newBuilder().build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException e) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditPass 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditPass 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        }
    }

    @Override
    public void auditReject(AuditTotalRequest request, StreamObserver<Empty> responseObserver) {
        try {
            List<AuditRequest> requestList = request.getRequestList();
            Assert.isTrue(requestList.size() <= adDanmakuAuditBatchLimit,
                    "目前单次批量审核最多允许" + adDanmakuAuditBatchLimit + "个弹幕组");
            requestList.forEach(singleRequest -> {
                SelfDanmakuGroupAuditBo singleAuditBo = convertAuditRequest2Bo(singleRequest);
                selfDanmakuGroupService.auditReject(singleAuditBo);
            });
            responseObserver.onNext(Empty.newBuilder().build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException e) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditReject 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditReject 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        }

    }

    @Override
    public void iconConfig(Empty request, StreamObserver<IconReply> responseObserver) {
        try {
            List<SelfDanmakuIconConfigBo> configBoList = selfDanmakuIconConfigService.queryBoList();
            List<IconEntity> iconEntities = convertIconBoList2EntityList(configBoList);
            IconReply.Builder builder = IconReply.newBuilder();
            if (!CollectionUtils.isEmpty(iconEntities)) {
                builder.addAllIconEntity(iconEntities);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException e) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:iconConfig 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:iconConfig 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        }
    }

    @Override
    public void auditRecord(DanmakuAuditRecordRequest request, StreamObserver<DanmakuAuditRecordReply> responseObserver) {
        try {
            QueryDanmakuGroupAuditRecordBo queryBo = convertAuditRecordRequest2QueryBo(request);
            List<DanmakuGroupAuditRecordBo> recordBoList = selfDanmakuGroupAuditRecordService.queryForList(queryBo);
            DanmakuAuditRecordReply reply = convertAuditRecordPageResult2Reply(recordBoList);
            responseObserver.onNext(reply);
            responseObserver.onCompleted();
        } catch (IllegalArgumentException e) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditRecord 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        } catch (Exception e) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(e.getMessage())
                    .asRuntimeException());
            log.error("{}:auditRecord 失败,{}", ID, ExceptionUtils.getStackTrace(e));
        }
    }

    private QueryDanmakuGroupAuditInfoBo convertAuditPageRequest2QueryBo(AuditPageRequest request) {
        return QueryDanmakuGroupAuditInfoBo.builder()
                .accountId(Utils.isPositive(request.getAccountId()) ? request.getAccountId() : null)
                .auditorName(request.getAuditorName())
                .auditStatus(Utils.isPositive(request.getAuditStatus()) ? request.getAuditStatus() : null)
                .startSendAuditTime(Utils.isPositive(request.getStartSendAuditTime()) ? request.getStartSendAuditTime() : null)
                .endSendAuditTime(Utils.isPositive(request.getEndSendAuditTime()) ? request.getEndSendAuditTime() : null)
                .startAuditTime(Utils.isPositive(request.getStartAuditTime()) ? request.getStartAuditTime() : null)
                .endAuditTime(Utils.isPositive(request.getEndAuditTime()) ? request.getEndAuditTime() : null)
                .groupIdList(request.getGroupIdList())
                .groupName(request.getGroupName())
                .groupType(request.getGroupType())
                .page(Utils.isPositive(request.getPage()) ? request.getPage() : null)
                .pageSize(Utils.isPositive(request.getSize()) ? request.getSize() : null)
                .orderBy("mtime desc")
                .build();
    }

    private AuditPageReply convertAuditInfoPageResult2Reply (PageResult<DanmakuGroupAuditInfoBo> pageResult) {
        AuditPageReply.Builder builder = AuditPageReply.newBuilder();
        builder.setTotal(pageResult.getTotal());
        List<SelfDanmakuGroupAuditEntity> entityList = convertAuditInfoBoList2EntityList(pageResult.getRecords());
        if (!CollectionUtils.isEmpty(entityList)) {
            builder.addAllEntity(entityList);
        }
        return builder.build();
    }

    private List<DanmakuBaseInfoEntity> convertDanmakuInfoBoList2EntityList(List<SelfDanmakuInfoBo> baseBoList) {
        if (CollectionUtils.isEmpty(baseBoList)) {
            return Collections.emptyList();
        }

        return baseBoList.stream().map(baseBo -> {
            return DanmakuBaseInfoEntity.newBuilder()
                    .setDanmakuType(baseBo.getDanmakuType())
                    .setDanmakuTypeDesc(SelfDanmakuTypeEnum.getByCode(baseBo.getDanmakuType()).getDesc())
                    .setDanmakuColor(baseBo.getDanmakuColor())
                    .setDanmakuIconId(baseBo.getDanmakuIconId())
                    .setDanmakuText(baseBo.getDanmakuText())
                    .setDanmakuPicUrl(baseBo.getDanmakuPicUrl())
                    .build();
        }).collect(Collectors.toList());
    }

    private DanmakuGroupDetailReply convertDetailBo2Reply(SelfDanmakuGroupBo detail) {
        if (Objects.isNull(detail)) {
            return DanmakuGroupDetailReply.getDefaultInstance();
        }

        DanmakuGroupDetailReply.Builder builder = DanmakuGroupDetailReply.newBuilder()
                .setId(detail.getId())
                .setAccountId(detail.getAccountId())
                .setGroupId(detail.getGroupId())
                .setGroupName(detail.getGroupName())
                .setGroupType(detail.getGroupType())
                .setGroupTypeDesc(SelfDanmakuGroupTypeEnum.getByCode(detail.getGroupType()).getDesc())
                .setGroupStatus(detail.getGroupStatus())
                .setGroupStatusDesc(SelfDanmakuAuditStatusEnum.getByCode(detail.getGroupAuditStatus()).getDesc())
                .setGroupAuditStatus(detail.getGroupAuditStatus())
                .setGroupAuditStatusDesc(SelfDanmakuAuditStatusEnum.getByCode(
                        detail.getGroupAuditStatus()).getDesc())
                .setReason(detail.getReason())
                .setVersion(detail.getVersion())
                .setHasShadow(detail.getHasShadow())
                .setCtime(Utils.getTimestamp2StringBySecond(detail.getCtime()))
                .setMtime(Utils.getTimestamp2StringBySecond(detail.getMtime()));
        List<DanmakuDetailEntity> entityList = convertDanmakuBoList2DetailEntityList(detail.getDanmakuBoList());
        if (!CollectionUtils.isEmpty(entityList)) {
            builder.addAllDanmakuEntity(entityList);
        }
        return builder.build();
    }

    private List<DanmakuDetailEntity> convertDanmakuBoList2DetailEntityList(List<SelfDanmakuBo> boList) {
        if (CollectionUtils.isEmpty(boList)) {
            return Collections.emptyList();
        }

        return boList.stream().map(bo -> {
            return DanmakuDetailEntity.newBuilder()
                    .setDanmakuId(bo.getDanmakuId())
                    .setAccountId(bo.getAccountId())
                    .setGroupId(bo.getGroupId())
                    .setDanmakuType(bo.getDanmakuType())
                    .setDanmakuTypeDesc(SelfDanmakuTypeEnum.getByCode(bo.getDanmakuType()).getDesc())
                    .setDanmakuText(bo.getDanmakuText())
                    .setDanmakuColor(bo.getDanmakuColor())
                    .setDanmakuIconId(bo.getDanmakuIconId())
                    .setDanmakuPicUrl(bo.getDanmakuPicUrl())
                    .setDanmakuStatus(bo.getDanmakuStatus())
                    .setDanmakuAuditStatus(bo.getDanmakuAuditStatus())
                    .setDanmakuMd5(bo.getDanmakuMd5())
                    .setCtime(Utils.getTimestamp2StringBySecond(bo.getCtime()))
                    .setMtime(Utils.getTimestamp2StringBySecond(bo.getMtime()))
                    .build();
        }).collect(Collectors.toList());
    }

    private List<SelfDanmakuGroupAuditEntity> convertAuditInfoBoList2EntityList(List<DanmakuGroupAuditInfoBo> auditInfoBoList) {
        if (CollectionUtils.isEmpty(auditInfoBoList)) {
            return Collections.emptyList();
        }

        return auditInfoBoList.stream().map(auditInfoBo -> {
            String auditTime = SelfDanmakuAuditStatusEnum.WAIT_AUDIT.getCode().equals(auditInfoBo.getAuditStatus()) ?
                    "" : Utils.getTimestamp2StringBySecond(auditInfoBo.getAuditTime());

            SelfDanmakuGroupAuditEntity.Builder builder = SelfDanmakuGroupAuditEntity.newBuilder()
                    .setAccountId(auditInfoBo.getAccountId())
                    .setAccountName(auditInfoBo.getAccountName())
                    .setGroupId(auditInfoBo.getGroupId())
                    .setLogId(auditInfoBo.getLogId())
                    .setGroupType(auditInfoBo.getGroupType())
                    .setGroupTypeDesc(SelfDanmakuGroupTypeEnum.getByCode(
                            auditInfoBo.getGroupType()).getDesc())
                    .setGroupName(auditInfoBo.getGroupName())
                    .setAuditorName(auditInfoBo.getAuditorName())
                    .setAuditStatus(auditInfoBo.getAuditStatus())
                    .setAuditStatusDesc(SelfDanmakuAuditStatusEnum.getByCode(
                            auditInfoBo.getAuditStatus()).getDesc())
                    .setSendAuditTime(Utils.getTimestamp2StringBySecond(auditInfoBo.getSendAuditTime()))
                    .setAuditTime(auditTime)
                    .setReason(auditInfoBo.getReason())
                    .setVersion(auditInfoBo.getVersion());
            List<DanmakuBaseInfoEntity> baseInfoEntityList =
                    convertDanmakuInfoBoList2EntityList(auditInfoBo.getDanmakuInfoBoList());
            if (!CollectionUtils.isEmpty(baseInfoEntityList)) {
                builder.addAllDanmakuEntity(baseInfoEntityList);
            }
            return builder.build();
        }).collect(Collectors.toList());
    }

    private SelfDanmakuGroupAuditBo convertAuditRequest2Bo(AuditRequest request) {
        Operator operator = Operator.builder()
                .operatorType(OperatorType.SYSTEM)
                .operatorName(request.getOperator())
                .build();
        return SelfDanmakuGroupAuditBo.builder()
                .operator(operator)
                .groupId(request.getGroupId())
                .reason(request.getReason())
                .version(request.getVersion())
                .build();
    }

    private List<IconEntity> convertIconBoList2EntityList(List<SelfDanmakuIconConfigBo> iconConfigBoList) {
        if (CollectionUtils.isEmpty(iconConfigBoList)) {
            return Collections.emptyList();
        }

        return iconConfigBoList.stream().map(iconConfigBo -> {
            return IconEntity.newBuilder()
                    .setIconId(iconConfigBo.getId())
                    .setIconName(iconConfigBo.getIconName())
                    .setIconUrl(iconConfigBo.getIconUrl())
                    .setColorCode(iconConfigBo.getColorCode())
                    .build();
        }).collect(Collectors.toList());
    }

    private QueryDanmakuGroupAuditRecordBo convertAuditRecordRequest2QueryBo(DanmakuAuditRecordRequest request) {
        return QueryDanmakuGroupAuditRecordBo.builder()
                .accountId(Utils.isPositive(request.getAccountId()) ? request.getAccountId() : null)
                .auditorName(request.getAuditorName())
                .operateType(Utils.isPositive(request.getOperateType()) ? request.getOperateType() : null)
                .startSendAuditTime(Utils.isPositive(request.getStartSendAuditTime()) ? request.getStartSendAuditTime() : null)
                .endSendAuditTime(Utils.isPositive(request.getEndSendAuditTime()) ? request.getEndSendAuditTime() : null)
                .startAuditTime(Utils.isPositive(request.getStartAuditTime()) ? request.getStartAuditTime() : null)
                .endAuditTime(Utils.isPositive(request.getEndAuditTime()) ? request.getEndAuditTime() : null)
                .workOrderId(Utils.isPositive(request.getWorkOrderId()) ? request.getWorkOrderId() : null)
                .groupIdList(request.getGroupIdList())
                .beginId(Utils.isPositive(request.getBeginId()) ? request.getBeginId() : null)
                .page(1)
                .pageSize(100)
                .orderBy("mtime desc")
                .build();
    }

    private DanmakuAuditRecordReply convertAuditRecordPageResult2Reply(List<DanmakuGroupAuditRecordBo> boList) {
        DanmakuAuditRecordReply.Builder builder = DanmakuAuditRecordReply.newBuilder();
        List<DanmakuAuditRecordEntity> entityList = convertAuditRecordList2EntityList(boList);
        if (!CollectionUtils.isEmpty(entityList)) {
            builder.addAllEntity(entityList);
        }
        return builder.build();
    }

    private List<DanmakuAuditRecordEntity> convertAuditRecordList2EntityList(List<DanmakuGroupAuditRecordBo> boList) {
        if (CollectionUtils.isEmpty(boList)) {
            return Collections.emptyList();
        }

        return boList.stream().map(bo -> {
            return DanmakuAuditRecordEntity.newBuilder()
                    .setId(bo.getId())
                    .setGroupId(bo.getGroupId())
                    .setGroupName(bo.getGroupName())
                    .setGroupType(bo.getGroupType())
                    .setGroupTypeDesc(SelfDanmakuGroupTypeEnum.getByCode(bo.getGroupType()).getDesc())
                    .setAccountId(bo.getAccountId())
                    .setAccountName(bo.getAccountName())
                    .setOperateType(bo.getOperateType())
                    .setAuditorName(bo.getAuditorName())
                    .setSendAuditTime(Utils.getTimestamp2StringBySecond(bo.getSendAuditTime()))
                    .setAuditTime(Utils.getTimestamp2StringBySecond(bo.getAuditTime()))
                    .setReason(bo.getReason())
                    .setWorkOrderId(bo.getWorkOrderId())
                    .setDanmakuNum(bo.getDanmakuNum())
                    .build();
        }).collect(Collectors.toList());
    }
}
