package com.bilibili.adp.v6.resource.common;

import com.bapis.account.service.Info;
import com.bapis.dynamic.service.feed.DynInfo;
import com.bapis.dynamic.service.feed.DynSimpleInfo;
import com.bilibili.adp.cpc.biz.services.account.InfoService;
import com.bilibili.adp.cpc.biz.services.dynamic.DynamicService;
import com.bilibili.adp.v6.resource.dynamic.bos.DynamicInfoBo;
import com.bilibili.adp.v6.resource.dynamic.converters.CommercialOrderDynamicConverter;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface DynamicInfo {
    default List<DynamicInfoBo> queryDynamicInfo(DynamicService dynamicService, InfoService accountService,
                                                 List<Long> dynamicList) {
        if (CollectionUtils.isEmpty(dynamicList)) {
            return Collections.emptyList();
        }
        Map<Long, DynInfo> dynInfosMap = dynamicService.dynInfosMap(dynamicList, false, true);
        List<Long> midList = dynInfosMap.values().stream().map(DynInfo::getSimpleInfo).map(DynSimpleInfo::getUid)
                .distinct().collect(Collectors.toList());
        Map<Long, Info> infosMap = accountService.infoMap(midList);
        return dynInfosMap.values().stream().map(dynInfo -> {
            long mid = dynInfo.getSimpleInfo().getUid();
            Info info = infosMap.getOrDefault(mid, Info.getDefaultInstance());
            return CommercialOrderDynamicConverter.MAPPER.toBo(info, dynInfo);
        }).collect(Collectors.toList());
    }
}
