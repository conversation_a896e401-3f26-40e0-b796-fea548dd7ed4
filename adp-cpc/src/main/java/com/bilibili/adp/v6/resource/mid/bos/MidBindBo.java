package com.bilibili.adp.v6.resource.mid.bos;

import com.bilibili.adp.v6.enums.mid.AuthModeEnum;
import com.bilibili.adp.v6.enums.mid.AuthShareScopeEnum;
import com.bilibili.adp.v6.enums.mid.AuthSourceEnum;
import com.bilibili.adp.v6.enums.mid.AuthTimeTypeEnum;
import lombok.Data;

@Data
public class MidBindBo {
    private Integer id;

    private Long mid;

    private AuthModeEnum authMode;

    private AuthShareScopeEnum shareScope;

    private AuthTimeTypeEnum authTimeType;

    private Long authEffectiveTime;

    private Long authExpireTime;

    private Integer accountId;

    private AuthSourceEnum source;

    private String userName;
}
