package com.bilibili.adp.v6.resource.archive.bos;

import com.bilibili.adp.cpc.enums.archive.ArchiveRequestTypeEnum;
import com.bilibili.adp.cpc.enums.archive.ArchiveShareScopeEnum;
import com.bilibili.adp.cpc.enums.commercial.CommercialOrderArchiveAuthModeEnum;
import com.bilibili.adp.cpc.enums.commercial.CommercialOrderArchiveTimeLimitEnum;
import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
public class GeneralVideoAuthorizeInfoBo {
    private Long accountId;
    private String accountName;

    private int customerId;
    private String customerName;

    private int agentId;
    private String agentName;

    private int productId;
    private String productName;

    private CommercialOrderArchiveAuthModeEnum authMode;

    private ArchiveShareScopeEnum shareScope;

    private Long avid;
    private String bvid;
    private String cover;
    private String title;

    private List<GeneralVideoMidInfoBo> generalVideoMidInfoBoList;

    private CommercialOrderArchiveTimeLimitEnum authTimeType;

    private Timestamp authStartTime;
    private Timestamp authEndTime;

    private ArchiveRequestTypeEnum requestType;

    private Timestamp renewalEndTime;
}
