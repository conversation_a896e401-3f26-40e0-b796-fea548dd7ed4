package com.bilibili.adp.v6.report.enums;

import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PromotionPurposeTypeV5 {
    LANDING_PAGE(PromotionPurposeType.LANDING_PAGE.getCode(), PromotionPurposeType.LANDING_PAGE.getDesc()),
    APP_DOWNLOAD(PromotionPurposeType.APP_DOWNLOAD.getCode(), PromotionPurposeType.APP_DOWNLOAD.getDesc()),
    SHOP_GOODS(PromotionPurposeType.SHOP_GOODS.getCode(), PromotionPurposeType.SHOP_GOODS.getDesc()),
    ON_SHELF_GAME(PromotionPurposeType.ON_SHELF_GAME.getCode(), PromotionPurposeType.ON_SHELF_GAME.getDesc()),
    LIVE_ROOM(PromotionPurposeType.LIVE_ROOM.getCode(), PromotionPurposeType.LIVE_ROOM.getDesc()),
    SALE_GOODS(PromotionPurposeType.SALE_GOODS.getCode(), PromotionPurposeType.SALE_GOODS.getDesc()),
    ENTERPRISE_PROMOTION(PromotionPurposeType.ENTERPRISE_PROMOTION.getCode(), PromotionPurposeType.ENTERPRISE_PROMOTION.getDesc()),
    BRAND_SPREAD(PromotionPurposeType.BRAND_SPREAD.getCode(), PromotionPurposeType.BRAND_SPREAD.getDesc()),
    GAME_CARD(PromotionPurposeType.GAME_CARD.getCode(), PromotionPurposeType.GAME_CARD.getDesc()),
    GAME_ACTIVITY_CARD(PromotionPurposeType.GAME_ACTIVITY_CARD.getCode(), PromotionPurposeType.GAME_ACTIVITY_CARD.getDesc()),
    ;
    private final int promotionPurposeType;
    private final String promotionPurposeTypeDesc;
}
