package com.bilibili.adp.v6.enums.mid;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AuthMidTypeEnum {
    ORDINARY(0, "个人号"),
    ENTERPRISE(1, "企业号"),
    BUSINESS(2, "经营号"),
    STAFF(3, "员工号"),;
    private final Integer code;
    private final String desc;

    public static AuthMidTypeEnum getByCode(Integer code) {
        for (AuthMidTypeEnum authMidTypeEnum : AuthMidTypeEnum.values()) {
            if (authMidTypeEnum.getCode().equals(code)) {
                return authMidTypeEnum;
            }
        }
        throw new IllegalArgumentException("不支持的账号类型");
    }
}
