package com.bilibili.adp.v6.resource.archive.bos;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/3
 * @description 合伙人任务bo
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PartnerTaskBo {


    private Long taskId;

    private String taskName;

    private String taskDate;

    private Integer authType;

    private String taskIcon;

    private String authSource;

    private String accountSource;

    private String authStatus;

    private String authDate;
}
