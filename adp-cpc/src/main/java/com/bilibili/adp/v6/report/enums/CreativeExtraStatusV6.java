package com.bilibili.adp.v6.report.enums;

import com.bilibili.adp.cpc.enums.CreativeExtraStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 推广创意状态
 * @see com.bilibili.adp.cpc.enums.CreativeExtraStatus
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum CreativeExtraStatusV6 {
    DEFAULT(
            StatusDimension.ALL,
            CreativeExtraStatus.NONE.getCode(),
            CreativeExtraStatus.NONE.getDesc(),
            AccountStatusV6.DEFAULT,
            CampaignStatusV6.DEFAULT,
            UnitStatusV6.DEFAULT,
            CreativeStatusV6.DEFAULT
    ),
    RUNNING(
            StatusDimension.ALL,
            CreativeExtraStatus.RUNNING.getCode(),
            CreativeExtraStatus.RUNNING.getDesc(),
            AccountStatusV6.VALID,
            CampaignStatusV6.VALID,
            UnitStatusV6.VALID,
            CreativeStatusV6.VALID
    ),
    NOT_ENOUGH_MONEY(
            StatusDimension.ACCOUNT,
            CreativeExtraStatus.NOT_ENOUGH_MONEY.getCode(),
            CreativeExtraStatus.NOT_ENOUGH_MONEY.getDesc(),
            AccountStatusV6.NOT_ENOUGH_MONEY,
            null,
            null,
            CreativeStatusV6.VALID
    ),
    CAMPAIGN_EXCEED_BUDGET(
            StatusDimension.CAMPAIGN,
            CreativeExtraStatus.CAMPAIGN_EXCEED_BUDGET.getCode(),
            CreativeExtraStatus.CAMPAIGN_EXCEED_BUDGET.getDesc(),
            null,
            CampaignStatusV6.BUDGET_EXCEED,
            null,
            CreativeStatusV6.VALID
    ),
    CAMPAIGN_PAUSED(
            StatusDimension.CAMPAIGN,
            CreativeExtraStatus.CAMPAIGN_PAUSED.getCode(),
            CreativeExtraStatus.CAMPAIGN_PAUSED.getDesc(),
            null,
            CampaignStatusV6.PAUSED,
            null,
            CreativeStatusV6.VALID
    ),
    UNIT_EXCEED_BUDGET(
            StatusDimension.UNIT,
            CreativeExtraStatus.UNIT_EXCEED_BUDGET.getCode(),
            CreativeExtraStatus.UNIT_EXCEED_BUDGET.getDesc(),
            null,
            null,
            UnitStatusV6.BUDGET_EXCEED,
            CreativeStatusV6.VALID
    ),
    UNIT_NOT_IN_LAUNCH_TIME(
            StatusDimension.UNIT,
            CreativeExtraStatus.UNIT_NOT_IN_LAUNCH_TIME.getCode(),
            CreativeExtraStatus.UNIT_NOT_IN_LAUNCH_TIME.getDesc(),
            null,
            null,
            UnitStatusV6.NOT_IN_LAUNCH_TIME,
            CreativeStatusV6.VALID
    ),
    UNIT_FINISHED(
            StatusDimension.UNIT,
            CreativeExtraStatus.UNIT_FINISHED.getCode(),
            CreativeExtraStatus.UNIT_FINISHED.getDesc(),
            null,
            null,
            UnitStatusV6.FINISHED,
            CreativeStatusV6.VALID
    ),
    UNIT_PAUSED(
            StatusDimension.UNIT,
            CreativeExtraStatus.UNIT_PAUSED.getCode(),
            CreativeExtraStatus.UNIT_PAUSED.getDesc(),
            null,
            null,
            UnitStatusV6.PAUSED,
            CreativeStatusV6.VALID
    ),
    UNIT_NOT_START(
            StatusDimension.UNIT,
            CreativeExtraStatus.UNIT_NOT_START.getCode(),
            CreativeExtraStatus.UNIT_NOT_START.getDesc(),
            null,
            null,
            UnitStatusV6.NOT_START,
            CreativeStatusV6.VALID
    ),
    ;

    private final StatusDimension statusDimension;
    private final int creativeExtraStatus;
    private final String creativeExtraStatusDesc;
    private final AccountStatusV6 accountStatus;
    private final CampaignStatusV6 campaignStatus;
    private final UnitStatusV6 unitStatus;
    private final CreativeStatusV6 creativeStatus;

    public static final Map<CreativeStatusV6, List<CreativeExtraStatusV6>> CREATIVE_EXTRA_STATUS_MAP = Arrays.stream(values())
            .collect(Collectors.groupingBy(CreativeExtraStatusV6::getCreativeStatus));

    public static List<CreativeExtraStatusV6> of(int accountStatus, int campaignStatus, int unitStatus, int creativeStatus) {
        List<CreativeExtraStatusV6> res = new ArrayList<>();

        Map<StatusDimension, List<CreativeExtraStatusV6>> groupByDimension = Arrays.stream(values()).collect(Collectors.groupingBy(CreativeExtraStatusV6::getStatusDimension));

        //创意额外状态 -> 全部维度 只关心 账户状态 + 计划状态 + 单元状态 + 创意状态
        for (CreativeExtraStatusV6 status : groupByDimension.get(StatusDimension.ALL)) {
            if (status.accountStatus.getAccountStatus() == accountStatus
                    && status.campaignStatus.getCampaignStatus() == campaignStatus
                    && status.unitStatus.getUnitStatus() == unitStatus
                    && status.creativeStatus.getCreativeStatus() == creativeStatus) {
                 res.add(status);
            }
        }

        //创意额外状态 -> 账户维度 只关心 账户状态 + 创意状态
        for (CreativeExtraStatusV6 status : groupByDimension.get(StatusDimension.ACCOUNT)) {
            if (status.accountStatus.getAccountStatus() == accountStatus
                    && status.creativeStatus.getCreativeStatus() == creativeStatus) {
                res.add(status);
            }
        }

        //创意额外状态 -> 计划维度 只关心 计划状态 + 创意状态
        for (CreativeExtraStatusV6 status : groupByDimension.get(StatusDimension.CAMPAIGN)) {
            if (status.campaignStatus.getCampaignStatus() == campaignStatus
                    && status.creativeStatus.getCreativeStatus() == creativeStatus) {
                res.add(status);
            }
        }

        //创意额外状态 -> 单元维度 只关心 单元状态 + 创意状态
        for (CreativeExtraStatusV6 status : groupByDimension.get(StatusDimension.UNIT)) {
            if (status.unitStatus.getUnitStatus() == unitStatus
                    && status.creativeStatus.getCreativeStatus() == creativeStatus) {
                res.add(status);
            }
        }
        return res;
    }

    public static CreativeExtraStatusV6 of(Integer creativeExtraStatus) {
        for (CreativeExtraStatusV6 status : values()) {
            if (status.creativeExtraStatus == creativeExtraStatus) {
                return status;
            }
        }
        log.error("unknown creativeExtraStatus:{}", creativeExtraStatus);
        return DEFAULT;
    }
}
