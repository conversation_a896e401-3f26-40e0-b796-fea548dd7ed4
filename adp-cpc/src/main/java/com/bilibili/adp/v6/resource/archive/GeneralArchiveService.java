package com.bilibili.adp.v6.resource.archive;

import cn.hutool.core.lang.Assert;
import com.bapis.ad.account.crm.acc.*;
import com.bapis.archive.service.Arc;
import com.bapis.archive.service.StaffInfo;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.SystemException;
import com.bilibili.adp.common.util.TimeUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.account.ProfileInfoService;
import com.bilibili.adp.cpc.biz.services.account.bos.ProfileDto;
import com.bilibili.adp.cpc.biz.services.account.config.AccountConfig;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.core.LaunchCreativeService;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeArchivePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauArchiveAuthorizedRelationPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauMidArchiveAcceptRecordPo;
import com.bilibili.adp.cpc.enums.LaunchStatus;
import com.bilibili.adp.cpc.enums.archive.*;
import com.bilibili.adp.cpc.enums.commercial.CommercialOrderArchiveAuthModeEnum;
import com.bilibili.adp.cpc.enums.commercial.CommercialOrderArchiveTimeLimitEnum;
import com.bilibili.adp.cpc.repo.AuthorizedArchiveEsRepo;
import com.bilibili.adp.launch.api.creative.dto.CpcCreativeAuditDto;
import com.bilibili.adp.passport.biz.manager.MessageManager;
import com.bilibili.adp.v6.constants.SanlianGeneralRpcConstants;
import com.bilibili.adp.v6.resource.archive.bos.*;
import com.bilibili.adp.v6.resource.archive.converters.CommercialOrderArchiveServiceConverter;
import com.bilibili.adp.v6.resource.archive.converters.GeneralArchiveServiceConverter;
import com.bilibili.adp.v6.resource.archive.converters.PartnerArchiveConverter;
import com.bilibili.adp.v6.resource.common.BasicGeneralAuthService;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.paging.Page;
import com.bilibili.bjcom.querydsl.paging.Pager;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.soa.ISoaAgentService;
import com.bilibili.crm.platform.soa.ISoaCustomerService;
import com.google.common.base.Strings;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.biz.constants.Constants.INT_FALSE;
import static com.bilibili.adp.cpc.biz.constants.Constants.INT_TRUE;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeArchive.lauCreativeArchive;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauArchiveAuthorizedRelation.lauArchiveAuthorizedRelation;
import static com.bilibili.adp.cpc.dao.querydsl.QLauMidArchiveAcceptRecord.lauMidArchiveAcceptRecord;

@Slf4j
@Service
@RequiredArgsConstructor
public class GeneralArchiveService extends BasicGeneralAuthService {

    private static final String GENERAL_ARCHIVE_BIND_SEND_MESSAGE_LINK = "https://cm.bilibili.com/art/activities/general-video-authorization-v2";
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;
    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    @RPCClient("sycpb.cpm.ad-account")
    private CrmAccountServiceGrpc.CrmAccountServiceBlockingStub crmAccountServiceBlockingStub;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ArchiveService archiveService;

    @Autowired
    private MessageManager messageManager;

    @Autowired
    private ProfileInfoService profileInfoService;
    @Resource
    private AuthorizedArchiveEsRepo authorizedArchiveRepo;
    @Resource
    private LaunchCreativeService launchCreativeService;
    @Resource
    private AccountConfig accountConfig;
    private static final String REJECT_REASON_INVALID_PUGC = "课堂稿件下架";

    private static void setRelationByScope(ArchiveShareScopeEnum shareScope,
                                           LauArchiveAuthorizedRelationPo lauArchiveAuthorizedRelationPo, int dependencyAgentId, int customerId,
                                           int productId) {
        // 根据不同的scope对应列不同值
        switch (shareScope) {
            case THIS_ACCOUNT_ONLY:
                lauArchiveAuthorizedRelationPo.setAgentId(0);
                lauArchiveAuthorizedRelationPo.setCustomerId(0);
                lauArchiveAuthorizedRelationPo.setProductId(0);
                break;
            case SAME_CUSTOMER_SAME_AGENT:
                lauArchiveAuthorizedRelationPo.setAgentId(dependencyAgentId);
                lauArchiveAuthorizedRelationPo.setCustomerId(customerId);
                lauArchiveAuthorizedRelationPo.setProductId(0);
                break;
            case SAME_CUSTOMER_SAME_BRAND:
                lauArchiveAuthorizedRelationPo.setAgentId(0);
                lauArchiveAuthorizedRelationPo.setCustomerId(customerId);
                lauArchiveAuthorizedRelationPo.setProductId(productId);
                break;
            case SAME_CUSTOMER_SAME_BRAND_SAME_AGENT:
                lauArchiveAuthorizedRelationPo.setAgentId(dependencyAgentId);
                lauArchiveAuthorizedRelationPo.setCustomerId(customerId);
                lauArchiveAuthorizedRelationPo.setProductId(productId);
                break;
            case SAME_CUSTOMER:
                lauArchiveAuthorizedRelationPo.setAgentId(0);
                lauArchiveAuthorizedRelationPo.setCustomerId(customerId);
                lauArchiveAuthorizedRelationPo.setProductId(0);
                break;
            case SAME_AGENT:
                lauArchiveAuthorizedRelationPo.setAgentId(dependencyAgentId);
                lauArchiveAuthorizedRelationPo.setCustomerId(0);
                lauArchiveAuthorizedRelationPo.setProductId(0);
                break;
            default:
                throw new IllegalArgumentException("不正确的共享范围");
        }
    }

    private void preProcessQueryBo(GeneralVideoArcAuthInfoQueryBo queryBo) {
        if (Objects.isNull(queryBo)) {
            return;
        }

        Assert.isTrue(Utils.isPositive(queryBo.getAccountId()), "查询普通内容稿件账户id不可为空");
        Assert.isTrue(Utils.isPositive(queryBo.getPage()), "查询普通内容稿件分页不可为空");
        Assert.isTrue(Utils.isPositive(queryBo.getPageSize()), "查询普通内容稿件分页大小不可为空");

        if (!Utils.isPositive(queryBo.getUpMid())) {
            queryBo.setUpMid(null);
        }

        if (!Utils.isPositive(queryBo.getAvid())) {
            queryBo.setAvid(null);
        }
    }

    public PageResult<LauArchiveAuthorizedRelationBaseBo> queryBaseBoByPage(GeneralVideoArcAuthInfoQueryBo queryBo) {
        PageResult<LauArchiveAuthorizedRelationPo> poPageResult = queryPoByPage(queryBo);
        int total = poPageResult.getTotal();
        List<LauArchiveAuthorizedRelationBaseBo> baseBoList = poPageResult.getRecords().stream()
                .map(po -> {
                    return LauArchiveAuthorizedRelationBaseBo.builder()
                            .id(po.getId())
                            .avid(po.getAvid())
                            .build();
                })
                .collect(Collectors.toList());
        return PageResult.<LauArchiveAuthorizedRelationBaseBo>builder()
                .records(baseBoList)
                .total(total)
                .build();
    }

    private PageResult<LauArchiveAuthorizedRelationPo> queryPoByPage(GeneralVideoArcAuthInfoQueryBo queryBo) {
        preProcessQueryBo(queryBo);

        Integer accountId = queryBo.getAccountId();
        Integer authMode = queryBo.getAuthMode();
        Integer authSource = queryBo.getAuthSource();
        Integer status = queryBo.getStatus();
        Long upMid = queryBo.getUpMid();
        Integer page = queryBo.getPage();
        Integer pageSize = queryBo.getPageSize();
        Long avid = queryBo.getAvid();
        Integer arcType = queryBo.getArcType();
        String keyWord = queryBo.getKeyWord();
        boolean fromMaster = queryBo.isFromMaster();

        // 客户、品牌、代理
        AccountBaseReply accountBase = crmAccountServiceBlockingStub.getAccountBase(
                AccountIdReq.newBuilder().setAccountId(accountId).build());
        int customerId = accountBase.getData().getCustomerId();
        int dependencyAgentId = accountBase.getData().getDependencyAgentId();
        int productId = accountBase.getData().getProductId();

        long total;
        List<LauArchiveAuthorizedRelationPo> rows;
        if (fromMaster && Strings.isNullOrEmpty(keyWord)) {
            // 客户or品牌or代理相同，通过共享得到的授权
            BooleanExpression expr = genShareExpr(accountId, customerId, dependencyAgentId, productId);

            Page<LauArchiveAuthorizedRelationPo> lauArchiveAuthorizedRelationPoPage = adBqf.selectFrom(
                    lauArchiveAuthorizedRelation).where(expr)
                    .whereIfNotNull(authMode, lauArchiveAuthorizedRelation.authMode::eq)
                    .whereIfNotNull(authSource, lauArchiveAuthorizedRelation.authSource::eq)
                    .whereIfNotNull(status, lauArchiveAuthorizedRelation.authStatus::eq)
                    .whereIfNotNull(upMid, lauArchiveAuthorizedRelation.mid::eq)
                    .whereIfNotNull(avid, lauArchiveAuthorizedRelation.avid::eq)
                    .whereIfNotNull(arcType, lauArchiveAuthorizedRelation.archiveType::eq)
                    .where(lauArchiveAuthorizedRelation.isDeleted.eq(INT_FALSE))
                    .orderBy(lauArchiveAuthorizedRelation.mtime.desc()).fetchPage(Pager.of(page, pageSize));
            total = lauArchiveAuthorizedRelationPoPage.getTotal();
            rows = lauArchiveAuthorizedRelationPoPage.getRows();
        } else {
            PageResult<LauArchiveAuthorizedRelationPo> arcAuthPosFromEs = authorizedArchiveRepo.getArchiveAuthorizedRelationPoList(accountId, customerId, dependencyAgentId, productId, authMode, authSource,
                    status, upMid, page, pageSize, avid, arcType, keyWord);
            total = arcAuthPosFromEs.getTotal();
            rows = arcAuthPosFromEs.getRecords();
        }
        if (!Utils.isPositive(total)) {
            return PageResult.emptyPageResult();
        }

        return PageResult.<LauArchiveAuthorizedRelationPo>builder()
                .records(rows)
                .total((int)total)
                .build();
    }

    public PageResult<GeneralArchiveAuthInfoBo> list(GeneralVideoArcAuthInfoQueryBo queryBo) {
        PageResult<LauArchiveAuthorizedRelationPo> poPageResult = queryPoByPage(queryBo);
        List<LauArchiveAuthorizedRelationPo> rows = poPageResult.getRecords();
        int total = poPageResult.getTotal();

        List<Long> avids = rows.stream()
                .map(LauArchiveAuthorizedRelationPo::getAvid).collect(Collectors.toList());
        Map<Long, Arc> arcMap = archiveService.arcsMap(avids);

        List<GeneralArchiveAuthInfoBo> bos = new ArrayList<>();
        for (LauArchiveAuthorizedRelationPo r : rows) {
            CommercialOrderAuthTimeBo commercialOrderAuthTimeBo = new CommercialOrderAuthTimeBo();
            commercialOrderAuthTimeBo.setTimeLimitType(
                    CommercialOrderArchiveTimeLimitEnum.getByCode(r.getAuthTimeType()));
            commercialOrderAuthTimeBo.setAuthBeginTime(r.getAuthStartTime().getTime());
            commercialOrderAuthTimeBo.setAuthEndTime(r.getAuthEndTime().getTime());
            GeneralArchiveAuthInfoBo build = GeneralArchiveAuthInfoBo.builder().authId(r.getId())
                    .timeInfo(commercialOrderAuthTimeBo)
                    .archiveInfo(CommercialOrderArchiveServiceConverter.MAPPER.toBo(arcMap.get(r.getAvid())))
                    .authMode(CommercialOrderArchiveAuthModeEnum.getByCode(r.getAuthMode()))
                    .shareScope(ArchiveShareScopeEnum.getByCode(r.getSharingScope()))
                    .authSource(ArchiveAuthSourceEnum.getByCode(r.getAuthSource()))
                    .authStatus(ArchiveAuthStatusEnum.getByCode(r.getAuthStatus()))
                    .sourceAccountId(Math.toIntExact(r.getAccountId()))
                    .renewalStatus(ArchiveAuthRenewalStatusEnum.getByCode(r.getRenewalStatus()))
                    .arcType(ArchiveAuthTypeEnum.getByCode(r.getArchiveType()))
                    .authTitle(r.getArchiveTitle())
                    .updateStatus(ArchiveAuthUpdateStatusEnum.getByCode(r.getUpdateStatus())).build();
            bos.add(build);
        }
        return PageResult.<GeneralArchiveAuthInfoBo>builder().total(total).records(bos).build();
    }

    private BooleanExpression genShareExpr(Integer accountId, int customerId, int dependencyAgentId, int productId) {
        // 仅本账号
        BooleanExpression expr = lauArchiveAuthorizedRelation.accountId.eq(Long.valueOf(accountId));
        // 同客户&同代理
        expr = expr.or(lauArchiveAuthorizedRelation.customerId.eq(customerId)
                .and(lauArchiveAuthorizedRelation.agentId.eq(dependencyAgentId))
                .and(lauArchiveAuthorizedRelation.sharingScope.eq(
                        ArchiveShareScopeEnum.SAME_CUSTOMER_SAME_AGENT.getCode())));
        // 同客户&同品牌
        expr = expr.or(lauArchiveAuthorizedRelation.customerId.eq(customerId)
                .and(lauArchiveAuthorizedRelation.productId.eq(productId))
                .and(lauArchiveAuthorizedRelation.sharingScope.eq(
                        ArchiveShareScopeEnum.SAME_CUSTOMER_SAME_BRAND.getCode())));
        // 同客户&同品牌&同代理
        expr = expr.or(lauArchiveAuthorizedRelation.customerId.eq(customerId)
                .and(lauArchiveAuthorizedRelation.productId.eq(productId))
                .and(lauArchiveAuthorizedRelation.agentId.eq(dependencyAgentId))
                .and(lauArchiveAuthorizedRelation.sharingScope.eq(
                        ArchiveShareScopeEnum.SAME_CUSTOMER_SAME_BRAND_SAME_AGENT.getCode())));
        // 同代理
        expr = expr.or(lauArchiveAuthorizedRelation.agentId.eq(dependencyAgentId)
                .and(lauArchiveAuthorizedRelation.sharingScope.eq(ArchiveShareScopeEnum.SAME_AGENT.getCode())));
        return expr;
    }

    public GeneralArchiveStatusBo state(Integer accountId, long avid) {
        mustNotCommercialArchive(avid);

        Map<Long, Arc> arcMap = archiveService.arcsMap(Collections.singletonList(avid));
        Assert.notNull(arcMap.get(avid), "稿件信息不存在");

        int authTimes = queryArchiveAuthTimes(accountId, avid);
        int renewalTimes = queryRenewalTimes(accountId, avid);

        // 是否已有授权
        // 客户、品牌、代理
        AccountBaseReply accountBase = crmAccountServiceBlockingStub.getAccountBase(
                AccountIdReq.newBuilder().setAccountId(accountId).build());
        int customerId = accountBase.getData().getCustomerId();
        int dependencyAgentId = accountBase.getData().getDependencyAgentId();
        int productId = accountBase.getData().getProductId();
        boolean hasAuthorized = hasArchiveAuth(accountId, avid, customerId, dependencyAgentId, productId);

        return GeneralArchiveServiceConverter.MAPPER.toBo(authTimes, renewalTimes,
                CommercialOrderArchiveServiceConverter.MAPPER.toBo(arcMap.get(avid)), hasAuthorized);
    }

    @Override
    protected int queryArchiveAuthTimes(int accountId, long avid) {
        String key = authTimeKeyGen(accountId, avid, "authorized");
        String i = redisTemplate.opsForValue().get(key);
        return i == null ? 0 : Integer.parseInt(i);
    }

    private int queryRenewalTimes(Integer accountId, long avid) {
        String key = authTimeKeyGen(accountId, avid, "renewal");
        String i = redisTemplate.opsForValue().get(key);
        return i == null ? 0 : Integer.parseInt(i);
    }

    private boolean hasArchiveAuth(Integer accountId, long avid, Integer customerId, Integer dependencyAgentId,
                                   Integer productId) {
        BooleanExpression expr = genShareExpr(accountId, customerId, dependencyAgentId, productId);
        LauArchiveAuthorizedRelationPo lauArchiveAuthorizedRelationPo = adBqf.selectFrom(lauArchiveAuthorizedRelation)
                .where(lauArchiveAuthorizedRelation.avid.eq(avid)).where(expr)
                .where(lauArchiveAuthorizedRelation.authStatus.in(ArchiveAuthStatusEnum.REVOKABLE_STATUS_SET))
                .where(lauArchiveAuthorizedRelation.isDeleted.eq(INT_FALSE)).fetchFirst();
        return lauArchiveAuthorizedRelationPo != null;
    }

    private String authTimeKeyGen(int accountId, long avid, String suffix) {
        String result = "archive_auth:{accountId}:{avid}".replace("{accountId}", String.valueOf(accountId))
                .replace("{avid}", String.valueOf(avid));
        if (!suffix.isEmpty()) {
            result += ":" + suffix;
        }
        return result;
    }

    public void batchBind(Integer accountId, GeneralArchiveBindBo bo) {
        List<Long> avids = bo.getAvids();
        for (Long avid : avids) {
            try {
                bindOne(accountId, avid, bo.getAuthMode(), bo.getTimeInfo(), bo.getShareScope(),
                        avids.size() > 1 ? ArchiveAuthSourceEnum.ACTIVITY : ArchiveAuthSourceEnum.APPLY);
            } catch (ServiceException | SystemException e) {
                log.error("发送站内信失败，avid={}, err={}", avid, e.getMessage());
            }
        }
    }

    private void bindOne(Integer accountId, Long avid, CommercialOrderArchiveAuthModeEnum authMode,
                         CommercialOrderAuthTimeBo timeInfo, ArchiveShareScopeEnum shareScope,
                         ArchiveAuthSourceEnum authSource) throws ServiceException, SystemException {
        mustTimeInfoLegal(timeInfo);
        mustNotCommercialArchive(avid);
        // 客户、品牌、代理
        AccountBaseReply accountBase = crmAccountServiceBlockingStub.getAccountBase(
                AccountIdReq.newBuilder().setAccountId(accountId).build());
        int customerId = accountBase.getData().getCustomerId();
        int dependencyAgentId = accountBase.getData().getDependencyAgentId();
        int productId = accountBase.getData().getProductId();

        // 稿件信息
        Map<Long, Arc> arcMap = archiveService.arcsMap(Collections.singletonList(avid));
        Assert.isTrue(arcMap.get(avid) != null, "稿件信息不存在");
        Arc arc = arcMap.get(avid);

        // 授权次数
        int authTimes = queryArchiveAuthTimes(accountId, avid);
        Assert.isTrue(authTimes < MAX_AUTH_REQUEST_TIME,
                "7日内已申请授权超过" + MAX_AUTH_REQUEST_TIME + "次，请下周再来试试吧。");

        // 已申请授权、等待生效或在生效中
        boolean hasAuthorized = hasArchiveAuth(accountId, avid, customerId, dependencyAgentId, productId);
        Assert.isTrue(!hasAuthorized, "已发起授权申请，无需重复申请");

        // 如果当前账号已有授权关系则更新
        LauArchiveAuthorizedRelationPo lauArchiveAuthorizedRelationPo = adBqf.selectFrom(lauArchiveAuthorizedRelation)
                .where(lauArchiveAuthorizedRelation.avid.eq(avid))
                .where(lauArchiveAuthorizedRelation.accountId.eq(Long.valueOf(accountId)))
                .where(lauArchiveAuthorizedRelation.isDeleted.eq(INT_FALSE)).fetchFirst();

        long relationId = 0;
        if (lauArchiveAuthorizedRelationPo == null) {
            lauArchiveAuthorizedRelationPo = new LauArchiveAuthorizedRelationPo();
        } else {
            relationId = lauArchiveAuthorizedRelationPo.getId();
        }

        // 根据不同的scope对应列不同值
        setRelationByScope(shareScope, lauArchiveAuthorizedRelationPo, dependencyAgentId, customerId, productId);

        lauArchiveAuthorizedRelationPo.setAvid(avid);
        lauArchiveAuthorizedRelationPo.setAccountId(Long.valueOf(accountId));
        lauArchiveAuthorizedRelationPo.setAuthMode(authMode.getCode());
        lauArchiveAuthorizedRelationPo.setAuthSource(authSource.getCode());
        if (CommercialOrderArchiveTimeLimitEnum.TIME.equals(timeInfo.getTimeLimitType())) {
            lauArchiveAuthorizedRelationPo.setAuthStartTime(new Timestamp(timeInfo.getAuthBeginTime()));
            lauArchiveAuthorizedRelationPo.setAuthEndTime(new Timestamp(timeInfo.getAuthEndTime()));
        } else {
            lauArchiveAuthorizedRelationPo.setAuthStartTime(TimeUtils.nowTimestamp());
            lauArchiveAuthorizedRelationPo.setAuthEndTime(
                    Timestamp.valueOf(LocalDate.now().plusYears(100).atTime(LocalTime.MIDNIGHT)));

        }
        lauArchiveAuthorizedRelationPo.setAuthTimeType(timeInfo.getTimeLimitType().getCode());
        lauArchiveAuthorizedRelationPo.setAuthStatus(ArchiveAuthStatusEnum.WAIT_ACCEPT.getCode());
        lauArchiveAuthorizedRelationPo.setMid(arc.getAuthor().getMid());
        lauArchiveAuthorizedRelationPo.setSharingScope(shareScope.getCode());

        if (relationId == 0) {
            // 插入授权关系
            relationId = adBqf.insert(lauArchiveAuthorizedRelation).insertGetKey(lauArchiveAuthorizedRelationPo);
        } else {
            adBqf.update(lauArchiveAuthorizedRelation).updateBean(lauArchiveAuthorizedRelationPo);
        }
        // 发送站内信并记录授权请求
        List<Long> midList = new ArrayList<>();
        midList.add(arc.getAuthor().getMid());
        boolean isMultiAuthor = 1 == arc.getRights().getIsCooperation();
        if (isMultiAuthor) {
            midList.addAll(arc.getStaffInfoList().stream().map(StaffInfo::getMid).collect(Collectors.toList()));
        }
        for (Long mid : midList) {
            LauMidArchiveAcceptRecordPo lauMidArchiveAcceptRecordPo = new LauMidArchiveAcceptRecordPo();
            lauMidArchiveAcceptRecordPo.setMid(mid);
            lauMidArchiveAcceptRecordPo.setRequestType(ArchiveRequestTypeEnum.AUTHORIZE.getCode());
            lauMidArchiveAcceptRecordPo.setRelationId(relationId);
            lauMidArchiveAcceptRecordPo.setAuthMode(authMode.getCode());
            lauMidArchiveAcceptRecordPo.setSharingScope(shareScope.getCode());
            long authId = adBqf.insert(lauMidArchiveAcceptRecord).insertGetKey(lauMidArchiveAcceptRecordPo);
            //发送站内信给mid
            String content = getContent(accountId, accountBase, shareScope);
            String linkUrl = UriComponentsBuilder.fromUriString(GENERAL_ARCHIVE_BIND_SEND_MESSAGE_LINK)
                    .queryParam("auth_id", authId).build(false).toUriString();
            String linkStr = String.format("#{查看详情}{\"%s\"}", linkUrl);
            messageManager.sendMessageToMids("视频授权申请",
                    String.format("您有一条来自%s的视频授权申请，%s", content, linkStr), Collections.singletonList(mid));
        }

        // 发送后对请求进行计数
        addArchiveAuthTime(accountId, avid);
    }


    private void addArchiveAuthTime(int accountId, long avid) {
        String key = authTimeKeyGen(accountId, avid, "authorized");
        incrAtEndOfWeek(key);
    }

    private void incrAtEndOfWeek(String key) {
        incrTimes(key, redisTemplate);
    }

    public void cancel(Integer accountId, String authId) {
        // 可撤回的
        LauArchiveAuthorizedRelationPo lauArchiveAuthorizedRelationPo = adBqf.selectFrom(lauArchiveAuthorizedRelation)
                .where(lauArchiveAuthorizedRelation.id.eq(Long.valueOf(authId)))
                .where(lauArchiveAuthorizedRelation.isDeleted.eq(0)).fetchFirst();
        Assert.notNull(lauArchiveAuthorizedRelationPo, "查不到此项授权");
        Assert.isTrue(accountId == lauArchiveAuthorizedRelationPo.getAccountId().intValue(),
                "不能撤回其他用户发起的申请");
        Assert.isTrue(
                ArchiveAuthStatusEnum.REVOKABLE_STATUS_SET.contains(lauArchiveAuthorizedRelationPo.getAuthStatus()),
                "只有对发起授权中的绑定关系进行撤回");

        // 关系表状态更新
        adBqf.update(lauArchiveAuthorizedRelation)
                .set(lauArchiveAuthorizedRelation.authStatus, ArchiveAuthStatusEnum.REVOKED.getCode())
                .where(lauArchiveAuthorizedRelation.id.eq(Long.valueOf(authId)))
                .where(lauArchiveAuthorizedRelation.authStatus.in(ArchiveAuthStatusEnum.REVOKABLE_STATUS_SET))
                .execute();
        // 授权请求表更新
        adBqf.update(lauMidArchiveAcceptRecord).set(lauMidArchiveAcceptRecord.isCancel, INT_TRUE)
                .set(lauMidArchiveAcceptRecord.isDeleted, INT_TRUE)
                .where(lauMidArchiveAcceptRecord.relationId.eq(Long.valueOf(authId)))
                .where(lauMidArchiveAcceptRecord.isDeleted.eq(INT_FALSE))
                .where(lauMidArchiveAcceptRecord.isCancel.eq(INT_FALSE)).execute();
    }

    public void renewal(Integer accountId, GeneralArchiveRenewalBo bo) {
        Long authId = bo.getAuthId();
        LauArchiveAuthorizedRelationPo lauArchiveAuthorizedRelationPo = adBqf.selectFrom(lauArchiveAuthorizedRelation)
                .where(lauArchiveAuthorizedRelation.id.eq(authId)).where(lauArchiveAuthorizedRelation.isDeleted.eq(0))
                .fetchFirst();
        Assert.notNull(lauArchiveAuthorizedRelationPo, "查不到此项授权");
        long avid = lauArchiveAuthorizedRelationPo.getAvid();
        CommercialOrderAuthTimeBo timeInfo = bo.getTimeInfo();
        Assert.isTrue(accountId == lauArchiveAuthorizedRelationPo.getAccountId().intValue(),
                "不能操作共享授权视频稿件");
        Assert.isTrue(
                ArchiveAuthStatusEnum.CAN_RENEWAL_STATUS_SET.contains(lauArchiveAuthorizedRelationPo.getAuthStatus()),
                "只能对授权待生效或授权中的绑定关系进行续期操作");
        Assert.isTrue(!ArchiveAuthRenewalStatusEnum.WAIT_ACCEPT.getCode()
                        .equals(lauArchiveAuthorizedRelationPo.getRenewalStatus()),
                "已经发起续期请求，请等待up主通过后再次发起");
        Assert.isTrue(
                lauArchiveAuthorizedRelationPo.getAuthTimeType() == CommercialOrderArchiveTimeLimitEnum.TIME.getCode(),
                "只能对固定期限的授权进行续期操作");
        if (timeInfo.getTimeLimitType().equals(CommercialOrderArchiveTimeLimitEnum.UNLIMITED)) {
            timeInfo.setAuthEndTime(
                    Timestamp.valueOf(LocalDate.now().plusYears(100).atTime(LocalTime.MIDNIGHT)).getTime());
        }
        Assert.isTrue(lauArchiveAuthorizedRelationPo.getAuthEndTime().before(new Timestamp(timeInfo.getAuthEndTime())),
                "续期时间不能早于当前授权结束时间");
        int renewalTimes = queryRenewalTimes(accountId, avid);
        Assert.isTrue(MAX_RENEWAL_REQUEST_TIME > renewalTimes,
                "7天内只能对同一账号发起" + MAX_RENEWAL_REQUEST_TIME + "次续期");

        // 更新续期状态
        adBqf.update(lauArchiveAuthorizedRelation)
                .set(lauArchiveAuthorizedRelation.renewalStatus, ArchiveAuthRenewalStatusEnum.WAIT_ACCEPT.getCode())
                .where(lauArchiveAuthorizedRelation.id.eq(authId)).execute();

        // 发送站内信并记录授权请求
        // 稿件信息
        Map<Long, Arc> arcMap = archiveService.arcsMap(Collections.singletonList(avid));
        Assert.notNull(arcMap.get(avid), "稿件信息不存在");
        Arc arc = arcMap.get(avid);

        List<Long> midList = new ArrayList<>();
        midList.add(arc.getAuthor().getMid());
        boolean isMultiAuthor = 1 == arc.getRights().getIsCooperation();
        if (isMultiAuthor) {
            midList.addAll(arc.getStaffInfoList().stream().map(StaffInfo::getMid).collect(Collectors.toList()));
        }
        // 插入申请记录表
        for (Long mid : midList) {
            LauMidArchiveAcceptRecordPo lauMidArchiveAcceptRecordPo = new LauMidArchiveAcceptRecordPo();
            lauMidArchiveAcceptRecordPo.setMid(mid);
            lauMidArchiveAcceptRecordPo.setRelationId(authId);
            lauMidArchiveAcceptRecordPo.setRequestType(ArchiveRequestTypeEnum.RENEWAL.getCode());
            lauMidArchiveAcceptRecordPo.setAuthEndTime(new Timestamp(timeInfo.getAuthEndTime()));
            long reqId = adBqf.insert(lauMidArchiveAcceptRecord).insertGetKey(lauMidArchiveAcceptRecordPo);
            //发送站内信给mid

            AccountBaseReply accountBase = crmAccountServiceBlockingStub.getAccountBase(
                    AccountIdReq.newBuilder().setAccountId(accountId).build());
            String content = getContent(accountId, accountBase,
                    ArchiveShareScopeEnum.getByCode(lauArchiveAuthorizedRelationPo.getSharingScope()));
            String linkUrl = UriComponentsBuilder.fromUriString(GENERAL_ARCHIVE_BIND_SEND_MESSAGE_LINK)
                    .queryParam("auth_id", reqId).build(false).toUriString();
            String linkStr = String.format("#{查看详情}{\"%s\"}", linkUrl);
            try {
                messageManager.sendMessageToMids("视频续期申请",
                        String.format("您有一条来自%s的视频续期申请，%s", content, linkStr),
                        Collections.singletonList(mid));
            } catch (ServiceException | SystemException e) {
                log.error("发送站内信失败，mid={}, err={}", mid, e.getMessage());
            }
        }

        addRenewalTime(accountId, avid);
    }

    private void addRenewalTime(int accountId, long avid) {
        String key = authTimeKeyGen(accountId, avid, "renewal");
        incrAtEndOfWeek(key);
    }

    public void update(Integer accountId, GeneralArchiveUpdateBo bo) {
        Long authId = bo.getAuthId();
        LauArchiveAuthorizedRelationPo lauArchiveAuthorizedRelationPo = adBqf.selectFrom(lauArchiveAuthorizedRelation)
                .where(lauArchiveAuthorizedRelation.id.eq(authId))
                .where(lauArchiveAuthorizedRelation.isDeleted.eq(INT_FALSE)).fetchFirst();
        Assert.notNull(lauArchiveAuthorizedRelationPo, "查不到此项授权");
        Assert.isTrue(accountId == lauArchiveAuthorizedRelationPo.getAccountId().intValue(),
                "不能操作共享授权视频稿件");
        Assert.isTrue(
                ArchiveAuthStatusEnum.CAN_RENEWAL_STATUS_SET.contains(lauArchiveAuthorizedRelationPo.getAuthStatus()),
                "只能对授权待生效或授权中的绑定关系进行更新操作");
        Assert.isTrue(!ArchiveAuthUpdateStatusEnum.WAIT_ACCEPT.getCode()
                        .equals(lauArchiveAuthorizedRelationPo.getUpdateStatus()),
                "已经发起编辑请求，请等待up主通过后再次发起");
        Assert.isTrue(diffUpdateProperties(bo, lauArchiveAuthorizedRelationPo), "前后一致，无需修改");
        AccountBaseReply accountBase = crmAccountServiceBlockingStub.getAccountBase(
                AccountIdReq.newBuilder().setAccountId(accountId).build());
        Long avid = lauArchiveAuthorizedRelationPo.getAvid();
        int updateTimes = queryUpdateTimes(accountId, avid);
        Assert.isTrue(MAX_UPDATE_REQUEST_TIME > updateTimes,
                "7天内只能对同一账号发起" + MAX_UPDATE_REQUEST_TIME + "次变更");


        adBqf.update(lauArchiveAuthorizedRelation).where(lauArchiveAuthorizedRelation.id.eq(authId))
                .set(lauArchiveAuthorizedRelation.updateStatus, ArchiveAuthUpdateStatusEnum.WAIT_ACCEPT.getCode())
                .execute();
        // 稿件信息
        Map<Long, Arc> arcMap = archiveService.arcsMap(Collections.singletonList(avid));
        Assert.notNull(arcMap.get(avid), "稿件信息不存在");
        Arc arc = arcMap.get(avid);
        List<Long> midList = new ArrayList<>();
        midList.add(arc.getAuthor().getMid());
        boolean isMultiAuthor = 1 == arc.getRights().getIsCooperation();
        if (isMultiAuthor) {
            midList.addAll(arc.getStaffInfoList().stream().map(StaffInfo::getMid).collect(Collectors.toList()));
        }
        // 插入申请记录表
        for (Long mid : midList) {
            LauMidArchiveAcceptRecordPo lauMidArchiveAcceptRecordPo = new LauMidArchiveAcceptRecordPo();
            lauMidArchiveAcceptRecordPo.setMid(mid);
            lauMidArchiveAcceptRecordPo.setRelationId(authId);
            lauMidArchiveAcceptRecordPo.setSharingScope(bo.getShareScope().getCode());
            lauMidArchiveAcceptRecordPo.setAuthMode(bo.getAuthMode().getCode());
            lauMidArchiveAcceptRecordPo.setRequestType(ArchiveRequestTypeEnum.UPDATE.getCode());
            long reqId = adBqf.insert(lauMidArchiveAcceptRecord).insertGetKey(lauMidArchiveAcceptRecordPo);
            //发送站内信给mid

            String content = getContent(accountId, accountBase, bo.getShareScope());
            String linkUrl = UriComponentsBuilder.fromUriString(GENERAL_ARCHIVE_BIND_SEND_MESSAGE_LINK)
                    .queryParam("auth_id", reqId).build(false).toUriString();
            String linkStr = String.format("#{查看详情}{\"%s\"}", linkUrl);
            try {
                messageManager.sendMessageToMids("视频授权内容变更申请",
                        String.format("您有一条来自%s的视频授权内容变更申请，%s", content, linkStr),
                        Collections.singletonList(mid));
            } catch (ServiceException | SystemException e) {
                log.error("发送站内信失败，mid={}, err={}", mid, e.getMessage());
            }
        }
        addUpdateTime(accountId, avid);
    }

    private boolean diffUpdateProperties(GeneralArchiveUpdateBo bo,
                                         LauArchiveAuthorizedRelationPo lauArchiveAuthorizedRelationPo) {
        if (bo.getShareScope().getCode() != lauArchiveAuthorizedRelationPo.getSharingScope()) {
            return true;
        }
        if (bo.getAuthMode().getCode() != lauArchiveAuthorizedRelationPo.getAuthMode()) {
            return true;
        }
        return false;
    }

    private int queryUpdateTimes(Integer accountId, Long avid) {
        String key = authTimeKeyGen(accountId, avid, "update");
        String i = redisTemplate.opsForValue().get(key);
        return i == null ? 0 : Integer.parseInt(i);
    }

    private void addUpdateTime(Integer accountId, Long avid) {
        String key = authTimeKeyGen(accountId, avid, "update");
        incrAtEndOfWeek(key);
    }

    public GeneralVideoAuthorizeInfoBo info(Long authId, Long mid) {
        LauMidArchiveAcceptRecordPo lauMidArchiveAcceptRecordPo = adBqf.selectFrom(lauMidArchiveAcceptRecord)
                .where(lauMidArchiveAcceptRecord.id.eq(authId)).fetchFirst();
        Assert.notNull(lauMidArchiveAcceptRecordPo, "无效的申请记录");
        Assert.isTrue(mid.equals(lauMidArchiveAcceptRecordPo.getMid()), "无权限查看他人的授权记录");
        Assert.isTrue(lauMidArchiveAcceptRecordPo.getIsCancel().equals(INT_FALSE), "授权请求已撤回");
        Assert.isTrue(lauMidArchiveAcceptRecordPo.getIsDeleted().equals(INT_FALSE), "授权请求已删除");
        ArchiveRequestTypeEnum requestType = ArchiveRequestTypeEnum.getByCode(
                lauMidArchiveAcceptRecordPo.getRequestType());

        LauArchiveAuthorizedRelationPo lauArchiveAuthorizedRelationPo = adBqf.selectFrom(lauArchiveAuthorizedRelation)
                .where(lauArchiveAuthorizedRelation.id.eq(lauMidArchiveAcceptRecordPo.getRelationId()))
                .where(lauArchiveAuthorizedRelation.isDeleted.eq(INT_FALSE)).fetchFirst();
        Assert.notNull(lauArchiveAuthorizedRelationPo, "单据id无效，本次申请流程已结束");

        switch (requestType) {
            case AUTHORIZE:

                // 授权
                Assert.isTrue(lauArchiveAuthorizedRelationPo.getAuthStatus()
                        .equals(ArchiveAuthStatusEnum.WAIT_ACCEPT.getCode()), "授权单据信息已过时");
                break;
            case RENEWAL:
                // 续期
                Assert.isTrue(lauArchiveAuthorizedRelationPo.getRenewalStatus()
                        .equals(ArchiveAuthRenewalStatusEnum.WAIT_ACCEPT.getCode()), "续期单据状态已更新");
                break;
            case UPDATE:
                Assert.isTrue(lauArchiveAuthorizedRelationPo.getUpdateStatus()
                        .equals(ArchiveAuthUpdateStatusEnum.WAIT_ACCEPT.getCode()), "编辑单据状态已更新");
        }
        // 客账信息
        Long accountId = lauArchiveAuthorizedRelationPo.getAccountId();
        AccountBaseReply accountBase = crmAccountServiceBlockingStub.getAccountBase(
                AccountIdReq.newBuilder().setAccountId(Math.toIntExact(accountId)).build());
        String accountName = accountBase.getData().getName();
        int customerId = accountBase.getData().getCustomerId();
        int agentId = accountBase.getData().getDependencyAgentId();
        int productId = accountBase.getData().getProductId();
        ArchiveShareScopeEnum sharingScope = ArchiveShareScopeEnum.getByCode(
                lauMidArchiveAcceptRecordPo.getSharingScope());
        SubNameData subNameData = getSubName(agentId, customerId, productId,
                sharingScope);
        // 稿件信息
        Long avid = lauArchiveAuthorizedRelationPo.getAvid();
        Map<Long, Arc> arcMap = archiveService.arcsMap(Collections.singletonList(avid));
        Assert.notNull(arcMap.get(avid), "稿件信息不存在");
        Arc arc = arcMap.get(avid);
        String cover = StringUtils.isNotEmpty(arc.getPic()) ? arc.getPic().replace("http", "https") : StringUtils.EMPTY;

        List<GeneralVideoMidInfoBo> generalVideoMidInfoBos = new ArrayList<>();
        generalVideoMidInfoBos.add(GeneralVideoMidInfoBo.builder().mid(Math.toIntExact(arc.getAuthor().getMid()))
                .name(arc.getAuthor().getName()).avatar(StringUtils.isNotEmpty(arc.getPic()) ? arc.getPic()
                        .replace("http", "https") : StringUtils.EMPTY).build());
        // up主列表
        if (INT_TRUE == arc.getRights().getIsCooperation()) {
            //mid封装
            for (StaffInfo staff : arc.getStaffInfoList()) {
                long staffMid = staff.getMid();
                ProfileDto profile = profileInfoService.getProfileByMid(staffMid);
                generalVideoMidInfoBos.add(
                        GeneralVideoMidInfoBo.builder().mid(Math.toIntExact(staffMid)).name(profile.getName())
                                .avatar(profile.getFace()).build());
            }
        }

        // 返回值
        return GeneralVideoAuthorizeInfoBo.builder().accountId(accountId).accountName(accountName)
                .customerId(customerId).customerName(subNameData.customerName).agentId(agentId)
                .agentName(subNameData.agentName)
                .productId(productId).productName(subNameData.productName)
                .authMode(CommercialOrderArchiveAuthModeEnum.getByCode(lauMidArchiveAcceptRecordPo.getAuthMode()))
                .shareScope(sharingScope).avid(avid).bvid(BVIDUtils.avToBv(avid)).cover(cover).title(arc.getTitle())
                .generalVideoMidInfoBoList(generalVideoMidInfoBos).authTimeType(
                        CommercialOrderArchiveTimeLimitEnum.getByCode(lauArchiveAuthorizedRelationPo.getAuthTimeType()))
                .authStartTime(lauArchiveAuthorizedRelationPo.getAuthStartTime())
                .authEndTime(lauArchiveAuthorizedRelationPo.getAuthEndTime())
                .requestType(ArchiveRequestTypeEnum.getByCode(lauMidArchiveAcceptRecordPo.getRequestType()))
                .renewalEndTime(lauMidArchiveAcceptRecordPo.getAuthEndTime()).build();
    }

    public void reject(Long authId, Long mid) {
        AuthorizeCtx result = validAuth(authId, mid);

        // 修改状态
        adBqf.update(lauMidArchiveAcceptRecord).where(lauMidArchiveAcceptRecord.id.eq(authId))
                .where(lauMidArchiveAcceptRecord.status.eq(ArchiveAuthReplyStatus.INIT.getCode()))
                .set(lauMidArchiveAcceptRecord.status, ArchiveAuthReplyStatus.REJECT.getCode()).execute();
        // 设置单据失效
        adBqf.update(lauMidArchiveAcceptRecord)
                .where(lauMidArchiveAcceptRecord.relationId.eq(result.lauMidArchiveAcceptRecordPo.getRelationId()))
                .where(lauMidArchiveAcceptRecord.requestType.eq(result.requestType.getCode()))
                .set(lauMidArchiveAcceptRecord.isDeleted, INT_TRUE).execute();

        // 流程中止，状态流转
        switch (result.requestType) {
            case AUTHORIZE:
                result.lauArchiveAuthorizedRelationPo.setAuthStatus(ArchiveAuthStatusEnum.DISMISSED.getCode());
                break;
            case RENEWAL:
                result.lauArchiveAuthorizedRelationPo.setRenewalStatus(
                        ArchiveAuthRenewalStatusEnum.DISMISSED.getCode());
                break;
            case UPDATE:
                result.lauArchiveAuthorizedRelationPo.setUpdateStatus(ArchiveAuthUpdateStatusEnum.DISMISSED.getCode());
                break;
        }
        adBqf.update(lauArchiveAuthorizedRelation).updateBean(result.lauArchiveAuthorizedRelationPo);
    }

    private AuthorizeCtx validAuth(Long authId, Long mid) {
        LauMidArchiveAcceptRecordPo lauMidArchiveAcceptRecordPo = adBqf.selectFrom(lauMidArchiveAcceptRecord)
                .where(lauMidArchiveAcceptRecord.id.eq(authId)).where(lauMidArchiveAcceptRecord.isDeleted.eq(INT_FALSE))
                .fetchFirst();
        Assert.notNull(lauMidArchiveAcceptRecordPo, "无效单据");
        Assert.isTrue(lauMidArchiveAcceptRecordPo.getMid().equals(mid), "非up主本人不能操作此单据");
        Assert.isTrue(lauMidArchiveAcceptRecordPo.getIsCancel().equals(INT_FALSE), "授权请求已撤回");
        Assert.isTrue(lauMidArchiveAcceptRecordPo.getStatus().equals(ArchiveAuthReplyStatus.INIT.getCode()),
                "重复操作");
        ArchiveRequestTypeEnum requestType = ArchiveRequestTypeEnum.getByCode(
                lauMidArchiveAcceptRecordPo.getRequestType());

        LauArchiveAuthorizedRelationPo lauArchiveAuthorizedRelationPo = adBqf.selectFrom(lauArchiveAuthorizedRelation)
                .where(lauArchiveAuthorizedRelation.id.eq(lauMidArchiveAcceptRecordPo.getRelationId()))
                .where(lauArchiveAuthorizedRelation.isDeleted.eq(0)).fetchFirst();
        Assert.notNull(lauArchiveAuthorizedRelationPo, "授权关系已删除");


        return new AuthorizeCtx(lauMidArchiveAcceptRecordPo, requestType, lauArchiveAuthorizedRelationPo);
    }

    public void accept(Long authId, Long mid) {
        AuthorizeCtx result = validAuth(authId, mid);

        // 修改状态
        adBqf.update(lauMidArchiveAcceptRecord).where(lauMidArchiveAcceptRecord.id.eq(authId))
                .where(lauMidArchiveAcceptRecord.status.eq(ArchiveAuthReplyStatus.INIT.getCode()))
                .set(lauMidArchiveAcceptRecord.status, ArchiveAuthReplyStatus.ACCEPT.getCode()).execute();

        List<LauMidArchiveAcceptRecordPo> acceptRecordPos = adBqf.selectFrom(lauMidArchiveAcceptRecord)
                .where(lauMidArchiveAcceptRecord.relationId.eq(result.lauMidArchiveAcceptRecordPo.getRelationId()))
                .where(lauMidArchiveAcceptRecord.requestType.eq(result.requestType.getCode()))
                .where(lauMidArchiveAcceptRecord.isDeleted.eq(INT_FALSE)).fetch();
        // 通过申请的up主数量
        long count = acceptRecordPos.stream()
                .filter(po -> po.getStatus().equals(ArchiveAuthReplyStatus.ACCEPT.getCode())).count();
        if (count != acceptRecordPos.size()) {
            return;
        }
        // 只有当所有作者都同意了才变更状态
        switch (result.requestType) {
            case AUTHORIZE:
                result.lauArchiveAuthorizedRelationPo.setAuthStatus(ArchiveAuthStatusEnum.WAIT_EFFECT.getCode());
                break;
            case RENEWAL:
                result.lauArchiveAuthorizedRelationPo.setRenewalStatus(ArchiveAuthRenewalStatusEnum.CONFIRM.getCode());
                result.lauArchiveAuthorizedRelationPo.setAuthEndTime(
                        result.lauMidArchiveAcceptRecordPo.getAuthEndTime());
                break;
            case UPDATE:
                result.lauArchiveAuthorizedRelationPo.setUpdateStatus(ArchiveAuthUpdateStatusEnum.CONFIRM.getCode());
                result.lauArchiveAuthorizedRelationPo.setAuthMode(result.lauMidArchiveAcceptRecordPo.getAuthMode());
                result.lauArchiveAuthorizedRelationPo.setSharingScope(
                        result.lauMidArchiveAcceptRecordPo.getSharingScope());
                // 客户、品牌、代理
                AccountBaseReply accountBase = crmAccountServiceBlockingStub.getAccountBase(AccountIdReq.newBuilder()
                        .setAccountId(Math.toIntExact(result.lauArchiveAuthorizedRelationPo.getAccountId())).build());
                int customerId = accountBase.getData().getCustomerId();
                int dependencyAgentId = accountBase.getData().getDependencyAgentId();
                int productId = accountBase.getData().getProductId();
                setRelationByScope(
                        ArchiveShareScopeEnum.getByCode(result.lauMidArchiveAcceptRecordPo.getSharingScope()),
                        result.lauArchiveAuthorizedRelationPo, dependencyAgentId, customerId, productId);

                break;
        }
        adBqf.update(lauArchiveAuthorizedRelation).updateBean(result.lauArchiveAuthorizedRelationPo);

        // 删除已通过的单据
        adBqf.update(lauMidArchiveAcceptRecord)
                .where(lauMidArchiveAcceptRecord.relationId.eq(result.lauMidArchiveAcceptRecordPo.getRelationId()))
                .where(lauMidArchiveAcceptRecord.requestType.eq(result.requestType.getCode()))
                .set(lauMidArchiveAcceptRecord.isDeleted, INT_TRUE).execute();
    }

    public void rpcBind(SanlianGeneralArchiveRpcBo.GeneralArchiveBindBo bo) throws ServiceException {
        long startTime = System.currentTimeMillis();
        //将bo.getAccountIds()转为List<Integer>
        List<Long> accountIdsLon = bo.getAccountId().stream().distinct().collect(Collectors.toList());
        List<Integer> accountIdsInt = bo.getAccountId().stream().map(Long::intValue).distinct().collect(Collectors.toList());
        long avid = bo.getAvid();
        String title = bo.getTitle();
        long authEndTime = bo.getAuthEndTime();
        long sharingScope = bo.getSharingScope();
        Integer source = bo.getSource();
        ArchiveAuthTypeEnum sourceEnum = ArchiveAuthTypeEnum.getByCode(source);
        ArchiveShareScopeEnum sharingScopeEnum = ArchiveShareScopeEnum.getByCode((int) sharingScope);
        CommercialOrderAuthTimeBo authTimeBo = new CommercialOrderAuthTimeBo();
        if (Utils.isPositive(authEndTime)) {
            authTimeBo.setTimeLimitType(CommercialOrderArchiveTimeLimitEnum.TIME);
            authTimeBo.setAuthBeginTime(startTime);
            authTimeBo.setAuthEndTime(authEndTime);
        }

        authTimeBo.setTimeLimitType(CommercialOrderArchiveTimeLimitEnum.UNLIMITED);

        //来源校验
        if (ArchiveAuthTypeEnum.UNKNOWN.equals(sourceEnum)) {
            throw new ServiceException(SanlianGeneralRpcConstants.ERR_PARAM, "source invalid :" + source);
        }
        //账户校验
        //如果账户不对应，抛异常
        BatchAccountLabelsReply batchAccountLabelsReply = crmAccountServiceBlockingStub.batchGetAccountLabels(BatchAccountLabelsReq.newBuilder().addAllAccountId(accountIdsInt).build());
        Map<Integer, List<Integer>> accountLabels = batchAccountLabelsReply.getDataList().stream().collect(Collectors.toMap(AccountLabelDto::getAccountId, AccountLabelDto::getLabelIdsList));
        for (Integer account : accountIdsInt) {
            List<Integer> labels = accountLabels.getOrDefault(account, Collections.emptyList());
            Assert.isTrue(labels.contains(accountConfig.getPugvLabelId()), "account invalid" + account);
        }

        Assert.isTrue(Utils.isPositive(avid), "avid invalid" + avid);

        // 稿件信息
        Map<Long, Arc> arcMap = archiveService.arcsMap(Collections.singletonList(avid));
        Assert.notNull(arcMap.getOrDefault(avid, null), "avid invalid" + avid);


        //校验时间
        mustTimeInfoLegal(authTimeBo);

        //校验花火
        mustNotCommercialArchive(avid);

        Arc arc = arcMap.get(avid);
        long mid = arc.getAuthor().getMid();

        if (Objects.requireNonNull(sourceEnum) == ArchiveAuthTypeEnum.PUGV) {
            int attribute = arc.getAttribute();
            Boolean isPugv = PartnerArchiveConverter.ATTRIBUTE_BI_FUNCTION.apply(attribute, ArchiveAttributeEnum.IS_PUGV.getIndex());
            if (!isPugv) {
                throw new ServiceException(SanlianGeneralRpcConstants.ERR_SOURCE_ARCHIVE, "source avid invalid source:" + source + "avid:" + avid);
            }
        }

        //对课堂定制化逻辑，稿件与账户一对一关系
        List<LauArchiveAuthorizedRelationPo> currentPugvAuthPos = adBqf.selectFrom(lauArchiveAuthorizedRelation)
                .where(lauArchiveAuthorizedRelation.avid.eq(avid))
                .where(lauArchiveAuthorizedRelation.isDeleted.eq(INT_FALSE))
                .where(lauArchiveAuthorizedRelation.accountId.in(accountIdsLon))
                .fetch();
        //构成map,key是accountid，value是主键id
        Map<Long, Long> currentPugvAuthPosMap = currentPugvAuthPos.stream().collect(Collectors.toMap(LauArchiveAuthorizedRelationPo::getAccountId, LauArchiveAuthorizedRelationPo::getId));

        List<Long> currentPugvAuthAccounts = currentPugvAuthPos.stream().map(LauArchiveAuthorizedRelationPo::getAccountId).collect(Collectors.toList());

        //对比currentPugvAuthAccounts与accountIdsLon中的差异
        List<Long> insertAccountIds = accountIdsLon.stream().filter(accountId -> !currentPugvAuthAccounts.contains(accountId)).collect(Collectors.toList());

        //先创建一个模版
        LauArchiveAuthorizedRelationPo templatePo = new LauArchiveAuthorizedRelationPo();
        templatePo.setAvid(avid);
        templatePo.setCustomerId(0);
        templatePo.setAgentId(0);
        templatePo.setProductId(0);
        templatePo.setAuthMode(sourceEnum.getCode());
        templatePo.setAuthSource(ArchiveAuthSourceEnum.RPC_PUSH.getCode());
        templatePo.setAuthStatus(ArchiveAuthStatusEnum.AUTHORIZED.getCode());
        templatePo.setAuthStartTime(TimeUtils.nowTimestamp());
        templatePo.setAuthEndTime(
                Timestamp.valueOf(LocalDate.now().plusYears(100).atTime(LocalTime.MIDNIGHT)));
        templatePo.setAuthTimeType(CommercialOrderArchiveTimeLimitEnum.UNLIMITED.getCode());
        templatePo.setSharingScope(sharingScopeEnum.getCode());
        templatePo.setMid(mid);
        templatePo.setArchiveType(sourceEnum.getCode());
        templatePo.setArchiveTitle(title);
        templatePo.setCtime(TimeUtils.nowTimestamp());
        templatePo.setMtime(TimeUtils.nowTimestamp());
        templatePo.setIsDeleted(INT_FALSE);

        //构建insertpo与updatepo
        List<LauArchiveAuthorizedRelationPo> insertPos = insertAccountIds.stream().map(accountId -> {
            LauArchiveAuthorizedRelationPo po = new LauArchiveAuthorizedRelationPo();
            BeanUtils.copyProperties(templatePo, po);
            po.setAccountId(accountId);
            return po;
        }).collect(Collectors.toList());

        //批量新增
        adBqf.insert(lauArchiveAuthorizedRelation).insertBeans(insertPos);
    }


    public void refreshAuthStatus() {
        Timestamp currentTime = Utils.getNow();
        Long lastMaxId = 0L;
        int batchSize = 1000;

        while (true) {
            List<LauArchiveAuthorizedRelationPo> lauArchiveAuthorizedRelationPos = adBqf.selectFrom(
                            lauArchiveAuthorizedRelation).where(lauArchiveAuthorizedRelation.id.gt(lastMaxId)
                            .and(lauArchiveAuthorizedRelation.authStatus.in(ArchiveAuthStatusEnum.REFRESHABLE_CODE_LIST))
                            .and(lauArchiveAuthorizedRelation.isDeleted.eq(INT_FALSE)))
                    .orderBy(lauArchiveAuthorizedRelation.id.asc()).limit(batchSize).fetch();

            if (CollectionUtils.isEmpty(lauArchiveAuthorizedRelationPos)) {
                break;
            }
            lastMaxId = lauArchiveAuthorizedRelationPos.get(lauArchiveAuthorizedRelationPos.size() - 1).getId();

            for (LauArchiveAuthorizedRelationPo po : lauArchiveAuthorizedRelationPos) {

                try {
                    Integer authStatus = po.getAuthStatus();

                    //视频待生效刷新
                    if (ArchiveAuthStatusEnum.WAIT_EFFECT.getCode().equals(authStatus)) {
                        if (currentTime.after(po.getAuthStartTime())) {
                            adBqf.update(lauArchiveAuthorizedRelation).set(lauArchiveAuthorizedRelation.authStatus,
                                            ArchiveAuthStatusEnum.AUTHORIZED.getCode())
                                    .where(lauArchiveAuthorizedRelation.id.eq(po.getId())).execute();
                        }
                    }

                    //视频过期下线
                    if (ArchiveAuthStatusEnum.AUTHORIZED.getCode().equals(authStatus)) {
                        if (currentTime.after(po.getAuthEndTime())) {
                            adBqf.update(lauArchiveAuthorizedRelation).set(lauArchiveAuthorizedRelation.authStatus,
                                            ArchiveAuthStatusEnum.LOSE_EFFECT.getCode())
                                    .where(lauArchiveAuthorizedRelation.id.eq(po.getId())).execute();
                        }

                    }
                } catch (Exception e) {
                    log.info("error", e);
                }
            }
        }
    }

    @AllArgsConstructor
    private static class AuthorizeCtx {
        public final LauMidArchiveAcceptRecordPo lauMidArchiveAcceptRecordPo;
        public final ArchiveRequestTypeEnum requestType;
        public final LauArchiveAuthorizedRelationPo lauArchiveAuthorizedRelationPo;
    }

    public void updateAuthArchiveTitle(
            Long avid,
            String title
    ) {
        List<LauArchiveAuthorizedRelationPo> authArcs = adBqf.selectFrom(lauArchiveAuthorizedRelation)
                .where(lauArchiveAuthorizedRelation.avid.eq(avid))
                .fetch();
        if (CollectionUtils.isEmpty(authArcs)) {
            log.info("课堂稿件AuthArcChangeSub updateAuthArchiveTitle pos=null avid={}", avid);
            return;
        }
        adBqf.update(lauArchiveAuthorizedRelation)
                .set(lauArchiveAuthorizedRelation.archiveTitle, title)
                .where(lauArchiveAuthorizedRelation.avid.eq(avid))
                .execute();
    }

    public void updateAuthArchiveStatus(
            Long avid,
            Integer preStatus,
            Integer toStatus
    ) {
        //授权记录
        List<LauArchiveAuthorizedRelationPo> authArcs = adBqf.selectFrom(lauArchiveAuthorizedRelation)
                .where(lauArchiveAuthorizedRelation.avid.eq(avid))
                .where(lauArchiveAuthorizedRelation.authStatus.eq(preStatus))
                .fetch();

        if (CollectionUtils.isEmpty(authArcs)) {
            log.info("课堂稿件AuthArcChangeSub updateAuthArchiveStatus auth  is null avid={}", avid);
            return;
        }

        adBqf.update(lauArchiveAuthorizedRelation)
                .set(lauArchiveAuthorizedRelation.authStatus, toStatus)
                .where(lauArchiveAuthorizedRelation.avid.eq(avid))
                .execute();
    }


    public void updateLoseEffect(
            Long avid
    ) {
        List<LauCreativeArchivePo> creativeArchivePos = adCoreBqf.selectFrom(lauCreativeArchive)
                .where(lauCreativeArchive.avid.eq(avid))
                .fetch();
        if (CollectionUtils.isEmpty(creativeArchivePos)) {
            log.info("课堂稿件AuthArcChangeSub updateLoseEffect creativeArchivePos is null avid={}", avid);
            return;
        }

        List<Integer> rejectCreativeIds = creativeArchivePos.stream().map(LauCreativeArchivePo::getCreativeId).collect(Collectors.toList());
        List<LauUnitCreativePo> creatives = adCoreBqf.from(lauUnitCreative)
                .where(lauUnitCreative.status.in(LaunchStatus.START.getCode(), LaunchStatus.STOP.getCode()))
                .where(lauUnitCreative.auditStatus.ne(AuditStatus.REJECT.getCode()))
                .where(lauUnitCreative.creativeId.in(rejectCreativeIds))
                .select(lauUnitCreative.creativeId, lauUnitCreative.version)
                .fetch(LauUnitCreativePo.class);
        if (CollectionUtils.isEmpty(creatives)) {
            log.info("课堂稿件AuthArcChangeSub updateLoseEffect reject creatives is null avid={},creativeIds={}", avid, rejectCreativeIds);
            return;
        }

        log.info("课堂稿件下架下线创意: {}", creatives.stream().map(LauUnitCreativePo::getCreativeId).collect(Collectors.toList()));
        for (LauUnitCreativePo creative : creatives) {
            try {
                launchCreativeService.auditReject(CpcCreativeAuditDto.builder()
                        .creativeId(creative.getCreativeId())
                        .reason(REJECT_REASON_INVALID_PUGC)
                        .version(creative.getVersion())
                        .operator(Operator.SYSTEM)
                        .reCheckFlag(IsValid.FALSE.getCode())
                        .build());
            } catch (Exception e) {
                log.error("课堂稿件下线创意失败,avid={},creativeId={}", avid, creative.getCreativeId(), e);
            }
        }
    }
}
