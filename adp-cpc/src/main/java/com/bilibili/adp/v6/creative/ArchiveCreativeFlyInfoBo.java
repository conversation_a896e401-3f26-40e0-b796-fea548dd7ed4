package com.bilibili.adp.v6.creative;

import com.bilibili.adp.cpc.core.constants.BusinessDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @ClassName ArchiveCreativeFlyInfoBo
 * <AUTHOR>
 * @Date 2025/4/15 7:57 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ArchiveCreativeFlyInfoBo {
    private Long avid;
    private Boolean isFly;
    private Boolean isBusinessFly;
    private Boolean isContentFly;

    public static ArchiveCreativeFlyInfoBo generateInstanceByBusinessDomain(Long avid, Integer businessDomain) {
        boolean isBusinessFly = Objects.equals(BusinessDomain.BUSINESS_FLY, businessDomain);
        boolean isContentFly = Objects.equals(BusinessDomain.CONTENT_FLY, businessDomain);
        boolean isFly = isBusinessFly || isContentFly;
        return ArchiveCreativeFlyInfoBo.builder()
                .avid(avid)
                .isFly(isFly)
                .isBusinessFly(isBusinessFly)
                .isContentFly(isContentFly)
                .build();
    }


}
