CREATE TABLE `mgk_agreement_permission`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `agreement_id`   bigint(20)          DEFAULT 0 not null comment '协议id',
    `permission_key` varchar(200)        default '' not null comment '用户的授权粒度, 每个agreement对应的permissionKey的组织方式会不一样',
    `agreement_name` varchar(200)        default '' not null comment '协议名称',
    `account_id`     bigint(20)          DEFAULT 0 not null comment '账户id',
    `agreed`         tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否同意过',
    `agree_time`     datetime   NOT NULL DEFAULT current_timestamp() COMMENT '授权时间',
    `ctime`          datetime   NOT NULL DEFAULT current_timestamp() COMMENT '发表时间',
    `mtime`          datetime   NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`        tinyint(4) NOT NULL DEFAULT 0 COMMENT '删除标记',
    PRIMARY KEY (`id`),
    KEY `idx_account_id` (`account_id`) USING BTREE,
    KEY `idx_permission_key` (`permission_key`) USING BTREE,
    UNIQUE KEY `idx_agreement_id_key` (`agreement_id`, `permission_key`) USING BTREE,
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='协议授权表';

