CREATE TABLE `mgk_material_inspiration_case`
(
    `id`                            bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增Id',

    # 基础信息
    `case_id`                       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '文章id',
    `material_id`                   varchar(128)        NOT NULL DEFAULT '' COMMENT '素材id 案例本身作为素材',
    `account_id`                    bigint(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户Id',
    `creator`                       varchar(45)         NOT NULL DEFAULT '' COMMENT '用户名称',
    `title`                         varchar(128)        NOT NULL DEFAULT '' COMMENT '标题',
    `industry_id`                   varchar(45)         NOT NULL DEFAULT '' COMMENT '行业名称',
    `industry_name`                 varchar(45)         NOT NULL DEFAULT '' COMMENT '行业名称',
    `commerce_first_category_id`    int(11)             NOT NULL DEFAULT '0' COMMENT '一级行业',
    `commerce_first_category_name`  varchar(100)        NOT NULL DEFAULT '' COMMENT '一级行业',
    `commerce_second_category_id`   int(11)             NOT NULL DEFAULT '0' COMMENT '二级行业',
    `commerce_second_category_name` varchar(100)        NOT NULL DEFAULT '' COMMENT '二级行业',
    `daihuo_first_category_name`    varchar(100)        NOT NULL DEFAULT '' COMMENT '带货一级类目',
    `daihuo_second_category_name`   varchar(100)        NOT NULL DEFAULT '' COMMENT '带货二级类目',
    `promotion_purpose_type`        varchar(50)         NOT NULL DEFAULT '' COMMENT '推广目的名称',
    `delivery_time`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '投放日期',

    `arch_type`          varchar(50)         NOT NULL DEFAULT '' COMMENT '稿件类型',
    `is_vertical_screen` tinyint(4)          NOT NULL DEFAULT 0 COMMENT '是否竖屏，默认false横屏',


    # 案例数据
    `play`               bigint(20)          NOT NULL DEFAULT '0' COMMENT '播放',
    `click`              bigint(20)          NOT NULL DEFAULT '0' COMMENT '点击量',
    `ctr`                varchar(20)         NOT NULL DEFAULT '0' COMMENT '点击率',
    `ctr_rank`           varchar(20)         NOT NULL DEFAULT '' COMMENT '点击率等级',
    `cvr`                varchar(20)         NOT NULL DEFAULT '0' COMMENT '点击转化率',
    `cvr_rank`           varchar(20)         NOT NULL DEFAULT '' COMMENT '点击转化率等级',
    `ctcvr`              varchar(20)         NOT NULL DEFAULT '0' COMMENT '曝光转化率',
    `ctcvr_rank`         varchar(20)         NOT NULL DEFAULT '' COMMENT '曝光转化率等级',
    `incr_play`          varchar(20)         NOT NULL DEFAULT '0' COMMENT '原生增量播放量 相对涨幅，如+50%',
    `incr_conv`          varchar(20)         NOT NULL DEFAULT '0' COMMENT '原生增量转化量 相对涨幅，如+50%',


    # 案例内容
    `content`            text COMMENT '案例内容json,包括内容、痛点、亮点、手法、画面分析等',
    `cover`              varchar(128)        NOT NULL DEFAULT '' COMMENT '封面地址',

    # 案例统计数据保留
    `article_read`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '阅读数',
    `article_like`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '点赞数',
    `article_status`     int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '状态 0-启用 1-禁用',
    `audit_status`       int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '状态 0-默认 1-审核通过 2-审核不通过',
    `deleted`            int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '是否删除 0-正常 1-删除',
    `ctime`              datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `mtime`              datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',


    PRIMARY KEY (`id`),
    KEY `ix_case` (`case_id`),
    KEY `ix_audit` (`audit_status`),
    KEY `ix_industry_name` (`industry_name`),
    KEY `ix_ctime` (`ctime`),
    KEY `ix_delivery_time` (`delivery_time`),
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='创意灵感案例'