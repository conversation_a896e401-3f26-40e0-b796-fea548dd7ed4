syntax = "proto3";
package hermers.interface.v1;
import "extension/wdcli/wdcli.proto";
import "hermers/common/task.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/hermers/interface;v1";
option java_package = "com.bapis.hermers.interfaces";
option java_multiple_files = true;

option (wdcli.appid) = "main.hermers-interface.hermers-biz";

service HermesInterface {
  rpc Run(RunReq) returns (RunResp);
  rpc RegisterRuleExp(RegisterRuleExpReq) returns (RegisterRuleExpRsp);
  rpc EditRuleExp(EditRuleExpReq) returns (RegisterRuleExpRsp);
  rpc QueryRuleExps(QueryRuleExpsReq) returns (QueryRuleExpsRsp);
}

message RunReq {
  int64 flow_id = 1;
  string data = 2;
  bool test = 3;
}

message RunResp {
  int64 flow_id = 1;
  string data = 2;
}

message RegisterRuleExpReq {
  string exp = 1;
  string comment = 2;
}

message RegisterRuleExpRsp {
  int64 id = 1;
  string exp = 2;
}

message EditRuleExpReq {
  int64 id = 1;
  string exp = 2;
  string comment = 3;
}

message QueryRuleExpsReq {
  repeated int64 ids = 1;
}

message QueryRuleExpsRsp {
  map<int64,.hermes.task.common.RuleExp> exps = 1;
}