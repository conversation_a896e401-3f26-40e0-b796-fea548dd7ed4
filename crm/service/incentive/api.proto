syntax = "proto3";
package growup.service.v1;

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option (wdcli.appid) = "studio.service.growup";
option go_package = "buf.bilibili.co/bapis/bapis-gen/crm/service.incentive;api";
option java_package = "com.bapis.crm.service.incentive";
option java_multiple_files = true;
option (gogoproto.goproto_getters_all) = true;

// 激励相关
service Incentive {
  // 余额
  rpc AccInfo (MIDArg) returns (AccInfoResponse) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/acc/customer/info"};
  }
  // UP主收入更新到了哪一天
  rpc IncomeUpDate (MIDArg) returns (UpIncomeDateResp) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/income/up/date"};
  }
  // up主收入最新一天收入和历史累计收入查询
  rpc IncomeUpSummary (IncomeUpSummaryArg) returns (IncomeUpSummaryReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/income/up/summary"};
  }
  // up主起飞收入最新一天收入和历史累计收入查询
  rpc FlyIncomeUpSummary (MIDArg) returns (IncomeUpSummaryReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/fly/income/up/summary"};
  }
  // 某段时间内的起飞收入每天的收入查询
  rpc FlyIncomeUpDaily(UpIncomeRangeReq) returns (UpDailyIncomeResp) {
    option (google.api.http) = {get:"/x/internal/growup-service/fly/income/up/daily"};
  }
  // 某段时间内的总收入查询
  rpc IncomeUpTotal(UpIncomeRangeReq) returns (UpTotalIncomeResp) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/income/up/total"};
  }
  // 某段时间内的每天的收入查询
  rpc IncomeUpDaily(UpIncomeRangeReq) returns (UpDailyIncomeResp) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/income/up/daily"};
  }
  // 某天的模板收入详情查询
  rpc TemplateIncomeDetail(TemplateIncomeDetailReq) returns (TemplateIncomeDetailResp) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/income/template/detail"};
  }
  // 初始化钱包
  rpc AccCreate (WalletCreateArg) returns (WalletCreateResponse) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/wallet/create"};
  }
  // 支付交易单 - 创建
  rpc TradeCreate (TradeCreateArg) returns (TradeCreateReply) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/trade/create"};
  }
  // 支付交易单 - 查询交易单信息
  rpc TradeDetail (TradeDetailArg) returns (TradeDetailReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/trade/detail"};
  }
  // 支付交易单 - 发起退款
  rpc TradeRefund (TradeRefundArg) returns (TradeRefundReply) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/trade/refund"};
  }
  // 支付交易单 - 发起支付
  rpc TradePay (TradePayArg) returns (TradePayReply) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/trade/pay"};
  }
  // 支付交易单 - 查询退款信息
  rpc TradeRefundInfo (RefundInfoArg) returns (RefundInfoReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/trade/refund/info"};
  }
  // 转账
  rpc FundTransfer (FundTransferArg) returns (FundTransferReply) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/fund/transfer"};
  }
  rpc PayTransferInfo (PayTransferInfoArg) returns (PayTransferInfoReply) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/transfer/info"};
  }
  rpc AccVerify (AccVerifyArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/acc/wallet/verify"};
  }
  // 签约模块 - 查询签约信息 - 所有 toC 的针对个人信息的查询都走这里
  rpc UpSignInfo (UpSignInfoArg) returns (UpSignInfoReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/acc/sign/info"};
  }
  rpc UpSignBlockInfo(MidArgReq) returns (UpSignBlockInfoReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/acc/sign/block_info"};
  }
  rpc UpSignQualified (UpSignQualifiedArg) returns (UpSignQualifiedReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/acc/sign/qualified"};
  }
  // 签约模块 - 发起申请
  rpc UpSignApply (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/sign/apply"};
  };
  // 签约模块 - 添加用户
  rpc UpSignAdd (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/sign/add"};
  };
  // 签约模块 - 申请通过
  rpc UpSignPass (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/sign/pass"};
  };
  // 签约模块 - 申请拒绝
  rpc UpSignReject (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/sign/reject"};
  };
  // 签约模块 - 帐号封禁
  rpc UpSignForbid (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/sign/forbid"};
  };
  // 签约模块 - 被动退出（清退）
  rpc UpSignDismiss (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/sign/dismiss"};
  };
  // 签约模块 - 主动退出
  rpc UpSignQuit (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/sign/quit"};
  };
  // 签约模块 - 恢复用户签约状态
  rpc UpSignRecovery (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/sign/recovery"};
  };
  // 签约模块 - 删除用户信息
  rpc UpSignDelete (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/sign/delete"};
  };
  // 签约模块 - 修改用户签约状态
  rpc UpSignState (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/sign/state"};
  };
  // 签约模块-查询用户封禁、签约、黑名单状态
  rpc UpSignAbnormal (MidArgReq) returns (UpSignAbnormalReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/acc/sign/abnormal"};
  }
  // 删除用户
  rpc UpSignAccDelete (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/delete"};
  };
  // 恢复用户信息
  rpc UpSignRecInfo (UpSignOpArg) returns (.google.protobuf.Empty) {
    option (google.api.http) = {post:"/x/internal/growup-service/incentive/acc/recovery_info"};
  };
  // 面向C端用户 判断是否营销号 视频低质等级1的不算
  rpc IsTrash (IsTrashArg) returns (IsTrashReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/is_trash"};
  };
  // up主负向管控判断
  rpc CheckUpNegative (CheckUpNegativeReq) returns (CheckUpNegativeResp){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/is_negative"};
  }
  // 查询up主在活动期间是否是作弊UP、机器人投稿(左右都是闭区间)
  rpc CheckIsCheatUp (CheckIsCheatUpReq) returns (CheckIsCheatUpResp){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/is_cheat_up"};
  }
  // 查询作弊名单、机器人名单
  rpc GetCheatUpList (GetCheatUpListReq) returns (GetCheatUpListResp){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/cheat_up_list"};
  }

  // 激励首页-先声激励icon
  rpc EvpIconIsShow (MidArgReq) returns (EvpIconIsShowReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/evp_icon_is_show"};
  }
  // 先声激励邀请卡是否显示
  rpc EvpInviteCardIsShow (MidArgReq) returns (EvpInviteCardIsShowReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/evp_invite_card_is_show"};
  }
  // 活动金时间段内的每日收益信息
  rpc OfflineIncome (OfflineIncomeArg) returns (OfflineIncomeReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/offline/income"};
  }
  // 活动金时间段内的总收益信息
  rpc OfflineIncomeTotal(OfflineIncomeArg) returns (OfflineIncomeTotalReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/offline/income/total"};
  }
  // 活动金时间段内的收益明细信息
  rpc OfflineIncomeDetail(OfflineIncomeDetailArg) returns (OfflineIncomeDetailReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/offline/income/detail"};
  }

  // 激励广告合并 - 查询用户激励升级信息
  rpc UpGrowPlusInfos (UpGrowPlusInfosReq) returns (UpGrowPlusInfosResp) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/plus/info"};
  }
  // 激励广告合并 - 查询用户广告权限接口，缓冲期结束后用到
  rpc UpAdStateInfo (UpAdStateInfoReq) returns (UpAdStateInfoResp) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/ad/info"};
  }
  // 激励广告合并 - 获取激励Plus缓冲期时间接口
  rpc GetPlusBufTime(.google.protobuf.Empty)  returns (GetPlusBufTimeResp) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/plus/buf_time"};
  }
  // UpDangerousPool 分页获取高危up主池内数据列表的接口
  rpc UpDangerousPools(UpDangerousPoolReq) returns (UpDangerousPoolListReply){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/danger/effect"};
  };
  // IsNeedSetAdIndustry 是否需要设置广告行业
  rpc IsNeedSetAdIndustry(IsNeedSetAdIndustryReq)  returns (IsNeedSetAdIndustryReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/plus/is/need/set_ad_industry"};
  }

  // 先富激励独家任务
  // 获取声明失效的稿件列表
  rpc GetFriSoleArchiveInvalidList(GetFriSoleArchiveInvalidListReq) returns (GetFriSoleArchiveInvalidListReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/fri/sole/archive/invalid/list"};
  }
  // 批量获取声明成功的稿件
  rpc BatchGetFriSoleArchiveValidList(BatchGetFriSoleArchiveValidListReq) returns (BatchGetFriSoleArchiveValidListReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/fri/sole/archive/valid/list/batch"};
  }
  // 批量获取声明失败的稿件
  rpc BatchGetFriSoleArchiveInValidList(BatchGetFriSoleArchiveInvalidListReq) returns (BatchGetFriSoleArchiveInvalidListReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/fri/sole/archive/invalid/list/batch"};
  }
  // 获取声明成功的稿件列表
  rpc GetFriSoleArchiveValidList(GetFriSoleArchiveValidListReq) returns (GetFriSoleArchiveValidListReply) {
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/fri/sole/archive/valid/list"};
  }
  // 取消声明
  rpc CancelFriSoleArchive(CancelFriSoleArchiveReq)returns(.google.protobuf.Empty){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/fri/sole/archive/cancel"};
  }
  // 获取mid指定日期的独家加权系数
  rpc GetFriSoleRatiosByDate(GetFriSoleRatiosByDateReq)returns(GetFriSoleRatesByDateReply){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/fri/sole/ratios"};
  }
  // 查询Avid某日收益
  rpc GetFriSoleArchiveDayIncomesByDate(GetFriSoleArchiveDayIncomesByDateReq)returns(GetFriSoleArchiveDayIncomesByDateReply){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/fri/sole/archive/dayIncomes"};
  }
  // 查询Avid某个广告分成收益
  rpc GetArchiveDayTrustAdIncomesByDate(GetArchiveDayTrustAdIncomesByDateReq)returns
      (GetArchiveDayTrustAdIncomesByDateReply){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/archive/trust-ad/dayIncomes"};
  }
  // 查询某日任务是否完成
  rpc GetTaskStatusByCdate(GetTaskStatusByCdateReq)returns(GetTaskStatusByCdateReply){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/task/status"};
  }
  // 批量查询Avid截止某日收益
  rpc BatchGetArchiveIncomeByDate(BatchGetArchiveIncomeByDateReq)returns(BatchGetArchiveIncomeByDateReply){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/archive/income/batch"};
  }
  // 获取白皮书信息
  rpc GetAllWhiteSkinBookConfigs(GetAllWhiteSkinBookConfigsReq)returns(GetAllWhiteSkinBookConfigsReply){
    option (google.api.http) = {get:"/x/internal/growup-service/incentive/white-skin-book/list"};
  }

  // 起飞改版联动
  rpc GetFlyWalletUpgradeSwitch(.google.protobuf.Empty)returns(GetFlyWalletUpgradeSwitchReply){
    option (google.api.http) = {get:"/x/internal/growup-service/fly/wallet/upgrade/switch"};
  }
}

message GetAllWhiteSkinBookConfigsReq{
  // 如果不传或传0，则表示查询全部
  BizTypeEnum biz_type = 1 [(gogoproto.jsontag) = "biz_type", json_name = "biz_type"] ;
  // 权益类型
  ShowEquityEnum equity_type = 2 [(gogoproto.jsontag) = "equity_type", json_name = "equity_type"] ;
}

enum ShowEquityEnum {
  EquityUnknown = 0;
  EquityClock = 1;                // 打卡
  EquityCmFire = 2;               // 商业花火
  EquityTavern = 3;               // 悬赏带货
  EquityTrustAd = 4;              // 广告分成
  EquityGrowupAv = 5;             // 创作激励视频
  EquityGrowupCol = 6;            // 创作激励专栏
  EquityGrowupMtr = 7;            // 创作激励素材
  EquityTemplateIncentive = 8;    // 模版激励
  EquityMallUp = 9;               // 工房
  EquityBizChargePlus = 10;       // 充电
  EquityBizMonthChargePlus = 11;  // 包月充电
  EquityBizCourse = 12;           // 课堂
  EquityBizPartner = 13;          // 合伙人
  EquityBizLive = 14;             // 直播
  EquityBizFly = 15;              // 起飞
}

enum BizTypeEnum {
  BizTypeUnknow = 0;
  // 优质up
  BizTypeUp = 1;
  // 优质案例
  BizTypeDemo = 2;
  // 创作学院
  BizTypeClass = 3;
}


message  GetCheatUpListReq {
  // 页码
  int32 Page = 1 [(gogoproto.moretags) = 'form:"page" validate:"required"'];
  // 页量,单次不超过100条
  int32 PageSize = 2 [(gogoproto.moretags) = 'form:"page_size" validate:"required,max=100"'];
  // 名单类型
  CheatUpTypeEnum Type = 3 [(gogoproto.moretags) = 'form:"type" validate:"required"'];
  // 日期,如果不传，则查全量
  int64 CDate = 4 [(gogoproto.moretags) = 'form:"cdate"'];
}

message GetCheatUpListResp {
  // 名单列表
  repeated CheatUpListItem CheatUpList = 1 [(gogoproto.jsontag) = "cheat_up_list", json_name = "cheat_up_list"];
  // 总数
  int64 Total = 2 [(gogoproto.jsontag) = "total", json_name = "total"];
}

message CheatUpListItem {
  // mid up主mid
  int64 Mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // 名单类型
  CheatUpTypeEnum Type = 2 [(gogoproto.jsontag) = "type", json_name = "type"];
  // 日期
  int64 CDate = 3 [(gogoproto.jsontag) = "cdate", json_name = "cdate"];
}

message CheckIsCheatUpResp {
  // 是否命中名单 0-未命中 1-命中
  int64 IsExist = 1 [(gogoproto.jsontag) = "is_exist", json_name = "is_exist"];
}

message CheckIsCheatUpReq {
  // mid up主mid
  int64 Mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  // 开始时间
  int64 Sdate = 2 [(gogoproto.moretags) = 'form:"sdate"'];
  // 结束时间
  int64 Edate = 3 [(gogoproto.moretags) = 'form:"edate"'];
  // 名单类型
  CheatUpTypeEnum Type = 4 [(gogoproto.moretags) = 'form:"type" validate:"required"'];
}

enum CheatUpTypeEnum{
  // 未知
  CheatUpTypeEnumUnknown = 0;
  // 作弊UP
  CheatUpTypeEnumCheat = 1;
  // 机器人投稿
  CheatUpTypeEnumRobot = 2;
}

message GetAllWhiteSkinBookConfigsReply {
  repeated WhiteSkinBookConfigItem list = 1 [(gogoproto.jsontag) = "list"];
}

message WhiteSkinBookConfigItem {
  // up主mid
  int64 mid = 1 [(gogoproto.jsontag) = "mid"];
  // 白皮书业务场景
  BizTypeEnum biz_type = 2 [(gogoproto.jsontag) = "biz_type"];
  // 案例类型/课程类型
  int64 case_type = 3  [(gogoproto.jsontag) = "case_type"];
  string case_id = 4 [(gogoproto.jsontag) = "case_id"];
  // 权益类型
  int64 equity_type = 5 [(gogoproto.jsontag) = "equity_type"];
  string  title = 6 [(gogoproto.jsontag) = "title"];
  string h5_cover = 7 [(gogoproto.jsontag) = "h5_cover"];
  string pc_cover = 8 [(gogoproto.jsontag) = "pc_cover"];
  // 跳转链接
  string link = 9 [(gogoproto.jsontag) = "link"];
  // up主描述
  string up_desc = 10 [(gogoproto.jsontag) = "up_desc"];
  int64 weigh = 11 [(gogoproto.jsontag) = "weigh"];
  // 子权益类型，对应直播权益：1-新手，2-进阶
  int64 sub_equity_type = 12 [(gogoproto.jsontag) = "sub_equity_type"];
}

message GetArchiveDayTrustAdIncomesByDateReq{
  // 稿件ids
  repeated int64 Avids = 1 [(gogoproto.moretags) = 'form:"avids" validate:"required,max=300"'];
  // 查询日期
  int64 Date = 2 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'date', json_name = "date", (gogoproto.moretags) = 'form:"date" validate:"required"'];
}

message GetArchiveDayTrustAdIncomesByDateReply{
  map<int64, int64> Avid2DayTrustAdIncomeMap = 1 [(gogoproto.jsontag) = "avid_2_day_trust_ad_income_map", json_name =
      "avid_2_day_trust_ad_income_map"];
}

message GetFlyWalletUpgradeSwitchReply{
  bool FlyNewInterfaceSwitch = 1 [(gogoproto.jsontag) = "fly_new_interface_switch", json_name = "fly_new_interface_switch"] ;
  bool FlyWalletForbiddenIncrSwitch = 2 [(gogoproto.jsontag) = "fly_wallet_forbidden_incr_switch", json_name = "fly_wallet_forbidden_incr_switch"];
}

message BatchGetArchiveIncomeByDateReq{
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  repeated int64 Avids = 2 [(gogoproto.moretags) = 'form:"avids" validate:"required,max=500"'];
  int64 Date = 3 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'date', json_name = "date", (gogoproto.moretags) = 'form:"date" validate:"required"'];
}
message BatchGetArchiveIncomeByDateReply{
  map<int64, ArchiveIncome> Avid2IncomeMap = 1 [(gogoproto.jsontag) = "avid_2_income_map", json_name = "avid_2_income_map"];
}
message ArchiveIncome{
  int64 DayIncome = 1 [(gogoproto.jsontag) = "day_income", json_name = "day_income"];
  int64 TotalIncome = 2 [(gogoproto.jsontag) = "total_income", json_name = "total_income"];
  int64 Mid = 3 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  int64 Avid = 4 [(gogoproto.jsontag) = "avid", json_name = "avid"];
  int64 Date = 5 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'date', json_name = "date"];
}

message GetTaskStatusByCdateReq{
  repeated int64 TaskTypes = 1 [(gogoproto.moretags) = 'form:"task_types" validate:"required,max=100"'];
  int64 Date = 2 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'date', json_name = "date", (gogoproto.moretags) = 'form:"date" validate:"required"'];
}
message GetTaskStatusByCdateReply{
  map<int64, int64> TaskType2StatusMap = 1 [(gogoproto.jsontag) = "task_type_2_status_map", json_name = "task_type_2_status_map"];
}
message GetFriSoleArchiveDayIncomesByDateReq{
  repeated int64 Avids = 1 [(gogoproto.moretags) = 'form:"avids" validate:"required,max=500"'];
  int64 Date = 2 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'date', json_name = "date", (gogoproto.moretags) = 'form:"date" validate:"required"'];
}
message GetFriSoleArchiveDayIncomesByDateReply{
  map<int64, int64> Avid2DayIncomeMap = 1 [(gogoproto.jsontag) = "avid_2_day_income_map", json_name = "avid_2_day_income_map"];
}
message TemplateIncomeDetailReq {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // 日期 -格式是2022-11-11
  string Date = 2 [(gogoproto.moretags) = 'form:"date" validate:"required"'];
  // pn 页码
  int32 pn = 3 [(gogoproto.moretags) = "form:\"pn\" validate:\"min=1\" default:\"1\""];
  // ps 页量
  int32 ps = 4 [(gogoproto.moretags) = "form:\"ps\" validate:\"max=500\" default:\"20\""];
}

message TemplateIncomeDetailResp {
  repeated TemplateIncome Data = 1 [(gogoproto.jsontag) = 'data', json_name = "data"];;
  // total 总数
  int32 total = 2 [(gogoproto.jsontag) = "total", json_name = "total"];
}

message TemplateIncome {
  // 模板ID
  int64 TemplateID = 1 [(gogoproto.jsontag) = 'template_id', json_name = "template_id"];
  // MID
  int64 MID = 2 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
  // 选入精选库时间 -秒级时间戳
  int64 PickTime = 3 [(gogoproto.jsontag) = 'pick_time', json_name = "pick_time"];
  // 收入截止日 -格式是2022-11-11
  string FinalIncomeDate = 4 [(gogoproto.jsontag) = 'final_income_date', json_name = "final_income_date"];
  // 收入 - 单位是分
  int64 Income = 5 [(gogoproto.jsontag) = 'income', json_name = "income"];
  // pick收入 - 单位是分
  int64 PickIncome = 6 [(gogoproto.jsontag) = 'pick_income', json_name = "pick_income"];
  // 数据批次 -格式是2022-11-11
  string LogDate = 7 [(gogoproto.jsontag) = 'log_date', json_name = "log_date"];
  // 优质模板收入
  int64 HighQualityIncome = 8 [(gogoproto.jsontag) = "high_quality_income", json_name = "high_quality_income"];
}

message GetFriSoleRatiosByDateReq{
  repeated int64 Mids = 1 [(gogoproto.moretags) = 'form:"mids" validate:"required,max=500"'];
}

message GetFriSoleRatesByDateReply{
  map<int64, int64> Mid2SoleRatioMap = 1 [(gogoproto.jsontag) = "mid_2_sole_ratio_map", json_name = "mid_2_sole_ratio_map"];
}

message CancelFriSoleArchiveReq{
  int64 Avid = 1 [(gogoproto.jsontag) = "avid", json_name = "avid"];
}

message GetFriSoleArchiveValidListReq{
  // mid up主mid
  int64 MID = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  int64 TaskId = 2 [(gogoproto.moretags) = 'form:"task_id" validate:"required"'];
}
message GetFriSoleArchiveValidListReply{
  repeated FriSoleValidArchive list = 1;
}
message FriSoleValidArchive{
  int64 Avid = 1 [(gogoproto.jsontag) = "avid", json_name = "avid"];
  string Bvid = 2 [(gogoproto.jsontag) = "bvid", json_name = "bvid"];
  string Cover = 3 [(gogoproto.jsontag) = "cover", json_name = "cover"];
  string Title = 4 [(gogoproto.jsontag) = "title", json_name = "title"];
  int64 PubTime = 5 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'pub_time', json_name = "pub_time"];
  UnNormalState UnNormalState = 6 [(gogoproto.jsontag) = "un_normal_state", json_name = "un_normal_state"];
  AngleMarkEnum AngleMark = 7 [(gogoproto.jsontag) = "angle_mark", json_name = "angle_mark"];
  bool ShowMenu = 8[(gogoproto.jsontag) = "show_menu", json_name = "show_menu"];
  bool IsAllowedCancel = 9[(gogoproto.jsontag) = "is_allowed_cancel", json_name = "is_allowed_cancel"];
  int64 Mid = 10[(gogoproto.jsontag) = "mid", json_name = "mid"];
}
message UnNormalState{
  UnnormalTypeEnum UnnormalType = 1[(gogoproto.jsontag) = "unnormal_type", json_name = "unnormal_type"];
  string DateTitle = 2 [(gogoproto.jsontag) = "date_title", json_name = "date_title"];
  int64 Date = 3 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'date', json_name = "date"];
  string DescTitle = 4 [(gogoproto.jsontag) = "desc_title", json_name = "desc_title"];
  string Desc = 5 [(gogoproto.jsontag) = "desc", json_name = "desc"];
  string Reason = 6 [(gogoproto.jsontag) = "reason", json_name = "reason"];
  repeated string ViolationUrlList = 7  [(gogoproto.jsontag) = "violation_url_list", json_name = "violation_url_list"];
}
enum AngleMarkEnum{
  AngleMarkSole = 0;
  AngleMarkCancel = 1;
  AngleMarkViolation = 2;
  AngleMarkInvalid = 3;
}
message BatchGetFriSoleArchiveValidListReq{
  // mid up主mid
  repeated int64 Mids = 1 [(gogoproto.moretags) = 'form:"mids" validate:"required,max=500"'];
  int64 TaskId = 2 [(gogoproto.moretags) = 'form:"task_id" validate:"required"'];
}
message BatchGetFriSoleArchiveValidListReply{
  map<int64, GetFriSoleArchiveValidListReply> Mid2ValidArchiveList = 1[(gogoproto.jsontag) = "mid_2_archive_List_map", json_name = "mid_2_archive_List_map"];
}

message BatchGetFriSoleArchiveInvalidListReq{
  // mid up主mid
  repeated int64 Mids = 1 [(gogoproto.moretags) = 'form:"mids" validate:"required,max=500"'];
  int64 TaskId = 2 [(gogoproto.moretags) = 'form:"task_id" validate:"required"'];
}
message BatchGetFriSoleArchiveInvalidListReply{
  map<int64, GetFriSoleArchiveInvalidListReply> Mid2InvalidArchiveList = 1[(gogoproto.jsontag) = "mid_2_invalid_archive_List_map", json_name = "mid_2_invalid_archive_List_map"];
}


message GetFriSoleArchiveInvalidListReq{
  // mid up主mid
  int64 MID = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  int64 TaskId = 2 [(gogoproto.moretags) = 'form:"task_id" validate:"required"'];
}
message GetFriSoleArchiveInvalidListReply{
  repeated FriSoleInvalidArchive list = 1;
}
message FriSoleInvalidArchive{
  int64 Avid = 1 [(gogoproto.jsontag) = "avid", json_name = "avid"];
  string Bvid = 2 [(gogoproto.jsontag) = "bvid", json_name = "bvid"];
  string Cover = 3 [(gogoproto.jsontag) = "cover", json_name = "cover"];
  string Title = 4 [(gogoproto.jsontag) = "title", json_name = "title"];
  int64 PubTime = 5 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'pub_time', json_name = "pub_time"];
  string FailReason = 6 [(gogoproto.jsontag) = "fail_reason", json_name = "fail_reason"];
  UnnormalTypeEnum UnnormalType = 7[(gogoproto.jsontag) = "unnormal_type", json_name = "unnormal_type"];
  int64 Mid = 8[(gogoproto.jsontag) = "mid", json_name = "mid"];
}
enum UnnormalTypeEnum{
  // 正常默认状态
  UnnormalNone = 0;
  // 时长不足
  UnnormalDuration = 1;
  // 非自治稿件
  UnnormalCopyright = 2;
  // 未开放浏览
  UnnormalPublish = 3;
  // 额度已满
  UnnormalQuota = 4;
  // 商单稿件
  UnnormalBizOrder = 5;
  // up主取消声明
  UnnormalCancel = 6;
  // 版权违规
  UnnormalViolation = 7;
  // 命中灰tag
  UnnormalGrayTag = 8;
  // 命中回查tag+四限
  UnnormalCheckTag = 9;
  // 退出先声激励
  UnnormalUpExit = 10;
}

message IsNeedSetAdIndustryReq{
  // mid up主mid
  int64 MID = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
}

message IsNeedSetAdIndustryReply{
  bool IsNeedSetAdIndustry = 1;
}

// UpDangerousPoolReq 获取高危up主池内数据
message UpDangerousPoolReq {
  // pn 页码 非必传
  int32 pn = 1 [
    (gogoproto.moretags) = "form:\"pn\" default:\"1\""
  ];
  // ps 页量 非必传
  int32 ps = 2 [
    (gogoproto.moretags) = "form:\"ps\" default:\"20\""
  ];
}

// UpDangerousPoolListReply 按条件获取高危up主池内数据列表
message UpDangerousPoolListReply {
  // list 营销号池结构对象列表
  repeated UpDangerousPool list = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
  // total 总数
  int32 total = 2 [(gogoproto.jsontag) = "total", json_name = "total"];
}
// UpDangerousPool  高危up主池结构对象.
message UpDangerousPool {
  // mid up主mid
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // ratio 降权系数（乘以1000）
  int32 ratio = 2 [(gogoproto.jsontag) = "ratio", json_name = "ratio"];
  // down_type 0、未开始 1、降权中 2、已结束
  int32 down_type = 3 [
    (gogoproto.jsontag) = "down_type", json_name = "down_type"
  ];
  // biz_type 业务类型 0 未知 1 视频 2 专栏 4-素材
  int32 biz_type = 4 [
    (gogoproto.jsontag) = "biz_type", json_name = "biz_type"
  ];
  // begin_date 开始时间
  int64 begin_date = 5 [
    (gogoproto.jsontag) = "begin_date", json_name = "begin_date",
    (gogoproto.casttype) = "go-common/library/time.Time"
  ];
  // end_date 结束时间
  int64 end_date = 6 [
    (gogoproto.jsontag) = "end_date", json_name = "end_date",
    (gogoproto.casttype) = "go-common/library/time.Time"
  ];
  // show_up 降权是否对up展示 0 未知 1外显 2不外显
  int32 show_up = 7 [
    (gogoproto.jsontag) = "show_up", json_name = "show_up"
  ];
}

message GetPlusBufTimeResp{
  int64 BufStartTime = 1 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'buf_start_time', json_name = "buf_start_time"];
  int64 BufEndTime = 2 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'buf_end_time', json_name = "buf_end_time"];
}

message UpAdStateInfoReq {
  // mid
  int64 MID = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
}

message UpAdStateInfoResp{
  // 用户广告权限，0-无权限，1-有权限
  UpAdStateType AdState = 1 [(gogoproto.jsontag) = "ad_state", json_name = "ad_state"];
}

enum UpAdStateType {
  // 默认无权限
  UpAdStateTypeNoPermission = 0;
  // 有权限
  UpAdStateTypePermission = 1;
}

message UpGrowPlusInfosReq {
  // mid
  repeated int64 Mids = 1 [(gogoproto.moretags) = 'form:"mids,split" validate:"required,max=100"'];
}

message UpGrowPlusInfosResp {
  map<int64, UpGrowUpgradeInfo> UpInfos = 1 [(gogoproto.jsontag) = "up_infos", json_name = "up_infos"];
}
message UpGrowUpgradeInfo{
  int64 Mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  UpGrowUpgradeType UpState = 2 [(gogoproto.jsontag) = "up_state", json_name = "up_state"];
  int64 FirstSignedAt = 3 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'first_signed_at', json_name = "first_signed_at"];
  int64 RejectAt = 4 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'reject_at', json_name = "reject_at"];
  // 是否同意升级，true-同意，false-拒绝
  bool IsUpgrade = 5;
}

enum UpGrowUpgradeType {
  // 未知
  UpStateTypeUnknown = 0;
  // 同意升级
  UpStateTypeAgree = 1;
  // 设置广告
  UpStateTypeSetAd = 2;
  // 暂不设置广告
  UpStateTypeSetNoAd = 3;
  // 同意并签约
  UpStateTypeAgreeAndSign = 4;
  // 拒绝
  UpStateTypeReject = 10;
}

message UpIncomeRangeReq {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // mid
  int64 MID = 2 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  // 查询开始日期 -秒级时间戳
  int64 Sdate = 3 [(gogoproto.jsontag) = 'sdate', json_name = "sdate", (gogoproto.moretags) = 'form:"sdate" validate:"required"'];
  // 查询结束日期 -秒级时间戳
  int64 Edate = 4 [(gogoproto.jsontag) = 'edate', json_name = "edate", (gogoproto.moretags) = 'form:"edate" validate:"required"'];
}

message UpIncomeDateResp {
  // 日期 -秒级时间戳
  int64 Date = 1 [(gogoproto.jsontag) = 'date', json_name = "date"];
}

message UpTotalIncomeResp {
  // 收入金额，单位分
  int64 TotalIncome = 1 [(gogoproto.jsontag) = "total_income", json_name = "total_income"];
}

message UpDailyIncomeResp {
  repeated DailyIncome Data = 1 [(gogoproto.jsontag) = 'data', json_name = "data"];
}

message DailyIncome {
  // 日期 -秒级时间戳
  int64 Date = 1 [(gogoproto.jsontag) = 'date', json_name = "date"];
  // 收入金额，单位分
  int64 Income = 2 [(gogoproto.jsontag) = 'income', json_name = "income"];
}

// CheckUpNegativeResp 根据mid、负向管控的类型获取up主负向管控判断resp
message CheckUpNegativeResp {
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // key：enum UpNegativeEnum
  map<int64, bool> states = 2 [(gogoproto.jsontag) = "states", json_name = "states"];
}

// CheckUpNegativeReq 根据mid、负向管控的类型获取up主负向管控判断req
message CheckUpNegativeReq {
  // MID up主mid
  int64 Mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  // Type 管控类型
  repeated UpNegativeEnum types = 2 [(gogoproto.moretags) = 'form:"types,split" validate:"required"'];
}

// UpNegativeEnum up管控类型枚举
// swagger:enum UpNegativeEnum
enum UpNegativeEnum {
  UpNegativeEnumUnknown = 0; // 未知
  UpNegativeEnumBlackList = 1; // 黑名单
  UpNegativeEnumLowQuality = 2; // 低质（前台显示为准）
  UpNegativeEnumCredit = 3; // 信用分
  UpNegativeEnumForbidden = 4; // 封禁
  UpNegativeEnumQuit = 5; // 清退
}

message UpSignApplyExtra {
  UpSignAccountType AccountType = 1;
  int32 SignType = 2;
}
message UpSignAddExtra {
  UpSignAccountType AccountType = 1;
}
message UpSignPassExtra {
}
message UpSignRejectExtra {
  string Reason = 1;
  int32 Days = 2;
}
message UpSignForbidExtra {
  string Reason = 1;
  int32 Days = 2;
  UpSignAccountState oldState = 3;
  ForbidTypeEnum forbidType = 4;
}
message UpSignDismissExtra {
  string Reason = 1;
  UpSignAccountState oldState = 2;
  DismissTypeEnum dismissType = 3;
}
message UpSignQuitExtra {
  string Reason = 1;
}
message UpSignRecoveryExtra {
}
message UpSignDeleteExtra {
}
message UpSignStateExtra {
  string table = 1;
  UpSignAccountState state = 2;
}
message UpSignAccDelExtra {
}
message UpSignRecUpExtra {
}
message UpSignExtra {
  UpSignApplyExtra ApplyInfo = 1 [(gogoproto.jsontag) = "apply_info,omitempty", json_name = "apply_info"];
  UpSignAddExtra AddInfo = 2 [(gogoproto.jsontag) = "add_info,omitempty", json_name = "add_info"];
  UpSignPassExtra PassInfo = 3 [(gogoproto.jsontag) = "pass_info,omitempty", json_name = "pass_info"];
  UpSignRejectExtra RejectInfo = 4 [(gogoproto.jsontag) = "reject_info,omitempty", json_name = "reject_info"];
  UpSignForbidExtra ForbidInfo = 5 [(gogoproto.jsontag) = "forbid_info,omitempty", json_name = "forbid_info"];
  UpSignDismissExtra DismissInfo = 6 [(gogoproto.jsontag) = "dismiss_info,omitempty", json_name = "dismiss_info"];
  UpSignQuitExtra QuitInfo = 7 [(gogoproto.jsontag) = "quit_info,omitempty", json_name = "quit_info"];
  UpSignRecoveryExtra RecoveryInfo = 8 [(gogoproto.jsontag) = "recovery_info,omitempty", json_name = "recovery_info"];
  UpSignDeleteExtra DeleteInfo = 9 [(gogoproto.jsontag) = "delete_info,omitempty", json_name = "delete_info"];
  UpSignStateExtra StateInfo = 10 [(gogoproto.jsontag) = "state_info,omitempty", json_name = "state_info"];
  UpSignAccDelExtra AccDelInfo = 11 [(gogoproto.jsontag) = "account_del_info,omitempty", json_name = "account_del_info"];
  UpSignRecUpExtra RecUpInfo = 12 [(gogoproto.jsontag) = "recovery_up_info,omitempty", json_name = "recovery_up_info"];
}

message UpSignQualifiedArg {
  int64 Mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  SignBiz SignBiz = 2 [(gogoproto.moretags) = 'form:"sign_biz" validate:"required"'];
}
message UpSignQualifiedReply {
  bool IsQualified = 1 [(gogoproto.jsontag) = "is_qualified", json_name = "is_qualified"];
}

message UpSignInfoArg {
  repeated int64 Mids = 1 [(gogoproto.moretags) = 'form:"mids,split" validate:"required,max=100"'];
  SignBiz SignBiz = 2 [(gogoproto.moretags) = 'form:"sign_biz" validate:"required"'];
}

message UpSignInfoReply {
  map<int64, UpSignInfo> UpInfos = 1 [(gogoproto.jsontag) = "up_infos", json_name = "up_infos"];
}

message UpSignAbnormalReply {
  bool IsBlocked = 1 [(gogoproto.jsontag) = "is_blocked", json_name = "is_blocked"];
  bool IsForbid = 2[(gogoproto.jsontag) = "is_forbid", json_name = "is_forbid"];
  bool IsDismissed = 3 [(gogoproto.jsontag) = "is_dismissed", json_name = "is_dismissed"];
}

message UpSignBlockInfoReply {
  bool IsBlocked = 1 [(gogoproto.jsontag) = "is_blocked", json_name = "is_blocked"];
  BlockTypeEnum BlockType = 2 [(gogoproto.jsontag) = 'block_type', json_name = "block_type"];
}

message UpSignInfo {
  int64 Mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  bool IsBlocked = 2 [(gogoproto.jsontag) = "is_blocked", json_name = "is_blocked"];
  UpSignAccountType accountType = 3 [(gogoproto.jsontag) = "account_type", json_name = "account_type"];
  UpSignAccountState accountState = 4 [(gogoproto.jsontag) = "account_state", json_name = "account_state"];
  int64 SignedAt = 5 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'signed_at', json_name = "signed_at"];
  int64 RejectAt = 6 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'reject_at', json_name = "reject_at"];
  ForbidTypeEnum ForbidType = 7 [(gogoproto.jsontag) = 'forbid_type', json_name = "forbid_type"];
  DismissTypeEnum DismissType = 8 [(gogoproto.jsontag) = 'dismiss_type', json_name = "dismiss_type"];
  int64 DissmissAt = 9 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'dismiss_at', json_name = "dismiss_at"];
  string Reason = 10 [(gogoproto.jsontag) = 'reason', json_name = "reason"];
  int64 ExpiredIn = 11 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'expired_in', json_name = "expired_in"];
  int64 QuitAt = 12 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'quit_at', json_name = "quit_at"];
  int64 Ctime = 13 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'ctime', json_name = "ctime"];
  int64 ApplyAt = 14 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'apply_at', json_name = "apply_at"];
  BlockTypeEnum BlockType = 15 [(gogoproto.jsontag) = 'block_type', json_name = "block_type"];
}

message IsTrashReply {
  // 是否营销号
  bool IsTrash = 1 [(gogoproto.jsontag) = 'is_trash', json_name = "is_trash"];
  // 视频营销号等级
  int32 VideoLevel = 2 [(gogoproto.jsontag) = 'video_level', json_name = "video_level"];
  // 视频是否营销号
  bool VideoIsTrash = 3 [(gogoproto.jsontag) = 'video_is_trash', json_name = "video_is_trash"];
  // 专栏是否营销号
  bool ColumnIsTrash = 4 [(gogoproto.jsontag) = 'column_is_trash', json_name = "column_is_trash"];
  // 视频是否营销号(展示)
  bool VideoShowIsTrash = 5 [(gogoproto.jsontag) = 'video_show_is_trash', json_name = "video_show_is_trash"];
}

message AccVerifyArg {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // mid
  int64 Mid = 2 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  // scene
  string Scene = 3 [(gogoproto.moretags) = 'form:"scene" validate:"required"'];
}

message UpSignOpArg {
  int64 Mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  SignBiz SignBiz = 2 [(gogoproto.moretags) = 'form:"sign_biz" validate:"required"'];
  string Operator = 3 [(gogoproto.moretags) = 'form:"operator" validate:"required,max=32"'];
  UpSignExtra Extra = 4 [(gogoproto.moretags) = 'form:"extra"'];
}

message IsTrashArg {
  int64 Mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
}

// swagger:enum SignBiz
enum SignBiz {
  SignBizUnknown = 0;
  // -1 - 所有业务
  SignBizAll = -1;
  // 1 - 视频业务
  SignBizVideo = 1;
  // 2 - 专栏业务
  SignBizColumn = 2;
  // 3 - 音频(素材库)业务
  SignBizBgm = 3;
  // 4 - 模板业务
  SignBizTemplate = 4;
}

enum UpSignAccountType {
  UpSignAccountTypeUnknown = 0;
  UpSignAccountTypeUGC = 1;
  UpSignAccountTypePGC = 2;
}

enum UpSignAccountState {
  UpSignAccountStateUnknown = 0;
  UpSignAccountStateToApply = 1;
  UpSignAccountStateToAudit = 2;
  UpSignAccountStateSigned = 3;
  UpSignAccountStateRejected = 4;
  UpSignAccountStateQuited = 5;
  UpSignAccountStateDismissed = 6;
  UpSignAccountStateForbidden = 7;
}

enum ForbidTypeEnum {
  ForbidTypeEnumUnknown = 0;
  // 固定封禁
  ForbidTypeEnumFixed = 1;
  // 信用分封禁
  ForbidTypeEnumCredit = 2;
}

enum DismissTypeEnum {
  DismissTypeEnumUnknown = 0;
  // 普通封禁
  DismissTypeEnumNormal = 1;
  // 信用分封禁
  DismissTypeEnumCredit = 2;
}

enum BlockTypeEnum {
  // 无归类
  BlockTypeDefault = 0;
  // 企业号
  BlockTypeCompany = 1;
  // 主站账号封禁
  BlockTypeAccountBlocked = 2;
}


message WalletCreateArg {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // mid
  int64 Mid = 2 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  // product
  string Product = 3 [(gogoproto.moretags) = 'form:"product" validate:"required"'];
}

message WalletCreateResponse {
  // mid 用户ID
  int64 Mid = 1 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
  // acc_id 帐号ID
  string AccId = 2 [(gogoproto.jsontag) = 'acc_id', json_name = "acc_id"];
}

message FundTransferArg {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // 商户订单号，需要保证相同单号的信息幂等
  string OrderID = 2 [(gogoproto.jsontag) = 'order_id', json_name = "order_id", (gogoproto.moretags) = 'form:"order_id" validate:"required"'];
  // 收款人mid
  int64 Mid = 3 [(gogoproto.jsontag) = 'mid', json_name = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"'];
  // 转账金额(分)
  int64 Amount = 4 [(gogoproto.jsontag) = 'amount', json_name = "amount", (gogoproto.moretags) = 'form:"amount" validate:"required"'];
  // 约定转账产品
  string Product = 5 [(gogoproto.jsontag) = 'product', json_name = "product", (gogoproto.moretags) = 'form:"product" validate:"required"'];
  // 转账描述
  string Content = 6 [(gogoproto.jsontag) = 'content', json_name = "content", (gogoproto.moretags) = 'form:"content" validate:"required"'];
  // 约定转账时间，即时到账无需传
  int64 SetTime = 7 [(gogoproto.jsontag) = 'set_time', json_name = "set_time", (gogoproto.moretags) = 'form:"set_time"'];
  // 支付成功回调地址，激励金转账需要传
  string NotifyURL = 8 [(gogoproto.jsontag) = 'notify_url', json_name = "notify_url", (gogoproto.moretags) = 'form:"notify_url"'];
  // 一些额外信息..
  FundTransferExtra Extra = 9 [(gogoproto.jsontag) = 'extra', json_name = "extra", (gogoproto.moretags) = 'form:"extra"'];
}

message FundTransferExtra {
  map<int64, int64> AidAmt = 1 [(gogoproto.jsontag) = 'aid_amt', json_name = "aid_amt"];
}

message FundTransferReply {
  // 商户订单号
  string OrderID = 1 [(gogoproto.jsontag) = 'order_id', json_name = "order_id"];
  // 激励交易号
  string TransferNO = 2 [(gogoproto.jsontag) = 'transfer_no', json_name = "transfer_no"];
}

message PayTransferInfoArg {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // 转账订单号
  repeated string TransferNO = 2 [(gogoproto.jsontag) = 'transfer_no', json_name = "transfer_no", (gogoproto.moretags) = 'form:"transfer_no" validate:"required,max=100"'];
}

enum PayTransState {
  UNKNOW = 0;
  //已创建
  WAITING = 2;
  // 转账中
  EFFECTING = 3;
  // 已到账
  DONE = 99;
  // 转账失败
  FAILED = 100;
}

message PayTransferInfo {
  string TransferNO = 1 [(gogoproto.jsontag) = 'transfer_no', json_name = "transfer_no"];
  // 约定转账时间
  int64 SetTime = 2 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'set_time', json_name = "set_time"];
  // 订单金额（分）
  int64 Amount = 3 [(gogoproto.jsontag) = 'amount', json_name = "amount"];
  // 订单状态
  PayTransState State = 4 [(gogoproto.jsontag) = 'state', json_name = "state"];
}

message PayTransferInfoReply {
  // key是订单号
  map<string, PayTransferInfo> infos = 1;
}

message RefundInfoReply {
  // 商户订单号，需要保证唯一
  string OrderID = 1 [(gogoproto.jsontag) = 'order_id', json_name = "order_id"];
  // 激励交易号
  string TradeNO = 2 [(gogoproto.jsontag) = 'trade_no', json_name = "trade_no"];
  // 金额，单位分
  int64 TotalAmount = 3 [(gogoproto.jsontag) = 'total_amount', json_name = "total_amount"];
  // 退款单号
  string RefundNO = 4 [(gogoproto.jsontag) = 'refund_no', json_name = "refund_no"];
  // 退款金额
  int64 RefundAmount = 5 [(gogoproto.jsontag) = 'refund_amount', json_name = "refund_amount"];
}

message RefundInfoArg {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // 商户订单号，需要保证唯一
  string OrderID = 2 [(gogoproto.jsontag) = 'order_id', json_name = "order_id", (gogoproto.moretags) = 'form:"order_id"'];
  // 激励交易号
  string TradeNO = 3 [(gogoproto.jsontag) = 'trade_no', json_name = "trade_no", (gogoproto.moretags) = 'form:"trade_no"'];
  // 退款单号
  string RefundNO = 4 [(gogoproto.jsontag) = 'refund_no', json_name = "refund_no", (gogoproto.moretags) = 'form:"refund_no" validate:"required"'];
}

message TradeRefundArg {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // 商户订单号，需要保证唯一
  string OrderID = 2 [(gogoproto.jsontag) = 'order_id', json_name = "order_id", (gogoproto.moretags) = 'form:"order_id"'];
  // 激励交易号
  string TradeNO = 3 [(gogoproto.jsontag) = 'trade_no', json_name = "trade_no", (gogoproto.moretags) = 'form:"trade_no"'];
  // 退款单号
  string RefundNO = 4 [(gogoproto.jsontag) = 'refund_no', json_name = "refund_no", (gogoproto.moretags) = 'form:"refund_no" validate:"required"'];
  // 退款金额
  int64 RefundAmount = 5 [(gogoproto.jsontag) = 'refund_amount', json_name = "refund_amount", (gogoproto.moretags) = 'form:"refund_amount" validate:"required"'];
  // 退款说明
  string RefundReason = 6 [(gogoproto.jsontag) = 'refund_reason', json_name = "refund_reason", (gogoproto.moretags) = 'form:"refund_reason"'];
}

message TradeRefundReply {
  // 商户订单号，需要保证唯一
  string OrderID = 1 [(gogoproto.jsontag) = 'order_id', json_name = "order_id"];
  // 激励交易号
  string TradeNO = 2 [(gogoproto.jsontag) = 'trade_no', json_name = "trade_no"];
  // 金额，单位分
  int64 TotalAmount = 3 [(gogoproto.jsontag) = 'total_amount', json_name = "total_amount"];
  // 退款单号
  string RefundNO = 4 [(gogoproto.jsontag) = 'refund_no', json_name = "refund_no"];
  // 退款金额
  int64 RefundAmount = 5 [(gogoproto.jsontag) = 'refund_amount', json_name = "refund_amount"];
}

message TradePayArg {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // 商户订单号，需要保证唯一
  string OrderID = 2 [(gogoproto.jsontag) = 'order_id', json_name = "order_id", (gogoproto.moretags) = 'form:"order_id"'];
  // 激励交易号
  string TradeNO = 3 [(gogoproto.jsontag) = 'trade_no', json_name = "trade_no", (gogoproto.moretags) = 'form:"trade_no"'];
  // payer mid
  int64 PayerID = 4 [(gogoproto.moretags) = 'form:"payer_id" validate:"required"'];
  // 支付类型
  int32  pay_type = 5 [(gogoproto.jsontag) = 'pay_type', json_name = "pay_type", (gogoproto.moretags) = 'form:"pay_type"'];
}

message TradePayReply {
  // 商户订单号，需要保证唯一
  string OrderID = 1 [(gogoproto.jsontag) = 'order_id', json_name = "order_id"];
  // 激励交易号
  string TradeNO = 2 [(gogoproto.jsontag) = 'trade_no', json_name = "trade_no"];
  // 金额，单位分
  int64 TotalAmount = 3 [(gogoproto.jsontag) = 'total_amount', json_name = "total_amount"];
}

message MIDArg {
  // mid
  int64 MID = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  // 商户 id
  string AppKey = 2 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
}

message AccInfoResponse {
  //  MID
  int64 MID = 1 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
  //  余额
  int64 Balance = 2 [(gogoproto.jsontag) = 'balance', json_name = "balance"];
  //  消费限制
  int64 ExchangeLimit = 3 [(gogoproto.jsontag) = 'exchange_limit', json_name = "exchange_limit"];
  //  是否有激励金钱包
  bool HasWallet = 4 [(gogoproto.jsontag) = 'has_wallet', json_name = "has_wallet"];
  // 待结算金额（账户余额）
  int64 UnwithdrawIncome = 5 [(gogoproto.jsontag) = 'unwithdraw_income', json_name = "unwithdraw_income"];
}

message TradeCreateArg {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // 商户订单号，需要保证唯一
  string OrderID = 2 [(gogoproto.jsontag) = 'order_id', json_name = "order_id", (gogoproto.moretags) = 'form:"order_id" validate:"required"'];
  // 订单标题
  string Subject = 3 [(gogoproto.jsontag) = 'subject', json_name = "subject", (gogoproto.moretags) = 'form:"subject" validate:"required,min=1,max=128"'];
  // 订单描述
  string Content = 4 [(gogoproto.jsontag) = 'content', json_name = "content", (gogoproto.moretags) = 'form:"content" validate:"min=0,max=256"'];
  // 相对失效时间，秒为单位
  int64 Expire = 5 [(gogoproto.jsontag) = 'expire', json_name = "expire", (gogoproto.moretags) = 'form:"expire" validate:"min=1,required"'];
  // 交易金额(分)
  int64 Amount = 6 [(gogoproto.jsontag) = 'amount', json_name = "amount", (gogoproto.moretags) = 'form:"amount" validate:"min=1,required"'];
  // 折扣金额(分)
  int64 Discount = 7 [(gogoproto.jsontag) = 'discount', json_name = "discount", (gogoproto.moretags) = 'form:"discount" validate:"min=0"'];
  // 交易成功异步通知地址
  string NotifyURL = 8 [(gogoproto.jsontag) = 'notify_url', json_name = "notify_url", (gogoproto.moretags) = 'form:"notify_url"'];
  // 指定激励金账号ID
  string SellerId = 9 [(gogoproto.jsontag) = 'seller_id', json_name = "seller_id", (gogoproto.moretags) = 'form:"seller_id"'];
}

message TradeCreateReply {
  // 商户订单号，需要保证唯一
  string OrderID = 1 [(gogoproto.jsontag) = 'order_id', json_name = "order_id"];
  // 激励交易号
  string TradeNO = 2 [(gogoproto.jsontag) = 'trade_no', json_name = "trade_no"];
  // 金额，单位分
  int64 TotalAmount = 3 [(gogoproto.jsontag) = 'total_amount', json_name = "total_amount"];
}

message TradeDetailArg {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // 商户订单号，需要保证唯一
  string OrderID = 2 [(gogoproto.jsontag) = 'order_id', json_name = "order_id", (gogoproto.moretags) = 'form:"order_id"'];
  // 激励交易号
  string TradeNO = 3 [(gogoproto.jsontag) = 'trade_no', json_name = "trade_no", (gogoproto.moretags) = 'form:"trade_no"'];
}

message TradeDetailReply {
  // 商户订单号，需要保证唯一
  string OrderID = 1 [(gogoproto.jsontag) = 'order_id', json_name = "order_id"];
  // 激励交易号
  string TradeNO = 2 [(gogoproto.jsontag) = 'trade_no', json_name = "trade_no"];
  // 交易状态
  int32 Status = 3 [(gogoproto.jsontag) = 'status', json_name = "status"];
  // 交易金额(分)
  int64 Amount = 4 [(gogoproto.jsontag) = 'amount', json_name = "amount"];
  // 退款金额(分)
  int64 Refund = 5 [(gogoproto.jsontag) = 'refund', json_name = "refund"];
  // 折扣金额(分)
  int64 Discount = 6 [(gogoproto.jsontag) = 'discount', json_name = "discount"];
}

message IncomeUpSummaryArg {
  // 商户 id
  string AppKey = 1 [(gogoproto.jsontag) = 'appkey', json_name = "appkey", (gogoproto.moretags) = 'form:"appkey" validate:"required,is-appkey"'];
  // 稿件类型: 1-视频  2-专栏 3-素材
  IncomeType Type = 2 [(gogoproto.jsontag) = 'type', json_name = "type", (gogoproto.moretags) = 'form:"type" validate:"required"'];
  //  MID
  int64 MID = 3 [(gogoproto.jsontag) = 'mid', json_name = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"'];
}

message IncomeUpSummaryReply {
  // 最新收入日期（格式为 yyyy:mm:dd）
  string Date = 1 [(gogoproto.jsontag) = 'date', json_name = "date"];
  // 最新日收入
  int64 DayIncome = 2 [(gogoproto.jsontag) = 'day_income', json_name = "day_income"];
  // 累计收入
  int64 TotalIncome = 3 [(gogoproto.jsontag) = 'total_income', json_name = "total_income"];
}

// swagger:enum IncomeType
enum IncomeType {
  Unknown = 0;
  // -1 - 所有业务
  All = -1;
  // 1 - 视频业务
  Video = 1;
  // 2 - 专栏业务
  Column = 2;
  // 3 - 音频(素材库)业务
  Material = 3;
  // 4 - 模板业务
  Template = 4;
}

message MidArgReq {
  int64 Mid = 1 [(gogoproto.jsontag) = 'mid', json_name = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"'];
}

message EvpIconIsShowReply {
  bool IsShow = 1 [(gogoproto.jsontag) = 'is_show', json_name = "is_show"];
}

message EvpInviteCardIsShowReply {
  bool IsShow = 1 [(gogoproto.jsontag) = 'is_show', json_name = "is_show"];
}
message OfflineIncomeArg {
  // mid
  int64 MID = 1 [(gogoproto.jsontag) = 'mid', json_name = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"'];
  // start_date
  int64 StartDate = 2 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'start_date', json_name = "start_date", (gogoproto.moretags) = 'form:"start_date" validate:"required"'];
  // end_date
  int64 EndDate = 3 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'end_date', json_name = "end_date", (gogoproto.moretags) = 'form:"end_date" validate:"required"'];
}

message OfflineIncomeReply {
  map<string, int64> income = 1 [(gogoproto.jsontag) = "income", json_name = "income"];
}

message OfflineIncomeTotalReply {
  int64 TotalIncome = 1 [(gogoproto.jsontag) = 'total_income', json_name = "total_income"];
}

message OfflineIncomeDetailArg {
  // mid
  int64 MID = 1 [(gogoproto.jsontag) = 'mid', json_name = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"'];
  // start_date
  int64 StartDate = 2 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'start_date', json_name = "start_date", (gogoproto.moretags) = 'form:"start_date"'];
  // end_date
  int64 EndDate = 3 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'end_date', json_name = "end_date", (gogoproto.moretags) = 'form:"end_date"'];
  // from
  int64 From = 5 [(gogoproto.jsontag) = 'from', json_name = "from", (gogoproto.moretags) = 'form:"from"'];
  // limit
  int64 Limit = 6 [(gogoproto.jsontag) = 'limit', json_name = "limit", (gogoproto.moretags) = 'form:"limit" validate:"min=10,max=100"'];
}

message OfflineIncomeDetailReply {
  repeated OfflineBonusInfo BonusInfo = 1 [(gogoproto.jsontag) = 'bonus_info', json_name = "bonus_info"];
  int64 Total = 2 [(gogoproto.jsontag) = 'total', json_name = "total"];
}

message OfflineBonusInfo {
  string ActivityName = 1 [(gogoproto.jsontag) = 'activity_name', json_name = "activity_name"];
  int64 ActivityId = 2 [(gogoproto.jsontag) = 'activity_id', json_name = "activity_id"];
  int64 BonusMoney = 3 [(gogoproto.jsontag) = 'bonus_money', json_name = "bonus_money"];
  int64 BonusSendTime = 4 [(gogoproto.casttype) = "go-common/library/time.Time", (gogoproto.jsontag) = 'bonus_send_time', json_name = "bonus_send_time"];
}