// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package main.crm.copyrightmusicpublicityinterface.v1;

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
// import "google/api/annotations.proto";

option (wdcli.appid) = "main.crm.copyrightmusicpublicityinterface";
option go_package = "buf.bilibili.co/bapis/bapis-gen/crm/service.music.publicity.interface.recommend;api";
option java_package = "com.bapis.crm.service.music.publicity.interfaces.recommend";
option java_multiple_files = true;
option (gogoproto.goproto_getters_all) = true;

service Recommend {
  // 根据music_id获取基础信息
  rpc GetMusicBaseInfo(GetMusicReq) returns (BaseMusicInfo);
  // 根据music_id获取基础信息
  rpc GetMusicInfos(GetMusicsReq) returns (GetMusicsResp);
  // 根据music_id获取推荐稿件数量(最新批次)
  rpc GetRecommendCount(GetRecommendCountReq) returns (GetRecommendCountResp);
  // 多曲目入口
  rpc MultipleEntrance(MultipleEntranceReq) returns (MultipleEntranceResp);
  // story流量批量获取bgm入口
  rpc StoryEntrance(StoryEntranceReq) returns (StoryEntranceResp);
  // 批量获取音乐入口信息
  rpc BatchQueryEntrances(BatchQueryEntrancesReq) returns (BatchQueryEntrancesResp);
  // 播放器入口获取tag信息接口 bgm与video 优先bgm 其次video
  rpc QueryPlayTagEntrances(MultipleEntranceReq) returns (EntranceResp);
  // 获取稿件识别的影娱video信息 业务干预及黑名单策略等 cid必需
  rpc QueryAidCidVideoInfo(MultipleEntranceReq) returns (EntranceResp);
  // 获取稿件识别的音乐bgm信息 由业务干预及黑名单策略等 包含多曲目 cid必需
  rpc QueryAidCidBgmInfo(MultipleEntranceReq) returns (EntranceResp);
  // 批量获取音乐播放量 最多请求20条
  rpc BatchQueryListenPv(BatchQueryListenPvReq) returns (BatchQueryListenPvResp);
  // 获取单个music_id对应的乐评相关信息
  rpc QueryMusicCriticism(QueryMusicCriticismReq) returns (QueryMusicCriticismResp);
  // 获取单个music_id对应的乐评均分
  rpc QueryMusicCriticismAverageMark(QueryCriticismAverageMarkReq) returns (QueryCriticismAverageMarkResp);
}


message GetMusicReq {
  string music_id = 1 [(gogoproto.moretags) = 'form:"music_id" validate:"required"'];
}

message GetMusicsReq {
  // 请求查询的音乐信息 music_id 数量最多10个
  repeated string music_ids = 1 [(gogoproto.moretags) = 'form:"music_ids" validate:"required"'];
}

message GetMusicsResp {
  // 返回有bgm入口的aid信息 aid->AidItem
  map<string, BaseMusicInfo> list = 1 [(gogoproto.jsontag) = "list"];
}

message BaseMusicInfo {
  // 具体业务id 根据返回的source 区分
  string music_id = 1 [(gogoproto.jsontag) = "music_id"];
  // 具体业务资源名称 根据返回的source 区分
  string music_title = 2 [(gogoproto.jsontag) = "music_title"];
  // 业务跳转地址
  string jump_url = 3 [(gogoproto.jsontag) = "jump_url"];
  //艺人名称
  string artist_name = 4 [(gogoproto.jsontag) = "artist_name"];
  //封面
  string cover_url = 5 [(gogoproto.jsontag) = "cover_url"];
  // 稿件aid 原创mv aid
  int64 aid = 6 [(gogoproto.jsontag) = "aid"];
  // 稿件cid 原创mv cid
  int64 cid = 7 [(gogoproto.jsontag) = "cid"];
  // 歌曲热歌榜成就信息
  BaseMusicHonor honor = 8 [(gogoproto.jsontag) = "honor"];
  // 关联稿件量级 推荐数
  int64 relation_count = 9 [(gogoproto.jsontag) = "relation_count"];
  // 音乐 总累计播放vv 关联稿件vv之和
  int64 music_vv = 10 [(gogoproto.jsontag) = "music_vv"];
  // 音乐 音乐热度值
  int64 music_heat = 11 [(gogoproto.jsontag) = "music_heat"];
  // 音乐 专辑 可能为空
  string  music_album= 12 [(gogoproto.jsontag) = "music_album"];
  // 音乐 出处来源 电影或者电视剧等 可能为空
  string music_reference = 13 [(gogoproto.jsontag) = "music_reference"];
}

message BaseMusicHonor {
  int64 period = 1  [(gogoproto.jsontag) = "period"]; // 期数
  int64 rank = 2  [(gogoproto.jsontag) = "rank"]; // 排名
  int64 category = 3  [(gogoproto.jsontag) = "category"]; // 成就类型
  string txt = 4  [(gogoproto.jsontag) = "txt"]; // 成就文本
  int64 list_id = 5  [(gogoproto.jsontag) = "list_id"]; // 榜单id
  string url = 6 [(gogoproto.jsontag) = "url"]; // 跳转url
}

message GetRecommendCountReq {
  repeated string music_ids = 1 [(gogoproto.moretags) = 'form:"music_ids" validate:"required"'];
}

message GetRecommendCountResp {
  map<string, int64> counts = 1 [(gogoproto.jsontag) = "counts"];
}

message MultipleEntranceReq {
  // 稿件aid
  int64 aid = 1 [(gogoproto.moretags) = 'form:"aid" validate:"required"'];
  // 稿件cid
  int64 cid = 2 [(gogoproto.moretags) = 'form:"cid"'];
  // 请求来源 空 默认普通tag区， play_tag 播放器屏幕入口 半屏右下角 全屏左上角
  string source = 3 [(gogoproto.moretags) = 'form:"source"'];
  // 稿件cid
  string platform = 4 [(gogoproto.moretags) = 'form:"platform"'];
}

message MultipleEntranceResp {
  // 0-aid下的cid不存在音乐  1-aid下的cid存在音乐
  int64 music_state = 1 [(gogoproto.jsontag) = "music_state"];
}

message EntranceResp {
  // 0-aid下的cid不存在音乐  1-aid下的cid存在音乐
  int64 music_state = 1 [(gogoproto.jsontag) = "music_state"];
  // 显示名称 ，音乐下发音乐名称 ，影娱下发 IP名称
  string display = 2 [(gogoproto.jsontag) = "display"];
  // 返回识别的数量
  int64 count = 3 [(gogoproto.jsontag) = "count"];
  // 返回识别类型 默认bgm ，动画影娱 cartoon
  string source = 4 [(gogoproto.jsontag) = "source"];
  //识别的具体信息 以及跳转地址
  MusicInfoItem  music_info = 5 [(gogoproto.jsontag) = "music_info"];
}

message MusicInfoItem {
  // 具体业务id 根据返回的source 区分
  string music_id = 1 [(gogoproto.jsontag) = "music_id"];
  // 具体业务资源名称 根据返回的source 区分
  string music_title = 2 [(gogoproto.jsontag) = "music_title"];
  // 业务跳转地址
  string jump_url = 3 [(gogoproto.jsontag) = "jump_url"];
}

message StoryEntranceReq {
  // 请求aid-cid 列表
  repeated AidCid aid_cid = 1 [(gogoproto.moretags) = 'form:"aid_cid"'];
  // 当前请求平台 android ios
  int64 platform = 2 [(gogoproto.moretags) = 'form:"platform"'];
  //实验组id music_0
  string sample_id = 3 [(gogoproto.moretags) = 'form:"sample_id"'];
}

message AidCid {
  // 稿件aid
  int64 aid = 1 [(gogoproto.moretags) = 'form:"aid"'];
  // 稿件cid
  int64 cid = 2 [(gogoproto.moretags) = 'form:"cid"'];
}

message StoryEntranceResp {
  // 返回有bgm入口的aid信息 aid->AidItem
  repeated AidItem list = 1 [(gogoproto.jsontag) = "list"];
}

message AidItem {
  // 稿件aid
  int64 aid = 1 [(gogoproto.jsontag) = "aid"];
  // 稿件cid
  int64 cid = 2 [(gogoproto.jsontag) = "cid"];
  // 名称
  string title = 3 [(gogoproto.jsontag) = "title"];
  // 跳转地址
  string jump_url = 4 [(gogoproto.jsontag) = "jump_url"];
}

message BatchQueryEntrancesReq {
  // 请求aid-cid 列表
  repeated AidCid aid_cid = 1 [(gogoproto.moretags) = 'form:"aid_cid"'];
}

message MusicInfo {
  string music_id = 1 [(gogoproto.jsontag) = "music_id"];
  string music_title = 2 [(gogoproto.jsontag) = "music_title"];
  // 艺人名称
  string artist_name = 3 [(gogoproto.jsontag) = "artist_name"];
  // 歌曲封面
  string music_cover = 4 [(gogoproto.jsontag) = "music_cover"];
  // 艺人id 未填充 2024.01 如果需要后续联系开发
  string artist_id = 5 [(gogoproto.jsontag) = "artist_id"];
}

message BatchQueryEntranceItem {
  // 稿件aid
  int64 aid = 1 [(gogoproto.jsontag) = "aid"];
  // 稿件cid
  int64 cid = 2 [(gogoproto.jsontag) = "cid"];
  // 音乐信息
  repeated MusicInfo music_infos = 3 [(gogoproto.jsontag) = "music_infos"];
}

message BatchQueryEntrancesResp {
  map<int64, BatchQueryEntranceItem> data = 1 [(gogoproto.jsontag) = "data"];
}

message BatchQueryListenPvReq {
  // 请求music_id列表
  repeated string music_ids = 1 [(gogoproto.moretags) = 'form:"music_ids" validate:"required"'];
}

message BatchQueryListenPvResp {
  map<string, int64> pv = 1 [(gogoproto.jsontag) = "pv"];
}

message QueryMusicCriticismReq {
  // musicId
  string music_id = 1 [(gogoproto.jsontag) = "music_id"];
  // 登录用户mid
  int64 mid = 2 [(gogoproto.jsontag) = "mid"];
}

message QueryMusicCriticismResp {
  // music_id对应的乐评均分
  double average_mark = 1 [(gogoproto.jsontag) = "average_mark"];
  // music_id对应的乐评热评
  string hottest_reply = 2 [(gogoproto.jsontag) = "hottest_reply"];
  // mid在music_id下的最后一次评分
  int64 last_mark = 3 [(gogoproto.jsontag) = "last_mark"];
}

message QueryCriticismAverageMarkReq {
  // musicId
  string music_id = 1 [(gogoproto.jsontag) = "music_id"];
}

message QueryCriticismAverageMarkResp {
  // music_id对应的乐评均分
  double average_mark = 1 [(gogoproto.jsontag) = "average_mark"];
}