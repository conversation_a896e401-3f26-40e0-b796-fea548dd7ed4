// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package main.crm.copyrightmusicpublicityinterface.v1;

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
// import "google/api/annotations.proto";

option (wdcli.appid) = "main.crm.copyrightmusicpublicityinterface";
option go_package = "buf.bilibili.co/bapis/bapis-gen/crm/service.music.publicity.interface.toplist;api";
option java_package = "com.bapis.crm.service.music.publicity.interfaces.toplist";
option java_multiple_files = true;
option (gogoproto.goproto_getters_all) = true;

service Toplist {
    rpc ToplistEntrance(ToplistEntranceReq) returns(ToplistEntranceReply);
}

message ToplistEntranceReq {
    int64 Aid = 1 [(gogoproto.moretags) = 'validate:"required"'];
    string MusicID = 2 [(gogoproto.moretags) = 'validate:"required"'];
}

message ToplistEntranceReply {
    ArcHonorEntrance ArcHonor = 1;
}

message ArcHonorEntrance {
    // 图标
    string icon = 1;
    // 夜间图标
    string icon_night = 2;
    // 文案
    string text = 3;
    // 附属文案（如收录）
    string text_extra = 4;
    // 文案颜色
    string text_color = 5;
    // 文案夜间颜色
    string text_color_night = 6;
    // 背景色
    string bg_color = 7;
    // 夜间背景色
    string bg_color_night = 8;
    // 跳转链接
    string url = 9;
    // 跳转链接文案
    string url_text = 10;
}