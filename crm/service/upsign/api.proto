syntax = "proto3";
package crm.service.upsign.v1;

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";

option (wdcli.appid) = "main.crm.up-sign-service";
option go_package = "buf.bilibili.co/bapis/bapis-gen/crm/service.upsign;api";
option java_package = "com.bapis.crm.service.upsign";
option java_multiple_files = true;
option (gogoproto.goproto_getters_all) = false;

service Sign {
    rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty){
        option (google.api.http) = {
            get: "/x/internal/upsign/ping"
        };
    }
    rpc GetContractParseResult(GetContractParseReq) returns (GetContractParseReply){
        option (google.api.http) = {
            get: "/x/internal/upsign/contract/parse/result"
        };
    }
    rpc GetContractsParseResult(GetContractsParseReq) returns (GetContractsParseReply){}

    rpc GetContractIds(GetContractIdsReq) returns (GetContractIdsReply){
        option (google.api.http) = {
            get: "/x/internal/upsign/contract/ids"
      };
    }
}

message GetContractParseReq {
    string contract_id = 1 [(gogoproto.moretags) = "form:\"contract_id\" validate:\"required\""];
}

message GetContractsParseReq {
    repeated string contract_ids = 1 [(gogoproto.moretags) = "form:\"contract_ids\" validate:\"min=1,max=100,required\""];
}

message GetContractParseReply {
    string contract_result = 1 [(gogoproto.jsontag) = "contract_result", json_name = "contract_result"];
}

message GetContractsParseReply {
    map<string, string> contract_results = 1 [(gogoproto.jsontag) = "contract_results", json_name = "contract_results"];
}

message GetContractIdsReq {
    int32 pn = 1 [(gogoproto.moretags) = "form:\"pn\" default:\"1\" validate:\"min=1\""];
    int32 ps = 2 [(gogoproto.moretags) = "form:\"ps\" default:\"100\" validate:\"min=1,max=1000\""];
}

message GetContractIdsReply {
    repeated string contract_ids = 1 [(gogoproto.jsontag) = "contract_ids", json_name = "contract_ids"];
    int32 pn = 2 [(gogoproto.jsontag) = "pn", json_name = "pn"];
    int32 ps = 3 [(gogoproto.jsontag) = "ps", json_name = "ps"];
    int32 total = 4 [(gogoproto.jsontag) = "total", json_name = "total"];
}