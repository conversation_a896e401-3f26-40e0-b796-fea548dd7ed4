// Copyright 2020-2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.alpha.registry.v1alpha1;

import "buf/alpha/registry/v1alpha1/generate.proto";
import "google/protobuf/timestamp.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/buf/alpha.registry.v1alpha1";
option java_package = "com.bapis.buf.alpha.registry.v1alpha1";
option java_multiple_files = true;

// Plugin represents a protoc plugin, such as protoc-gen-go.
message Plugin {
  // The ID of the plugin, which uniquely identifies the plugin.
  string id = 1;
  // The name of the plugin, i.e. "protoc-gen-go".
  string name = 2;
  // The name of the owner of the plugin. Either a username or
  // organization name.
  string owner = 3;
  // The visibility of the plugin.
  PluginVisibility visibility = 4;
  // deprecated means this plugin is deprecated.
  bool deprecated = 5;
  // deprecation_message is the message shown if the plugin is deprecated.
  string deprecation_message = 6;
  // The creation time of the plugin.
  google.protobuf.Timestamp create_time = 7;
  // The last update time of the plugin object.
  google.protobuf.Timestamp update_time = 8;
}

// PluginVersion represents a specific build of a plugin,
// such as protoc-gen-go v1.4.0.
message PluginVersion {
  // The ID of the plugin version, which uniquely identifies the plugin version.
  // Mostly used for pagination.
  string id = 1;
  // The name of the version, i.e. "v1.4.0".
  string name = 2;
  // The name of the plugin to which this version relates.
  string plugin_name = 3;
  // The owner of the plugin to which this version relates.
  string plugin_owner = 4;
  // The full container image digest associated with this plugin version including
  // the algorithm.
  // Ref: https://github.com/opencontainers/image-spec/blob/main/descriptor.md#digests
  string container_image_digest = 5;
  // Optionally define the runtime libraries.
  repeated RuntimeLibrary runtime_libraries = 6;
}

// PluginVisibility defines the visibility options available
// for Plugins and Templates.
enum PluginVisibility {
  PLUGIN_VISIBILITY_UNSPECIFIED = 0;
  PLUGIN_VISIBILITY_PUBLIC = 1;
  PLUGIN_VISIBILITY_PRIVATE = 2;
}

// Template defines a set of plugins that should be used together
// i.e. "go-grpc" would include protoc-gen-go and protoc-gen-go-grpc.
message Template {
  // The ID of the template, which uniquely identifies the template.
  string id = 1;
  // The name of the template, i.e. "grpc-go".
  string name = 2;
  // The name of the owner of the template. Either a
  // username or organization name.
  string owner = 3;
  // Must not contain duplicate plugins. Order of plugin configs
  // dictates insertion point order. Note that we're
  // intentionally putting most of the plugin configuration
  // in the template, so that template versions are
  // less likely to cause breakages for users.
  repeated PluginConfig plugin_configs = 4;
  // The visibility of the template.
  PluginVisibility visibility = 5;
  // deprecated means this template is deprecated.
  bool deprecated = 8;
  // deprecation_message is the message shown if the template is deprecated.
  string deprecation_message = 9;
  // The creation time of the template.
  google.protobuf.Timestamp create_time = 10;
  // The last update time of the template object.
  google.protobuf.Timestamp update_time = 11;
}

// PluginConfig defines a runtime configuration for a plugin.
message PluginConfig {
  // deleted is no longer used and should be replaced by inaccessible.
  reserved "deleted";
  reserved 4;
  // The owner of the plugin to which this config relates.
  string plugin_owner = 1;
  // The name of the plugin to which this config relates.
  string plugin_name = 2;
  // Parameters that should be provided to the plugin. These are
  // joined with a "," before being provided to the plugin at runtime.
  repeated string parameters = 3;
  // True if the source plugin is inaccessible by the user.
  bool inaccessible = 5;
}

// TemplateVersion defines a template at a
// specific set of versions for the contained plugins.
message TemplateVersion {
  // The ID of the template version, which uniquely identifies the template version.
  // Mostly used for pagination.
  string id = 1;
  // The name of the template version, i.e. "v1".
  string name = 2;
  // The owner of the template to which this version relates.
  string template_owner = 3;
  // The name of the template to which this version relates.
  string template_name = 4;
  // A map from plugin owner and name to version for the plugins
  // defined in the template. Every plugin in the template
  // must have a corresponding version in this array.
  repeated PluginVersionMapping plugin_versions = 5;
}

// PluginVersionMapping maps a plugin_id to a version.
message PluginVersionMapping {
  // deleted is no longer used and should be replaced by inaccessible.
  reserved "deleted";
  reserved 4;
  // The owner of the plugin to which this mapping relates.
  string plugin_owner = 1;
  // The name of the plugin to which this mapping relates.
  string plugin_name = 2;
  // The version of the plugin to use, i.e. "v1.4.0".
  string version = 3;
  // True if the source plugin is inaccessible by the user.
  bool inaccessible = 5;
}

// PluginService manages plugins.
service PluginService {
  // ListPlugins returns all the plugins available to the user. This includes
  // public plugins, those uploaded to organizations the user is part of,
  // and any plugins uploaded directly by the user.
  rpc ListPlugins(ListPluginsRequest) returns (ListPluginsResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // ListUserPlugins lists all plugins belonging to a user.
  rpc ListUserPlugins(ListUserPluginsRequest) returns (ListUserPluginsResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // ListOrganizationPlugins lists all plugins for an organization.
  rpc ListOrganizationPlugins(ListOrganizationPluginsRequest) returns (ListOrganizationPluginsResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // GetPluginVersion returns the plugin version, if found.
  rpc GetPluginVersion(GetPluginVersionRequest) returns (GetPluginVersionResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // ListPluginVersions lists all the versions available for the specified plugin.
  rpc ListPluginVersions(ListPluginVersionsRequest) returns (ListPluginVersionsResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // GetPlugin returns the plugin, if found.
  rpc GetPlugin(GetPluginRequest) returns (GetPluginResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // DeletePlugin deletes the plugin, if it exists. Note that deleting
  // a plugin may cause breaking changes for templates using that plugin,
  // and should be done with extreme care.
  rpc DeletePlugin(DeletePluginRequest) returns (DeletePluginResponse) {
    option idempotency_level = IDEMPOTENT;
  }
  // GetTemplate returns the template, if found.
  rpc GetTemplate(GetTemplateRequest) returns (GetTemplateResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // ListTemplates returns all the templates available to the user. This includes
  // public templates, those owned by organizations the user is part of,
  // and any created directly by the user.
  rpc ListTemplates(ListTemplatesRequest) returns (ListTemplatesResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // ListTemplatesUserCanAccess is like ListTemplates, but does not return
  // public templates.
  rpc ListTemplatesUserCanAccess(ListTemplatesUserCanAccessRequest) returns (ListTemplatesUserCanAccessResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // ListUserPlugins lists all templates belonging to a user.
  rpc ListUserTemplates(ListUserTemplatesRequest) returns (ListUserTemplatesResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // ListOrganizationTemplates lists all templates for an organization.
  rpc ListOrganizationTemplates(ListOrganizationTemplatesRequest) returns (ListOrganizationTemplatesResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // GetTemplateVersion returns the template version, if found.
  rpc GetTemplateVersion(GetTemplateVersionRequest) returns (GetTemplateVersionResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // ListTemplateVersions lists all the template versions available for the specified template.
  rpc ListTemplateVersions(ListTemplateVersionsRequest) returns (ListTemplateVersionsResponse) {
    option idempotency_level = NO_SIDE_EFFECTS;
  }
  // DeleteTemplate deletes the template, if it exists.
  rpc DeleteTemplate(DeleteTemplateRequest) returns (DeleteTemplateResponse) {
    option idempotency_level = IDEMPOTENT;
  }
}

message ListPluginsRequest {
  uint32 page_size = 1;
  // The first page is returned if this is empty.
  string page_token = 2;
  bool reverse = 3;
}

message ListPluginsResponse {
  repeated Plugin plugins = 1;
  // There are no more pages if this is empty.
  string next_page_token = 2;
}

message ListUserPluginsRequest {
  // The owner to list plugins for.
  string owner = 1;
  uint32 page_size = 2;
  // The first page is returned if this is empty.
  string page_token = 3;
  bool reverse = 4;
}

message ListUserPluginsResponse {
  repeated Plugin plugins = 1;
  // There are no more pages if this is empty.
  string next_page_token = 2;
}

message ListOrganizationPluginsRequest {
  // The organization to list plugins for.
  string organization = 1;
  uint32 page_size = 2;
  // The first page is returned if this is empty.
  string page_token = 3;
  bool reverse = 4;
}

message ListOrganizationPluginsResponse {
  repeated Plugin plugins = 1;
  // There are no more pages if this is empty.
  string next_page_token = 2;
}

message GetPluginVersionRequest {
  // The owner of the plugin the version belongs to.
  string owner = 1;
  // The name of the plugin the version belongs to.
  string name = 2;
  // The name of the version.
  string version = 3;
}

message GetPluginVersionResponse {
  PluginVersion plugin_version = 1;
}

message ListPluginVersionsRequest {
  // The owner of the plugin to list versions for.
  string owner = 1;
  // The name of the plugin to list versions for.
  string name = 2;
  // The number of items to return.
  uint32 page_size = 3;
  // The first page is returned if this is empty.
  string page_token = 4;
  bool reverse = 5;
}

message ListPluginVersionsResponse {
  repeated PluginVersion plugin_versions = 1;
  // There are no more pages if this is empty.
  string next_page_token = 2;
}

message GetPluginRequest {
  // The owner of the plugin.
  string owner = 1;
  // The name of the plugin.
  string name = 2;
}

message GetPluginResponse {
  Plugin plugin = 1;
}

message DeletePluginRequest {
  // The owner of the plugin to delete.
  string owner = 1;
  // The name of the plugin to delete.
  string name = 2;
}

message DeletePluginResponse {}

message GetTemplateRequest {
  // The owner of the template.
  string owner = 1;
  // The name of the template.
  string name = 2;
}

message GetTemplateResponse {
  Template template = 1;
}

message ListTemplatesRequest {
  uint32 page_size = 1;
  // The first page is returned if this is empty.
  string page_token = 2;
  bool reverse = 3;
}

message ListTemplatesResponse {
  repeated Template templates = 1;
  // There are no more pages if this is empty.
  string next_page_token = 2;
}

message ListTemplatesUserCanAccessRequest {
  uint32 page_size = 1;
  // The first page is returned if this is empty.
  string page_token = 2;
  bool reverse = 3;
}

message ListTemplatesUserCanAccessResponse {
  repeated Template templates = 1;
  // There are no more pages if this is empty.
  string next_page_token = 2;
}

message ListUserTemplatesRequest {
  // The owner of the templates to list for.
  string owner = 1;
  uint32 page_size = 2;
  // The first page is returned if this is empty.
  string page_token = 3;
  bool reverse = 4;
}

message ListUserTemplatesResponse {
  repeated Template templates = 1;
  // There are no more pages if this is empty.
  string next_page_token = 2;
}

message GetTemplateVersionRequest {
  // The owner of the template the version belongs to.
  string owner = 1;
  // The name of the template the version belongs to.
  string name = 2;
  // The name of the version.
  string version = 3;
}

message GetTemplateVersionResponse {
  TemplateVersion template_version = 1;
}

message ListOrganizationTemplatesRequest {
  // The organization of the templates to list for.
  string organization = 1;
  uint32 page_size = 2;
  // The first page is returned if this is empty.
  string page_token = 3;
  bool reverse = 4;
}

message ListOrganizationTemplatesResponse {
  repeated Template templates = 1;
  // There are no more pages if this is empty.
  string next_page_token = 2;
}

message ListTemplateVersionsRequest {
  // The owner of the template to list versions for.
  string owner = 1;
  // The name of the template to list versions for.
  string name = 2;
  // The number of items to return.
  uint32 page_size = 3;
  // The first page is returned if this is empty.
  string page_token = 4;
  bool reverse = 5;
}

message ListTemplateVersionsResponse {
  repeated TemplateVersion template_versions = 1;
  // There are no more pages if this is empty.
  string next_page_token = 2;
}

message DeleteTemplateRequest {
  // The owner of the template to delete.
  string owner = 1;
  // The name of the template to delete.
  string name = 2;
}

message DeleteTemplateResponse {}