syntax = "proto3";
package ad.pandora.resource;

import "ad/pandora/resource/campaign_api.proto";
import "extension/wdcli/wdcli.proto";
import "google/protobuf/wrappers.proto";
import "ad/pandora/resource/unit_api.proto";
import "ad/pandora/core/operator_api.proto";

option (wdcli.appid) = "sycpb.cpm.cpm-pandora";
option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/pandora.resource;api";
option java_package = "com.bapis.ad.pandora.resource";
option java_multiple_files = true;

service CreativeResourceService {
  rpc ListNestedTemplateGroup(ListNestedTemplateGroupReq) returns (ListNestedTemplateGroupResp);
  rpc GetCreativeContent(GetCreativeContentReq) returns (GetCreativeContentResp);
  rpc GetCreativeGoodsContent(GetCreativeGoodsContentReq) returns(GetCreativeGoodsContentResp);
  rpc GetCreativeSupportBiliNative(GetCreativeSupportBiliNativeReq) returns (GetCreativeSupportBiliNativeResp);
  rpc ListCreativeTitle(ListCreativeTitleReq) returns (ListCreativeTitleResp);
  rpc ListCreativeImage(ListCreativeImageReq) returns (ListCreativeImageResp);
}

enum MonitorType {
  MONITOR_UNKNOWN = 0;
  // 展示
  MONITOR_VIEW = 1;
  // 点击
  MONITOR_CLICK = 2;
  // 播放0秒
  MONITOR_PLAY = 3;
  // 播放3秒
  MONITOR_PLAY_3S = 4;
  // 播放5秒
  MONITOR_PLAY_5S = 5;
  // 游戏点击
  MONITOR_GAME_CLICK = 6;
  // 评论链接点击
  MONITOR_COMMENT_CLICK = 8;
  // 播放10秒
  MONITOR_PLAY_10S = 9;
  // 播放15秒
  MONITOR_PLAY_15S = 10;
  // 播放监测
  MONITOR_PLAY_MONITORING = 11;
}

enum CreativeArchiveSource {
  ARCHIVE_SOURCE_UNKNOWN = 0;
  // 子账号投稿
  ARCHIVE_SOURCE_CM_VIDEO = 1;
  // 普通稿件
  ARCHIVE_SOURCE_BILI_ARCHIVE = 2;
  // 花火商单
  ARCHIVE_SOURCE_FIREWORKS_ARCHIVE = 3;
  // 普通内容
  ARCHIVE_SOURCE_CONTENT_ARCHIVE = 4;
  // 手动绑定
  ARCHIVE_SOURCE_MANUAL_BIND = 5;
  // 新商单
  ARCHIVE_SOURCE_CM_ARCHIVE = 6;
  // 内容起飞
  ARCHIVE_SOURCE_CONTENT_FLY = 7;
  // OGV
  ARCHIVE_SOURCE_OGV = 8;  
}

enum CreativeDynamicSource {
  DYNAMIC_SOURCE_UNKNOWN = 0;
  // 授权mid的动态
  DYNAMIC_SOURCE_MID_AUTHORIZED_DYNAMIC = 1;
  // 手动绑定的动态
  DYNAMIC_SOURCE_ADHOC_DYNAMIC = 2;
  // 暗投的动态
  DYNAMIC_SOURCE_HIDDEN = 3;
  // 商单动态
  DYNAMIC_SOURCE_CM_ORDER = 4;
}

enum GoodsStyle {
  // 不启用
  GOODS_STYLE_NOT_SUPPORT = 0;
  // 购物车
  GOODS_STYLE_SHOPPING_CART = 1;
  // 评论前置
  GOODS_STYLE_COMMENT_AHEAD = 2;
  // 一跳唤起
  GOODS_STYLE_DIRECT_CALL_UP = 4;
  // 直播购物车
  GOODS_STYLE_LIVE_SHOPPING_CART = 5;
}

enum SmartTitleType {
  SMART_TITLE_UNKNOWN = 0;
  // 区域
  SMART_TITLE_AREA = 1;
  // 性别
  SMART_TITLE_GENDER = 2;
  // 设备
  SMART_TITLE_DEVICE = 3;
  // 年龄
  SMART_TITLE_AGE = 4;
}

// deprecated
enum ImageRatioType {
  RATIO_UNKNOWN = 0;
  RATIO_16_10 = 1;
  RATIO_16_9 = 2;
  RATIO_4_3 = 3;
  RATIO_145_12 = 4;
  RATIO_1_2 = 5;
}

enum LaunchMode {
  // 普通投放
  NORMAL = 0;
  // 原生投放
  NATIVE = 1;
}

// promotion_purpose_content 类型，对应 lau_creative_extra 表的 ppc_type 字段
enum PpcType {
  UNKNOWN = 0;
  // 稿件播放页
  ARCHIVE = 1;
  // 动态
  DYNAMIC = 2;
  // 直播间
  LIVE_ROOM = 3;
  // 内容投放
  NATIVE_CONTENT = 4;
  // 游戏中心
  GAME_CENTER = 5;
  // 其它
  OTHER = 6;
  //OGV
  OGV = 7;
  //会员购
  SHOP_GOODS = 8;
  //游戏卡
  GAME_CARD = 9;
  //大会员
  MEGA_VIP = 10;
  //课堂
  PUGV = 11;
  //猫耳
  CAT_EAR = 12;
}

message ListNestedTemplateGroupReq {
  int32 unit_id = 1;
  // 是否原生, 非必填
  google.protobuf.Int32Value is_bili_native = 2;
  // 流量来源, 非必填
  google.protobuf.Int32Value channel_id = 3;
  // 场景, 非必填
  repeated int32 scene_id_set = 4;
  // 是否程序化创意, 非必填
  google.protobuf.Int32Value is_programmatic = 5;
  // 是否优选场景, 非必填
  google.protobuf.Int32Value is_prefer_scene = 6;
  // 模板组id 非必填
  google.protobuf.Int32Value template_group_id = 7;
  // 账户id 必填
  int32 account_id = 8;
  core.Operator operator = 9;

  // 逐步下线历史单元id字段 需要补充额外信息
  CreativeConfigCampaignUnitInfo creative_config_campaign_unit_info = 10;

  int32 current_general_version = 11;
}

message CreativeConfigCampaignUnitInfo {

  resource.AdType ad_type = 1;

  resource.PromotionPurposeType promotion_purpose_type = 2;

  resource.PromotionContentType promotion_content_type = 3;

  resource.BaseTarget base_target = 4;

  resource.CpaTarget cpa_target = 5;

  resource.CpaTarget deep_cpa_target = 6;

  int32 is_no_bid = 7;
}

message ListNestedTemplateGroupResp {
  // 可选的流量来源列表
  repeated ChannelInfo channel_set = 1;
  // 可选的场景列表
  repeated SceneInfo scene_set = 2;
  // 是否支持优选场景
  int32 is_support_prefer_scene = 3;
  // 是否支持程序化创意
  int32 is_support_programmatic = 4;
  // 可选的模板组列表
  repeated TemplateGroupInfo template_group_set = 5;
  // 是否支持原生投放
  int32 is_support_bili_native = 6;
  // general_version
  int32 general_version = 7;
}

// deprecated
message TemplateGroupInfo {
  int32 template_group_id = 1;
  string template_group_name = 2;
  // 标题最小长度
  int32 title_min_length = 3;
  // 标题最大长度
  int32 title_max_length = 4;
  // 描述最小长度
  int32 description_min_length = 5;
  // 描述最大长度
  int32 description_max_length = 6;
  // 是否支持图片
  int32 is_support_image = 7;
  // 支持的图片信息详情
  repeated TemplateGroupImageInfo template_group_image_set = 8;
  // 是否支持非小号稿件
  int32 is_support_archive = 9;
  // 是否支持小号稿件
  int32 is_support_video = 10;
  // 是否支持原生投放
  int32 is_support_bili_native = 11;
  // 是否支持简易直播间
  int32 is_support_simple_live = 12;
  // 是否支持动态
  int32 is_support_bili_dynamic = 13;
  // 是否支持程序化创意
  int32 is_support_programmatic = 14;
  // 支持的商业标列表
  repeated BusMarkInfo bus_mark_set = 15;
  // 支持的按钮列表
  repeated ButtonInfo button_set = 16;
  // 支持的gif信息详情
  repeated TemplateGroupImageInfo template_group_gif_set = 17;
}

// deprecated
message TemplateGroupImageInfo {
  // 图片文件最小值
  int32 min_size_kb = 1;
  // 图片文件最大值
  int32 max_size_kb = 2;
  // 图片尺寸
  ImageRatioType ratio_type = 3;
}

// deprecated
message TemplateInfo {
  int32 template_id = 1;
  string template_name = 2;
  // 是否暗投
  int32 is_hidden = 3;
  // 是否gif
  int32 is_gif = 4;
  // 卡片类型
  int32 card_type = 5;
  // 是否动态卡片
  int32 is_dynamic_card = 6;
  // 图片尺寸
  ImageRatioType image_ratio_type = 7;
  // 账号标签
  repeated int32 account_label_id_set = 8;
  // 创意形态
  int32 creative_style = 9;
  // 按钮
  repeated ButtonInfo button_info = 10;
  // 图片大小限制
  int32 image_kb_limit = 11;
  // gif大小限制
  int32 animation_kb_limit = 12;
  // 是否支持原生
  int32 is_support_native = 13;
}

message SlotGroupInfo {
  int32 slot_group_id = 1;
  string slot_group_name = 2;
  // 场景
  repeated int32 scene_id_set = 3;
  // 流量类型
  repeated int32 channel_id_set = 4;
  repeated int32 account_label_id_set = 5;
  // 是否支持搜索
  int32 is_support_search_ad = 6;
  // 是否支持nobid
  int32 is_support_no_bid = 7;
  // 是否支持优选广告位
  int32 is_support_prefer_scene = 8;
  // 广告位id
  repeated int32 slot_id_set = 9;
}

message ChannelInfo {
  int32 channel_id = 1;
  string channel_name = 2;
  int32 order = 3;
}

message SceneInfo {
  int32 scene_id = 1;
  string scene_name = 2;
  int32 channel_id = 3;
  int32 order = 4;
  ImageRatioType preview_image_ratio_type = 5;
}

// deprecated
message BusMarkInfo {
  int32 bus_mark_id = 1;
  string bus_mark_name = 2;
  repeated BusMarkStyleInfo bus_mark_style = 3;
}

// deprecated
message BusMarkStyleInfo {
  // 商业标类型
  BusMarkType type = 1;
  // 商业标样式
  BusMarkStyle style = 2;
  // 商业标App使用模式
  BusMarkAppMode app_mode = 3;
  // 商业标使用列类型
  BusMarkColumnType column_type = 4;
  // 框颜色
  string frame_bg_color = 5;
  // 背景颜色
  string background_color = 6;
  // 文案
  string copy_writing = 7;
  // 文案颜色
  string copy_writing_color = 8;
  // 图片url
  string image_url = 9;
  // 图片宽
  int32 image_width = 10;
  // 图片高
  int32 image_height = 11;
}

// deprecated
message ButtonInfo {
  int32 button_id = 1;
  ButtonCopyType button_type = 2;
  string button_name = 3;
}

message CreativeContentConfigInfo {
  // 是否支持标题
  Accessibility title_accessibility = 1;
  // 是否支持描述
  Accessibility description_accessibility = 2;
  // 是否支持跳转链接
  Accessibility jump_url_accessibility = 3;
  // 是否支持唤起链接
  Accessibility scheme_url_accessibility = 4;
  // 是否支持微信小游戏
  Accessibility wechat_mini_game_accessibility = 5;
  // 是否支持stroy创意组件
  Accessibility story_component_accessibility = 6;
  // 是否支持黄车
  Accessibility yellow_car_component_accessibility = 7;
  // 黄车组件类型
  repeated GoodsStyle support_yellow_car_component_type = 8;
  // 空间设置 包括品牌空间和自定义品牌空间
  Accessibility brand_space_accessibility = 9;
  // 是否支持按钮
  Accessibility button_accessibility = 10;
  // 是否支持弹幕组件
  Accessibility danmaku_component_accessibility = 11;
  // 是否支持评论组件
  Accessibility comment_component_accessibility = 12;
  // 是否支持框下组件
  Accessibility underframe_component_accessibility = 13;
  // 是否支持资质
  Accessibility qualification_accessibility = 14;
  // 是否支持广告标
  Accessibility bus_mark_id_accessibility = 15;
  // 是否支持播放监测
  Accessibility play_monitoring_accessibility = 16;
  // 是否支持ogv高能起播片段
  Accessibility ogv_highlight_accessibility = 17;
}

message GetCreativeContentReq {
  // 单元id
  int32 unit_id = 1;
  // 投放模式
  LaunchMode launch_mode = 2;
  // 模板组id
  int32 template_group_id = 3;
  // 场景id
  repeated int32 scene_id = 4;
  // 账户id
  int32 account_id = 5;

  CreativeConfigCampaignUnitInfo creative_config_campaign_unit_info = 6;
}

message GetCreativeContentResp {
  CreativeContentConfigInfo creative_content_config = 1;
}

message GetCreativeGoodsContentReq {
  // 稿件avid 带货目前限制单次最多查询20条 不可与动态id同时查询
  repeated int64 avid = 1;
  // 动态id 带货目前限制单次最多查询20条 不可与稿件id同时查询
  repeated int64 dynamic_id = 2;
  // 商品详情查询位置
  repeated int32 place_types_require_goods_detail = 3;
  // 商品详情查询数量限制
  int32 goods_detail_limit_size = 4;
  // 商品详情-过滤商品来源
  repeated int32 source_type = 5;
  // 是否需要查询置顶评论
  int32 search_top_comment = 6;
}

message GetCreativeGoodsContentResp {
  repeated GetCreativeGoodsContentInfo entity = 1;
}

message GetCreativeSupportBiliNativeReq {
  int32 unit_id = 1;
  int32 account_id = 2;
  CreativeConfigCampaignUnitInfo creative_config_campaign_unit_info = 3;
}

message GetCreativeSupportBiliNativeResp {
  int32 is_support_bili_native = 1;
}

message GetCreativeGoodsContentInfo {
  // 稿件cid/图文动态id
  int64 content_id = 1;
  // 是否cid带货
  bool is_cid_goods = 2;
  // 是否弹幕带货
  bool barrage_has_goods = 3;
  // 是否浮层带货
  bool supernatant_has_goods = 4;
  // 是否框下带货
  bool recommend_has_goods = 5;
  // 是否评论带货
  bool comment_has_goods = 6;
  // 是否动态带货
  bool dynamic_has_goods = 7;
  // 置顶评论带货
  bool comment_with_top_goods = 8;
  // 评论带货商品详情列表 ctime倒排 top10
  repeated CommentGoodsInfo comment_goods_set = 9;

  // 黄车是否展示
  bool yellow_car_show_status = 10;

  repeated PlaceGoodsInfo place_goods_info = 11;
}

message PlaceGoodsInfo {
  int32 place_type = 1;
  repeated CommentGoodsInfo goods = 2;
  int32 total = 3;
}

// 其实不一定是comment
message CommentGoodsInfo {
  // 商品id
  int64 item_id = 1;
  // 商品外部唯一id
  string outer_id = 2;
  // 商品来源
  int32 source = 3;
  // 商品名称
  string item_name = 4;
  // 带货组件新建时间
  int64 ctime = 5;
  //商品主图
  string main_img_url = 6;
  //商品来源icon
  string icon_url = 7;
  //h5链接
  string jump_url = 8;
  //第三方应用唤起链接
  string schema_url = 9;
}

message ListCreativeTitleReq {
  repeated int32 creative_ids = 1;
}

message ListCreativeImageReq {
  repeated int32 creative_ids = 1;
}

message ListCreativeTitleResp {
  repeated SingleCreativeTitleResp data = 1;
}

message SingleCreativeTitleResp {
  int32 creative_id = 1;
  // 原始标题
  string raw_title = 2;
  // 转义后的标题
  string title = 3;
  int64 material_id = 4;
  string material_md5 = 5;
  int32 is_aigc_replace = 6;
}

message ListCreativeImageResp {
  repeated SingleCreativeImageResp data = 1;
}

message SingleCreativeImageResp {
  int32 creative_id = 1;
  string image_url = 2;
  string image_md5 = 3;
  int64 material_id = 4;
  int32 is_aigc_replace = 5;
}

// deprecated
enum ButtonCopyType {
  BUTTON_COPY_TYPE_UNKNOWN = 0;

  // 跳转链接
  BUTTON_COPY_TYPE_JUMP_LINK = 1;
  // APP下载和唤醒
  BUTTON_COPY_TYPE_APP_DOWN_AWAKEN = 2;
  // 安卓游戏下载
  BUTTON_COPY_TYPE_ANDROID_DOWNLOAD = 5;
  // 直播预约
  BUTTON_COPY_TYPE_LIVE_BOOKING = 6;
  //新应用唤起
  BUTTON_COPY_TYPE_NEW_AWAKEN = 7;
}

// deprecated
enum BusMarkType {
  // 无标
  BUS_MARK_TYPE_NONE = 0;
  // 图片标
  BUS_MARK_TYPE_PIC = 1;
  //  文字标
  BUS_MARK_TYPE_TEXT = 2;
}

// deprecated
enum BusMarkStyle {
  // 不支持
  BUS_MARK_STYLE_UNSUPPORTED = 0;
  // 实心
  BUS_MARK_STYLE_SOLID = 1;
  // 空心
  BUS_MARK_STYLE_HOLLOW = 2;
  // 空白
  BUS_MARK_STYLE_EMPTY = 3;
}

// deprecated
enum BusMarkAppMode {
  // 日间模式
  BUS_MARK_APP_MODE_DAY = 0;
  // 夜间模式
  BUS_MARK_APP_MODE_NIGHT = 1;
}

// deprecated
enum BusMarkColumnType {
  // 不区分单双列
  BUS_MARK_COLUMN_TYPE_NONE = 0;
  // 单列
  BUS_MARK_COLUMN_TYPE_SINGLE = 1;
  // 双列
  BUS_MARK_COLUMN_TYPE_DOUBLE = 2;
}