syntax = "proto3";
package ad.pandora.resource;

import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "sycpb.cpm.cpm-pandora";
option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/pandora.resource;api";
option java_package = "com.bapis.ad.pandora.resource";
option java_multiple_files = true;

service TargetService {
    rpc GetTargetPackage(GetTargetPackageReq) returns (GetTargetPackageReply);
}

message GetTargetPackageReq {
    int32 target_package_id = 1;
}

message GetTargetPackageReply {
    TargetPackageInfo target_package_info = 1;
}

message TargetPackageInfo {
    int32 target_package_id = 1;
    string target_package_name = 2;
    TargetInfo target_info = 5;
}

message TargetInfo {
    // 地域定向-地理划分
    repeated int32 area = 1;
    // 地域定向-发展划分
    repeated int32 area_level = 2;
    // 地域定向类型
    AreaType area_type = 3;
    // 设备平台定向 整合关联定向
    repeated OsTargetInfo os = 4;
    // 网络定向
    repeated int32 network = 5;
    // 性别定向
    repeated int32 gender = 6;
    // 年龄定向
    repeated int32 age = 7;
    // app 分类定向
    repeated int32 app_category = 8;
    // 人群包定向
    CrowdPackTargetInfo crowd_pack = 9;
    // 视频关键词兴趣定向
    TagTargetInfo arc_tag_interest = 10;
    // 视频关键词定向
    TagTargetInfo arc_tag = 11;
    // 已安装用户过滤
    InstalledUserFilterTargetInfo installed_user_filter = 12;
    // 已转化用户过滤
    ConvertedUserFilterType converted_user_filter = 13;
    // 智能放量
    IntelligentMassTargetInfo intelligent_mass = 14;
    // 视频分区定向
    repeated int32 video_partition = 15;
    // 行业优选
    int32 is_profession_auto = 16;
    // 行业兴趣人群包
    repeated int32 profession_interest_crowd_pack = 17;
    // 手机价格
    repeated int32 phone_price = 18;
    // 视频分区兴趣定向 粉丝关系定向
    ArchiveContentTargetInfo archive_content = 19;
    // 老的分区定向
    repeated int32 category = 20;
}

message OsTargetInfo {
    // 平台定向
    OsTarget os_target = 1;
    // 操作系统版本定向
    repeated int32 os_version = 2;
    // bilibili客户端版本定向
    OsBiliClientTargetInfo os_bili_client = 3;
    // 设备品牌定向 仅安卓支持
    repeated int32 device_brand = 4;
}

message OsBiliClientTargetInfo {
    // 关系
    Relation relation = 1;
    // 较小的版本
    int32 smaller_version = 2;
    // 较大的版本
    int32 larger_version = 3;
}

message CrowdPackTargetInfo {
    repeated int32 include_crowd_pack_id = 1;
    repeated int32 exclude_crowd_pack_id = 2;
}

message ArchiveContentTargetInfo {
    // 视频分区兴趣
    repeated int64 video_second_partition = 1;
    // 包含该UP主粉丝
    repeated int64 include_their_fans_mid = 2;
    // 排除该UP主粉丝
    repeated int64 exclude_their_fans_mid = 3;
}

message TagTargetInfo {
    TagTargetType tag_fuzzy_type = 1;
    repeated string tag = 2;
}

message InstalledUserFilterTargetInfo {
    InstalledUserFilterType type = 1;
    repeated int32 content = 2;
}

message IntelligentMassTargetInfo {
    // 智能放量类型
    repeated IntelligentMassTargetType intelligent_mass_target_type = 1;
    // 扩展种子人群包
    repeated int32 extra_crowd_pack_id = 2;
}

enum AreaType {
    // 全部
    ALL = 0;
    // 实时在此的用户
    CURRENT = 1;
    // 常住在此的用户
    PERMANENT = 2;
    // 旅游在此的用户
    TRIP = 3;
}

enum OsTarget {
    OS_UNKNOWN = 0;
    // 安卓
    OS_ANDROID = 399;
    // IOS
    OS_IOS = 398;
    // IPAD
    OS_IPAD = 421;
}

enum Relation {
    //  不限
    RELATION_NOT_LIMIT = 0;
    // 介于两者之间
    RELATION_BETWEEN = 1;
    // 小于等于
    RELATION_LTE = 2;
    // 大于等于
    RELATION_GTE = 3;
    // 小于
    RELATION_LT = 4;
    // 大于
    RELATION_GT = 5;
}

enum TagTargetType {
    // 无
    NONE = 0;
    // 精确匹配
    EXACT = 1;
    // 模糊匹配
    FUZZY = 2;
}

enum InstalledUserFilterType {
    // 不限
    INSTALLED_NOT_LIMIT = 0;
    // 过滤
    INSTALLED_FILTER = 1;
    // 定向
    INSTALLED_TARGET = 2;
    // 安装
    INSTALLED_INSTALL = 3;
}

enum ConvertedUserFilterType {
    // 不限
    CONVERTED_NOT_LIMIT = 0;
    // 品牌
    CONVERTED_BRAND = 637;
    // 公司组
    CONVERTED_COMPANY_GROUP = 482;
    // 账户
    CONVERTED_ACCOUNT = 483;
    // 计划
    CONVERTED_CAMPAIGN = 484;
    // 单元
    CONVERTED_UNIT = 485;
}

enum IntelligentMassTargetType {
    INT_MASS_UNKNOWN = 0;
    INT_MASS_AGE = 486;
    INT_MASS_GENDER = 487;
    INT_MASS_AREA = 488;
    INT_MASS_BUSINESS_INTEREST = 489;
    INT_MASS_CROWD_PACK = 490;
    INT_MASS_EXTRA_CROWD_PACK = 491;
    INT_MASS_OS = 635;
    INT_MASS_DEVICE_BRAND = 636;
}

