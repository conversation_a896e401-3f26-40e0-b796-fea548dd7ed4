syntax = "proto3";
package ad.pandora.core;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/pandora.core;api";
option java_package = "com.bapis.ad.pandora.core";
option java_multiple_files = true;

service CrowdService {
  rpc ListCrowdItems(ListCrowdItemsReq) returns (ListCrowdItemResp);
}

enum CrowdType {
  AREA = 0;
  GENDER = 1;
  AGE = 3;
}

message CrowdItem {
  int32 id = 1;
  string name = 2;
  repeated CrowdItem children = 4;
}

message ListCrowdItemsReq {
  CrowdType crowd_type = 1;
}

message ListCrowdItemResp {
  repeated CrowdItem crowd_item_set = 1;
}