syntax = "proto3";
import "extension/wdcli/wdcli.proto";
import "google/protobuf/wrappers.proto";

package ad.mgk;


option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/mgk.material";
option java_package = "com.bapis.ad.mgk.material";
option java_multiple_files = true;
option (wdcli.appid) = "sycpb.cpm.mgk-portal";


// appid: sycpb.cpm.mgk-portal
service CreativeCourseMngService{

  // 查询所有一级+二级目录列表
  rpc ListDirectory(DirectoryListReq) returns (CourseDirectoryList);

  // 查询子目录列表
  rpc ListSubDirectory(SubDirectoryListReq) returns (CourseDirectoryList);

  // 添加目录
  rpc AddDirectory(DirectoryAddReq) returns (CourseDirectory);

  // 删除目录
  rpc DeleteDirectory(DirectoryDeleteReq) returns (CourseDirectory);

  // 更新目录
  rpc UpdateDirectory(DirectoryUpdateReq) returns (CourseDirectory);

  // 查询课程列表
  rpc PageCourse(CoursePageReq) returns (CreativeCoursePage);

  // 添加课程
  rpc AddCourse(CourseAddReq) returns (CreativeCourse);

  // 删除课程
  rpc DeleteCourse(CourseDeleteReq) returns (CreativeCourse);

  // 更新课程
  rpc UpdateCourse(CourseUpdateReq) returns (CreativeCourse);

}

message CourseDirectoryList{

  repeated CourseDirectory directory_list = 1;
}




message CourseDirectory{

  int64 directory_id = 1;

  string directory_name = 2;

  int32 level = 3;

  int32 sort_priority = 4;

  repeated CourseDirectory child_directory = 5;

  string node_type = 6;

  string mtime = 7;

  string ctime = 8;

  bool is_show = 9;


}


message CreativeCoursePage{

  repeated CreativeCourse course_list = 1;

  int32 total_count = 2;

  int32 page = 3;

}

message CreativeCourse{

  int64 course_id = 1;

  string node_type = 2;

  string course_type = 3;

  string author = 4;

  string doc_title = 5;

  string doc_summary = 6;

  string doc_content = 7;

  int32 sort_priority = 8;

  string ctime = 9;

  string mtime = 10;

  bool is_show = 11;

  int64 first_directory_id = 12;

  string first_directory_name = 13;

  int64 second_directory_id = 14;

  string second_directory_name = 15;

  int64 parent_directory_id = 16;

  string parent_directory_name = 17;

  CreativeCourseExtra course_extra = 18;

  string cover_url = 19;

  string cover_md5 = 20;

  int64 avid = 21;

  string bvid = 22;

  string video_title = 23;

  int32 video_duration = 24;

  int64 up_mid = 25;

  string favorite_key = 26;

  bool favorite = 27;

  repeated int64 ancestor_ids = 28;

  bool jump_enabled = 29;

  string jump_url = 30;

}



message CreativeCourseExtra{

  string cover_url = 1;

  string cover_md5 = 2;

  int64 avid = 3;

  string bvid = 4;

  string video_title = 5;

  int32 video_duration = 6;

  int64 up_mid = 7;

  string author = 8;

  bool jump_enabled = 9;

  string jump_url = 10;

}


message DirectoryListReq{

  google.protobuf.BoolValue is_show = 1;

}

message SubDirectoryListReq{

  // 父目录id
  int64 parent_directory_id = 1;
}

message DirectoryAddReq{

  // 层级，1=一级目录，2=二级目录
  int32 level = 1;

  // 父目录id, 一级目录的父目录id可不提供
  int64 parent_directory_id = 2;

  // 目录名
  string directory_name = 3;

  // 排序优先级
  int32 sort_priority = 4;
}


message DirectoryDeleteReq{

  // 目录id
  int64 directory_id = 1;
}

message DirectoryUpdateReq{

  // 目录id
  int64 directory_id = 1;

  // 目录名
  google.protobuf.StringValue directory_name = 2;

  // 排序优先级
  google.protobuf.Int32Value sort_priority = 3;

  google.protobuf.BoolValue is_show = 4;
}


message CoursePageReq{


  int32 pn = 1;

  int32 ps = 2;


  // 如果为空表示所有的课程，
  google.protobuf.Int64Value first_directory_id = 3;

  // 当二级目录指定时，其实以及目录id已经不重要了， 当二级目标为空是， 表示查询一级目录下的所有课程
  google.protobuf.Int64Value second_directory_id = 4;

  // 排序方式枚举， pubtime (降序)或priority (升序) . 默认pubtime
  MaterialSortBy sort_by = 5;

  // 课程类型, 不传表示所有类型， video表示视频课程， doc表示文档课程
  google.protobuf.StringValue course_type = 7;

  // 是否回表查询文档内存，如果文档存在时 可选，默认false
  google.protobuf.BoolValue retrieve_doc = 8;

  // 不传表示查询所有， 传表示传讯对应的可见状态
  google.protobuf.BoolValue is_show = 9;
}


enum MaterialSortBy{


  priority = 0;

  pubtime = 1;


}
enum CourseDocType{

  video = 0;

  doc = 1;
}


message CourseAddReq{

  // 课程类型
  CourseDocType course_type = 1;

  // 就是课程序号，排序优先级
  int32 sort_priority = 2;

  // 课程标题
  string doc_title = 3;

  // 课程摘要
  string doc_summary = 4;

  // 课程内容
  string doc_content = 5;

  // 是否可见
  bool is_show = 6;

  // 目录id，注意目前业务上只有二级目录才能添加课程
  int64 parent_directory_id = 7;

  // 无论视频文本课程都需要， 课程额外信息, 封面
  string cover_url = 8;

  // 无论视频文本课程都需要， 课程额外信息, 封面md5
  string cover_md5 = 9;

  // 视频课程需要，avid
  int64 avid = 10;

  // 视频课程需要， bvid
  string bvid = 11;

  // 视频课程需要， 视频标题
  string video_title = 12;

  // 视频课程需要， 视频时长, 秒
  int32 video_duration = 13;

  // 视频课程需要， up主id
  int64 up_mid = 14;

  // 作者，这里直接复用为课程讲师
  string author = 15;

  bool jump_enabled = 16;

  string jump_url = 17;

}


message CourseDeleteReq{

  // 课程id
  int64 course_id = 1;
}


message CourseUpdateReq{

  // 课程id
  int64 course_id = 1;

  // 课程类型
  CourseDocType course_type = 2;

  // 就是课程序号，排序优先级
  google.protobuf.Int32Value sort_priority = 3;

  // 课程标题
  google.protobuf.StringValue doc_title = 4;

  // 课程摘要
  google.protobuf.StringValue doc_summary = 5;

  // 课程内容
  google.protobuf.StringValue doc_content = 6;

  // 是否可见
  google.protobuf.BoolValue is_show = 7;

  // 目录id，注意目前业务上只有二级目录才能添加课程
  google.protobuf.Int64Value parent_directory_id = 8;

  // 无论视频文本课程都需要， 课程额外信息, 封面
  google.protobuf.StringValue cover_url = 9;

  // 无论视频文本课程都需要， 课程额外信息, 封面md5
  google.protobuf.StringValue cover_md5 = 10;

  // 视频课程需要，avid
  google.protobuf.Int64Value avid = 11;

  // 视频课程需要， bvid
  google.protobuf.StringValue bvid = 12;

  // 视频课程需要， 视频标题
  google.protobuf.StringValue video_title = 13;

  // 视频课程需要， 视频时长, 秒
  google.protobuf.Int32Value video_duration = 14;

  // 视频课程需要， up主id
  google.protobuf.Int64Value up_mid = 15;

  // 作者，这里直接复用为课程讲师
  google.protobuf.StringValue author = 16;

  google.protobuf.BoolValue jump_enabled = 17;

  google.protobuf.StringValue jump_url = 18;

}




