syntax = "proto3";

import "google/protobuf/empty.proto";
package ad.mgk.business_tool;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/mgk.business_tool";
option java_package = "com.bapis.ad.mgk.business_tool";
option java_multiple_files = true;

// appid: sycpb.cpm.mgk-portal
service MgkBusinessToolService {
  //查询建站落地页投放用信息
  rpc GetLaunchPageByPage(LandingPagesReq) returns (LaunchPageByPageReply);
  //删除落地页
  rpc Disable(HandlePagesReq) returns (.google.protobuf.Empty);
  //查询建站落地页汇总数据
  rpc GetPageSumData(PageWithTimeReq) returns (PageFormSumDataReply);
  //分页查询建站落地页回收数据
  rpc GetPageDataByPage(PageWithTimePageReq) returns (PageFormDataPageReply);
  //查询建站落地页回收数据用于下载
  rpc GetPageData(PageWithTimeReq) returns (PageFormDataReply);

  //查询获客链接授权链接
  rpc GetCustomerAcqAuthLink(HandleCustomerAcqLinkAuthReq) returns (CustomerAcqLinkAuthReply);
  //不分页查询获客链接信息
  rpc GetCustomerAcqLink(CustomerAcqLinkReq) returns (CustomerAcqLinkListReply);
  //分页查询获客链接信息
  rpc GetCustomerAcqLinkByPage(CustomerAcqLinkPageReq) returns (CustomerAcqLinkPageReply);
  //查询获客链接汇总数据
  rpc GetCustomerAcqLinkSumData(CustomerAcqLinkWithTimeReq) returns (CustomerAcqLinkSumDataReply);
  //分页查询获客链接回收数据
  rpc GetCustomerAcqLinkDataByPage(CustomerAcqLinkWithTimePageReq) returns (CustomerAcqLinkDataPageReply);
  //查询获客链接回收数据用于下载
  rpc GetCustomerAcqLinkData(CustomerAcqLinkWithTimeReq) returns (CustomerAcqLinkDataReply);
  //创建在线咨询组件
  rpc CreateOnlineConsultComponent(CreateOnlineConsultComponentReq) returns (CreateOnlineConsultComponentReply);
  //查询在线咨询组件
  rpc QueryOnlineConsultComponentList(QueryOnlineConsultComponentReq) returns (QueryOnlineConsultComponentReply);
  //增加经营助手权限账号标签 678
  rpc addAllowLoginBusinessToolMgkLabel(AccountReq) returns (BusinessToolMgkLabelReply);
  // 根据tool_id 查询第三方落地页
  rpc getThirdPartyLandingPageByPageId(QueryThirdPartyLandingPageByPageIdsReq) returns (QueryThirdPartyLandingPageByPageIdsReply);
  // 给账号添加经营号工具标签
  rpc addBusinessToolLabel(AddBusinessToolLabelReq) returns (AddBusinessToolLabelReply);
  // 查询账号对应的客户名称
  rpc queryAccountCustomerName(QueryAccountCustomerNameReq) returns (QueryAccountCustomerNameReply);

  rpc addAccountAwakenApp(AddAccountAwakenAppReq) returns (AddAccountAwakenAppReply);

  rpc deleteAccountAwakenApp(DeleteAccountAwakenAppReq) returns (DeleteAccountAwakenAppReply);

  rpc queryAccountAwakenApp(QueryAccountAwakenAppReq) returns (QueryAccountAwakenAppReply);

  rpc queryAccountsAwakenApp(QueryAccountsAwakenAppReq) returns (QueryAccountsAwakenAppReply);
}


enum MgkPageStatus {// 落地页状态
  MGK_PAGE_STATUS_UNKNOWN = 0; //  未知
  MGK_PAGE_STATUS_UNPUBLISHED = 1; // 未发布
  MGK_PAGE_STATUS_PUBLISHED = 2; // 已发布
  MGK_PAGE_STATUS_DOWNLINE = 3; // 已下线
  MGK_PAGE_STATUS_ADMIN_REJECT = 4; // 管理员驳回
  MGK_PAGE_STATUS_DELETED = 5; // 已删除
  MGK_PAGE_STATUS_WAIT_AUDIT = 6; // 待审核
  MGK_PAGE_STATUS_AUDIT_REJECT = 7; // 审核拒绝
}

message LandingPagesReq {
  string name_like = 1;
  repeated int32 account_id = 2;
  repeated int64 mgk_page_id = 3;
  repeated int32 status = 4;
  int32 page = 5;
  int32 size = 6;
  repeated int32 template_style_list = 7;
}

message PageReq {
  int64 mgk_page_id = 1;
}

message PageWithTimeReq {
  int64 mgk_page_id = 1;
  int64 start_time = 2;
  int64 end_time = 3;
  int32 account_id = 4;
}

message PageWithTimePageReq {
  int64 mgk_page_id = 1;
  int64 start_time = 2;
  int64 end_time = 3;
  int32 account_id = 4;
  int32 page = 5;
  int32 size = 6;
}

message HandlePagesReq {
  repeated int64 mgk_page_id = 1;
  int32 account_id = 2;
  //操作人姓名
  string operator_name = 3;
}

message LaunchPageReq {
  repeated int32 account_id = 1;
  repeated int64 page_id = 2;
  repeated int32 template_style = 3;
  repeated int32 status = 4;
  repeated int32 type = 5;
  repeated int32 is_video_page = 6;
  int32 has_dpa_goods = 7;
  string name = 8;
}

message LaunchPageReply {
  repeated LaunchPage page = 1;
}

message LaunchPageByPageReply {
  repeated LaunchPage page = 1;
  int32 total = 2;
}

message LaunchPage {
  int32 account_id = 1;
  int64 page_id = 2;
  string name = 3;
  string title = 4;
  string page_cover = 5;
  string reason = 6;
  int32 status = 7;
  string status_desc = 8;
  //投放链接
  string launch_url = 9;
  int64 mtime = 10;
  int64 ctime = 11;
  //是否包含待审核的版本
  bool contain_auditing_version = 12;
  int32 template_style = 13;
}

message PageFormSumDataReply {
  //表单提交数
  int32 form_submit_count = 1;
}

message PageFormDataPageReply {
  int64 total = 1;
  repeated PageFormItemDataReply data_replies = 2;
  repeated string headers = 3;
}

message PageFormDataReply {
  repeated string headers = 1;
  repeated PageFormItemDataReply data_replies = 2;
}

message PageFormItemDataReply {
  int64 page_id = 1;
  int64 mtime = 2;
  //表单项值
  repeated string values = 3;
}


message CustomerAcqLinkPageReq {
  string name_like = 1;
  int32 account_id = 2;
  //
  int32 status = 3;
  int32 page = 4;
  int32 size = 5;
  repeated int64 ids = 6;
}

message CustomerAcqLinkReq {
  string name_like = 1;
  int32 account_id = 2;
  //
  int32 status = 3;
  repeated int64 ids = 4;
  repeated string link_ids = 5;
}

message HandleCustomerAcqLinkAuthReq {
  int32 account_id = 1;
}

message CustomerAcqLinkAuthReply {
  //授权链接
  string auth_url = 1;
  string account_name = 2;
}

message CustomerAcqLinkPageReply {
  repeated CustomerAcqLinkReply replies = 1;
  int32 total = 2;
}

message CustomerAcqLinkListReply {
  repeated CustomerAcqLinkReply replies = 1;
}

message CustomerAcqLinkReply {
  int64 id = 1;
  string link_id = 2;
  string link_name = 3;
  int64 create_time = 4;
  string link_url = 5;
  int32 status = 6;
}

message CustomerAcqLinkWithTimePageReq {
  int64 id = 1;
  int64 start_time = 2;
  int64 end_time = 3;
  int32 page = 4;
  int32 size = 5;
  int32 account_id = 6;
}

message CustomerAcqLinkWithTimeReq {
  int64 id = 1;
  int64 start_time = 2;
  int64 end_time = 3;
  int32 account_id = 4;
}

enum CustomerAcqLinkStatus{
  CUSTOMER_ACQ_LINK_STATUS_UNKNOWN = 0;
  CUSTOMER_ACQ_LINK_STATUS_VALID = 1;
  CUSTOMER_ACQ_LINK_STATUS_INVALID = 2;
}

message CustomerAcqLinkSumDataReply {
  //涨粉数
  int32 add_fans = 1;
  //开口数
  int32 wx_chat = 2;
}

message CustomerAcqLinkDataReply {
  repeated string headers = 1;
  repeated CustomerAcqLinkDataItemReply replies = 2;
}

message CustomerAcqLinkDataPageReply {
  repeated string headers = 1;
  repeated CustomerAcqLinkDataItemReply replies = 2;
  int64 total = 3;
}

message CustomerAcqLinkDataItemReply {
  //涨粉或者开口
  string data_type_desc = 1;
  int64 mtime = 2;
}

message AccountReq{
  int32 account_id = 1;
}

message BusinessToolMgkLabelReply{
  int32 code = 1;
  string msg = 2;
}

message CreateOnlineConsultComponentReq{
  int64 account_id = 1;
  int64 mid = 2;
  string content = 3;
}

message CreateOnlineConsultComponentReply{
  int64 id = 1;
  int64 account_id = 2;
  string content = 3;
  int64 tool_id = 4;
  int32 status = 5;
  string ctime = 6;
  string mtime = 7;
}

message QueryOnlineConsultComponentReq{
  int64 account_id = 1;
}

message QueryOnlineConsultComponentReply{
  repeated CreateOnlineConsultComponentReply online_consult_component_list = 1;
}

message QueryThirdPartyLandingPageByPageIdsReq{
  repeated int64 page_ids = 1;
}

message QueryThirdPartyLandingPageByPageIdsReply{
  repeated QueryThirdPartyLandingPageByPageIdInfo third_party_landing_page_infos = 1;
}

message QueryThirdPartyLandingPageByPageIdInfo{
  int32 account = 1;
  int64 page_id = 2 ;
  string page_name = 3;
  string page_url = 4;
  string creator = 5;
}

message AddBusinessToolLabelReq{
  int32 account_id = 1;
  int32 label_id = 2;
}

message AddBusinessToolLabelReply{
  int32 code = 1;
  string msg = 2;
}

message QueryAccountCustomerNameReq{
  repeated int32 account_id = 1;
}

message QueryAccountCustomerNameReply{
  repeated QueryAccountCustomerNameSingleInfo infos = 1;
}

message QueryAccountCustomerNameSingleInfo{
  int32 account_id = 1;
  string customer_name = 2;
}

message AddAccountAwakenAppReq{
  int32 account_id = 1;
  int32 app_id = 2;
}

message AddAccountAwakenAppReply{
  int32 code = 1;
  string msg = 2;
}

message DeleteAccountAwakenAppReq{
  int32 account_id = 1;
  int32 app_id = 2;
}

message DeleteAccountAwakenAppReply{
  int32 code = 1;
  string msg = 2;
}

message QueryAccountAwakenAppReq{
  int32 account_id = 1;
}

message QueryAccountAwakenAppReply{
  repeated int32 app_ids = 1;
}

message QueryAccountsAwakenAppReq{
  repeated int32 account_ids = 1;
}

message QueryAccountsAwakenAppSingleInfo{
  int32 account_id = 1;
  repeated int32 app_ids = 2;
}

message QueryAccountsAwakenAppReply{
  repeated QueryAccountsAwakenAppSingleInfo infos = 1;
}