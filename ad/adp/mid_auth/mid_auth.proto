syntax = "proto3";
package ad.adp.mid_auth;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/adp.mid_auth";
option java_package = "com.bapis.ad.adp.mid_auth";
option java_multiple_files = true;

message AccountMid {
  int64 accountId = 1;
  int64 mid = 2;
}

message BatchBindAccountsWithMidsReq {
  repeated AccountMid accountMidList = 1;
  string userName = 2;
  string excelUrl = 3;
}

message BatchBindAccountsWithMidsResp {}


message ListBindAccountsMidsReq {
  int64 accountId = 1;
  int64 mid = 2;

  int32 page = 10;
  int32 pageSize = 11;
}

message AccountMidMappingInfo {
  int64 id = 1;
  int64 accountId = 2;
  int64 mid = 3;
  string name = 4;
  int64 ctime = 5;
  string userName = 6;
  string accountName = 7;
}

message ListBindAccountsMidsResp {
  repeated AccountMidMappingInfo bindInfos = 1;
  int32 page = 2;
  int32 pageSize = 3;
  int32 total = 4;
}

message BatchUnbindAccountsMidsReq {
  repeated int64 ids = 1;
  string userName = 2;
}

message BatchUnbindAccountsMidsResp {}

service AdpMidAuthService {
  rpc ListBindAccountsMids(ListBindAccountsMidsReq) returns(ListBindAccountsMidsResp);
  rpc BatchBindAccountsWithMids(BatchBindAccountsWithMidsReq) returns (BatchBindAccountsWithMidsResp);
  rpc BatchUnbindAccountsMids(BatchUnbindAccountsMidsReq) returns (BatchUnbindAccountsMidsResp);
}