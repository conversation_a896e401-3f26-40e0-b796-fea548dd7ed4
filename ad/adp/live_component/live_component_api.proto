syntax = "proto3";
package ad.adp.live_component;

import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/adp.live_component;api";
option java_package = "com.bapis.ad.adp.live_component";
option java_multiple_files = true;
option (wdcli.appid) = "sycpb.cpm.cpm-adp";

// appid: sycpb.cpm.cpm-adp
//直播卡转化组件
service LiveComponentService {

  // 新增直播转化组件
  rpc AddLiveComponents(AddLiveComponentsReq)returns(AddLiveComponentReply);

  //解绑转化组件
  rpc DisableComponent(DeleteLiveComponentReq)returns(DeleteLiveComponentReply);

}

message AddLiveComponentsReq {
  // 必传
  int64 room_id = 1;
  //uid
  int64 uid = 2;
  //场景
  string scene = 3;
  //图片url
  string image_url = 4;
  //场景
  string title = 5;
  //工具类型
  int32 tool_type = 6;
  //工具id
  string tool_id = 7;

  //账户id
  int32 account_id = 8;

  //在线预约站内外类型 1-站内 2-站外
  int32 booking_outer_type = 9;

  //按钮文案
  string button_text = 10;

  //营销组件的类型
  int32 business_tool_type = 11;

  string sub_title = 12;
}

message AddLiveComponentReply {
  //创意id
  int64 creative_id = 1;
  // 是否创建成功
  bool success = 2;
  // msg
  string msg = 3;
}

message DeleteLiveComponentReq {
  int64 uid = 1;
  int64 creative_id = 2;
  //场景
  string scene = 3;
}

message DeleteLiveComponentReply {
  //创意id
  int64 creative_id = 1;
  //账户id
  int32 account_id = 2;
  // 是否创建成功
  bool success = 3;
  // msg
  string msg = 4;
}

// 经营工具类型
enum ToolTypeEnum {
  //无意义
  TOOL_TYPE_ENUM_UNSPECIFIED = 0;
  //落地页
  TOOL_TYPE_ENUM_LANDING_PAGE = 1;
}

// 绑定的营销组件类型
enum BusinessToolTypeEnum{
  // 在线预约
  BOOKING = 0;

  //企业微信
  WORK_WECHAT = 1;

  //小程序
  APPLETS = 2;

  //在线咨询
  ONLINE_CONSULT = 3;

  //第三方落地页
  THIRD_PARTY_LANDING_PAGE_URL = 4;

  //应用推广
  APP_PACKAGE = 5;
}