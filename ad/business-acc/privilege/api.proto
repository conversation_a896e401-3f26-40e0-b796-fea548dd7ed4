syntax = "proto3";

package ad.business.acc.clue;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/business.acc.privilege;api";
option java_package = "com.bapis.ad.business.acc.privilege";
option java_multiple_files = true;

import "extension/wdcli/wdcli.proto";
option (wdcli.appid) = "mall.c.business-account";

service PrivilegeService {
  //查询经营号权益白名单列表
  rpc QueryPrivilegeList(QueryPrivilegeListReq) returns (QueryPrivilegeListReply);
  //添加经营号权益白名单
  rpc AddPrivilege(AddPrivilegeReq) returns (AddPrivilegeReply);
  //删除经营号权益白名单
  rpc DeletePrivilege(DeletePrivilegeReq) returns (DeletePrivilegeReply);
}

message QueryPrivilegeListReq {
  repeated int64 mid_list = 1;
  int64 start_time = 2;
  int64 end_time = 3;
  int32 page_num = 4;
  int32 page_size = 5;
}

message QueryPrivilegeListReply {
  repeated PrivilegeInfo list = 1;
  int32 total = 2;
}

message PrivilegeInfo {
  int64 mid = 1;
  string mid_name = 2;
  int32 role_type = 3;
  int32 ad_id = 4;
  string customer_name = 5;
  string first_industry_name = 6;
  string second_industry_name = 7;
  string third_industry_name = 8;
  string operator = 9;
  string role_type_name = 10;
  int64 ctime = 11;
}

message AddPrivilegeReq {
  repeated int64 mid = 1;
  string operator = 2;
}

message AddPrivilegeReply {
  repeated AddPrivilegeSingleReply replies = 1;
}

message AddPrivilegeSingleReply{
  repeated int64 mid = 1;
  string msg = 2;
}

message DeletePrivilegeReq {
  repeated int64 mid_list = 1;
  string operator = 2;
}

message DeletePrivilegeReply {
  int32 code = 1;
  string msg = 2;
}