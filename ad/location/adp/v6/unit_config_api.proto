syntax = "proto3";
package ad.location.adp.v6;

import "ad/location/adp/config_input.proto";
import "ad/pandora/resource/unit_api.proto";
import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "sycpb.cpm.cpm-location";
option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/location.adp.v6;api";
option java_package = "com.bapis.ad.location.adp.v6";
option java_multiple_files = true;

service UnitConfigService {
    rpc UnitPpcConfig(UnitPpcConfigReq) returns(UnitPpcConfigReply);
    rpc UnitContentConfig(UnitContentConfigReq) returns(UnitContentConfigReply);
    rpc UnitCpaTargetConfig(UnitCpaTargetConfigReq) returns(UnitCpaTargetConfigReply);
    rpc UnitDeepCpaTargetConfig(UnitDeepCpaTargetConfigReq) returns(UnitDeepCpaTargetConfigReply);
    rpc UnitAssistCpaTargetConfig(UnitAssistCpaTargetConfigReq) returns(UnitAssistCpaTargetConfigReply);
    rpc UnitTwoStageMinBidConfig(UnitTwoStageMinBidConfigReq) returns(UnitTwoStageMinBidConfigReply);
    rpc UnitCpaTargetAttrConfig(UnitCpaTargetAttrConfigReq) returns(UnitCpaTargetAttrConfigReply);
    rpc UnitLimitByCostConfig(UnitLimitByCostConfigReq) returns(UnitLimitByCostConfigReply);
    rpc UnitLimitByLabelConfig(UnitLimitByLabelConfigReq) returns(UnitLimitByLabelConfigReply);
}

message UnitPpcConfigReq {

}

message UnitPpcConfigReply {
    repeated UnitPpcConfigEntity entity = 1;
}

message UnitPpcConfigEntity {
    ConfigInput input = 1;
    pandora.resource.PromotionContentTypeInfo output = 2;
}

message UnitContentConfigReq {

}

message UnitContentConfigReply {
    repeated UnitContentConfigEntity entity = 1;
}

message UnitContentConfigEntity {
    ConfigInput input = 1;
    pandora.resource.UnitContentConfigInfo output = 2;
}

// 浅层优化目标
message UnitCpaTargetConfigReq {

}

message UnitCpaTargetConfigReply {
    repeated UnitCpaTargetConfigEntity entity = 1;
}

message UnitCpaTargetConfigEntity {
    ConfigInput input = 1;
    UnitCpaTargetConfigOutput output = 2;
}

message UnitCpaTargetConfigOutput {
    int32 cpa_target = 1;
    string cpa_target_code = 2;
    string cpa_target_name = 3;
    string cpa_target_desc = 4;
    int32 support_no_bid = 5;
}

// 深层优化目标
message UnitDeepCpaTargetConfigReq {

}


message UnitDeepCpaTargetConfigReply {
    repeated UnitDeepCpaTargetConfigEntity entity = 1;
}


message UnitDeepCpaTargetConfigEntity {
    ConfigInput input = 1;
    UnitDeepCpaTargetConfigOutput output = 2;
}

message UnitDeepCpaTargetConfigOutput {
    int32 deep_cpa_target = 1;
    string deep_cpa_target_code = 2;
    string deep_cpa_target_name = 3;
    string deep_cpa_target_desc = 4;
    int32 support_no_bid = 5;
}

// 辅助探索优化目标
message UnitAssistCpaTargetConfigReq {

}


message UnitAssistCpaTargetConfigReply {
    repeated UnitAssistCpaTargetConfigEntity entity = 1;
}


message UnitAssistCpaTargetConfigEntity {
    ConfigInput input = 1;
    UnitAssistCpaTargetConfigOutput output = 2;
}

message UnitAssistCpaTargetConfigOutput {
    int32 assist_cpa_target = 1;
    string assist_cpa_target_code = 2;
    string assist_cpa_target_name = 3;
    string assist_cpa_target_desc = 4;
    int32 support_no_bid = 5;
}

message UnitTwoStageMinBidConfigReq {

}

message UnitTwoStageMinBidConfigReply {
    repeated UnitTwoStageMinBidConfigEntity entity = 1;
}

message UnitTwoStageMinBidConfigEntity {
    ConfigInput input = 1;
    pandora.resource.CpaTargetBidConfigInfo output = 2;
}

message UnitCpaTargetAttrConfigReq {

}

message UnitCpaTargetAttrConfigReply {
    repeated UnitCpaTargetAttrConfigEntity entity = 1;
}

message UnitCpaTargetAttrConfigEntity {
    ConfigInput input = 1;
    pandora.resource.CpaTargetAttr output = 2;
}

message UnitLimitByCostConfigReq {

}

message UnitLimitByCostConfigReply {
    repeated UnitLimitByCostConfigEntity entity = 1;
}

message UnitLimitByCostConfigInput {
    double account_min_cost = 1;
    double account_max_cost = 2;
}

message UnitLimitByCostConfigEntity {
    UnitLimitByCostConfigInput input = 1;
    pandora.resource.UnitLimitInfo output = 2;
}

message UnitLimitByLabelConfigReq {

}

message UnitLimitByLabelConfigReply {
    repeated UnitLimitByLabelConfigEntity entity = 1;
}

message UnitLimitByLabelConfigEntity {
    ConfigInput input = 1;
    pandora.resource.UnitLimitInfo output = 2;
}