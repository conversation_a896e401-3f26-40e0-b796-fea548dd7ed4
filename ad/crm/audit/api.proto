syntax = "proto3";
package ad.crm.audit;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/crm.audit;api";
option java_package = "com.bapis.ad.crm.audit";
option java_multiple_files = true;

// appid: sycpb.cpm.crm-portal
service CrmAuditWorkOrderService {
    // 个人起飞账号创建资质审核接口
    rpc CreateQualificationAudit(QualificationAuditReq) returns (QualificationAuditResp);
    // 查询个人起飞账号上传的资质接口
    rpc QueryQualificationInfo(QueryQualificationInfoReq) returns (QueryQualificationInfoResp);
    // 更新个人起飞账号资质审核
    rpc UpdateQualificationAudit(QualificationAuditReq) returns (QualificationAuditResp);
    // 删除个人起飞资质信息
    rpc DeleteQualificationInfo(QualificationInfoReq) returns (QualificationInfoResp);
}

message QualificationInfoReq {
    int64 mid  = 1;
}

message QualificationInfoResp {
    int32 code = 1;
    string message = 2;
    bool result = 3;
}

message QueryQualificationInfoReq {
    int64 mid  = 1;
    bool isNeedSnapshot = 2;
}

message QueryQualificationInfoResp {
    int32 code = 1;
    string message = 2;
    QualificationInfo qualificationInfo = 3;
}


message QualificationAuditReq {
    // 个人起飞id
    int64 mid  = 1;
    // 是否需要过审核 true 需要过审核 false 不需要过审核。
    bool needAudit = 2;
    // 资质信息
    QualificationInfo qualificationInfo = 3;

}

message QualificationAuditResp {
    int32 code = 1;
    string message = 2;
    bool result = 3;
}

message QualificationInfo {
    // 运营者身份证姓名
    string operation_name = 1;
    // 运营者手机号归属地
    string operation_number_addr = 2;
    // 运营者手机号
    string operation_number = 3;
    // 联系邮箱
    string email = 4;
    // 所在地址
    string address = 5;
    // 企业全称
    string enterprise_name = 6;
    // 企业一级分类
    string enterprise_first_industry = 7;
    // 企业二级分类
    string enterprise_second_industry = 8;
    // 机构类型
    string organization_type = 9;
    // 企业规模
    string enterprise_scale = 10;
    // 注册资金
    string registered_capital = 11;
    // 统一社会信用代码
    string business_licence_code = 12;
    // 官网地址
    string official_website = 13;
    // 事业单位证明/营业执照
    repeated string business_licence_picture = 14;
    // 机构认证申请公函
    repeated string organization_register_picture = 15;
    // 补充文件
    repeated string supplement_picture = 16;
    // 主体资质分类
    int32 qualification_type_id = 17;
    // 营业执照到期时间
    int64 business_licence_expire_date = 18;
    // 是否存在客户资质，0-不存在，1-存在
    int32 is_exists = 19;
}
