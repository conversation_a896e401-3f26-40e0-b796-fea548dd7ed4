syntax = "proto3";
package ad.crm.category;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/crm.break_tape;api";
option java_package = "com.bapis.ad.crm.break_tape";
option java_multiple_files = true;

//appid:sycpb.cpm.crm-portal
service BreakTapeService {
  // 刷新三连撞线信息
  rpc refreshBreakTape(BreakTapeQueryReq) returns (BreakTapeReply);

}

message BreakTapeQueryReq{
  int64 begin_time = 1; // 开始时间
  int64 end_time = 2; // 结束时间
}

message BreakTapeReply{
  int32 code = 1;
  string message = 2;
}
