syntax = "proto3";
package ad.crm.resource;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/crm.department;api";
option java_package = "com.bapis.ad.crm.department";
option java_multiple_files = true;
service DepartmentServiceApi {
  //  查询部门信息
  rpc QueryDepartment (QueryDepartmentRequest) returns (QueryDepartmentResponse) {}
}
message QueryDepartmentRequest{
  repeated int32 department_id = 1;
}
message QueryDepartmentResponse{
  int32 code = 1;
  string message = 2;
  repeated DepartmentDetail data = 3;
}
message DepartmentDetail{
  int32 department_id = 2;
  string department_name = 3;
}