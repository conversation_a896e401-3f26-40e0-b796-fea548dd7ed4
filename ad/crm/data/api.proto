syntax = "proto3";
package ad.crm.data;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/crm.data;api";
option java_package = "com.bapis.ad.crm.data";
option java_multiple_files = true;

// appid: sycpb.cpm.crm-portal
service CrmDataService {
    // 查询账户投放数据
    rpc QueryLaunchDataWithPage(LaunchDataPageReq) returns (LaunchDataPageReply);
}

message LaunchDataPageReq {
    // 账号id
    int32 account_id = 1;
    // 汇总方式 1-按天 2-按月 3-全部 4-按周
    int32 group_type = 2;
    // 售卖类型
    repeated int32 sales_type = 3;
    // 开始时间
    int64 from_time = 4;
    // 结束时间
    int64 to_time = 5;
    // 排序字段
    string sort_field = 6;
    // 排序方式
    int32 sort_type = 7;
    // 分页
    int32 page = 8;
    // 分页大小
    int32 page_size = 9;
}

message LaunchDataPageReply {
    int32 total = 1;
    repeated LaunchDataEntity record = 2;
}

message LaunchDataEntity {
    // 日期范围 字符串
    string date = 1;
    // 广告主账户ID
    int32 account_id = 2;
    // 广告主账户名称
    string account_name = 3;
    // 广告主客户id
    int32 account_customer_id = 4;
    // 广告主客户名称
    string account_customer_name = 5;
    // 代理商客户id
    int32 dependency_agent_customer_id = 6;
    // 代理商客户名称
    string dependency_agent_customer_name = 7;
    // 代理商名称
    string dependency_agent_name = 8;
    // 产品名称
    string product_name = 9;
    // 售卖类型
    int32 sales_type = 10;
    // 售卖类型描述
    string sales_type_desc = 11;
    // 现金总消耗(厘)
    int64 total_cash_consume_milli = 12;
    // 返货总消耗(厘)
    int64 total_red_packet_consume_milli = 13;
    // 专项返货总消耗(厘)
    int64 total_special_red_packet_consume_milli = 14;
    // 整体总消耗(厘)
    int64 total_consume_milli = 15;
    // 三连总消耗(厘)
    int64 san_lian_launch_total_consume_milli = 16;
    // 线索通实扣(厘)
    int64 clue_pass_deduct = 17;
    // 曝光量
    int64 show_count = 18;
    // 点击量
    int64 click_count = 19;
    // CTR 百分比x1000
    int64 click_rate = 20;
    // CPC(厘)
    int64 cost_per_click = 21;
    // ECPM(厘)
    int64 average_cost_per_thousand = 22;
    // 粉丝增长数
    int64 fan_follow_count = 23;
    // 涨粉成本
    int64 fans_increase_cost = 24;
}