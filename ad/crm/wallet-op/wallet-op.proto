syntax = "proto3";

package ad.crm.walletop;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/crm.walletop;api";
option java_package = "com.bapis.ad.crm.wallet.op";
option java_multiple_files = true;



service CrmWalletOpService {

  rpc queryWalletByAccIds(CrmWalletReq) returns (CrmWalletBaseReply);

  rpc backRedPacket(CommReq) returns (CommonBaseReply);

  rpc deductRedPacket(CommReq) returns (CommonBaseReply);

  rpc refundRedPacket(CommReq) returns (CommonBaseReply);

  rpc trustFundRecharge(CommReq) returns (CommonBaseReply);

  rpc trustFundDeduct(CommReq) returns (CommonBaseReply);

  rpc withHoldFundRecharge(CommReq) returns (CommonBaseReply);

  rpc withHoldFundDeduct(CommReq) returns (CommonBaseReply);

}

message CrmWalletReq{
  repeated int32 account_ids = 1;
}

message CrmWalletBaseReply{
  int32 code = 1;
  string message = 2;
  repeated CrmWalletDto data = 3;
}

message CrmWalletDto{

  //账号ID
  int32 accountId = 1;
  //现金(单位分）
  int64 cash = 2;
  // 返货 (单位分）
  int64 red_packet = 3;
  //专返 (单位分）
  int64 special_red_packet = 4;

  // 起飞-现金-托管-余额（单位分）
  int64 trust_cash = 5;

  // 签约-托管余额（单位分）
  int64 trust_fund = 6;

  //签约-订单余额（单位分）
  int64 withhold_fund = 7;

  //起飞-激励金-托管-余额（单位分）
  int64 trust_incentive = 8;

  //起飞-起飞币-托管-余额（单位分）
  int64 trust_fly_coin = 9;


}


message CommonBaseReply{
  int32 code = 1;
  string message = 2;
}

message CommReq{
  string systemType = 1;
  int32 accountId = 2;
  int64 amount = 3;
  string serialNumber = 4;
  int32 backPolicyType = 5;
}
