syntax = "proto3";
package ad.product.banner;

import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/product.banner;api";
option java_package = "com.bapis.ad.product.banner";
option java_multiple_files = true;
option (wdcli.appid) = "sycpb.cpm.cpm-product-portal";

service BannerService {
  // 新增banner
  rpc insertBanner(UpsertBannerReq) returns (BannerResp);
  // 更新banner
  rpc updateBanner(UpsertBannerReq) returns (BannerResp);
  // 根据id删除单个banner
  rpc deleteBanner(SingleBannerReq) returns (BannerResp);
  // 根据id查询单个banner详情
  rpc getBanner(SingleBannerReq) returns (BannerDetailResp);
  // 分页查询banner列表
  rpc listBanners(BannerPageReq) returns (BannerPageResp);
}

message UpsertBannerReq {
  // id
  int32 id = 1;
  // 名称
  string name = 2;
  // 图片链接
  string image_url = 3;
  // 跳转链接
  string jump_url = 4;
  // 生效开始时间戳
  int64 start_time = 5;
  // 生效结束时间戳
  int64 end_time = 6;
  // 顺序
  int32 sort = 7;
  // 业务类型
  int32 business_type = 8;
  // 操作人
  string operator = 9;
}

message SingleBannerReq {
  // banner id
  int32 id = 1;
}

message BannerResp {
  // 返回码
  int32 code = 1;
  // 是否成功
  bool success = 2;
  // 错误信息
  string message = 3;
}

message BannerDetailResp {
  BannerDto banner = 1;
}

message BannerPageReq {
  // 名称，模糊查询
  string name_like = 1;
  // 业务类型
  int32 business_type = 2;
  // 状态
  BannerStatus status = 3;
  // 页号
  int32 page = 4;
  // 分页大小
  int32 size = 5;
  // 是否按照展示顺序排序
  bool sort = 6;
}

message BannerPageResp {
  // 总数
  int32 total = 1;
  // banner列表
  repeated BannerDto records = 2;
}

enum BannerStatus {
  // 未知
  UNKNOWN = 0;
  // 待生效
  TO_BE_EFFECTIVE = 1;
  // 已生效
  EFFECTIVE = 2;
  // 已过期
  EXPIRED = 3;
}

message BannerDto {
  // id
  int32 id = 1;
  // 名称
  string name = 2;
  // 图片链接
  string image_url = 3;
  // 跳转链接
  string jump_url = 4;
  // 生效开始时间戳
  int64 start_time = 5;
  // 生效结束时间戳
  int64 end_time = 6;
  // 状态
  BannerStatus status = 7;
  // 顺序
  int32 sort = 8;
  // 业务类型
  int32 business_type = 9;
  // 操作人
  string operator = 10;
  // 创建时间戳
  int64 ctime = 11;
  // 更新时间戳
  int64 mtime = 12;
}