syntax = "proto3";
import "extension/wdcli/wdcli.proto";

package ad.cmc.goods_exchange;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/cmc.goods_exchange;api";
option java_package = "com.bapis.ad.cmc.goods_exchange";
option java_multiple_files = true;
option (wdcli.appid) = "sycpb.cpm.tavern-platform";
service GoodsExchange {

  //添加商品替换配置
  rpc addExchange(GoodsExchangeProcessReq) returns (GoodsExchangeProcessResp);

  //更新商品替换配置
  rpc editExchange(GoodsExchangeProcessReq) returns (GoodsExchangeProcessResp);

  //删除商品替换配置
  rpc deleteExchange(GoodsExchangeProcessReq) returns (GoodsExchangeProcessResp);

  //查询商品替换配置
  rpc queryExchange(GoodsExchangeQueryReq) returns (GoodsExchangeQueryResp);

  //商品链接识别
  rpc distinguishGoodsUrls(GoodsUrlReq) returns (GoodsUrlDistinctResp);

}

message GoodsUrlDistinctResp {
  GoodsUrlDistinctResult results = 1;
}

message GoodsUrlDistinctResult {
  //商品id
  string itemId = 1;
  //商品来源
  int32 sourceType = 2;
  //商品url
  string goodsUrl = 3;

  string message = 4;
}

message GoodsUrlReq {
  //商品url
  string goodsUrl = 1;
}

message GoodsExchangeProcessReq {
  int64 id = 1;
  //商品id
  int64 itemId = 2;
  //原目标id
  string origTargetId = 3;
  //现目标id
  string newTargetId = 4;
  //目标类型 1-稿件
  int32 targetType = 5;
  //百分比 10代表10%
  int32 expPercent = 6;
  //开始时间
  string beginTime = 7;
  //结束时间
  string endTime = 8;
  //操作人
  string operatorName = 9;
  //现商品id
  int64 newItemId = 10;
}

message GoodsExchangeProcessResp {
  int32 code = 1;
  string errorMessage = 2;
}

message GoodsExchangeQueryReq {
  int32 page = 1;
  int32 size = 2;
}

message GoodsExchangeQueryResp {
  int32 total = 1;
  repeated GoodsExchangeResult results = 2;
}

message GoodsExchangeResult {
  int64 id = 1;
  //商品id
  int64 itemId = 2;
  //原目标id
  string origTargetId = 3;
  //现目标id
  string newTargetId = 4;
  //目标类型 1-稿件
  int32 targetType = 5;
  //百分比 10代表10%
  int32 expPercent = 6;
  //开始时间
  string beginTime = 7;
  //结束时间
  string endTime = 8;
  //操作人
  string operatorName = 9;
  //提示文案
  string notice = 10;
  //商品链接
  string itemUrl = 11;
  //现商品id
  int64 newItemId = 12;
  //现商品链接
  string newItemUrl = 13;

}