syntax = "proto3";
package ad.scv.shield;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/scv.shield;api";
option java_package = "com.bapis.ad.scv.shield";
option java_multiple_files = true;

import "ad/audit/api.proto";

// appid: sycpb.cpm.scv
service ShieldService {
  // 查询
  rpc list(ShieldListReq)returns(ShieldResp);

  // 保存视频
  rpc saveShield(SaveShieldReq)returns(SaveShieldResp);

  // 编辑
  rpc update(UpdateShieldReq)returns(SaveShieldResp);

  // 删除
  rpc delete(UpdateShieldReq)returns(SaveShieldResp);

  // 修改上下线状态
  rpc UpdateStatus(UpdateStatusReq)returns(SaveShieldResp);

}

message ShieldListReq {
  int32 page = 1;
  int32 size = 2;
  string name = 3;
  string platform_name = 4;
  int32 av_scope = 5;
  repeated int64 av_scope_ids = 6;
  int32 shield_scope = 7;
  repeated int64 shield_scope_ids = 8;
  string operator_name = 9;
  int64 begin_time = 10;
  int64 end_time = 11;
  int32 scene_id = 12;
}

message ShieldResp {
  int32 total = 1;
  repeated ShieldDetails details = 2;
}

message ShieldDetails {
  int32 id = 1;
  int32 status = 2;
  string name = 3;
  repeated string platform_name = 4;
  int32 av_scope = 5;
  repeated int64 av_scope_ids = 6;
  string av_mapping_excel_url = 7;
  int32 shield_scope = 8;
  repeated int64 shield_scope_ids= 9;
  string shield_mapping_excel_url = 10;
  string operator_name = 11;
  int64 begin_time = 12;
  int64 end_time = 13;
  int64 ctime = 14;
  int64 mtime = 15;
  repeated int32 scene_id = 16;
  // 是否长期屏蔽 0-否 1-是
  int32 is_long_term_shield = 17;
}

message SaveShieldReq {
  int32 id = 1;
  int32 status = 2;
  string name = 3;
  repeated string platform_name = 4;
  int32 av_scope = 5;
  repeated int64 av_scope_ids = 6;
  string av_mapping_excel_url = 7;
  int32 shield_scope = 8;
  repeated int64 shield_scope_ids = 9;
  string shield_mapping_excel_url = 10;
  ad.audit.Operator operator = 11;
  int64 begin_time = 12;
  int64 end_time = 13;
  repeated int32 scene_id = 14;
  // 是否长期屏蔽 0-否 1-是
  int32 is_long_term_shield = 15;
}

message SaveShieldResp {
  int32 code = 1;
  string result = 2;
}

message UpdateShieldReq {
  int32 id = 1;
  int32 status = 2;
  string name = 3;
  repeated string platform_name = 4;
  int32 av_scope = 5;
  repeated int64 av_scope_ids = 6;
  string av_mapping_excel_url = 7;
  int32 shield_scope = 8;
  repeated int64 shield_scope_ids = 9;
  string shield_mapping_excel_url = 10;
  ad.audit.Operator operator = 11;
  int64 begin_time = 12;
  int64 end_time = 13;
  repeated int32 scene_id = 14;
  // 是否长期屏蔽 0-否 1-是
  int32 is_long_term_shield = 15;
}

message UpdateStatusReq {
  int32 id = 1;
  int32 status = 2; // 状态: 0-禁用,1-启用
  ad.audit.Operator operator = 3;
}