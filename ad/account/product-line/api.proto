syntax = "proto3";
package ad.account.product;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/account.product.line;api";
option java_package = "com.bapis.ad.account.product.line";
option java_multiple_files = true;

service AccountProductLineService {

  rpc QueryAccountProductLineByIds(QueryAccountProductLineReq) returns (QueryAccountProductLineResp);

}

message QueryAccountProductLineReq {
  repeated int32 product_line_ids = 1;
}

message QueryAccountProductLineResp {
  int32 code = 1;       // 0正确 其他异常
  string message = 2;   // 错误信息
  repeated ProductLineDto data = 3;
}

message ProductLineDto {
  int32 id = 1; // 产品线id
  int32 group_id = 2; // 集团id
  string name = 3; // 产品线名称
  string tag = 4; // 产品线标签
  int32 status = 5; // 状态：1-启用 2-禁用

}