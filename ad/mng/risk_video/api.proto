syntax = "proto3";
package ad.mng.risk_video;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/mng.risk_video;api";
option java_package = "com.bapis.ad.mng.risk_video";
option java_multiple_files = true;

service RiskVideoService{

  // 查询视频的风控信息
  rpc QueryRiskVideo(QueryRiskVideoReq) returns (QueryRiskVideoResp);
}

message QueryRiskVideoReq {
  repeated int64 avids = 1;
}

message QueryRiskVideoResp {
  repeated SingleQueryRiskVideoResp list = 1;
}

message SingleQueryRiskVideoResp {
  int64 avid = 1;
  string bvid = 2;
  string main_site_audit_res = 3;
  int32 archive_type = 4;
  string remark = 5;
}
