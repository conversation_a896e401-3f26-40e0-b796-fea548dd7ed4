syntax = "proto3";

package ad.brand.creative;

import "ad/brand/common.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/ad/brand.creative;api";
option java_package = "com.bapis.ad.brand.creative";
option java_multiple_files = true;


//创建创意预览请求
message CreateCreativePreviewReq {
    common.Requester requester = 1 [json_name = "requester"];
    int64 creative_id = 2 [json_name = "creative_id"];
    int64 mid_id = 3 [json_name = "mid_id"];
    int32 order_product = 4 [json_name = "order_product"];
}

//创建创意预览响应
message CreateCreativePreviewRes {
    common.Responser responser = 1 [json_name = "responser"];
}