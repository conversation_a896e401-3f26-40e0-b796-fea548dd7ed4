syntax = "proto3";

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package infra.mg.live_cdninfo.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/mg.live.cdninfo;v1";
option java_package = "com.bapis.infra.mg.live.cdninfo";
option java_multiple_files = true;
option (wdcli.appid) = "infra.mg.live-cdninfo";

enum CoverVersion {
    LIVE_CDN_OTHER   = 0;
    LIVE_CDN_COLD    = 1;
    LIVE_CDN_HOT     = 2;
}

message BwConf {
    float  min_usage         = 1;
    float  max_usage         = 2;
    uint32 concurrency       = 3;
    uint32 batch_concurrency = 4;
    uint32 batch_factor      = 5;
}

message McdnConfig {
    float    max_ratio      = 1;
    repeated BwConf bw_conf = 2;
}

message NcdnConfig {
    float    max_ratio          = 1;
    repeated BwConf switch_conf = 2;
    repeated BwConf node_conf   = 3;
}

message CdnConf {
    McdnConfig mcdn_config          = 1;
    NcdnConfig ncdn_config          = 2;
    IdcReserveConf idc_reserve_conf = 3; //idc直播预留带宽配置
}

message IdcReserveConf {
    bool               reserve_idc_bw = 1; //是否预留idc带宽总开关
    map<string, float> idc_rate_map   = 2; //key: idc, value: idc直播预留带宽的比例
}

message Empty {
}

message NodeIspInfo {
    string ip          = 1;
    string isp         = 2;
    double limitMbps   = 3;
    double inMbps      = 4;
    double outMbps     = 5;
    int32  initial_wnd = 6;
    int32  batch_initial_wnd = 7;
    string ipv6        = 8;
}

message BanNodeItem {
    string   nodeId             = 1;
    string   hostname           = 2;
    repeated NodeIspInfo ipinfo = 3;
    uint32   tag                = 4; //1:live-relay
    repeated string abilities   = 5; //混跑标签
}

message IdcIspItem {
    string isp = 1;
    double bw  = 2;
}

message SwitchBWItem {
    string   id                = 1;
    double   limitMbps         = 2;
    double   outMbps           = 3;
    int32    initial_wnd       = 4;
    int32    batch_initial_wnd = 5;
}

message BanIdcItem {
    string   idc_id            = 1
    [(gogoproto.moretags) = 'form:"type" validate:"omitempty"'];
    bool     bw_is_avail       = 2;
    repeated IdcIspItem isps   = 3;
    repeated BanNodeItem nodes = 4;
    bool     minbw_is_avail    = 5;
    double   total_bw          = 6;
    SwitchBWItem   max_bw      = 7;
    SwitchBWItem   max_live_bw = 8;
    bool     is_support_b2     = 9;
    string   province          = 10; //idc所在省份
    string   region            = 11; //idc所在区域
}

message BanCdnInfo {
    map<string, BanIdcItem> idc_info = 1; // key:idc server id
}

message RangeLevel {
    int32   min = 1;
    int32   max = 2;
}

message BanZoneList {
    repeated string idcs = 1;
}

message BanZoneCover {
    uint32   zone_id               = 1;
    repeated BanZoneList zone_list = 2; 
}

message BanZoneInfo {
    map<uint32, BanZoneCover> zone_cover = 1;
}

message BanZoneInfoWithRange {
    RangeLevel       flv_range_level     = 1;
    RangeLevel       hls7_range_level    = 2;
    map<uint32, BanZoneCover> zone_cover = 3;
    CoverVersion     version             = 4;
    string           zone_version        = 5;
}

message MultiBanZoneInfoWithRange {
    repeated  BanZoneInfoWithRange  zone_info = 1;
}

message IDCS {
    repeated  string  idcs = 1;
}

message Cover {
    string      business                = 1;
    string      country                 = 2;
    string      region                  = 3;
    string      province                = 4;
    string      isp                     = 5;
    string      temperature             = 6;
    repeated    string gary_sid         = 7;
    repeated    string gary_sid_suffix  = 8;
    repeated    string idc_ids          = 9;
    map<string, IDCS>  sid_idc          = 10;
}

message Node {
    string       idc_id            = 1;
    string       idc_name          = 2;
    string       resource_type     = 3;
    string       role              = 4;
    float        limit_bw          = 5; // 该节点直播可以使用的总带宽
    string       isp               = 6;
}

message ResResult { // from bili-cesium
    repeated     Cover     covers   = 1;
    map<string, IDCS>   backup_idc  = 2;
    map<string, Node>      nodes    = 3; //key:idc id
}

// 1
message BanRespData {
    BanCdnInfo    cdn_info   = 1
    [(gogoproto.moretags) = 'form:"cdn_info" validate:"required"'];
    BanCdnInfo    mcdn_info  = 2;
    MultiBanZoneInfoWithRange  multi_zone_info      = 3; // 冷热流覆盖范围 + 自建CDN覆盖配置
    MultiBanZoneInfoWithRange  multi_free_zone_info = 4; // 免流类型，冷热流覆盖范围 + 自建CDN覆盖配置
}

message FetchBanRespMsg {
    uint32     code    = 1;
    string     message = 2;
    BanRespData data   = 3;
}

service CdnInfoService {
    rpc FetchBanLiveCdnInfo(Empty) returns (FetchBanRespMsg);  // 实现该rpc接口
}

message CdnCoverItem {
    string zone   = 1;
    uint32 zoneid = 2;
    string idc    = 3;
}

message CdnCoverData {
    string                 version  = 1;
    bool                   is_apply = 2;
    repeated CdnCoverItem  detail   = 3;
}

message CdnCoverList {
    int32         code    = 1;
    string        message = 2;
    CdnCoverData  data    = 3;
}

// multi coverlist version info
message Range {
    int32 min = 1;
    int32 max = 2;
}

message VersionWithRange {
    string   zone_version     = 1;
    Range    flv_level_range  = 2;
    Range    hls_level_range  = 3;
    string   tf_zone_version  = 4;
}

message MultiZoneVersion {
    int32       code                   = 1;
    string      message                = 2;
    repeated    VersionWithRange data  = 3;
}
