syntax = "proto3";

package infra.slb.admin.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/slb.admin.v1;v1";
option java_package = "com.bapis.infra.slb.admin.v1";
option java_multiple_files = true;

// RMS服务
service RmsService {
  // 节点对比
  rpc CompareRmsAndSlbNodes (google.protobuf.Empty) returns (CompareRmsAndSlbReply) {
    option (google.api.http) = {
      get: "/v1/admin/slb/rms/diff_nodes"
    };
  }
}

message CompareRmsAndSlbReply {
  repeated string missing_nodes = 1;
  repeated string extra_nodes = 2;
}