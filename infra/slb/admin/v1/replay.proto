syntax = "proto3";

package infra.slb.admin.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/slb.admin.v1;v1";
option java_package = "com.bapis.infra.slb.admin.v1";
option java_multiple_files = true;


service ReplayService {
  // 流量回放
  rpc ReplayCheck (ReplayCheckRequest) returns (ReplayCheckReply) {
    option (google.api.http) = {
      get: "/v1/admin/slb/replay/check"
    };
  }
}

message ReplayCheckRequest {
  // slb 集群名称
  string cluster = 1 [(google.api.field_behavior) = REQUIRED];
  // 回放的应用名称
  string service = 2;
  // 回话的域名
  string domain = 3;
  // 回放目标
  string target = 4 [(google.api.field_behavior) = REQUIRED];
  // 回放请求数量
  int32 count = 5;
  // 是否回放https
  bool is_tls = 6;
}

message ReplayCheckReply {
  // 回放成功数量
  int32 success_count = 1;
  // 回放失败数量
  int32 error_count = 2;
  // 回放日志
  repeated string logs = 3;
}