syntax = "proto3";

package infra.slb.admin.v2;

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
// import "google/api/field_behavior.proto";
import "infra/slb/admin/v2/upstream.proto";
import "infra/slb/admin/v2/domain.proto";
import "infra/slb/admin/v2/cluster.proto";
import "infra/slb/admin/v2/certificate.proto";
import "infra/slb/admin/v2/config.proto";


option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/slb.admin.v2;v2";
option java_package = "com.bapis.infra.slb.admin.v2";
option java_multiple_files = true;

// 发布历史服务
service DeploymentService {
  // 创建发布
  rpc CreateDeployment(CreateDeploymentReq) returns (Deployment) {
    option (google.api.http) = {
      post: "/v2/admin/slb/deployments"
      body: "*"
    };
  }
  // 查询是否开启绿色通道
  rpc CheckFastPass(CheckFastPassRequest) returns (CheckFastPassReply) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments/fastpass"
    };
  }
  // 关闭所有工单
  rpc FixDeployment(google.protobuf.Empty) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments/fix"
    };
  }
  // 获取发布详情
  rpc GetDeployment(DeploymentReq) returns (Deployment) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments/{id}"
    };
  }
  // 获取发布列表
  rpc ListDeployments(ListDeploymentsReq) returns (DeploymentSet) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments"
    };
  }
  // 发布流程确认
  rpc ConfirmDeployment(DeploymentReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments/{id}/confirm"
    };
  }
  // 发布流程继续
  rpc ContinueDeployment(DeploymentReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments/{id}/continue"
    };
  }
  rpc UpdateDeploymentMode(UpdateModeReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      patch: "v2/admin/slb/deployments/{id}/mode"
      body: "*"
    };
  }
  // 发布流程结单
  rpc FinishDeployment(DeploymentReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments/{id}/finish"
    };
  }
  // 发布流程回滚
  rpc RollbackDeployment(DeploymentReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments/{id}/rollback"
    };
  }
  // 发布流程撤销
  rpc CancelDeployment(DeploymentReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments/{id}/cancel"
      };
  }
  // 发布流程关闭
  rpc CloseDeployment(DeploymentReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments/{id}/close"
    };
  }
  // 获取发布配置
  rpc GetDeploymentConfigs(DeploymentReq) returns (Configs) {
    option (google.api.http) = {
      get: "/v2/admin/slb/deployments/{id}/configs"
    };
  }
  // 获取版本列表
  rpc ListReleaseVersions(ListDeploymentsReq) returns (ReleaseVersionSet) {
    option (google.api.http) = {
      get: "/v2/admin/slb/release_versions"
    };
  }
  // 开启绿色通道
  rpc CreateFastPass(FastPass) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v2/admin/slb/deployments/fastpass"
      body: "*"
    };
  }
}

message CheckFastPassRequest {
  string service = 1;
  string domain = 2;
}

message CheckFastPassReply{
  bool  is_fast = 1;
}


// 发布记录
message Deployment {
  // 发布记录ID
  int64 id = 1;
  // 发布类型: domain 0 ; upstream 1
  int32 type = 2;
  // 域名ID
  int64 domain_id = 3;
  // 应用ID
  int64 upstream_id = 4;
  // 证书ID
  int64 certificate_id = 5;
  // 当前发布版本
  int32  version = 6;
  // 上次发布版本
  int32  pre_version = 7;
  // 发布节点
  repeated string nodes = 8;
  // 当前发布阶段
  int32 step = 9;
  //  发布日志
  string logs = 10;
  //  发布描述
  string description = 11;
  //  发布状态
  int32 state = 12;
  //  创建时间
  string ctime = 13;
  //  修改时间
  string mtime = 14;
  //  集群
  repeated string clusters = 15;
  // 操作人
  string operator = 16;
  // 域名对象
  Domain domain = 17;
  // upstream 对象
  Upstream upstream = 18;
  // certificate 对象
  Certificate certificate = 19;
  // changePilot 接流规则
  repeated double step_percents = 20;
  // 倒计时
  int32  countdown = 21;
  //  集群信息
  repeated Cluster cluster_infos = 22;
  // 环境
  string env = 23;
  // slo 预检结果 0: 正常 1：预警 2：跌破阈值
  int32 slo_check = 24;
  // 按集群发布的节点
  repeated DeploymentNode targets = 25;
  // 发布模式
  int32  mode = 26;
}

message DeploymentNode {
  //  节点IP
  repeated string Nodes = 1;
  //  集群
  string cluster = 2;
  //  发布进度
  int32 progress = 3;
  //  发布批次
  int32 current_step = 4;
  //  总批次
  int32 total_steps = 5;
}

// 发布集合
message DeploymentSet {
  // 发布列表
  repeated Deployment items = 1;
  // 列表总数
  int32 total = 2;
  // 分页数
  int32 page = 3;
  // 分页大小
  int32 size = 4;
}

// 创建发布
message CreateDeploymentReq {
  // 发布类型
  int32 type = 1;
  //  域名ID
  int64 domain_id = 2;
  //  应用ID
  int64 upstream_id =3;
  // 证书ID
  int64 certificate_id = 4;
  //  发布描述
  string description = 5;
  //  版本
  int32  version = 6;
  //  集群（待废弃字段）
  string cluster = 7;
  // 环境
  string env = 8;
  // 集群
  repeated string clusters = 9;
}

// 发布操作请求信息
message DeploymentReq {
  int64 id = 1;
}

// 获取发布列表请求信息
message ListDeploymentsReq {
  // 过虑条件：filters.state=0
  map<string, string> filters = 1;
  // 排序：mtime desc/asc
  string order_by = 2;
  // 分页数
  int32 page_num = 3;
  // 分页大小
  int32 page_size = 4;
}

// 绿色通道
message FastPass {
  // APPID
  string appid = 1;
  // 持续时间 （单位：秒）
  int32 duration = 2;
  // 描述
  string reason = 3;
  // 平台ID （前端无需关注）
  int32 platform_id = 4;
  // 申请原因
  string type = 5;
  // 域名
  string domain = 6;
}

message UpdateModeReq {
  int64 id = 1;
  int32 mode = 2;
}