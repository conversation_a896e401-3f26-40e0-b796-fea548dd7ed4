syntax = "proto3";

package infra.slb.admin.v2;

import "google/api/field_behavior.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/slb.admin.v2;v2";
option java_package = "com.bapis.infra.slb.admin.v2";
option java_multiple_files = true;

// 证书
message Certificate {
  // 证书ID
  int64 id = 1;
  // 证书类型,false/自签证书,true/生产证书
  bool type = 2;
  // 证书名称
  string name = 3;
  // 颁发机构
  string issuer = 4;
  // 有效域
  string subject = 5;
  // 证书
  string public_key = 6;
  // 私钥
  string private_key = 7;
  // 颁发时间
  string start_date = 8;
  // 截止时间
  string expire_date = 9;
  // 状态 （0/1/2 未发布/已发布/已删除）
  int64  state = 10;
  // 创建时间
  string ctime = 11;
  // 修改时间
  string mtime = 12;
  // 关联集群
  repeated CertificateCluster clusters = 13;
}

message CertificateCluster {
  // ID
  int64 id = 1;
  // 证书
  string certificate = 2;
  // 集群
  string cluster = 3;
  // 状态
  int64  state = 4;
}

// 证书管理
service CertificateService {
  // 证书签发
  rpc SignCertificate (SignCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      post: "/v2/admin/slb/certs/sign"
      body: "*"
    };
  }
  // 证书匹配
  rpc MatchCertificate (MatchCertificateRequest) returns (MatchCertificateReply) {
    option (google.api.http) = {
      get: "/v2/admin/slb/certs/match"
      additional_bindings {
        get: "/v1/admin/slb/certs/match"
      }
    };
  }
  // 证书上传
  rpc UploadCertificate (UploadCertificateRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v2/admin/slb/certs/upload"
      body: "*"
    };
  }
  // 证书下载
  rpc DownloadCertificate (DownloadCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      get: "/v2/admin/slb/certs/download"
    };
  }
  // 证书展示
  rpc ListCertificates (ListCertificatesRequest) returns (CertificatesSet) {
    option (google.api.http) = {
      get: "/v2/admin/slb/certs/list"
    };
  }
  // 证书删除
  rpc DeleteCertificate (DeleteCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      delete: "/v2/admin/slb/certs/delete/{id}"
    };
  }
  // 证书更新
  rpc UpdateCertificate (UpdateCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      post: "/v2/admin/slb/certs/update"
      body: "certificate"
    };
  }
  // 获取单集群所有证书
  rpc FindByCluster (FindByClusterRequest) returns (FindByClusterReplay) {
    option (google.api.http) = {
      get: "/v2/admin/slb/certs/content"
    };
  }
  // 获取证书的基准版本号
  rpc FindBasisCertificateVersion (FindBasisCertificateVersionRequest) returns (FindBasisCertificateVersionReplay) {
    option (google.api.http) = {
      get: "/v2/admin/slb/certs/versions"
    };
  }
}

message SignCertificateRequest {
  // 证书名称
  string name = 1;
  // 有效域
  repeated string domains = 2;
}

message MatchCertificateRequest {
  // 域名
  string domain = 1;
  // 集群
  string cluster = 2;
}

message MatchCertificateReply {
  // 适配的证书
  repeated Certificate items = 1;
}

message UploadCertificateRequest {
  // 证书名称
  string name = 1;
  // 证书公钥
  string public_key = 2;
  // 证书私钥
  string private_key = 3;
}

message DownloadCertificateRequest {
  // 证书名称
  string name = 1;
}

message ListCertificatesRequest {
  // 过虑条件
  map<string, string> filters = 1;
  // 分页数
  int32 page_num = 3;
  // 分页大小
  int32 page_size = 4;
}

message CertificatesSet {
  // 分组列表
  repeated Certificate items = 1;
  // 列表总数
  int32 total = 2;
  // 分页数
  int32 page = 3;
  // 分页大小
  int32 size = 4;
}

message DeleteCertificateRequest {
  // 接口ID
  int64 id = 1 [(google.api.field_behavior) = REQUIRED];
}

message UpdateCertificateRequest {
  // 证书
  Certificate certificate = 1;
}

message FindByClusterRequest {
  // 集群
  string cluster = 1;
}

message FindByClusterReplay {
  // 证书
  repeated Certificate items = 1;
}

message FindBasisCertificateVersionRequest{
  // 证书ID
  int64 certificate_id = 1;
}

message FindBasisCertificateVersionReplay {
  // 基准版本号
  int32 version = 1;
}
