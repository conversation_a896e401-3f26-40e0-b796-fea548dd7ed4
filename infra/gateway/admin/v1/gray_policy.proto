syntax = "proto3";

package infra.gateway.admin.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/field_mask.proto";
import "infra/gateway/admin/v1/deployment.proto";
import "infra/gateway/admin/v1/endpoint.proto";
import "infra/gateway/admin/v1/priority_config.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/gateway.admin.v1;v1";
option java_package = "com.bapis.infra.gateway.admin.v1";
option java_multiple_files = true;

message GrayPolicy {
    int64 id = 1;
    string name = 2;
    string gateway = 3;
    string color = 4;
    int64 endpoint_count = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
    int64 matched_radio = 6;
    int64 trace_sampling_rate = 7;
    bool enabled = 8;
    //所属服务
    string service = 9;
    //创建人
    string creator = 10;
    // 发布状态
    int32 state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
    // 创建时间
    string ctime = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
    // 修改时间
    string mtime = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

    repeated GrayPolicyEndpoint endpoints = 14;
    // 分组 ID
    int64 group_id = 15;
    // 关联灰度配置 ID
    int64 priority_config_id = 16 [(google.api.field_behavior) = OUTPUT_ONLY];
    // 关联的灰度配置
    GroupPriorityConfig priority_config = 17 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message GrayPolicyEndpoint {
    int64 endpoint_id = 1;
    int64 policy_id = 2;
    // 匹配类型：0 满足其一 1 满足所有
    int64 match_type = 3;
    // 创建时间
    string ctime = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
    // 修改时间
    string mtime = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
    repeated conditionSchema conditions = 6;
    infra.gateway.admin.v1.Endpoint endpoint = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message conditionSchema {
    string key = 1;
    string condition = 2;
    string value = 3;
}

message CreateGrayPolicyReq {
    GrayPolicy gray_policy = 1 [(google.api.field_behavior) = REQUIRED];
}

message UpdateGrayPolicyReq {
    GrayPolicy gray_policy = 1 [(google.api.field_behavior) = REQUIRED];
    // 更新字段
    google.protobuf.FieldMask update_mask = 2;
}

message ListGrayPolicyReq {
    // 过虑条件：filters.gateway=test-gw
    map<string, string> filters = 1;
    // 列表排序：mtime desc/asc
    string order_by = 2;
    // 分页数
    int32 page_num = 3;
    // 分页大小
    int32 page_size = 4;
}

message GrayPoliciesSet {
    repeated GrayPolicy items = 1;
    // 接口总数
    int32 total = 2;
    // 分页数
    int32 page = 3;
    // 分数大小
    int32 size = 4;
}

message GetGrayPolicyReq {
    int64 id = 1 [(google.api.field_behavior) = REQUIRED];
}

message DeleteGrayPolicyReq {
    int64 id = 1 [(google.api.field_behavior) = REQUIRED];
}

message CreateGrayPolicyDeploymentReq {
    int64 id = 1 [(google.api.field_behavior) = REQUIRED];
    string remark = 2 [(google.api.field_behavior) = REQUIRED];
}

message CreateGrayPolicyPriorityConfigPushReq {
    int64 id = 1 [(google.api.field_behavior) = REQUIRED];
    string remark = 2 [(google.api.field_behavior) = REQUIRED];
}

message BatchCreateGrayPolicyPriorityConfigPushReq {
    repeated int64 id = 1 [(google.api.field_behavior) = REQUIRED];
    string remark = 2 [(google.api.field_behavior) = REQUIRED];
}

message BatchCreateGrayPolicyPriorityConfigPushReply {
    map<int64, gateway.admin.v1.GroupPriorityConfig> configs = 1;
}

service GrayPolicyService {
    rpc CreateGrayPolicy(CreateGrayPolicyReq) returns (GrayPolicy) {
        option (google.api.http) = {
            post: "/v1/admin/gateway/graypolicies"
            body: "gray_policy"
        };
    }
    rpc UpdateGrayPolicy(UpdateGrayPolicyReq) returns (GrayPolicy) {
        option (google.api.http) = {
            patch: "/v1/admin/gateway/graypolicies"
            body: "gray_policy"
        };
    }
    rpc ListGrayPolicies(ListGrayPolicyReq) returns (GrayPoliciesSet) {
        option (google.api.http) = {
            get: "/v1/admin/gateway/graypolicies"
        };
    }
    rpc GetGrayPolicy(GetGrayPolicyReq) returns (GrayPolicy) {
        option (google.api.http) = {
            get: "/v1/admin/gateway/graypolicies/{id}"
        };
    }
    rpc DeleteGrayPolicy(DeleteGrayPolicyReq) returns (GrayPolicy) {
        option (google.api.http) = {
            delete: "/v1/admin/gateway/graypolicies/{id}"
        };
    }
    rpc CreateGrayPolicyDeployment(CreateGrayPolicyDeploymentReq) returns (gateway.admin.v1.Deployment) {
        option (google.api.http) = {
            post: "/v1/admin/gateway/graypolicies/{id}/deployment"
            body: "*"
        };
    }
    rpc CreateGrayPolicyPriorityConfigPush(CreateGrayPolicyPriorityConfigPushReq) returns (gateway.admin.v1.GroupPriorityConfig) {
        option (google.api.http) = {
            post: "/v1/admin/gateway/graypolicies/{id}/priority-config"
            body: "*"
        };
    }
    rpc BatchCreateGrayPolicyPriorityConfigPush(BatchCreateGrayPolicyPriorityConfigPushReq) returns (BatchCreateGrayPolicyPriorityConfigPushReply) {
        option (google.api.http) = {
            post: "/v1/admin/gateway/graypolicies/batch-create-priority-config"
            body: "*"
        };
    }
}