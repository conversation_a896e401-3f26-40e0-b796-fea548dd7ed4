syntax = "proto3";

package infra.gateway.llm.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/gateway.llm.v1;v1";
option java_multiple_files = true;
option java_package = "com.bapis.infra.gateway.llm.v1";

// 发布信息
message Release {
  // 发布ID
  int64 id = 1;
  // 所属网关
  string gateway = 2;
  // 灰度标签
  string canary = 3;
  // 配置版本
  string version = 4;
  // 配置信息
  string config = 5;
  // 发布状态：0-发布成功
  int32 state = 6;
  // 创建时间
  string ctime = 7;
  // 修改时间
  string mtime = 8;
}

// 发布配置集合
message ReleaseSet {
  // 发布配置列表
  repeated Release items = 1;
  // 列表总数
  int32 total = 2;
  // 分页数
  int32 page = 3;
  // 分页大小
  int32 size = 4;
}

// 发布服务
service ReleaseService {
  // 获取发布信息
  rpc PreviewRelease(PreviewReleaseReq) returns (Release) {
    option (google.api.http) = {get: "/v1/llm/gateway/releases/preview"};
  }
  // 获取配置列表
  rpc ListReleases(ListReleasesReq) returns (ReleaseSet) {
    option (google.api.http) = {get: "/v1/llm/gateway/releases/list"};
  }
  // 获取发布信息
  rpc GetRelease(GetReleaseReq) returns (Release) {
    option (google.api.http) = {get: "/v1/llm/gateway/releases/{id}"};
  }
}

// 预览发布配置
message PreviewReleaseReq {
  // 网关
  string gateway = 1 [(google.api.field_behavior) = REQUIRED];
}

// 获取发布列表请求
message ListReleasesReq {
  // 网关
  string gateway = 1 [(google.api.field_behavior) = REQUIRED];
}

// 获取发布信息请求
message GetReleaseReq {
  // id
  int64 id = 1 [(google.api.field_behavior) = REQUIRED];
}
