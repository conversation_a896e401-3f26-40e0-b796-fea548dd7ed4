syntax = "proto3";

package infra.gateway.llm.v1;

import "google/api/annotations.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/gateway.llm.v1;v1";
option java_multiple_files = true;
option java_package = "com.bapis.infra.gateway.llm.v1";

// 元信息服务
service CmdbService {
  // 创建网关
  rpc ListApps(ListAppsReq) returns (ListAppsReply) {
    option (google.api.http) = {get: "/v1/llm/gateway/cmdb/apps"};
  }
}

message ListAppsReq {}

message ListAppsReply {
  // 应用列表
  repeated string items = 1;
}
