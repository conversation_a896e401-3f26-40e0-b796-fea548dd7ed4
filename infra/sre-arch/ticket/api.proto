syntax = "proto3";
package ticket.service.v1;

import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/sre.arch.ticket;api";
option java_package = "com.bapis.infra.sre.arch.ticket";
option (wdcli.appid) = "infra.sre-arch.sre-ticket";
option java_multiple_files = true;

service TicketRpcService {
    // 人员查询接口
    rpc ListMember(ListMemberRpcReq) returns (ListMemberRpcResp);
    // 服务组查询接口
    rpc ListSkillGroup(ListSkillGroupRpcReq) returns (ListSkillGroupRpcResp);
    // 创建工单
    rpc Create(CreateReq) returns (CreateResp);
    // 工单列表查询接口
    rpc List(ListReq) returns (ListResp);
    // 根据工单ID查询工单详情
    rpc Find(FindReq) returns (FindResp);
    // 催单
    rpc Urge (UrgeReq) returns (UrgeResp);
    // 回复工单
    rpc Reply(ReplyReq) returns (ReplyResp);
    // 转交工单
    rpc Transfer(TransferReq) returns (TransferResp);
    // 工单状态变更
    rpc ChangeStatus(ChangeStatusReq) returns (ChangeStatusResp);
    // 问题分类获取
    rpc GetProblem(GetProblemReq) returns (GetProblemResp);

}

message GetProblemReq{
    Auth auth = 1;
    int64 page_num = 2;
    int64 page_size = 3;
}

message GetProblemResp {
    bool success = 1;
    Error error = 2;
    string message = 3;
    // 人员数据
    ProblemData data = 4; 
}

message ProblemData{
    repeated ProblemVo problems = 1;
    int64 total = 2;
    int64 page_num = 3;
    int64 page_size = 4;
}

message ProblemVo {
    int64 id = 1;
    string name = 2;
    int64 tag_id = 3;
    int64 parent_id = 4;
    repeated int64 skill_group_ids = 5;
    string tag_name = 6;
    repeated SkillGroupVO skill_group_details = 7;
    bool online = 8;
    string tag_color = 10;
}

message SkillGroupVO{
    int64 id = 1;
    string name = 2;
}





enum TicketAssignEnum {
    TicketAssignEnumUnknown = 0;

    // 技能组分配
    TicketAssignEnumGroup = 1;

    // 分配个人
    TicketAssignEnumPerson = 2;
}

enum TicketLevelEnum {
    TicketLevelEnumUnknown = 0;
    // 一般工单
    TicketLevelEnumNormal = 1; 
    // 紧急工单
    TicketLevelEnumEmergency = 2; 
}

enum TicketStatus {
    TicketStatusUnknown = 0;
    // 未处理
    TicketStatusUnDeal = 1; 
    // 处理中
    TicketStatusDealing = 2; 
    // 已完结
    TicketStatusDone = 3; 
    // 已归档
    TicketStatusClosed = 4; 
}

message Auth{
    // token,鉴权
    string token = 1; 
}

enum Error {
    ErrorUnknown = 0;
    ErrorTokenError = 100001;
    ErrorCallerError = 100002;
    ErrorParamIllegal = 1001;
    ErrorTicketNotExisted = 100;
    ErrorTicketMemberNotExisted = 101;
}

message ListSkillGroupRpcReq{
    Auth auth = 1; 
    // 组名模糊查询
    string group_name_like = 2; 
    int64 page_num = 3;
    int64 page_size = 4;
    // Ids批量查询
    repeated int64 ids = 5; 
}

message ListSkillGroupRpcResp{
    bool success = 1;
    Error error = 2;
    string message = 3;
    ListMemberData data = 4;

}

message ListMemberRpcReq{
    Auth auth = 1;
    // ad_account
    string ad_account = 2;  
    int64 id = 3;
    repeated int64 ids = 4;
}

message ListMemberData {
    // 组信息
    repeated SkillGroupExtraVO groups = 1; 
    int64 total = 2;
    int64 page_num = 3;
    int64 page_size = 4;
}

message SkillGroupExtraVO {
    int64 id = 1;
    // 组名称
    string name = 2;
    Member owner = 5;
    // 管理员
    repeated Member admins = 6; 
    // 成员
    repeated Member members = 7; 
    // 类型
    string type = 8; 
}

message ListMemberRpcResp{
    bool success = 1;
    Error error = 2;
    string message = 3;
    // 人员数据
    MembersData data = 4; 
}

message Member {
    // 主键ID
    int64 id = 1; 
    string ad_account = 2;
    string phone = 3; 
    string email = 4;
    string leader_ad_account = 5;
    string name = 6;
    // 工号
    string emp_id = 8; 
}

message MembersData{
    repeated Member members = 1;
    int64 total = 2 ;
    int64 page_num = 3;
    int64 page_size = 4;
}

message ChangeStatusReq{
    Auth auth = 1;
    // 工单ID
    string ticket_id = 2;
    // 变更状态
    TicketStatus status = 3; 
}

message ChangeStatusResp{
    bool success = 1;
    Error error = 2;
    string message = 3;
}

message TransferReq{
    Auth auth = 1;
    // 工单ID
    string ticket_id = 2; 
     // 转交目标（用户AdAccount）
    string ad_account = 3;
    string operator = 4;
}

message TransferResp{
    bool success = 1;
    Error error = 2;
    string message = 3;
}

message ReplyResp{
    bool success = 1;
    Error error = 2;
    string message = 3;
}

message ReplyReq{
    Auth auth = 1;
    // 工单ID
    string ticket_id = 2;
    Comment comment = 3;
    string operator = 4;
}


message UrgeReq {
    Auth auth = 1;
    // 工单ID
    string ticket_id = 2;
}

message UrgeResp{
    bool success = 1;
    Error error = 2;
    string message = 3;
}

message CreateReq{
    Auth auth = 1;
    // 标题
    string title = 2; 
    // 描述
    string desc = 3; 
    // 问题分类ID
    int64 problem_id = 4;
    // 工单等级
    TicketLevelEnum level = 5;
    // 工单分派方式
    TicketAssignEnum assign_type = 6;
    // 分派模式 如果指定到人，则这里需要传入memberId
    int64 member_id = 7;
    repeated string picture_urls = 8;
    repeated string video_urls = 9;
    repeated string attch_urls = 10;
    string creator = 11;
    // 自定义表单，需要序列化为json传入。
    string custom_fields = 100;
}

message CreateResp{
    bool success = 1;
    Error error = 2;
    string message = 3;
    CreateData data = 4;
}

message CreateData{
    // 工单ID
    string ticket_id = 1; 
}

message ListReq{
    Auth auth = 1;
    // 工单名称，模糊查询
    string title_like = 2; 
    // 工单内容，模糊查询
    string desc_like = 3; 
    // 工单状态
    TicketStatus status = 4;
    // 工单等级
    TicketLevelEnum level = 5;
    // 创建者
    string creator = 6;
    // 技能组ID
    int64 skill_group_id = 8;
    // 问题分类ID
    int64 problem_id = 9;
    // 当前处理人
    int64 member_id = 13;
    // 工单ID
    string ticket_id = 15;
    // 分页页数
    int64 page_num = 99;
    // 分页Limit
    int64 page_size = 100;
}

message ListResp{
    bool success = 1;
    Error error = 2;
    string message = 3;
    ListData data = 4;
}

message ListData{
    repeated TicketExtraVo tickets = 1;
    int64 total = 2 ;
    int64 page_num = 3;
    int64 page_size = 4;
}

message BasicTicket{
    // 工单ID
    string ticket_id = 1;
    // 工单标题
    string title = 2;
    // 工单状态
    TicketStatus status = 3;
    // 工单状态描述
    string status_desc = 4;
    // 工单类型
    TicketLevelEnum level = 5;
    // 工单类型描述
    string level_desc = 6;
    // 问题分类
    int64 problem_id = 7;
    // 问题分类描述
    string problem_desc = 8;
    // 技能组ID
    int64 skill_group_id = 9;
    // 技能组名称
    string skill_group_name = 10;
    // 创建时间
    int64 create_time = 11;
    // 自定义表单 json格式
    string custom_field_json_data = 12;
    // 处理人id
    int64 deal_member_id = 13;
    // 处理人Name
    string deal_member_show_name = 14;
    // 工单创建者
    string creator = 15;
    // 工单创建者ID
    string creator_show_name = 16;
    // 工单分派类型
    TicketAssignEnum assign_type = 17;
    // 工单内容描述
    string desc = 18;
    // 图片urls
    repeated string picture_urls = 19;
    // 视频urls
    repeated string video_urls = 20;
    // 附件urls
    repeated string attch_urls = 21;
}

message Comment{
    // 备注
    string comment = 1;
    // 上传图片url
    repeated string picutre_urls = 2;
    // 上传附件url
    repeated string attch_urls = 3;
}

message TicketExtraVo{
    BasicTicket basic_information = 1;
    repeated Comment comments = 2;
    repeated Opeartion operations = 3;
}

message Opeartion {
    OperationType action = 1;
    string operator = 2;
    int64 timestamp = 3;
    string detail = 4;
    string comment = 5 ;
    repeated string picture_urls = 6;
    repeated string video_urls = 7;
    repeated string attch_urls = 8 ;
}

enum OperationType {
    OperationTypeUnknown = 0;
    TicketOperationTypeCreate = 1;
    TicketOperationTypeTransfer = 2;
    TicketOperationTypeRelation = 3;
    TicketOperationAssign = 4;
    TicketOperationDone = 5;
    TicketOperationReOpen = 6;
    TicketOperationAccept = 7;
    TicketOperationInnerReminder = 8;
    TicketOperationClose = 9;
    TicketOperationTypeUnRelation = 10;
    TicketOperationUpgrade = 11;
    TicketOperationTypeRelationed = 12;
    TicketOperationComment = 13;
}


message FindReq{
    Auth auth = 1;
    string ticket_id = 2;
}

message FindResp{
    bool success = 1;
    Error error = 2;
    string message = 3;
    ListFind data = 4;
}

message ListFind{
    TicketExtraVo ticket = 1;
}