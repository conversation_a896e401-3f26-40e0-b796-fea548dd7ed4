syntax = "proto3";

// `buf.bilibili.co:"bapis/bapis-gen,disablegogocompatible"`

package infra.navigator.service.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/navigator.service.v1;v1";
option java_package = "com.bapis.infra.navigator.service.v1";
option java_multiple_files = true;

message UpdateReleaseStatusReq {
    // 规则唯一ID(数据库自增主键)，非ruleId
    repeated int64 rule_uids = 1;
    // 规则发布状态, 0未发布，1已发布
    int32 state = 2;
}

message UpdateReleaseStatusResp {
    bool result = 1;
}

message CommonRuleReply {
    int64 id = 1;
    int64 rule_id = 2;
    int64 version = 3;
}

message BatchDeleteByRuleIdReq {
    repeated int64 rule_ids = 1;
    string operator = 2;
}

message BatchDeleteByRuleIdReply {
    int32 deleted = 1;
}

message BatchDeleteReq {
    repeated int64 ids = 1;
    string operator = 2;
}

message BatchDeleteReply {
    bool result = 1;
}