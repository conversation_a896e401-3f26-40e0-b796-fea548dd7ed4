syntax = "proto3";

package infra.akali.ng.service.v1;

import "extension/wdcli/wdcli.proto";
import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/field_mask.proto";

option (wdcli.appid) = "infra.akali.akali-ng";
option go_package = "buf.bilibili.co/bapis/bapis-gen/infra/akali.ng.service.v1;v1";
option java_multiple_files = true;
option java_package = "com.bapis.infra.akali.ng.service.v1";

//  数据融合
service DataIntegrationService {
    rpc GetDataIntegration(GetDataIntegrationReq) returns (DataIntegration) {
        option (google.api.http) = {
            get: "/akaling/v1/data-integration/get"
        };
    }
    rpc CreateDataIntegration(CreateDataIntegrationReq) returns (CreateDataIntegrationReply) {
        option (google.api.http) = {
            post: "/akaling/v1/data-integration/create"
            body: "data_integration"
        };
    }
    rpc ListDataIntegration(ListDataIntegrationReq) returns (ListDataIntegrationReply) {
        option (google.api.http) = {
            get: "/akaling/v1/data-integration/list"
        };
    }
    rpc DeleteDataIntegration(DeleteDataIntegrationReq) returns (DataIntegration) {
        option (google.api.http) = {
            delete: "/akaling/v1/data-integration/delete"
        };
    }
    rpc UpdateDataIntegration(UpdateDataIntegrationReq) returns (DataIntegration) {
        option (google.api.http) = {
            post: "/akaling/v1/data-integration/update"
            body: "data_integration"
        };
    }
}

service GitLabIntegration {
    rpc Webhook(WebhookReq) returns (WebhookReply) {
        option (google.api.http) = {
            post: "/akaling/openapi/v1/data-integration/gitlab/webhook"
            body: "event"
        };
    }
}

service ExporterIntegration {
    rpc ListExporterServices(ListExporterServicesReq) returns (ListExporterServicesReply) {
        option (google.api.http) = {
            get: "/akaling/openapi/v1/data-integration/exporter/service/list"
        };
    }
}

message ListExporterServicesReq {
    string mode = 1;
}

message ListExporterServicesReply {
    repeated string items = 1;
}

message GetDataIntegrationReq {
    string app_id = 1 [(google.api.field_behavior) = REQUIRED];
    string name = 2 [(google.api.field_behavior) = REQUIRED];
}

message CreateDataIntegrationReq {
    DataIntegration data_integration = 1;
}

message CreateDataIntegrationReply {
    DataIntegration data_integration = 1;
    map<string, string> extra = 2;
}

message DataIntegration {
    int64 id = 1;
    string creator = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
    string modifier = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
    string app_id = 4;
    string name = 5;
    string ctime = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
    string mtime = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
    bool disable = 8;
    google.protobuf.Struct config = 9;
}

message ListDataIntegrationReq {
    string appid = 1;
}

message ListDataIntegrationReply {
    repeated DataIntegration items = 1;
}

message DeleteDataIntegrationReq {
    string appid = 1 [(google.api.field_behavior) = REQUIRED];
    string name = 2 [(google.api.field_behavior) = REQUIRED];
}

message UpdateDataIntegrationReq {
    DataIntegration data_integration = 1 [(google.api.field_behavior) = REQUIRED];
    google.protobuf.FieldMask update_mask = 2;
}

message WebhookReq {
    google.protobuf.Struct event = 1;
    string token = 2;
}

message WebhookReply {}