package com.bilibili.mall.business.account.service.impl;

import com.bapis.ad.mgk.business_tool.*;
import com.bilibili.mall.business.account.annotation.AsyncRequest;
import com.bilibili.mall.business.account.api.enums.AuthTypeEnum;
import com.bilibili.mall.business.account.api.vo.base.BaseDataExportRepVO;
import com.bilibili.mall.business.account.api.vo.base.BaseDataQueryVO;
import com.bilibili.mall.business.account.api.vo.base.BaseDataRespVO;
import com.bilibili.mall.business.account.api.vo.base.BaseToolVO;
import com.bilibili.mall.business.account.api.vo.workWechat.*;
import com.bilibili.mall.business.account.constants.Constants;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.AdAccountLabelGrpcService;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.WorkWechatGrpcService;
import com.bilibili.mall.business.account.domain.domains.account.entity.BizAccountUserInfo;
import com.bilibili.mall.business.account.domain.domains.account.service.BizAccountDomainService;
import com.bilibili.mall.business.account.enums.ComponentRelationType;
import com.bilibili.mall.business.account.infrastructure.exception.BizErrorCode;
import com.bilibili.mall.business.account.infrastructure.exception.ClientViewException;
import com.bilibili.mall.business.account.service.VideoComponentService;
import com.bilibili.mall.business.account.service.WorkWechatToolService;
import com.bilibili.mall.business.account.utils.ExcelUtil;
import com.bilibili.mall.common.response.BaseResponse;
import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import com.github.pagehelper.PageInfo;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.mall.business.account.service.impl.BookingToolServiceImpl.sdf;

/**
 * <AUTHOR>
 * @since 2024/2/2
 */
@Service
@Slf4j
public class WorkWechatToolServiceImpl implements WorkWechatToolService {

    @Resource
    private BizAccountDomainService bizAccountDomainService;

    @Resource
    private WorkWechatGrpcService workWechatGrpcService;

    @Lazy
    @Resource
    private VideoComponentService videoComponentService;

    @Resource
    private ExcelUtil excelUtil;

    @Resource
    private AdAccountLabelGrpcService adAccountLabelGrpcService;

    @Value("${work.wechat.img.url}")
    @DynamicValue
    private String workWechatImgUrl;

    @Override
    public WorkWechatLinkDTO addWorkWechat(@NonNull Long loginUid) {
        checkAuthorization(loginUid);
        BizAccountUserInfo bizAccountUserInfo = bizAccountDomainService.checkUserAdId(loginUid);
        HandleCustomerAcqLinkAuthReq req = HandleCustomerAcqLinkAuthReq.newBuilder()
                .setAccountId(bizAccountUserInfo.getAdId().intValue())
                .build();
        CustomerAcqLinkAuthReply customerAcqAuthLink = workWechatGrpcService.getCustomerAcqAuthLink(req);
        return WorkWechatLinkDTO.builder().link(customerAcqAuthLink.getAuthUrl())
                .accountName(customerAcqAuthLink.getAccountName())
                .build();
    }

    @Override
    public PageInfo<WorkWechatVO> queryWorkWechat(WorkWechatQueryVO queryVO, Long loginUid) {
        checkAuthorization(loginUid);
        BizAccountUserInfo bizAccountUserInfo = bizAccountDomainService.checkUserAdId(loginUid);
        CustomerAcqLinkPageReply customerAcqLinkByPage = queryWorkWechatFromRemote(queryVO, bizAccountUserInfo);
        if (Objects.isNull(customerAcqLinkByPage)) {
            return PageInfo.of(new ArrayList<>());
        }
        PageInfo<WorkWechatVO> pageInfo = new PageInfo<>();
        pageInfo.setTotal(customerAcqLinkByPage.getTotal());
        List<WorkWechatVO> workWechatVOS = obtainWorkWechatVOByReply(customerAcqLinkByPage);
        workWechatVOS.forEach(w -> w.setAssociateArchsNum(videoComponentService.selectBusinessCompBindCount(bizAccountUserInfo, AuthTypeEnum.CONTACT_QW.getCode(), String.valueOf(w.getId()), ComponentRelationType.VIDEO.getType())));
        pageInfo.setList(workWechatVOS);
        return pageInfo;
    }

    private static List<WorkWechatVO> obtainWorkWechatVOByReply(CustomerAcqLinkPageReply customerAcqLinkByPage) {
        return customerAcqLinkByPage.getRepliesList().stream().map(customerAcqLink -> WorkWechatVO.builder()
                .id(customerAcqLink.getId())
                .name(customerAcqLink.getLinkName())
                .ctime(new Date(customerAcqLink.getCreateTime()))
                .status(customerAcqLink.getStatus())
                .launchUrl(customerAcqLink.getLinkUrl())
                .build()).collect(Collectors.toList());
    }

    private CustomerAcqLinkPageReply queryWorkWechatFromRemote(WorkWechatQueryVO queryVO, BizAccountUserInfo bizAccountUserInfo) {
        CustomerAcqLinkPageReq.Builder req = CustomerAcqLinkPageReq.newBuilder()
                .setAccountId(bizAccountUserInfo.getAdId().intValue());
        if (Objects.nonNull(queryVO.getId())) {
            req.addIds(queryVO.getId());
        }
        if (Objects.nonNull(queryVO.getPage()) && Objects.nonNull(queryVO.getPageSize())) {
            req.setPage(queryVO.getPage())
                    .setSize(queryVO.getPageSize());
        }
        req.setStatus(1);
        if (Objects.nonNull(queryVO.getStatus())) {
            req.setStatus(queryVO.getStatus());
        }
        if (StringUtils.isNoneBlank(queryVO.getName())) {
            req.setNameLike(queryVO.getName());
        }
        return workWechatGrpcService.getCustomerAcqLinkByPage(req.build());
    }

    @Override
    public WorkWechatVO findWorkWechat(Long id, Long loginUid) {
        checkAuthorization(loginUid);
        BizAccountUserInfo bizAccountUserInfo = bizAccountDomainService.checkUserAdId(loginUid);
        CustomerAcqLinkPageReply customerAcqLinkPageReply = queryWorkWechatFromRemote(WorkWechatQueryVO.builder().status(1).id(id).page(1).pageSize(1).build(), bizAccountUserInfo);
        if (CollectionUtils.isEmpty(customerAcqLinkPageReply.getRepliesList())) {
            customerAcqLinkPageReply = queryWorkWechatFromRemote(WorkWechatQueryVO.builder().status(2).id(id).page(1).pageSize(1).build(), bizAccountUserInfo);
            if (CollectionUtils.isEmpty(customerAcqLinkPageReply.getRepliesList())) {
                log.warn("未查询到企业微信 , id - {}", id);
                return null;
            }
        }
        return obtainWorkWechatVOByReply(customerAcqLinkPageReply).get(0);
    }

    @Override
    public WorkWechatDataTotalVO getDataTotal(BaseDataQueryVO dataQueryVO, Long loginUid) {
        checkAuthorization(loginUid);
        CustomerAcqLinkSumDataReply customerAcqLinkSumData = workWechatGrpcService.getCustomerAcqLinkSumData(obtainCustomerAcqLinkWithTimeReq(dataQueryVO, loginUid));
        return WorkWechatDataTotalVO.builder()
                .likeNum(customerAcqLinkSumData.getAddFans())
                .chatNum(customerAcqLinkSumData.getWxChat())
                .build();
    }

    private CustomerAcqLinkWithTimeReq obtainCustomerAcqLinkWithTimeReq(BaseDataQueryVO dataQueryVO, Long loginUid) {
        checkAuthorization(loginUid);
        BizAccountUserInfo bizAccountUserInfo = bizAccountDomainService.checkUserAdId(loginUid);
        CustomerAcqLinkWithTimeReq.Builder builder = CustomerAcqLinkWithTimeReq.newBuilder()
                .setAccountId(bizAccountUserInfo.getAdId().intValue()).setId(dataQueryVO.getToolId());
        if (Objects.nonNull(dataQueryVO.getStart())) {
            builder.setStartTime(dataQueryVO.getStart().getTime());
        }
        if (Objects.nonNull(dataQueryVO.getEnd())) {
            builder.setEndTime(dataQueryVO.getEnd().getTime());
        }
        return builder.build();
    }

    @Override
    public BaseDataRespVO getData(BaseDataQueryVO dataQueryVO, Long loginUid) {
        checkAuthorization(loginUid);
        CustomerAcqLinkWithTimePageReq.Builder builder = CustomerAcqLinkWithTimePageReq.newBuilder()
                .setAccountId(bizAccountDomainService.checkUserAdId(loginUid).getAdId().intValue())
                .setPage(dataQueryVO.getPage())
                .setSize(dataQueryVO.getPageSize())
                .setId(dataQueryVO.getToolId());
        if (Objects.nonNull(dataQueryVO.getStart())) {
            builder.setStartTime(dataQueryVO.getStart().getTime());
        }
        if (Objects.nonNull(dataQueryVO.getStart())) {
            builder.setEndTime(dataQueryVO.getEnd().getTime());
        }
        CustomerAcqLinkDataPageReply customerAcqLinkDataByPage = workWechatGrpcService.getCustomerAcqLinkDataByPage(builder.build());
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        LinkedList<Map<String, String>> heads = obtainHead();
        pageInfo.setTotal(customerAcqLinkDataByPage.getTotal());
        if (customerAcqLinkDataByPage.getTotal() == 0) {
            heads.clear();
        }
        pageInfo.setList(customerAcqLinkDataByPage.getRepliesList().stream().map(pageData -> {
            Map<String, Object> map = new HashMap<>();
            map.put("获客时间", sdf.format(new Date(pageData.getMtime())));
            map.put("获客类型", pageData.getDataTypeDesc());
            return map;
        }).collect(Collectors.toList()));
        return BaseDataRespVO.builder().head(heads).data(pageInfo).build();
    }

    private LinkedList<Map<String, String>> obtainHead() {
        LinkedList<Map<String, String>> heads = new LinkedList<>();
        Map<String, String> head1 = new HashMap<>();
        Map<String, String> head2 = new HashMap<>();
        head1.put("获客时间", "获客时间");
        head2.put("获客类型", "获客类型");
        heads.add(head1);
        heads.add(head2);
        return heads;
    }

    @Override
    @AsyncRequest(asyncKey = "#dataQueryVO.asyncKey", async = "#dataQueryVO.async")
    public BaseResponse<BaseDataExportRepVO> exportData(BaseDataQueryVO dataQueryVO, Long loginUid) {
        checkAuthorization(loginUid);
        CustomerAcqLinkDataReply customerAcqLinkData = workWechatGrpcService.getCustomerAcqLinkData(obtainCustomerAcqLinkWithTimeReq(dataQueryVO, loginUid));
        if (CollectionUtils.isEmpty(customerAcqLinkData.getRepliesList())) {
            throw new ClientViewException(BizErrorCode.DATA_EMPTY);
        }
        List<WorkWechatDataDetailVO> collect = customerAcqLinkData.getRepliesList().stream().map(c -> WorkWechatDataDetailVO.builder()
                .type(c.getDataTypeDesc())
                .time(sdf.format(new Date(c.getMtime())))
                .build()).collect(Collectors.toList());
        return new BaseResponse<>(BaseDataExportRepVO.builder().fileUrl(excelUtil.exportWithUrl(WorkWechatDataDetailVO.class, collect, String.format("work_wechat_data-%d_%d.xlsx", dataQueryVO.getToolId(), new Date().getTime()), "")).asyncKey(dataQueryVO.getAsyncKey()).build());
    }

    @Override
    public BaseToolVO findBaseTool(Long id, Integer type, Long loginMid) {
        return transfer(findWorkWechat(id, loginMid));
    }

    @Override
    public List<BaseToolVO> queryBaseTool(Integer type, String name, Long midAndValidLogin) {
        return queryWorkWechat(WorkWechatQueryVO.builder().status(1).page(1).pageSize(Constants.MAX_PAGE_SIZE).name(name).build(), midAndValidLogin).getList().stream().map(this::transfer).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * WorkWechatVO 转化成BaseTool
     */
    private BaseToolVO transfer(WorkWechatVO workWechatVO) {
        if (Objects.isNull(workWechatVO)) {
            return null;
        }
        return BaseToolVO.builder()
                .id(String.valueOf(workWechatVO.getId()))
                .name(workWechatVO.getName())
                .createTime(workWechatVO.getCtime())
                .status(workWechatVO.getStatus())
                .imgUrl(workWechatImgUrl)
                .launchUrl(workWechatVO.getLaunchUrl())
                .build();
    }

    @Override
    public boolean hasAuthorization(Long loginUid) {
//        BizAccountUserInfo bizAccountUserInfo = bizAccountDomainService.checkUserAdId(loginUid);
//        CheckAccountLabelReply checkAccountLabelReply = adAccountLabelGrpcService.checkAccountWithLabel(CheckAccountLabelReq.newBuilder().setAccountId(bizAccountUserInfo.getAdId().intValue()).setLabelId(Constants.WORK_WECHAT_LABEL_ID).build());
//        return Objects.nonNull(checkAccountLabelReply) && checkAccountLabelReply.getResult();
        return true;
    }

    @Override
    public void checkAuthorization(Long loginUid) {
        if (!hasAuthorization(loginUid)) {
            throw new ClientViewException("无企业微信权限");
        }
    }
}
