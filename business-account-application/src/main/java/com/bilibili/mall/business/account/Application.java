package com.bilibili.mall.business.account;

import com.bilibili.mall.common.utils.MainCostUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * starter
 *
 * <AUTHOR> DDD
 * @since ********
 */
@EnableFeignClients
@EnableAsync(proxyTargetClass = true)
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication
@EnableScheduling
public class Application {

    /**
     * 启动步骤(接入 paladin 后有问题请先查阅文档, 不要一报错就问其琛和冲)
     * 1. 在 VM options 中添加启动参数 -Dspring.profiles.active=local
     * 2. 仔细阅读启动类的每一行代码以及注释 * 3
     * 3. 运行项目即可启动成功
     *
     * @param args 启动参数
     * @see <a href="https://info.bilibili.co/display/OpenPlateform/kraken-paladin">https://info.bilibili.co/display/OpenPlateform/kraken-paladin</a>
     */
    public static void main(String[] args) {
        MainCostUtils.statistics(() -> {
            if("local".equalsIgnoreCase(System.getProperty("spring.profiles.active"))) {
                // 如果接入 paladin 平台, 在 local 模式下需要打开下面两行配置
                 System.setProperty("APP_ID", "mall.c.business-account");
                 System.setProperty("CONF_TOKEN", "2b7a6ce088ffcaae764cf6aa8f49ffa1");
            }
            // 配置需要拉取的所有 paladin namespace, 这里请手动修改下, 按需配置
            System.setProperty("kraken.paladin.namespaces", "application,feign,mybatis,ratelimit-config,swagger,databus");
            SpringApplication.run(Application.class, args);
        }, "mall.c.business-account");
    }

}
