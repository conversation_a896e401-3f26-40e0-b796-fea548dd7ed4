package com.bilibili.mall.business.account.service;

import com.bilibili.mall.business.account.api.request.account.*;
import com.bilibili.mall.business.account.api.vo.account.*;
import com.bilibili.mall.business.account.bos.BizAccountUserBo;
import com.bilibili.mall.business.account.databus.dto.WorkOrderAuditResult;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.dto.*;
import com.bilibili.mall.business.account.dto.*;
import com.bilibili.mall.business.account.infrastructure.entity.BizAccountUser;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/31 2:28 下午
 */

public interface BizAccountService {

    UserLoginAccountVO queryLoginUserInfo(Long loginUid);

    PageInfo<StaffInfoVO> queryStaffList(StaffQueryRequest request, Long loginUid);

    /**
     * 员工账号绑定
     *
     * @param request
     */
    BindUserInfoVO queryToBindInfo(StaffInfoRequest request);

    StaffInviteInfoVO inviteUser(StaffInviteRequest request, Long loginUid);

    StaffInviteStatusInfoVO queryInviteStatus(StaffInviteQueryRequest request);

    TokenInfoVO queryTokenInfo(TokenInfoQueryRequest request, Long loginUid);

    Void acceptTokenInfo(TokenInfoAcceptRequest request, Long loginUid);


    /**
     * 员工账号授权修改
     *
     * @param request
     */
    Void updateStaffAuth(StaffAuthRequest request, Long loginUid);

    /**
     * 员工备注修改
     *
     * @param request
     */
    Void updateStaffRemark(StaffRemarkRequest request, Long loginUid);

    /**
     * 员工账号解绑
     *
     * @param request
     */
    Void unBindStaff(StaffUnbindRequest request, Long loginUid);

    void sendBizAccountInfoMsgForMaster(Long uid);

    UserLoginAccountVO fakeLogin(Long uid, Long managerUid);

    void checkFakeLoginUid(Long loginUid);

    /**
     * 缓存查询 5分钟左右
     *
     * @param uid
     * @return
     */
    Integer queryShopId(Long uid);

    Boolean appMsgCount(Long loginUid);

    Map<Long, BizAccountUserBo> getBizAccountMap();

    Boolean accountOpenAuth(Long uid, String authCode);

    Boolean hasBusinessPrivilege(Long loginUid);

    List<Long> queryPrivilegeUidList(Long loginUid);

    List<BizAccountUser> queryByAdId(Long adId);

    AccountStatusDto queryAccountStatus(Long uid);

    CheckRegisterAccountDto checkRegisterPersonalAccount(Long uid);

    CheckRegisterAccountDto checkRegisterEnterpriseAccount(Long uid);

    Integer registerEnterpriseAccount(RegisterEnterpriseAccountDto dto);

    Integer registerPersonalAccount(RegisterPersonalAccountDto dto);

    Integer updateEnterpriseAccount(RegisterEnterpriseAccountDto dto);

    Integer updatePersonalAccount(RegisterPersonalAccountDto dto);

    /**
     * 查询蓝v信息
     *
     * @param uid
     */
    UserOfficialInfoDto queryUserOfficialInfo(Long uid);


    Integer getPhoneVerificationCode(String ip, String phoneNumber);

    CrmBihuoQualificationDto queryBihuoEnterpriseInfo(Long uid);

    AccountQualificationDto queryCustomerInfoDetail(Long uid);

    AccountQualificationDto queryCustomerInfoDetail(String businessLicenceCode, String personalIdCardNumber);

    /**
     * 查询一级行业列表
     *
     * @return 行业列表
     */
    List<IndustryInfoDto> queryFirstIndustry();

    /**
     * 根据父ID查询行业列表
     *
     * @param pid 父ID
     * @return 行业列表
     */
    List<IndustryInfoDto> queryIndustryByPid(Integer pid);

    /**
     * 查询资质类型列表
     *
     * @return 资质类型列表
     */
    List<CrmQualificationTypeDto> getQualificationList();

    List<CrmQualificationTypeDto> getSpecialInfoTypeList();

    void addOneAccount(Long mid, Long adId, Integer accountType, Integer hasAssistant);

    void processQualificationMessage(WorkOrderAuditResult workOrderAuditResult);

    BiliUserInfoDto queryBiliUserInfo(Long uid);

    Boolean authBlueVInfo(Long uid);

    Boolean authBihuoInfo(Long uid);

    void updateEnterpriseAccountByBlueVInfo(Long uid);

    RealNameInfoCheckDto realNameInfoCheck(String idCardNumber, String realName, Long uid);

    PersonalAccountCrmInfoDto queryCrmPersonalExistAccount(String idCardNumber);
}
