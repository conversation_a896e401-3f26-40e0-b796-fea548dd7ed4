package com.bilibili.mall.business.account.service.impl;

import com.bapis.ad.mgk.business_tool.CreateOnlineConsultComponentReply;
import com.bapis.ad.mgk.business_tool.CreateOnlineConsultComponentReq;
import com.bapis.ad.mgk.business_tool.QueryOnlineConsultComponentReply;
import com.bapis.ad.mgk.business_tool.QueryOnlineConsultComponentReq;
import com.bilibili.mall.business.account.api.enums.AuthTypeEnum;
import com.bilibili.mall.business.account.api.vo.base.BaseToolVO;
import com.bilibili.mall.business.account.api.vo.onlineconsult.OnlineConsultVO;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.WorkWechatGrpcService;
import com.bilibili.mall.business.account.domain.domains.account.entity.BizAccountUserInfo;
import com.bilibili.mall.business.account.domain.domains.account.service.BizAccountDomainService;
import com.bilibili.mall.business.account.enums.ComponentRelationType;
import com.bilibili.mall.business.account.infrastructure.exception.BizErrorCode;
import com.bilibili.mall.business.account.service.OnlineConsultService;
import com.bilibili.mall.business.account.service.VideoComponentService;
import com.bilibili.mall.business.account.utils.DateUtil;
import com.bilibili.mall.common.exception.ClientViewException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/22 17:19
 */
@Service
public class OnlineConsultServiceImpl implements OnlineConsultService {

    @Resource
    private WorkWechatGrpcService workWechatGrpcService;

    @Resource
    private BizAccountDomainService bizAccountDomainService;

    @Resource
    private VideoComponentService videoComponentService;

    @Override
    public boolean addOnlineConsult(Long mid) {
        BizAccountUserInfo bizAccountUserInfo = bizAccountDomainService.queryAccountUserInfo(mid);
        if (Objects.isNull(bizAccountUserInfo)) {
            throw new ClientViewException("用户信息无效");
        }
        CreateOnlineConsultComponentReq request = CreateOnlineConsultComponentReq.newBuilder()
                .setAccountId(bizAccountUserInfo.getAdId())
                .setMid(bizAccountUserInfo.getUid())
                .setContent("在线咨询")
                .build();
        try {
            CreateOnlineConsultComponentReply createOnlineConsultComponentReply = workWechatGrpcService.createOnlineConsultComponent(request);
            return true;
        } catch (Exception e) {
            // 重复创建校验
            if (e.getMessage().contains("该帐号已有创建过的工具")) {
                throw new com.bilibili.mall.business.account.infrastructure.exception.ClientViewException(BizErrorCode.ONLINE_CONSULT_EXIST);
            }
        }
        return false;
    }

    @Override
    public OnlineConsultVO queryOnlineConsult(Long mid) {
        BizAccountUserInfo bizAccountUserInfo = bizAccountDomainService.queryAccountUserInfo(mid);
        if (Objects.isNull(bizAccountUserInfo)) {
            throw new ClientViewException("用户信息无效");
        }
        CreateOnlineConsultComponentReply createOnlineConsultComponentReply = getCreateOnlineConsultComponentReply(bizAccountUserInfo.getAdId());
        if (createOnlineConsultComponentReply == null) {
            return null;
        }
        int compCount = videoComponentService.selectBusinessCompBindCount(bizAccountUserInfo, AuthTypeEnum.ONLINE_CONSULT.getCode(), String.valueOf(createOnlineConsultComponentReply.getToolId()), ComponentRelationType.VIDEO.getType());
        int dynCount = videoComponentService.selectBusinessCompBindCount(bizAccountUserInfo, AuthTypeEnum.ONLINE_CONSULT.getCode(), String.valueOf(createOnlineConsultComponentReply.getToolId()), ComponentRelationType.DYNAMIC.getType());
        OnlineConsultVO onlineConsultVO = buildBaseOnlineConsultVO(createOnlineConsultComponentReply);
        onlineConsultVO.setAssociateArchsNum(compCount);
        onlineConsultVO.setAssociateDynNum(dynCount);
        return onlineConsultVO;
    }

    private OnlineConsultVO buildBaseOnlineConsultVO(CreateOnlineConsultComponentReply createOnlineConsultComponentReply) {
        OnlineConsultVO onlineConsultVO = new OnlineConsultVO();
        onlineConsultVO.setCreateTime(DateUtil.formatDate(createOnlineConsultComponentReply.getCtime()));
        onlineConsultVO.setId(String.valueOf(createOnlineConsultComponentReply.getToolId()));
        onlineConsultVO.setName(createOnlineConsultComponentReply.getContent());
        onlineConsultVO.setStatus(createOnlineConsultComponentReply.getStatus());
        return onlineConsultVO;
    }

    @Override
    public CreateOnlineConsultComponentReply getCreateOnlineConsultComponentReply(Long adid) {
        QueryOnlineConsultComponentReq request = QueryOnlineConsultComponentReq.newBuilder()
                .setAccountId(adid)
                .build();
        QueryOnlineConsultComponentReply queryOnlineConsultComponentReply = workWechatGrpcService.queryOnlineConsultComponentList(request);
        if (Objects.isNull(queryOnlineConsultComponentReply) || CollectionUtils.isEmpty(queryOnlineConsultComponentReply.getOnlineConsultComponentListList())) {
            return null;
        }
        CreateOnlineConsultComponentReply createOnlineConsultComponentReply = queryOnlineConsultComponentReply.getOnlineConsultComponentListList().get(0);
        return createOnlineConsultComponentReply;
    }

    @Override
    public BaseToolVO findBaseTool(Long id, Integer type, Long loginMid) {
        BizAccountUserInfo bizAccountUserInfo = bizAccountDomainService.queryAccountUserInfo(loginMid);
        CreateOnlineConsultComponentReply createOnlineConsultComponentReply = getCreateOnlineConsultComponentReply(bizAccountUserInfo.getAdId());
        if (Objects.isNull(createOnlineConsultComponentReply) || (!Objects.isNull(id) && !id.equals(createOnlineConsultComponentReply.getToolId()))) {
            return null;
        }
        BaseToolVO baseToolVO = new BaseToolVO();
        baseToolVO.setId(String.valueOf(createOnlineConsultComponentReply.getToolId()));
        baseToolVO.setName(createOnlineConsultComponentReply.getContent());
        baseToolVO.setCreateTime(DateUtil.formatDate(createOnlineConsultComponentReply.getCtime()));
        baseToolVO.setStatus(createOnlineConsultComponentReply.getStatus());
        return baseToolVO;
    }

    @Override
    public List<BaseToolVO> queryBaseTool(Integer type, String name, Long midAndValidLogin) {
        BaseToolVO baseToolVO = findBaseTool(null, null, midAndValidLogin);
        if (StringUtils.isNotBlank(name) && !baseToolVO.getName().contains(name)) {
            return null;
        }
        return Arrays.asList(baseToolVO);
    }
}
