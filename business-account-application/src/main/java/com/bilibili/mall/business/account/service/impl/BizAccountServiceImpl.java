package com.bilibili.mall.business.account.service.impl;

import com.alibaba.fastjson.JSON;
import com.bapis.account.service.v2.*;
import com.bapis.ad.account.crm.acc.AccountBaseReply;
import com.bapis.ad.account.crm.acc.AccountIdReq;
import com.bapis.ad.account.crm.acc.CrmAccountServiceGrpc;
import com.bapis.ad.adp.component.BatchDeleteComponentReq;
import com.bapis.ad.adp.component.BatchDeleteComponentRes;
import com.bapis.ad.adp.component.DeleteCompponentRequest;
import com.bapis.ad.crm.account.UserType;
import com.bapis.ad.crm.customer.CustomerCategory;
import com.bapis.ad.crm.customer.PromotionType;
import com.bapis.ad.mgk.business_tool.CreateOnlineConsultComponentReply;
import com.bilibili.mall.business.account.api.dto.BizAccountInfoMsg;
import com.bilibili.mall.business.account.api.enums.AuthTypeEnum;
import com.bilibili.mall.business.account.api.enums.RoleTypeEnum;
import com.bilibili.mall.business.account.api.enums.UserOpTypeEnum;
import com.bilibili.mall.business.account.api.request.account.*;
import com.bilibili.mall.business.account.api.vo.account.*;
import com.bilibili.mall.business.account.bos.BizAccountUserBo;
import com.bilibili.mall.business.account.config.BusinessQualificationConfig;
import com.bilibili.mall.business.account.constants.Constants;
import com.bilibili.mall.business.account.converter.AccountConverter;
import com.bilibili.mall.business.account.converter.CrmConverter;
import com.bilibili.mall.business.account.databus.dto.WorkOrderAuditResult;
import com.bilibili.mall.business.account.domain.anticorruption.consumer.AuthorityIntranetConsumer;
import com.bilibili.mall.business.account.domain.anticorruption.consumer.ConversationOptConsumer;
import com.bilibili.mall.business.account.domain.anticorruption.consumer.StaffConsumer;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.*;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.dto.*;
import com.bilibili.mall.business.account.domain.anticorruption.rpcservice.UserService;
import com.bilibili.mall.business.account.domain.databus.dto.ToolRemoveMsg;
import com.bilibili.mall.business.account.domain.databus.pub.BizAccountInfoPub;
import com.bilibili.mall.business.account.domain.databus.pub.ToolRemoveMsgPub;
import com.bilibili.mall.business.account.domain.domains.account.component.InviteTokenComponent;
import com.bilibili.mall.business.account.domain.domains.account.entity.BizAccountUserInfo;
import com.bilibili.mall.business.account.domain.domains.account.entity.UserTokenInfo;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountInfoRepository;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountQualificationInfoRepository;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountUserLogRepository;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountUserRepository;
import com.bilibili.mall.business.account.domain.domains.account.service.BizAccountDomainService;
import com.bilibili.mall.business.account.domain.enums.QualificationAuditStatusEnum;
import com.bilibili.mall.business.account.dto.*;
import com.bilibili.mall.business.account.enums.*;
import com.bilibili.mall.business.account.infrastructure.entity.*;
import com.bilibili.mall.business.account.infrastructure.exception.BizErrorCode;
import com.bilibili.mall.business.account.infrastructure.utils.CommonHelper;
import com.bilibili.mall.business.account.infrastructure.utils.FunctionUtil;
import com.bilibili.mall.business.account.service.*;
import com.bilibili.mall.common.enhance.exception.BusinessException;
import com.bilibili.mall.common.exception.ClientViewException;
import com.bilibili.mall.common.response.BaseResponse;
import com.bilibili.mall.customer.workbench.api.dto.staff.StaffMessageListDTO;
import com.bilibili.mall.customer.workbench.api.dto.workbench.ConversationStaffListDTO;
import com.bilibili.mall.customer.workbench.api.dto.workbench.StaffMainDTO;
import com.bilibili.mall.customer.workbench.api.enums.CustomerBizIdEnum;
import com.bilibili.mall.customer.workbench.api.query.staff.StaffMainQuery;
import com.bilibili.mall.customer.workbench.api.query.staff.StaffMessageQuery;
import com.bilibili.mall.customer.workbench.api.query.workbench.ConversationStaffListQuery;
import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import com.bilibili.mall.kraken.retry.annotation.RetryConn;
import com.bilibili.warp.databus.Message;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.bilibili.mall.business.account.config.BusinessQualificationConfig.REDISSON_LOCK_PREFIX;

/**
 * <AUTHOR>
 * @date 2024/1/31 4:19 下午
 */
@Slf4j
@Service
public class BizAccountServiceImpl implements BizAccountService {
    @Resource
    private BizAccountDomainService bizAccountDomainService;
    @Resource
    private UserService userService;
    @Resource
    private BizAccountInfoRepository bizAccountInfoRepository;
    @Resource
    private BizAccountUserRepository bizAccountUserRepository;
    @Resource
    private BizAccountUserLogRepository bizAccountUserLogRepository;
    @Resource
    private AccountMemberGrpcService accountMemberGrpcService;
    @Resource
    private InviteTokenComponent inviteTokenComponent;
    @Resource
    private ToolRemoveMsgPub toolRemoveMsgPub;
    @Resource
    private AppletsToolService appletsToolService;
    @Resource
    private WorkWechatToolService workWechatToolService;
    @Resource
    private ConversationOptConsumer conversationOptConsumer;
    @Resource
    private VideoComponentService videoComponentService;
    @Resource
    private BizAccountInfoPub bizAccountInfoPub;
    @Resource
    private CommentComponentService commentComponentService;
    @Resource
    private AuthorityIntranetConsumer authorityIntranetConsumer;
    @Resource
    private StaffConsumer staffConsumer;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private AdAccountLabelGrpcService adAccountLabelService;
    @Resource
    private AdAccountInfoGrpcService adAccountInfoGrpcService;
    @Resource
    private OnlineConsultService onlineConsultService;

    /**
     * 员工号白名单
     */
    @DynamicValue
    @Value("${staffIdWhiteList:}")
    private String staffIdWhiteList;

    private static final int MAX_STAFF_COUNT = 100;

    @DynamicValue
    @Value("${delete.retry.times}")
    private int deleteRetryTimes;

    @Value("${wechat.app.id:20}")
    private Integer WECHAT_APP_ID;

    @Value("${enterprise.wechat.app.id:163}")
    private Integer ENTERPRISE_WECHAT_APP_ID;

    @DynamicValue
    @Value("${register.phone.verify.switch:0}")
    private Integer REGISTER_PHONE_VERIFY_SWITCH;

    @DynamicValue
    @Value("${assistant.auth.united.first.industry.id.list:1123}")
    private String assistantAuthUnitedFirstIndustryIdList;
    // 账号注册相关的错误信息已移至 RegisterAccountErrorEnum

    @RPCClient("sycpb.cpm.ad-account")
    private CrmAccountServiceGrpc.CrmAccountServiceBlockingStub accountServiceBlockingStub;

    private volatile Map<Long, BizAccountUserBo> bizAccountMap = new HashMap<>();
    @Autowired
    private BizAccountQualificationInfoRepository bizAccountQualificationInfoRepository;
    @Autowired
    private AdCrmGrpcService adCrmGrpcService;
    @Autowired
    private UserAccountGrpcService userAccountGrpcService;
    @Autowired
    private BusinessQualificationConfig businessQualificationConfig;
    @Autowired
    private RedissonClient redissonclient;

    @Override
    public UserLoginAccountVO queryLoginUserInfo(Long loginUid) {
        boolean firstJumpCallUp = false;
        BizAccountUserInfo bizAccountUserInfo =
                bizAccountDomainService.queryAccountUserInfo(loginUid);

        UserAccountBaseInfoDto userInfo = userService.querySingleUserInfo(String.valueOf(loginUid));

        List<String> loginUidAuthList = new ArrayList<>();
        if (Objects.nonNull(bizAccountUserInfo)) {
            int adAccountId = bizAccountUserInfo.getAdId().intValue();
            Set<Integer> adAccountLabels = adAccountLabelService.queryAdAccountLabels(adAccountId);
            List<Integer> appIds = adAccountInfoGrpcService.queryAdAccountAwakenApp(adAccountId);
            List<String> masterAuthListV2 = queryAdAccountAuth(adAccountId, adAccountLabels, appIds);

            if (Objects.equals(bizAccountUserInfo.getRoleType(), RoleTypeEnum.MASTER.getType())) {
                loginUidAuthList = masterAuthListV2;

                if (masterAuthListV2.contains(AuthTypeEnum.APP_PACKAGE.getCode()) && adAccountLabels.contains(Constants.FIRST_JUMP_CALL_UP_LABEL_ID)) {
                    firstJumpCallUp = true;
                }
            } else if (Objects.equals(bizAccountUserInfo.getRoleType(), RoleTypeEnum.STAFF.getType())) {
                for (String masterAuth : masterAuthListV2) {
                    if (bizAccountUserInfo.getAuthList().contains(masterAuth)) {
                        loginUidAuthList.add(masterAuth);
                    }
                    if (bizAccountUserInfo.getAuthList().contains(BizAccountAuthEnum.RESERVE.getCode()) && masterAuth.equals(AuthTypeEnum.THIRD_PARTY_LANDING_PAGE_URL.getCode())) {
                        loginUidAuthList.add(AuthTypeEnum.THIRD_PARTY_LANDING_PAGE_URL.getCode());
                    }
                }
                loginUidAuthList = loginUidAuthList.stream().distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEqualCollection(bizAccountUserInfo.getAuthList(), loginUidAuthList)) {
                    BizAccountUser bizAccountUser = new BizAccountUser();
                    bizAccountUser.setAuth(FunctionUtil.joinByByComma(loginUidAuthList, String::valueOf));
                    bizAccountUser.setUid(loginUid);
                    bizAccountUserRepository.updateByUid(bizAccountUser);
                }
            }

        }
        AccountBaseReply accountBase = null;
        if (Objects.nonNull(bizAccountUserInfo)) {
            accountBase = accountServiceBlockingStub.getAccountBase(AccountIdReq.newBuilder().setAccountId(bizAccountUserInfo.getAdId().intValue()).build());
        }
        boolean isPersonalAccount = false;
        if (Objects.nonNull(accountBase) && accountBase.getCode() == 0) {
            //
            isPersonalAccount = accountBase.getData().getUserType() != 1;
        }

        return UserLoginAccountVO.builder()
                .accountId(
                        Objects.nonNull(bizAccountUserInfo)
                                ? bizAccountUserInfo.getAccountId()
                                : null)
                .uid(loginUid)
                .roleType(
                        Objects.nonNull(bizAccountUserInfo)
                                ? bizAccountUserInfo.getRoleType()
                                : null)
                .userNickName(Objects.nonNull(userInfo) ? userInfo.getName() : "")
                .userFace(Objects.nonNull(userInfo) ? userInfo.getFace() : "")
                .authList(
                        Objects.nonNull(bizAccountUserInfo)
                                ? bizAccountUserInfo.getAuthList()
                                : null)
                .authListV2(loginUidAuthList)
                .hasThirdPartyLandingPage(loginUidAuthList.contains(AuthTypeEnum.THIRD_PARTY_LANDING_PAGE_URL.getCode()))
                .hasVideoManagerBusinessData(redisTemplate.opsForSet().isMember("business-account-video-manager-business-data-member", loginUid))
                .shopId(bizAccountDomainService.queryAccountShopIdDefaultZero(loginUid))
                .firstJumpCallUp(firstJumpCallUp)
                .isPersonalAccount(isPersonalAccount)
                .hasAssistant(Objects.nonNull(bizAccountUserInfo)
                        ? bizAccountUserInfo.getHasAssistant()
                        : null)
                .build();
    }

    @Override
    public PageInfo<StaffInfoVO> queryStaffList(StaffQueryRequest request, Long loginUid) {
        BizAccountUserInfo bizAccountUserInfo =
                bizAccountDomainService.queryAccountUserInfo(loginUid);
        if (Objects.isNull(bizAccountUserInfo)) {
            throw new ClientViewException("非法账号");
        }
        if (!Objects.equals(bizAccountUserInfo.getRoleType(), RoleTypeEnum.MASTER.getType())) {
            throw new ClientViewException("非企业账号，不能查看其他员工信息");
        }

        PageMethod.startPage(request.getPageNo(), request.getPageSize());
        Page<BizAccountUser> staffListPage =
                (Page<BizAccountUser>)
                        bizAccountUserRepository.selectByAccountIdAndRole(
                                bizAccountUserInfo.getAccountId(), RoleTypeEnum.STAFF.getType());
        PageInfo<BizAccountUser> originPageInfo = new PageInfo<>(staffListPage);
        PageInfo<StaffInfoVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(originPageInfo, resultPageInfo, "list");


        String uids = FunctionUtil.joinByByComma(originPageInfo.getList(), v -> String.valueOf(v.getUid()));
        List<UserAccountBaseInfoDto> userInfos = userService.queryUserInfo(uids);


        resultPageInfo.setList(AccountConverter.convert2StaffInfoVOS(originPageInfo, userInfos));
        return resultPageInfo;
    }

    /**
     * 员工账号绑定
     *
     * @param request
     */
    @Override
    public BindUserInfoVO queryToBindInfo(StaffInfoRequest request) {
        if (CommonHelper.checkSensitiveUid(request.getUid())) {
            throw new ClientViewException("该账号不支持绑定");
        }

        UserAccountBaseInfoDto userInfo = userService.querySingleUserInfo(String.valueOf(request.getUid()));
        if (userInfo == null) {
            throw new ClientViewException("用户不存在");
        }

        boolean realName = accountMemberGrpcService.hasRealName(request.getUid());
        if (!realName) {
            throw new ClientViewException("该账号未进行实名认证，认证后支持绑定");
        }

        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(request.getUid());
        if (Objects.nonNull(bizAccountUser)) {
            throw new ClientViewException("该账号已经绑定过经营号");
        }
        return BindUserInfoVO.builder()
                .uid(userInfo.getMid())
                .userNickName(userInfo.getName())
                .userFace(userInfo.getFace())
                .build();
    }

    @Override
    public StaffInviteInfoVO inviteUser(StaffInviteRequest request, Long loginUid) {
        //员工号白名单校验
        if (!StringUtils.isEmpty(staffIdWhiteList)) {
            List<Long> whiteList = FunctionUtil.splitByComma(staffIdWhiteList, Long::valueOf);
            if (!whiteList.contains(request.getStaffUid())) {
                throw new ClientViewException("邀请的用户不在白名单内");
            }
        }
        //工具白名单校验
        toolWhiteListCheck(request.getAuthList(), loginUid);

        BizAccountUserInfo bizAccountUserInfo =
                bizAccountDomainService.queryAccountUserInfo(loginUid);
        if (Objects.isNull(bizAccountUserInfo)
                || !Objects.equals(bizAccountUserInfo.getRoleType(), RoleTypeEnum.MASTER.getType())) {
            throw new ClientViewException("非企业账号不能绑定员工");
        }
        if (Objects.isNull(bizAccountUserInfo.getAdId()) || Objects.equals(0L, bizAccountUserInfo.getAdId())) {
            throw new ClientViewException("经营号未绑定广告账户");
        }
        UserAccountBaseInfoDto userInfo = userService.querySingleUserInfo(String.valueOf(loginUid));
        if (userInfo == null) {
            throw new ClientViewException("查询用户信息异常，请重试");
        }

        final int tokenExpire = 5 * 60;
        long now = System.currentTimeMillis();

        String token = InviteTokenComponent.genToken(request.getStaffUid());

        UserTokenInfo tokenInfo =
                UserTokenInfo.builder()
                        .accountId(bizAccountUserInfo.getAccountId())
                        .uid(request.getStaffUid())
                        .inviterUid(loginUid)
                        .remarkName(request.getStaffRemarkName())
                        .authList(request.getAuthList())
                        .bizAccountFace(userInfo.getFace())
                        .bizAccountName(userInfo.getName())
                        .token(token)
                        .expire(now + tokenExpire * 1000)
                        .status(UserTokenInfo.TokenStatusEnum.WAIT)
                        .build();

        inviteTokenComponent.saveToken(token, tokenInfo, tokenExpire);

        return StaffInviteInfoVO.builder().token(token).expire(tokenInfo.getExpire()).build();
    }

    private void toolWhiteListCheck(List<String> authList, Long uid) {
        if (CollectionUtils.isEmpty(authList) || Objects.isNull(uid)) {
            return;
        }
        //检查微信小程序白名单
        if (authList.contains(AuthTypeEnum.MINI_APP.getCode())) {
            boolean hasAuthorization = appletsToolService.hasAuthorization(uid);
            if (!Boolean.TRUE.equals(hasAuthorization)) {
                throw new ClientViewException("邀请的用户不在小程序白名单内");
            }
        }
        //检查企微白名单
        if (authList.contains(AuthTypeEnum.CONTACT_QW.getCode())) {
            boolean hasAuthorization = workWechatToolService.hasAuthorization(uid);
            if (!Boolean.TRUE.equals(hasAuthorization)) {
                throw new ClientViewException("邀请的用户不在企微白名单内");
            }
        }
    }

    @Override
    public StaffInviteStatusInfoVO queryInviteStatus(StaffInviteQueryRequest request) {
        UserTokenInfo tokenInfo = inviteTokenComponent.getTokenInfo(request.getToken());
        if (Objects.isNull(tokenInfo)) {
            throw new ClientViewException("二维码已失效，请重新生成");
        }
        return StaffInviteStatusInfoVO.builder()
                .status(tokenInfo.getStatus().getCode())
                .expire(tokenInfo.getExpire())
                .build();
    }

    @Override
    public TokenInfoVO queryTokenInfo(TokenInfoQueryRequest request, Long loginUid) {
        UserTokenInfo tokenInfo = inviteTokenComponent.getTokenInfo(request.getToken());
        if (Objects.isNull(tokenInfo)) {
            throw new ClientViewException("二维码已失效");
        }
        if (!Objects.equals(tokenInfo.getUid(), loginUid)) {
            throw new ClientViewException("请选择正确的二维码");
        }

        // 更新扫码状态
        if (UserTokenInfo.TokenStatusEnum.WAIT.equals(tokenInfo.getStatus())) {
            // 等待扫码
            tokenInfo.setStatus(UserTokenInfo.TokenStatusEnum.SCAN);
            long expire = (tokenInfo.getExpire() - System.currentTimeMillis()) / 1000;
            if (expire > 0) {
                inviteTokenComponent.saveToken(request.getToken(), tokenInfo, expire);
            }
        }

        return TokenInfoVO.builder()
                .accountId(tokenInfo.getAccountId())
                .bizAccountName(tokenInfo.getBizAccountName())
                .bizAccountFace(tokenInfo.getBizAccountFace())
                .build();
    }

    @Override
    public Void acceptTokenInfo(TokenInfoAcceptRequest request, Long loginUid) {
        UserTokenInfo tokenInfo = inviteTokenComponent.getTokenInfo(request.getToken());
        if (Objects.isNull(tokenInfo)) {
            throw new ClientViewException("二维码已失效,请重新扫码");
        }
        if (!Objects.equals(tokenInfo.getUid(), loginUid)) {
            throw new ClientViewException("请选择正确的二维码");
        }
        if (UserTokenInfo.TokenStatusEnum.FINISHED.equals(tokenInfo.getStatus())) {
            throw new ClientViewException("已经授权成功");
        }

        BizAccountInfo bizAccountInfo = bizAccountInfoRepository.selectByAccountId(tokenInfo.getAccountId());
        if (Objects.isNull(bizAccountInfo)) {
            throw new ClientViewException("经营号不存在");
        }
        if (Objects.isNull(bizAccountInfo.getAdId()) || Objects.equals(0L, bizAccountInfo.getAdId())) {
            throw new ClientViewException("经营号未绑定广告账户");
        }

        Integer staffCount = bizAccountUserRepository.countByAccountIdAndRole(tokenInfo.getAccountId(), RoleTypeEnum.STAFF.getType());
        if (staffCount > MAX_STAFF_COUNT) {
            throw new ClientViewException("该经营号绑定账号数量已达上限");
        }

        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(tokenInfo.getUid());
        if (Objects.nonNull(bizAccountUser)) {
            throw new ClientViewException("你已经绑定过其他经营号啦");
        }

        boolean realName = accountMemberGrpcService.hasRealName(tokenInfo.getUid());
        if (!realName) {
            throw new ClientViewException("账号还未进行实名认证");
        }

        //绑定员工
        bizAccountUser = bizAccountDomainService.doBindStaff(tokenInfo);

        tokenInfo.setStatus(UserTokenInfo.TokenStatusEnum.FINISHED);
        long expire = (tokenInfo.getExpire() - System.currentTimeMillis()) / 1000;
        if (expire > 0) {
            inviteTokenComponent.saveToken(request.getToken(), tokenInfo, expire);
        }

        // 账号变更消息
        log.info("acceptTokenInfo pubBizAccountInfoMsg bizAccountUser:{}", JSON.toJSON(bizAccountUser));
        pubBizAccountInfoMsg(bizAccountUser.getUid(), bizAccountUser.getAccountId(), bizAccountUser.getRoleType(), FunctionUtil.splitByComma(bizAccountUser.getAuth(), String::valueOf), (byte) 0);
        return null;
    }

    /**
     * 员工账号授权修改
     *
     * @param request
     */
    @Override
    public Void updateStaffAuth(StaffAuthRequest request, Long loginUid) {
        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(request.getStaffUid());
        if (Objects.isNull(bizAccountUser)) {
            throw new ClientViewException("未找到绑定的账号信息");
        }

        //工具白名单校验
        toolWhiteListCheck(request.getAuthList(), request.getStaffUid());

        //校验登录账号是否主账号
        checkMasterRole(loginUid);

        // 权限减少，减少权限需要通知直播
        List<String> originCodes =
                FunctionUtil.splitByComma(bizAccountUser.getAuth(), String::valueOf);
        List<String> removeCodes =
                originCodes.stream()
                        .filter(Objects::nonNull)
                        .filter(v -> !request.getAuthList().contains(v))
                        .distinct()
                        .collect(Collectors.toList());

        // 权限点对应绑定的组件
        Map<String, List<ComponentInfo>> componentMap = videoComponentService.getUserValidComponentAvidList(request.getStaffUid(), removeCodes);
        // 检验是否可解绑权限点
        checkCanUnbind(componentMap, removeCodes, bizAccountUser);

        // 变更权限
        BizAccountUser updateUser = new BizAccountUser();
        updateUser.setId(bizAccountUser.getId());
        updateUser.setAuth(FunctionUtil.joinByByComma(request.getAuthList(), String::valueOf));
        bizAccountUserRepository.updateById(updateUser);

        if (!CollectionUtils.isEmpty(removeCodes)) {
            ToolRemoveMsg removeMsg =
                    ToolRemoveMsg.builder()
                            .uid(request.getStaffUid())
                            .unBindStaff(false)
                            .typeCodes(removeCodes)
                            .build();
            Message message =
                    Message.Builder.of(String.valueOf(request.getStaffUid()), removeMsg).build();
            toolRemoveMsgPub.pubWithRetry(message);
        }

        // 账号变更消息
        pubBizAccountInfoMsg(bizAccountUser.getUid(), bizAccountUser.getAccountId(), bizAccountUser.getRoleType(), request.getAuthList(), (byte) 1);

        // log
        BizAccountUserOpLog userOpLog =
                BizAccountUserOpLog.builder()
                        .uid(loginUid)
                        .opUid(request.getStaffUid())
                        .opType(UserOpTypeEnum.AUTH_MODIFY.getType())
                        .detail(JSON.toJSONString(updateUser))
                        .build();
        bizAccountUserLogRepository.saveAsync(userOpLog);

        // 删除在线预约评论组件
        deleteOnlineConsultComponents(bizAccountUser, componentMap.get(AuthTypeEnum.ONLINE_CONSULT.getCode()), "员工授权已解除，后续此员工已无法使用此经营组件功能，该员工部分稿件下的组件自动删除失败，请联系员工自行删除，稿件id:s%");
        return null;
    }

    private void checkCanUnbind(Map<String, List<ComponentInfo>> componentMap, List<String> removeCodes, BizAccountUser bizAccountUser) {
        // 解绑在线咨询
        checkOnlineConsult(removeCodes, bizAccountUser);

        // 解绑在线预约、企微、小程序
        List<String> checkCodes = Arrays.asList(AuthTypeEnum.RESERVE.getCode(), AuthTypeEnum.CONTACT_QW.getCode(), AuthTypeEnum.MINI_APP.getCode());
        List<String> checkRemoveCodes = removeCodes.stream().filter(checkCodes::contains).collect(Collectors.toList());
        componentMap.forEach((key, value) -> {
            if (checkRemoveCodes.contains(key) && CollectionUtils.isNotEmpty(value)) {
                throw new com.bilibili.mall.business.account.infrastructure.exception.ClientViewException(BizErrorCode.COMPONENT_EXIST);
            }
        });
    }

    private void checkOnlineConsult(List<String> removeCodes, BizAccountUser bizAccountUser) {
        if (CollectionUtils.isNotEmpty(removeCodes) && removeCodes.contains(AuthTypeEnum.ONLINE_CONSULT.getCode())) {
            // 是否存在未结束会话
            BaseResponse<ConversationStaffListDTO> conversationResponse = getConversationStaffs(bizAccountUser);
            if (Objects.isNull(conversationResponse) || conversationResponse.code != 0) {
                throw new ClientViewException("存在未结束会话,请重新提交");
            }
            if (conversationResponse.data != null && CollectionUtils.isNotEmpty(conversationResponse.data.getConversationStaffDTOList())) {
                throw new com.bilibili.mall.business.account.infrastructure.exception.ClientViewException(BizErrorCode.GOING_CHAT);
            }
        }
    }

    private void deleteOnlineConsultComponents(BizAccountUser bizAccountUser, List<ComponentInfo> componentInfoList, String showMessage) {
        if (CollectionUtils.isEmpty(componentInfoList)) {
            return;
        }
        List<Long> avidList = componentInfoList.stream().map(i -> Long.parseLong(i.getRelationId())).collect(Collectors.toList());
        // 在线预约评论组件稿件ID列表
        BizAccountInfo accountInfo = bizAccountInfoRepository.selectByAccountId(bizAccountUser.getAccountId());
        // 批量删除在线预约评论组件

        List<Long> failedAvidsList = deleteComponent(avidList, accountInfo, 1);

        // 失败重试
        if (CollectionUtils.isNotEmpty(failedAvidsList)) {
            failedAvidsList = deleteComponent(failedAvidsList, accountInfo, deleteRetryTimes);
        }
        // 重试失败
        if (CollectionUtils.isNotEmpty(failedAvidsList)) {
            throw new com.bilibili.mall.business.account.infrastructure.exception.ClientViewException(BizErrorCode.DELETE_COMPONENT_FAIL.getCode(), String.format("", JSON.toJSONString(failedAvidsList)));
        }
    }

    private List<Long> deleteComponent(List<Long> avidList, BizAccountInfo accountInfo, int retryTimes) {
        List<Long> failedAvidsList = new ArrayList<>();
        if (CollectionUtils.isEmpty(avidList)) {
            return failedAvidsList;
        }
        for (int page = 0; ; page++) {
            int endIndex = Math.min((page + 1) * 20, avidList.size());
            List<DeleteCompponentRequest> deleteCompponentRequestList = new ArrayList<>();
            List<Long> handlerAvidList = avidList.subList(page * 20, endIndex);
            handlerAvidList.forEach(avid -> {
                DeleteCompponentRequest deleteRequest = DeleteCompponentRequest.newBuilder().setAccountId(accountInfo.getAdId().intValue()).setAvid(avid).build();
                deleteCompponentRequestList.add(deleteRequest);
            });
            BatchDeleteComponentReq batchDeleteReq = BatchDeleteComponentReq.newBuilder().addAllDeleteComponentRequest(deleteCompponentRequestList).build();
            BatchDeleteComponentRes batchDeleteComponentRes = null;
            for (; retryTimes > 0; retryTimes--) {
                log.info("deleteComponent retryTimes:{}", retryTimes);
                retryTimes--;
                try {
                    batchDeleteComponentRes = commentComponentService.batchDeleteComponent(batchDeleteReq);
                    retryTimes = 0;
                } catch (Exception e) {
                    log.info("deleteComponent retry");
                }
            }
            if (Objects.isNull(batchDeleteComponentRes) || CollectionUtils.isNotEmpty(batchDeleteComponentRes.getFailedAvidsList())) {
                log.info("batchDeleteComponent failedAvidsList:{}", Objects.isNull(batchDeleteComponentRes) ? "" : JSON.toJSON(batchDeleteComponentRes.getFailedAvidsList()));
                failedAvidsList.addAll(Objects.isNull(batchDeleteComponentRes) ? handlerAvidList : batchDeleteComponentRes.getFailedAvidsList());
            }
            if (endIndex >= avidList.size()) {
                break;
            }
        }
        return failedAvidsList;
    }

    private BaseResponse<ConversationStaffListDTO> getConversationStaffs(BizAccountUser bizAccountUser) {
        ConversationStaffListQuery conversationStaffListQuery = new ConversationStaffListQuery();
        conversationStaffListQuery.setStaffMid(bizAccountUser.getUid());
        conversationStaffListQuery.setParentShopId(bizAccountUser.getAccountId());
        conversationStaffListQuery.setState(1);
        conversationStaffListQuery.setPageNum(1);
        conversationStaffListQuery.setPageSize(1);
        conversationStaffListQuery.setBizId(CustomerBizIdEnum.BUSINESS_ACCOUNT.getType());
        BaseResponse<ConversationStaffListDTO> conversationResponse = null;
        try {
            conversationResponse = conversationOptConsumer.getConversationList(conversationStaffListQuery);
        } catch (Exception e) {
            log.error("getConversationStaffs error, uid:{} accountId:{}", bizAccountUser.getUid(), bizAccountUser.getAccountId(), e);
        }
        return conversationResponse;
    }

    private void pubBizAccountInfoMsg(long uid, Long accountId, Integer roleType, List<String> authList, Byte opType) {
        BizAccountInfo accountInfo = null;
        if (Objects.nonNull(accountId)) {
            accountInfo = bizAccountInfoRepository.selectByAccountId(accountId);
        }
        BizAccountInfoMsg bizAccountInfoMsg =
                BizAccountInfoMsg.builder()
                        .uid(uid)
                        .masterUid(Objects.isNull(accountInfo) ? null : accountInfo.getUid())
                        .accountId(accountId)
                        .roleType(roleType)
                        .authList(authList)
                        .opType(opType)
                        .unBindAccountId(opType.equals((byte) 2) ? accountId : null)
                        .build();
        Message message =
                Message.Builder.of(String.valueOf(uid), bizAccountInfoMsg).build();
        bizAccountInfoPub.pubWithRetry(message);
    }

    /**
     * 员工备注修改
     *
     * @param request
     * @param loginUid
     */
    @Override
    public Void updateStaffRemark(StaffRemarkRequest request, Long loginUid) {
        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(request.getStaffUid());
        if (Objects.isNull(bizAccountUser)) {
            throw new ClientViewException("未找到绑定的账号信息");
        }

        //校验登录账号是否主账号
        checkMasterRole(loginUid);


        BizAccountUser updateUser = new BizAccountUser();
        updateUser.setId(bizAccountUser.getId());
        updateUser.setRemarkName(request.getRemark());
        bizAccountUserRepository.updateById(updateUser);

        // log
        BizAccountUserOpLog userOpLog =
                BizAccountUserOpLog.builder()
                        .uid(loginUid)
                        .opUid(request.getStaffUid())
                        .opType(UserOpTypeEnum.REMARK_MODIFY.getType())
                        .detail(JSON.toJSONString(updateUser))
                        .build();
        bizAccountUserLogRepository.saveAsync(userOpLog);
        return null;
    }

    /**
     * 员工账号解绑
     *
     * @param request
     */
    @Override
    public Void unBindStaff(StaffUnbindRequest request, Long loginUid) {
        //校验登录账号是否主账号
        checkMasterRole(loginUid);

        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(request.getStaffUid());
        List<String> originCodes = FunctionUtil.splitByComma(bizAccountUser.getAuth(), String::valueOf);
        // 权限点对应绑定的组件
        Map<String, List<ComponentInfo>> componentMap = videoComponentService.getUserValidComponentAvidList(request.getStaffUid(), originCodes);
        // 检验是否可解绑权限点
        checkCanUnbind(componentMap, originCodes, bizAccountUser);

        bizAccountDomainService.doUnBindStaff(request, loginUid);

        // 账号变更消息
        pubBizAccountInfoMsg(request.getStaffUid(), bizAccountUser.getAccountId(), null, new ArrayList<>(), (byte) 2);

        // 删除在线咨询对应组件
        if (originCodes.contains(AuthTypeEnum.ONLINE_CONSULT.getCode())) {
            // 删除
            deleteOnlineConsultComponents(bizAccountUser, componentMap.get(AuthTypeEnum.ONLINE_CONSULT.getCode()), "员工号已解绑，后续此账号已无法使用经营组件功能，该账号部分稿件下的组件自动删除失败，请联系其自行删除，稿件id:s%");

        }
        return null;
    }

    @Override
    @Async("taskExecutor")
    @RetryConn(times = 3, unit = TimeUnit.SECONDS, interval = 5)
    public void sendBizAccountInfoMsgForMaster(Long uid) {
        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(uid);
        List<String> authList = FunctionUtil.splitByComma(bizAccountUser.getAuth(), String::valueOf);
        if (Objects.equals(RoleTypeEnum.MASTER.getType(), bizAccountUser.getRoleType())) {
            authList = AuthTypeEnum.allCodes();
        }
        // 账号变更消息
        pubBizAccountInfoMsg(bizAccountUser.getUid(), bizAccountUser.getAccountId(), bizAccountUser.getRoleType(), authList, (byte) 0);
    }

    @Override
    public UserLoginAccountVO fakeLogin(Long loginUid, Long managerUid) {
        if (Objects.isNull(loginUid) || Objects.isNull(managerUid)) {
            throw new ClientViewException("伪登录账号信息不足.");
        }
        // step1: 校验uid是不是白名单
        checkWhiteList(loginUid);
        // step2: 校验managerUid是不是有效经营号uid
        return getUserLoginAccountVO(managerUid);
    }

    @Override
    public void checkFakeLoginUid(Long loginUid) {
        if (Objects.isNull(loginUid)) {
            throw new ClientViewException("loginUid为空");
        }
        // step1: 校验uid是不是白名单
        checkWhiteList(loginUid);
    }

    @Override
    public Integer queryShopId(Long uid) {
        BizAccountUserBo bizAccountUser = bizAccountMap.get(uid);
        if (null == bizAccountUser) {
            return null;
        }

        UserLoginAccountVO userLoginAccountVO = queryLoginUserInfo(uid);
        if (null == userLoginAccountVO.getAccountId()) {
            return null;
        }
        StaffMainQuery staffMainQuery = new StaffMainQuery();
        staffMainQuery.setBizId(CustomerBizIdEnum.BUSINESS_ACCOUNT.getType());
        staffMainQuery.setMainMid(userLoginAccountVO.getUid());
        staffMainQuery.setParentShopId(userLoginAccountVO.getAccountId());
        BaseResponse<StaffMainDTO> response = staffConsumer.getStaffMain(staffMainQuery);
        if (Objects.isNull(response) || Objects.isNull(response.data) || Objects.isNull(response.data.getShopId())) {
            throw new com.bilibili.mall.common.exception.ClientViewException("获取客服主体信息失败");
        }
        return response.data.getShopId();
    }

    @Override
    public Boolean appMsgCount(Long loginUid) {
        StaffMessageQuery staffMessageQuery = new StaffMessageQuery();
        staffMessageQuery.setBizId(CustomerBizIdEnum.BUSINESS_ACCOUNT.getType());
        staffMessageQuery.setStaffMid(loginUid);
        BaseResponse<StaffMessageListDTO> response = staffConsumer.getStaffMessage(staffMessageQuery);
        if (Objects.isNull(response) || Objects.isNull(response.data)) {
            throw new com.bilibili.mall.common.exception.ClientViewException("获取经营号客服消息信息失败");
        }
        return "1".equals(response.data.getHasNewMessage());
    }

    private UserLoginAccountVO getUserLoginAccountVO(Long managerUid) {
        UserLoginAccountVO accountVO = queryLoginUserInfo(managerUid);
        if (Objects.isNull(accountVO.getAccountId())) {
            throw new ClientViewException("该uid无经营号权限");
        }
        return accountVO;
    }

    private void checkWhiteList(Long loginUid) {
        try {
            BaseResponse<Boolean> response = authorityIntranetConsumer.isFakeLoginWhiteListAccount(loginUid);
            if (Objects.isNull(response) || !Boolean.TRUE.equals(response.data)) {
                log.error("运营账号非白名单账号。loginUid:{}", loginUid);
                throw new ClientViewException("运营账号非白名单账号");
            }
        } catch (Throwable throwable) {
            log.error("运营账号非白名单账号。loginUid:{}", loginUid, throwable);
            throw new ClientViewException("运营账号非白名单账号");
        }

    }

    private void checkMasterRole(Long loginUid) {
        BizAccountUserInfo bizAccountUserInfo =
                bizAccountDomainService.queryAccountUserInfo(loginUid);
        if (Objects.isNull(bizAccountUserInfo)) {
            throw new ClientViewException("未找到绑定的账号信息");
        }
        if (!Objects.equals(bizAccountUserInfo.getRoleType(), RoleTypeEnum.MASTER.getType())) {
            throw new ClientViewException("非企业主账号，不能修改其他员工信息");
        }
    }

    @PostConstruct
    public void cacheLoad() {
        List<BizAccountUser> bizAccountInfos = bizAccountUserRepository.selectAll();
        Map<Long, BizAccountUserBo> accountUserBoMap = new HashMap<>();

        for (BizAccountUser bizAccountInfo : bizAccountInfos) {
            BizAccountUserBo bizAccountUserBo = buildBizAccountUserBo(bizAccountInfo);
            if (bizAccountUserBo == null) continue;
            accountUserBoMap.put(bizAccountInfo.getUid(), bizAccountUserBo);
        }

        bizAccountMap = accountUserBoMap;
    }

    private BizAccountUserBo buildBizAccountUserBo(BizAccountUser bizAccountInfo) {
        BizAccountUserBo bizAccountUserBo = BizAccountUserBo.builder()
                .bizAccountUser(bizAccountInfo)
                .build();
        if (null != bizAccountInfo.getAccountId()) {
            StaffMainQuery staffMainQuery = new StaffMainQuery();
            staffMainQuery.setBizId(CustomerBizIdEnum.BUSINESS_ACCOUNT.getType());
            staffMainQuery.setMainMid(bizAccountInfo.getUid());
            staffMainQuery.setParentShopId(bizAccountInfo.getAccountId());
            BaseResponse<StaffMainDTO> response = staffConsumer.getStaffMain(staffMainQuery);
            if (Objects.isNull(response) || Objects.isNull(response.data) || Objects.isNull(response.data.getShopId())) {
                log.info("account cache 获取shopId 不存在|失败 response {} , staffMainQuery {} ", response, staffMainQuery);
            } else {
                bizAccountUserBo.setShopId(response.data.getShopId());
            }
        }

        //onlineConsult query
        // 子账号 要有ONLINE_CONSULT的权限 且主张号建了在线咨询组件
        // 主账号建了在线咨询组件
        if (bizAccountInfo.getRoleType().equals(RoleTypeEnum.MASTER.getType()) || bizAccountInfo.getAuth().contains(ToolTypeEnum.ONLINE_CONSULT.toString())) {
            //有在线咨询组件的 才返回
            CreateOnlineConsultComponentReply onlineConsultComponentReply = onlineConsultService.getCreateOnlineConsultComponentReply(bizAccountInfo.getAdId().longValue());
            if (null != onlineConsultComponentReply) {
                bizAccountUserBo.setOnlineConsultComponentId(onlineConsultComponentReply.getToolId());
            }
        }
        return bizAccountUserBo;
    }

    @Override
    public Map<Long, BizAccountUserBo> getBizAccountMap() {
        return bizAccountMap;
    }

    @Scheduled(fixedDelay = 5 * 60 * 1000)
    public void bizAccountRefreshTask() {
        cacheLoad();
    }

    private List<String> queryAdAccountAuth(Integer AdAccountId, Set<Integer> adAccountLabels, List<Integer> appId) {

        List<String> authListV2 = new ArrayList<>();
        authListV2.add(BizAccountAuthEnum.RESERVE.getCode());

        if (appId.contains(ENTERPRISE_WECHAT_APP_ID)) {
            authListV2.add(BizAccountAuthEnum.CONTACT_QW.getCode());
        }
        if (appId.contains(WECHAT_APP_ID)) {
            authListV2.add(BizAccountAuthEnum.MINI_APP.getCode());
        }
        if (adAccountLabels.contains(BizAccountAuthEnum.ONLINE_CONSULT.getLabelId())) {
            authListV2.add(BizAccountAuthEnum.ONLINE_CONSULT.getCode());
        }
        if (adAccountLabels.contains(BizAccountAuthEnum.THIRD_PARTY_LANDING_PAGE_URL.getLabelId())) {
            authListV2.add(BizAccountAuthEnum.THIRD_PARTY_LANDING_PAGE_URL.getCode());
        }
        //应用唤起权限
        if (CollectionUtils.isNotEmpty(appId)) {
            authListV2.add(BizAccountAuthEnum.APP_PACKAGE.getCode());
        }

        return authListV2;
    }

    @Override
    public Boolean accountOpenAuth(Long uid, String authCode) {
        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(uid);
        if (Objects.equals(authCode, BizAccountAuthEnum.THIRD_PARTY_LANDING_PAGE_URL.getCode()) || Objects.equals(authCode, BizAccountAuthEnum.ONLINE_CONSULT.getCode())) {

            if (Objects.equals(bizAccountUser.getRoleType(), RoleTypeEnum.STAFF.getType())) {
                throw new IllegalArgumentException("员工账号无法开通权限");
            }
            BizAccountAuthEnum authEnum = BizAccountAuthEnum.getByCode(authCode);
            if (Objects.isNull(authEnum)) {
                throw new IllegalArgumentException("权限码不存在");
            }
            Integer labelId = authEnum.getLabelId();

            adAccountLabelService.addAdAccountLabel(bizAccountUser.getAdId(), labelId);
        } else if (Objects.equals(authCode, BizAccountAuthEnum.CONTACT_QW.getCode())) {
            adAccountInfoGrpcService.addAdAccountAwakenApp(bizAccountUser.getAdId(), ENTERPRISE_WECHAT_APP_ID);
        } else if (Objects.equals(authCode, BizAccountAuthEnum.MINI_APP.getCode())) {
            adAccountInfoGrpcService.addAdAccountAwakenApp(bizAccountUser.getAdId(), WECHAT_APP_ID);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean hasBusinessPrivilege(Long loginUid) {
        return redisTemplate.opsForSet().isMember("business-account-video-manager-business-data-member", loginUid);
    }

    public List<Long> queryPrivilegeUidList(Long loginUid) {
        List<Long> mids = new ArrayList<>();
        mids.add(loginUid);
        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(loginUid);
        if (RoleTypeEnum.MASTER.getType() == bizAccountUser.getRoleType()) {
            List<BizAccountUser> bizAccountUsers = bizAccountUserRepository.selectByAccountIdAndRole(bizAccountUser.getAccountId(), RoleTypeEnum.STAFF.getType());
            mids.addAll(bizAccountUsers.stream().map(BizAccountUser::getUid).collect(Collectors.toList()));
        }
        return mids;
    }

    public List<BizAccountUser> queryByAdId(Long adId) {
        return bizAccountUserRepository.queryByAdId(adId);
    }

    @Override
    public AccountStatusDto queryAccountStatus(Long uid) {
        AccountStatusDto accountStatusDto = new AccountStatusDto();
        accountStatusDto.setEnterpriseQualificationStatus(QualificationAuditStatusEnum.NOT_AUDIT.getCode());
        accountStatusDto.setPersonalQualificationStatus(QualificationAuditStatusEnum.NOT_AUDIT.getCode());

        List<BizAccountQualificationInfoPo> bizAccountQualificationInfoPos = bizAccountQualificationInfoRepository.queryQualificationInfoByUid(uid);
        for (BizAccountQualificationInfoPo bizAccountQualificationInfoPo : bizAccountQualificationInfoPos) {
            if (Objects.equals(bizAccountQualificationInfoPo.getAccountType(), AccountTypeEnum.PERSONAL.getCode())) {
                accountStatusDto.setPersonalQualificationStatus(bizAccountQualificationInfoPo.getQualificationStatus());
                WorkOrderInfoDetailDto workOrderInfoDetailDto = null;
                if (Objects.equals(bizAccountQualificationInfoPo.getQualificationStatus(), QualificationAuditStatusEnum.AUDIT_FAIL.getCode())) {
                    workOrderInfoDetailDto = adCrmGrpcService.queryWorkOrderInfoDetailByWorkOrderId(bizAccountQualificationInfoPo.getWorkOrderId());
                } else if (Objects.equals(bizAccountQualificationInfoPo.getLastQualificationStatus(), QualificationAuditStatusEnum.AUDIT_FAIL.getCode())) {
                    workOrderInfoDetailDto = adCrmGrpcService.queryWorkOrderInfoDetailByWorkOrderId(bizAccountQualificationInfoPo.getLastWorkOrderId());
                }
                accountStatusDto.setPersonalAuditMessage(Optional.ofNullable(workOrderInfoDetailDto).map(WorkOrderInfoDetailDto::getMessage).orElse(null));
            } else if (Objects.equals(bizAccountQualificationInfoPo.getAccountType(), AccountTypeEnum.ENTERPRISE.getCode())) {
                accountStatusDto.setEnterpriseQualificationStatus(bizAccountQualificationInfoPo.getQualificationStatus());
                WorkOrderInfoDetailDto workOrderInfoDetailDto = null;
                if (Objects.equals(bizAccountQualificationInfoPo.getQualificationStatus(), QualificationAuditStatusEnum.AUDIT_FAIL.getCode())) {
                    workOrderInfoDetailDto = adCrmGrpcService.queryWorkOrderInfoDetailByWorkOrderId(bizAccountQualificationInfoPo.getWorkOrderId());
                } else if (Objects.equals(bizAccountQualificationInfoPo.getLastQualificationStatus(), QualificationAuditStatusEnum.AUDIT_FAIL.getCode())) {
                    workOrderInfoDetailDto = adCrmGrpcService.queryWorkOrderInfoDetailByWorkOrderId(bizAccountQualificationInfoPo.getLastWorkOrderId());
                }
                accountStatusDto.setEnterpriseAuditMessage(Optional.ofNullable(workOrderInfoDetailDto).map(WorkOrderInfoDetailDto::getMessage).orElse(null));
            }

        }
        return accountStatusDto;

    }

    @Override
    public CheckRegisterAccountDto checkRegisterPersonalAccount(Long uid) {
        CheckRegisterAccountDto checkRegisterAccountDto = new CheckRegisterAccountDto();

        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(uid);

        //子账号不允许开通
        if (Objects.nonNull(bizAccountUser) && Objects.equals(bizAccountUser.getRoleType(), RoleTypeEnum.STAFF.getType())) {
            RegisterPersonalAccountErrorEnum errorEnum = RegisterPersonalAccountErrorEnum.STAFF_ACCOUNT_CAN_NOT_REGISTER;
            checkRegisterAccountDto.setCode(errorEnum.getCode());
            checkRegisterAccountDto.setMsg(errorEnum.getMsg());
            return checkRegisterAccountDto;
        }

        //有蓝v不可开通个人号
        UserOfficialInfoDto userOfficialInfoDto = accountMemberGrpcService.queryOfficialInfo(uid);
        if (Objects.nonNull(userOfficialInfoDto)) {
            RegisterPersonalAccountErrorEnum errorEnum = RegisterPersonalAccountErrorEnum.HAS_BLUE_V;
            checkRegisterAccountDto.setCode(errorEnum.getCode());
            checkRegisterAccountDto.setMsg(errorEnum.getMsg());
            return checkRegisterAccountDto;
        }

        //有必火企业版不可开通个人号
        CrmBihuoQualificationDto qualificationInfo = adCrmGrpcService.queryBihuoEnterpriseQualification(uid);
        if (Objects.nonNull(qualificationInfo) && qualificationInfo.getIsExists() == 1) {
            RegisterPersonalAccountErrorEnum errorEnum = RegisterPersonalAccountErrorEnum.HAS_BIHUO_ENTERPRISE;
            checkRegisterAccountDto.setCode(errorEnum.getCode());
            checkRegisterAccountDto.setMsg(errorEnum.getMsg());
            return checkRegisterAccountDto;
        }

        //查询是否有企业号流程中
        BizAccountQualificationInfoPo bizAccountQualificationInfoPo = bizAccountQualificationInfoRepository.queryQualificationInfoByUidAndAccountType(uid, AccountTypeEnum.ENTERPRISE.getCode());
        if (Objects.nonNull(bizAccountQualificationInfoPo)) {
            Integer qualificationStatus = bizAccountQualificationInfoPo.getQualificationStatus();
            if (Objects.equals(qualificationStatus, QualificationAuditStatusEnum.AUDITING.getCode()) || Objects.equals(qualificationStatus, QualificationAuditStatusEnum.AUDIT_SUCCESS.getCode())) {
                RegisterPersonalAccountErrorEnum errorEnum = RegisterPersonalAccountErrorEnum.ENTERPRISE_ACCOUNT_PROCESSING;
                checkRegisterAccountDto.setCode(errorEnum.getCode());
                checkRegisterAccountDto.setMsg(errorEnum.getMsg());
                return checkRegisterAccountDto;
            }
        }

        RealNameStrippedInfo realNameStrippedInfo = accountMemberGrpcService.queryRealNameStrippedInfo(uid);
        if (!Objects.equals(realNameStrippedInfo.getStatus(), RealNameStatusEnum.HAS_REAL_NAME.getCode())) {
            RegisterPersonalAccountErrorEnum errorEnum = RegisterPersonalAccountErrorEnum.REAL_NAME_NOT_REGISTER;
            checkRegisterAccountDto.setCode(errorEnum.getCode());
            checkRegisterAccountDto.setMsg(errorEnum.getMsg());
            return checkRegisterAccountDto;
        }
        if (!Objects.equals(realNameStrippedInfo.getCardType(), RealNameCardTypeEnum.ID_CARD.getCode())) {
            RegisterPersonalAccountErrorEnum errorEnum = RegisterPersonalAccountErrorEnum.REAL_NAME_CARD_TYPE_ERROR;
            checkRegisterAccountDto.setCode(errorEnum.getCode());
            checkRegisterAccountDto.setMsg(errorEnum.getMsg());
            return checkRegisterAccountDto;
        }

        RegisterPersonalAccountErrorEnum successEnum = RegisterPersonalAccountErrorEnum.SUCCESS;
        checkRegisterAccountDto.setCode(successEnum.getCode());
        checkRegisterAccountDto.setMsg(successEnum.getMsg());
        return checkRegisterAccountDto;
    }

    @Override
    public CheckRegisterAccountDto checkRegisterEnterpriseAccount(Long uid) {
        CheckRegisterAccountDto checkRegisterAccountDto = new CheckRegisterAccountDto();

        Boolean isBlueVMember = redisTemplate.opsForSet().isMember("biz-account-auth-blue-v-info", uid);
        if (BooleanUtils.isTrue(isBlueVMember)) {
            RegisterEnterpriseAccountErrorEnum errorEnum = RegisterEnterpriseAccountErrorEnum.HAS_AUTH_BLUE_V;
            checkRegisterAccountDto.setCode(errorEnum.getCode());
            checkRegisterAccountDto.setMsg(errorEnum.getMsg());
            return checkRegisterAccountDto;
        }

        Boolean isBihuoMember = redisTemplate.opsForSet().isMember("biz-account-auth-bihuo-info", uid);
        if (BooleanUtils.isTrue(isBihuoMember)) {
            RegisterEnterpriseAccountErrorEnum errorEnum = RegisterEnterpriseAccountErrorEnum.HAS_AUTH_BIHUO;
            checkRegisterAccountDto.setCode(errorEnum.getCode());
            checkRegisterAccountDto.setMsg(errorEnum.getMsg());
            return checkRegisterAccountDto;
        }

        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(uid);

        //子账号不允许开通
        if (Objects.nonNull(bizAccountUser) && Objects.equals(bizAccountUser.getRoleType(), RoleTypeEnum.STAFF.getType())) {
            RegisterEnterpriseAccountErrorEnum errorEnum = RegisterEnterpriseAccountErrorEnum.STAFF_ACCOUNT_CAN_NOT_REGISTER;
            checkRegisterAccountDto.setCode(errorEnum.getCode());
            checkRegisterAccountDto.setMsg(errorEnum.getMsg());
            return checkRegisterAccountDto;
        }

        //查询是否有个人号流程中
        BizAccountQualificationInfoPo bizAccountQualificationInfoPo = bizAccountQualificationInfoRepository.queryQualificationInfoByUidAndAccountType(uid, AccountTypeEnum.PERSONAL.getCode());
        if (Objects.nonNull(bizAccountQualificationInfoPo)) {
            Integer qualificationStatus = bizAccountQualificationInfoPo.getQualificationStatus();
            if (Objects.equals(qualificationStatus, QualificationAuditStatusEnum.AUDITING.getCode()) || Objects.equals(qualificationStatus, QualificationAuditStatusEnum.AUDIT_SUCCESS.getCode())) {
                RegisterEnterpriseAccountErrorEnum errorEnum = RegisterEnterpriseAccountErrorEnum.PERSONAL_ACCOUNT_PROCESSING;
                checkRegisterAccountDto.setCode(errorEnum.getCode());
                checkRegisterAccountDto.setMsg(errorEnum.getMsg());
                return checkRegisterAccountDto;
            }
        }

        UserOfficialInfoDto userOfficialInfoDto = accountMemberGrpcService.queryOfficialInfo(uid);
        if (Objects.nonNull(userOfficialInfoDto)) {
            RegisterEnterpriseAccountErrorEnum errorEnum = RegisterEnterpriseAccountErrorEnum.HAS_BLUE_V;
            checkRegisterAccountDto.setCode(errorEnum.getCode());
            checkRegisterAccountDto.setMsg(errorEnum.getMsg());
            return checkRegisterAccountDto;
        }


        //有必火企业版账号
        CrmBihuoQualificationDto qualificationInfo = adCrmGrpcService.queryBihuoEnterpriseQualification(uid);
        if (Objects.nonNull(qualificationInfo) && qualificationInfo.getIsExists() == 1) {
            RegisterEnterpriseAccountErrorEnum errorEnum = RegisterEnterpriseAccountErrorEnum.HAS_BIHUO_ENTERPRISE;
            checkRegisterAccountDto.setCode(errorEnum.getCode());
            checkRegisterAccountDto.setMsg(errorEnum.getMsg());
            return checkRegisterAccountDto;
        }

        RegisterEnterpriseAccountErrorEnum successEnum = RegisterEnterpriseAccountErrorEnum.SUCCESS;
        checkRegisterAccountDto.setCode(successEnum.getCode());
        checkRegisterAccountDto.setMsg(successEnum.getMsg());
        return checkRegisterAccountDto;
    }


    @Override
    public Integer registerEnterpriseAccount(RegisterEnterpriseAccountDto dto) {
        log.info("registerEnterpriseAccount start, uid:{}, businessLicenseCode:{}", dto.getUid(), dto.getBusinessLicenseCode());

        RLock lock = redissonclient.getLock(REDISSON_LOCK_PREFIX + dto.getUid());
        try {
            boolean tryLockSuccess = lock.tryLock();
            if (!tryLockSuccess) {
                log.info("registerEnterpriseAccount tryLock failed, uid:{}", dto.getUid());
                return QualificationAuditStatusEnum.NOT_AUDIT.getCode();
            }

            // 验证手机验证码
            validatePhoneVerificationCode(dto.getLinkmanPhone(), dto.getLinkmanVerificationCode().toString());

            // 验证账号注册资格
            Long registerUid = dto.getUid();
            validateAccountRegistrationEligibility(registerUid);

            // 查询是否已存在相同营业执照的客户
            String businessLicenseCode = dto.getBusinessLicenseCode();
            List<CustomerInfoDetailDto> customerInfoDetailDtos = adCrmGrpcService.queryCustomerInfoDetail(
                    CustomerQueryReqDto.builder().businessLicenceCode(businessLicenseCode).customerCategories(Collections.singletonList(CustomerCategory.ORG_CUSTOMER_VALUE)).build());
            log.info("registerEnterpriseAccount query customer result, uid:{}, customerSize:{}", registerUid, customerInfoDetailDtos.size());

            // 根据查询结果处理不同情况
            if (customerInfoDetailDtos.size() == 1 && customerInfoDetailDtos.get(0).getCustomerCategory() != 2) {
                // 存在一个客户且不是个人客户，更新客户信息
                log.info("registerEnterpriseAccount enter update branch, uid:{}, customerId:{}", registerUid, customerInfoDetailDtos.get(0).getId());
                return handleEnterpriseAccountUpdate(dto, customerInfoDetailDtos.get(0), registerUid);
            } else if (customerInfoDetailDtos.size() > 1) {
                // 存在多个客户，抛出异常
                log.warn("registerEnterpriseAccount multiple customers found, uid:{}, customerSize:{}", registerUid, customerInfoDetailDtos.size());
                throw new BusinessException("企业客户映射过多，请联系管理员");
            } else {
                // 不存在客户，创建新客户
                log.info("registerEnterpriseAccount enter create branch, uid:{}", registerUid);
                return handleEnterpriseAccountCreate(dto, registerUid);
            }
        } catch (BusinessException e) {
            log.error("registerEnterpriseAccount business exception, uid:{}, error:{}", dto.getUid(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("registerEnterpriseAccount exception, uid:{}", dto.getUid(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }


    @Override
    public Integer registerPersonalAccount(RegisterPersonalAccountDto dto) {
        log.info("registerPersonalAccount start, uid:{}, personalIdCardNumber:{}", dto.getUid(), dto.getPersonalIdCardNumber());
        RLock lock = redissonclient.getLock(REDISSON_LOCK_PREFIX + dto.getUid());
        try {
            boolean tryLockSuccess = lock.tryLock();
            if (!tryLockSuccess) {
                log.info("registerPersonalAccount tryLock failed, uid:{}", dto.getUid());
                return QualificationAuditStatusEnum.NOT_AUDIT.getCode();
            }

            // 验证账号注册资格
            Long registerUid = dto.getUid();
            validateAccountRegistrationEligibility(registerUid);

            // 验证实名信息
            String personalIdCardNumber = dto.getPersonalIdCardNumber();
            String userName = dto.getUserName();
            validateRealNameInfo(personalIdCardNumber, userName, registerUid);
            log.info("registerPersonalAccount real name validation passed, uid:{}", registerUid);

            // 查询是否已存在相同身份证号的客户
            List<CustomerInfoDetailDto> customerInfoDetailDtos = adCrmGrpcService.queryCustomerInfoDetail(
                    CustomerQueryReqDto.builder().personalIdCardNumber(personalIdCardNumber).customerCategories(Collections.singletonList(CustomerCategory.PERSONAL_CUSTOMER_VALUE)).build());
            log.info("registerPersonalAccount query customer result, uid:{}, customerSize:{}", registerUid, customerInfoDetailDtos.size());

            // 根据查询结果处理不同情况
            if (customerInfoDetailDtos.size() == 1) {
                // 存在一个客户，更新客户信息
                log.info("registerPersonalAccount enter update branch, uid:{}, customerId:{}", registerUid, customerInfoDetailDtos.get(0).getId());
                return handlePersonalAccountUpdate(dto, customerInfoDetailDtos.get(0));
            } else if (customerInfoDetailDtos.size() > 1) {
                // 存在多个客户，选择ID最大的客户进行处理
                log.info("registerPersonalAccount enter create-and-audit branch (multiple customers), uid:{}, customerSize:{}", registerUid, customerInfoDetailDtos.size());
                CustomerInfoDetailDto customerInfoDetailDto = customerInfoDetailDtos.stream()
                        .sorted(Comparator.comparing(CustomerInfoDetailDto::getId).reversed())
                        .findFirst().get();
                log.info("registerPersonalAccount selected customer, uid:{}, customerId:{}", registerUid, customerInfoDetailDto.getId());
                return handlePersonalAccountCreateAndAudit(dto, customerInfoDetailDto);
            } else {
                // 不存在客户，创建新客户
                log.info("registerPersonalAccount enter create branch, uid:{}", registerUid);
                return handlePersonalAccountCreate(dto);
            }
        } catch (BusinessException e) {
            log.error("registerPersonalAccount business exception, uid:{}, error:{}", dto.getUid(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("registerPersonalAccount exception, uid:{}", dto.getUid(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Integer updateEnterpriseAccount(RegisterEnterpriseAccountDto dto) {
        BizAccountQualificationInfoPo qualificationInfoPo = bizAccountQualificationInfoRepository.queryQualificationInfoByUidAndAccountType(dto.getUid(), AccountTypeEnum.ENTERPRISE.getCode());
        Integer adCustomerId = qualificationInfoPo.getAdCustomerId();
        Integer lastQualificationStatus = qualificationInfoPo.getLastQualificationStatus();
        if (Objects.equals(lastQualificationStatus, QualificationAuditStatusEnum.AUDITING.getCode())) {
            throw new BusinessException("账号审核中 uid:{}", dto.getUid());
        }
        dto.setIsAgent(businessQualificationConfig.getIsAgent());
        dto.setIsInner(businessQualificationConfig.getIsInner());
        dto.setCustomerCategory(businessQualificationConfig.getCustomerCategory());
        dto.setDepartmentId(businessQualificationConfig.getEnterpriseDepartmentId());
        dto.setAreaId(businessQualificationConfig.getEnterpriseAreaId());
        dto.setCustomerCategory(BusinessQualificationConfig.ENTERPRISE_CUSTOMER_CATEGORY_DEFAULT_VALUE);
        dto.setGroupId(businessQualificationConfig.getEnterpriseGroupId());
        dto.setCustomerId(adCustomerId);

        Integer workOrderId = 0;

        if (adCustomerId > 0) {
            workOrderId = adCrmGrpcService.updateEnterpriseInfo(dto);
        } else {
            workOrderId = adCrmGrpcService.registerEnterpriseAccount(dto);
            qualificationInfoPo.setWorkOrderId(workOrderId);
            qualificationInfoPo.setQualificationStatus(QualificationAuditStatusEnum.AUDITING.getCode());
        }
        redisTemplate.opsForSet().add("biz-account-register-qualification-work-order", workOrderId);

        qualificationInfoPo.setLastWorkOrderId(workOrderId);
        qualificationInfoPo.setLastQualificationStatus(QualificationAuditStatusEnum.AUDITING.getCode());
        bizAccountQualificationInfoRepository.updateQualificationInfo(qualificationInfoPo);


        return QualificationAuditStatusEnum.AUDITING.getCode();
    }

    @Override
    public Integer updatePersonalAccount(RegisterPersonalAccountDto dto) {
        BizAccountQualificationInfoPo qualificationInfoPo = bizAccountQualificationInfoRepository.queryQualificationInfoByUidAndAccountType(dto.getUid(), AccountTypeEnum.PERSONAL.getCode());
        Integer adCustomerId = qualificationInfoPo.getAdCustomerId();
        Integer lastQualificationStatus = qualificationInfoPo.getLastQualificationStatus();
        if (Objects.equals(lastQualificationStatus, QualificationAuditStatusEnum.AUDITING.getCode())) {
            throw new BusinessException("账号审核中 uid:{}", dto.getUid());
        }
        dto.setIsAgent(businessQualificationConfig.getIsAgent());
        dto.setIsInner(businessQualificationConfig.getIsInner());
        dto.setCustomerCategory(businessQualificationConfig.getCustomerCategory());
        dto.setDepartmentIds(businessQualificationConfig.getPersonalUpdateDepartmentIds());
        dto.setAreaId(businessQualificationConfig.getPersonalUpdateAreaId());
        dto.setCustomerCategory(BusinessQualificationConfig.PERSONAL_CUSTOMER_CATEGORY_DEFAULT_VALUE);
        dto.setCustomerId(adCustomerId);
        Integer workOrderId = 0;

        if (adCustomerId > 0) {
            workOrderId = adCrmGrpcService.updatePersonalInfo(dto);
        } else {
            workOrderId = adCrmGrpcService.registerPersonalAccount(dto);
            qualificationInfoPo.setWorkOrderId(workOrderId);
            qualificationInfoPo.setQualificationStatus(QualificationAuditStatusEnum.AUDITING.getCode());
        }
        redisTemplate.opsForSet().add("biz-account-register-qualification-work-order", workOrderId);

        qualificationInfoPo.setLastWorkOrderId(workOrderId);
        qualificationInfoPo.setLastQualificationStatus(QualificationAuditStatusEnum.AUDITING.getCode());
        bizAccountQualificationInfoRepository.updateQualificationInfo(qualificationInfoPo);

        return QualificationAuditStatusEnum.AUDITING.getCode();
    }

    @Override
    public UserOfficialInfoDto queryUserOfficialInfo(Long uid) {
        return accountMemberGrpcService.queryOfficialInfo(uid);
    }

    @Override
    public Integer getPhoneVerificationCode(String ip, String phoneNumber) {
        return adAccountInfoGrpcService.getPhoneVerificationCode(ip, phoneNumber);
    }


    @Override
    public CrmBihuoQualificationDto queryBihuoEnterpriseInfo(Long uid) {
        return adCrmGrpcService.queryBihuoEnterpriseQualification(uid);
    }

    @Override
    public AccountQualificationDto queryCustomerInfoDetail(Long uid) {
        List<BizAccountQualificationInfoPo> bizAccountQualificationInfoPos = bizAccountQualificationInfoRepository.queryQualificationInfoByUid(uid);

        AccountQualificationDto accountQualificationDto = new AccountQualificationDto();

        if (CollectionUtils.isEmpty(bizAccountQualificationInfoPos)) {
            throw new BusinessException("没有账号信息");
        }

        for (BizAccountQualificationInfoPo qualificationInfoPo : bizAccountQualificationInfoPos) {
            Integer accountType = qualificationInfoPo.getAccountType();

            Integer workOrderId = qualificationInfoPo.getWorkOrderId();

            if (Objects.equals(qualificationInfoPo.getAccountType(), AccountTypeEnum.PERSONAL.getCode())) {
                CustomerInfoDetailDto customerInfoDetailDto = null;
                if (qualificationInfoPo.getQualificationStatus().equals(QualificationAuditStatusEnum.AUDIT_SUCCESS.getCode())) {
                    customerInfoDetailDto = adCrmGrpcService.queryCustomerInfoDetail(CustomerQueryReqDto.builder().customerId(qualificationInfoPo.getAdCustomerId()).build()).get(0);
                    accountQualificationDto.setAccountType(AccountTypeEnum.PERSONAL.getCode());
                } else {
                    if (Objects.isNull(workOrderId)) {
                        throw new ClientViewException("工单id不存在");
                    }
                    customerInfoDetailDto = adCrmGrpcService.queryCustomerInfoDetailByWorkOrderId(workOrderId);
                }

                PersonalAccountQualificationDto personalAccountQualificationDto = CrmConverter.convertor.convertToPersonalAccountQualificationDto(customerInfoDetailDto);
                accountQualificationDto.setPersonalQualification(personalAccountQualificationDto);
                String businessLicenceCode = personalAccountQualificationDto.getPersonalIdCardNumber();
                List<CustomerInfoDetailDto> customerInfoDetailDtos = adCrmGrpcService.queryCustomerInfoDetail(CustomerQueryReqDto.builder().personalIdCardNumber(businessLicenceCode).build());
                if (customerInfoDetailDtos.size() > 1) {
                    accountQualificationDto.setCanNotUpdate(1);
                }
            } else if (Objects.equals(qualificationInfoPo.getAccountType(), AccountTypeEnum.ENTERPRISE.getCode())) {
                CustomerInfoDetailDto customerInfoDetailDto = null;
                if (qualificationInfoPo.getQualificationStatus().equals(QualificationAuditStatusEnum.AUDIT_SUCCESS.getCode())) {
                    customerInfoDetailDto = adCrmGrpcService.queryCustomerInfoDetail(CustomerQueryReqDto.builder().customerId(qualificationInfoPo.getAdCustomerId()).build()).get(0);
                    accountQualificationDto.setAccountType(AccountTypeEnum.ENTERPRISE.getCode());
                } else {
                    if (Objects.isNull(workOrderId)) {
                        throw new ClientViewException("工单id不存在");
                    }
                    customerInfoDetailDto = adCrmGrpcService.queryCustomerInfoDetailByWorkOrderId(workOrderId);
                }

                EnterpriseAccountQualificationDto enterpriseAccountQualificationDto = CrmConverter.convertor.convertToEnterpriseAccountQualificationDto(customerInfoDetailDto);
                if (customerInfoDetailDto.getPromotionType().equals(PromotionType.ONLINE_STORE_VALUE)) {
                    enterpriseAccountQualificationDto.setStoreUrl(customerInfoDetailDto.getDomains().get(0));
                    enterpriseAccountQualificationDto.setStoreQualificationPics(customerInfoDetailDto.getIcpPics().stream().map(CustomerAttachInfoDto::getUrl).collect(Collectors.toList()));
                }
                accountQualificationDto.setEnterpriseQualification(enterpriseAccountQualificationDto);

            }
            if (!Objects.equals(workOrderId, qualificationInfoPo.getLastWorkOrderId())) {
                if (Objects.equals(qualificationInfoPo.getLastQualificationStatus(), QualificationAuditStatusEnum.AUDITING.getCode())) {
                    accountQualificationDto.setHasNewVersionAuditing(1);
                } else if (Objects.equals(qualificationInfoPo.getLastQualificationStatus(), QualificationAuditStatusEnum.AUDIT_FAIL.getCode())) {
                    accountQualificationDto.setHasNewVersionReject(1);
                }
                CustomerInfoDetailDto lastCustomerInfoDetailDto = adCrmGrpcService.queryCustomerInfoDetailByWorkOrderId(qualificationInfoPo.getLastWorkOrderId());
                if (accountType.equals(AccountTypeEnum.ENTERPRISE.getCode())) {
                    EnterpriseAccountQualificationDto enterpriseAccountQualificationDto = CrmConverter.convertor.convertToEnterpriseAccountQualificationDto(lastCustomerInfoDetailDto);
                    if (lastCustomerInfoDetailDto.getPromotionType().equals(PromotionType.ONLINE_STORE_VALUE)) {
                        enterpriseAccountQualificationDto.setStoreUrl(lastCustomerInfoDetailDto.getDomains().get(0));
                        enterpriseAccountQualificationDto.setStoreQualificationPics(lastCustomerInfoDetailDto.getIcpPics().stream().map(CustomerAttachInfoDto::getUrl).collect(Collectors.toList()));
                    }
                    accountQualificationDto.setNewVersionEnterpriseQualification(enterpriseAccountQualificationDto);
                } else if (accountType.equals(AccountTypeEnum.PERSONAL.getCode())) {
                    PersonalAccountQualificationDto personalAccountQualificationDto = CrmConverter.convertor.convertToPersonalAccountQualificationDto(lastCustomerInfoDetailDto);
                    accountQualificationDto.setNewVersionPersonalQualification(personalAccountQualificationDto);
                }
            }

        }

        return accountQualificationDto;
    }

    @Override
    public AccountQualificationDto queryCustomerInfoDetail(String businessLicenceCode, String personalIdCardNumber) {
        if (StringUtils.isNotBlank(personalIdCardNumber) && StringUtils.isNotBlank(businessLicenceCode)) {
            throw new BusinessException("不可同时传入营业执照编码和身份证号");
        }
        CustomerQueryReqDto customerQueryReqDto = new CustomerQueryReqDto();
        if (StringUtils.isNotBlank(personalIdCardNumber)) {
            customerQueryReqDto.setPersonalIdCardNumber(personalIdCardNumber);
        }
        if (StringUtils.isNotBlank(businessLicenceCode)) {
            customerQueryReqDto.setBusinessLicenceCode(businessLicenceCode);
        }
        List<CustomerInfoDetailDto> customerInfoDetailDtos = adCrmGrpcService.queryCustomerInfoDetail(customerQueryReqDto);
        if (customerInfoDetailDtos.isEmpty()) {
            return null;
        }
        CustomerInfoDetailDto customerInfoDetailDto = customerInfoDetailDtos.get(0);

        AccountQualificationDto accountQualificationDto = new AccountQualificationDto();

        if (StringUtils.isNotBlank(businessLicenceCode)) {
            EnterpriseAccountQualificationDto enterpriseAccountQualificationDto = CrmConverter.convertor.convertToEnterpriseAccountQualificationDto(customerInfoDetailDto);
            accountQualificationDto.setEnterpriseQualification(enterpriseAccountQualificationDto);
        } else if (StringUtils.isNotBlank(personalIdCardNumber)) {
            PersonalAccountQualificationDto personalAccountQualificationDto = CrmConverter.convertor.convertToPersonalAccountQualificationDto(customerInfoDetailDto);
            accountQualificationDto.setPersonalQualification(personalAccountQualificationDto);
        }
        return accountQualificationDto;
    }

    @Override
    public List<IndustryInfoDto> queryFirstIndustry() {
        return adCrmGrpcService.queryUnitedFirstIndustry();
    }

    @Override
    public List<IndustryInfoDto> queryIndustryByPid(Integer pid) {
        return adCrmGrpcService.queryUnitedIndustryByPid(Collections.singletonList(pid));
    }

    @Override
    public List<CrmQualificationTypeDto> getQualificationList() {
        return adCrmGrpcService.getQualificationList();
    }

    @Override
    public List<CrmQualificationTypeDto> getSpecialInfoTypeList() {
        return adCrmGrpcService.getSpecialInfoTypeList();
    }

    @Override
    public void addOneAccount(Long mid, Long adId, Integer accountType, Integer hasAssistant) {
        bizAccountDomainService.doAddAccount(mid, adId, accountType, hasAssistant);
        sendBizAccountInfoMsgForMaster(mid);
    }

    @Override
    public void processQualificationMessage(WorkOrderAuditResult workOrderAuditResult) {
        if (Objects.equals(workOrderAuditResult.getWorkOrderType(), 2)) {

            Integer workOrderId = workOrderAuditResult.getWorkOrderId();
            Boolean isMember = redisTemplate.opsForSet().isMember("biz-account-register-qualification-work-order", workOrderId);
            if (BooleanUtils.isNotTrue(isMember)) {
                log.info("processQualificationMessage not find redis work order id :{}", workOrderId);
                return;
            }
            BizAccountQualificationInfoPo qualificationInfoPo = bizAccountQualificationInfoRepository.queryQualificationInfoByOrderId(workOrderId);
            qualificationInfoPo.setLastQualificationStatus(workOrderAuditResult.getWorkOrderStatus());
            if (Objects.equals(qualificationInfoPo.getWorkOrderId(), qualificationInfoPo.getLastWorkOrderId())) {
                qualificationInfoPo.setQualificationStatus(workOrderAuditResult.getWorkOrderStatus());
            }

            redisTemplate.opsForSet().remove("biz-account-register-qualification-work-order", workOrderId);

            if (Objects.equals(workOrderAuditResult.getWorkOrderStatus(), QualificationAuditStatusEnum.AUDIT_SUCCESS.getCode())) {
                Integer customerId = workOrderAuditResult.getMappingId();

                Long uid = qualificationInfoPo.getUid();

                BiliUserInfoDto biliUserInfoDto = queryBiliUserInfo(uid);
                CustomerInfoDetailDto customerInfoDetailDto = adCrmGrpcService.queryCustomerInfoDetailByWorkOrderId(workOrderId);

                CreateAccountAndAuditDto createAccountAndAuditDto = new CreateAccountAndAuditDto();

                createAccountAndAuditDto.setCustomerId(customerId);
                int userType = Objects.equals(qualificationInfoPo.getAccountType(), AccountTypeEnum.ENTERPRISE.getCode()) ?
                        UserType.ORG_USER_VALUE : UserType.PERSONAL_USER_VALUE;
                createAccountAndAuditDto = createBaseAccountAndAuditDto(customerId, userType, customerInfoDetailDto, biliUserInfoDto);

                // 设置特殊字段
                createAccountAndAuditDto.setUnitedFirstIndustryId(customerInfoDetailDto.getUnitedFirstIndustryId());
                createAccountAndAuditDto.setUnitedSecondIndustryId(customerInfoDetailDto.getUnitedSecondIndustryId());
                createAccountAndAuditDto.setUnitedThirdIndustryId(customerInfoDetailDto.getUnitedThirdIndustryId());
                createAccountAndAuditDto.setBrandDomain(CollectionUtils.isEmpty(customerInfoDetailDto.getDomains()) ?
                        BusinessQualificationConfig.BRAND_DOMAIN_DEFAULT_VALUE : customerInfoDetailDto.getDomains().get(0));
                createAccountAndAuditDto.setUserName(BusinessQualificationConfig.USER_NAME_PREFIX + biliUserInfoDto.getUserName());

                Integer accountId = adCrmGrpcService.createAccountAndAudit(createAccountAndAuditDto);
                qualificationInfoPo.setAdAccountId(accountId);
                qualificationInfoPo.setAdCustomerId(customerId);

                Integer hasAssistant = ValidEnum.NO.getCode();
                if (Objects.equals(qualificationInfoPo.getAccountType(), AccountTypeEnum.ENTERPRISE.getCode())) {
                    hasAssistant = ValidEnum.YES.getCode();
                } else {
                    Set<Integer> industryIdSet = Arrays.stream(assistantAuthUnitedFirstIndustryIdList.split(",")).map(Integer::valueOf).collect(Collectors.toSet());
                    if (industryIdSet.contains(createAccountAndAuditDto.getUnitedFirstIndustryId())) {
                        hasAssistant = ValidEnum.YES.getCode();
                    }
                }
                addOneAccount(uid, accountId.longValue(), qualificationInfoPo.getAccountType(), hasAssistant);
            }
            bizAccountQualificationInfoRepository.updateQualificationInfo(qualificationInfoPo);

        } else if (Objects.equals(workOrderAuditResult.getWorkOrderType(), 3)) {
            Integer workOrderId = workOrderAuditResult.getWorkOrderId();
            Boolean isMember = redisTemplate.opsForSet().isMember("biz-account-register-qualification-work-order", workOrderId);
            if (BooleanUtils.isNotTrue(isMember)) {
                log.info("processQualificationMessage not find redis work order id :{}", workOrderId);
                return;
            }
            BizAccountQualificationInfoPo qualificationInfoPo = bizAccountQualificationInfoRepository.queryQualificationInfoByOrderId(workOrderId);
            qualificationInfoPo.setLastQualificationStatus(workOrderAuditResult.getWorkOrderStatus());
            if (Objects.equals(qualificationInfoPo.getWorkOrderId(), qualificationInfoPo.getLastWorkOrderId())) {
                qualificationInfoPo.setQualificationStatus(workOrderAuditResult.getWorkOrderStatus());
            }

            if (Objects.equals(workOrderAuditResult.getWorkOrderStatus(), QualificationAuditStatusEnum.AUDIT_SUCCESS.getCode())
                    && Objects.equals(qualificationInfoPo.getWorkOrderId(), qualificationInfoPo.getLastWorkOrderId())) {
                Integer customerId = workOrderAuditResult.getMappingId();

                Long uid = qualificationInfoPo.getUid();

                BiliUserInfoDto biliUserInfoDto = queryBiliUserInfo(uid);
                CustomerInfoDetailDto customerInfoDetailDto = adCrmGrpcService.queryCustomerInfoDetailByWorkOrderId(workOrderId);

                CreateAccountAndAuditDto createAccountAndAuditDto = new CreateAccountAndAuditDto();

                createAccountAndAuditDto.setCustomerId(customerId);
                int userType = Objects.equals(qualificationInfoPo.getAccountType(), AccountTypeEnum.ENTERPRISE.getCode()) ?
                        UserType.ORG_USER_VALUE : UserType.PERSONAL_USER_VALUE;
                createAccountAndAuditDto = createBaseAccountAndAuditDto(customerId, userType, customerInfoDetailDto, biliUserInfoDto);

                // 设置特殊字段
                createAccountAndAuditDto.setUnitedFirstIndustryId(customerInfoDetailDto.getUnitedFirstIndustryId());
                createAccountAndAuditDto.setUnitedSecondIndustryId(customerInfoDetailDto.getUnitedSecondIndustryId());
                createAccountAndAuditDto.setUnitedThirdIndustryId(customerInfoDetailDto.getUnitedThirdIndustryId());
                createAccountAndAuditDto.setBrandDomain(CollectionUtils.isEmpty(customerInfoDetailDto.getDomains()) ?
                        BusinessQualificationConfig.BRAND_DOMAIN_DEFAULT_VALUE : customerInfoDetailDto.getDomains().get(0));
                createAccountAndAuditDto.setUserName(BusinessQualificationConfig.USER_NAME_PREFIX + biliUserInfoDto.getUserName());

                Integer accountId = adCrmGrpcService.createAccountAndAudit(createAccountAndAuditDto);
                qualificationInfoPo.setAdAccountId(accountId);
                qualificationInfoPo.setAdCustomerId(customerId);

                Integer hasAssistant = ValidEnum.NO.getCode();
                if (Objects.equals(qualificationInfoPo.getAccountType(), AccountTypeEnum.ENTERPRISE.getCode())) {
                    hasAssistant = ValidEnum.YES.getCode();
                } else {
                    Set<Integer> industryIdSet = Arrays.stream(assistantAuthUnitedFirstIndustryIdList.split(",")).map(Integer::valueOf).collect(Collectors.toSet());
                    if (industryIdSet.contains(createAccountAndAuditDto.getUnitedFirstIndustryId())) {
                        hasAssistant = ValidEnum.YES.getCode();
                    }
                }
                addOneAccount(uid, accountId.longValue(), qualificationInfoPo.getAccountType(), hasAssistant);
            }

            redisTemplate.opsForSet().remove("biz-account-register-qualification-work-order", workOrderId);
            bizAccountQualificationInfoRepository.updateQualificationInfo(qualificationInfoPo);
        }
    }

    @Override
    public BiliUserInfoDto queryBiliUserInfo(Long uid) {
        GetUserInfosReq getUserInfosReq = GetUserInfosReq.newBuilder()
                .addMids(uid)
                .addViews(UserViewItem.newBuilder()
                        .setView(UserViewEnum.BASE_INFO_VIEW)
                        .build())
                .build();
        UsersInfoReply userInfos = userAccountGrpcService.getUserInfos(getUserInfosReq);
        UserInfo userInfo = userInfos.getInfosMap().get(uid);
        if (Objects.isNull(userInfo)) {
            throw new BusinessException("未查询到b站用户信息 uid：" + uid);
        }
        String name = userInfo.getBaseInfo().getName();
        String face = userInfo.getBaseInfo().getFace();
        return BiliUserInfoDto.builder().userName(name).face(face).build();
    }

    @Override
    public Boolean authBlueVInfo(Long uid) {
        redisTemplate.opsForSet().add("biz-account-auth-blue-v-info", uid);
        return Boolean.TRUE;
    }


    @Override
    public Boolean authBihuoInfo(Long uid) {
        redisTemplate.opsForSet().add("biz-account-auth-bihuo-info", uid);
        return Boolean.TRUE;
    }

    @Override
    public void updateEnterpriseAccountByBlueVInfo(Long uid) {
        BizAccountQualificationInfoPo qualificationInfoPo = bizAccountQualificationInfoRepository.queryAuditPassQualificationInfoByUid(uid);
        if (Objects.isNull(qualificationInfoPo)) {
            return;
        }
        if (Objects.equals(qualificationInfoPo.getAccountType(), AccountTypeEnum.PERSONAL.getCode())) {
            return;
        }
        Integer adCustomerId = qualificationInfoPo.getAdCustomerId();
        List<CustomerInfoDetailDto> customerInfoDetailDtos = adCrmGrpcService.queryCustomerInfoDetail(CustomerQueryReqDto.builder().customerId(adCustomerId).build());
        if (CollectionUtils.isNotEmpty(customerInfoDetailDtos) && customerInfoDetailDtos.size() > 1) {
            return;
        }

        CustomerInfoDetailDto customerInfoDetailDto = customerInfoDetailDtos.get(0);
        UserOfficialInfoDto userOfficialInfoDto = accountMemberGrpcService.queryOfficialInfo(uid);

        if (Objects.isNull(userOfficialInfoDto)) {
            return;
        }

        // 检查字段是否一致，如果不一致则需要更新
        boolean needUpdate = false;

        // 创建 RegisterEnterpriseAccountDto 对象
        RegisterEnterpriseAccountDto updateDto = new RegisterEnterpriseAccountDto();
        updateDto.setCustomerId(adCustomerId);
        updateDto.setUid(uid);

        // 将 customerInfoDetailDto 中的值赋给 updateDto
        updateDto.setQualificationTypeId(customerInfoDetailDto.getQualificationTypeId());
        updateDto.setQualificationTypeName(customerInfoDetailDto.getQualificationTypeIdDesc());
        updateDto.setUnitedFirstIndustryId(customerInfoDetailDto.getUnitedFirstIndustryId());
        updateDto.setUnitedFirstIndustryName(customerInfoDetailDto.getUnitedFirstIndustryName());
        updateDto.setUnitedSecondIndustryId(customerInfoDetailDto.getUnitedSecondIndustryId());
        updateDto.setUnitedSecondIndustryName(customerInfoDetailDto.getUnitedSecondIndustryName());
        updateDto.setUnitedThirdIndustryId(customerInfoDetailDto.getUnitedThirdIndustryId());
        updateDto.setUnitedThirdIndustryName(customerInfoDetailDto.getUnitedThirdIndustryName());
        updateDto.setIcpRecordNumber(customerInfoDetailDto.getIcpRecordNumber());
        updateDto.setIsBusinessLicenceIndefinite(customerInfoDetailDto.getIsBusinessLicenceIndefinite());
        updateDto.setIsBusinessLicenceIndefiniteDesc(customerInfoDetailDto.getIsBusinessLicenceIndefiniteDesc());
        updateDto.setBusinessLicenseExpireDate(customerInfoDetailDto.getBusinessLicenceExpireDate());

        // 域名
        if (customerInfoDetailDto.getDomains() != null && !customerInfoDetailDto.getDomains().isEmpty()) {
            updateDto.setDomain(customerInfoDetailDto.getDomains().get(0));
        }

        // ICP记录照片
        if (customerInfoDetailDto.getIcpPics() != null && !customerInfoDetailDto.getIcpPics().isEmpty()) {
            List<String> icpRecordPics = customerInfoDetailDto.getIcpPics().stream()
                    .map(CustomerAttachInfoDto::getUrl)
                    .collect(Collectors.toList());
            updateDto.setIcpRecordPics(icpRecordPics);
        }

        // 线下店铺的商品照片
        if (customerInfoDetailDto.getStoreItemPics() != null && !customerInfoDetailDto.getStoreItemPics().isEmpty()) {
            List<String> storeItemPics = customerInfoDetailDto.getStoreItemPics().stream()
                    .map(CustomerAttachInfoDto::getUrl)
                    .collect(Collectors.toList());
            updateDto.setStoreItemPics(storeItemPics);
        }

        // 线下店铺的实体照片
        if (customerInfoDetailDto.getStoreEntityPics() != null && !customerInfoDetailDto.getStoreEntityPics().isEmpty()) {
            List<String> storeEntityPics = customerInfoDetailDto.getStoreEntityPics().stream()
                    .map(CustomerAttachInfoDto::getUrl)
                    .collect(Collectors.toList());
            updateDto.setStoreEntityPics(storeEntityPics);
        }

        // 线下店铺的地址
        updateDto.setStoreAddress(customerInfoDetailDto.getStoreAddress());

        // 公司名称
        if (!Objects.equals(customerInfoDetailDto.getUserName(), userOfficialInfoDto.getUserName())) {
            updateDto.setUserName(userOfficialInfoDto.getUserName());
            needUpdate = true;
        } else {
            updateDto.setUserName(customerInfoDetailDto.getUserName());
        }

        // 营业执照编码
        if (!Objects.equals(customerInfoDetailDto.getBusinessLicenceCode(), userOfficialInfoDto.getBusinessLicenseCode())) {
            updateDto.setBusinessLicenseCode(userOfficialInfoDto.getBusinessLicenseCode());
            needUpdate = true;
        } else {
            updateDto.setBusinessLicenseCode(customerInfoDetailDto.getBusinessLicenceCode());
        }

        // 联系人电话
        if (!Objects.equals(customerInfoDetailDto.getLinkmanPhone(), userOfficialInfoDto.getLinkmanPhone())) {
            updateDto.setLinkmanPhone(userOfficialInfoDto.getLinkmanPhone());
            needUpdate = true;
        } else {
            updateDto.setLinkmanPhone(customerInfoDetailDto.getLinkmanPhone());
        }

        // 联系人邮箱
        if (!Objects.equals(customerInfoDetailDto.getLinkmanEmail(), userOfficialInfoDto.getLinkmanEmail())) {
            updateDto.setLinkmanEmail(userOfficialInfoDto.getLinkmanEmail());
            needUpdate = true;
        } else {
            updateDto.setLinkmanEmail(customerInfoDetailDto.getLinkmanEmail());
        }

        // 联系人地址
        if (!Objects.equals(customerInfoDetailDto.getLinkmanAddress(), userOfficialInfoDto.getLinkmanAddress())) {
            updateDto.setLinkmanAddress(userOfficialInfoDto.getLinkmanAddress());
            needUpdate = true;
        } else {
            updateDto.setLinkmanAddress(customerInfoDetailDto.getLinkmanAddress());
        }

        // 营业执照照片
        if (userOfficialInfoDto.getBusinessLicensePics() != null && !userOfficialInfoDto.getBusinessLicensePics().isEmpty()) {
            // 将字符串列表转换为 CustomerAdditionalQualificationInfoDto 列表
            List<String> businessLicensePics = userOfficialInfoDto.getBusinessLicensePics();
            updateDto.setBusinessLicensePics(businessLicensePics);
            needUpdate = true;
        } else if (customerInfoDetailDto.getBusinessLicencePics() != null && !customerInfoDetailDto.getBusinessLicencePics().isEmpty()) {
            // 使用原有的营业执照照片
            List<String> businessLicensePics = customerInfoDetailDto.getBusinessLicencePics().stream()
                    .map(CustomerAttachInfoDto::getUrl)
                    .collect(Collectors.toList());
            updateDto.setBusinessLicensePics(businessLicensePics);
        }

        // 公函
        if (userOfficialInfoDto.getBusinessAuth() != null && !userOfficialInfoDto.getBusinessAuth().isEmpty()) {
            // 直接比较 businessAuth 字段
            boolean needUpdateBusinessAuth = false;

            // 如果 customerInfoDetailDto 中没有 businessAuth 或者为空，需要更新
            if (customerInfoDetailDto.getBusinessAuth() == null || customerInfoDetailDto.getBusinessAuth().isEmpty()) {
                needUpdateBusinessAuth = true;
            } else {
                // 比较 URL
                Set<String> customerUrls = customerInfoDetailDto.getBusinessAuth().stream()
                        .map(CustomerAdditionalQualificationInfoDto::getUrl)
                        .collect(Collectors.toSet());

                Set<String> userUrls = new HashSet<>(userOfficialInfoDto.getBusinessAuth());

                // 如果 URL 集合不同，需要更新
                if (!customerUrls.equals(userUrls)) {
                    needUpdateBusinessAuth = true;
                }
            }

            if (needUpdateBusinessAuth) {
                // 创建新的 CustomerAdditionalQualificationInfoDto 列表
                List<CustomerAdditionalQualificationInfoDto> businessAuthList = userOfficialInfoDto.getBusinessAuth().stream()
                        .map(url -> {
                            CustomerAdditionalQualificationInfoDto dto = new CustomerAdditionalQualificationInfoDto();
                            dto.setUrl(url);
                            dto.setSpecialInfoTypeId(BusinessQualificationConfig.ENTERPRISE_SPECIAL_INFO_TYPE_ID_DEFAULT_VALUE);
                            dto.setSpecialInfoTypeName(BusinessQualificationConfig.ENTERPRISE_SPECIAL_INFO_TYPE_NAME_DEFAULT_VALUE);
                            return dto;
                        })
                        .collect(Collectors.toList());

                updateDto.setBusinessAuthPics(businessAuthList);
                needUpdate = true;
            }
        }

        // 处理额外资质信息列表（行业资质）
        if (customerInfoDetailDto.getQualifications() != null && !customerInfoDetailDto.getQualifications().isEmpty()) {
            updateDto.setQualifications(customerInfoDetailDto.getQualifications());
        }

        // 设置推广类型，默认为1（网站）
        if (customerInfoDetailDto.getPromotionType() != null) {
            updateDto.setPromotionType(customerInfoDetailDto.getPromotionType());
        }

        // 如果有不一致的字段，则执行更新
        if (needUpdate) {
            // 设置必填默认字段
            updateDto.setIsAgent(businessQualificationConfig.getIsAgent());
            updateDto.setIsInner(businessQualificationConfig.getIsInner());
            updateDto.setCustomerCategory(businessQualificationConfig.getCustomerCategory());
            updateDto.setDepartmentIds(businessQualificationConfig.getEnterpriseUpdateDepartmentIds());
            updateDto.setAreaId(businessQualificationConfig.getEnterpriseUpdateAreaId());
            updateDto.setGroupId(businessQualificationConfig.getEnterpriseUpdateGroupId());

            // 执行更新
            Integer workOrderId = adCrmGrpcService.updateEnterpriseInfo(updateDto);
            log.info("updateEnterpriseAccountByBlueVInfo success, uid:{}, customerId:{}, workOrderId:{}", uid, adCustomerId, workOrderId);
        }
    }

    @Override
    public RealNameInfoCheckDto realNameInfoCheck(String idCardNumber, String realName, Long uid) {

        return accountMemberGrpcService.realNameInfoCheck(idCardNumber, realName, uid);

    }


    public PersonalAccountCrmInfoDto queryCrmPersonalExistAccount(String idCardNumber) {
        List<CustomerInfoDetailDto> customerInfoDetailDtos = adCrmGrpcService.queryCustomerInfoDetail(
                CustomerQueryReqDto.builder()
                        .personalIdCardNumber(idCardNumber)
                        .customerCategories(Collections.singletonList(CustomerCategory.PERSONAL_CUSTOMER_VALUE))
                        .build());
        if (customerInfoDetailDtos.size() <= 1) {
            return null;
        }
        CustomerInfoDetailDto customerInfoDetailDto = customerInfoDetailDtos.stream()
                .sorted(Comparator.comparing(CustomerInfoDetailDto::getId).reversed())
                .findFirst()
                .get();
        String unitedFirstIndustryName = customerInfoDetailDto.getUnitedFirstIndustryName();
        String userName = customerInfoDetailDto.getUserName();

        return PersonalAccountCrmInfoDto.builder().unitedFirstIndustryDesc(unitedFirstIndustryName).username(userName).build();
    }

    // ==================== 辅助方法 ====================

    /**
     * 验证手机验证码
     *
     * @param phone            手机号
     * @param verificationCode 验证码
     */
    private void validatePhoneVerificationCode(String phone, String verificationCode) {
        log.info("validatePhoneVerificationCode start, phone:{}, verifySwitch:{}", phone, REGISTER_PHONE_VERIFY_SWITCH);
        if (Objects.equals(REGISTER_PHONE_VERIFY_SWITCH, 1)) {
            Integer result = adAccountInfoGrpcService.validatePhoneVerificationCode(
                    new String(Base64.getEncoder().encode(phone.getBytes(StandardCharsets.UTF_8))),
                    verificationCode);
            log.info("validatePhoneVerificationCode result:{}", result);
            if (!Objects.equals(result, 0)) {
                log.warn("validatePhoneVerificationCode failed, phone:{}, result:{}", phone, result);
                throw new BusinessException("验证码错误");
            }
        }
        log.info("validatePhoneVerificationCode success, phone:{}", phone);
    }

    /**
     * 验证账号注册资格
     *
     * @param uid 用户ID
     */
    private void validateAccountRegistrationEligibility(Long uid) {
        log.info("validateAccountRegistrationEligibility start, uid:{}", uid);


        // 检查账号是否已绑定经营号
        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(uid);
        if (Objects.nonNull(bizAccountUser)) {
            log.warn("validateAccountRegistrationEligibility account already bound, uid:{}, accountId:{}", uid, bizAccountUser.getAccountId());
            throw new BusinessException("该账号已经绑定过经营号");
        }

        log.info("validateAccountRegistrationEligibility success, uid:{}", uid);

        // 检查是否有正在审核中或已通过的资质信息
        List<BizAccountQualificationInfoPo> bizAccountQualificationInfoPos = bizAccountQualificationInfoRepository.queryQualificationInfoByUid(uid);
        log.info("validateAccountRegistrationEligibility query qualification info, uid:{}, infoSize:{}", uid, bizAccountQualificationInfoPos.size());

        bizAccountQualificationInfoPos = bizAccountQualificationInfoPos.stream()
                .filter(bizAccountQualificationInfoPo ->
                        Objects.equals(bizAccountQualificationInfoPo.getQualificationStatus(), QualificationAuditStatusEnum.AUDIT_SUCCESS.getCode())
                                || Objects.equals(bizAccountQualificationInfoPo.getQualificationStatus(), QualificationAuditStatusEnum.AUDITING.getCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(bizAccountQualificationInfoPos)) {
            log.warn("validateAccountRegistrationEligibility account in process, uid:{}, filteredInfoSize:{}", uid, bizAccountQualificationInfoPos.size());
            throw new BusinessException("该账号在流程中");
        }


    }

    /**
     * 验证实名信息
     *
     * @param personalIdCardNumber 身份证号
     * @param userName             用户名
     * @param uid                  用户ID
     */
    private void validateRealNameInfo(String personalIdCardNumber, String userName, Long uid) {
        log.info("validateRealNameInfo start, uid:{}, userName:{}", uid, userName);
        RealNameInfoCheckDto realNameInfoCheckDto = accountMemberGrpcService.realNameInfoCheck(personalIdCardNumber, userName, uid);
        log.info("validateRealNameInfo check result, uid:{}, realNameCheck:{}, idCardCheck:{}",
                uid, realNameInfoCheckDto.getRealNameCheckResult(), realNameInfoCheckDto.getIdCardNumberCheckResult());

        if (BooleanUtils.isNotTrue(realNameInfoCheckDto.getRealNameCheckResult()) ||
                BooleanUtils.isNotTrue(realNameInfoCheckDto.getIdCardNumberCheckResult())) {
            log.warn("validateRealNameInfo failed, uid:{}, realNameCheck:{}, idCardCheck:{}",
                    uid, realNameInfoCheckDto.getRealNameCheckResult(), realNameInfoCheckDto.getIdCardNumberCheckResult());
            throw new BusinessException("实名信息或身份证号信息校验失败，与主站不一致");
        }

        log.info("validateRealNameInfo success, uid:{}", uid);
    }

    /**
     * 创建基础账号审核DTO
     *
     * @param customerId 客户ID
     * @param userType   用户类型
     * @return 账号审核DTO
     */
    private CreateAccountAndAuditDto createBaseAccountAndAuditDto(Integer customerId, int userType, CustomerInfoDetailDto customerInfoDetailDto, BiliUserInfoDto biliUserInfoDto) {
        CreateAccountAndAuditDto createAccountAndAuditDto = new CreateAccountAndAuditDto();
        createAccountAndAuditDto.setCustomerId(customerId);
        createAccountAndAuditDto.setAccountType(BusinessQualificationConfig.ACCOUNT_TYPE_DEFAULT_VALUE);
        createAccountAndAuditDto.setIsInner(BusinessQualificationConfig.IS_INNER_ENUM_DEFAULT_VALUE);
        createAccountAndAuditDto.setUserType(userType);
        createAccountAndAuditDto.setFinanceType(BusinessQualificationConfig.FINANCE_TYPE_DEFAULT_VALUE);
        createAccountAndAuditDto.setDepartmentId(businessQualificationConfig.getCommonDepartmentId());
        createAccountAndAuditDto.setIsSupportFly(businessQualificationConfig.getIsSupportFly());
        createAccountAndAuditDto.setIsSupportPickup(businessQualificationConfig.getIsSupportPickup());
        createAccountAndAuditDto.setAdStatus(businessQualificationConfig.getAdStatus());
        createAccountAndAuditDto.setGdStatus(businessQualificationConfig.getGdStatus());
        createAccountAndAuditDto.setIsSupportContent(businessQualificationConfig.getIsSupportContent());
        createAccountAndAuditDto.setIsSupportGame(businessQualificationConfig.getIsSupportGame());
        createAccountAndAuditDto.setIsSupportSeller(businessQualificationConfig.getIsSupportSeller());
        createAccountAndAuditDto.setIsSupportDpa(businessQualificationConfig.getIsSupportDpa());
        createAccountAndAuditDto.setIsSupportMas(businessQualificationConfig.getIsSupportMas());
        createAccountAndAuditDto.setIsSupportCluePass(businessQualificationConfig.getIsSupportCluePass());
        createAccountAndAuditDto.setUnitedFirstIndustryId(customerInfoDetailDto.getUnitedFirstIndustryId());
        createAccountAndAuditDto.setUnitedSecondIndustryId(customerInfoDetailDto.getUnitedSecondIndustryId());
        createAccountAndAuditDto.setUnitedThirdIndustryId(customerInfoDetailDto.getUnitedThirdIndustryId());
        createAccountAndAuditDto.setBrandDomain(CollectionUtils.isEmpty(customerInfoDetailDto.getDomains()) ?
                BusinessQualificationConfig.BRAND_DOMAIN_DEFAULT_VALUE : customerInfoDetailDto.getDomains().get(0));
        createAccountAndAuditDto.setUserName(BusinessQualificationConfig.USER_NAME_PREFIX + biliUserInfoDto.getUserName());

        return createAccountAndAuditDto;
    }

    /**
     * 处理企业账号注册 - 更新已有客户信息
     *
     * @param dto                   注册信息
     * @param customerInfoDetailDto 客户详情
     * @param uid                   用户ID
     * @return 审核状态码
     */
    private Integer handleEnterpriseAccountUpdate(RegisterEnterpriseAccountDto dto, CustomerInfoDetailDto customerInfoDetailDto, Long uid) {
        log.info("handleEnterpriseAccountUpdate start, uid:{}, customerId:{}", uid, customerInfoDetailDto.getId());
        dto.setCustomerId(customerInfoDetailDto.getId());
        // 设置必要的默认值
        dto.setIsAgent(businessQualificationConfig.getIsAgent());
        dto.setIsInner(businessQualificationConfig.getIsInner());
        dto.setCustomerCategory(businessQualificationConfig.getCustomerCategory());
        dto.setDepartmentId(businessQualificationConfig.getEnterpriseDepartmentId());
        dto.setAreaId(businessQualificationConfig.getEnterpriseAreaId());
        dto.setCustomerCategory(BusinessQualificationConfig.ENTERPRISE_CUSTOMER_CATEGORY_DEFAULT_VALUE);
        dto.setGroupId(businessQualificationConfig.getEnterpriseGroupId());
        Integer orderId = adCrmGrpcService.updateEnterpriseInfo(dto);
        log.info("handleEnterpriseAccountUpdate update enterprise info success, uid:{}, orderId:{}", uid, orderId);

        // 记录工单信息
        redisTemplate.opsForSet().add("biz-account-register-qualification-work-order", orderId);
        bizAccountQualificationInfoRepository.createQualificationInfo(uid, orderId, AccountTypeEnum.ENTERPRISE.getCode());
        log.info("handleEnterpriseAccountUpdate create qualification info success, uid:{}, orderId:{}", uid, orderId);
        return QualificationAuditStatusEnum.AUDITING.getCode();
    }

    /**
     * 处理企业账号注册 - 创建新客户
     *
     * @param dto 注册信息
     * @param uid 用户ID
     * @return 审核状态码
     */
    private Integer handleEnterpriseAccountCreate(RegisterEnterpriseAccountDto dto, Long uid) {
        log.info("handleEnterpriseAccountCreate start, uid:{}", uid);

        // 设置必要的默认值
        dto.setIsAgent(businessQualificationConfig.getIsAgent());
        dto.setIsInner(businessQualificationConfig.getIsInner());
        dto.setCustomerCategory(businessQualificationConfig.getCustomerCategory());
        dto.setDepartmentId(businessQualificationConfig.getEnterpriseDepartmentId());
        dto.setAreaId(businessQualificationConfig.getEnterpriseAreaId());
        dto.setCustomerCategory(BusinessQualificationConfig.ENTERPRISE_CUSTOMER_CATEGORY_DEFAULT_VALUE);
        dto.setGroupId(businessQualificationConfig.getEnterpriseGroupId());
        log.info("handleEnterpriseAccountCreate set default values, uid:{}, departmentId:{}, areaId:{}, groupId:{}",
                uid, dto.getDepartmentId(), dto.getAreaId(), dto.getGroupId());

        // 注册企业账号
        Integer orderId = adCrmGrpcService.registerEnterpriseAccount(dto);
        log.info("handleEnterpriseAccountCreate register enterprise account success, uid:{}, orderId:{}", uid, orderId);

        // 记录工单信息
        redisTemplate.opsForSet().add("biz-account-register-qualification-work-order", orderId);
        bizAccountQualificationInfoRepository.createQualificationInfo(uid, orderId, AccountTypeEnum.ENTERPRISE.getCode());
        log.info("handleEnterpriseAccountCreate create qualification info success, uid:{}, orderId:{}", uid, orderId);
        return QualificationAuditStatusEnum.AUDITING.getCode();
    }

    /**
     * 处理个人账号注册 - 更新已有客户信息
     *
     * @param dto                   注册信息
     * @param customerInfoDetailDto 客户详情
     * @return 审核状态码
     */
    private Integer handlePersonalAccountUpdate(RegisterPersonalAccountDto dto, CustomerInfoDetailDto customerInfoDetailDto) {
        log.info("handlePersonalAccountUpdate start, uid:{}, customerId:{}", dto.getUid(), customerInfoDetailDto.getId());
        dto.setCustomerId(customerInfoDetailDto.getId());

        // 设置必要的默认值
        dto.setIsAgent(businessQualificationConfig.getIsAgent());
        dto.setIsInner(businessQualificationConfig.getIsInner());
        dto.setCustomerCategory(businessQualificationConfig.getCustomerCategory());
        dto.setDepartmentIds(businessQualificationConfig.getPersonalDepartmentIds());
        dto.setAreaId(businessQualificationConfig.getPersonalAreaId());
        dto.setCustomerCategory(BusinessQualificationConfig.PERSONAL_CUSTOMER_CATEGORY_DEFAULT_VALUE);
        dto.setPersonalIdCardType(BusinessQualificationConfig.PERSONAL_ID_CARD_TYPE_DEFAULT_VALUE);
        log.info("handlePersonalAccountUpdate set default values, uid:{}, departmentIds:{}, areaId:{}",
                dto.getUid(), dto.getDepartmentIds(), dto.getAreaId());

        Integer orderId = adCrmGrpcService.updatePersonalInfo(dto);
        log.info("handlePersonalAccountUpdate update personal info success, uid:{}, orderId:{}", dto.getUid(), orderId);

        // 记录工单信息
        redisTemplate.opsForSet().add("biz-account-register-qualification-work-order", orderId);
        bizAccountQualificationInfoRepository.createQualificationInfo(dto.getUid(), orderId, AccountTypeEnum.PERSONAL.getCode());
        log.info("handlePersonalAccountUpdate create qualification info success, uid:{}, orderId:{}", dto.getUid(), orderId);
        return QualificationAuditStatusEnum.AUDITING.getCode();
    }

    /**
     * 处理个人账号注册 - 创建账号并审核
     *
     * @param dto                   注册信息
     * @param customerInfoDetailDto 客户详情
     * @return 审核状态码
     */
    private Integer handlePersonalAccountCreateAndAudit(RegisterPersonalAccountDto dto, CustomerInfoDetailDto customerInfoDetailDto) {
        log.info("handlePersonalAccountCreateAndAudit start, uid:{}, customerId:{}", dto.getUid(), customerInfoDetailDto.getId());
        Integer customerId = customerInfoDetailDto.getId();

        BiliUserInfoDto biliUserInfoDto = queryBiliUserInfo(dto.getUid());

        // 创建账号审核DTO
        CreateAccountAndAuditDto createAccountAndAuditDto = createBaseAccountAndAuditDto(customerId, UserType.ORG_USER_VALUE, customerInfoDetailDto, biliUserInfoDto);
        log.info("handlePersonalAccountCreateAndAudit created base account audit dto, uid:{}, customerId:{}", dto.getUid(), customerId);

        // 创建账号并审核
        Integer accountId = adCrmGrpcService.createAccountAndAudit(createAccountAndAuditDto);
        log.info("handlePersonalAccountCreateAndAudit create and audit account success, uid:{}, accountId:{}", dto.getUid(), accountId);

        // 创建资质信息
        BizAccountQualificationInfoPo qualificationInfoPo = new BizAccountQualificationInfoPo();
        qualificationInfoPo.setUid(dto.getUid());
        qualificationInfoPo.setAdCustomerId(customerId);
        qualificationInfoPo.setAdAccountId(accountId);
        qualificationInfoPo.setAccountType(AccountTypeEnum.PERSONAL.getCode());
        qualificationInfoPo.setQualificationStatus(QualificationAuditStatusEnum.AUDIT_SUCCESS.getCode());
        qualificationInfoPo.setLastQualificationStatus(QualificationAuditStatusEnum.AUDIT_SUCCESS.getCode());

        bizAccountQualificationInfoRepository.createQualificationInfo(qualificationInfoPo);
        log.info("handlePersonalAccountCreateAndAudit create qualification info success, uid:{}, accountId:{}", dto.getUid(), accountId);

        addOneAccount(dto.getUid(), Long.valueOf(accountId), AccountTypeEnum.ENTERPRISE.getCode(), ValidEnum.YES.getCode());
        log.info("handlePersonalAccountCreateAndAudit add one account success, uid:{}, accountId:{}", dto.getUid(), accountId);
        return QualificationAuditStatusEnum.AUDIT_SUCCESS.getCode();
    }

    /**
     * 处理个人账号注册 - 创建新客户
     *
     * @param dto 注册信息
     * @return 审核状态码
     */
    private Integer handlePersonalAccountCreate(RegisterPersonalAccountDto dto) {
        log.info("handlePersonalAccountCreate start, uid:{}", dto.getUid());

        // 设置必要的默认值
        dto.setIsAgent(businessQualificationConfig.getIsAgent());
        dto.setIsInner(businessQualificationConfig.getIsInner());
        dto.setCustomerCategory(businessQualificationConfig.getCustomerCategory());
        dto.setDepartmentIds(businessQualificationConfig.getPersonalDepartmentIds());
        dto.setAreaId(businessQualificationConfig.getPersonalAreaId());
        dto.setCustomerCategory(BusinessQualificationConfig.PERSONAL_CUSTOMER_CATEGORY_DEFAULT_VALUE);
        dto.setPersonalIdCardType(BusinessQualificationConfig.PERSONAL_ID_CARD_TYPE_DEFAULT_VALUE);
        log.info("handlePersonalAccountCreate set default values, uid:{}, departmentIds:{}, areaId:{}",
                dto.getUid(), dto.getDepartmentIds(), dto.getAreaId());

        // 注册个人账号
        Integer orderId = adCrmGrpcService.registerPersonalAccount(dto);
        log.info("handlePersonalAccountCreate register personal account success, uid:{}, orderId:{}", dto.getUid(), orderId);

        // 记录工单信息
        redisTemplate.opsForSet().add("biz-account-register-qualification-work-order", orderId);
        bizAccountQualificationInfoRepository.createQualificationInfo(dto.getUid(), orderId, AccountTypeEnum.PERSONAL.getCode());
        log.info("handlePersonalAccountCreate create qualification info success, uid:{}, orderId:{}", dto.getUid(), orderId);
        return QualificationAuditStatusEnum.AUDITING.getCode();
    }
}
