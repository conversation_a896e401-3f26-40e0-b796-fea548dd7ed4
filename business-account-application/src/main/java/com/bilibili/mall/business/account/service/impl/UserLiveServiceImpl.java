package com.bilibili.mall.business.account.service.impl;

import com.bapis.account.service.v2.UserInfo;
import com.bilibili.mall.business.account.api.enums.AppToolBizStatusEnum;
import com.bilibili.mall.business.account.api.enums.AuthTypeEnum;
import com.bilibili.mall.business.account.api.enums.RoleTypeEnum;
import com.bilibili.mall.business.account.api.request.live.UserToolRequest;
import com.bilibili.mall.business.account.api.vo.base.BaseToolVO;
import com.bilibili.mall.business.account.api.vo.live.AuthTypeVO;
import com.bilibili.mall.business.account.api.vo.live.ToolVO;
import com.bilibili.mall.business.account.api.vo.live.UserToolVO;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.AdAccountInfoGrpcService;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.AdAccountLabelGrpcService;
import com.bilibili.mall.business.account.domain.domains.account.entity.BizAccountUserInfo;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountInfoRepository;
import com.bilibili.mall.business.account.domain.domains.account.service.BizAccountDomainService;
import com.bilibili.mall.business.account.enums.BizAccountAuthEnum;
import com.bilibili.mall.business.account.enums.BookingOuterTypeEnum;
import com.bilibili.mall.business.account.enums.ToolTypeEnum;
import com.bilibili.mall.business.account.infrastructure.entity.BizAccountInfo;
import com.bilibili.mall.business.account.infrastructure.utils.FunctionUtil;
import com.bilibili.mall.business.account.service.BaseToolService;
import com.bilibili.mall.business.account.service.UserLiveService;
import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/19 5:26 下午
 */
@Slf4j
@Service
public class UserLiveServiceImpl implements UserLiveService {

    @Resource(name = "taskExecutor")
    private Executor executor;

    @Resource
    private BizAccountDomainService bizAccountDomainService;

    @Resource
    private BaseToolService baseToolService;

    @Resource
    private BizAccountInfoRepository bizAccountInfoRepository;

    @Resource
    private AdAccountLabelGrpcService adAccountLabelGrpcService;

    @Autowired
    private AdAccountInfoGrpcService adAccountInfoGrpcService;

    /**
     * 员工号白名单
     */
    @DynamicValue
    @Value("${liveUserIdWhiteList:}")
    private String liveUserIdWhiteList;

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 查询用户可用的工具信息
     *
     * @param request
     * @return
     */
    @Override
    public UserToolVO queryUserTools(UserToolRequest request) {
        //白名单校验
        if (!StringUtils.isEmpty(liveUserIdWhiteList)) {
            List<Long> whiteList = FunctionUtil.splitByComma(liveUserIdWhiteList, Long::valueOf);
            if (!whiteList.contains(request.getUid())) {
                return UserToolVO.builder().haveBindBizAccount(Boolean.FALSE).build();
            }
        }

        BizAccountUserInfo accountUserInfo = bizAccountDomainService.queryAccountUserInfo(request.getUid());
        if (Objects.isNull(accountUserInfo)) {
            return UserToolVO.builder().haveBindBizAccount(Boolean.FALSE).build();
        }
        Set<Integer> adAccountLabels = adAccountLabelGrpcService.queryAdAccountLabels(accountUserInfo.getAdId().intValue());
        Boolean hasThirdAuth = Boolean.FALSE;
        if (Objects.equals(accountUserInfo.getRoleType(), RoleTypeEnum.MASTER.getType())) {
            hasThirdAuth = adAccountLabels.contains(BizAccountAuthEnum.THIRD_PARTY_LANDING_PAGE_URL.getLabelId());
            List<Integer> appIds = adAccountInfoGrpcService.queryAdAccountAwakenApp(accountUserInfo.getAdId().intValue());
            if (!CollectionUtils.isEmpty(appIds) && !accountUserInfo.getAuthList().contains(AuthTypeEnum.APP_PACKAGE.getCode())) {
                accountUserInfo.getAuthList().add(AuthTypeEnum.RESERVE.getCode());
            }
        } else if (Objects.equals(accountUserInfo.getRoleType(), RoleTypeEnum.STAFF.getType())) {
            hasThirdAuth = adAccountLabels.contains(BizAccountAuthEnum.THIRD_PARTY_LANDING_PAGE_URL.getLabelId()) && accountUserInfo.getAuthList().contains(BizAccountAuthEnum.RESERVE.getCode());
        }

        UserToolVO userToolVO = UserToolVO.builder()
                .haveBindBizAccount(Boolean.TRUE)
                .adId(accountUserInfo.getAdId())
                .roleType(accountUserInfo.getRoleType())
                .authTypeList(accountUserInfo.getAuthList().stream().map(r -> Objects.requireNonNull(AuthTypeEnum.getAuthTypeByCode(r)).getType()).collect(Collectors.toList()))
                .build();
         // todo 待直播侧一起优化
//        if (null != request.getAuthType() && request.getAuthType() >= 0) {
            userToolVO.setCategoryList(convert2VOList(accountUserInfo.getAuthList(), request.getUid(), hasThirdAuth, AuthTypeEnum.getAuthTypeByType(request.getAuthType())));
//        }
        return userToolVO;
    }

    private List<AuthTypeVO> convert2VOList(List<String> codes, Long uid, Boolean hasThirdPartyLandingPathAuth, AuthTypeEnum authTypeEnum) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        List<AuthTypeVO> res = new ArrayList<>();

        // 目前直播仅开放在线预约组件
        // 2024-09-19 新增在线咨询
//        boolean haveReserve =
//                codes.stream().anyMatch(v -> Objects.equals(AuthTypeEnum.RESERVE.getCode(), v)) && AuthTypeEnum.RESERVE == authTypeEnum;
//        boolean haveOnlineConsult =
//                codes.stream().anyMatch(v -> Objects.equals(AuthTypeEnum.ONLINE_CONSULT.getCode(), v)) && AuthTypeEnum.ONLINE_CONSULT == authTypeEnum;
//        boolean haveContactQW =
//                codes.stream().anyMatch(v -> Objects.equals(AuthTypeEnum.CONTACT_QW.getCode(), v)) && AuthTypeEnum.CONTACT_QW == authTypeEnum;
//        boolean haveMiniApp =
//                codes.stream().anyMatch(v -> Objects.equals(AuthTypeEnum.MINI_APP.getCode(), v)) && AuthTypeEnum.MINI_APP == authTypeEnum;
//        boolean haveAppPackage =
//                codes.stream().anyMatch(v -> Objects.equals(AuthTypeEnum.APP_PACKAGE.getCode(), v)) && AuthTypeEnum.APP_PACKAGE == authTypeEnum;

        boolean haveReserve =
                codes.stream().anyMatch(v -> Objects.equals(AuthTypeEnum.RESERVE.getCode(), v)) ;
        boolean haveOnlineConsult =
                codes.stream().anyMatch(v -> Objects.equals(AuthTypeEnum.ONLINE_CONSULT.getCode(), v)) ;
        boolean haveContactQW =
                codes.stream().anyMatch(v -> Objects.equals(AuthTypeEnum.CONTACT_QW.getCode(), v)) ;
        boolean haveMiniApp =
                codes.stream().anyMatch(v -> Objects.equals(AuthTypeEnum.MINI_APP.getCode(), v)) ;
        boolean haveAppPackage =
                codes.stream().anyMatch(v -> Objects.equals(AuthTypeEnum.APP_PACKAGE.getCode(), v)) ;

        CompletableFuture<Void> appBuildAsync = null;
        CompletableFuture<Void> buildReserveAsync = null;
        CompletableFuture<Void> buildContractQWAsync = null;
        CompletableFuture<Void> buildMiniAppAsync = null;
        CompletableFuture<Void> buildOnlineConsultAsync = null;

        if (haveAppPackage) {
            appBuildAsync = CompletableFuture.runAsync(() -> buildAppPackageData(uid, res), executor);
        }

        if (haveReserve) {
            buildReserveAsync = CompletableFuture.runAsync(() -> buildReserve(uid, hasThirdPartyLandingPathAuth, res), executor);
        }

        if (haveContactQW) {
            buildContractQWAsync = CompletableFuture.runAsync(() -> buildContractQW(uid, res), executor);
        }

        if (haveMiniApp) {
            buildMiniAppAsync = CompletableFuture.runAsync(() -> buildMiniApp(uid, res), executor);
        }
        if (haveOnlineConsult) {
            buildOnlineConsultAsync = CompletableFuture.runAsync(() -> buildOnlineConsult(uid, res), executor);
        }

        if (haveAppPackage) {
            getCompleteFutureValue(appBuildAsync, 2000);
        }
        if (haveReserve) {
            getCompleteFutureValue(buildReserveAsync, 2000);
        }
        if (haveContactQW) {
            getCompleteFutureValue(buildContractQWAsync, 2000);
        }
        if (haveMiniApp) {
            getCompleteFutureValue(buildMiniAppAsync, 2000);
        }
        if (haveOnlineConsult) {
            getCompleteFutureValue(buildOnlineConsultAsync, 2000);
        }
        res.sort(Comparator.comparing(r -> AuthTypeEnum.getAuthTypeByCode(r.getCode()).getType()));
        return res;
    }

    private <T> T getCompleteFutureValue(CompletableFuture<T> completableFuture, Integer milliseconds) {
        try {
            return completableFuture.get(milliseconds, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("getCompleteFutureValue ", e);
            throw new RuntimeException(e);
        }
    }

    private void buildOnlineConsult(Long uid, List<AuthTypeVO> res) {
        List<BaseToolVO> toolList = null;
        try {
            toolList = baseToolService.queryBaseTool(ToolTypeEnum.ONLINE_CONSULT.getType(), null, uid);
        } catch (Exception e) {
            log.warn("查询工具列表异常 uid={}", uid);
        }
        List<ToolVO> toolVOList =
                FunctionUtil.convertList(
                        toolList,
                        v ->
                                ToolVO.builder()
                                        .toolsId(v.getId())
                                        .name(v.getName())
                                        .imageUrl(v.getImgUrl())
                                        .build());
        AuthTypeVO catVo =
                AuthTypeVO.builder()
                        .code(AuthTypeEnum.ONLINE_CONSULT.getCode())
                        .name(AuthTypeEnum.ONLINE_CONSULT.getDesc())
                        .toolList(toolVOList)
                        .forbidOutsite(Boolean.FALSE)
                        .build();
        res.add(catVo);
    }

    private void buildMiniApp(Long uid, List<AuthTypeVO> res) {
        List<BaseToolVO> toolList = null;
        try {
            toolList = baseToolService.queryBaseTool(ToolTypeEnum.APPLETS.getType(), null, uid);
            toolList = toolList.stream().filter(toolVo -> Objects.equals(toolVo.getStatus(), 1)).collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("查询工具列表异常 uid={}", uid);
        }
        List<ToolVO> toolVOList =
                FunctionUtil.convertList(
                        toolList,
                        v ->
                                ToolVO.builder()
                                        .toolsId(v.getId())
                                        .name(v.getName())
                                        .imageUrl(v.getImgUrl())
                                        .build());
        AuthTypeVO catVo =
                AuthTypeVO.builder()
                        .code(AuthTypeEnum.MINI_APP.getCode())
                        .name(AuthTypeEnum.MINI_APP.getDesc())
                        .toolList(toolVOList)
                        .forbidOutsite(Boolean.FALSE)
                        .build();
        res.add(catVo);
    }

    private void buildContractQW(Long uid, List<AuthTypeVO> res) {
        List<BaseToolVO> toolList = null;
        try {
            toolList = baseToolService.queryBaseTool(ToolTypeEnum.WORK_WECHAT.getType(), null, uid);
        } catch (Exception e) {
            log.warn("查询工具列表异常 uid={}", uid);
        }
        List<ToolVO> toolVOList =
                FunctionUtil.convertList(
                        toolList,
                        v ->
                                ToolVO.builder()
                                        .toolsId(v.getId())
                                        .name(v.getName())
                                        .imageUrl(v.getImgUrl())
                                        .build());
        AuthTypeVO catVo =
                AuthTypeVO.builder()
                        .code(AuthTypeEnum.CONTACT_QW.getCode())
                        .name(AuthTypeEnum.CONTACT_QW.getDesc())
                        .toolList(toolVOList)
                        .forbidOutsite(Boolean.FALSE)
                        .build();
        res.add(catVo);
    }

    private void buildReserve(Long uid, Boolean hasThirdPartyLandingPathAuth, List<AuthTypeVO> res) {
        List<BaseToolVO> bookingBaseToolVoList = new ArrayList<>();
        List<BaseToolVO> thirdPartyLandingPageBaseToolVoList = new ArrayList<>();

        try {
            bookingBaseToolVoList = baseToolService.queryBaseTool(ToolTypeEnum.BOOKING.getType(), null, uid);
            if (BooleanUtils.isTrue(hasThirdPartyLandingPathAuth)) {
                BizAccountUserInfo bizAccountUserInfo = bizAccountDomainService.checkUserAdId(uid);
                Long accountId = bizAccountUserInfo.getAccountId();
                BizAccountInfo bizAccountInfo = bizAccountInfoRepository.selectByAccountId(accountId);

                thirdPartyLandingPageBaseToolVoList = baseToolService.queryBaseTool(ToolTypeEnum.THIRD_PARTY_LANDING_PAGE_URL.getType(), null, bizAccountInfo.getUid());
            }
        } catch (Exception e) {
            log.warn("查询工具列表异常 uid={}", uid);
        }

        List<ToolVO> bookingToolVOList =
                FunctionUtil.convertList(
                        bookingBaseToolVoList,
                        v ->
                                ToolVO.builder()
                                        .toolsId(v.getId())
                                        .name(v.getName())
                                        .imageUrl(v.getImgUrl())
                                        .bookingOuterType(BookingOuterTypeEnum.IN_SITE.getCode())
                                        .build());
        List<ToolVO> thirdPartyLandingPageToolVOList =
                FunctionUtil.convertList(
                        thirdPartyLandingPageBaseToolVoList,
                        v ->
                                ToolVO.builder()
                                        .toolsId(v.getId())
                                        .name(v.getName())
                                        .imageUrl(v.getImgUrl())
                                        .bookingOuterType(BookingOuterTypeEnum.OUT_SITE.getCode())
                                        .build());

        List<ToolVO> toolVOList = new ArrayList<>();
        toolVOList.addAll(bookingToolVOList);
        toolVOList.addAll(thirdPartyLandingPageToolVOList);

        AuthTypeVO catVo =
                AuthTypeVO.builder()
                        .code(AuthTypeEnum.RESERVE.getCode())
                        .name(AuthTypeEnum.RESERVE.getDesc())
                        .forbidOutsite(!hasThirdPartyLandingPathAuth)
                        .toolList(toolVOList)
                        .build();
        res.add(catVo);
    }

    private void buildAppPackageData(Long uid, List<AuthTypeVO> res) {
        List<BaseToolVO> toolList = null;
        try {
            toolList = baseToolService.queryBaseTool(ToolTypeEnum.APP_PACKAGE.getType(), null, uid);
        } catch (Exception e) {
            log.warn("查询工具列表异常 uid={}", uid);
        }
        toolList = toolList.stream().filter(r -> AppToolBizStatusEnum.EFFECT.getCode().equals(r.getStatus())).collect(Collectors.toList());
        List<ToolVO> toolVOList =
                FunctionUtil.convertList(
                        toolList,
                        v ->
                                ToolVO.builder()
                                        .toolsId(v.getId())
                                        .name(v.getName())
                                        .imageUrl(v.getImgUrl())
                                        .androidAppPackageBo(v.getAndroidAppPackageBo())
                                        .iosAppPackageBo(v.getIosAppPackageBo())
                                        .build());
        AuthTypeVO catVo =
                AuthTypeVO.builder()
                        .code(AuthTypeEnum.APP_PACKAGE.getCode())
                        .name(AuthTypeEnum.APP_PACKAGE.getDesc())
                        .toolList(toolVOList)
                        .forbidOutsite(Boolean.FALSE)
                        .build();
        res.add(catVo);
    }

    private Boolean queryUserThirdPartyLandingPageAuth(Long uid) {
        return redisTemplate.opsForSet().isMember("business-account-third-party-landing-page-member", uid);
    }

    private Boolean queryUserThirdPartyLandingPageAuthByAdId(Long adId) {
        Set<Integer> adAccountLabels = adAccountLabelGrpcService.queryAdAccountLabels(adId.intValue());
        return adAccountLabels.contains(BizAccountAuthEnum.THIRD_PARTY_LANDING_PAGE_URL.getLabelId());
    }

}




