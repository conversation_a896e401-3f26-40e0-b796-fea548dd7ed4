syntax = "proto3";

package ai.creativity.bubblerecommendgateway2;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/ai/creativity.bubble.recommend.gateway2;v1";
option java_package = "com.bapis.ai.creativity.bubble.recommend.gateway2";
option java_multiple_files = true;
option (wdcli.appid) = "ai.creativity.bubble-recommend-gateway2";

service BubbleRecommendService {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);

  rpc SyncReq(BubbleRecommendRequest) returns (BubbleRecommendResponse) {
    option (google.api.http) = {
      get: "/dl/api/aibubble_recommend/sync_req"
    };
  }
}

message BubbleRecommendRequest { 
  string upload_id = 1 [(gogoproto.moretags) = 'form:"upload_id" validate:"required"']; // 投稿流程唯一标识
  string mid       = 2 [(gogoproto.moretags) = 'form:"mid"       validate:"required"']; // 用户mid
  int32  upfrom    = 3 [(gogoproto.moretags) = 'form:"upfrom"    validate:"required"']; // 投稿来源: 1-web 2-粉版 3-必剪
  string platform  = 4 [(gogoproto.moretags) = 'form:"platform"  validate:"required"']; // 平台类型: pc android ios
  int64  build     = 5 [(gogoproto.moretags) = 'form:"build"     validate:"required"']; // 客户端版本号
}

message BubbleRecommendResponse {
  int32  error_code           = 1 [(gogoproto.jsontag) = "error_code", json_name = "error_code"]; // 错误码:  0-成功
  string error_msg            = 2 [(gogoproto.jsontag) = "error_msg",  json_name = "error_msg"];  // 错误描述
  repeated BubbleInfo bubbles = 3 [(gogoproto.jsontag) = "bubbles",    json_name = "bubbles"];    // 推荐结果-成功时有且仅有top1
}

message BubbleInfo {
  int64  id   = 1 [(gogoproto.jsontag) = "id",   json_name = "id"];   // 气泡id
  string url  = 2 [(gogoproto.jsontag) = "url",  json_name = "url"];  // 落地页
  string icon = 3 [(gogoproto.jsontag) = "icon", json_name = "icon"]; // 气泡类型
  string related_tab_url = 4; // 关联的tab
  int64  stime           = 5; // 展示时间段的开始时间戳，无时间段时不填写
  int64  etime           = 6; // 展示时间段的结束时间戳，无时间段时不填写
}