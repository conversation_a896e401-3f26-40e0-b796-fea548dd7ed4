// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
// import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package ai.efficiency.umeng.v1;
// NOTE: 最后请删除这些无用的注释 (゜-゜)つロ

option go_package = "buf.bilibili.co/bapis/bapis-gen/ai/efficiency.umeng;v1";
option java_package = "com.bapis.ai.efficiency.umeng";
option java_multiple_files = true;
option (gogoproto.goproto_getters_all) = false;
option (wdcli.appid) = "ai.efficiency.umeng";

service UMengProxy {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);
  rpc GetUMengData(GetUMengDataReq) returns (GetUMengDataResp);
}

message GetUMengDataReq {
  repeated string idValues = 1 [(gogoproto.moretags) = 'form:"idValues" validate:"required"'];
}

message GetUMengDataResp {
  Result result = 1 [(gogoproto.jsontag) = 'result'];
  bool matchStatus = 2 [(gogoproto.jsontag) = 'matchStatus'];
  string idValue = 3 [(gogoproto.jsontag) = 'idValue'];
  int64 Code = 4 [(gogoproto.jsontag) = 'code'];
  string RequestID = 5 [(gogoproto.jsontag) = 'requestId']; 
}

message Result {
  map<string,string> list1 = 1 [(gogoproto.jsontag) = 'list1'];
  map<string,string> base1 = 2 [(gogoproto.jsontag) = 'base1'];
  map<string,string> ext1 = 3 [(gogoproto.jsontag) = 'ext1'];
  map<string,string> list2 = 4 [(gogoproto.jsontag) = 'list2'];
  map<string,string> list2kv = 5 [(gogoproto.jsontag) = 'list2kv'];
  repeated string interestV3 = 6 [(gogoproto.jsontag) = 'interestV3'];
  string dan2 = 7 [(gogoproto.jsontag) = 'dan2'];
}