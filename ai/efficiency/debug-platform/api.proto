syntax = "proto3";

// import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
import "google/protobuf/empty.proto";

package  ai.efficiency.debug_platform.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/ai/efficiency.debug.platform;v1";
option java_package = "com.bapis.ai.efficiency.debug.platform";
option java_multiple_files = true;
option (wdcli.appid) = "ai.efficiency.debug-platform";

service DebugPlatform {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);

  //获取打标数量
  rpc GetGSBRecordsNum(GetGSBRecordsNumReq)returns(GetGSBRecordsNumResp);
}

message GetGSBRecordsNumReq{
  BusinessType BusinessType = 1 ;
  int32 Dimension = 2;
  string CDate = 3;
}

message GetGSBRecordsNumResp{
  int64 GSBRecordNum = 1;
}

enum BusinessType {
  ALL = 0;
  SEARCH = 1;
  STORY = 2;
  PEGASUS = 3;
  REALTED = 4;
  OTT = 5;
  WEB_SEARCH = 6;
  WEB_PEGASUS = 7;
  WEB_REALTED = 8;
}