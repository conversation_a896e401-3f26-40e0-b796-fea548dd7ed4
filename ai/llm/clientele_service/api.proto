syntax = "proto3";
package ai.llm.clientele_service;

import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/ai/llm.clientele_service;api";
option java_package = "com.bapis.ai.llm.clientele_service";
option java_multiple_files = true;
option (wdcli.appid) = "ai.llm.llm-clientele-service";

service LLMClienteleService {
    // 获取客服会话中的所有消息
    rpc GetChat(ChatRequest) returns (ChatResponse);
}


message ChatRequest {
    // 消息的唯一标识
    uint64 msgkey = 1;
    // 消息的序号（供排序用）
    uint64 seqno = 2;
    // 会话id，用于串联上下文
    uint64 machine_sess_id = 3;
    // 发送方：0-机器人，1-人工客服，2-通知卡片，3-用户智能会话中主要是0/2/3
    uint32 sender_type = 4;
    // 用户id
    uint64 mid = 5;
    // 消息类型
    uint32 msg_type = 6;
    // 消息的内容
    string msg_content = 7;
    // 秒级时间戳
    uint32 ts = 8;
    // 技能组id
    uint32 gid = 9;
    // 店铺id，对于官方客服，值为0
    uint32 shop_id = 10;
    // 店铺类型，0-官方，1-第三方商家，2-带货
    uint32 shop_father_id = 11;
    // 类型，0-未命中灰度，1-命中单轮，2-命中多轮
    uint32 type = 12;
}

message ChatResponse {
    // 0 正常收到消息，非0表示错误
    int32 code = 1;
    // 错误码对应的消息
    string err_msg = 2;
}