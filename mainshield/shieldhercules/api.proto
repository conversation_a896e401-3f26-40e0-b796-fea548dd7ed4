// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package shield.factor.service.v1;

// NOTE: 最后请删除这些无用的注释 (゜-゜)つロ

option go_package = "buf.bilibili.co/bapis/bapis-gen/mainshield/shieldhercules;api";
option java_package = "com.bapis.mainshield.shieldhercules";
option java_multiple_files = true;
option (wdcli.appid) = "main.shield.shieldhercules";

service Factor {
  rpc ParamFactor(ParamFactorReq) returns (ParamFactorResp) {
    option (google.api.http) = {
      post: "/param_factor"
    };
  }

  rpc UserHasTag(UserHasTagReq) returns (UserHasTagResp) {
    option (google.api.http) = {
      get: "/user/has_tag"
    };
  }

  rpc UserSetTag(UserSetTagReq) returns (UserSetTagResp) {
    option (google.api.http) = {
      post: "/user/set_tag"
    };
  }
}

message ParamFactorReq {
  string scene = 1 [(gogoproto.moretags) = 'form:"scene" validate:"required"'];
  string event_ctx = 2[(gogoproto.moretags) = 'form:"event_ctx" validate:"required"']; //事件上下文信息
  int64 event_ts = 3 [(gogoproto.moretags) = 'form:"event_ts"']; //事件发生事件
  string trace_id = 4 [(gogoproto.moretags) = 'form:"trace_id"']; //事件唯一标示
  string callback = 6 [(gogoproto.moretags) = 'form:"callback"']; //回调链接
}

message ParamFactorResp {
  map<string, string> json_data = 1 [(gogoproto.jsontag) = 'json_data',json_name="json_data"];
  map<string, string> text_data = 2 [(gogoproto.jsontag) = 'text_data',json_name="text_data"];
  map<string, int64> int_data = 3 [(gogoproto.jsontag) = 'int_data',json_name="int_data"];
  map<string, bool> bool_data = 4 [(gogoproto.jsontag) = 'bool_data',json_name="bool_data"];
  map<string, float> float_data = 5 [(gogoproto.jsontag) = 'float_data',json_name="float_data"];
}

message UserHasTagReq {
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  string tag = 2 [(gogoproto.moretags) = 'form:"tag" validate:"required"'];
}

message UserHasTagResp {
  bool has = 1 [(gogoproto.jsontag) = 'has',json_name="has"];
}

message UserSetTagReq {
  int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
  string tag = 2[(gogoproto.moretags) = 'form:"tag" validate:"required"'];
  int64 ttl = 3[(gogoproto.moretags) = 'form:"ttl"'];
}

message UserSetTagResp {
  int64 id = 1 [(gogoproto.jsontag) = 'id',json_name="id"];
}