syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

package community.shield.erebus_service.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/mainshield/erebus;v1";
option java_package = "com.bapis.mainshield.erebus";
option java_multiple_files = true;
option (gogoproto.goproto_getters_all) = false;
option (wdcli.appid) = "community.shield.erebus-service";

service ErebusService {
  rpc CheckBuvidIsControl(CheckBuvidIsControlReq) returns (CheckBuvidIsControlResp);
}

message CheckBuvidIsControlReq {
  string buvid = 1[(gogoproto.jsontag) = 'buvid validate:"required"'];
  string biz = 2[(gogoproto.jsontag) = 'biz validate:"required"'];
  int64 biz_id = 3[(gogoproto.jsontag) = 'biz_id'];
  int64 mid = 4[(gogoproto.jsontag) = 'mid'];
  string content = 5[(gogoproto.jsontag) = 'content'];
}

message CheckBuvidIsControlResp {
  bool hit = 1 [(gogoproto.jsontag) = 'hit'];
}