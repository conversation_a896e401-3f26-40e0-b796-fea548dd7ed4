syntax = "proto3";

/*
 * v0.1.0
 * 用户登陆信息
 */
package copyright.right.model;

option go_package = "buf.bilibili.co/bapis/bapis-gen/copyright/right.model;v1";
option java_package = "com.bapis.copyright.right.model";
option java_multiple_files = true;

message UserReq {
  //主键id
  int64 id = 1;
  //供应商名称
  string supplier = 2;
  //供应商社会信用代码
  string username = 3;
  //密码
  string password = 4;
  //创建人
  string created_by = 5;
  //更新人
  string updated_by = 6;
  //用户类型
  int64 user_type = 7;
}

message UserReply {
  //主键id
  int64 id = 1;
  //供应商名称
  string supplier = 2;
  //供应商社会信用代码
  string username = 3;
  //密码
  string password = 4;
  //创建人
  string created_by = 5;
  //更新人
  string updated_by = 6;
  //用户类型
  int64 user_type = 7;
}

message UserDetailReq {
  //供应商社会信用代码
  string username = 1;
}

message UserPasswordReq {
  //主键id
  int64 id = 1;
  //供应商名称
  string supplier = 2;
  //供应商社会信用代码
  string username = 3;
  //旧密码
  string password = 4;
  //新密码
  string new_password = 5;
  //创建人
  string created_by = 6;
  //更新人
  string updated_by = 7;
}

message UserIdReq{
  //主键id
  int64 id = 1;
}

message UserSupplierReq{
  //供应商名称
  string supplier = 1;
}

message UserSupplierListReq{

}

message UserSupplierListReply{
  repeated UserReply user_list = 1;
}

message UserListReq{

  string username = 1;

  int64 user_type = 2;

  int64 page_no = 3;

  int64 offset = 4;
}

message UserCommonInfo{
  //主键id
  int64 id = 1;
  //供应商名称
  string supplier = 2;
  //供应商社会信用代码
  string username = 3;
  //密码
  string password = 4;
  //创建人
  string created_by = 5;
  //更新人
  string updated_by = 6;
  //用户类型
  int64 user_type = 7;
}

message UserListReply{
  //作品信息列表
  repeated UserCommonInfo user_list = 1;
  //页码
  int64 page_no = 2;
  //每页条数
  int64 offset = 3;
  //总条数
  int64 total = 4;
}



