syntax = "proto3";

/*
 * v0.1.0
 * 物流信息
 */
package copyright.right.model;

option go_package = "buf.bilibili.co/bapis/bapis-gen/copyright/right.model;v1";
option java_package = "com.bapis.copyright.right.model";
option java_multiple_files = true;

message LogisticsReq {
  //主键id
  int64 id = 1;
  //物流公司
  string logistics_company = 2;
  //物流单号
  string logistics_no = 3;
  //备注
  string comment = 4;
  //邮寄图片
  string logistics_picture = 5;
  //物流信息所属流程(样品物流,防伪标物流,大货物流)
  string process = 6;
  //物流信息所属流程编号
  string process_no = 7;
  //创建人
  string created_by = 8;
  //更新人
  string updated_by = 9;
}

message DeleteLogisticsReq {
  //主键id
  int64 id = 1;
}

message LogisticsListReply{
  //物流信息列表
  repeated Logistics logistics = 1;
}

message Logistics {
  //主键id
  int64 id = 1;
  //物流公司
  string logistics_company = 2;
  //物流单号
  string logistics_no = 3;
  //备注
  string comment = 4;
  //邮寄图片
  string logistics_picture = 5;
  //物流信息所属流程(样品物流,防伪标物流,大货物流)
  string process = 6;
  //物流信息所属流程编号
  string process_no = 7;
  //创建人
  string created_by = 8;
  //更新人
  string updated_by = 9;
}

message LogisticsReply {
  //主键id
  int64 id = 1;
  //物流公司
  string logistics_company = 2;
  //物流单号
  string logistics_no = 3;
  //备注
  string comment = 4;
  //邮寄图片
  string logistics_picture = 5;
  //物流信息所属流程(样品物流,防伪标物流,大货物流)
  string process = 6;
  //物流信息所属流程编号
  string process_no = 7;
  //创建人
  string created_by = 8;
  //更新人
  string updated_by = 9;
}

message LogisticsIdReq {
  //主键id
  int64 id = 1;
}

message LogisticsIdsReq{
  repeated int64 ids = 1;
}

message LogisticsProcessReq{
  repeated LogisticsProcessDetail process_detail_list = 1;
}

message LogisticsProcessDetail{
  //物流信息所属流程(样品物流,防伪标物流,大货物流)
  string process = 1;
  //物流信息所属流程编号
  string process_no = 2;
}