syntax = "proto3";

/*
 * v0.1.0
 * 费用项
 */
package copyright.right.model;

option go_package = "buf.bilibili.co/bapis/bapis-gen/copyright/right.model;v1";
option java_package = "com.bapis.copyright.right.model";
option java_multiple_files = true;

message ExpenseCategoryListReq{
  repeated int64 ids = 1;
}

message ExpenseCategoryListReply{
  repeated ExpenseCategoryDetail expense_category_list = 1;
}

message ExpenseCategoryDetail{
  /**
   * 主键
   */
  int64 id = 1;

  /**
   * 费用项名称
   */
   string name = 2;

  /**
   * 分级 二级为最末级
   */
  int32 level = 3;

  /**
   * 费用项描述
   */
  string description = 4;

  /**
   * 父费用项id
   */
  int64 parent_id = 5;
}

message ExpenseElementListReq{
  repeated string abbr_list = 1;
}

message ExpenseElementListReply{
  repeated ExpenseElementDetail expense_element_list = 1;
}

message ExpenseElementDetail{
  /**
   * 主键
   */
  int64 id = 1;

  /**
   * 要素名称
   */
  string name = 2;

  /**
   * 费用要素简写(公式填写使用)
   */
  string abbr = 3;

  /**
   * 费用要素具体描述
   */
  string description = 4;

  /**
   * 是否可编辑
   */
  int32 editable = 5;

  /**
   * 取值来源
   */
  string source = 6;
}

message AddExpenseTemplateReq{

  /**
   * 费用要素模板
   */
  string name = 1;

  /**
   * 费用模板公式
   */
  string expression = 2;

  /**
   * 状态 ONLINE/OFFLINE 上架/下架
   */
  string status = 3;

  /**
   * 费用分类id
   */
  int64 categoryId = 4;

  /**
   * 操作人
   */
  string operator = 5;
}

message UpdateExpenseTemplateReq{
  /**
    * 主键id
    */
  int64 id = 1;

  /**
   * 状态 ONLINE/OFFLINE 上架/下架
   */
  string status = 2;

  /**
   * 操作人
   */
  string operator = 3;
}

message ExpenseTemplateListReq{
  /**
   * 费用分类id
   */
  int64 category_id = 1;

  /**
   * 状态
   */
  string status = 2;

  // 页码
  int64 page_no = 3;

  // 每页条数
  int64 offset = 4;
}


message ExpenseRevisionReq{
  //id
  int64 id = 1;
  //作品id
  int64 work_id = 2;
  //合同编号
  string agreement_no = 3;
  //费用项id
  int64 expense_category_id = 4;
  //修改方式
  string modify_type = 5;
  //订正金额
  string amount = 6;
  //币种
  string currency = 7;
  //备注
  string reason = 8;
  //取消原因
  string cancel_reason = 9;
  //状态
  string status = 10;
  //所属部门
  string department = 11;
  //处理人
  string dealer = 12;
  //经办人
  string operator = 13;
  //创建人
  string created_by = 14;
  //更新人
  string updated_by = 15;
}

message ExpenseRevisionReqBatch{
  repeated ExpenseRevisionReq ExpenseRevisionBatch = 1;
}

message CloseExpenseRevisionReq{
  //项目id
  int64 id = 1;
  //操作用户
  string operator = 2;
  //撤销原因
  string cancel_reason = 3;
}

message ExpenseRevisionDetailReq{
  //id
  int64 id = 1;
  //操作人
  string operator = 2;
}


message ExpenseRevisionListReq{
  //id
  int64 id = 1;
  //作品id
  int64 work_id = 2;
  //费用项id
  int64 expense_category_id = 3;
  //任务状态
  string task_status = 4;
  //查询用户
  string operator = 5;
  //页码
  int64 page_no = 6;
  //每页条数
  int64 offset = 7;

}


message ExpenseTemplateListReply{
  repeated ExpenseTemplateDetail expense_template_list = 1;
  //页码
  int64 page_no = 2;
  //每页条数
  int64 offset = 3;
  //总条数
  int64 total = 4;
}

message ExpenseTemplateDetailReq{
  //id
  int64 id = 1;
  //操作人
  string operator = 2;
}

message ExpenseTemplateDetail{
  /**
   * 主键id
   */
  int64 id = 1;

  /**
   * 费用模板名称
   */
  string name = 2;

  /**
   * 费用模板公式
   */
  string expression = 3;

  /**
   * 状态 ONLINE/OFFLINE 上架/下架
   */
  string status = 4;

  /**
   * 费用分类id
   */
  int64 categoryId = 5;

  /**
   * 包含费用要素列表以,分割
   */
  string elements = 6;
}

message ExpenseRevisionListReply{
  repeated ExpenseRevisionDetailReply expense_revisions = 1;
  //页码
  int64 page_no = 2;
  //每页条数
  int64 offset = 3;
  //总条数
  int64 total = 4;
}

message ExpenseRevisionDetailReply{
  //id
  int64 id = 1;
  //作品id
  int64 work_id = 2;
  //合同编号
  string agreement_no = 3;
  //费用项id
  int64 expense_category_id = 4;
  //修改方式
  string modify_type = 5;
  //订正金额
  string amount = 6;
  //币种
  string currency = 7;
  //备注
  string reason = 8;
  //取消原因
  string cancel_reason = 9;
  //状态
  string status = 10;
  //所属部门
  string department = 11;
  //处理人
  string dealer = 12;
  //经办人
  string operator = 13;
  //创建时间(yyyy-MM-dd HH:mm:ss格式)
  string ctime = 14;
  //更新时间(yyyy-MM-dd HH:mm:ss格式)
  string mtime = 15;
  //创建人
  string created_by = 16;
  //更新人
  string updated_by = 17;
}

message ElementValueReq{
  /**
   * 费用要素简称列表
   */
  repeated string abbr_list = 1;

  /**
   * 作品id
   */
  int64 work_id = 2;

  /**
   * 合同编号
   */
  string agreement_no = 3;

  /**
    * crm资源包id
    */
  string crm_resource_id = 4;

  /**
   * crm资源包名称
   */
  string crm_resource_name = 5;

  /**
   * crm收入类型
   */
  string crm_income_type = 6;

  /**
   * 归属月份
   */
  string attributive_month = 7;
}

message ElementValueReply{
  repeated ElementValueDetail element_value_list = 1;
}

message ElementValueDetail{
  /**
   * 费用要素简称
   */
  string abbr = 1;

  /**
   * 金额
   */
  string amount = 2;

  /**
   * 币种
   */
  string currency = 3;
}

message CreateEstimateReq{
  /**
    * 预估单流程名
    */
  string name = 1;

  /**
   * 费用分类id
   */
  int64 expense_category_id = 2;

  /**
   * 费用模板id
   */
  int64 expense_template_id = 3;

  /**
   * 状态
   */
  string status = 4;

  /**
   * 币种
   */
  string currency = 5;

  /**
   * 上传人
   */
  string created_by = 6;

  /**
   * 部门
   */
  string department = 7;

  /**
   * 预估单item列表
   */
   repeated CreateEstimateItemReq estimate_item_list = 8;
}

message CreateEstimateItemReq{
  /**
   * 交易id
   */
  int64 request_id = 1;

  /**
   * 合同编号
   */
  string agreement_no = 2;

  /**
   * 归属月份
   */
  string attributive_month = 3;

  /**
   * crm合同编号
   */
  string crm_resource_id = 4;

  /**
   * 费用模板id
   */
  int64 expense_template_id = 5;

  /**
   * 作品id
   */
  int64 work_id = 6;

  /**
   * 权利项id
   */
  int64 right_category_id = 7;

  /**
   * 资产编号
   */
  string asset_no = 8;

  /**
   * 人工参数
   */
  string artificial_param = 9;

  /**
   * 金额
   */
  string amount = 10;

  /**
   * 币种
   */
  string currency = 11;

  /**
   * crm收入类型
   */
  string crm_income_type = 12;
}

message EstimateListReq{
  /**
   * 流程名称
   */
  string name = 1;

  /**
   * 费用分类id
   */
  int64 expense_category_id = 2;

  /**
   * 作品id
   */
  int64 work_id = 3;

  /**
   * 任务状态（RUNNING("流程中"), CANCELED("已取消"),ARCHIVED("已完成")）
   */
  string status = 4;

  /**
   * 操作人
   */
  string operator = 5;

  /**
   * 页码
   */
  int32 page_no = 6;

  /**
   * 每页条数
   */
  int32 offset = 7;
}

message EstimateDetailReq{
  /**
   * 预估单id
   */
  int64 id = 1;

  /**
   * 操作人
   */
  string operator = 2;
}

message EstimateListReply{
  //预估单列表
  repeated EstimateDetail estimate_list = 1;
  //页码
  int64 page_no = 2;
  //每页条数
  int64 offset = 3;
  //总条数
  int64 total = 4;
}

message EstimateDetail{
  /**
   * 主键id
   */
  int64 id = 1;

  /**
   * 预估单流程名
   */
  string name = 2;

  /**
   * 费用分类id
   */
  int64 expense_category_id = 3;

  /**
   * 费用模板id
   */
  int64 expense_template_id = 4;

  /**
   * 审批人
   */
  string dealer = 5;

  /**
   * 状态
   */
  string status = 6;

  /**
   * 总金额
   */
  string amount = 7;

  /**
   * 币种
   */
  string currency = 8;

  /**
   * 创建时间
   */
  string ctime = 9;

  /**
   * 修改时间(留档/取消时间)
   */
  string mtime = 10;

  /**
   * 上传人
   */
  string created_by = 11;

  /**
   * 备注(取消原因)
   */
  string remark = 12;

  /**
   * 部门
   */
  string department = 13;

  /**
   * 预估单item列表
   */
   repeated EstimateItemDetail estimate_item_list = 14;
}

message EstimateItemDetail{
  /**
   * 主键id
   */
  int64 id = 1;

  /**
   * 预估单id
   */
  int64 estimate_id = 2;

  /**
   * 交易id
   */
  int64 request_id = 3;

  /**
   * 合同编号
   */
  string agreement_no = 4;

  /**
   * 归属月份
   */
  string attributive_month = 5;

  /**
   * crm合同编号
   */
  string crm_resource_id = 6;

  /**
   * 作品id
   */
  int64 work_id = 7;

  /**
   * 权利项id
   */
  int64 right_category_id = 8;

  /**
   * 资产编号
   */
  string asset_no = 9;

  /**
   * 人工参数
   */
  string artificial_param = 10;

  /**
   * 金额
   */
  string amount = 11;

  /**
   * 币种
   */
  string currency =12;

  /**
   * crm资源名称
   */
  string crm_resource_name = 13;

  /**
   * crm收入类型
   */
  string crm_income_type = 14;
}

message RateEstimateReq{
  /**
   * 预估单id
   */
  int64 estimate_id = 1;

  /**
   * 是否通过 1通过 0不通过
   */
  int32 approve_result = 2;

  /**
   * 备注
   */
  string remark = 3;

  /**
   * 操作人
   */
  string operator = 4;
}

message CloseEstimateReq{
  /**
   * 预估单id
   */
  int64 estimate_id = 1;

  /**
   * 备注
   */
  string remark = 2;

  /**
   * 操作人
   */
  string operator = 3;
}

message CreateStatementReq{
  /**
    * 结算单流程名
    */
  string name = 1;

  /**
   * 费用分类id
   */
  int64 expense_category_id = 2;

  /**
   * 费用模板id
   */
  int64 expense_template_id = 3;

  /**
   * 状态
   */
  string status = 4;

  /**
   * 币种
   */
  string currency = 5;

  /**
   * 上传人
   */
  string created_by = 6;

  /**
   * 部门
   */
  string department = 7;

  /**
   * 结算单item列表
   */
  repeated CreateStatementItemReq statement_item_list = 8;

}

message CreateStatementItemReq{
  /**
   * 交易id
   */
  int64 request_id = 1;

  /**
   * 合同编号
   */
  string agreement_no = 2;

  /**
   * 归属月份
   */
  string attributive_month = 3;

  /**
   * crm资源id
   */
  string crm_resource_id = 4;

  /**
   * crm收入类型
   */
  string crm_income_type = 5;

  /**
   * 费用模板id
   */
  int64 expense_template_id = 6;

  /**
   * 作品id
   */
  int64 work_id = 7;

  /**
   * 权利项id
   */
  int64 right_category_id = 8;

  /**
   * 资产编号
   */
  string asset_no = 9;

  /**
   * 人工参数
   */
  string artificial_param = 10;

  /**
   * 金额
   */
  string amount = 11;

  /**
   * 币种
   */
  string currency = 12;
}

message StatementListReq{
  /**
   * 流程名称
   */
  string name = 1;

  /**
   * 费用分类id
   */
  int64 expense_category_id = 2;

  /**
   * 作品id
   */
  int64 work_id = 3;

  /**
   * 任务状态（RUNNING("流程中"), CANCELED("已取消"),ARCHIVED("已完成")）
   */
  string status = 4;

  /**
   * 操作人
   */
  string operator = 5;

  /**
   * 页码
   */
  int32 page_no = 6;

  /**
   * 每页条数
   */
  int32 offset = 7;
}

message StatementListReply{
  //结算单列表
  repeated StatementDetail statement_list = 1;
  //页码
  int64 page_no = 2;
  //每页条数
  int64 offset = 3;
  //总条数
  int64 total = 4;
}

message StatementDetailReq{
  /**
   * 预估单id
   */
  int64 id = 1;

  /**
   * 操作人
   */
  string operator = 2;
}

message StatementDetail{
  /**
   * 主键id
   */
  int64 id = 1;

  /**
   * 结算单流程名
   */
  string name = 2;

  /**
   * 费用分类id
   */
  int64 expense_category_id = 3;

  /**
   * 费用模板id
   */
  int64 expense_template_id = 4;

  /**
   * 审批人
   */
  string dealer = 5;

  /**
   * 状态
   */
  string status = 6;

  /**
   * 总金额
   */
  string amount = 7;

  /**
   * 币种
   */
  string currency = 8;

  /**
   * 创建时间
   */
  string ctime = 9;

  /**
   * 修改时间(留档/取消时间)
   */
  string mtime = 10;

  /**
   * 上传人
   */
  string created_by = 11;

  /**
   * 备注(取消原因)
   */
  string remark = 12;

  /**
   * 部门
   */
  string department = 13;

  /**
   * 结算单item列表
   */
  repeated StatementItemDetail statement_item_list = 14;
}

message StatementItemDetail{
  /**
   * 主键id
   */
  int64 id = 1;

  /**
   * 结算单id
   */
  int64 statement_id = 2;

  /**
   * 交易id
   */
  int64 request_id = 3;

  /**
   * 合同编号
   */
  string agreement_no = 4;

  /**
   * 归属月份
   */
  string attributive_month = 5;

  /**
   * crm资源id
   */
  string crm_resource_id = 6;

  /**
   * 作品id
   */
  int64 work_id = 7;

  /**
   * 权利项id
   */
  int64 right_category_id = 8;

  /**
   * 资产编号
   */
  string asset_no = 9;

  /**
   * 人工参数
   */
  string artificial_param = 10;

  /**
   * 金额
   */
  string amount = 11;

  /**
   * 币种
   */
  string currency =12;

  /**
  * crm资源名称
  */
  string crm_resource_name = 13;

  /**
   * crm收入类型
   */
  string crm_income_type = 14;
}

message RateStatementReq{
  /**
   * 结算单id
   */
  int64 statement_id = 1;

  /**
   * 是否通过 1通过 0不通过
   */
  int32 approve_result = 2;

  /**
   * 备注
   */
  string remark = 3;

  /**
   * 操作人
   */
  string operator = 4;
}

message CloseStatementReq{
  /**
   * 结算单id
   */
  int64 statement_id = 1;

  /**
   * 备注
   */
  string remark = 2;

  /**
   * 操作人
   */
  string operator = 3;
}
