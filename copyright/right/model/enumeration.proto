syntax = "proto3";
 
/*
 * v0.1.0
 * 枚举信息
 */
package copyright.right.model;

option go_package = "buf.bilibili.co/bapis/bapis-gen/copyright/right.model;v1";
option java_package = "com.bapis.copyright.right.model";
option java_multiple_files = true;

message EnumerationReq{
  //枚举类型
  string enum_type = 1;
  //主键id
  int64 id = 2;
  //枚举名称
  string name = 3;
  //枚举描述
  string description = 4;
  //所属业务类型
  string business_type = 5;
  //enum组类型
  string group_type = 6;
  //创建人
  string created_by = 7;
  //更新人
  string updated_by = 8;
}

message Enumeration{
  //id
  int64 id = 1;
  //枚举名称
  string name = 2;
  //枚举类型
  string enum_type = 3;
  //枚举描述
  string description = 4;
  //所属业务类型
  string business_type = 5;
  //enum组类型
  string group_type = 6;
}

message EnumerationReply{
  repeated Enumeration enumeration_list = 1;
}

message DeleteEnumerationReq {
  //主键id
  int64 id = 1;
}

message EnumerationListReq{
  //主键id
  int64 id = 1;
  //枚举名称
  string name = 2;
  //枚举类型
  string enum_type = 3;
  //枚举描述
  string description = 4;
  //所属业务类型
  string business_type = 5;
  //enum组类型
  string group_type = 6;
  // 页码
  int64 page_no = 7;
  //每页条数
  int64 offset = 8;
}

message EnumerationListReply{
  //枚举表
  repeated EnumerationCommonInfo enumeration_list = 1;
  //页码
  int64 page_no = 2;
  //每页条数
  int64 offset = 3;
  //总条数
  int64 total = 4;
}


message EnumerationCommonInfo{
  //主键id
  int64 id = 1;
  //枚举名称
  string name = 2;
  //枚举类型
  string enum_type = 3;
  //枚举描述
  string description = 4;
  //所属业务类型
  string business_type = 5;
  //enum组类型
  string group_type = 6;
  //创建人
  string created_by = 7;
  //更新人
  string updated_by = 8;
}
