syntax = "proto3";

/*
 * v0.1.0
 * 版权资产信息
 */
package copyright.right.model;

option go_package = "buf.bilibili.co/bapis/bapis-gen/copyright/right.model;v1";
option java_package = "com.bapis.copyright.right.model";
option java_multiple_files = true;

message CopyrightAssetListReq{
  //作品id
  int64 work_id = 1;
  //权利项id
  int64 right_item_category_id = 2;
  //页码
  int64 page_no = 3;
  //每页条数
  int64 offset = 4;
  //是否授权资产
  bool authorization = 5;
  //作品分类id
  int64 work_category_id = 6;
  //作品名称
  string work_name = 7;
  //权利性质
  string permission_nature = 8;
  //权利区域
  string permission_area = 9;
  //是否可转授
  string allow_transfer = 10;
  //ott
  string ott = 11;
  //iptv
  string iptv = 12;
  //是否二创
  string secondary_innovation = 13;
  //维权权利
  string rights_protection = 14;
  //优先合作权
  string priority_cooperation = 15;
  //版权链是否完整
  string is_complete = 16;
  //是否可缓存下载
  string can_download = 17;
  //优先合作权
  string right_authority = 18;
  //开始时间
  string start_time = 19;
  //结束时间
  string end_time = 20;
  //权利状态
  string status = 21;
  //知识产权Id
  int64 ip_id = 22;
  /**
   * 合同全称
   */
  string agreement_name = 23;

  /**
   * 合同编号
   */
  string agreement_no = 24;

  /**
   * 甲方
   */
  string first_party = 25;

  /**
   * 乙方
   */
  string cooperation_partner = 26;

  /**
   * 留档开始时间
   */
  string agreement_start_mtime = 27;

  /**
   * 留档结束时间
   */
  string agreement_end_mtime = 28;

  /**
   * 是否过滤音乐版权
   */
  bool filter_music = 29;
}

message CopyrightAssetListReply{
  //资产列表
  repeated CopyrightAssetListDetail copyright_asset_list  = 1;
  //页码
  int64 pn = 2;
  //每页条数
  int64 ps = 3;
  //总条数
  int64 total = 4;
}

message CopyrightAssetListDetail{
  // 作品id
  int64 work_id = 1;
  //权利项id
  int64 right_item_category_id = 2;
  //开始时间
  string start_time = 3;
  //结束时间
  string end_time = 4;
  //权利期限
  string duration = 5;
  //状态
  string status = 6;
  //创建人
  string created_by = 7;
  //修改人
  string updated_by = 8;
  //创建时间
  string ctime = 9;
  //更新时间
  string mtime = 10;
  //权利性质
  string permission_nature = 11;
  //权利区域
  string permission_area = 12;
  //资产编号
  string asset_no = 13;
  //合同编号
  string agreement_no = 14;
  //合同名称
  string agreement_name = 15;
}

message CopyrightAssetDetailReq{
  //作品id
  int64 work_id = 1;
  //是否授权资产
  bool authorization = 2;
  //策略id
  int64 strategy_id = 3;
  //操作人
  string operator = 4;
  //是否验权
  bool right = 5;
}

message CopyrightAssetDetailReply{
  //资产列表
  repeated CopyrightAssetDetail copyright_asset_list = 1;
}

message CopyrightAssetDetail{
  //作品id
  int64 work_id = 1;
  //权利项id
  int64 right_item_category_id = 2;
  //权利资产列表
  repeated CopyrightAssetAttr right_attr_list = 3;
  //策略id
  int64 strategy_id = 4;
}

message CopyrightAssetAttr{
  int64 right_item_attr_id = 1;
  string right_item_attr_name = 2;
  string right_item_attr_value = 3;
  string option_value = 4;
  int32 attr_valid = 5;
  string created_by = 6;
  string updated_by = 7;
  string start_time = 8;
  string end_time = 9;
  string duration = 10;
  string ctime = 11;
  string mtime = 12;
  string status = 13;
}

message CopyrightAssetRecordListReq{
  int64 work_id = 1;
  //页码
  int64 page_no = 2;
  //每页条数
  int64 offset = 3;
}

message CopyrightAssetRecordListReply{
  //资产列表
  repeated CopyrightAssetRecord copyright_asset_record_list  = 1;
  //页码
  int64 pn = 2;
  //每页条数
  int64 ps = 3;
  //总条数
  int64 total = 4;
}

message CopyrightAssetRecord{
  int64 work_id = 1;
  int64 right_item_category_id = 2;
  string right_item_attr_name =3;
  string before_value = 4;
  string after_value = 5;
  string changed_time = 6;
  string action_type = 7;
  string created_by = 8;
  string optional_value = 9;
}

message CopyrightAssetPolicyReply{
  //key->策略id,value->策略投放数
  map<int64, int64> policyGroupInfo = 1;
}