syntax = "proto3";
package vas.xactivity.v2;
option go_package = "buf.bilibili.co/bapis/bapis-gen/vas/xactivity.service.v2;v2";
option java_package = "com.bapis.vas.xactivity.service.v2";
option java_multiple_files = true;
option (wdcli.appid) = "main.vas.xactivity-service";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "vas/nft/card_frontend/service/api.proto";
import "live/order/v2/order.proto";
import "vip/open_order/api.proto";
import "vas/nft/common/common.proto";
import "pgc/service/activity/lt/model.proto";
import "extension/wdcli/wdcli.proto";
import "vas/common/collection.proto";

// 抽卡C端接口
service DLCActFrontend {
    // 活动基础信息
    // Tag: Frontend_DLC活动信息
    rpc BasicInfo(BasicInfoReq) returns (BasicInfoResp);
    // 活动预约
    // Tag: Frontend_DLC活动信息
    rpc Book(BookReq) returns (CommonExecResp);
    // 奖池抽取奖品
    // Tag: Frontend_DLC活动抽奖
    rpc DrawItem(DrawItemReq) returns (DrawItemResp);
    // 发送具体的卡片，先用于运营后台批量发送
    // Tag: Frontend_DLC活动抽奖
    rpc SendCard(SendCardReq) returns (SendCardResp);
    // 奖池详情
    // Tag: Frontend_DLC活动奖池
    rpc LotteryDetail(LotteryDetailReq) returns (LotteryDetailResp);
    // 奖池首页详情
    // Tag: Frontend_DLC活动奖池
    rpc LotteryHomeDetail(LotteryHomeDetailReq) returns (LotteryHomeDetailResp);
    // 收集度奖励列表,OGV活动专用
    // Tag: Frontend_DLC活动收集度奖励
    rpc CollectRedeemList(CollectRedeemListFrontendReq) returns (CollectRedeemListFrontendResp);
    // 收集度奖励详情
    // Tag: Frontend_DLC活动收集度奖励
    rpc CollectRedeemDetail(CollectRedeemDetailFrontReq) returns (CollectRedeemDetailFrontResp);
    // 收集度奖励实物奖励地址信息
    // Tag: Frontend_DLC活动收集度奖励
    rpc CollectRedeemExpress(CollectRedeemExpressReq) returns (CollectRedeemExpressResp);
    // 领取收集度奖励
    // Tag: Frontend_DLC活动收集度奖励
    rpc CollectRedeem(CollectRedeemReq) returns (CollectRedeemResp);
    // 图鉴列表(隐私设置下客态不可见)
    // Tag: Frontend_DLC活动卡牌
    rpc ActItemList(ActItemListReq) returns (ActItemListResp);
    // 图鉴详情(陀螺仪类型卡牌专用)
    // Tag: Frontend_DLC活动卡牌
    rpc CardDetail(CardDetailReq) returns (CardDetailResp);
    // 持有人列表（展示评论背景图，隐私设置下客态不可见头像昵称）
    // Tag: Frontend_DLC活动信息
    rpc HolderTopList(HolderTopListReq) returns (HolderTopListResp);
    // 收藏家列表（展示评论背景图，隐私设置下客态不可见头像昵称）
    // Tag: Frontend_DLC活动卡牌
    rpc AllTopList(AllTopListReq) returns (AllTopListResp);
    // 卡牌信息，含持有率，发行量和card_id_list
    // Tag: Frontend_DLC活动卡牌
    rpc CardInfo(CardInfoReq) returns (CardTypeIdItem);
    // 活动勋章信息
    // Tag: 收藏家勋章
    rpc ActMedalInfoList(ActMedalInfoListReq) returns (ActMedalInfoListResp);
    // 用户实名校验接口
    rpc RealNameVerify(RealNameVerifyReq) returns (RealNameVerifyResp);
    // 查询DLC活动或图鉴分享短链
    rpc GetDLCShortURl(GetDLCShortURlReq) returns (GetDLCShortURlResp);
    // 查询某个活动下的所有卡池的卡片奖励
    rpc GetActLotteryItemList(GetActLotteryItemListReq) returns (GetActLotteryItemListResp);
    // 查询所有活动ID
    rpc GetAllActId(GetAllActIdReq) returns (GetAllActIdResp);
    // 查询所有活动信息 供装扮feed流使用
    rpc ListActForGarbFeed(ListActForGarbFeedReq) returns (ListActForGarbFeedResp);
    rpc ListActForGarbSearch(ListActForGarbSearchReq) returns (ListActForGarbSearchResp);
    // 根据活动ID查询上链状态
    rpc GetUpChainStatusByActId(GetUpChainStatusByActIdReq) returns (GetUpChainStatusByActIdResp);
    // 根SpuID查询上链状态
    rpc GetUpChainStatusBySpuId(GetUpChainStatusBySpuIdReq) returns (GetUpChainStatusByActIdResp);
    // 查询用户拥有的集卡活动资产信息
    rpc ListUserBannerCardGroupByAct(ListUserBannerCardGroupByActReq) returns (ListUserBannerCardGroupByActResp);

    // -------------- 交易相关接口 --------------
    // 支付预检
    rpc PayPreCheck(live.order.CommonPreCheckReq) returns (live.order.CommonPreCheckResp);
    // 支付回调
    rpc PayDelivery(live.order.CommonDeliveryReq) returns (live.order.CommonDeliveryResp);

    // 支付预检 大会员交易
    rpc CommonPreCheck(open.order.service.v1.CommonPreCheckCallbackReq) returns (open.order.service.v1.CommonPreCheckCallbackResp);
    // 支付回调发货 大会员交易
    rpc CommonPayDelivery(open.order.service.v1.CommonDeliveryCallbackReq) returns (open.order.service.v1.CommonDeliveryCallbackResp);
    // 支付面板
    rpc PayPanel(PayPanelReq) returns (PayPanelResp);

    // 是否需要实名验证
    rpc GetNotNeedRealName(GetNotNeedRealNameReq) returns (GetNotNeedRealNameResp);

    rpc UserParticipatedItem(UserParticipatedItemReq) returns (UserParticipatedItemResp);
    // 根据装扮内容 列出相关活动列表
    rpc GarbItemActList(GarbItemActListReq) returns (GarbItemActListResp);
    // 查询收藏馆资产数据
    rpc ListCardAssetForCollection(ListCardAssetForCollectionReq) returns (ListCardAssetForCollectionResp);
    // 查询闪屏资产
    rpc ListCardAssetForSplash(ListCardAssetForSplashReq) returns (ListCardAssetForSplashResp);
    // 查询小组件资产
    rpc ListCardAssetForWidget(ListCardAssetForWidgetReq) returns (ListCardAssetForWidgetResp);
    // 查询装扮在哪些收集度奖励中
    rpc GarbItemCollectionRedeemList(GarbItemCollectionRedeemListReq) returns (GarbItemCollectionRedeemListResp);
    // 获取集卡活动总数 默认包含新老集卡活动、已结束+状态上线的活动
    rpc CardActTotal(CardActTotalReq) returns (CardActTotalResp);

    // 用户权益页卡片列表
    rpc DlcUserCardList(DlcUserCardListReq) returns (DlcUserCardListReply);
    rpc DlcUserCardListStream(DlcUserCardListReq) returns (stream DlcUserCardListStreamReply);
    // 用户权益页v2
    rpc DlcUserRightLanding(DlcUserRightLandingReq) returns (DlcUserRightLandingResp);
    // 被访问者活动信息&勋章编号&收集度&拥有卡牌
    rpc DlcUserCardLanding(DlcUserCardLandingReq) returns (DlcUserCardLandingResp);
    // 用户某个活动下持有的装扮名称和类型
    rpc DlcUserGarbAsset(DlcUserAssetsReq) returns (DlcUserAssetsReply);

    // dlc资产装备
    rpc DlcUserEquipLoad(DlcUserEquipLoadReq) returns (DlcUserEquipLoadReply);

    // dlc用户评论背景
    rpc DlcActUserCommentBg(DlcActUserCommentBgReq) returns (DlcActUserCommentBgReply);
    // dlc用户是否拥有装扮权益
    rpc DlcUserRightHasGarb(DlcUserRightHasGarbReq) returns (DlcUserRightHasGarbReply);

    // dlc奖品用户列表下载（csv格式，仅实物）
    rpc DlcActRewardUserList(DlcActRewardUserListReq) returns (DlcActRewardUserListReply);
    // 话题发布鉴权
    rpc TopicRight(TopicRightReq) returns (TopicRightResp);
    // 空间头部话题列表
    rpc TopicList(TopicListReq) returns (TopicListResp);
    // 资源库存
    rpc ItemResources(ItemResourcesReq) returns (ItemResourcesResp);
    // 素材根据绑定类型+id获取活动id
    rpc GetActIdByMaterialBinding(GetActIdByMaterialBindingReq) returns (GetActIdByMaterialBindingResp);
    // dlc活动+奖池信息,返回参数按需增加
    rpc DLCActInfos(DLCActInfosReq) returns (DLCActInfosResp);

    // DLC抽奖记录对账check和补偿
    rpc CheckDrawLogAndCompensate(CheckDrawLogAndCompensateReq) returns (FCommonResp);

    // VipCardRightsInfo 大会员中心卡片权益信息
    rpc VipCardRightsInfo(VipCardRightsInfoReq) returns (VipCardRightsInfoReply);

    // DLC活动资产背包
    rpc DLCAssetBag(DLCAssetBagReq) returns (DLCAssetBagResp);
    rpc DLCAssetBagV2(DLCAssetBagV2Req) returns (DLCAssetBagV2Resp);
    rpc UserAssetBagList(UserAssetBagListReq) returns (UserAssetBagListResp);

    // DLC活动资产背包 stream
    rpc DLCAssetBagStream(DLCAssetBagReq) returns (stream DLCAssetBagStreamResp);

    // DLC卡牌权益使用列表 端上按钮点击
    rpc DlcCardRightUsage(DlcCardRightUsageReq) returns (DlcCardRightUsageResp);

    // 查询活动下所有卡牌信息
    rpc GetActLotteryInfo(GetActLotteryInfoReq) returns (GetActLotteryInfoResp);
    // 查询用户在多个活动/卡池下的抽数
    rpc UserDrawCounts(UserDrawCountsReq) returns (UserDrawCountsResp);

    // 查询用户获取的所有自定义收集度奖励
    rpc UserAllCollectRedeemDiy(UserAllCollectRedeemDiyReq) returns (UserAllCollectRedeemDiyReply);
    // 批量查询用户自定义奖励的领取结果
    rpc UserCollectRedeemDiyOption(UserCollectRedeemDiyOptionReq) returns (UserCollectRedeemDiyOptionResp);
    // 获自定义奖励的所有可选项
    rpc CollectRedeemDiyOptDetail(CollectRedeemDiyOptDetailReq) returns (CollectRedeemDiyOptDetailResp);

    // 用户选择自定义收集度奖励
    rpc UserCollectRedeemDiyDraw(UserCollectRedeemDiyDrawReq) returns (UserCollectRedeemDiyDrawReply);

    // 根据收集度奖励id查询，收集度任务简介
    rpc GetCollectRedeemByIds(GetCollectRedeemReq) returns (GetCollectRedeemResp);

    // 通过收藏集ID获取收藏集下的收集度奖励
    rpc ActCollectInfo(ActCollectInfoReq) returns (ActCollectInfoResp);

    // 自定义收集度奖励的信息
    rpc ActCollectDiyInfo(ActCollectDiyInfoReq) returns (ActCollectDiyInfoReply);

    // 获取某个活动下所有奖池的销量（如果获取单奖池，可扩展字段）
    rpc ActLotterySales(ActLotterySalesReq) returns (ActLotterySalesReply);

    // 查询一个uid拥有的所有dlc的spuids
    rpc GetUserSpuIds(GetUserSpuIdsReq) returns (GetUserSpuIdsResp);

    // C端展示的实体兑换配置,获取收藏集下当前应该展示的一个实体兑换
    rpc PhysicalEntityExchangeToC(PhysicalEntityExchangeToCReq) returns (PhysicalEntityExchangeToCResp);
    // 用户应该拥有的勋章装扮资产数据
    rpc UserOwnedActMedalInfos(UserOwnedActMedalInfosReq) returns (UserOwnedActMedalInfosResp);
    // C端展示的实体兑换配置，获取指定的一个实体兑换并返回指定奖池相关信息
    rpc PhysicalEntityExchangeWithPool(PhysicalEntityExchangeWithPoolReq) returns (PhysicalEntityExchangeWithPoolResp);
    // 活动下所有实体兑换的简单参数：第一期只用于删缓存拼key
    rpc PhysicalEntitySimpleConfInAct(PhysicalEntitySimpleConfInActReq) returns (PhysicalEntitySimpleConfInActResp);
    rpc MedalInfoByCardId(MedalInfoByCardIdReq) returns (MedalInfoByCardIdResp);

    // 抽卡记录
    rpc DrawRecord(DrawRecordReq) returns (DrawRecordResp);
    //    // dlc 列表  todo 废弃接口
    //    rpc DlcActList(DlcActListReq) returns (DlcActListResp);
    // dlc 列表
    rpc DlcActToCList(DlcActToCListReq) returns (DlcActToCListResp);
    // 根据关联Mid取收藏集信息
    rpc DLCActInfosByRelatedMid(DLCActInfosByRelatedMidReq) returns (DLCActInfosByRelatedMidResp);
    rpc CollectRedeemInfos(CollectRedeemInfosReq) returns (CollectRedeemInfosResp);

    // ----裂变活动用-----
    // 完成预约任务 并返回是否触发幂等
    rpc BookLotteryTask(BookLotteryTaskReq) returns (BookLotteryTaskResp);
    // 获取奖池信息
    rpc FissionGetLotteryInfo(FissionGetLotteryInfoReq) returns (FissionGetLotteryInfoResp);
    // 获取抽数对应奖励配置
    rpc FissionRewardByDrawChance(FissionRewardByDrawChanceReq) returns (FissionRewardByDrawChanceResp);

    // dlc支付跳转检查
    rpc DlcPaymentPreCheck(DlcPaymentPreCheckReq) returns (DlcPaymentPreCheckReply);
    // DLC挽留页面数据
    rpc DLCRetentionPage(DLCRetentionPageReq) returns (DLCRetentionPageResp);
    // 根据卡牌id拉取闪屏资源
    rpc SplashInfoByCardTypeIds(SplashInfoByCardTypeIdsReq) returns (SplashInfoByCardTypeIdsResp);
    // 收集度奖励合成动图任务创建
    rpc CollectCompoundVideoTaskAdd(CollectCompoundVideoTaskAddReq) returns (CollectCompoundVideoTaskAddResp);
    // 删除支付缓存
    rpc DelCachePaymentCashHit(DelCachePaymentCashHitReq) returns (DelCachePaymentCashHitReply);
    // 根据卡牌id拉取小组件资源
    rpc WidgetInfoByCardTypeIds(WidgetInfoByCardTypeIdsReq) returns (WidgetInfoByCardTypeIdsResp);
    rpc EchoBindBackground(EchoBindBackgroundReq) returns (EchoBindBackgroundResp);
    rpc EchoBackgroundList(EchoBackgroundListReq) returns (EchoBackgroundListResp);
    rpc EchoBackgroundInfo(EchoBackgroundInfoReq) returns (EchoBackgroundInfoResp);

    // ----抽卡机-----
    // 抽卡机数据
    rpc DLCCardMachineInfo(DLCCardMachineInfoReq) returns (DLCCardMachineInfoResp);
    rpc CardDecomposeStateInfo(CardDecomposeStateInfoReq) returns (CardDecomposeStateInfoResp);

    // 榜单奖励结果公示
    rpc DLCRankRedeemResult(DLCRankRedeemResultReq) returns (DLCRankRedeemResultResp);

    // 推荐收藏集到ogv推荐位
    rpc RecommendOgv(RecommendOgvReq) returns (RecommendOgvResp);
}
message RecommendOgvReq {
    string season_id = 1 [(gogoproto.jsontag) = "season_id", (gogoproto.moretags) = 'form:"season_id"', json_name = "season_id"];
}
message RecommendOgvResp {
    // 推荐数据
    repeated Data list = 1;
    message Data {
        // 收藏集名称
        string name = 1;
        // 收藏集id
        int64 collection_id = 2;
        // 卡池id
        int64 card_id = 3;
        // 相关推荐-跳转收藏集url
        string load_url = 4;
        // 相关推荐-封面url
        string cover_url = 5;
        // 相关推荐-主标题
        string title = 6;
        // 相关推荐-副标题
        string subtitle = 7;
    }
}

message PayPanelReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}

message PayPanelResp {
    // 活动信息
    ActInfo act_info = 1 [(gogoproto.moretags) = 'form:"act_info"', (gogoproto.jsontag) = 'act_info', json_name = 'act_info'];

    message ActInfo {
        // 活动标题
        string act_title = 1 [(gogoproto.jsontag) = "act_title", (gogoproto.moretags) = 'form:"act_title"', json_name = "act_title"];
        // 系统当前时间戳
        int64 cur_time = 2 [(gogoproto.jsontag) = "cur_time", (gogoproto.moretags) = 'form:"cur_time"', json_name = "cur_time"];
        // 活动开始时间戳
        int64 start_time = 3 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
        // 活动结束时间戳
        int64 end_time = 4 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
        // 是否上链，1=是，0=否
        int64 is_up_chain = 5 [(gogoproto.jsontag) = "is_up_chain", (gogoproto.moretags) = 'form:"is_up_chain"', json_name = "is_up_chain"];
        // 商品spu_id
        int64 spu_id = 6 [(gogoproto.jsontag) = "spu_id", (gogoproto.moretags) = 'form:"spu_id"', json_name = "spu_id"];
        // 奖池信息
        repeated LotteryInfo lottery_list = 7 [(gogoproto.jsontag) = "lottery_list", (gogoproto.moretags) = 'form:"lottery_list"', json_name = "lottery_list"];

        message LotteryInfo {
            // 奖池id
            int64 lottery_id = 1 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
            // 奖池名称
            string lottery_name = 2 [(gogoproto.jsontag) = "lottery_name", (gogoproto.moretags) = 'form:"lottery_name"', json_name = "lottery_name"];
            // 奖池开始时间戳
            int64 start_time = 3 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
            // 奖池结束时间戳
            int64 end_time = 4 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
            // 奖池图片
            string lottery_image = 5 [(gogoproto.jsontag) = "lottery_image", (gogoproto.moretags) = 'form:"lottery_image"', json_name = "lottery_image"];
            // 商品id
            int64 sku_id = 6 [(gogoproto.jsontag) = "sku_id", (gogoproto.moretags) = 'form:"sku_id"', json_name = "sku_id"];
            // 购买单价(厘)
            int64 price = 7 [(gogoproto.jsontag) = "price", (gogoproto.moretags) = 'form:"price"', json_name = "price"];
            // 奖池类型，1 常驻奖池  2 纪念奖池
            int64 lottery_type = 8 [(gogoproto.jsontag) = "lottery_type", (gogoproto.moretags) = 'form:"lottery_type"', json_name = "lottery_type"];
            // 是否永久生效 0-否，1-是
            int64 effective_forever = 9 [(gogoproto.jsontag) = "effective_forever", (gogoproto.moretags) = 'form:"effective_forever"', json_name = "effective_forever"];
            // 是否免费 0-否，1-是
            int64 free = 10 [(gogoproto.jsontag) = "free", (gogoproto.moretags) = 'form:"free"', json_name = "free"];
        }
    }
}

message ActCollectInfoReq {
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
}
message ActCollectInfoResp {
    repeated CollectRedeem list = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}
message CollectRedeem {
    int64 collect_id = 1 [(gogoproto.jsontag) = "collect_id", (gogoproto.moretags) = 'form:"collect_id" validate:"required"', json_name = "collect_id"];
}

message GetCollectRedeemReq {
    repeated int64 collect_ids = 1 [(gogoproto.jsontag) = "collect_ids", (gogoproto.moretags) = 'form:"collect_ids" validate:"required"', json_name = "collect_ids"];
}
message GetCollectRedeemResp {
    map<int64, CollectRedeemProf> infos = 1 [(gogoproto.jsontag) = "infos", (gogoproto.moretags) = 'form:"infos" validate:"required"', json_name = "infos"];
}

message CollectRedeemProf {
    // 收集度奖励标题
    string redeem_item_name = 1 [(gogoproto.moretags) = 'form:"redeem_item_name"', (gogoproto.jsontag) = 'redeem_item_name', json_name = 'redeem_item_name'];
    // 简介
    string redeem_text = 2 [(gogoproto.moretags) = 'form:"redeem_text"', (gogoproto.jsontag) = 'redeem_text', json_name = 'redeem_text'];
    // 封面
    string redeem_item_image = 3 [(gogoproto.moretags) = 'form:"redeem_item_image"', (gogoproto.jsontag) = 'redeem_item_image', json_name = 'redeem_item_image'];
    // 所属收藏集id
    int64 act_id = 4 [(gogoproto.moretags) = 'form:"act_id"', (gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
    // 收藏集name
    string act_name = 5 [(gogoproto.moretags) = 'form:"act_name"', (gogoproto.jsontag) = 'act_name', json_name = 'act_name'];
    // 收藏集封面图
    string act_img = 6 [(gogoproto.moretags) = 'form:"act_img"', (gogoproto.jsontag) = 'act_img', json_name = 'act_img'];
}

message BookLotteryTaskReq {
    // 用户ID
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"', json_name = "mid"];
    // 活动ID
    int64 lottery_id = 2 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 0只看状态不执行预约 1完成预约操作
    bool operate = 3 [(gogoproto.jsontag) = "operate", (gogoproto.moretags) = 'form:"operate"', json_name = "operate"];
}
message BookLotteryTaskResp {
    // 是否命中幂等
    bool hit_idempotent = 1 [(gogoproto.jsontag) = "hit_idempotent", (gogoproto.moretags) = 'form:"hit_idempotent"', json_name = "hit_idempotent"];
    CommonExecResp common_resp = 2 [(gogoproto.jsontag) = "common_resp", (gogoproto.moretags) = 'form:"common_resp"', json_name = "common_resp"];
}

message FissionGetLotteryInfoReq {
    // 奖池id
    int64 lottery_id = 1 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
}

message FissionGetLotteryInfoResp {
    // 外显时间
    int64 display_start_time = 1 [(gogoproto.jsontag) = "display_start_time", (gogoproto.moretags) = 'form:"display_start_time"', json_name = "display_start_time"];
    // 开售时间
    int64 sale_start_time = 2 [(gogoproto.jsontag) = "sale_start_time", (gogoproto.moretags) = 'form:"sale_start_time"', json_name = "sale_start_time"];
    // 活动名称
    int64 act_id = 3 [(gogoproto.moretags) = 'form:"act_id"', (gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
    // 活动名称
    string act_name = 4 [(gogoproto.moretags) = 'form:"act_name"', (gogoproto.jsontag) = 'act_name', json_name = 'act_name'];
    // 奖池名称
    string lottery_name = 5 [(gogoproto.moretags) = 'form:"lottery_name"', (gogoproto.jsontag) = 'lottery_name', json_name = 'lottery_name'];
    // 奖池封面图
    string lottery_image = 6 [(gogoproto.moretags) = 'form:"lottery_image"', (gogoproto.jsontag) = 'lottery_image', json_name = 'lottery_image'];
    // 分享主标题、子标题、文案
    ShareInfo share_info = 7 [(gogoproto.jsontag) = "share_info", (gogoproto.moretags) = 'form:"share_info"', json_name = "share_info"];
    // 分享信息
    message ShareInfo {
        // 分享主标题
        string main_title = 1 [(gogoproto.jsontag) = "main_title", (gogoproto.moretags) = 'form:"main_title"', json_name = "main_title"];
        // 分享子标题
        string sub_title = 2 [(gogoproto.jsontag) = "sub_title", (gogoproto.moretags) = 'form:"sub_title"', json_name = "sub_title"];
        // 分享文案
        string share_content = 3 [(gogoproto.jsontag) = "share_content", (gogoproto.moretags) = 'form:"share_content"', json_name = "share_content"];
    }
    // 奖池状态（埋点用） 0-预约期 1-售卖期 2-结束
    int64 lottery_status = 8 [(gogoproto.moretags) = 'form:"lottery_status"', (gogoproto.jsontag) = 'lottery_status', json_name = 'lottery_status'];
}

message FissionRewardByDrawChanceReq {
    // 奖池id
    int64 lottery_id = 1 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 抽数
    int64 draw_count = 2 [(gogoproto.jsontag) = "draw_count", (gogoproto.moretags) = 'form:"draw_count"', json_name = "draw_count"];
}

message FissionRewardByDrawChanceResp {
    // 奖品
    RewardItem reward = 3 [(gogoproto.moretags) = 'form:"reward"', (gogoproto.jsontag) = 'reward', json_name = 'reward'];
    // 额外奖励
    repeated RewardItem extra_reward = 4 [(gogoproto.moretags) = 'form:"extra_reward"', (gogoproto.jsontag) = 'extra_reward', json_name = 'extra_reward'];
    message RewardItem {
        // 奖励图
        string image = 1 [(gogoproto.moretags) = 'form:"image"', (gogoproto.jsontag) = 'image', json_name = 'image'];
        // 奖励名称
        string name = 2 [(gogoproto.moretags) = 'form:"name"', (gogoproto.jsontag) = 'name', json_name = 'name'];
    }
}

message DlcActToCListReq {
    //
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 场景 1:dlc列表 2:优惠券使用
    int64 scene = 2 [(gogoproto.jsontag) = "scene", (gogoproto.moretags) = 'form:"scene"', json_name = "scene"];
    // 通兑券 token
    string coupon_token = 3 [(gogoproto.jsontag) = "coupon_token", (gogoproto.moretags) = 'form:"coupon_token"', json_name = "coupon_token"];
    // 位置
    int64 site = 4 [(gogoproto.jsontag) = "site", (gogoproto.moretags) = 'form:"site"', json_name = "site"];
}

message DlcActToCListResp {
    // 列表
    repeated Item list = 1 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
    // 是否有更多
    bool is_more = 2 [(gogoproto.jsontag) = "is_more", (gogoproto.moretags) = 'form:"is_more"', json_name = "is_more"];
    // 位置
    int64 site = 3 [(gogoproto.jsontag) = "site", (gogoproto.moretags) = 'form:"site"', json_name = "site"];
    message Item {
        // dlc 活动id
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 收藏集名称
        string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
        // 活动图片-背景图
        string act_pic = 3 [(gogoproto.jsontag) = "act_pic", (gogoproto.moretags) = 'form:"act_pic"', json_name = "act_pic"];
        // 售价
        int64 sale_price = 4 [(gogoproto.jsontag) = "sale_price", (gogoproto.moretags) = 'form:"sale_price"', json_name = "sale_price"];
        // 活动描述
        string act_desc = 5 [(gogoproto.jsontag) = "act_desc", (gogoproto.moretags) = 'form:"act_desc"', json_name = "act_desc"];
        // 活动标签
        string tag = 6 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
        // 卡池id
        int64 lottery_id = 7 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
        // 卡池类型 1-常驻奖池 2-限定奖池
        int64 lottery_type = 8 [(gogoproto.jsontag) = "lottery_type", (gogoproto.moretags) = 'form:"lottery_type"', json_name = "lottery_type"];
        // 活动跳转链接
        string act_link = 9 [(gogoproto.jsontag) = "act_link", (gogoproto.moretags) = 'form:"act_link"', json_name = "act_link"];
    }
}

// message DlcActListReq {
//     //
//     int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
//     // 场景 1:dlc列表 2:优惠券使用
//     int64 scene = 2 [(gogoproto.jsontag) = "scene", (gogoproto.moretags) = 'form:"scene"', json_name = "scene"];
//     // 通兑券 token
//     string coupon_token = 3 [(gogoproto.jsontag) = "coupon_token", (gogoproto.moretags) = 'form:"coupon_token"', json_name = "coupon_token"];
//     // 位置
//     int64 site = 4 [(gogoproto.jsontag) = "site", (gogoproto.moretags) = 'form:"site"', json_name = "site"];
// }
//
// message DlcActListResp {
//     // 列表
//     repeated Item list = 1 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
//     // 是否有更多
//     bool is_more = 2 [(gogoproto.jsontag) = "is_more", (gogoproto.moretags) = 'form:"is_more"', json_name = "is_more"];
//     // 位置
//     int64 site = 3 [(gogoproto.jsontag) = "site", (gogoproto.moretags) = 'form:"site"', json_name = "site"];
//     message Item {
//         // dlc 活动id
//         int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
//         // 收藏集名称
//         string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
//         // 活动图片-背景图
//         string act_pic = 3 [(gogoproto.jsontag) = "act_pic", (gogoproto.moretags) = 'form:"act_pic"', json_name = "act_pic"];
//         // 售价
//         int64 sale_price = 4 [(gogoproto.jsontag) = "sale_price", (gogoproto.moretags) = 'form:"sale_price"', json_name = "sale_price"];
//         // 活动描述
//         string act_desc = 5 [(gogoproto.jsontag) = "act_desc", (gogoproto.moretags) = 'form:"act_desc"', json_name = "act_desc"];
//         // 活动标签
//         string tag = 6 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
//         // 卡池id
//         int64 lottery_id = 7 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
//         // 卡池类型 1-常驻奖池 2-限定奖池
//         int64 lottery_type = 8 [(gogoproto.jsontag) = "lottery_type", (gogoproto.moretags) = 'form:"lottery_type"', json_name = "lottery_type"];
//         // 活动跳转链接
//         string act_link = 9 [(gogoproto.jsontag) = "act_link", (gogoproto.moretags) = 'form:"act_link"', json_name = "act_link"];
//     }
// }

message DrawRecordReq {
    // 抽取者mid
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 抽取者mid
    int64 act_id = 2 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 抽取者mid
    int64 pool_id = 3 [(gogoproto.jsontag) = "pool_id", (gogoproto.moretags) = 'form:"pool_id"', json_name = "pool_id"];
    // 上次返回的最后一条记录的抽卡时间戳:只用来表示标记最后一条数据来自哪个表
    int64 last_record_time = 4 [(gogoproto.jsontag) = "last_record_time", (gogoproto.moretags) = 'form:"last_record_time"', json_name = "last_record_time"];
    // 上次返回的最后一条记录的id
    int64 last_record_id = 5 [(gogoproto.jsontag) = "last_record_id", (gogoproto.moretags) = 'form:"last_record_id"', json_name = "last_record_id"];
    // page size 默认10
    int64 ps = 6 [(gogoproto.jsontag) = "ps", (gogoproto.moretags) = 'form:"ps"', json_name = "ps"];
}

message DrawRecordResp {
    // 列表
    repeated vas.nft.DLCDrawRecordItem list = 1 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
    // 本次返回的最后一条记录的抽卡时间戳:只用来表示标记最后一条数据来自哪个表，没有别的任何意义
    int64 last_record_time = 2 [(gogoproto.jsontag) = "last_record_time", (gogoproto.moretags) = 'form:"last_record_time"', json_name = "last_record_time"];
    // 上次返回的最后一条记录的id
    int64 last_record_id = 3 [(gogoproto.jsontag) = "last_record_id", (gogoproto.moretags) = 'form:"last_record_id"', json_name = "last_record_id"];
    // 是否还有下一页
    int64 has_more = 4 [(gogoproto.jsontag) = "has_more", (gogoproto.moretags) = 'form:"has_more"', json_name = "has_more"];
}

message GetUserSpuIdsReq {
    int64 uid = 1 [(gogoproto.jsontag) = "uid", (gogoproto.moretags) = 'form:"uid"', json_name = "uid"];
    int64 is_up_chain = 2 [(gogoproto.jsontag) = "is_up_chain", (gogoproto.moretags) = 'form:"is_up_chain"', json_name = "is_up_chain"];
}

message GetUserSpuIdsResp {
    repeated int64 spu_ids = 1 [(gogoproto.jsontag) = "spu_ids", (gogoproto.moretags) = 'form:"spu_ids"', json_name = "spu_ids"];
}

message CardActTotalReq {}

message CardActTotalResp {
    int64 total = 1 [(gogoproto.jsontag) = "total", (gogoproto.moretags) = 'form:"total"', json_name = "total"];
}

message GarbItemCollectionRedeemListReq {
    string garb_item_id = 1 [(gogoproto.jsontag) = "garb_item_id", (gogoproto.moretags) = 'form:"garb_item_id"', json_name = "garb_item_id"];
}

message GarbItemCollectionRedeemListResp {
    repeated int64 collection_id_list = 1 [(gogoproto.jsontag) = "collection_id_list", (gogoproto.moretags) = 'form:"collection_id_list"', json_name = "collection_id_list"];
}

message ListCardAssetForCollectionReq {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    int64 vmid = 2 [(gogoproto.jsontag) = "vmid", (gogoproto.moretags) = 'form:"vmid"', json_name = "vmid"];
    // item_id 在 level = 2 时才有意义
    repeated vas.common.CollectionBase items = 3 [(gogoproto.jsontag) = "items", (gogoproto.moretags) = 'form:"mid"', json_name = "items"];
    vas.common.CollectionInfoLevel level = 4 [(gogoproto.jsontag) = "level", (gogoproto.moretags) = 'form:"level"', json_name = "level"];
    // level = 3 时该字段有意义，若 last_item_id 非空 只会取items数据的第一条
    vas.common.ItemPage page = 5 [(gogoproto.jsontag) = "page", (gogoproto.moretags) = 'form:"page"', json_name = "page"];
    string source = 6 [(gogoproto.jsontag) = "source", (gogoproto.moretags) = 'form:"source"', json_name = "source"];
    // 设备信息
    DeviceInfo device = 7 [(gogoproto.jsontag) = "device", (gogoproto.moretags) = 'form:"device"', json_name = "device"];
}

message ListCardAssetForCollectionResp {
    repeated vas.common.Collection result = 1 [(gogoproto.jsontag) = "result", (gogoproto.moretags) = 'form:"result"', json_name = "result"];
}

message GarbItemActListReq {
    // 装扮列表id
    repeated string garb_item_id_list = 1 [(gogoproto.jsontag) = "garb_item_id_list", (gogoproto.moretags) = 'form:"garb_item_id_list"', json_name = "garb_item_id_list"];
}

message GarbItemActListResp {
    repeated Item item_list = 1 [(gogoproto.jsontag) = "item_list", (gogoproto.moretags) = 'form:"item_list"', json_name = "item_list"];
    message Item {
        // 装扮id
        string garb_item_id = 1 [(gogoproto.jsontag) = "garb_item_id", (gogoproto.moretags) = 'form:"garb_item_id"', json_name = "garb_item_id"];
        // 活动id列表
        repeated ActItem act_item_list = 2 [(gogoproto.jsontag) = "act_item_list", (gogoproto.moretags) = 'form:"act_item_list"', json_name = "act_item_list"];
    }
    message ActItem {
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 是否在收集度奖励中 0-否 1-是
        int64 is_collect = 2 [(gogoproto.jsontag) = "is_collect", (gogoproto.moretags) = 'form:"is_collect"', json_name = "is_collect"];
    }
}

message ActMedalInfoListReq {
    // 活动id 不传 or 为0 筛选全部
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}

message ActMedalInfoListResp {
    repeated ActMedalInfo medal_info_list = 1 [(gogoproto.jsontag) = "medal_info_list", (gogoproto.moretags) = 'form:"medal_info_list"', json_name = "medal_info_list"];
    message ActMedalInfo {
        // 活动id
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 等级门槛-卡牌数
        repeated int64 own_card_cnt_level = 2 [(gogoproto.jsontag) = "own_card_cnt_level", (gogoproto.moretags) = 'form:"own_card_cnt_level"', json_name = "own_card_cnt_level"];
        // 等级积分 示例：[1,10,40,120,250]
        repeated int64 score_level = 3 [(gogoproto.jsontag) = "score_level", (gogoproto.moretags) = 'form:"score_level"', json_name = "score_level"];
        // 勋章图  第一位表示活动，第二位表示等级，第三位表示场景
        // 示例： 14_1_1 =
        // "https://i0.hdslb.com/bfs/garb/item/bb95a716723fa17354aa18ae10323903747c79ec.png"
        map<string, string> medal_scene_image = 4 [(gogoproto.jsontag) = "medal_scene_image", (gogoproto.moretags) = 'form:"medal_scene_image"', json_name = "medal_scene_image"];
        // 编号色值  1："#C79085"
        map<string, string> level_number_color = 5 [(gogoproto.jsontag) = "level_number_color", (gogoproto.moretags) = 'form:"level_number_color"', json_name = "level_number_color"];
        // 老装扮列表后台评论背景映射id
        int64 garb_item_base_id = 6 [(gogoproto.jsontag) = "garb_item_base_id", (gogoproto.moretags) = 'form:"garb_item_base_id"', json_name = "garb_item_base_id"];
        // 老装扮列表后台动态卡片映射id
        int64 garb_dc_item_base_id = 7 [(gogoproto.jsontag) = "garb_dc_item_base_id", (gogoproto.moretags) = 'form:"garb_dc_item_base_id"', json_name = "garb_dc_item_base_id"];
    }
}

// 执行接口公共返回
message CommonExecResp {
    // 错误码 0表示成功 其他表示失败
    int64 err_code = 1 [(gogoproto.jsontag) = "err_code", (gogoproto.moretags) = 'form:"err_code"', json_name = "err_code"];
    // 错误信息，失败情况下返回
    string err_message = 2 [(gogoproto.jsontag) = "err_message", (gogoproto.moretags) = 'form:"err_message"', json_name = "err_message"];
}

message CollectRedeemReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 用户ID
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 要领取的收集奖励id
    int64 collect_id = 3 [(gogoproto.jsontag) = "collect_id", (gogoproto.moretags) = 'form:"collect_id"', json_name = "collect_id"];
    // ip
    string ip = 5 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
    // user_agent
    string user_agent = 6 [(gogoproto.jsontag) = "user_agent", (gogoproto.moretags) = 'form:"user_agent"', json_name = "user_agent"];
    // 收货地址
    ExpressInfo express_info = 7 [(gogoproto.jsontag) = "express_info", (gogoproto.moretags) = 'form:"express_info"', json_name = "express_info"];
    // 自定义奖品选择
    CollectDiyInfo diy_info = 8 [(gogoproto.jsontag) = "diy_info", (gogoproto.moretags) = 'form:"diy_info"', json_name = "diy_info"];
}

message CollectRedeemResp {
    // 领取的收集度奖励id
    int64 collect_id = 1 [(gogoproto.jsontag) = "collect_id", (gogoproto.moretags) = 'form:"collect_id"', json_name = "collect_id"];
    // 收集奖励的物品类型 1:数字周边卡牌 2：表情包， 3：头像挂件， 4：装扮永久套装，7：观影券 8: cdk 9：实物奖励 10:限时装扮散件
    int64 redeem_item_type = 3 [(gogoproto.jsontag) = "redeem_item_type", (gogoproto.moretags) = 'form:"redeem_item_type"', json_name = "redeem_item_type"];
    // 收集奖励的物品id
    string redeem_item_id = 4 [(gogoproto.jsontag) = "redeem_item_id", (gogoproto.moretags) = 'form:"redeem_item_id"', json_name = "redeem_item_id"];
    // 收集奖励的物品名称
    string redeem_item_name = 5 [(gogoproto.jsontag) = "redeem_item_name", (gogoproto.moretags) = 'form:"redeem_item_name"', json_name = "redeem_item_name"];
    // 收集奖励的物品图片
    string redeem_item_image = 6 [(gogoproto.jsontag) = "redeem_item_image", (gogoproto.moretags) = 'form:"redeem_item_image"', json_name = "redeem_item_image"];
    // 收集奖励的卡牌详情
    CardItemInfo card_item = 7 [(gogoproto.jsontag) = "card_item", (gogoproto.moretags) = 'form:"card_item"', json_name = "card_item"];
    // 收集奖励的物品图片下载链接
    string redeem_item_image_download = 8 [(gogoproto.jsontag) = "redeem_item_image_download", (gogoproto.moretags) = 'form:"redeem_item_image_download"', json_name = "redeem_item_image_download"];
    // 道具跳转链接
    string jump_url = 9 [(gogoproto.jsontag) = "jump_url", (gogoproto.moretags) = 'form:"jump_url"', json_name = "jump_url"];
    message CardItemInfo {
        // 收集奖励领取的卡牌信息
        nft.CardTypeInfo card_type_info = 1 [(gogoproto.jsontag) = "card_type_info", (gogoproto.moretags) = 'form:"card_type_info"', json_name = "card_type_info"];
        // 卡牌概率，百分比，保留两位小数
        double card_chance = 2 [(gogoproto.jsontag) = "card_chance", (gogoproto.moretags) = 'form:"card_chance"', json_name = "card_chance"];
        // 卡片信息
        DrawCardInfo card_info = 3 [(gogoproto.jsontag) = "card_info", (gogoproto.moretags) = 'form:"card_info"', json_name = "card_info"];
        // 总发行量
        int64 card_type_cnt = 4 [(gogoproto.jsontag) = "card_type_cnt", (gogoproto.moretags) = 'form:"card_type_cnt"', json_name = "card_type_cnt"];
        // 总发行量展示
        string card_type_cnt_show = 5 [(gogoproto.jsontag) = "card_type_cnt_show", (gogoproto.moretags) = 'form:"card_type_cnt_show"', json_name = "card_type_cnt_show"];
        message DrawCardInfo {
            // 卡片id
            int64 card_id = 1 [(gogoproto.jsontag) = "card_id", (gogoproto.moretags) = 'form:"card_id"', json_name = "card_id"];
            // 卡片编号(补齐6位)
            string card_no = 2 [(gogoproto.jsontag) = "card_no", (gogoproto.moretags) = 'form:"card_no"', json_name = "card_no"];
            // hash值 (拿不到，字段废弃)
            string hash_code = 3 [(gogoproto.jsontag) = "hash_code", (gogoproto.moretags) = 'form:"hash_code"', json_name = "hash_code"];
            // 卡片权益
            CardRight card_right = 4 [(gogoproto.jsontag) = "card_right", (gogoproto.moretags) = 'form:"card_right"', json_name = "card_right"];
            // 卡片权益展示(前端专用)
            CardRightShow card_right_show = 5 [(gogoproto.jsontag) = "card_right_show", (gogoproto.moretags) = 'form:"card_right_show"', json_name = "card_right_show"];
            // 卡牌描述文本，如：DLC圣诞限定款
            string card_ext_text = 6 [(gogoproto.jsontag) = "card_ext_text", (gogoproto.moretags) = 'form:"card_ext_text"', json_name = "card_ext_text"];
        }
    }
    message GarbItemInfo {
        // 当发放粉丝套装时，生成的粉丝编号
        repeated int64 fan_num = 1 [(gogoproto.jsontag) = "fan_num", (gogoproto.moretags) = 'form:"fan_num"', json_name = "fan_num"];
    }
}

message LotteryDetailReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 用户ID
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 客户端类型 (android、ios、androidHD、iosHD、其他)
    string platform = 3 [(gogoproto.jsontag) = "platform", (gogoproto.moretags) = 'form:"platform"', json_name = "platform"];
    // 客户端build号
    int64 build_id = 4 [(gogoproto.jsontag) = "build_id", (gogoproto.moretags) = 'form:"build_id"', json_name = "build_id"];
    // ip
    string ip = 5 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
    // user_agent
    string user_agent = 6 [(gogoproto.jsontag) = "user_agent", (gogoproto.moretags) = 'form:"user_agent"', json_name = "user_agent"];
    // 奖池id
    int64 lottery_id = 7 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
}

message LotteryDetailResp {
    int64 lottery_id = 1 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 奖池名称
    string lottery_name = 2 [(gogoproto.jsontag) = "name", (gogoproto.moretags) = 'form:"name"', json_name = "name"];
    // 开始展示时间
    int64 display_time = 3 [(gogoproto.jsontag) = "display_time", (gogoproto.moretags) = 'form:"display_time"', json_name = "display_time"];
    // 轮次开始时间戳
    int64 start_time = 4 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
    // 轮次结束时间戳
    int64 end_time = 5 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
    // 本奖池剩余抽奖次数
    int64 draw_left_cnt = 8 [(gogoproto.jsontag) = "draw_left_cnt", (gogoproto.moretags) = 'form:"draw_left_cnt"', json_name = "draw_left_cnt"];
    // 商品id
    int64 goods_id = 24 [(gogoproto.jsontag) = "goods_id", (gogoproto.moretags) = 'form:"goods_id"', json_name = "goods_id"];
    // 购买单价(金瓜子)
    int64 price = 9 [(gogoproto.jsontag) = "price", (gogoproto.moretags) = 'form:"price"', json_name = "price"];
    // 商品名
    string goods_name = 10 [(gogoproto.jsontag) = "goods_name", (gogoproto.moretags) = 'form:"goods_name"', json_name = "goods_name"];
    // 奖池描述
    string lottery_desc = 11 [(gogoproto.jsontag) = "lottery_desc", (gogoproto.moretags) = 'form:"lottery_desc"', json_name = "lottery_desc"];
    // 奖池总商品数
    int64 item_total_cnt = 12 [(gogoproto.jsontag) = "item_total_cnt", (gogoproto.moretags) = 'form:"item_total_cnt"', json_name = "item_total_cnt"];
    // 奖池已拥有的商品数
    int64 item_owned_cnt = 13 [(gogoproto.jsontag) = "item_owned_cnt", (gogoproto.moretags) = 'form:"item_owned_cnt"', json_name = "item_owned_cnt"];
    // 该奖池的所有售卖数量
    int64 total_sale_amount = 14 [(gogoproto.jsontag) = "total_sale_amount", (gogoproto.moretags) = 'form:"total_buy_cnt"', json_name = "total_sale_amount"];
    // 奖励信息
    repeated ItemInfo item_list = 15 [(gogoproto.jsontag) = "item_list", (gogoproto.moretags) = 'form:"item_list"', json_name = "item_list"];
    // 是否永久生效 0-否，1-是
    int64 effective_forever = 16 [(gogoproto.jsontag) = "effective_forever", (gogoproto.moretags) = 'form:"effective_forever"', json_name = "effective_forever"];
    // 保底抽数据
    LotteryBottom lottery_bottom = 17 [(gogoproto.jsontag) = "lottery_bottom", (gogoproto.moretags) = 'form:"lottery_bottom"', json_name = "lottery_bottom"];
    message ItemInfo {
        // 奖品类型， 1:数字周边卡牌 2：表情包， 3：头像挂件， 4：装扮永久套装
        int64 item_type = 1 [(gogoproto.jsontag) = "item_type", (gogoproto.moretags) = 'form:"item_type"', json_name = "item_type"];
        // 稀缺度信息， 10 普通款， 30 隐藏款
        int64 scarcity = 2 [(gogoproto.jsontag) = "scarcity", (gogoproto.moretags) = 'form:"scarcity"', json_name = "scarcity"];
        // 当 item_type 为 1 时，使用此卡牌信息字段
        CardInfo card_info = 3 [(gogoproto.jsontag) = "card_info", (gogoproto.moretags) = 'form:"card_info"', json_name = "card_info"];
    }
    message CardInfo {
        // 卡牌信息
        nft.CardTypeInfo card_type_info = 1 [(gogoproto.jsontag) = "card_type_info", (gogoproto.moretags) = 'form:"card_type_info"', json_name = "card_type_info"];
        // 是否展示new标签
        int64 is_new_tag = 2 [(gogoproto.jsontag) = "is_new_tag", (gogoproto.moretags) = 'form:"is_new_tag"', json_name = "is_new_tag"];
        // 是否展示up标签
        int64 is_up_tag = 3 [(gogoproto.jsontag) = "is_up_tag", (gogoproto.moretags) = 'form:"is_up_tag"', json_name = "is_up_tag"];
        TagInfo tag = 4 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
        // 是否限量卡牌,1=是，0=否
        int64 is_limited_card = 5 [(gogoproto.jsontag) = "is_limited_card", (gogoproto.moretags) = 'form:"is_limited_card"', json_name = "is_limited_card"];
        // 库存信息
        CardStockInfo stock_info = 6 [(gogoproto.jsontag) = "stock_info", (gogoproto.moretags) = 'form:"stock_info"', json_name = "stock_info"];
        // 卡牌稀缺度，10 普通款，20 普通稀缺， 30 小隐藏， 40 大隐藏
        int64 card_scarcity = 7 [(gogoproto.jsontag) = "card_scarcity", (gogoproto.moretags) = 'form:"card_scarcity"', json_name = "card_scarcity"];
        // 卡牌额外信息
        CardTypeMetaInfo meta_info = 8 [(gogoproto.moretags) = 'form:"meta_info"', (gogoproto.jsontag) = 'meta_info', json_name = 'meta_info'];
    }
    message LotteryBottom {
        // 用户当前抽奖次数，大隐藏
        int64 draw_cnt = 1 [(gogoproto.jsontag) = "draw_cnt", (gogoproto.moretags) = 'form:"draw_cnt"', json_name = "draw_cnt"];
        // 保底总抽数，大隐藏
        int64 bottom_cnt = 2 [(gogoproto.jsontag) = "bottom_cnt", (gogoproto.moretags) = 'form:"bottom_cnt"', json_name = "bottom_cnt"];

        // 隐藏款保底次数
        int64 bottom_cnt_rate = 3 [(gogoproto.jsontag) = "bottom_cnt_rate", (gogoproto.moretags) = 'form:"bottom_cnt_rate"', json_name = "bottom_cnt_rate"];
    }
    // 故障提示toast （接口正常 非核心参数有误）
    string fault_toast = 18 [(gogoproto.jsontag) = "fault_toast", (gogoproto.moretags) = 'form:"fault_toast"', json_name = "fault_toast"];
}

message LotteryHomeDetailReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 用户ID
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 奖池id
    int64 lottery_id = 3 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
}

message LotteryHomeDetailResp {
    // 奖池id
    int64 lottery_id = 1 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 奖池名称
    string lottery_name = 2 [(gogoproto.jsontag) = "name", (gogoproto.moretags) = 'form:"name"', json_name = "name"];
    // 奖励信息
    repeated ItemInfo item_list = 3 [(gogoproto.jsontag) = "item_list", (gogoproto.moretags) = 'form:"item_list"', json_name = "item_list"];
    // 收集度奖励列表信息，跟奖池关联
    CollectInfo collect_info = 4 [(gogoproto.jsontag) = "collect_list", (gogoproto.moretags) = 'form:"collect_list"', json_name = "collect_list"];
    // 抽奖按钮气泡
    repeated ButtonBubble button_bubble = 5 [(gogoproto.jsontag) = "button_bubble", (gogoproto.moretags) = 'form:"button_bubble"', json_name = "button_bubble"];
    // 奖池引导信息
    BasicInfoResp.GuideInfo guide_info = 6 [(gogoproto.jsontag) = "guide_info", (gogoproto.moretags) = 'form:"guide_info"', json_name = "guide_info"];
    // 是否已经预约 (0否 1是)
    int64 is_booked = 7 [(gogoproto.jsontag) = "is_booked", (gogoproto.moretags) = 'form:"is_booked"', json_name = "is_booked"];
    // 累计预约人数
    int64 total_book_cnt = 8 [(gogoproto.jsontag) = "total_book_cnt", (gogoproto.moretags) = 'form:"total_book_cnt"', json_name = "total_book_cnt"];
    // 是否参加裂变 0:不参加 1:参加
    int64 is_fission = 9 [(gogoproto.jsontag) = "is_fission", (gogoproto.moretags) = 'form:"is_fission"', json_name = "is_fission"];
    // 实体按钮展示 0:不展示 1:展示【领实体】2:展示【我的邮寄】
    int64 physical_exchange = 10 [(gogoproto.jsontag) = "physical_exchange", (gogoproto.moretags) = 'form:"physical_exchange"', json_name = "physical_exchange"];
    // 活动攻略->卡牌应用和权益
    repeated ActRightsInfo act_rights_infos = 11 [(gogoproto.moretags) = 'form:"act_rights_infos"', (gogoproto.jsontag) = 'act_rights_infos', json_name = 'act_rights_infos'];
    message ItemInfo {
        // 奖品类型， 1:数字周边卡牌
        int64 item_type = 1 [(gogoproto.jsontag) = "item_type", (gogoproto.moretags) = 'form:"item_type"', json_name = "item_type"];
        // 当 item_type 为 1 时，使用此卡牌信息字段
        CardInfo card_info = 3 [(gogoproto.jsontag) = "card_info", (gogoproto.moretags) = 'form:"card_info"', json_name = "card_info"];
    }
    message CardInfo {
        // 当 item_type 为 1 时，使用此卡牌信息字段
        // 卡牌ID
        int64 card_type_id = 1 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id"', json_name = "card_type_id"];
        // 卡牌名
        string card_name = 2 [(gogoproto.jsontag) = "card_name", (gogoproto.moretags) = 'form:"card_name"', json_name = "card_name"];
        // 图片地址
        string card_img = 3 [(gogoproto.jsontag) = "card_img", (gogoproto.moretags) = 'form:"card_img"', json_name = "card_img"];
        // 卡牌类型(枚举值 1图片 2视频)
        int64 card_type = 4 [(gogoproto.jsontag) = "card_type", (gogoproto.moretags) = 'form:"card_type"', json_name = "card_type"];
        // 视频地址列表
        repeated string video_list = 9 [(gogoproto.jsontag) = "video_list", (gogoproto.moretags) = 'form:"video_list"', json_name = "video_list"];
        // 是否为陀螺仪素材卡牌  （0否 1是）
        int64 is_physical_orientation = 10 [(gogoproto.jsontag) = "is_physical_orientation", (gogoproto.moretags) = 'form:"is_physical_orientation"', json_name = "is_physical_orientation"];
        // 卡牌稀缺度枚举
        int64 card_scarcity = 11 [(gogoproto.jsontag) = "card_scarcity", (gogoproto.moretags) = 'form:"card_scarcity"', json_name = "card_scarcity"];
        // 是否为无声视频 0否 1是
        int64 is_mute = 12 [(gogoproto.jsontag) = "is_mute", (gogoproto.moretags) = 'form:"is_mute"', json_name = "is_mute"];
        // 宽
        int64 width = 13 [(gogoproto.jsontag) = "width", (gogoproto.moretags) = 'form:"width"', json_name = "width"];
        // 高
        int64 height = 14 [(gogoproto.jsontag) = "height", (gogoproto.moretags) = 'form:"height"', json_name = "height"];
        // 卡牌描述文本，如：DLC圣诞限定款
        string card_ext_text = 15 [(gogoproto.jsontag) = "card_ext_text", (gogoproto.moretags) = 'form:"card_ext_text"', json_name = "card_ext_text"];
        // 图片下载地址
        string card_img_download = 16 [(gogoproto.jsontag) = "card_img_download", (gogoproto.moretags) = 'form:"card_img_download"', json_name = "card_img_download"];
        // 视频下载地址列表
        repeated string video_list_download = 17 [(gogoproto.jsontag) = "video_list_download", (gogoproto.moretags) = 'form:"video_list_download"', json_name = "video_list_download"];
        // 字幕链接
        string subtitles_url = 18 [(gogoproto.jsontag) = "subtitles_url", (gogoproto.moretags) = 'form:"subtitles_url"', json_name = "subtitles_url"];
        // 播放设置
        Play play = 19 [(gogoproto.jsontag) = "play", (gogoproto.moretags) = 'form:"play"', json_name = "play"];
        // 角标展示
        TagInfo tag = 20 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
        // 卡牌子类型
        int64 card_sub_type = 21 [(gogoproto.jsontag) = "card_sub_type", (gogoproto.moretags) = 'form:"card_sub_type"', json_name = "card_sub_type"];
        // 是否展示new标签
        int64 is_new_tag = 22 [(gogoproto.jsontag) = "is_new_tag", (gogoproto.moretags) = 'form:"is_new_tag"', json_name = "is_new_tag"];
        // 是否展示up标签
        int64 is_up_tag = 23 [(gogoproto.jsontag) = "is_up_tag", (gogoproto.moretags) = 'form:"is_up_tag"', json_name = "is_up_tag"];
        // 是否限量卡牌,1=是，0=否
        int64 is_limited_card = 24 [(gogoproto.jsontag) = "is_limited_card", (gogoproto.moretags) = 'form:"is_limited_card"', json_name = "is_limited_card"];
        // 库存信息
        CardStockInfo stock_info = 25 [(gogoproto.jsontag) = "stock_info", (gogoproto.moretags) = 'form:"stock_info"', json_name = "stock_info"];
        // 卡牌额外信息
        CardTypeMetaInfo meta_info = 26 [(gogoproto.moretags) = 'form:"meta_info"', (gogoproto.jsontag) = 'meta_info', json_name = 'meta_info'];
    }
    message CollectInfo {
        // 收集度奖励列表信息
        repeated CollectRedeemInfo collect_infos = 1 [(gogoproto.jsontag) = "collect_infos", (gogoproto.moretags) = 'form:"collect_infos"', json_name = "collect_infos"];
        // 收集度奖励任务链
        repeated CollectRedeemInfo collect_chain = 2 [(gogoproto.jsontag) = "collect_chain", (gogoproto.moretags) = 'form:"collect_chain"', json_name = "collect_chain"];
    }
    // 抽奖按钮气泡
    message ButtonBubble {
        // 抽奖次数
        int64 lottery_num = 1 [(gogoproto.jsontag) = "lottery_num", (gogoproto.moretags) = 'form:"lottery_num"', json_name = "lottery_num"];
        // 气泡文案
        string bubble_text = 2 [(gogoproto.jsontag) = "bubble_text", (gogoproto.moretags) = 'form:"bubble_text"', json_name = "bubble_text"];
    }
}

// 活动维度的权益展示信息
message ActRightsInfo {
    // 权益生效范围：1 活动维度、2 卡池维度
    // 目前仅用来做标识，无实际意义
    int64 range_type = 1 [(gogoproto.moretags) = 'form:"range_type"', (gogoproto.jsontag) = 'range_type', json_name = 'range_type'];
    // 权益类型：1 头像应用、2 空间头图、3 评论区卡牌、4 卡牌赠送、5 闪屏、6 设置为小组件 7 设置为壁纸
    int64 rights_type = 2 [(gogoproto.moretags) = 'form:"rights_type"', (gogoproto.jsontag) = 'rights_type', json_name = 'rights_type'];
    // 大部分权益仅需要一张图作为资源
    string resource = 3 [(gogoproto.moretags) = 'form:"resource"', (gogoproto.jsontag) = 'resource', json_name = 'resource'];
    // 权益补充，补充资源等
    string extra = 4 [(gogoproto.moretags) = 'form:"extra"', (gogoproto.jsontag) = 'extra', json_name = 'extra'];
}

// 基础信息
message BasicInfoReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 用户ID
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 客户端类型 (android、ios、androidHD、iosHD、其他)
    string platform = 3 [(gogoproto.jsontag) = "platform", (gogoproto.moretags) = 'form:"platform"', json_name = "platform"];
    // 客户端build号
    int64 build_id = 4 [(gogoproto.jsontag) = "build_id", (gogoproto.moretags) = 'form:"build_id"', json_name = "build_id"];
    // ip
    string ip = 5 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
    // user_agent
    string user_agent = 6 [(gogoproto.jsontag) = "user_agent", (gogoproto.moretags) = 'form:"user_agent"', json_name = "user_agent"];
    // 风控上下文
    bytes gaia = 7 [(gogoproto.jsontag) = "gaia", (gogoproto.moretags) = 'form:"gaia"', json_name = "gaia"];
}

message DiscountInfo {
    // 折扣类型 vip_first_buy 大会员首抽
    string discount_type = 1;
    // 折扣 1 表示1折
    int64 discount_percent = 2;
    // 折扣文案
    string discount_tag = 3;
    // 原价 厘
    int64 origin_price = 4;
    // 价格 当前显示价格  厘
    int64 price = 5;
    // 没有折扣 引导
    DiscountGuidance discount_guidance = 6;
    message DiscountGuidance {
        // 引导文案
        string guidance_text = 1;
        // 引导跳转地址
        string guidance_url = 2;
    }
}

message BasicInfoResp {
    // 活动标题
    string act_title = 1 [(gogoproto.jsontag) = "act_title", (gogoproto.moretags) = 'form:"act_title"', json_name = "act_title"];
    // 系统当前时间戳
    int64 cur_time = 4 [(gogoproto.jsontag) = "cur_time", (gogoproto.moretags) = 'form:"cur_time"', json_name = "cur_time"];
    // 活动开始时间戳
    int64 start_time = 5 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
    // 活动结束时间戳
    int64 end_time = 6 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
    // 预约开始时间戳
    int64 pre_start_time = 7 [(gogoproto.jsontag) = "pre_start_time", (gogoproto.moretags) = 'form:"pre_start_time"', json_name = "pre_start_time"];
    // 预约结束时间戳
    int64 pre_end_time = 8 [(gogoproto.jsontag) = "pre_end_time", (gogoproto.moretags) = 'form:"pre_end_time"', json_name = "pre_end_time"];
    // 是否已经预约 (0否 1是)
    int64 is_booked = 11 [(gogoproto.jsontag) = "is_booked", (gogoproto.moretags) = 'form:"is_booked"', json_name = "is_booked"];
    // 活动攻略
    string act_desc = 12 [(gogoproto.jsontag) = "act_desc", (gogoproto.moretags) = 'form:"act_desc"', json_name = "act_desc"];
    // 奖池信息
    repeated LotteryInfo lottery_list = 13 [(gogoproto.jsontag) = "lottery_list", (gogoproto.moretags) = 'form:"lottery_list"', json_name = "lottery_list"];
    // 分享主标题、子标题、文案
    ShareInfo share_info = 14 [(gogoproto.jsontag) = "share_info", (gogoproto.moretags) = 'form:"share_info"', json_name = "share_info"];
    // 图鉴封面图
    string act_y_img = 21 [(gogoproto.jsontag) = "act_y_img", (gogoproto.moretags) = 'form:"act_y_img"', json_name = "act_y_img"];
    // 开场动画信息
    AnimationInfo animation_info = 22 [(gogoproto.jsontag) = "animation_info", (gogoproto.moretags) = 'form:"animation_info"', json_name = "animation_info"];
    // 累计预约人数
    int64 total_book_cnt = 31 [(gogoproto.jsontag) = "total_book_cnt", (gogoproto.moretags) = 'form:"total_book_cnt"', json_name = "total_book_cnt"];
    // 累计购买人数
    int64 total_buy_cnt = 32 [(gogoproto.jsontag) = "total_buy_cnt", (gogoproto.moretags) = 'form:"total_buy_cnt"', json_name = "total_buy_cnt"];
    // 横版灯光背景图
    string horizontal_card_light_url = 33 [(gogoproto.jsontag) = "horizontal_card_light_url", (gogoproto.moretags) = 'form:"horizontal_card_light_url"', json_name = "horizontal_card_light_url"];
    // 横版底座背景图
    string horizontal_card_shadow_url = 34 [(gogoproto.jsontag) = "horizontal_card_shadow_url", (gogoproto.moretags) = 'form:"horizontal_card_shadow_url"', json_name = "horizontal_card_shadow_url"];
    // 竖版灯光背景图
    string vertical_card_light_url = 35 [(gogoproto.jsontag) = "vertical_card_light_url", (gogoproto.moretags) = 'form:"vertical_card_light_url"', json_name = "vertical_card_light_url"];
    // 竖版底座背景图
    string vertical_card_shadow_url = 36 [(gogoproto.jsontag) = "vertical_card_shadow_url", (gogoproto.moretags) = 'form:"vertical_card_shadow_url"', json_name = "vertical_card_shadow_url"];
    // 是否有预热阶段(0否 1是)
    int64 is_pre = 38 [(gogoproto.jsontag) = "is_pre", (gogoproto.moretags) = 'form:"is_pre"', json_name = "is_pre"];
    // 是否命中风控
    int64 is_pre_risk = 40 [(gogoproto.jsontag) = "is_pre_risk", (gogoproto.moretags) = 'form:"is_pre_risk"', json_name = "is_pre_risk"];
    // 是否有收藏家排名
    int64 is_collector_rank = 41 [(gogoproto.jsontag) = "is_collector_rank", (gogoproto.moretags) = 'form:"is_collector_rank"', json_name = "is_collector_rank"];
    // 收集度奖励列表信息
    repeated CollectRedeemInfo collect_list = 42 [(gogoproto.jsontag) = "collect_list", (gogoproto.moretags) = 'form:"collect_list"', json_name = "collect_list"];
    // 活动外显标题
    string display_title = 43 [(gogoproto.jsontag) = "display_title", (gogoproto.moretags) = 'form:"display_title"', json_name = "display_title"];
    // 是否永久生效 0-否，1-是
    int64 effective_forever = 44 [(gogoproto.jsontag) = "effective_forever", (gogoproto.moretags) = 'form:"effective_forever"', json_name = "effective_forever"];

    // 活动引导信息
    GuideInfo guide_info = 45 [(gogoproto.jsontag) = "guide_info", (gogoproto.moretags) = 'form:"guide_info"', json_name = "guide_info"];

    // 是否能转赠 0-否，1-是
    int64 is_can_donate = 46 [(gogoproto.jsontag) = "is_can_donate", (gogoproto.moretags) = 'form:"is_can_donate"', json_name = "is_can_donate"];

    // 是否上链，1=是，0=否
    int64 is_up_chain = 47 [(gogoproto.jsontag) = "is_up_chain", (gogoproto.moretags) = 'form:"is_up_chain"', json_name = "is_up_chain"];
    // 相关的mid，用于自动关注，目前最多3个
    repeated string related_mids = 48 [(gogoproto.jsontag) = "related_mids", (gogoproto.moretags) = 'form:"related_mids"', json_name = "related_mids"];
    // 登录用户 是否是vip， 不登录 or 不是 为false
    bool is_vip = 49 [(gogoproto.jsontag) = "is_vip", (gogoproto.moretags) = 'form:"is_vip"', json_name = "is_vip"];
    // 任务信息(ogv任务系统数据)
    TaskList task_list = 50 [(gogoproto.jsontag) = "task_list", (gogoproto.moretags) = 'form:"task_list"', json_name = "task_list"];
    // 引导 弹窗 信息
    PopupInfo popup_info = 51 [(gogoproto.jsontag) = "popup_info", (gogoproto.moretags) = 'form:"popup_info"', json_name = "popup_info"];
    // 活动攻略 浅色
    string act_desc_light = 52 [(gogoproto.jsontag) = "act_desc_light", (gogoproto.moretags) = 'form:"act_desc_light"', json_name = "act_desc_light"];
    // 抽卡是否需要实名验证
    // 1=不需要实名认证，0=需要实名认证
    int64 not_need_realname = 53 [(gogoproto.jsontag) = "not_need_realname", (gogoproto.moretags) = 'form:"not_need_realname"', json_name = "not_need_realname"];
    // 产品亮点
    string product_introduce = 54 [(gogoproto.jsontag) = "product_introduce", (gogoproto.moretags) = 'form:"product_introduce"', json_name = "product_introduce"];
    // 应用头像展示
    string app_head_show = 55 [(gogoproto.jsontag) = "app_head_show", (gogoproto.moretags) = 'form:"app_head_show"', json_name = "app_head_show"];
    // 应用空间头图展示
    string app_space_show = 56 [(gogoproto.jsontag) = "app_space_show", (gogoproto.moretags) = 'form:"app_space_show"', json_name = "app_space_show"];
    // 收藏家勋章信息
    string collector_medal_info = 57 [(gogoproto.jsontag) = "collector_medal_info", (gogoproto.moretags) = 'form:"collector_medal_info"', json_name = "collector_medal_info"];
    // 话题信息
    TopicInfo topic = 58 [(gogoproto.jsontag) = "topic", (gogoproto.moretags) = 'form:"topic"', json_name = "topic"];
    // 活动是否参加实体卡牌兑换
    bool physical_exchange = 59 [(gogoproto.jsontag) = "physical_exchange", (gogoproto.moretags) = 'form:"physical_exchange"', json_name = "physical_exchange"];
    // act_square_img 商品封面图/方形图
    string act_square_img = 60 [(gogoproto.jsontag) = "act_square_img", (gogoproto.moretags) = 'form:"act_square_img"', json_name = "act_square_img"];
    // 默认tab定位奖池ID
    int64 tab_lottery_id = 61 [(gogoproto.jsontag) = "tab_lottery_id", (gogoproto.moretags) = 'form:"tab_lottery_id"', json_name = "tab_lottery_id"];
    // related_mids用户信息
    map<int64, RelatedUserInfo> related_user_infos = 62 [(gogoproto.jsontag) = "related_user_infos", json_name = "related_user_infos", (gogoproto.moretags) = 'form:"related_user_infos"'];
    // 保存图片能力，1-是，非1否
    int64 save_image_right = 63 [(gogoproto.moretags) = 'form:"save_image_right"', (gogoproto.jsontag) = 'save_image_right', json_name = 'save_image_right'];
    // 是否展示分解规则 0不展示 1展示
    int64 show_decompose_rule = 64 [(gogoproto.moretags) = 'form:"show_decompose_rule"', (gogoproto.jsontag) = 'show_decompose_rule', json_name = 'show_decompose_rule'];
    // 是否展示绘星卡规则 0不展示 1展示
    int64 show_smelt_rule = 65 [(gogoproto.moretags) = 'form:"show_smelt_rule"', (gogoproto.jsontag) = 'show_smelt_rule', json_name = 'show_smelt_rule'];
    // 交易灰度标记 1 灰度走新交易系统 0 非灰度
    int64 gray_transaction = 66 [(gogoproto.moretags) = 'form:"gray_transaction"', (gogoproto.jsontag) = 'gray_transaction', json_name = 'gray_transaction'];
    // 话题信息
    message TopicInfo {
        // 话题id
        int64 id = 1 [(gogoproto.jsontag) = "id", (gogoproto.moretags) = 'form:"id"', json_name = "id"];
    }
    message PopupInfo {
        // 弹窗类型 1 图片 2 文字
        int64 popup_type = 1 [(gogoproto.jsontag) = "popup_type", (gogoproto.moretags) = 'form:"popup_type"', json_name = "popup_type"];
        // 弹窗内容
        string popup_content = 2 [(gogoproto.jsontag) = "popup_content", (gogoproto.moretags) = 'form:"popup_content"', json_name = "popup_content"];
        // 按钮文案
        string button_text = 3 [(gogoproto.jsontag) = "button_text", (gogoproto.moretags) = 'form:"button_text"', json_name = "button_text"];
        // 按钮跳转地址
        string button_jump_url = 4 [(gogoproto.jsontag) = "button_jump_url", (gogoproto.moretags) = 'form:"button_jump_url"', json_name = "button_jump_url"];
    }
    message TaskList {
        // 任务列表
        repeated pgc.service.activity.lt.TaskListItem item = 1 [(gogoproto.jsontag) = "item", (gogoproto.moretags) = 'form:"item"', json_name = "item"];
        // 任务组 名称
        string task_name = 2 [(gogoproto.jsontag) = "task_name", (gogoproto.moretags) = 'form:"task_name"', json_name = "task_name"];
        // 任务组 跳转地址
        string task_jump_url = 3 [(gogoproto.jsontag) = "task_jump_url", (gogoproto.moretags) = 'form:"task_jump_url"', json_name = "task_jump_url"];
        // 任务组 跳转文案
        string task_jump_text = 4 [(gogoproto.jsontag) = "task_jump_text", (gogoproto.moretags) = 'form:"task_jump_text"', json_name = "task_jump_text"];
        // 任务 活动id
        int64 task_activity_id = 5 [(gogoproto.jsontag) = "task_activity_id", (gogoproto.moretags) = 'form:"task_activity_id"', json_name = "task_activity_id"];
        // 任务组 类型 vip表示大会员类任务
        string task_type = 6 [(gogoproto.jsontag) = "task_type", (gogoproto.moretags) = 'form:"task_type"', json_name = "task_type"];
    }

    // 分享信息
    message ShareInfo {
        // 分享主标题
        string main_title = 1 [(gogoproto.jsontag) = "main_title", (gogoproto.moretags) = 'form:"main_title"', json_name = "main_title"];
        // 分享子标题
        string sub_title = 2 [(gogoproto.jsontag) = "sub_title", (gogoproto.moretags) = 'form:"sub_title"', json_name = "sub_title"];
        // 分享文案
        string share_content = 3 [(gogoproto.jsontag) = "share_content", (gogoproto.moretags) = 'form:"share_content"', json_name = "share_content"];
    }
    // 开卡动画信息
    message AnimationInfo {
        map<string, string> animation_draw_url = 1 [(gogoproto.jsontag) = "animation_draw_url", (gogoproto.moretags) = 'form:"animation_draw_url"', json_name = "animation_draw_url"];
        map<string, string> animation_entry_url = 2 [(gogoproto.jsontag) = "animation_entry_url", (gogoproto.moretags) = 'form:"animation_entry_url"', json_name = "animation_entry_url"];
    }
    // 奖池信息
    message LotteryInfo {
        // 奖池id
        int64 lottery_id = 1;
        // 奖池名称
        string lottery_name = 2 [(gogoproto.jsontag) = "lottery_name", (gogoproto.moretags) = 'form:"lottery_name"', json_name = "lottery_name"];
        // 开始展示时间
        int64 display_time = 3 [(gogoproto.jsontag) = "display_time", (gogoproto.moretags) = 'form:"display_time"', json_name = "display_time"];
        // 轮次开始时间戳
        int64 start_time = 4 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
        // 轮次结束时间戳
        int64 end_time = 5 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
        // 奖池图片
        string lottery_image = 6 [(gogoproto.jsontag) = "lottery_image", (gogoproto.moretags) = 'form:"lottery_image"', json_name = "lottery_image"];
        // 本奖池剩余抽奖次数
        int64 draw_cnt = 8 [(gogoproto.jsontag) = "draw_cnt", (gogoproto.moretags) = 'form:"draw_cnt"', json_name = "draw_cnt"];
        // 商品id
        int64 goods_id = 9 [(gogoproto.jsontag) = "goods_id", (gogoproto.moretags) = 'form:"goods_id"', json_name = "goods_id"];
        // 购买单价(厘)
        int64 price = 11 [(gogoproto.jsontag) = "price", (gogoproto.moretags) = 'form:"price"', json_name = "price"];
        // 商品名
        string goods_name = 12 [(gogoproto.jsontag) = "goods_name", (gogoproto.moretags) = 'form:"goods_name"', json_name = "goods_name"];
        // 奖池描述
        string lottery_desc = 13 [(gogoproto.jsontag) = "lottery_desc", (gogoproto.moretags) = 'form:"lottery_desc"', json_name = "lottery_desc"];
        // 奖池总商品数
        int64 item_total_cnt = 14 [(gogoproto.jsontag) = "item_total_cnt", (gogoproto.moretags) = 'form:"item_total_cnt"', json_name = "item_total_cnt"];
        // 奖池已拥有的商品数
        int64 item_owned_cnt = 15 [(gogoproto.jsontag) = "item_owned_cnt", (gogoproto.moretags) = 'form:"item_owner_cnt"', json_name = "item_owned_cnt"];
        // 该奖池的所有售卖数量
        int64 total_sale_amount = 16 [(gogoproto.jsontag) = "total_sale_amount", (gogoproto.moretags) = 'form:"total_sale_amount"', json_name = "total_sale_amount"];
        // 奖池类型，1 常驻奖池  2 纪念奖池
        int64 lottery_type = 17 [(gogoproto.jsontag) = "lottery_type", (gogoproto.moretags) = 'form:"lottery_type"', json_name = "lottery_type"];
        // 是否永久生效 0-否，1-是
        int64 effective_forever = 18 [(gogoproto.jsontag) = "effective_forever", (gogoproto.moretags) = 'form:"effective_forever"', json_name = "effective_forever"];
        DiscountInfo discount = 19 [(gogoproto.jsontag) = "discount", (gogoproto.moretags) = 'form:"discount"'];
        // 是否免费 0-否，1-是
        int64 free = 20 [(gogoproto.jsontag) = "free", (gogoproto.moretags) = 'form:"free"', json_name = "free"];
        // 限售模式，1=永久奖池，2=限时奖池，3=限量奖池
        int64 sale_mode = 21 [(gogoproto.jsontag) = "sale_mode", (gogoproto.moretags) = 'form:"sale_mode"', json_name = "sale_mode"];
    }
    // 收集度奖励
    message CollectRedeemInfo {
        // 收集奖励id
        int64 collect_id = 1 [(gogoproto.jsontag) = "collect_id", (gogoproto.moretags) = 'form:"collect_id"', json_name = "collect_id"];
        // 收集奖励任务的开始时间
        int64 start_time = 2 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
        // 收集奖励任务的结束时间
        int64 end_time = 3 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
        // 领取条件文案
        string redeem_text = 4 [(gogoproto.jsontag) = "redeem_text", (gogoproto.moretags) = 'form:"redeem_text"', json_name = "redeem_text"];
        // 收集奖励的物品类型 1:数字周边卡牌 2：表情包， 3：头像挂件，4：装扮永久套装, 7：观影券 8: cdk 9：实物奖励 10:限时装扮散件
        int64 redeem_item_type = 7 [(gogoproto.jsontag) = "redeem_item_type", (gogoproto.moretags) = 'form:"redeem_item_type"', json_name = "redeem_item_type"];
        // 收集奖励的物品id
        string redeem_item_id = 8 [(gogoproto.jsontag) = "redeem_item_id", (gogoproto.moretags) = 'form:"redeem_item_id"', json_name = "redeem_item_id"];
        // 收集奖励的物品名称
        string redeem_item_name = 9 [(gogoproto.jsontag) = "redeem_item_name", (gogoproto.moretags) = 'form:"redeem_item_name"', json_name = "redeem_item_name"];
        // 收集奖励的物品图片
        string redeem_item_image = 10 [(gogoproto.jsontag) = "redeem_item_image", (gogoproto.moretags) = 'form:"redeem_item_image"', json_name = "redeem_item_image"];
        // 已收集的数量
        int64 owned_item_amount = 11 [(gogoproto.jsontag) = "owned_item_amount", (gogoproto.moretags) = 'form:"owned_item_amount"', json_name = "owned_item_amount"];
        // 收集奖励要求的数量
        int64 require_item_amount = 12 [(gogoproto.jsontag) = "require_item_amount", (gogoproto.moretags) = 'form:"require_item_amount"', json_name = "require_item_amount"];
        // 已兑换次数
        int64 has_redeemed_cnt = 13 [(gogoproto.jsontag) = "has_redeemed_cnt", (gogoproto.moretags) = 'form:"has_redeemed_cnt"', json_name = "has_redeemed_cnt"];
        // 是否永久生效 0-否，1-是
        int64 effective_forever = 14 [(gogoproto.jsontag) = "effective_forever", (gogoproto.moretags) = 'form:"effective_forever"', json_name = "effective_forever"];
        // 收集奖励的物品图片下载链接
        string redeem_item_image_download = 15 [(gogoproto.jsontag) = "redeem_item_image_download", (gogoproto.moretags) = 'form:"redeem_item_image_download"', json_name = "redeem_item_image_download"];
        // 收集奖励的物品图片下载链接
        CardItem card_item = 16 [(gogoproto.jsontag) = "card_item", (gogoproto.moretags) = 'form:"card_item"', json_name = "card_item"];
        // 道具跳转链接
        string jump_url = 17 [(gogoproto.jsontag) = "jump_url", (gogoproto.moretags) = 'form:"jump_url"', json_name = "jump_url"];
        // 领取条件类型,scarcity=稀有度，card_type_id=指定卡牌，lottery_num=抽数，custom=自定义
        string redeem_cond_type = 18 [(gogoproto.jsontag) = "redeem_cond_type", (gogoproto.moretags) = 'form:"redeem_cond_type"', json_name = "redeem_cond_type"];
        // 剩余库存：默认传-1
        int64 remain_stock = 19 [(gogoproto.jsontag) = "remain_stock", (gogoproto.moretags) = 'form:"remain_stock"', json_name = "remain_stock"];
        // 全部库存：默认传-1
        int64 total_stock = 20 [(gogoproto.jsontag) = "total_stock", (gogoproto.moretags) = 'form:"total_stock"', json_name = "total_stock"];
        repeated string redeem_items_optional = 21 [(gogoproto.jsontag) = "redeem_items_optional", (gogoproto.moretags) = 'form:"redeem_items_optional"', json_name = "redeem_items_optional"];
        // 卡牌数据
        message CardItem {
            // 抽中卡牌信息
            nft.CardTypeInfo card_type_info = 1 [(gogoproto.jsontag) = "card_type_info", (gogoproto.moretags) = 'form:"card_type_info"', json_name = "card_type_info"];
            // 播放设置
            Play play = 2 [(gogoproto.jsontag) = "play", (gogoproto.moretags) = 'form:"play"', json_name = "play"];
            // 角标展示
            TagInfo tag = 3 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
        }
    }

    // 活动引导
    message GuideInfo {
        // 活动引导标题
        string title = 1 [(gogoproto.jsontag) = "title", (gogoproto.moretags) = 'form:"title"', json_name = "title"];
        // 活动引导文案
        string guide_content = 2 [(gogoproto.jsontag) = "guide_content", (gogoproto.moretags) = 'form:"guide_content"', json_name = "guide_content"];
        // 引导跳转url
        string jump_url = 3 [(gogoproto.jsontag) = "jump_url", (gogoproto.moretags) = 'form:"jump_url"', json_name = "jump_url"];
    }

    // related_mids用户信息
    message RelatedUserInfo {
        // 主播uid
        int64 uid = 2 [(gogoproto.jsontag) = "uid", json_name = "uid", (gogoproto.moretags) = 'form:"uid"'];
        // 主播昵称
        string nickname = 3 [(gogoproto.jsontag) = "nickname", json_name = "nickname", (gogoproto.moretags) = 'form:"nickname"'];
        // 头像
        string avatar = 4 [(gogoproto.jsontag) = "avatar", json_name = "avatar", (gogoproto.moretags) = 'form:"avatar"'];
    }
}

// 用户预约
message BookReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 用户ID
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"', json_name = "mid"];
    // 风控上下文
    bytes gaia = 3 [(gogoproto.jsontag) = "gaia", (gogoproto.moretags) = 'form:"gaia"', json_name = "gaia"];
    // 活动ID
    int64 lottery_id = 4 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
}

// 支付预检
message CommonPreCheckReq {
    // 用户id
    int64 uid = 1 [(gogoproto.jsontag) = "uid", (gogoproto.moretags) = 'form:"uid" validate:"required"', json_name = "uid"];
    // 主播/UP主id
    int64 ruid = 2 [(gogoproto.jsontag) = "ruid", (gogoproto.moretags) = 'form:"ruid"', json_name = "ruid"];
    // 商品ID
    int64 goods_id = 3 [(gogoproto.jsontag) = "goods_id", (gogoproto.moretags) = 'form:"goods_id"', json_name = "goods_id"];
    // 商品数量
    int64 goods_num = 4 [(gogoproto.jsontag) = "goods_num", (gogoproto.moretags) = 'form:"goods_num"', json_name = "goods_num"];
    // 平台  ios/android/pc/h5
    string platform = 5 [(gogoproto.jsontag) = "platform", (gogoproto.moretags) = 'form:"platform"', json_name = "platform"];
    // mobile_app
    string mobile_app = 6 [(gogoproto.jsontag) = "mobile_app", (gogoproto.moretags) = 'form:"mobile_app"', json_name = "mobile_app"];
    // 价格(金瓜子 1000金瓜子=1元)
    int64 price = 7 [(gogoproto.jsontag) = "price", (gogoproto.moretags) = 'form:"price"', json_name = "price"];
    // ip 地址
    string ip = 8 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
    // 业务透传字段
    string biz_extra = 9 [(gogoproto.jsontag) = "biz_extra", (gogoproto.moretags) = 'form:"biz_extra"', json_name = "biz_extra"];
    // 业务类型 101表示集卡业务
    int64 context_type = 10 [(gogoproto.jsontag) = "context_type", (gogoproto.moretags) = 'form:"context_type"', json_name = "context_type"];
    // 业务id
    string context_id = 11 [(gogoproto.jsontag) = "context_id", (gogoproto.moretags) = 'form:"context_id"', json_name = "context_id"];
}
message CommonPreCheckResp {
    // 是否可购买  0:不可购买 1:可购买
    int64 can_buy = 1 [(gogoproto.jsontag) = "can_buy", (gogoproto.moretags) = 'form:"can_buy"', json_name = "can_buy"];
    // 不可购买原因
    string reason = 2 [(gogoproto.jsontag) = "reason", (gogoproto.moretags) = 'form:"reason"', json_name = "reason"];
    // 需要透传的错误码，不传会使用订单服务通用错误码
    string err_code = 3 [(gogoproto.jsontag) = "err_code", (gogoproto.moretags) = 'form:"err_code"', json_name = "err_code"];
}

// 抽取奖品
message DrawItemReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 用户ID
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"', json_name = "mid"];
    // 奖池id
    int64 lottery_id = 3 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 抽取次数
    int64 lottery_num = 4 [(gogoproto.jsontag) = "lottery_num", (gogoproto.moretags) = 'form:"lottery_num"', json_name = "lottery_num"];
    // ip
    string ip = 5 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
    //  // 直播间房间号（弃用，使用biz_info字段）
    //  int64 room_id = 6 [    //    (gogoproto.jsontag) = "room_id",    //    (gogoproto.moretags) = 'form:"room_id"',    //    json_name = "room_id"
    //  ];
    // 抽奖的业务方来源信息（为兼容端上透传，
    string biz_info = 7 [(gogoproto.jsontag) = "biz_info", (gogoproto.moretags) = 'form:"biz_info"', json_name = "biz_info"];
    // 设备
    DeviceInfo device = 8;
}
message DrawItemResp {
    // 错误码 0表示成功 其他表示失败
    int64 err_code = 1 [(gogoproto.jsontag) = "err_code", (gogoproto.moretags) = 'form:"err_code"', json_name = "err_code"];
    // 错误信息，失败情况下返回
    string err_message = 2 [(gogoproto.jsontag) = "err_message", (gogoproto.moretags) = 'form:"err_message"', json_name = "err_message"];
    // 本奖池剩余机会数
    int64 left_chance = 3 [(gogoproto.jsontag) = "left_chance", (gogoproto.moretags) = 'form:"left_chance"', json_name = "left_chance"];
    repeated ItemInfo item_list = 4 [(gogoproto.jsontag) = "item_list", (gogoproto.moretags) = 'form:"item_list"', json_name = "item_list"];
    message ItemInfo {
        // 奖品类型：1:数字周边卡牌 2：表情包， 3：头像挂件， 4：装扮永久套装
        int64 item_type = 1 [(gogoproto.jsontag) = "item_type", (gogoproto.moretags) = 'form:"item_type"', json_name = "item_type"];
        // 稀缺度信息， 10 普通款， 30 隐藏款
        int64 item_scarcity = 2 [(gogoproto.jsontag) = "item_scarcity", (gogoproto.moretags) = 'form:"item_scarcity"', json_name = "item_scarcity"];
        // 当 item_type 取值为 1：数字周边卡牌时，使用此卡牌物品信息
        CardItem card_item = 3 [(gogoproto.jsontag) = "card_item", (gogoproto.moretags) = 'form:"card_item"', json_name = "card_item"];
        // 样式显示
        Display display = 4 [(gogoproto.jsontag) = "display", (gogoproto.moretags) = 'form:"display"', json_name = "display"];
    }
    message CardItem {
        // 抽中卡牌信息
        nft.CardTypeInfo card_type_info = 1 [(gogoproto.jsontag) = "card_type_info", (gogoproto.moretags) = 'form:"card_type_info"', json_name = "card_type_info"];
        // 卡牌概率，百分比，保留两位小数
        double card_chance = 2 [(gogoproto.jsontag) = "card_chance", (gogoproto.moretags) = 'form:"card_chance"', json_name = "card_chance"];
        // 卡片信息
        DrawCardInfo card_info = 3 [(gogoproto.jsontag) = "card_info", (gogoproto.moretags) = 'form:"card_info"', json_name = "card_info"];
        // 总发行量
        int64 card_type_cnt = 4 [(gogoproto.jsontag) = "card_type_cnt", (gogoproto.moretags) = 'form:"card_type_cnt"', json_name = "card_type_cnt"];
        // 总发行量展示
        string card_type_cnt_show = 5 [(gogoproto.jsontag) = "card_type_cnt_show", (gogoproto.moretags) = 'form:"card_type_cnt_show"', json_name = "card_type_cnt_show"];
        // 播放设置
        Play play = 6 [(gogoproto.jsontag) = "play", (gogoproto.moretags) = 'form:"play"', json_name = "play"];
        // 标签展示
        TagInfo tag = 7 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];

        message DrawCardInfo {
            // 卡片id
            int64 card_id = 1 [(gogoproto.jsontag) = "card_id", (gogoproto.moretags) = 'form:"card_id"', json_name = "card_id"];
            // 卡片编号(补齐6位)
            string card_no = 2 [(gogoproto.jsontag) = "card_no", (gogoproto.moretags) = 'form:"card_no"', json_name = "card_no"];
            // hash值 (拿不到，字段废弃)
            string hash_code = 3 [(gogoproto.jsontag) = "hash_code", (gogoproto.moretags) = 'form:"hash_code"', json_name = "hash_code"];
            // 卡片权益
            CardRight card_right = 4 [(gogoproto.jsontag) = "card_right", (gogoproto.moretags) = 'form:"card_right"', json_name = "card_right"];
            // 卡片权益展示(前端专用)
            CardRightShow card_right_show = 5 [(gogoproto.jsontag) = "card_right_show", (gogoproto.moretags) = 'form:"card_right_show"', json_name = "card_right_show"];
            // 卡牌描述文本，如：DLC圣诞限定款
            string card_ext_text = 6 [(gogoproto.jsontag) = "card_ext_text", (gogoproto.moretags) = 'form:"card_ext_text"', json_name = "card_ext_text"];
            // 卡片编号颜色格式
            vas.common.FanNumColorFormat color_format = 7 [(gogoproto.jsontag) = "color_format", json_name = "color_format"];
            // 熔炼锁定状态 1未锁定 2已上锁 3锁死
            int64 smelt_lock_status = 8 [(gogoproto.jsontag) = "smelt_lock_status", json_name = "smelt_lock_status"];
            // 卡牌额外信息
            CardMetaInfo meta_info = 9 [(gogoproto.moretags) = 'form:"meta_info"', (gogoproto.jsontag) = 'meta_info', json_name = 'meta_info'];
        }
    }
    message Display {
        // 奖励角标
        ItemCornerMark corner_mark = 1 [(gogoproto.jsontag) = "corner_mark", (gogoproto.moretags) = 'form:"corner_mark"', json_name = "corner_mark"];
        // 奖励边框
        ItemFrame frame = 2 [(gogoproto.jsontag) = "frame", (gogoproto.moretags) = 'form:"frame"', json_name = "frame"];
    }
    // 角标
    message ItemCornerMark {
        // 图片链接
        string img_url = 1 [(gogoproto.jsontag) = "img_url", (gogoproto.moretags) = 'form:"img_url"', json_name = "img_url"];
    }
    // 边框
    message ItemFrame {
        // 图片链接
        string img_url = 1 [(gogoproto.jsontag) = "img_url", (gogoproto.moretags) = 'form:"img_url"', json_name = "img_url"];
        // 小图链接
        string small_img_url = 2 [(gogoproto.jsontag) = "small_img_url", (gogoproto.moretags) = 'form:"small_img_url"', json_name = "small_img_url"];
    }
}

message SendCardReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 用户ID
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"', json_name = "mid"];
    // 默认奖池id，当scene=1时为收集度奖励ID
    int64 lottery_id = 3 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 卡片类型id
    int64 card_type_id = 4 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id"', json_name = "card_type_id"];
    // 发送的张数，最大限制10，考虑系统并发
    int32 num = 5 [(gogoproto.jsontag) = "num", (gogoproto.moretags) = 'form:"num" validate:"min=1,max=10"', json_name = "num"];
    // 是否要通知
    bool notify = 6 [(gogoproto.jsontag) = "notify", (gogoproto.moretags) = 'form:"notify"', json_name = "notify"];
    // 发放场景，默认0=奖池，1=收集度奖励
    int64 scene = 7 [(gogoproto.jsontag) = "scene", (gogoproto.moretags) = 'form:"scene"', json_name = "scene"];
    // 唯一幂等id
    string msg_id = 8 [(gogoproto.jsontag) = "msg_id", (gogoproto.moretags) = 'form:"msg_id"', json_name = "msg_id"];
    // 扩展参数
    Extend extend = 9;
    message Extend {
        // 业务方标识
        int64 biz_id = 1;
        // 结算价格 单位厘（不传默认以奖池价格） 总价 必须 <= 商品原价*数量
        int64 settlement_price = 2;
        // 结算渠道 1 表示非ios 2 ios
        int64 settlement_channel = 3;
    }
    // 发放来源 0 ogv道具后台 1 后台发放 2 活动发放
    int64 source = 10;
    // 卡牌拥有人的mid，如果没有则同mid
    int64 owner_mid = 11 [(gogoproto.jsontag) = "owner_mid", (gogoproto.moretags) = 'form:"owner_mid"', json_name = "owner_mid"];
}

message SendCardResp {}

// 转赠校验
message TransferCheckReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 持有用户ID
    int64 from_uid = 2 [(gogoproto.jsontag) = "from_uid", (gogoproto.moretags) = 'form:"from_uid" validate:"required"', json_name = "from_uid"];
    // 卡牌id
    int64 card_type_id = 3 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id" validate:"required"', json_name = "card_type_id"];
    // 卡片id
    int64 card_id = 4 [(gogoproto.jsontag) = "card_id", (gogoproto.moretags) = 'form:"card_id" validate:"required"', json_name = "card_id"];
}
message TransferCheckResp {
    // 是否通过校验 (0否 1是)
    int64 is_pass = 1 [(gogoproto.jsontag) = "is_pass", (gogoproto.moretags) = 'form:"is_pass"', json_name = "is_pass"];
    // 不通过的原因code (0:成功 400:头像 401:空间头图 402:评论背景)
    int64 err_code = 2 [(gogoproto.jsontag) = "err_code", (gogoproto.moretags) = 'form:"err_code"', json_name = "err_code"];
    // 不通过的原因
    string err_msg = 3 [(gogoproto.jsontag) = "err_msg", (gogoproto.moretags) = 'form:"err_msg"', json_name = "err_msg"];
}

// 图鉴列表
message ActItemListReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 主态uid（为0表示查看本次活动图鉴）
    int64 ruid = 2 [(gogoproto.jsontag) = "ruid", (gogoproto.moretags) = 'form:"ruid"', json_name = "ruid"];
    // 客态uid（为0表示当前用户未登录）
    int64 uid = 3 [(gogoproto.jsontag) = "uid", (gogoproto.moretags) = 'form:"uid"', json_name = "uid"];
    // ip
    string ip = 4 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
    // 用户持有状态过滤，0：所有  1：已拥有 2：未拥有
    int64 owned_status = 5 [(gogoproto.jsontag) = "owned_status", (gogoproto.moretags) = 'form:"owned_status"', json_name = "owned_status"];
}
message ActItemListResp {
    // 用户名
    string uname = 1 [(gogoproto.jsontag) = "uname", (gogoproto.moretags) = 'form:"uname"', json_name = "uname"];
    // 用户头像
    string uface = 2 [(gogoproto.jsontag) = "uface", (gogoproto.moretags) = 'form:"uface"', json_name = "uface"];
    // 图鉴页图片
    string act_y_img = 3 [(gogoproto.jsontag) = "act_y_img", (gogoproto.moretags) = 'form:"act_y_img"', json_name = "act_y_img"];
    // 图鉴所有物品种类数量
    int64 total_item_cnt = 4 [(gogoproto.jsontag) = "total_item_cnt", (gogoproto.moretags) = 'form:"total_item_cnt"', json_name = "total_item_cnt"];
    // 图鉴已拥有的物品种类数量
    int64 owned_item_cnt = 5 [(gogoproto.jsontag) = "owned_item_cnt", (gogoproto.moretags) = 'form:"owned_item_cnt"', json_name = "owned_item_cnt"];
    // 奖励物品信息
    repeated ItemInfo item_list = 15 [(gogoproto.jsontag) = "item_list", (gogoproto.moretags) = 'form:"item_list"', json_name = "item_list"];
    message ItemInfo {
        // 奖品类型， 1:数字周边卡牌 2：表情包， 3：头像挂件， 4：装扮永久套装
        int64 item_type = 1 [(gogoproto.jsontag) = "item_type", (gogoproto.moretags) = 'form:"item_type"', json_name = "item_type"];
        // 稀缺度信息，  10： 普通款， 30： 隐藏款, 1000： 兑换福利
        int64 item_scarcity = 2 [(gogoproto.jsontag) = "item_scarcity", (gogoproto.moretags) = 'form:"item_scarcity"', json_name = "item_scarcity"];
        CardItem card_item = 3 [(gogoproto.jsontag) = "card_item", (gogoproto.moretags) = 'form:"card_item"', json_name = "card_item"];
        message CardItem {
            // 当 item_type 为 1 时，使用此卡牌信息字段
            // 卡牌ID
            int64 card_type_id = 3 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id"', json_name = "card_type_id"];
            // 卡牌名
            string card_name = 4 [(gogoproto.jsontag) = "card_name", (gogoproto.moretags) = 'form:"card_name"', json_name = "card_name"];
            // 图片地址
            string card_img = 5 [(gogoproto.jsontag) = "card_img", (gogoproto.moretags) = 'form:"card_img"', json_name = "card_img"];
            // 卡牌类型(枚举值 1图片 2视频)
            int64 card_type = 6 [(gogoproto.jsontag) = "card_type", (gogoproto.moretags) = 'form:"card_type"', json_name = "card_type"];
            // 拥有的卡片实体ID列表，为空表示未拥有
            repeated CardIdItem card_id_list = 7 [(gogoproto.jsontag) = "card_id_list", (gogoproto.moretags) = 'form:"card_id_list"', json_name = "card_id_list"];
            // 卡片总发行量（活动结束后出现）
            int64 total_cnt = 8 [(gogoproto.jsontag) = "total_cnt", (gogoproto.moretags) = 'form:"total_cnt"', json_name = "total_cnt"];
            // 卡片总发行量（活动结束后出现）
            string total_cnt_show = 9 [(gogoproto.jsontag) = "total_cnt_show", (gogoproto.moretags) = 'form:"total_cnt_show"', json_name = "total_cnt_show"];
            // 卡片持有率, 前端展示万分比，预约期前端不展示
            int64 holding_rate = 10 [(gogoproto.jsontag) = "holding_rate", (gogoproto.moretags) = 'form:"holding_rate"', json_name = "holding_rate"];
            // 视频地址列表
            repeated string video_list = 11 [(gogoproto.jsontag) = "video_list", (gogoproto.moretags) = 'form:"video_list"', json_name = "video_list"];
            // 是否为陀螺仪素材卡牌  （0否 1是）
            int64 is_physical_orientation = 12 [(gogoproto.jsontag) = "is_physical_orientation", (gogoproto.moretags) = 'form:"is_physical_orientation"', json_name = "is_physical_orientation"];
            // 卡牌稀缺度枚举
            int64 card_scarcity = 13 [(gogoproto.jsontag) = "card_scarcity", (gogoproto.moretags) = 'form:"card_scarcity"', json_name = "card_scarcity"];
            // 是否为无声视频 0否 1是
            int64 is_mute = 14 [(gogoproto.jsontag) = "is_mute", (gogoproto.moretags) = 'form:"is_mute"', json_name = "is_mute"];
            // 宽
            int64 width = 15 [(gogoproto.jsontag) = "width", (gogoproto.moretags) = 'form:"width"', json_name = "width"];
            // 高
            int64 height = 16 [(gogoproto.jsontag) = "height", (gogoproto.moretags) = 'form:"height"', json_name = "height"];
            // 卡牌描述文本，如：DLC圣诞限定款
            string card_ext_text = 17 [(gogoproto.jsontag) = "card_ext_text", (gogoproto.moretags) = 'form:"card_ext_text"', json_name = "card_ext_text"];
            // 图片下载地址
            string card_img_download = 18 [(gogoproto.jsontag) = "card_img_download", (gogoproto.moretags) = 'form:"card_img_download"', json_name = "card_img_download"];
            // 视频下载地址列表
            repeated string video_list_download = 19 [(gogoproto.jsontag) = "video_list_download", (gogoproto.moretags) = 'form:"video_list_download"', json_name = "video_list_download"];
            // 字幕链接
            string subtitles_url = 20 [(gogoproto.jsontag) = "subtitles_url", (gogoproto.moretags) = 'form:"subtitles_url"', json_name = "subtitles_url"];
            // 播放设置
            Play play = 21 [(gogoproto.jsontag) = "play", (gogoproto.moretags) = 'form:"play"', json_name = "play"];
            // 角标展示
            TagInfo tag = 22 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
            // 卡牌子类型
            int64 card_sub_type = 23 [(gogoproto.jsontag) = "card_sub_type", (gogoproto.moretags) = 'form:"card_sub_type"', json_name = "card_sub_type"];
            // 是否限量卡牌,1=是，0=否
            int64 is_limited_card = 24 [(gogoproto.jsontag) = "is_limited_card", (gogoproto.moretags) = 'form:"is_limited_card"', json_name = "is_limited_card"];
            CardStockInfo stock_info = 25 [(gogoproto.jsontag) = "stock_info", (gogoproto.moretags) = 'form:"stock_info"', json_name = "stock_info"];
            // 卡牌额外信息
            CardTypeMetaInfo meta_info = 26 [(gogoproto.moretags) = 'form:"meta_info"', (gogoproto.jsontag) = 'meta_info', json_name = 'meta_info'];
        }
    }
}

// 背包列表
// 主客态简单来说就是 uid拿着手机看ruid的图鉴页
message DLCAssetBagReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 奖池ID
    int64 lottery_id = 2 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 主态uid（为0表示查看本次活动图鉴）
    int64 ruid = 3 [(gogoproto.jsontag) = "ruid", (gogoproto.moretags) = 'form:"ruid"', json_name = "ruid"];
    // 客态uid（为0表示当前用户未登录）
    int64 uid = 4 [(gogoproto.jsontag) = "uid", (gogoproto.moretags) = 'form:"uid"', json_name = "uid"];
    // ip
    string ip = 5 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
    // 用户持有状态过滤，0：所有  1：已拥有 2：未拥有
    int64 owned_status = 6 [(gogoproto.jsontag) = "owned_status", (gogoproto.moretags) = 'form:"owned_status"', json_name = "owned_status"];
    // 业务传输trace_id
    string trace_id = 7 [(gogoproto.jsontag) = "trace_id", (gogoproto.moretags) = 'form:"trace_id"', json_name = "trace_id"];
    // 设备信息
    DeviceInfo device = 8;
}

message DLCAssetBagResp {
    // 用户名
    string uname = 1 [(gogoproto.jsontag) = "uname", (gogoproto.moretags) = 'form:"uname"', json_name = "uname"];
    // 用户头像
    string uface = 2 [(gogoproto.jsontag) = "uface", (gogoproto.moretags) = 'form:"uface"', json_name = "uface"];
    // 图鉴页图片
    string act_y_img = 3 [(gogoproto.jsontag) = "act_y_img", (gogoproto.moretags) = 'form:"act_y_img"', json_name = "act_y_img"];
    // 卡牌总数
    int64 total_item_cnt = 4 [(gogoproto.jsontag) = "total_item_cnt", (gogoproto.moretags) = 'form:"total_item_cnt"', json_name = "total_item_cnt"];
    // 已拥有的卡牌总数
    int64 owned_item_cnt = 5 [(gogoproto.jsontag) = "owned_item_cnt", (gogoproto.moretags) = 'form:"owned_item_cnt"', json_name = "owned_item_cnt"];
    // 卡牌信息
    repeated CardItemInfoInBag item_list = 6 [(gogoproto.jsontag) = "item_list", (gogoproto.moretags) = 'form:"item_list"', json_name = "item_list"];
    // 收集度任务奖励信息
    repeated CollectRedeemInfo collect_list = 7 [(gogoproto.jsontag) = "collect_list", (gogoproto.moretags) = 'form:"collect_list"', json_name = "collect_list"];
    // 奖池simple列表：奖池数>1时，包含【全部奖池】这一项
    repeated SimpleLottery lottery_simple_list = 8 [(gogoproto.jsontag) = "lottery_simple_list", (gogoproto.moretags) = 'form:"lottery_simple_list"', json_name = "lottery_simple_list"];
    // 奖池简单列举，用于背包页顶部选择
    message SimpleLottery {
        // lottery_name【全部奖池】时，lottery_id=0
        int64 lottery_id = 1 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
        string lottery_name = 2 [(gogoproto.jsontag) = "lottery_name", (gogoproto.moretags) = 'form:"lottery_name"', json_name = "lottery_name"];
    }
    // 故障提示toast （接口正常 非核心参数有误）
    string fault_toast = 9 [(gogoproto.jsontag) = "fault_toast", (gogoproto.moretags) = 'form:"fault_toast"', json_name = "fault_toast"];
    // 是否展示分解卡牌入口 0不展示 1展示
    int64 show_decompose_entrance = 10 [(gogoproto.jsontag) = "show_decompose_entrance", (gogoproto.moretags) = 'form:"show_decompose_entrance"', json_name = "show_decompose_entrance"];
    // 待领取标记 用于显示转赠的红点
    int64 receive_sign = 11 [(gogoproto.jsontag) = "receive_sign", (gogoproto.moretags) = 'form:"receive_sign"', json_name = "receive_sign"];
}

message DLCAssetBagStreamResp {
    // 目前只对 CardItemInfoInBag 数组进行切分传输
    DLCAssetBagResp resp = 1 [(gogoproto.jsontag) = "resp", (gogoproto.moretags) = 'form:"resp"', json_name = "resp"];
    // stream参数标记位:only_item>0 说明只包含CardItemInfoInBag信息
    int64 only_item = 2 [(gogoproto.jsontag) = "only_item", (gogoproto.moretags) = 'form:"only_item"', json_name = "only_item"];
}

message CardItemInfoInBag {
    // 奖品类型， 1:数字周边卡牌
    int64 item_type = 1 [(gogoproto.jsontag) = "item_type", (gogoproto.moretags) = 'form:"item_type"', json_name = "item_type"];
    // 稀缺度信息，  10： 普通款， 30： 隐藏款
    int64 item_scarcity = 2 [(gogoproto.jsontag) = "item_scarcity", (gogoproto.moretags) = 'form:"item_scarcity"', json_name = "item_scarcity"];
    CardItem card_item = 3 [(gogoproto.jsontag) = "card_item", (gogoproto.moretags) = 'form:"card_item"', json_name = "card_item"];
    message CardItem {
        // 当 item_type 为 1 时，使用此卡牌信息字段
        // 卡牌ID
        int64 card_type_id = 1 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id"', json_name = "card_type_id"];
        // 卡牌名
        string card_name = 2 [(gogoproto.jsontag) = "card_name", (gogoproto.moretags) = 'form:"card_name"', json_name = "card_name"];
        // 图片地址
        string card_img = 3 [(gogoproto.jsontag) = "card_img", (gogoproto.moretags) = 'form:"card_img"', json_name = "card_img"];
        // 卡牌类型(枚举值 1图片 2视频)
        int64 card_type = 4 [(gogoproto.jsontag) = "card_type", (gogoproto.moretags) = 'form:"card_type"', json_name = "card_type"];
        // 拥有的卡片实体ID列表，为空表示未拥有
        repeated CardIdItem card_id_list = 5 [(gogoproto.jsontag) = "card_id_list", (gogoproto.moretags) = 'form:"card_id_list"', json_name = "card_id_list"];
        // 卡片总发行量（活动结束后出现）
        int64 total_cnt = 6 [(gogoproto.jsontag) = "total_cnt", (gogoproto.moretags) = 'form:"total_cnt"', json_name = "total_cnt"];
        // 卡片总发行量（活动结束后出现）
        string total_cnt_show = 7 [(gogoproto.jsontag) = "total_cnt_show", (gogoproto.moretags) = 'form:"total_cnt_show"', json_name = "total_cnt_show"];
        // 卡片持有率, 前端展示万分比，预约期前端不展示
        int64 holding_rate = 8 [(gogoproto.jsontag) = "holding_rate", (gogoproto.moretags) = 'form:"holding_rate"', json_name = "holding_rate"];
        // 视频地址列表
        repeated string video_list = 9 [(gogoproto.jsontag) = "video_list", (gogoproto.moretags) = 'form:"video_list"', json_name = "video_list"];
        // 是否为陀螺仪素材卡牌  （0否 1是）
        int64 is_physical_orientation = 10 [(gogoproto.jsontag) = "is_physical_orientation", (gogoproto.moretags) = 'form:"is_physical_orientation"', json_name = "is_physical_orientation"];
        // 卡牌稀缺度枚举
        int64 card_scarcity = 11 [(gogoproto.jsontag) = "card_scarcity", (gogoproto.moretags) = 'form:"card_scarcity"', json_name = "card_scarcity"];
        // 是否为无声视频 0否 1是
        int64 is_mute = 12 [(gogoproto.jsontag) = "is_mute", (gogoproto.moretags) = 'form:"is_mute"', json_name = "is_mute"];
        // 宽
        int64 width = 13 [(gogoproto.jsontag) = "width", (gogoproto.moretags) = 'form:"width"', json_name = "width"];
        // 高
        int64 height = 14 [(gogoproto.jsontag) = "height", (gogoproto.moretags) = 'form:"height"', json_name = "height"];
        // 卡牌描述文本，如：DLC圣诞限定款
        string card_ext_text = 15 [(gogoproto.jsontag) = "card_ext_text", (gogoproto.moretags) = 'form:"card_ext_text"', json_name = "card_ext_text"];
        // 图片下载地址
        string card_img_download = 16 [(gogoproto.jsontag) = "card_img_download", (gogoproto.moretags) = 'form:"card_img_download"', json_name = "card_img_download"];
        // 视频下载地址列表
        repeated string video_list_download = 17 [(gogoproto.jsontag) = "video_list_download", (gogoproto.moretags) = 'form:"video_list_download"', json_name = "video_list_download"];
        // 字幕链接
        string subtitles_url = 18 [(gogoproto.jsontag) = "subtitles_url", (gogoproto.moretags) = 'form:"subtitles_url"', json_name = "subtitles_url"];
        // 播放设置
        Play play = 19 [(gogoproto.jsontag) = "play", (gogoproto.moretags) = 'form:"play"', json_name = "play"];
        // 角标展示
        TagInfo tag = 20 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
        // 卡牌子类型
        int64 card_sub_type = 21 [(gogoproto.jsontag) = "card_sub_type", (gogoproto.moretags) = 'form:"card_sub_type"', json_name = "card_sub_type"];
        // 是否限量卡牌,1=是，0=否
        int64 is_limited_card = 22 [(gogoproto.jsontag) = "is_limited_card", (gogoproto.moretags) = 'form:"is_limited_card"', json_name = "is_limited_card"];
        CardStockInfo stock_info = 23 [(gogoproto.jsontag) = "stock_info", (gogoproto.moretags) = 'form:"stock_info"', json_name = "stock_info"];
        // 卡牌额外信息
        CardTypeMetaInfo meta_info = 24 [(gogoproto.moretags) = 'form:"meta_info"', (gogoproto.jsontag) = 'meta_info', json_name = 'meta_info'];
    }
}

message CardTypeIdItem {
    // 卡牌ID
    int64 card_type_id = 1 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id"', json_name = "card_type_id"];
    // 卡牌名
    string card_name = 2 [(gogoproto.jsontag) = "card_name", (gogoproto.moretags) = 'form:"card_name"', json_name = "card_name"];
    // 图片地址
    string card_img = 3 [(gogoproto.jsontag) = "card_img", (gogoproto.moretags) = 'form:"card_img"', json_name = "card_img"];
    // 卡牌类型(枚举值 1图片 2视频)
    int64 card_type = 4 [(gogoproto.jsontag) = "card_type", (gogoproto.moretags) = 'form:"card_type"', json_name = "card_type"];
    // 拥有的卡片实体ID列表，为空表示未拥有
    repeated CardIdItem card_id_list = 5 [(gogoproto.jsontag) = "card_id_list", (gogoproto.moretags) = 'form:"card_id_list"', json_name = "card_id_list"];
    // 卡片总发行量（活动结束后出现）
    int64 total_cnt = 6 [(gogoproto.jsontag) = "total_cnt", (gogoproto.moretags) = 'form:"total_cnt"', json_name = "total_cnt"];
    // 卡片总发行量（活动结束后出现）
    string total_cnt_show = 7 [(gogoproto.jsontag) = "total_cnt_show", (gogoproto.moretags) = 'form:"total_cnt_show"', json_name = "total_cnt_show"];
    // 卡片持有率, 前端展示万分比，预约期前端不展示
    int64 holding_rate = 8 [(gogoproto.jsontag) = "holding_rate", (gogoproto.moretags) = 'form:"holding_rate"', json_name = "holding_rate"];
    // 视频地址列表
    repeated string video_list = 9 [(gogoproto.jsontag) = "video_list", (gogoproto.moretags) = 'form:"video_list"', json_name = "video_list"];
    // 是否为陀螺仪素材卡牌  （0否 1是）
    int64 is_physical_orientation = 10 [(gogoproto.jsontag) = "is_physical_orientation", (gogoproto.moretags) = 'form:"is_physical_orientation"', json_name = "is_physical_orientation"];
    // 卡牌稀缺度枚举
    int64 card_scarcity = 11 [(gogoproto.jsontag) = "card_scarcity", (gogoproto.moretags) = 'form:"card_scarcity"', json_name = "card_scarcity"];
    // 是否为无声视频 0否 1是
    int64 is_mute = 12 [(gogoproto.jsontag) = "is_mute", (gogoproto.moretags) = 'form:"is_mute"', json_name = "is_mute"];
    // 宽
    int64 width = 13 [(gogoproto.jsontag) = "width", (gogoproto.moretags) = 'form:"width"', json_name = "width"];
    // 高
    int64 height = 14 [(gogoproto.jsontag) = "height", (gogoproto.moretags) = 'form:"height"', json_name = "height"];
}

message CardIdItem {
    // 卡片ID
    int64 card_id = 1 [(gogoproto.jsontag) = "card_id", (gogoproto.moretags) = 'form:"card_id"', json_name = "card_id"];
    // 卡片编号(补齐6位)
    string card_no = 2 [(gogoproto.jsontag) = "card_no", (gogoproto.moretags) = 'form:"card_no"', json_name = "card_no"];
    // 卡片状态（0未上链 1已上链 2转赠冻结 3转移中 4兑换中）
    int64 status = 3 [(gogoproto.jsontag) = "status", (gogoproto.moretags) = 'form:"status"', json_name = "status"];
    // 链上哈希
    string hash_code = 5 [(gogoproto.jsontag) = "hash_code", (gogoproto.moretags) = 'form:"hash_code"', json_name = "hash_code"];
    // 卡片权益
    CardRight card_right = 6 [(gogoproto.jsontag) = "card_right", (gogoproto.moretags) = 'form:"card_right"', json_name = "card_right"];
    // 卡片权益展示(前端专用)
    CardRightShow card_right_show = 7 [(gogoproto.jsontag) = "card_right_show", (gogoproto.moretags) = 'form:"card_right_show"', json_name = "card_right_show"];
    // 卡牌描述文本，如：DLC圣诞限定款
    // （逐步废弃，优先使用CardItem中的card_ext_text）
    string card_ext_text = 8 [(gogoproto.jsontag) = "card_ext_text", (gogoproto.moretags) = 'form:"card_ext_text"', json_name = "card_ext_text"];
    // 卡片编号颜色格式
    vas.common.FanNumColorFormat color_format = 9 [(gogoproto.jsontag) = "color_format", json_name = "color_format"];
    // 熔炼锁定状态 1未锁定 2已上锁 3锁死
    int64 smelt_lock_status = 10 [(gogoproto.jsontag) = "smelt_lock_status", json_name = "smelt_lock_status"];
    // 卡牌额外信息
    CardMetaInfo meta_info = 11 [(gogoproto.moretags) = 'form:"meta_info"', (gogoproto.jsontag) = 'meta_info', json_name = 'meta_info'];
}

// 卡片权益
message CardRight {
    // 是否具备头像权益 0否 1是
    int64 is_head_pic = 1 [(gogoproto.jsontag) = "is_head_pic", (gogoproto.moretags) = 'form:"is_head_pic"', json_name = "is_head_pic"];
    // 头像权益有效期 0表示永久 其余表示过期时间戳（yyyy-MM-dd 23:59:59）
    int64 head_pic_expire = 2 [(gogoproto.jsontag) = "head_pic_expire", (gogoproto.moretags) = 'form:"head_pic_expire"', json_name = "head_pic_expire"];
    // 是否具备空间头图权益 0否 1是
    int64 is_banner_bg = 3 [(gogoproto.jsontag) = "is_banner_bg", (gogoproto.moretags) = 'form:"is_banner_bg"', json_name = "is_banner_bg"];
    // 头像权益有效期 0表示永久 其余表示过期时间戳（yyyy-MM-dd 23:59:59）
    int64 banner_bg_expire = 4 [(gogoproto.jsontag) = "banner_bg_expire", (gogoproto.moretags) = 'form:"banner_bg_expire"', json_name = "banner_bg_expire"];
    // 卡牌转赠权益 0否 1是
    int64 is_transfer = 6 [(gogoproto.jsontag) = "is_transfer", (gogoproto.moretags) = 'form:"is_transfer"', json_name = "is_transfer"];
}
message CardRightShow {
    // 是否可设置头像 0否 1是 （超过限时时间则不可设置）
    int64 is_head_pic = 1 [(gogoproto.jsontag) = "is_head_pic", (gogoproto.moretags) = 'form:"is_head_pic"', json_name = "is_head_pic"];
    // 点击设置头像toast，空表示不弹toast
    string head_pic_toast = 2 [(gogoproto.jsontag) = "head_pic_toast", (gogoproto.moretags) = 'form:"head_pic_toast"', json_name = "head_pic_toast"];
    // 是否可设置空间头图 0否 1是 （超过限时时间则不可设置）
    int64 is_banner_bg = 3 [(gogoproto.jsontag) = "is_banner_bg", (gogoproto.moretags) = 'form:"is_banner_bg"', json_name = "is_banner_bg"];
    // 点击设置头图toast，空表示不弹toast
    string banner_bg_toast = 4 [(gogoproto.jsontag) = "banner_bg_toast", (gogoproto.moretags) = 'form:"banner_bg_toast"', json_name = "banner_bg_toast"];
    // 头像角标（为空则不展示角标）
    string head_tag_url = 5 [(gogoproto.jsontag) = "head_tag_url", (gogoproto.moretags) = 'form:"head_tag_url"', json_name = "head_tag_url"];
    // 头图角标（为空则不展示角标）
    string banner_tag_url = 6 [(gogoproto.jsontag) = "banner_tag_url", (gogoproto.moretags) = 'form:"banner_tag_url"', json_name = "banner_tag_url"];
    // 头像过期时间格式化字符串 2022-10-20
    string head_expire_format = 7 [(gogoproto.jsontag) = "head_expire_format", (gogoproto.moretags) = 'form:"head_expire_format"', json_name = "head_expire_format"];
    // 头图过期时间格式化字符串 2022-10-20
    string banner_expire_format = 8 [(gogoproto.jsontag) = "banner_expire_format", (gogoproto.moretags) = 'form:"banner_expire_format"', json_name = "banner_expire_format"];
    // 卡牌预览页 使用按钮角标
    string use_tag_url = 9 [(gogoproto.jsontag) = "use_tag_url", (gogoproto.moretags) = 'form:"use_tag_url"', json_name = "use_tag_url"];
    // 点击使用按钮toast，空表示不弹toast
    string use_toast = 10 [(gogoproto.jsontag) = "use_toast", (gogoproto.moretags) = 'form:"use_toast"', json_name = "use_toast"];
    // 编号权益可被设置的列表，目前被客户端使用在图鉴预览页
    repeated NumberSettingMenu number_menus = 11 [(gogoproto.jsontag) = "number_menus", (gogoproto.moretags) = 'form:"number_menus"', json_name = "number_menus"];
    message NumberSettingMenu {
        // 是否可被使用，1:可以，0:不可以
        int64 can_set = 1;
        // 标题
        string title = 2;
        // 关联的跳转链接
        string target_url = 3;
        // 1.跳动态卡片 2.跳评论背景、3 设置为闪屏、4 设置为小组件
        int64 jump_type = 4;
        // 非空则展示，目前仅设置为开屏需要，即【大会员专属】
        string tips = 5;
    }
}

message DlcCardRightUsageReq {
    // mid
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 收藏集id
    int64 act_id = 2 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 卡面id
    int64 card_type_id = 3 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id"', json_name = "card_type_id"];
    // 卡牌资产id
    int64 card_id = 4 [(gogoproto.jsontag) = "card_id", (gogoproto.moretags) = 'form:"card_id"', json_name = "card_id"];
    // 设备信息
    DeviceInfo device = 5;
    string ip = 6 [(gogoproto.moretags) = 'form:"ip"', (gogoproto.jsontag) = 'ip', json_name = 'ip'];
}

message DlcCardRightUsageResp {
    repeated UsageMenu list = 1 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
}

message UsageMenu {
    // 是否可被使用，1:可以，0:不可以
    int64 can_set = 1;
    // 标题
    string title = 2;
    // 关联的跳转链接
    string target_url = 3;
    // 1.跳动态卡片 2.跳评论背景、3 设置为闪屏、4 设置为小组件 5.头像 6.头图 7 壁纸
    int64 jump_type = 4;
    // 非空则展示，目前仅设置为开屏需要，即【大会员专属】
    Tips tips = 5;
    // 点击时弹出的toast 有toast就不跳转
    string click_toast = 6 [(gogoproto.jsontag) = "click_toast", (gogoproto.moretags) = 'form:"click_toast"', json_name = "click_toast"];
    // 菜单类型，用于替换jump_type枚举
    string action_type = 7 [(gogoproto.jsontag) = 'action_type', json_name = 'action_type'];
    // 额外数据
    map<string, string> data = 8 [(gogoproto.jsontag) = 'data', json_name = 'data'];

    message Tips {
        // 0下发内容为文案 1下发内容为图片链接
        int64 type = 1;
        // 文字/链接
        string value = 2;
    }
}

// 图鉴详情(陀螺仪类型卡牌专用)
message CardDetailReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 卡牌类型id
    int64 card_type_id = 2 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id" validate:"required"', json_name = "card_type_id"];
    // 用户id
    int64 mid = 3 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // ip
    string ip = 4 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
}
message CardDetailResp {
    // 卡牌信息
    nft.CardTypeInfo card_type_info = 1 [(gogoproto.jsontag) = "card_type_info", (gogoproto.moretags) = 'form:"card_type_info"', json_name = "card_type_info"];
    // 已拥有卡片信息
    repeated CardIdItem card_id_list = 2 [(gogoproto.jsontag) = "card_id_list", (gogoproto.moretags) = 'form:"card_id_list"', json_name = "card_id_list"];
}

message GetCardRightByCardIdReq {
    // 卡片id
    int64 card_id = 1 [(gogoproto.jsontag) = "card_id", (gogoproto.moretags) = 'form:"card_id" validate:"required"', json_name = "card_id"];
}

// 持有人列表（展示评论背景图，隐私设置下客态不可见头像昵称）
message HolderTopListReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 卡牌id
    int64 card_type_id = 2 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id" validate:"required"', json_name = "card_type_id"];
    // 当前登录用户id
    int64 mid = 3 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // ip
    string ip = 4 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
    // 页码
    int64 page = 5 [(gogoproto.jsontag) = "page", (gogoproto.moretags) = 'form:"page"', json_name = "page"];
    // 页数
    int64 size = 6 [(gogoproto.jsontag) = "size", (gogoproto.moretags) = 'form:"size"', json_name = "size"];
}
message HolderTopListResp {
    repeated RankItem list = 1 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
}
message RankItem {
    int64 uid = 1 [(gogoproto.jsontag) = "uid", (gogoproto.moretags) = 'form:"uid"', json_name = "uid"];
    // 昵称
    string uname = 2 [(gogoproto.jsontag) = "uname", (gogoproto.moretags) = 'form:"uname"', json_name = "uname"];
    // 头像
    string face = 3 [(gogoproto.jsontag) = "face", (gogoproto.moretags) = 'form:"face"', json_name = "face"];
    // 评论背景图
    string comment_pic = 4 [(gogoproto.jsontag) = "comment_pic", (gogoproto.moretags) = 'form:"comment_pic"', json_name = "comment_pic"];
    // 积分
    int64 score = 5 [(gogoproto.jsontag) = "score", (gogoproto.moretags) = 'form:"score"', json_name = "score"];
}

// 收藏家列表
message AllTopListReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 当前登录用户id
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // ip
    string ip = 3 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
    // 页码
    int64 page = 4 [(gogoproto.jsontag) = "page", (gogoproto.moretags) = 'form:"page"', json_name = "page"];
    // 页数
    int64 size = 5 [(gogoproto.jsontag) = "size", (gogoproto.moretags) = 'form:"size"', json_name = "size"];
}
message AllTopListResp {
    repeated RankItem list = 1 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
    // 活动名称
    string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
    // 活动图片
    string act_x_img = 3 [(gogoproto.jsontag) = "act_x_img", (gogoproto.moretags) = 'form:"act_x_img"', json_name = "act_x_img"];
    // 评论背景图
    string comment_pic = 4 [(gogoproto.jsontag) = "comment_pic", (gogoproto.moretags) = 'form:"comment_pic"', json_name = "comment_pic"];
    // 登录用户排名，从1开始，-1为未上榜
    int64 rank = 5 [(gogoproto.jsontag) = "rank", (gogoproto.moretags) = 'form:"rank"', json_name = "rank"];
    // 登陆用户积分
    int64 score = 6 [(gogoproto.jsontag) = "score", (gogoproto.moretags) = 'form:"score"', json_name = "score"];
    // 昵称
    string uname = 7 [(gogoproto.jsontag) = "uname", (gogoproto.moretags) = 'form:"uname"', json_name = "uname"];
    // 头像
    string face = 8 [(gogoproto.jsontag) = "face", (gogoproto.moretags) = 'form:"face"', json_name = "face"];
    // 是否展示积分 1:展示，0:不展示
    int64 show_score = 9 [(gogoproto.jsontag) = "show_score", (gogoproto.moretags) = 'form:"show_score"', json_name = "show_score"];
}

// 活动列表（个人空间入口）
message ActListReq {
    // 当前登录用户id
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 获取条数
    int64 page_size = 2 [(gogoproto.jsontag) = "page_size", (gogoproto.moretags) = 'form:"page_size"', json_name = "page_size"];
    // 偏移量(初始传0)
    int64 offset = 3 [(gogoproto.jsontag) = "offset", (gogoproto.moretags) = 'form:"offset"', json_name = "offset"];
    // up主id
    int64 up_uid = 4 [(gogoproto.jsontag) = "up_uid", (gogoproto.moretags) = 'form:"up_uid"', json_name = "up_uid"];
}
message ActListResp {
    // 活动列表
    repeated ActInfo list = 1 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
    // 分页偏移量（-1表示无下一页）
    int64 offset = 2 [(gogoproto.jsontag) = "offset", (gogoproto.moretags) = 'form:"offset"', json_name = "offset"];
    // 是否设置隐私(0否 1是)
    int64 is_private = 3 [(gogoproto.jsontag) = "is_private", (gogoproto.moretags) = 'form:"is_private"', json_name = "is_private"];
    // up主头像，隐私设置显示默认
    string up_face = 4 [(gogoproto.jsontag) = "up_face", (gogoproto.moretags) = 'form:"up_face"', json_name = "up_face"];
    // up主昵称，隐私设置显示默认
    string up_name = 5 [(gogoproto.jsontag) = "up_name", (gogoproto.moretags) = 'form:"up_name"', json_name = "up_name"];
    message ActInfo {
        // 活动ID
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 图鉴封面
        string act_x_img = 2 [(gogoproto.jsontag) = "act_x_img", (gogoproto.moretags) = 'form:"act_x_img"', json_name = "act_x_img"];
        // 活动封面
        string act_y_img = 3 [(gogoproto.jsontag) = "act_y_img", (gogoproto.moretags) = 'form:"act_y_img"', json_name = "act_y_img"];
        // 累积卡牌数量
        int64 card_cnt = 4 [(gogoproto.jsontag) = "card_cnt", (gogoproto.moretags) = 'form:"card_cnt"', json_name = "card_cnt"];
        // 评论背景图
        string commet_bg_pic = 5 [(gogoproto.jsontag) = "commet_bg_pic", (gogoproto.moretags) = 'form:"commet_bg_pic"', json_name = "commet_bg_pic"];
        // 活动名称
        string act_name = 6 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
        // 累计抽卡机会数量
        int64 left_chance = 7 [(gogoproto.jsontag) = "left_chance", (gogoproto.moretags) = 'form:"left_chance"', json_name = "left_chance"];
        // 提示文案  "已收集X张"（已无开卡机会的情况下） 或
        // "已收集X张，有X次开卡机会"（有开卡机会的的情况下）
        string show_msg = 8 [(gogoproto.jsontag) = "show_msg", (gogoproto.moretags) = 'form:"show_msg"', json_name = "show_msg"];
    }
}

// 活动详情（装扮商城入口）
message ActDetailReq {
    // 多个活动id
    repeated int64 act_ids = 1 [(gogoproto.jsontag) = "act_ids", (gogoproto.moretags) = 'form:"act_ids"', json_name = "act_ids"];
}
message ActDetailResp {
    // 活动信息
    map<int64, ActInfo> act_info_map = 1 [(gogoproto.jsontag) = "act_info_map", (gogoproto.moretags) = 'form:"act_info_map"', json_name = "act_info_map"];
    message ActInfo {
        // 活动ID
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 图鉴封面
        string act_x_img = 2 [(gogoproto.jsontag) = "act_x_img", (gogoproto.moretags) = 'form:"act_x_img"', json_name = "act_x_img"];
        // 活动封面
        string act_y_img = 3 [(gogoproto.jsontag) = "act_y_img", (gogoproto.moretags) = 'form:"act_y_img"', json_name = "act_y_img"];
        // 售卖价格（金瓜子 1000:1）
        int64 sale_price = 4 [(gogoproto.jsontag) = "sale_price", (gogoproto.moretags) = 'form:"sale_price"', json_name = "sale_price"];
        // 活动状态 （0:未开始 1:预约中 2:进行中 3:已结束）
        int64 act_status = 5 [(gogoproto.jsontag) = "act_status", (gogoproto.moretags) = 'form:"act_status"', json_name = "act_status"];
        // 活动名称
        string act_name = 6 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
        // 已售订单数量
        int64 sale_amount = 7 [(gogoproto.jsontag) = "sale_amount", (gogoproto.moretags) = 'form:"sale_amount"', json_name = "sale_amount"];
        // 预约人数
        int64 book_num = 9 [(gogoproto.jsontag) = "book_num", (gogoproto.moretags) = 'form:"book_num"', json_name = "book_num"];
    }
}

// 活动预演态查询
message ActIsPreviewReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}
message ActIsPreviewResp {
    // 预演态标识 （0非预演态 1预演态）
    int64 is_preview = 1 [(gogoproto.jsontag) = "is_preview", (gogoproto.moretags) = 'form:"is_preview"', json_name = "is_preview"];
}
// 活动当前时间
message ActCurTimeReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
}
message ActCurTimeResp {
    // 当前时间
    int64 cur_time = 1 [(gogoproto.jsontag) = "cur_time", (gogoproto.moretags) = 'form:"cur_time"', json_name = "cur_time"];
}

// 活动配置的卡牌种类数量请求
message ActCardTypeCountReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
}

// 活动配置的卡牌种类数量返回值
message ActCardTypeCountResp {
    // 去重后的card_type_id总数
    int64 count = 1 [(gogoproto.jsontag) = "count", (gogoproto.moretags) = 'form:"count"', json_name = "count"];
    //
    map<int64, int64> card_type_id_info = 2 [(gogoproto.jsontag) = "card_type_id_info", (gogoproto.moretags) = 'form:"card_type_id_info"', json_name = "card_type_id_info"];
}

// 卡牌信息
message CardInfoReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 卡牌类型id
    int64 card_type_id = 2 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id" validate:"required"', json_name = "card_type_id"];
    // 用户id
    int64 mid = 3 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // ip
    string ip = 4 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
}

// 支付回调
message CommonDeliveryReq {
    // 用户id
    int64 uid = 1 [(gogoproto.jsontag) = "uid", (gogoproto.moretags) = 'form:"uid" validate:"required"', json_name = "uid"];
    // 主播/UP主id
    int64 ruid = 2 [(gogoproto.jsontag) = "ruid", (gogoproto.moretags) = 'form:"ruid"', json_name = "ruid"];
    // 商品ID
    int64 goods_id = 3 [(gogoproto.jsontag) = "goods_id", (gogoproto.moretags) = 'form:"goods_id"', json_name = "goods_id"];
    // 商品数量
    int64 goods_num = 4 [(gogoproto.jsontag) = "goods_num", (gogoproto.moretags) = 'form:"goods_num"', json_name = "goods_num"];
    // 平台  ios/android/pc/h5
    string platform = 5 [(gogoproto.jsontag) = "platform", (gogoproto.moretags) = 'form:"platform"', json_name = "platform"];
    // mobile_app
    string mobile_app = 6 [(gogoproto.jsontag) = "mobile_app", (gogoproto.moretags) = 'form:"mobile_app"', json_name = "mobile_app"];
    // 订单金额
    int64 total_price = 7 [(gogoproto.jsontag) = "total_price", (gogoproto.moretags) = 'form:"total_price"', json_name = "total_price"];
    // 订单号
    string order_id = 8 [(gogoproto.jsontag) = "order_id", (gogoproto.moretags) = 'form:"order_id"', json_name = "order_id"];
    // 订单创建时间
    string order_create_time = 9 [(gogoproto.jsontag) = "order_create_time", (gogoproto.moretags) = 'form:"order_create_time"', json_name = "order_create_time"];
    // 业务透传字段
    string biz_extra = 10 [(gogoproto.jsontag) = "biz_extra", (gogoproto.moretags) = 'form:"biz_extra"', json_name = "biz_extra"];
    // 业务类型 101表示集卡业务
    int64 context_type = 11 [(gogoproto.jsontag) = "context_type", (gogoproto.moretags) = 'form:"context_type"', json_name = "context_type"];
    // 业务id
    string context_id = 12 [(gogoproto.jsontag) = "context_id", (gogoproto.moretags) = 'form:"context_id"', json_name = "context_id"];
}
message CommonDeliveryResp {
    // 发货状态
    // 0: 未发货(订单会重试发货)
    // 100: 发货成功
    // 101: 发货失败(会退款)
    // 102: 发货失败(不会退款)
    int64 status = 1 [(gogoproto.jsontag) = "status", (gogoproto.moretags) = 'form:"status"', json_name = "status"];
}

// 用户实名校验接口
message RealNameVerifyReq {
    // 当前登录用户id
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 登录ip
    string ip = 2 [(gogoproto.jsontag) = "ip", (gogoproto.moretags) = 'form:"ip"', json_name = "ip"];
}
message RealNameVerifyResp {
    // 错误码 0表示不通过实名认证 1表示通过实名认证
    int64 is_pass = 1 [(gogoproto.jsontag) = "is_pass", (gogoproto.moretags) = 'form:"is_pass"', json_name = "is_pass"];
    // 是否满足年龄限制周岁 0:小于14周岁 1：已满14岁 2：未知（未通过实名认证 或
    // 证件信息不能判别是否成年等）
    int64 age_level = 2 [(gogoproto.jsontag) = "age_level", (gogoproto.moretags) = 'form:"age_level"', json_name = "age_level"];
}

message GetDLCShortURlReq {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    string url = 2 [(gogoproto.jsontag) = "url", (gogoproto.moretags) = 'form:"url"', json_name = "url"];
}

message GetDLCShortURlResp {
    string short_url = 1 [(gogoproto.jsontag) = "short_url", (gogoproto.moretags) = 'form:"short_url"', json_name = "short_url"];
    string qrcode_url = 2 [(gogoproto.jsontag) = "qrcode_url", (gogoproto.moretags) = 'form:"qrcode_url"', json_name = "qrcode_url"];
}

message GetActLotteryItemListReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
}

message GetActLotteryItemListResp {
    // 奖池单卡奖励信息信息
    map<int64, LotteryItemList> list = 1 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
}

message LotteryItemList {
    repeated LotteryItemInfo ItemList = 2;

    message LotteryItemInfo {
        int64 ItemId = 1 [(gogoproto.jsontag) = "item_id", (gogoproto.moretags) = 'form:"item_id"', json_name = "item_id"];
        int64 Weight = 2 [(gogoproto.jsontag) = "weight", (gogoproto.moretags) = 'form:"weight"', json_name = "weight"];
        int64 IsNew = 3 [(gogoproto.jsontag) = "is_new", (gogoproto.moretags) = 'form:"is_new"', json_name = "is_new"];
        int64 IsUp = 4 [(gogoproto.jsontag) = "is_up", (gogoproto.moretags) = 'form:"is_up"', json_name = "is_up"];
    }
}

message GetAllActIdReq {}

message GetAllActIdResp {
    repeated int64 DlcActId = 1 [(gogoproto.jsontag) = "dlc_act_id", (gogoproto.moretags) = "dlc_act_id", json_name = "dlc_act_id"];
    repeated int64 NftActId = 2 [(gogoproto.jsontag) = "nft_act_id", (gogoproto.moretags) = "nft_act_id", json_name = "nft_act_id"];
}

message ListActForGarbFeedReq {}

message ListActForGarbFeedResp {
    repeated ActInfo act_info_list = 1 [(gogoproto.jsontag) = "act_info_list", (gogoproto.moretags) = 'form:"act_info_list"', json_name = "act_info_list"];
    message ActInfo {
        // 活动ID
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 活动名称
        string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
        // 活动状态 （0:未开始 1:预约中 2:进行中 3:已结束）
        int64 act_status = 3 [(gogoproto.jsontag) = "act_status", (gogoproto.moretags) = 'form:"act_status"', json_name = "act_status"];
        // 售卖价格 单位：厘
        int64 sale_price = 4 [(gogoproto.jsontag) = "sale_price", (gogoproto.moretags) = 'form:"sale_price"', json_name = "sale_price"];
        // 销量
        int64 sale_amount = 5 [(gogoproto.jsontag) = "sale_amount", (gogoproto.moretags) = 'form:"sale_amount"', json_name = "sale_amount"];
        // 预约人数
        int64 book_num = 6 [(gogoproto.jsontag) = "book_num", (gogoproto.moretags) = 'form:"book_num"', json_name = "book_num"];
        // 上新时间
        int64 new_arrival_time = 7 [(gogoproto.jsontag) = "new_arrival_time", (gogoproto.moretags) = 'form:"new_arrival_time"', json_name = "new_arrival_time"];
        // 装扮分组id
        int64 suit_group_id = 8 [(gogoproto.jsontag) = "suit_group_id", (gogoproto.moretags) = 'form:"suit_group_id"', json_name = "suit_group_id"];
        // 活动封面
        string image_cover = 9 [(gogoproto.jsontag) = "image_cover", (gogoproto.moretags) = 'form:"image_cover"', json_name = "image_cover"];
        // 奖池id
        int64 lottery_id = 10 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
        // 奖池类型
        int64 lottery_type = 11 [(gogoproto.jsontag) = "lottery_type", (gogoproto.moretags) = 'form:"lottery_type"', json_name = "lottery_type"];
        // 奖池销量
        int64 lottery_sale_amount = 12 [(gogoproto.jsontag) = "lottery_sale_amount", (gogoproto.moretags) = 'form:"lottery_sale_amount"', json_name = "lottery_sale_amount"];
        // 活动开始预热时间
        int64 act_pre_start_time = 13 [(gogoproto.jsontag) = "act_pre_start_time", (gogoproto.moretags) = 'form:"act_pre_start_time"', json_name = "act_pre_start_time"];
        // 活动开始时间
        int64 act_start_time = 14 [(gogoproto.jsontag) = "act_start_time", (gogoproto.moretags) = 'form:"act_start_time"', json_name = "act_start_time"];
    }
}

message ListActForGarbSearchReq {
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    repeated ActInfo act_info_list = 2 [(gogoproto.jsontag) = "act_info_list", (gogoproto.moretags) = 'form:"act_info_list"', json_name = "act_info_list"];
    message ActInfo {
        // 活动ID
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 奖池id
        int64 lottery_id = 2 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    }
}

message ListActForGarbSearchResp {
    repeated ActInfo act_info_list = 1 [(gogoproto.jsontag) = "act_info_list", (gogoproto.moretags) = 'form:"act_info_list"', json_name = "act_info_list"];
    message ActInfo {
        // 活动ID
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 活动名称
        string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
        // 活动状态 （0:未开始 1:预约中 2:进行中 3:已结束）
        int64 act_status = 3 [(gogoproto.jsontag) = "act_status", (gogoproto.moretags) = 'form:"act_status"', json_name = "act_status"];
        // 售卖价格 单位：厘
        int64 sale_price = 4 [(gogoproto.jsontag) = "sale_price", (gogoproto.moretags) = 'form:"sale_price"', json_name = "sale_price"];
        // 销量
        int64 sale_amount = 5 [(gogoproto.jsontag) = "sale_amount", (gogoproto.moretags) = 'form:"sale_amount"', json_name = "sale_amount"];
        // 预约人数
        int64 book_num = 6 [(gogoproto.jsontag) = "book_num", (gogoproto.moretags) = 'form:"book_num"', json_name = "book_num"];
        // 上新时间
        int64 new_arrival_time = 7 [(gogoproto.jsontag) = "new_arrival_time", (gogoproto.moretags) = 'form:"new_arrival_time"', json_name = "new_arrival_time"];
        // 装扮分组id
        int64 suit_group_id = 8 [(gogoproto.jsontag) = "suit_group_id", (gogoproto.moretags) = 'form:"suit_group_id"', json_name = "suit_group_id"];
        // 活动封面
        string image_cover = 9 [(gogoproto.jsontag) = "image_cover", (gogoproto.moretags) = 'form:"image_cover"', json_name = "image_cover"];
        // 奖池id
        int64 lottery_id = 10 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
        // 奖池类型
        int64 lottery_type = 11 [(gogoproto.jsontag) = "lottery_type", (gogoproto.moretags) = 'form:"lottery_type"', json_name = "lottery_type"];
        // 奖池销量
        int64 lottery_sale_amount = 12 [(gogoproto.jsontag) = "lottery_sale_amount", (gogoproto.moretags) = 'form:"lottery_sale_amount"', json_name = "lottery_sale_amount"];
        // 活动开始预热时间
        int64 act_pre_start_time = 13 [(gogoproto.jsontag) = "act_pre_start_time", (gogoproto.moretags) = 'form:"act_pre_start_time"', json_name = "act_pre_start_time"];
        // 活动开始时间
        int64 act_start_time = 14 [(gogoproto.jsontag) = "act_start_time", (gogoproto.moretags) = 'form:"act_start_time"', json_name = "act_start_time"];
        // 折扣价格 单位：厘，由于是定制接口，该字段需要和is_vip一起才能判断能否使用折扣
        int64 discount_price = 15 [(gogoproto.jsontag) = "discount_price", (gogoproto.moretags) = 'form:"discount_price"', json_name = "discount_price"];
        // 折扣文案
        string discount_tag = 16 [(gogoproto.jsontag) = "discount_tag", (gogoproto.moretags) = 'form:"discount_tag"', json_name = "discount_tag"];
        bool is_vip = 17 [(gogoproto.jsontag) = "is_vip", (gogoproto.moretags) = 'form:"is_vip"', json_name = "is_vip"];
        // 免费配置 0-否 1-是
        int64 free = 18 [(gogoproto.jsontag) = "free", (gogoproto.moretags) = 'form:"free"', json_name = "free"];
        // 限售模式，1=永久奖池，2=限时奖池，3=限量奖池
        int64 sale_mode = 19 [(gogoproto.jsontag) = "sale_model", (gogoproto.moretags) = 'form:"sale_model"', json_name = "sale_model"];
        // 剩余奖池限量卡牌总库存
        int64 surplus_stock = 20 [(gogoproto.jsontag) = "surplus_stock", (gogoproto.moretags) = 'form:"surplus_stock"', json_name = "surplus_stock"];
        // 开始时间戳
        int64 sale_start_time = 21 [(gogoproto.jsontag) = "sale_start_time", (gogoproto.moretags) = 'form:"sale_start_time"', json_name = "sale_start_time"];
        // 结束时间戳
        int64 sale_end_time = 22 [(gogoproto.jsontag) = "sale_end_time", (gogoproto.moretags) = 'form:"sale_end_time"', json_name = "sale_end_time"];
    }
}

message CollectRedeemListFrontendReq {
    // mid
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"', json_name = "mid"];
    // 活动ID
    int64 act_id = 2 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
}
message CollectRedeemListFrontendResp {
    // 收集奖励详情列表
    repeated BasicInfoResp.CollectRedeemInfo collect_infos = 1 [(gogoproto.jsontag) = "collect_infos", (gogoproto.moretags) = 'form:"collect_infos"', json_name = "collect_infos"];
}

message CollectRedeemDetailFrontReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.moretags) = 'form:"act_id"'];
    // 收集度奖励id
    int64 collect_id = 2 [(gogoproto.moretags) = 'form:"collect_id"'];
    // mid
    int64 mid = 3 [(gogoproto.moretags) = 'form:"mid"'];
}

message CollectRedeemExpressReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.moretags) = 'form:"act_id"'];
    // 收集度奖励id
    int64 collect_id = 2 [(gogoproto.moretags) = 'form:"collect_id"'];
    // mid
    int64 mid = 3 [(gogoproto.moretags) = 'form:"mid"'];
}

message CollectRedeemExpressResp {
    ExpressInfo express_info = 1 [(gogoproto.jsontag) = "express_info", (gogoproto.moretags) = 'form:"express_info"', json_name = "express_info"];
    int64 submit_time = 2 [(gogoproto.jsontag) = "submit_time", (gogoproto.moretags) = 'form:"submit_time"', json_name = "submit_time"];
}

message CollectRedeemDetailFrontResp {
    // 收集奖励id
    int64 collect_id = 1 [(gogoproto.jsontag) = "collect_id", (gogoproto.moretags) = 'form:"collect_id"', json_name = "collect_id"];
    // 收集奖励任务的开始时间
    int64 start_time = 2 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
    // 收集奖励任务的结束时间
    int64 end_time = 3 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
    // 领取条件文案
    string redeem_text = 4 [(gogoproto.jsontag) = "redeem_text", (gogoproto.moretags) = 'form:"redeem_text"', json_name = "redeem_text"];
    // cdk兑换码
    string cdk = 5 [(gogoproto.jsontag) = "cdk", (gogoproto.moretags) = 'form:"cdk"', json_name = "cdk"];
    // 兑换须知：实物奖励
    string exchange_notice = 6 [(gogoproto.jsontag) = "exchange_notice", (gogoproto.moretags) = 'form:"exchange_notice"', json_name = "exchange_notice"];
    // 收集奖励的物品类型 1:数字周边卡牌 2：表情包， 3：头像挂件，4：装扮永久套装, 7：观影券 8: cdk 9：实物奖励 10:限时装扮散件
    int64 redeem_item_type = 7 [(gogoproto.jsontag) = "redeem_item_type", (gogoproto.moretags) = 'form:"redeem_item_type"', json_name = "redeem_item_type"];
    // 收集奖励的物品id
    string redeem_item_id = 8 [(gogoproto.jsontag) = "redeem_item_id", (gogoproto.moretags) = 'form:"redeem_item_id"', json_name = "redeem_item_id"];
    // 收集奖励的物品名称
    string redeem_item_name = 9 [(gogoproto.jsontag) = "redeem_item_name", (gogoproto.moretags) = 'form:"redeem_item_name"', json_name = "redeem_item_name"];
    // 收集奖励的物品图片
    string redeem_item_image = 10 [(gogoproto.jsontag) = "redeem_item_image", (gogoproto.moretags) = 'form:"redeem_item_image"', json_name = "redeem_item_image"];
    // 已收集的数量
    int64 owned_item_amount = 11 [(gogoproto.jsontag) = "owned_item_amount", (gogoproto.moretags) = 'form:"owned_item_amount"', json_name = "owned_item_amount"];
    // 收集奖励要求的数量
    int64 require_item_amount = 12 [(gogoproto.jsontag) = "require_item_amount", (gogoproto.moretags) = 'form:"require_item_amount"', json_name = "require_item_amount"];
    // 已兑换次数
    int64 has_redeemed_cnt = 13 [(gogoproto.jsontag) = "has_redeemed_cnt", (gogoproto.moretags) = 'form:"has_redeemed_cnt"', json_name = "has_redeemed_cnt"];
    // 是否永久生效 0-否，1-是
    int64 effective_forever = 14 [(gogoproto.jsontag) = "effective_forever", (gogoproto.moretags) = 'form:"effective_forever"', json_name = "effective_forever"];
    // 收集奖励的物品图片下载链接
    string redeem_item_image_download = 15 [(gogoproto.jsontag) = "redeem_item_image_download", (gogoproto.moretags) = 'form:"redeem_item_image_download"', json_name = "redeem_item_image_download"];
    // 收集奖励的物品图片下载链接
    BasicInfoResp.CollectRedeemInfo.CardItem card_item = 16 [(gogoproto.jsontag) = "card_item", (gogoproto.moretags) = 'form:"card_item"', json_name = "card_item"];
    // 道具跳转链接
    string jump_url = 17 [(gogoproto.jsontag) = "jump_url", (gogoproto.moretags) = 'form:"jump_url"', json_name = "jump_url"];
    // 领取条件类型,scarcity=稀有度，card_type_id=指定卡牌，lottery_num=抽数，custom=自定义
    string redeem_cond_type = 18 [(gogoproto.jsontag) = "redeem_cond_type", (gogoproto.moretags) = 'form:"redeem_cond_type"', json_name = "redeem_cond_type"];
    // 剩余库存：默认传-1
    string remain_stock = 19 [(gogoproto.jsontag) = "remain_stock", (gogoproto.moretags) = 'form:"remain_stock"', json_name = "remain_stock"];
    // 全部库存：默认传-1
    string total_stock = 20 [(gogoproto.jsontag) = "total_stock", (gogoproto.moretags) = 'form:"total_stock"', json_name = "total_stock"];
    // 排行榜奖励用
    RankInfo rank_info = 21 [(gogoproto.jsontag) = "rank_info", (gogoproto.moretags) = 'form:"rank_info"', json_name = "rank_info"];
    message RankInfo {
        // 起始排名 包含
        int64 from = 1 [(gogoproto.moretags) = 'form:"from"', (gogoproto.jsontag) = 'from', json_name = 'from'];
        // 结束排名 包含
        int64 to = 2 [(gogoproto.moretags) = 'form:"to"', (gogoproto.jsontag) = 'to', json_name = 'to'];
        // 我的当前排名
        int64 my_rank = 3 [(gogoproto.moretags) = 'form:"my_rank"', (gogoproto.jsontag) = 'my_rank', json_name = 'my_rank'];
        // 截止时间戳
        int64 deadline = 4 [(gogoproto.moretags) = 'form:"deadline"', (gogoproto.jsontag) = 'deadline', json_name = 'deadline'];
    }
}

message GetUpChainStatusByActIdReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}

message GetUpChainStatusBySpuIdReq {
    // 活动ID
    int64 spu_id = 1 [(gogoproto.jsontag) = "spu_id", (gogoproto.moretags) = 'form:"spu_id"', json_name = "spu_id"];
}

message Play {
    // 播放进度总时长 单位ms
    int64 duration = 1 [(gogoproto.jsontag) = "duration", (gogoproto.moretags) = 'form:"duration"', json_name = "duration"];
    // 试播时长 单位毫秒
    int64 trial_duration = 2 [(gogoproto.jsontag) = "trial_duration", (gogoproto.moretags) = 'form:"trial_duration"', json_name = "trial_duration"];
    // 是否播放字幕
    bool show_subtitles = 3 [(gogoproto.jsontag) = "show_subtitles", (gogoproto.moretags) = 'form:"show_subtitles"', json_name = "show_subtitles"];
    // 是否展示进度条
    bool show_progress_bar = 4 [(gogoproto.jsontag) = "show_progress_bar", (gogoproto.moretags) = 'form:"show_progress_bar"', json_name = "show_progress_bar"];
}

message TagInfo {
    // 角标图片
    string image_url = 1 [(gogoproto.jsontag) = "image_url", (gogoproto.moretags) = 'form:"image_url"', json_name = "image_url"];
    // 角标文本内容
    string content = 2 [(gogoproto.jsontag) = "content", (gogoproto.moretags) = 'form:"content"', json_name = "content"];
}

message ListUserBannerCardGroupByActReq {
    // 查看者mid
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 被查看者mid
    int64 vmid = 2 [(gogoproto.jsontag) = "vmid", (gogoproto.moretags) = 'form:"vmid"', json_name = "vmid"];
    // 活动起点
    int64 offset_act_id = 3 [(gogoproto.jsontag) = "offset_act_id", (gogoproto.moretags) = 'form:"offset_act_id"', json_name = "offset_act_id"];
    // 指定活动
    int64 act_id = 4 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 数据量大小
    int64 size = 5 [(gogoproto.jsontag) = "size", (gogoproto.moretags) = 'form:"size"', json_name = "size"];
}

message GetUpChainStatusByActIdResp {
    // 上链状态，1=上链，0=下链
    int64 is_up_chain = 1 [(gogoproto.jsontag) = "is_up_chain", (gogoproto.moretags) = 'form:"is_up_chain"', json_name = "is_up_chain"];
}

message ListUserBannerCardGroupByActResp {
    // 是否还有剩余数据 0-无 1-有
    int64 has_more = 1 [(gogoproto.jsontag) = "has_more", (gogoproto.moretags) = 'form:"has_more"', json_name = "has_more"];
    // 总活动系列数
    int64 act_total = 2 [(gogoproto.jsontag) = "total", (gogoproto.moretags) = 'form:"total"', json_name = "total"];
    // 参与的活动数
    int64 participate_act_total = 3 [(gogoproto.jsontag) = "participate_act_total", (gogoproto.moretags) = 'form:"participate_act_total"', json_name = "participate_act_total"];
    // 下一页起始活动id
    int64 participate_next_act_id = 4 [(gogoproto.jsontag) = "participate_next_act_id", (gogoproto.moretags) = 'form:"participate_next_act_id"', json_name = "participate_next_act_id"];
    repeated CardActItem item_list = 5 [(gogoproto.jsontag) = "item_list", (gogoproto.moretags) = 'form:"item_list"', json_name = "item_list"];
    message CardActItem {
        // 活动id
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 活动名称
        string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];

        // 卡牌种类信息
        repeated CardItem card_item_list = 3 [(gogoproto.jsontag) = "card_item_list", (gogoproto.moretags) = 'form:"card_item_list"', json_name = "card_item_list"];
        // 查看者是否有此活动资产 0-无 1-有
        int64 viewer_has_asset = 4 [(gogoproto.jsontag) = "viewer_has_asset", (gogoproto.moretags) = 'form:"viewer_has_asset"', json_name = "viewer_has_asset"];
    }
    message CardItem {
        repeated int64 card_id_list = 1 [(gogoproto.jsontag) = "card_id_list", (gogoproto.moretags) = 'form:"card_id_list"', json_name = "card_id_list"];
        // 卡牌id
        int64 card_type_id = 2 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id"', json_name = "card_type_id"];
        string name = 3 [(gogoproto.jsontag) = "name", (gogoproto.moretags) = 'form:"name"', json_name = "name"];
        // 卡牌概览图（用于列表页、缩略图等场景）
        string overview_image = 4 [(gogoproto.jsontag) = "overview_image", (gogoproto.moretags) = 'form:"overview_image"', json_name = "overview_image"];
        // 卡牌类型 1-图片 2-视频
        int64 material_type = 5 [(gogoproto.jsontag) = "material_type", (gogoproto.moretags) = 'form:"material_type"', json_name = "material_type"];
        // 卡牌子类型
        int64 material_sub_type = 6 [(gogoproto.jsontag) = "material_sub_type", (gogoproto.moretags) = 'form:"material_sub_type"', json_name = "material_sub_type"];
        // 卡牌素材内容
        vas.nft.common.CardTypeContent content = 7 [(gogoproto.jsontag) = "content", (gogoproto.moretags) = 'form:"content"', json_name = "content"];
    }
}

message UserParticipatedItemReq {
    int64 mid = 1;
}

message UserParticipatedItemResp {
    int64 time = 1;
    repeated ItemSimple data = 2;
}

message ItemSimple {
    int64 mid = 1;
    string op = 2;
    int64 biz_type = 3;
    int64 biz_id = 4;
    string biz_sub_type = 5;
    int64 fetch_time = 6;
    int64 show_number = 7;
    int64 sort = 8;
    string item_id = 9;
    int64 expire = 10;
}

message DlcUserCardListReq {
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 被查看mid（肯定不为0）
    int64 vmid = 2 [(gogoproto.jsontag) = "vmid", (gogoproto.moretags) = 'form:"vmid"', json_name = "vmid"];
    // 主态uid（为0表示用户未登录）
    int64 mid = 3 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 业务传输trace_id
    string trace_id = 4 [(gogoproto.jsontag) = "trace_id", (gogoproto.moretags) = 'form:"trace_id"', json_name = "trace_id"];
    // 设备信息
    DeviceInfo device = 5;
}

message DlcUserCardListReply {
    // 用户名
    string uname = 1;
    // 用户头像
    string uface = 2;
    // 收藏集名称
    string act_name = 3;
    // 活动类型:1.集卡 2.dlc
    int64 act_type = 4;
    // 主态是否有资产（查看的人）
    int64 has_asset = 5;
    // 收藏集封面图
    string cover_image = 6;
    // 收集排行
    int64 collect_rank = 7;
    // 收集度
    CollectProgress collect = 8;
    // 评论背景
    CardBg card_bg = 9;
    // 奖励物品信息
    repeated BasicInfoResp.CollectRedeemInfo reward_list = 10;
    // 卡片信息
    repeated UserRightCardItem card_list = 11;
    // 活动信息
    CommonBasicIndo card_basic_info = 12;
    message CommonBasicIndo {
        // 横版灯光背景图
        string horizontal_card_light_url = 1;
        // 横版底座背景图
        string horizontal_card_shadow_url = 2;
        // 竖版灯光背景图
        string vertical_card_light_url = 3;
        // 竖版底座背景图
        string vertical_card_shadow_url = 4;
        // 是否能转赠 0-否，1-是
        int64 is_can_donate = 5;
        // 保存图片能力，1-是，非1否
        int64 save_image_right = 6;
    }
    // 隐私设置是否设置隐藏
    int64 privacy_hide = 13;
    // 主态用户是否持有绘星卡 0否1是
    int64 holding_smelt = 14;
}

message DlcUserCardListStreamReply {
    // 目前只对 UserRightCardItem 数组进行切分传输
    DlcUserCardListReply resp = 1 [(gogoproto.jsontag) = "resp", (gogoproto.moretags) = 'form:"resp"', json_name = "resp"];
    // stream参数标记位:only_item>0 说明只包含UserRightCardItem信息
    int64 only_item = 2 [(gogoproto.jsontag) = "only_item", (gogoproto.moretags) = 'form:"only_item"', json_name = "only_item"];
}

message CommonBasicInfo {
    // 横版灯光背景图
    string horizontal_card_light_url = 1;
    // 横版底座背景图
    string horizontal_card_shadow_url = 2;
    // 竖版灯光背景图
    string vertical_card_light_url = 3;
    // 竖版底座背景图
    string vertical_card_shadow_url = 4;
    // 是否能转赠 0-否，1-是
    int64 is_can_donate = 5;
    // 保存图片能力，1-是，非1否
    int64 save_image_right = 6;
}

message DlcUserRightLandingReq {
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 主态uid（为0表示用户未登录）
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
}

message DlcUserRightLandingResp {
    DlcRightAct act_basic_info = 1;
    // ==活动维度信息==
    message DlcRightAct {
        // 收藏集名称
        string act_name = 1;
        // 活动类型:1.集卡 2.dlc
        int64 act_type = 2;
        // 收藏集封面图
        string cover_image = 3;
        // 活动信息:卡牌展示背景图
        CommonBasicInfo card_basic_info = 4;
        DiscountInfo discount_info = 5;
        bool only_free_lottery = 6;
    }
    // 装扮散件信息
    map<string, DlcActGarbWithRewardConds> items = 2;
    message DlcActGarbWithRewardConds {
        // 所有同part散件
        repeated DlcActGarbWithRewardCond items = 1;
    }
    // 是否拥有至少一张奖池卡牌
    bool hasCardAsset = 3;
}

// 活动&奖池&收集度奖励的【散件基础信息&资产】&【获取条件】
message DlcActGarbWithRewardCond {
    // 领取条件
    string reward_cond = 1;
    // icon
    string icon_url = 2;
    string image_url = 3;
    // 访问者资产情况
    VisitorAsset visitor_asset = 4;
    message VisitorAsset {
        bool has_asset = 1;
        bool is_equip = 2;
    }
    // 卡牌信息
    nft.CardTypeInfo card_info = 5;
    // 装扮散件属性
    map<string, string> garb_properties = 6;
    // 勋章信息
    CardBg card_bg = 7;
}

message DlcUserCardLandingReq {
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 被查看mid（肯定不为0）
    int64 vmid = 2 [(gogoproto.jsontag) = "vmid", (gogoproto.moretags) = 'form:"vmid"', json_name = "vmid"];
    // 主态uid（为0表示用户未登录）
    int64 mid = 3 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    int64 pn = 4 [(gogoproto.jsontag) = "pn", (gogoproto.moretags) = 'form:"pn"', json_name = "pn"];
    int64 ps = 5 [(gogoproto.jsontag) = "ps", (gogoproto.moretags) = 'form:"ps"', json_name = "ps"];
}

message DlcUserCardLandingResp {
    // 用户&收集进度信息
    DlcVisitedUserInfo user_basic_info = 1;
    // 卡牌列表
    repeated UserRightCardItem card_list = 2;
    // 分页
    int64 pn = 3;
    int64 ps = 4;
    int64 total = 5;
    message DlcVisitedUserInfo {
        // 用户名
        string uname = 1;
        // 用户头像
        string uface = 2;
        // 收集排行
        int64 collect_rank = 3;
        // 评论背景
        CardBg card_bg = 4;
        // 收集度
        CollectProgress collect = 5;
        // 隐私设置是否设置隐藏
        bool privacy_hide = 6;
    }
}

message UserRightCardItem {
    // 单种卡片持有数量
    int64 card_owned_cnt = 1;
    // 卡片编号
    string card_no = 2;
    // scarcity_rate 1.普通 2.稀有
    int64 scarcity_rate = 3;
    // 卡片实体
    ActItemListResp.ItemInfo.CardItem card_item = 4;
    // item_type 1.数字周边图鉴
    int64 item_type = 5;
    // 临时字段：是否使用scarcity_rate判断稀有/普通，为false则用card_item.card_scarcity判断大小隐藏
    // manager后台数据校验完之后弃用，全部改为大小隐藏展示
    bool use_scarcity_rate = 6;
}

// CollectProgress 收集进度
message CollectProgress {
    // 种类数量
    int64 total_item_cnt = 1;
    // 已拥有的种类数量
    int64 owned_item_cnt = 2;
}

message DlcUserAssetsReq {
    int64 mid = 1;
    int64 act_id = 2 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}
message DlcUserAssetsReply {
    string cover_img = 1;
    string uname = 2;
    string uface = 3;
    int64 total_card_cnt = 4;
    repeated DlcUserAsset assets = 5;
    CollectProgress collect = 6;
    int64 first_get_time = 7;
    // 评论背景
    CardBg card_bg = 8;
    int64 act_type = 9;
    bool equiped = 10;
    string act_name = 11;
}
message CardBg {
    string img = 1;
    int64 bg_no = 2;
    string bg_no_color = 3;
}
message DlcUserAsset {
    int64 part = 1;
    int64 biz_id = 2;
    bool equiped = 3;
    string name = 4;
    string get_from = 5;
    string img = 6;
    int64 biz_type = 7;
    Item item = 8;
    message Item {
        string name = 1;
        map<string, string> properties = 2;
    }
}

message DlcUserEquipLoadReq {
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    int64 unload = 3 [(gogoproto.jsontag) = "unload", (gogoproto.moretags) = 'form:"unload"', json_name = "unload"];
}
message DlcUserEquipLoadReply {}
message DlcActUserCommentBgReq {
    int64 act_id = 1;
    repeated int64 mids = 2;
}
message DlcActUserCommentBgReply {
    map<int64, CardBg> data = 1;
}
message DlcUserRightHasGarbReq {
    int64 act_id = 1;
    int64 mid = 2;
}
message DlcUserRightHasGarbReply {
    bool has = 1;
}

message GetNotNeedRealNameReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}
message GetNotNeedRealNameResp {
    // 是否需要实名验证
    // 1=不需要实名，0=需要实名
    int64 not_need_realname = 1 [(gogoproto.jsontag) = "not_need_realname", (gogoproto.moretags) = 'form:"not_need_realname"', json_name = "not_need_realname"];
}
message DlcActRewardUserListReq {
    string reward_id = 1;
    string start_time = 2;
    string end_time = 3;
    int64 mid = 4;
    // 请求来源 false.manager后台 true.开放平台(需要鉴权)
    bool need_auth = 5;
}

message DlcActRewardUserListReply {
    repeated CSVRow rows = 1;
    string file_name = 2;
}

message CSVRow {
    repeated string cell = 1;
}

message ExpressInfo {
    string name = 1 [(gogoproto.jsontag) = "name", (gogoproto.moretags) = 'form:"name"', json_name = "name"];
    string telephone_number = 2 [(gogoproto.jsontag) = "telephone_number", (gogoproto.moretags) = 'form:"telephone_number"', json_name = "telephone_number"];
    CityInfo city_info = 3 [(gogoproto.jsontag) = "city_info", (gogoproto.moretags) = 'form:"city_info"', json_name = "city_info"];
    string address_detail = 4 [(gogoproto.jsontag) = "address_detail", (gogoproto.moretags) = 'form:"address_detail"', json_name = "address_detail"];
    string note = 5 [(gogoproto.jsontag) = "note", (gogoproto.moretags) = 'form:"note"', json_name = "note"];
}

message CityInfo {
    string country_code = 1 [(gogoproto.jsontag) = "country_code", (gogoproto.moretags) = 'form:"country_code"', json_name = "country_code"];
    string country_name = 2 [(gogoproto.jsontag) = "country_name", (gogoproto.moretags) = 'form:"country_name"', json_name = "country_name"];
    string province_code = 3 [(gogoproto.jsontag) = "province_code", (gogoproto.moretags) = 'form:"province_code"', json_name = "province_code"];
    string province_name = 4 [(gogoproto.jsontag) = "province_name", (gogoproto.moretags) = 'form:"province_name"', json_name = "province_name"];
    string city_code = 5 [(gogoproto.jsontag) = "city_code", (gogoproto.moretags) = 'form:"city_code"', json_name = "city_code"];
    string city_name = 6 [(gogoproto.jsontag) = "city_name", (gogoproto.moretags) = 'form:"city_name"', json_name = "city_name"];
    string district_code = 7 [(gogoproto.jsontag) = "district_code", (gogoproto.moretags) = 'form:"district_code"', json_name = "district_code"];
    string district_name = 8 [(gogoproto.jsontag) = "district_name", (gogoproto.moretags) = 'form:"district_name"', json_name = "district_name"];
}

message TopicListReq {
    // 用户mid
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
}

message TopicListResp {
    repeated TopicItem topic_list = 1 [(gogoproto.jsontag) = "topic_list", (gogoproto.moretags) = 'form:"topic_list"', json_name = "topic_list"];
    message TopicItem {
        // 话题id
        int64 topic_id = 1 [(gogoproto.jsontag) = "topic_id", (gogoproto.moretags) = 'form:"topic_id"', json_name = "topic_id"];
        // 话题名称
        string topic_name = 2 [(gogoproto.jsontag) = "topic_name", (gogoproto.moretags) = 'form:"topic_name"', json_name = "topic_name"];
        // 话题类型 商业=1 增值=2
        int64 topic_type = 3 [(gogoproto.jsontag) = "topic_type", (gogoproto.moretags) = 'form:"topic_type"', json_name = "topic_type"];
        // 话题链接
        string url = 4 [(gogoproto.jsontag) = "url", (gogoproto.moretags) = 'form:"url"', json_name = "url"];
    }
}

message TopicRightReq {
    // 用户mid
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 话题id
    int64 topic_id = 2 [(gogoproto.jsontag) = "topic_id", (gogoproto.moretags) = 'form:"topic_id"', json_name = "topic_id"];
    // 请求来源 vas-增值 topic-话题服务
    string source = 3 [(gogoproto.jsontag) = "source", (gogoproto.moretags) = 'form:"source"', json_name = "source"];
}

message TopicRightResp {
    // 是否有权限 0-否 1-是
    int64 allow = 1 [(gogoproto.jsontag) = "allow", (gogoproto.moretags) = 'form:"allow"', json_name = "allow"];
}

// CardStockInfo 卡牌库存信息
message CardStockInfo {
    // 剩余库存
    int64 stock = 1 [(gogoproto.jsontag) = "stock", (gogoproto.moretags) = 'form:"stock"', json_name = "stock"];
    // 总库存
    int64 total_stock = 2 [(gogoproto.jsontag) = "total_stock", (gogoproto.moretags) = 'form:"total_stock"', json_name = "total_stock"];
}

// Deprecated CardExtInfo 卡牌扩展信息 统一用cardholds表目录下的结构体 这个不维护
message CardExtInfo {
    // 奖池是否自动开启
    bool is_lottery_auto_open = 1 [(gogoproto.jsontag) = "is_lottery_auto_open", (gogoproto.moretags) = 'form:"is_lottery_auto_open"', json_name = "is_lottery_auto_open"];
    // 收集度奖励id
    int64 collect_redeem_id = 2 [(gogoproto.jsontag) = "collect_redeem_id", (gogoproto.moretags) = 'form:"collect_redeem_id"', json_name = "collect_redeem_id"];
    // dlc活动id
    int64 dlc_act_id = 3 [(gogoproto.jsontag) = "dlc_act_id", (gogoproto.moretags) = 'form:"dlc_act_id"', json_name = "dlc_act_id"];
    // 收集度外显标题
    string dlc_act_display_title = 4 [(gogoproto.jsontag) = "dlc_act_display_title", (gogoproto.moretags) = 'form:"dlc_act_display_title"', json_name = "dlc_act_display_title"];
    // 奖池id
    int64 lottery_id = 5 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 奖池名称
    string lottery_name = 6 [(gogoproto.jsontag) = "lottery_name", (gogoproto.moretags) = 'form:"lottery_name"', json_name = "lottery_name"];
    // 卡牌的获取途径 收集度奖励：collect_redeem  dlc活动抽奖：dlc_act_lottery
    string get_from = 7 [(gogoproto.jsontag) = "get_from", (gogoproto.moretags) = 'form:"get_from"', json_name = "get_from"];
    // 奖池类型  1：常驻奖池， 2：纪念DLC奖池
    int64 lottery_type = 8 [(gogoproto.jsontag) = "lottery_type", (gogoproto.moretags) = 'form:"lottery_type"', json_name = "lottery_type"];
    // 奖池是否免费  0：否， 1：是
    int64 lottery_free = 9 [(gogoproto.jsontag) = "lottery_free", (gogoproto.moretags) = 'form:"lottery_free"', json_name = "lottery_free"];
    // 奖池物品奖励标签  new
    bool lottery_item_is_new = 10 [(gogoproto.jsontag) = "lottery_item_is_new", (gogoproto.moretags) = 'form:"lottery_item_is_new"', json_name = "lottery_item_is_new"];
    // 奖池物品奖励标签  up
    bool lottery_item_is_up = 11 [(gogoproto.jsontag) = "lottery_item_is_up", (gogoproto.moretags) = 'form:"lottery_item_is_up"', json_name = "lottery_item_is_up"];
    // 获取卡牌的直播间房间号，为0表示不是非直播间抽取
    int64 room_id = 12 [(gogoproto.jsontag) = "room_id", (gogoproto.moretags) = 'form:"room_id"', json_name = "room_id"];
    // 奖池物品的稀有度， 10：普通款， 30：隐藏款
    int64 scarcity = 13 [(gogoproto.jsontag) = "scarcity", (gogoproto.moretags) = 'form:"scarcity"', json_name = "scarcity"];
    // 卡牌编号，可为空
    int64 card_no = 14 [(gogoproto.jsontag) = "card_no", (gogoproto.moretags) = 'form:"card_no"', json_name = "card_no"];
}

message ItemResourcesReq {
    // bizId 业务ID
    string biz_id = 1 [(gogoproto.jsontag) = "biz_id", (gogoproto.moretags) = 'form:"biz_id"', json_name = "biz_id"];
    // bizItemIds 业务item id
    repeated string biz_item_ids = 2 [(gogoproto.jsontag) = "biz_item_ids", (gogoproto.moretags) = 'form:"biz_item_ids"', json_name = "biz_item_ids"];
    // master 是否直接查库
    bool master = 3 [(gogoproto.jsontag) = "master", (gogoproto.moretags) = 'form:"master"', json_name = "master"];
}
message ItemResourcesResp {
    map<string, ItemInfo> item_map = 1 [(gogoproto.jsontag) = "item_map", (gogoproto.moretags) = 'form:"item_map"', json_name = "item_map"];
    message ItemInfo {
        // biz_id
        string biz_id = 1 [(gogoproto.jsontag) = "biz_id", (gogoproto.moretags) = 'form:"biz_id"', json_name = "biz_id"];
        // biz_item_id
        string biz_item_id = 2 [(gogoproto.jsontag) = "biz_item_id", (gogoproto.moretags) = 'form:"biz_item_id"', json_name = "biz_item_id"];
        // stock_total 总库存
        int64 stock_total = 3 [(gogoproto.jsontag) = "stock_total", (gogoproto.moretags) = 'form:"stock_total"', json_name = "stock_total"];
        // stock_surplus 剩余库存
        int64 stock_surplus = 4 [(gogoproto.jsontag) = "stock_surplus", (gogoproto.moretags) = 'form:"stock_surplus"', json_name = "stock_surplus"];
        // ver 乐观锁
        int64 ver = 5 [(gogoproto.jsontag) = "ver", (gogoproto.moretags) = 'form:"ver"', json_name = "ver"];
    }
}

message GetActIdByMaterialBindingReq {
    int64 bind_goods_id = 1 [(gogoproto.jsontag) = "bind_goods_id", (gogoproto.moretags) = 'form:"bind_goods_id"', json_name = "bind_goods_id"];
    int64 bind_type = 2 [(gogoproto.jsontag) = "bind_type", (gogoproto.moretags) = 'form:"bind_type"', json_name = "bind_type"];
}

message GetActIdByMaterialBindingResp {
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}

message DLCActInfosReq {
    // 批量活动ID
    repeated int64 act_ids = 1 [(gogoproto.jsontag) = "act_ids", (gogoproto.moretags) = 'form:"act_ids"', json_name = "act_ids"];
    // 生效的活动信息 会检查 活动 奖池的生效
    bool force_effective = 2 [(gogoproto.jsontag) = "force_effective", (gogoproto.moretags) = 'form:"force_effective"', json_name = "force_effective"];
}

message DLCActInfosResp {
    // 活动信息
    map<int64, ActInfo> act_info = 1 [(gogoproto.jsontag) = "act_info", (gogoproto.moretags) = 'form:"act_info"', json_name = "act_info"];
    message ActInfo {
        // 活动ID
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 活动名称
        string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
        // 常驻奖池ID
        int64 permanent_lottery_id = 3 [(gogoproto.jsontag) = "permanent_lottery_id", (gogoproto.moretags) = 'form:"permanent_lottery_id"', json_name = "permanent_lottery_id"];
        // 关联奖池信息
        map<int64, LotteryInfo> lottery_info = 4 [(gogoproto.jsontag) = "lottery_info", (gogoproto.moretags) = 'form:"lottery_info"', json_name = "lottery_info"];
        // act_square_img 商品封面图/方形图
        string act_square_img = 5 [(gogoproto.jsontag) = "act_square_img", (gogoproto.moretags) = 'form:"act_square_img"', json_name = "act_square_img"];
        // 父分组ID
        int64 suit_group_id = 6 [(gogoproto.jsontag) = "suit_group_id", (gogoproto.moretags) = 'form:"suit_group_id"', json_name = "suit_group_id"];
        // 子分组ID
        int64 suit_sub_group_id = 7 [(gogoproto.jsontag) = "suit_sub_group_id", (gogoproto.moretags) = 'form:"suit_sub_group_id"', json_name = "suit_sub_group_id"];
        // 创作者
        int64 owner_mid = 8 [(gogoproto.jsontag) = "owner_mid", (gogoproto.moretags) = 'form:"owner_mid"', json_name = "owner_mid"];
        // spu_id
        int64 spu_id = 9 [(gogoproto.jsontag) = "spu_id", (gogoproto.moretags) = 'form:"spu_id"', json_name = "spu_id"];
        // 话题id
        int64 topic_id = 10 [(gogoproto.jsontag) = "topic_id", (gogoproto.moretags) = 'form:"topic_id"', json_name = "topic_id"];
    }
    message LotteryInfo {
        // 奖池ID
        int64 lottery_id = 1 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
        // 奖池名称
        string lottery_name = 2 [(gogoproto.jsontag) = "lottery_name", (gogoproto.moretags) = 'form:"lottery_name"', json_name = "lottery_name"];
        // 奖池类型  1：常驻奖池， 2：纪念DLC奖池
        int64 lottery_type = 3 [(gogoproto.jsontag) = "lottery_type", (gogoproto.moretags) = 'form:"lottery_type"', json_name = "lottery_type"];
        // 奖池图片
        string lottery_img = 4 [(gogoproto.jsontag) = "lottery_img", (gogoproto.moretags) = 'form:"lottery_img"', json_name = "lottery_img"];
        // 奖池展示时间
        int64 display_start_time = 5 [(gogoproto.jsontag) = "display_start_time", (gogoproto.moretags) = 'form:"display_start_time"', json_name = "display_start_time"];
        // 奖池售卖开始时间
        int64 sale_start_time = 6 [(gogoproto.jsontag) = "sale_start_time", (gogoproto.moretags) = 'form:"sale_start_time"', json_name = "sale_start_time"];
        // sku_id
        int64 sku_id = 7 [(gogoproto.jsontag) = "sku_id", (gogoproto.moretags) = 'form:"sku_id"', json_name = "sku_id"];
        // 价格 单位 厘
        int64 price = 8 [(gogoproto.jsontag) = "price", (gogoproto.moretags) = 'form:"price"', json_name = "price"];
        // 免费奖池 0-否 1-是
        int64 free = 9 [(gogoproto.jsontag) = "free", (gogoproto.moretags) = 'form:"free"', json_name = "free"];
    }
}

message CheckDrawLogAndCompensateReq {
    // 业务ID=数值binlog msg_id，由source:tid组成
    string msg_id = 1 [(gogoproto.jsontag) = "msg_id", (gogoproto.moretags) = 'form:"msg_id"', json_name = "msg_id"];
    // 数值ID=数值binlog score_id，由score_id:custom_id组成
    string score_id = 2 [(gogoproto.jsontag) = "score_id", (gogoproto.moretags) = 'form:"score_id"', json_name = "score_id"];
    // 用户id=数值binlog uid
    int64 mid = 3 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 数值扣减数量=数值binlog num
    int64 num = 4 [(gogoproto.jsontag) = "num", (gogoproto.moretags) = 'form:"num"', json_name = "num"];
    // 创建时间=数值binlog update_time
    int64 ctime = 5 [(gogoproto.jsontag) = "ctime", (gogoproto.moretags) = 'form:"ctime"', json_name = "ctime"];
}

// 执行接口公共返回
message FCommonResp {
    // 错误码 0表示成功 其他表示失败
    int64 status_code = 1 [(gogoproto.jsontag) = "status_code", (gogoproto.moretags) = 'form:"status_code"', json_name = "status_code"];
    // 错误信息
    string status_message = 2 [(gogoproto.jsontag) = "status_message", (gogoproto.moretags) = 'form:"status_message"', json_name = "status_message"];
}

message VipCardRightsInfoReq {
    // 用户ID
    int64 mid = 1 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 权益类型
    // 1=装扮套装（会员中心领福利）
    // 2=收藏集奖池（会员中心领福利）
    // 3=收藏集奖池（会员中心抽卡福利）收藏集里只有奖池
    // 100=大会员首抽福利活动
    int64 rights_type = 2 [(gogoproto.jsontag) = "rights_type", (gogoproto.moretags) = 'form:"rights_type"', json_name = "rights_type"];
    // 权益id，对应rights_type，装扮套装ID/奖池ID
    repeated int64 rights_id = 3 [(gogoproto.jsontag) = "rights_id", (gogoproto.moretags) = 'form:"rights_id"', json_name = "rights_id"];
}
message VipCardRightsInfoReply {
    // 权益类型，1=装扮套装，2=收藏集奖池
    int64 rights_type = 1 [(gogoproto.jsontag) = "rights_type", (gogoproto.moretags) = 'form:"rights_type"', json_name = "rights_type"];
    // 权益信息
    repeated RightsInfo rights_infos = 2 [(gogoproto.jsontag) = "rights_infos", (gogoproto.moretags) = 'form:"rights_infos"', json_name = "rights_infos"];
    message RightsInfo {
        // 权益名称
        string rights_name = 1 [(gogoproto.jsontag) = "rights_name", (gogoproto.moretags) = 'form:"rights_name"', json_name = "rights_name"];
        // 权益ID
        int64 rights_id = 2 [(gogoproto.jsontag) = "rights_id", (gogoproto.moretags) = 'form:"rights_id"', json_name = "rights_id"];
        // 权益属性
        repeated RightsProps rights_props = 3 [(gogoproto.jsontag) = "rights_props", (gogoproto.moretags) = 'form:"rights_props"', json_name = "rights_props"];
        // 素材信息
        repeated ItemInfo item_infos = 4 [(gogoproto.jsontag) = "item_infos", (gogoproto.moretags) = 'form:"item_infos"', json_name = "item_infos"];
    }

    message ItemInfo {
        // 图片
        string image = 1 [(gogoproto.jsontag) = "image", (gogoproto.moretags) = 'form:"image"', json_name = "image"];
        // 名称
        string name = 2 [(gogoproto.jsontag) = "name", (gogoproto.moretags) = 'form:"name"', json_name = "name"];
        // 类型
        int64 type = 3 [(gogoproto.jsontag) = "type", (gogoproto.moretags) = 'form:"type"', json_name = "type"];
    }

    // 权益属性
    message RightsProps {
        // 权益属性类型,1=收藏卡，2=免费体验卡，3=专享折扣，4=是否使用专享折扣
        int64 type = 1 [(gogoproto.jsontag) = "type", (gogoproto.moretags) = 'form:"type"', json_name = "type"];
        // 属性名称
        string name = 2 [(gogoproto.jsontag) = "name", (gogoproto.moretags) = 'form:"name"', json_name = "name"];
        // 属性值
        string value = 3 [(gogoproto.jsontag) = "value", (gogoproto.moretags) = 'form:"value"', json_name = "value"];
    }
}

// 收集度奖励info,奖励绑奖池的需求建的，后面最好用这个
message CollectRedeemInfo {
    // 收集奖励id
    int64 collect_id = 1 [(gogoproto.jsontag) = "collect_id", (gogoproto.moretags) = 'form:"collect_id"', json_name = "collect_id"];
    // 收集奖励任务的开始时间
    int64 start_time = 2 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
    // 收集奖励任务的结束时间
    int64 end_time = 3 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
    // 领取条件文案
    string redeem_text = 4 [(gogoproto.jsontag) = "redeem_text", (gogoproto.moretags) = 'form:"redeem_text"', json_name = "redeem_text"];
    // 收集奖励的物品类型 1:数字周边卡牌 2：表情包， 3：头像挂件，4：装扮永久套装, 7：观影券 8: cdk 9：实物奖励 10:限时装扮散件
    int64 redeem_item_type = 7 [(gogoproto.jsontag) = "redeem_item_type", (gogoproto.moretags) = 'form:"redeem_item_type"', json_name = "redeem_item_type"];
    // 收集奖励的物品id
    string redeem_item_id = 8 [(gogoproto.jsontag) = "redeem_item_id", (gogoproto.moretags) = 'form:"redeem_item_id"', json_name = "redeem_item_id"];
    // 收集奖励的物品名称
    string redeem_item_name = 9 [(gogoproto.jsontag) = "redeem_item_name", (gogoproto.moretags) = 'form:"redeem_item_name"', json_name = "redeem_item_name"];
    // 收集奖励的物品图片
    string redeem_item_image = 10 [(gogoproto.jsontag) = "redeem_item_image", (gogoproto.moretags) = 'form:"redeem_item_image"', json_name = "redeem_item_image"];
    // 已收集的数量
    int64 owned_item_amount = 11 [(gogoproto.jsontag) = "owned_item_amount", (gogoproto.moretags) = 'form:"owned_item_amount"', json_name = "owned_item_amount"];
    // 收集奖励要求的数量
    int64 require_item_amount = 12 [(gogoproto.jsontag) = "require_item_amount", (gogoproto.moretags) = 'form:"require_item_amount"', json_name = "require_item_amount"];
    // 已兑换次数
    int64 has_redeemed_cnt = 13 [(gogoproto.jsontag) = "has_redeemed_cnt", (gogoproto.moretags) = 'form:"has_redeemed_cnt"', json_name = "has_redeemed_cnt"];
    // 是否永久生效 0-否，1-是
    int64 effective_forever = 14 [(gogoproto.jsontag) = "effective_forever", (gogoproto.moretags) = 'form:"effective_forever"', json_name = "effective_forever"];
    // 收集奖励的物品图片下载链接
    string redeem_item_image_download = 15 [(gogoproto.jsontag) = "redeem_item_image_download", (gogoproto.moretags) = 'form:"redeem_item_image_download"', json_name = "redeem_item_image_download"];
    // 收集奖励的物品图片下载链接
    CardItem card_item = 16 [(gogoproto.jsontag) = "card_item", (gogoproto.moretags) = 'form:"card_item"', json_name = "card_item"];
    // 道具跳转链接
    string jump_url = 17 [(gogoproto.jsontag) = "jump_url", (gogoproto.moretags) = 'form:"jump_url"', json_name = "jump_url"];
    // 领取条件类型,scarcity=稀有度，card_type_id=指定卡牌，lottery_num=抽数，custom=自定义
    string redeem_cond_type = 18 [(gogoproto.jsontag) = "redeem_cond_type", (gogoproto.moretags) = 'form:"redeem_cond_type"', json_name = "redeem_cond_type"];
    // 剩余库存：默认传-1
    int64 remain_stock = 19 [(gogoproto.jsontag) = "remain_stock", (gogoproto.moretags) = 'form:"remain_stock"', json_name = "remain_stock"];
    // 全部库存：默认传-1
    int64 total_stock = 20 [(gogoproto.jsontag) = "total_stock", (gogoproto.moretags) = 'form:"total_stock"', json_name = "total_stock"];
    // 绑定奖池ID
    int64 lottery_id = 21 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 奖励标签
    string reward_tag = 22 [(gogoproto.jsontag) = "reward_tag", (gogoproto.moretags) = 'form:"reward_tag"', json_name = "reward_tag"];
    // 详细预览图（奖励封面图点开之后展示
    string redeem_detail_image = 23 [(gogoproto.jsontag) = "redeem_detail_image", (gogoproto.moretags) = 'form:"redeem_detail_image"', json_name = "redeem_detail_image"];
    // 详细预览视频（奖励封面图点开之后展示
    repeated string redeem_detail_videos = 24 [(gogoproto.jsontag) = "redeem_detail_videos", (gogoproto.moretags) = 'form:"redeem_detail_videos"', json_name = "redeem_detail_videos"];
    int64 sort = 25 [(gogoproto.jsontag) = "sort", (gogoproto.moretags) = 'form:"sort"', json_name = "sort"];
    repeated string redeem_items_optional = 26 [deprecated = true, (gogoproto.jsontag) = "redeem_items_optional", (gogoproto.moretags) = 'form:"redeem_items_optional"', json_name = "redeem_items_optional"];
    // 解锁条件，到达解锁条件才能解锁
    UnlockCondition unlock_condition = 27 [(gogoproto.moretags) = 'form:"unlock_condition"', (gogoproto.jsontag) = 'unlock_condition', json_name = 'unlock_condition'];
    // 自定义奖励多选一
    repeated RedeemItemOptional redeem_item_optional_list = 28 [(gogoproto.jsontag) = "redeem_item_optional_list", (gogoproto.moretags) = 'form:"redeem_item_optional_list"', json_name = "redeem_item_optional_list"];
    // 领取奖励的次数
    RedeemCount redeem_count = 29 [(gogoproto.jsontag) = "redeem_count", (gogoproto.moretags) = 'form:"redeem_count"', json_name = "redeem_count"];
    // 排行榜奖励用
    RankInfo rank_info = 30 [(gogoproto.jsontag) = "rank_info", (gogoproto.moretags) = 'form:"rank_info"', json_name = "rank_info"];
    // 卡牌数据
    message CardItem {
        // 抽中卡牌信息
        nft.CardTypeInfo card_type_info = 1 [(gogoproto.jsontag) = "card_type_info", (gogoproto.moretags) = 'form:"card_type_info"', json_name = "card_type_info"];
        // 播放设置
        Play play = 2 [(gogoproto.jsontag) = "play", (gogoproto.moretags) = 'form:"play"', json_name = "play"];
        // 角标展示
        TagInfo tag = 3 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
        CardItemInfoInBag card_asset_info = 4 [(gogoproto.jsontag) = "card_asset_info", (gogoproto.moretags) = 'form:"card_asset_info"', json_name = "card_asset_info"];
    }
    message UnlockCondition {
        // 是否已解锁，若已解锁，无需关心剩余字段
        bool unlocked = 1 [(gogoproto.moretags) = 'form:"unlocked"', (gogoproto.jsontag) = 'unlocked', json_name = 'unlocked'];
        // 解锁条件类型，1奖池买抽达到特定数额、2活动买抽达到特定数额
        int64 lock_type = 2 [(gogoproto.moretags) = 'form:"lock_type"', (gogoproto.jsontag) = 'lock_type', json_name = 'lock_type'];
        // 过期时间戳，到达此时间后不允许解锁
        int64 expire_at = 3 [(gogoproto.moretags) = 'form:"expire_at"', (gogoproto.jsontag) = 'expire_at', json_name = 'expire_at'];
        // 首次解锁时间，未解锁时该值为-1
        int64 unlocked_at = 4 [(gogoproto.moretags) = 'form:"unlocked_at"', (gogoproto.jsontag) = 'unlocked_at', json_name = 'unlocked_at'];
        // 解锁门槛
        int64 unlock_threshold = 5 [(gogoproto.moretags) = 'form:"unlock_threshold"', (gogoproto.jsontag) = 'unlock_threshold', json_name = 'unlock_threshold'];
        // 当前值
        int64 current_threshold = 6 [(gogoproto.moretags) = 'form:"current_threshold"', (gogoproto.jsontag) = 'current_threshold', json_name = 'current_threshold'];
    }

    // 领取次数
    message RedeemCount {
        // 用户已经领取的次数
        int64 has_redeem_count = 1 [(gogoproto.moretags) = 'form:"has_redeem_count"', (gogoproto.jsontag) = 'has_redeem_count', json_name = 'has_redeem_count'];
        // 用户维度收集度奖励可领取的次数，当redeem_count_type=1时有意义
        int64 redeem_count = 2 [(gogoproto.moretags) = 'form:"redeem_count"', (gogoproto.jsontag) = 'redeem_count', json_name = 'redeem_count'];
        // 领取类型0=单次领取，1=多次领取，-1=无限制领取
        int64 redeem_count_type = 3 [(gogoproto.moretags) = 'form:"redeem_count_type"', (gogoproto.jsontag) = 'redeem_count_type', json_name = 'redeem_count_type'];
    }

    message RankInfo {
        // 起始排名 包含
        int64 from = 1 [(gogoproto.moretags) = 'form:"from"', (gogoproto.jsontag) = 'from', json_name = 'from'];
        // 结束排名 包含
        int64 to = 2 [(gogoproto.moretags) = 'form:"to"', (gogoproto.jsontag) = 'to', json_name = 'to'];
        // 我的当前排名
        int64 my_rank = 3 [(gogoproto.moretags) = 'form:"my_rank"', (gogoproto.jsontag) = 'my_rank', json_name = 'my_rank'];
        // 截止时间戳
        int64 deadline = 4 [(gogoproto.moretags) = 'form:"deadline"', (gogoproto.jsontag) = 'deadline', json_name = 'deadline'];
    }
}

message RedeemItemOptional {
    // 选项名
    string name = 1 [(gogoproto.moretags) = 'form:"name"', (gogoproto.jsontag) = 'name', json_name = 'name'];
    // 选项图片
    string image = 2 [(gogoproto.moretags) = 'form:"image"', (gogoproto.jsontag) = 'image', json_name = 'image'];
    // 是否活动,0=未获得或者无状态，1=已获得
    int64 is_get = 3 [(gogoproto.moretags) = 'form:"is_get"', (gogoproto.jsontag) = 'is_get', json_name = 'is_get'];
}

message GetActLotteryInfoReq {
    int64 act_id = 1 [(gogoproto.moretags) = 'form:"act_id"', (gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
}

message GetActLotteryInfoResp {
    ActInfo result = 1 [(gogoproto.jsontag) = 'result', json_name = 'result'];
    message ActInfo {
        // 活动ID
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 活动名称
        string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
        // 关联奖池信息
        repeated LotteryInfo lottery_infos = 3 [(gogoproto.jsontag) = "lottery_infos", (gogoproto.moretags) = 'form:"lottery_infos"', json_name = "lottery_infos"];
    }
    message LotteryInfo {
        // 奖池ID
        int64 lottery_id = 1 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
        // 奖池名称
        string lottery_name = 2 [(gogoproto.jsontag) = "lottery_name", (gogoproto.moretags) = 'form:"lottery_name"', json_name = "lottery_name"];
        // 奖池类型  1：常驻奖池， 2：纪念DLC奖池
        int64 lottery_type = 3 [(gogoproto.jsontag) = "lottery_type", (gogoproto.moretags) = 'form:"lottery_type"', json_name = "lottery_type"];
        // 奖池图片
        string lottery_img = 4 [(gogoproto.jsontag) = "lottery_img", (gogoproto.moretags) = 'form:"lottery_img"', json_name = "lottery_img"];
        // 卡牌信息
        repeated CardTypeInfo card_type_infos = 5 [(gogoproto.jsontag) = 'card_type_infos', json_name = 'card_type_infos'];
        // 跳转地址
        string jump_url = 6 [(gogoproto.jsontag) = 'jump_url', json_name = 'jump_url'];
    }
    message CardTypeInfo {
        int64 card_type_id = 1 [(gogoproto.jsontag) = 'card_type_id', json_name = 'card_type_id'];
    }
}

message UserDrawCountsReq {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
    repeated ActInfo act_infos = 2 [(gogoproto.jsontag) = 'act_infos', json_name = 'act_infos'];
    message ActInfo {
        int64 act_id = 1 [(gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
        // 不填代表检查当前活动下所有
        repeated LotteryInfo lottery_infos = 2 [(gogoproto.jsontag) = 'lottery_infos', json_name = 'lottery_infos'];
    }
    message LotteryInfo {
        int64 lottery_id = 1 [(gogoproto.jsontag) = 'lottery_id', json_name = 'lottery_id'];
    }
}

message UserDrawCountsResp {
    repeated ActInfo result = 1 [(gogoproto.jsontag) = 'act_infos', json_name = 'act_infos'];
    message ActInfo {
        int64 act_id = 1 [(gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
        string act_name = 2 [(gogoproto.jsontag) = 'act_name', json_name = 'act_name'];
        string preview_image = 3 [(gogoproto.jsontag) = 'preview_image', json_name = 'preview_image'];
        repeated LotteryInfo lottery_infos = 4 [(gogoproto.jsontag) = 'lottery_infos', json_name = 'lottery_infos'];
    }
    message LotteryInfo {
        int64 lottery_id = 1 [(gogoproto.jsontag) = 'lottery_id', json_name = 'lottery_id'];
        int64 draw_left_count = 2 [(gogoproto.jsontag) = 'draw_left_count', json_name = 'draw_left_count'];
    }
}

message UserCollectRedeemDiyDrawReq {
    // 用户mid
    int64 mid = 1;
    // 活动id
    int64 act_id = 2 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 收集度奖励id
    int64 collect_id = 3 [(gogoproto.jsontag) = "collect_id", (gogoproto.moretags) = 'form:"collect_id"', json_name = "collect_id"];
    // 收集度奖励选项
    string option_value = 4 [(gogoproto.jsontag) = "option_value", (gogoproto.moretags) = 'form:"option_value"', json_name = "option_value"];
}

message UserCollectRedeemDiyDrawReply {}

message CollectDiyInfo {
    string option_value = 1 [(gogoproto.jsontag) = "option_value", (gogoproto.moretags) = 'form:"option_value"', json_name = "option_value"];
}

message ActCollectDiyInfoReq {
    repeated ActCollectUK items = 1;
    int64 mid = 2;
    message ActCollectUK {
        int64 act_id = 1;
        int64 collect_id = 2;
    }
}

message ActCollectDiyInfoReply {
    map<int64, CollectItem> items = 1;

    message CollectItem {
        // 领取的收集度奖励id
        int64 collect_id = 1;
        // 收集奖励的物品名称
        string redeem_item_name = 2;
        // 收集奖励的物品图片
        string redeem_item_image = 3;
        // 收集度奖励外显文案
        string redeem_text = 4;
        // 收集奖励任务的开始时间
        int64 redeem_start_time = 5;
        // 收集奖励任务的结束时间
        int64 redeem_end_time = 6;
        int64 user_owned_cnt = 7;
        int64 total_cnt = 8;
        int64 lottery_id = 9;
        string lottery_img = 10;
        string lottery_name = 11;
        int64 act_id = 12;
        // 是否真的绑定了奖池 判断是否走的兜底常驻奖池
        bool real_bind_lottery = 13;
        int64 lock_type = 14;        // 解锁维度
        int64 unlock_threshold = 15; // 解锁门槛
        // 总领取次数
        int64 total_collect_count = 16;
    }
}

message UserAllCollectRedeemDiyReq {
    int64 mid = 1;
}

message UserAllCollectRedeemDiyReply {
    repeated UserCollectRedeemDiyItem items = 1;
}

message UserCollectRedeemDiyItem {
    int64 act_id = 1;
    repeated int64 collect_id = 2;
}

message UserCollectRedeemDiyOptionReq {
    repeated int64 mids = 1;
    repeated int64 collect_id = 2;
}

message UserCollectRedeemDiyOptionResp {
    repeated UserCollectRedeemDiyOption diy_detail_list = 1;
}

message UserCollectRedeemDiyOption {
    int64 act_id = 1;
    int64 mid = 2;
    int64 collect_id = 3;
    string option = 4;
    int64 claim_time = 5;
    int64 lottery_id = 6;
}

message CollectRedeemDiyOptDetailReq {
    repeated ActCollectUK collect_uk = 2;
}

message ActCollectUK {
    int64 act_id = 1;
    int64 collect_id = 2;
}

message CollectRedeemDiyOptDetailResp {
    map<int64, CollectRedeemDiyOptions> diy_opt_map = 1;
}

message CollectRedeemDiyOptions {
    map<string, RedeemItemOptional> opt_map = 1;
}
message ActLotterySalesReq {
    int64 act_id = 1;
    bool ignore_free = 2;
}

message ActLotterySalesReply {
    int64 sales_total = 1;
    map<int64, int64> lottery_sales = 2;
}

message PhysicalEntityExchangeToCReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}

message PhysicalEntityExchangeToCResp {
    // 实体兑换配置：所有上线状态的配置中：优先返回 进行中>最近结束>即将开始>空
    EntityExchangeDetailToC entity_exchange = 1 [(gogoproto.jsontag) = "entity_exchange", (gogoproto.moretags) = 'form:"entity_exchange"', json_name = "entity_exchange"];
    // 没有可展示的实体兑换时：-1:展示未开始态，-2展示已结束态
    int64 default_state = 2 [(gogoproto.jsontag) = "default_state", (gogoproto.moretags) = 'form:"default_state"', json_name = "default_state"];
}

message EntityExchangeDetailToC {
    // 实体id
    int64 entity_exchange_id = 1 [(gogoproto.jsontag) = "entity_exchange_id", (gogoproto.moretags) = 'form:"entity_exchange_id"', json_name = "entity_exchange_id"];
    // 实体id
    int64 act_id = 2 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 实体名称
    string entity_exchange_name = 3 [(gogoproto.jsontag) = "entity_exchange_name", (gogoproto.moretags) = 'form:"entity_exchange_name"', json_name = "entity_exchange_name"];
    // 生效开始时间
    int64 start_time = 4 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
    // 生效结束时间
    int64 end_time = 5 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
    // 兑换须知
    string exchange_desc = 6 [(gogoproto.jsontag) = "exchange_desc", (gogoproto.moretags) = 'form:"exchange_desc"', json_name = "exchange_desc"];
    // 发货说明
    string postage_desc = 7 [(gogoproto.jsontag) = "postage_desc", (gogoproto.moretags) = 'form:"postage_desc"', json_name = "postage_desc"];
    // 备注
    string note = 8 [(gogoproto.jsontag) = "note", (gogoproto.moretags) = 'form:"note"', json_name = "note"];
    // 支付描述
    string pay_desc = 9 [(gogoproto.jsontag) = "pay_desc", (gogoproto.moretags) = 'form:"pay_desc"', json_name = "pay_desc"];
    // 支付时间
    int64 pay_expire = 10 [(gogoproto.jsontag) = "pay_expire", (gogoproto.moretags) = 'form:"pay_expire"', json_name = "pay_expire"];
    // 支付商品id
    int64 pay_good_id = 11 [(gogoproto.jsontag) = "good_id", (gogoproto.moretags) = 'form:"good_id"', json_name = "good_id"];
    repeated ExchangeRuleItem rule_info = 12 [(gogoproto.jsontag) = "rule_info", (gogoproto.moretags) = 'form:"rule_info"', json_name = "rule_info"];
    repeated ActCardExchangeInfo card_info = 13 [(gogoproto.jsontag) = "card_info", (gogoproto.moretags) = 'form:"card_info"', json_name = "card_info"];
    message ExchangeRuleItem {
        string reward_id = 1 [(gogoproto.jsontag) = "reward_id", (gogoproto.moretags) = 'form:"reward_id"', json_name = "reward_id"];
        int64 reward_type = 2 [(gogoproto.jsontag) = "reward_type", (gogoproto.moretags) = 'form:"reward_type"', json_name = "reward_type"];
        string name = 3 [(gogoproto.jsontag) = "name", (gogoproto.moretags) = 'form:"name"', json_name = "name"];
        string preview_image = 4 [(gogoproto.jsontag) = "preview_image", (gogoproto.moretags) = 'form:"preview_image"', json_name = "preview_image"];
        int64 exchange_cost = 5 [(gogoproto.jsontag) = "exchange_cost", (gogoproto.moretags) = 'form:"exchange_cost"', json_name = "exchange_cost"];
        string exchange_desc = 6 [(gogoproto.jsontag) = "exchange_desc", (gogoproto.moretags) = 'form:"exchange_desc"', json_name = "exchange_desc"];
        int64 price = 7 [(gogoproto.jsontag) = "price", (gogoproto.moretags) = 'form:"price"', json_name = "price"];
        int64 postage = 8 [(gogoproto.jsontag) = "postage", (gogoproto.moretags) = 'form:"postage"', json_name = "postage"];
    }
    message ActCardExchangeInfo {
        int64 card_type_id = 1 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id"', json_name = "card_type_id"];
    }
    // 1:统计全部卡牌 2:统计实体活动期间的卡牌
    int64 statistic_type = 14 [(gogoproto.jsontag) = "statistic_type", (gogoproto.moretags) = 'form:"statistic_type"', json_name = "statistic_type"];
    // 业务类型，0=收藏集，1=活动
    int64 biz_type = 15 [(gogoproto.jsontag) = "biz_type", (gogoproto.moretags) = 'form:"biz_type"', json_name = "biz_type"];
}

message UserOwnedActMedalInfosReq {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
}

message UserOwnedActMedalInfosResp {
    repeated MedalInfo medal_infos = 1 [(gogoproto.jsontag) = 'medal_infos', json_name = 'medal_infos'];
    message MedalInfo {
        int64 act_id = 1 [(gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
        int64 card_bg_item_id = 2 [(gogoproto.jsontag) = 'card_bg_item_id', json_name = 'card_bg_item_id'];
        int64 card_item_id = 3 [(gogoproto.jsontag) = 'card_item_id', json_name = 'card_item_id'];
    }
}

message PhysicalEntityExchangeWithPoolReq {
    // 奖池id
    int64 pool_id = 1 [(gogoproto.jsontag) = "pool_id", (gogoproto.moretags) = 'form:"pool_id"', json_name = "pool_id"];
    // 实体兑换id
    int64 entity_id = 2 [(gogoproto.jsontag) = "entity_id", (gogoproto.moretags) = 'form:"entity_id"', json_name = "entity_id"];
}

message PhysicalEntityExchangeWithPoolResp {
    // 实体兑换配置：指定的entity_id
    EntityExchangeDetailToC entity_exchange = 1 [(gogoproto.jsontag) = "entity_exchange", (gogoproto.moretags) = 'form:"entity_exchange"', json_name = "entity_exchange"];
    // 活动id
    EntityActInfoFromAct act_info = 2 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // 从奖池中取的参数
    EntityActInfoFromPool pool_info = 3 [(gogoproto.jsontag) = "pool_info", (gogoproto.moretags) = 'form:"pool_info"', json_name = "pool_info"];
}

message EntityActInfoFromAct {
    // 活动id
    int64 act_id = 1 [(gogoproto.jsontag) = "pool_id", (gogoproto.moretags) = 'form:"pool_id"', json_name = "pool_id"];
    // 活动名称
    string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
}

message EntityActInfoFromPool {
    // 奖池id
    int64 pool_id = 1 [(gogoproto.jsontag) = "pool_id", (gogoproto.moretags) = 'form:"pool_id"', json_name = "pool_id"];
    // 奖池名称
    string pool_name = 2 [(gogoproto.jsontag) = "pool_name", (gogoproto.moretags) = 'form:"pool_name"', json_name = "pool_name"];
    // 奖池图
    string pool_cover_img = 3 [(gogoproto.jsontag) = "pool_cover_img", (gogoproto.moretags) = 'form:"pool_cover_img"', json_name = "pool_cover_img"];
}

message PhysicalEntitySimpleConfInActReq {
    // 活动id
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
}

message PhysicalEntitySimpleConfInActResp {
    // 基本配置参数列表
    repeated SimpleConf list = 1 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
    message SimpleConf {
        // 实体兑换id
        int64 entity_id = 1 [(gogoproto.jsontag) = "entity_id", (gogoproto.moretags) = 'form:"entity_id"', json_name = "entity_id"];
    }
}

message MedalInfoByCardIdReq {
    int64 card_id = 1;
}

message MedalInfoByCardIdResp {
    int64 act_id = 1;
    int64 garb_item_base_id = 2;
    int64 garb_dc_item_base_id = 3;
}

message DLCActInfosByRelatedMidReq {
    // 收藏集关联Mid
    int64 related_mid = 1 [(gogoproto.jsontag) = "related_mid", (gogoproto.moretags) = 'form:"related_mid"', json_name = "related_mid"];
    // 是否只取开售状态的
    bool is_sale = 2 [(gogoproto.jsontag) = "is_sale", (gogoproto.moretags) = 'form:"is_sale"', json_name = "is_sale"];
}
message DLCActInfosByRelatedMidResp {
    // 活动信息
    repeated ActInfo act_list = 1 [(gogoproto.jsontag) = "act_list", (gogoproto.moretags) = 'form:"act_list"', json_name = "act_list"];
    message ActInfo {
        // 活动ID
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 活动名称
        string act_name = 2 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
        // 开售时间
        int64 start_time = 3 [(gogoproto.jsontag) = "start_time", (gogoproto.moretags) = 'form:"start_time"', json_name = "start_time"];
        // 结束时间
        int64 end_time = 4 [(gogoproto.jsontag) = "end_time", (gogoproto.moretags) = 'form:"end_time"', json_name = "end_time"];
        // 常驻奖池ID
        int64 permanent_lottery_id = 5 [(gogoproto.jsontag) = "permanent_lottery_id", (gogoproto.moretags) = 'form:"permanent_lottery_id"', json_name = "permanent_lottery_id"];
        // 关联奖池信息
        map<int64, LotteryInfo> lottery_info = 6 [(gogoproto.jsontag) = "lottery_info", (gogoproto.moretags) = 'form:"lottery_info"', json_name = "lottery_info"];
    }
    message LotteryInfo {
        // 奖池ID
        int64 lottery_id = 1 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
        // 奖池名称
        string lottery_name = 2 [(gogoproto.jsontag) = "lottery_name", (gogoproto.moretags) = 'form:"lottery_name"', json_name = "lottery_name"];
        // 奖池类型  1：常驻奖池， 2：纪念DLC奖池
        int64 lottery_type = 3 [(gogoproto.jsontag) = "lottery_type", (gogoproto.moretags) = 'form:"lottery_type"', json_name = "lottery_type"];
        // 奖池图片
        string lottery_img = 4 [(gogoproto.jsontag) = "lottery_img", (gogoproto.moretags) = 'form:"lottery_img"', json_name = "lottery_img"];
        // 奖池展示时间
        int64 display_start_time = 5 [(gogoproto.jsontag) = "display_start_time", (gogoproto.moretags) = 'form:"display_start_time"', json_name = "display_start_time"];
        // 奖池售卖开始时间
        int64 sale_start_time = 6 [(gogoproto.jsontag) = "sale_start_time", (gogoproto.moretags) = 'form:"sale_start_time"', json_name = "sale_start_time"];
        // sku_id
        int64 sku_id = 7 [(gogoproto.jsontag) = "sku_id", (gogoproto.moretags) = 'form:"sku_id"', json_name = "sku_id"];
        // 价格 单位 厘
        int64 price = 8 [(gogoproto.jsontag) = "price", (gogoproto.moretags) = 'form:"price"', json_name = "price"];
        // 免费奖池 0-否 1-是
        int64 free = 9 [(gogoproto.jsontag) = "free", (gogoproto.moretags) = 'form:"free"', json_name = "free"];
    }
}
message CollectRedeemInfosReq {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
    repeated CollectRedeemId ids = 2 [(gogoproto.jsontag) = 'ids', json_name = 'ids'];
    message CollectRedeemId {
        int64 act_id = 1 [(gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
        int64 lottery_id = 2 [(gogoproto.jsontag) = 'lottery_id', json_name = 'lottery_id'];
        int64 collect_id = 3 [(gogoproto.jsontag) = 'collect_id', json_name = 'collect_id'];
    }
}

message CollectRedeemInfosResp {
    repeated CollectRedeemInfoWithAct result = 1;
    message CollectRedeemInfoWithAct {
        int64 act_id = 1 [(gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
        CollectRedeemInfo collect_redeem_info = 2 [(gogoproto.jsontag) = 'collect_redeem_info', json_name = 'collect_redeem_info'];
    }
}
message DlcPaymentPreCheckReq {
    int64 mid = 1;
    string mobile_app = 2 [(gogoproto.jsontag) = "mobi_app", (gogoproto.moretags) = 'form:"mobi_app"', json_name = "mobi_app"];
    int64 build = 3 [(gogoproto.jsontag) = "build", (gogoproto.moretags) = 'form:"build"', json_name = "build"];
}

message DlcPaymentPreCheckReply {
    int64 res = 1;
}

message DLCRetentionPageReq {
    // 收藏集ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
    // mid
    int64 mid = 2 [(gogoproto.jsontag) = "mid", (gogoproto.moretags) = 'form:"mid"', json_name = "mid"];
    // 奖池ID
    int64 lottery_id = 3 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 取指定页面来源的数据，不传走默认逻辑
    int64 page_source = 4 [(gogoproto.jsontag) = "page_source", (gogoproto.moretags) = 'form:"page_source"', json_name = "page_source"];
}

message DLCRetentionPageResp {
    // 页面类型，0=没数据，1=二次强调权益弹窗，2=导流弹窗
    int64 page_type = 1 [(gogoproto.jsontag) = "page_type", (gogoproto.moretags) = 'form:"page_type"', json_name = "page_type"];
    // 内容结构
    string content = 2 [(gogoproto.jsontag) = "content", (gogoproto.moretags) = 'form:"content"', json_name = "content"];

    // 后续用以下结构
    repeated Item list = 3 [(gogoproto.jsontag) = "list", (gogoproto.moretags) = 'form:"list"', json_name = "list"];
    message Item {
        // 页面类型，0=没数据，1=二次强调权益弹窗，2=导流弹窗
        int64 page_type = 1 [(gogoproto.jsontag) = "page_type", (gogoproto.moretags) = 'form:"page_type"', json_name = "page_type"];
        // 内容结构
        string content = 2 [(gogoproto.jsontag) = "content", (gogoproto.moretags) = 'form:"content"', json_name = "content"];
    }
}

message DeviceInfo {
    int64 build = 1 [(gogoproto.moretags) = 'form:"build"'];
    string mobi_app = 2 [(gogoproto.moretags) = 'form:"mobi_app"'];
    string device = 3 [(gogoproto.moretags) = 'form:"device"'];
}

message ListCardAssetForSplashReq {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
    // item_id 在 level = 2 时才有意义
    repeated vas.common.CollectionBase items = 3 [(gogoproto.jsontag) = "items", (gogoproto.moretags) = 'form:"mid"', json_name = "items"];
}

message ListCardAssetForSplashResp {
    repeated vas.common.SplashGroupInfo result = 1 [(gogoproto.jsontag) = 'result', json_name = 'result'];
}

message SplashInfoByCardTypeIdsReq {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
    int64 act_id = 2 [(gogoproto.moretags) = 'form:"act_id"', (gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
    repeated int64 card_type_ids = 3 [(gogoproto.moretags) = 'form:"card_type_ids"', (gogoproto.jsontag) = 'card_type_ids', json_name = 'card_type_ids'];
}

message SplashInfoByCardTypeIdsResp {
    repeated vas.common.SplashDetailInfo result = 1 [(gogoproto.jsontag) = 'result', json_name = 'result'];
}

message CollectCompoundVideoTaskAddReq {
    // 收集度奖励ID
    int64 collect_id = 1 [(gogoproto.jsontag) = "collect_id", (gogoproto.moretags) = 'form:"collect_id"', json_name = "collect_id"];
}
message CollectCompoundVideoTaskAddResp {}
message DelCachePaymentCashHitReq {
    int64 mid = 1;
}
message DelCachePaymentCashHitReply {}
message ListCardAssetForWidgetReq {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
    // item_id 在 level = 2 时才有意义
    repeated vas.common.CollectionBase items = 3 [(gogoproto.jsontag) = "items", (gogoproto.moretags) = 'form:"mid"', json_name = "items"];
}

message ListCardAssetForWidgetResp {
    repeated vas.common.WidgetGroup result = 1 [(gogoproto.jsontag) = 'result', json_name = 'result'];
}

message WidgetInfoByCardTypeIdsReq {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
    // 单卡牌信息
    repeated WidgetInfo widget_infos = 2 [(gogoproto.moretags) = 'form:"widget_infos"', (gogoproto.jsontag) = 'widget_infos', json_name = 'widget_infos'];
    message WidgetInfo {
        // 小组件类型
        int64 widget_type = 1 [(gogoproto.jsontag) = 'widget_type', json_name = 'widget_type'];
        // 资源信息
        repeated WidgetSingleInfo resources = 2 [(gogoproto.jsontag) = 'resources', json_name = 'resources'];
    }
    message WidgetSingleInfo {
        // card_type_id 卡牌类型id
        int64 card_type_id = 1 [(gogoproto.moretags) = 'form:"card_type_id"', (gogoproto.jsontag) = 'card_type_id', json_name = 'card_type_id'];
        // 本地正在使用的图片资源，如果不穿，则不会校验是否过期
        string resource = 2 [(gogoproto.moretags) = 'form:"resource"', (gogoproto.jsontag) = 'resource', json_name = 'resource'];
    }
}

message WidgetInfoByCardTypeIdsResp {
    repeated vas.common.WidgetResource result = 1 [(gogoproto.jsontag) = 'result', json_name = 'result'];
}

message EchoBindBackgroundReq {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
    int64 biz_id = 2 [(gogoproto.moretags) = 'form:"biz_id"', (gogoproto.jsontag) = 'biz_id', json_name = 'biz_id'];
    int64 bg_id = 3 [(gogoproto.moretags) = 'form:"bg_id"', (gogoproto.jsontag) = 'bg_id', json_name = 'bg_id'];
}

message EchoBindBackgroundResp {}

message EchoBackgroundListReq {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
    int64 biz_id = 2 [(gogoproto.moretags) = 'form:"biz_id"', (gogoproto.jsontag) = 'biz_id', json_name = 'biz_id'];
}

message EchoBackgroundListResp {
    repeated vas.common.EchoBgGroupInfo group_infos = 1 [(gogoproto.jsontag) = 'group_infos', json_name = 'group_infos'];
    // 顶部提示文案
    string top_tips = 2 [(gogoproto.jsontag) = 'top_tips', json_name = 'top_tips'];
}

message EchoBackgroundInfoReq {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
    int64 biz_id = 2 [(gogoproto.moretags) = 'form:"biz_id"', (gogoproto.jsontag) = 'biz_id', json_name = 'biz_id'];
}

message EchoBackgroundInfoResp {
    EchoBackgroundCardInfo result = 1 [(gogoproto.jsontag) = 'result', json_name = 'result'];
}

message EchoBackgroundCardInfo {
    // 绑定的特定背景id，即card_type_id
    int64 bg_id = 1 [(gogoproto.jsontag) = 'bg_id', json_name = 'bg_id'];
    // 背景图地址
    string resource = 2 [(gogoproto.jsontag) = 'resource', json_name = 'resource'];
}

message DLCCardMachineInfoReq {
    // 密钥
    string secret_key = 1 [(gogoproto.moretags) = 'form:"secret_key"', (gogoproto.jsontag) = 'secret_key', json_name = 'secret_key'];
}
message DLCCardMachineInfoResp {
    // 抽卡机名称
    string machine_name = 1 [(gogoproto.jsontag) = "machine_name", (gogoproto.moretags) = 'form:"machine_name"', json_name = "machine_name"];
    // 活动方
    string owner = 2 [(gogoproto.jsontag) = "owner", (gogoproto.moretags) = 'form:"owner"', json_name = "owner"];
    repeated DLCInfo dlc_list = 3 [(gogoproto.jsontag) = 'dlc_list', json_name = 'dlc_list'];
    message DLCInfo {
        // 收藏集ID
        int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id"', json_name = "act_id"];
        // 奖池ID
        int64 lottery_id = 2 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
        // 收藏集名称
        string act_name = 3 [(gogoproto.jsontag) = "act_name", (gogoproto.moretags) = 'form:"act_name"', json_name = "act_name"];
        // 收藏集图片
        string act_pic = 4 [(gogoproto.jsontag) = "act_pic", (gogoproto.moretags) = 'form:"act_pic"', json_name = "act_pic"];
    }
}

message CardDecomposeStateInfoReq {
    int64 card_type_id = 1;
    int64 act_id = 2;
}

message CardDecomposeStateInfoResp {
    // 是否是典藏卡
    int64 collection_card = 1;
    // 是否限量卡
    int64 limit_card = 2;
    // 是否福利卡
    int64 free_card = 3;
    // 是否上链
    int64 up_chain = 4;
    // 卡牌稀有度
    int64 card_scarcity = 5;
}

message CardTypeMetaInfo {
    // 是否是绘星卡，0否1是
    int64 is_smelt_card = 1 [(gogoproto.moretags) = 'form:"is_smelt_card"', (gogoproto.jsontag) = 'is_smelt_card', json_name = 'is_smelt_card'];
    // 左上角标签信息
    string smelt_tag = 2 [(gogoproto.moretags) = 'form:"smelt_tag"', (gogoproto.jsontag) = 'smelt_tag', json_name = 'smelt_tag'];
    // 资源
    CardTypeSmeltResource smelt_resource = 3 [(gogoproto.moretags) = 'form:"smelt_resource"', (gogoproto.jsontag) = 'smelt_resource', json_name = 'smelt_resource'];
}

message CardMetaInfo {
    // 是否是绘星卡，0否1是
    int64 is_smelt_card = 1 [(gogoproto.moretags) = 'form:"is_smelt_card"', (gogoproto.jsontag) = 'is_smelt_card', json_name = 'is_smelt_card'];
    // 是否有熔炼的资源，0没有，1有，（未来可能拓展）
    int64 has_smelted_resource = 2 [(gogoproto.moretags) = 'form:"has_smelted_resource"', (gogoproto.jsontag) = 'has_smelted_resource', json_name = 'has_smelted_resource'];
    // 是否有熔炼的参数，0没有，1有，（未来可能拓展）
    int64 has_smelted_params = 3 [(gogoproto.moretags) = 'form:"has_smelted_params"', (gogoproto.jsontag) = 'has_smelted_params', json_name = 'has_smelted_params'];
    // 卡面是否发生变换
    int64 cover_replaced = 4 [(gogoproto.moretags) = 'form:"cover_replaced"', (gogoproto.jsontag) = 'cover_replaced', json_name = 'cover_replaced'];
    // 角标图片
    string smelt_tag = 5 [(gogoproto.moretags) = 'form:"smelt_tag"', (gogoproto.jsontag) = 'smelt_tag', json_name = 'smelt_tag'];
    // 资源
    CardSmeltResource smelt_resource = 6 [(gogoproto.moretags) = 'form:"smelt_resource"', (gogoproto.jsontag) = 'smelt_resource', json_name = 'smelt_resource'];
    // 跳转链接
    CardSmeltJump smelt_jump = 7 [(gogoproto.moretags) = 'form:"smelt_jump"', (gogoproto.jsontag) = 'smelt_jump', json_name = 'smelt_jump'];
    // 熔炼生成资源状态，非1为资源未ready
    int64 resource_state = 8 [(gogoproto.moretags) = 'form:"resource_state"', (gogoproto.jsontag) = 'resource_state', json_name = 'resource_state'];
}

message CardTypeSmeltResource {
    // 效果动图，紧凑边框srk
    string dynamic_image = 1 [(gogoproto.moretags) = 'form:"dynamic_image"', (gogoproto.jsontag) = 'dynamic_image', json_name = 'dynamic_image'];
    // 效果静图
    string static_image = 2 [(gogoproto.moretags) = 'form:"static_image"', (gogoproto.jsontag) = 'static_image', json_name = 'static_image'];
}
message CardSmeltResource {
    // 效果静图，紧凑边框srk，动态图对应兜底
    string static_image = 1 [(gogoproto.moretags) = 'form:"static_image"', (gogoproto.jsontag) = 'static_image', json_name = 'static_image'];
    // 头像裁剪用
    string avatar_image = 2 [(gogoproto.moretags) = 'form:"avatar_image"', (gogoproto.jsontag) = 'avatar_image', json_name = 'avatar_image'];
    // 图片地址 目前只用于分享
    string card_img = 3 [(gogoproto.jsontag) = "card_img", (gogoproto.moretags) = 'form:"card_img"', json_name = "card_img"];
    // 图片下载地址
    string card_img_download = 4 [(gogoproto.jsontag) = "card_img_download", (gogoproto.moretags) = 'form:"card_img_download"', json_name = "card_img_download"];
    // 视频下载地址列表
    repeated string video_list_download = 5 [(gogoproto.jsontag) = "video_list_download", (gogoproto.moretags) = 'form:"video_list_download"', json_name = "video_list_download"];
}

message CardSmeltJump {
    // 渲染H5跳转链接
    string render_url = 1 [(gogoproto.moretags) = 'form:"render_url"', (gogoproto.jsontag) = 'render_url', json_name = 'render_url'];
    // 重塑链接
    string reshape_url = 2 [(gogoproto.moretags) = 'form:"reshape_url"', (gogoproto.jsontag) = 'reshape_url', json_name = 'reshape_url'];
}

message DLCRankRedeemResultReq {
    int64 act_id = 1 [(gogoproto.moretags) = 'form:"act_id"', (gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
    int64 collect_id = 2 [(gogoproto.moretags) = 'form:"collect_id"', (gogoproto.jsontag) = 'collect_id', json_name = 'collect_id'];
}

message DLCRankRedeemResultResp {
    // 公示列表
    repeated RankItem list = 1;
    // 截止时间
    int64 deadline = 2;
    // 公示结束时间
    int64 end_time = 3;
    // 排名开始
    int64 rank_from = 4;
    // 排名结束
    int64 rank_to = 5;
    message RankItem {
        // 原始排名
        int64 origin_rank = 1;
        // 用户名称
        string uname = 2;
        // 用户头像
        string uface = 3;
    }
}
message AssetBagItem {
    int64 item_type = 1;
}

message AssetItemCard {
    CardTypeIdInfo card_type_info = 1;
    CardIdItem card_info = 2;
    message CardTypeIdInfo {
        // 卡牌ID
        int64 card_type_id = 1 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id"', json_name = "card_type_id"];
        // 卡牌名
        string card_name = 2 [(gogoproto.jsontag) = "card_name", (gogoproto.moretags) = 'form:"card_name"', json_name = "card_name"];
        // 图片地址
        string card_img = 3 [(gogoproto.jsontag) = "card_img", (gogoproto.moretags) = 'form:"card_img"', json_name = "card_img"];
        // 卡牌类型(枚举值 1图片 2视频)
        int64 card_type = 4 [(gogoproto.jsontag) = "card_type", (gogoproto.moretags) = 'form:"card_type"', json_name = "card_type"];
        // 卡片总发行量（活动结束后出现）
        int64 total_cnt = 6 [(gogoproto.jsontag) = "total_cnt", (gogoproto.moretags) = 'form:"total_cnt"', json_name = "total_cnt"];
        // 卡片总发行量（活动结束后出现）
        string total_cnt_show = 7 [(gogoproto.jsontag) = "total_cnt_show", (gogoproto.moretags) = 'form:"total_cnt_show"', json_name = "total_cnt_show"];
        // 卡片持有率, 前端展示万分比，预约期前端不展示
        int64 holding_rate = 8 [(gogoproto.jsontag) = "holding_rate", (gogoproto.moretags) = 'form:"holding_rate"', json_name = "holding_rate"];
        // 视频地址列表
        repeated string video_list = 9 [(gogoproto.jsontag) = "video_list", (gogoproto.moretags) = 'form:"video_list"', json_name = "video_list"];
        // 是否为陀螺仪素材卡牌  （0否 1是）
        int64 is_physical_orientation = 10 [(gogoproto.jsontag) = "is_physical_orientation", (gogoproto.moretags) = 'form:"is_physical_orientation"', json_name = "is_physical_orientation"];
        // 卡牌稀缺度枚举
        int64 card_scarcity = 11 [(gogoproto.jsontag) = "card_scarcity", (gogoproto.moretags) = 'form:"card_scarcity"', json_name = "card_scarcity"];
        // 是否为无声视频 0否 1是
        int64 is_mute = 12 [(gogoproto.jsontag) = "is_mute", (gogoproto.moretags) = 'form:"is_mute"', json_name = "is_mute"];
        // 宽
        int64 width = 13 [(gogoproto.jsontag) = "width", (gogoproto.moretags) = 'form:"width"', json_name = "width"];
        // 高
        int64 height = 14 [(gogoproto.jsontag) = "height", (gogoproto.moretags) = 'form:"height"', json_name = "height"];
        // 卡牌描述文本，如：DLC圣诞限定款
        string card_ext_text = 15 [(gogoproto.jsontag) = "card_ext_text", (gogoproto.moretags) = 'form:"card_ext_text"', json_name = "card_ext_text"];
        // 图片下载地址
        string card_img_download = 16 [(gogoproto.jsontag) = "card_img_download", (gogoproto.moretags) = 'form:"card_img_download"', json_name = "card_img_download"];
        // 视频下载地址列表
        repeated string video_list_download = 17 [(gogoproto.jsontag) = "video_list_download", (gogoproto.moretags) = 'form:"video_list_download"', json_name = "video_list_download"];
        // 字幕链接
        string subtitles_url = 18 [(gogoproto.jsontag) = "subtitles_url", (gogoproto.moretags) = 'form:"subtitles_url"', json_name = "subtitles_url"];
        // 播放设置
        Play play = 19 [(gogoproto.jsontag) = "play", (gogoproto.moretags) = 'form:"play"', json_name = "play"];
        // 角标展示
        TagInfo tag = 20 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
        // 卡牌子类型
        int64 card_sub_type = 21 [(gogoproto.jsontag) = "card_sub_type", (gogoproto.moretags) = 'form:"card_sub_type"', json_name = "card_sub_type"];
        // 是否限量卡牌,1=是，0=否
        int64 is_limited_card = 22 [(gogoproto.jsontag) = "is_limited_card", (gogoproto.moretags) = 'form:"is_limited_card"', json_name = "is_limited_card"];
        CardStockInfo stock_info = 23 [(gogoproto.jsontag) = "stock_info", (gogoproto.moretags) = 'form:"stock_info"', json_name = "stock_info"];
        // 卡牌额外信息
        CardTypeMetaInfo meta_info = 24 [(gogoproto.moretags) = 'form:"meta_info"', (gogoproto.jsontag) = 'meta_info', json_name = 'meta_info'];
    }
}

message AssetItemCollect {
    string unique_id = 1;
    // 收集奖励id
    int64 collect_id = 2 [(gogoproto.jsontag) = "collect_id", (gogoproto.moretags) = 'form:"collect_id"', json_name = "collect_id"];
    // 收集奖励的物品类型 1:数字周边卡牌 2：表情包， 3：头像挂件，4：装扮永久套装, 7：观影券 8: cdk 9：实物奖励 10:限时装扮散件
    int64 redeem_item_type = 7 [(gogoproto.jsontag) = "redeem_item_type", (gogoproto.moretags) = 'form:"redeem_item_type"', json_name = "redeem_item_type"];
    // 收集奖励的物品id
    string redeem_item_id = 8 [(gogoproto.jsontag) = "redeem_item_id", (gogoproto.moretags) = 'form:"redeem_item_id"', json_name = "redeem_item_id"];
    // 收集奖励的物品名称
    string redeem_item_name = 9 [(gogoproto.jsontag) = "redeem_item_name", (gogoproto.moretags) = 'form:"redeem_item_name"', json_name = "redeem_item_name"];
    // 收集奖励的物品图片
    string redeem_item_image = 10 [(gogoproto.jsontag) = "redeem_item_image", (gogoproto.moretags) = 'form:"redeem_item_image"', json_name = "redeem_item_image"];
    // 是否永久生效 0-否，1-是
    int64 effective_forever = 14 [(gogoproto.jsontag) = "effective_forever", (gogoproto.moretags) = 'form:"effective_forever"', json_name = "effective_forever"];
    // 道具跳转链接
    string jump_url = 17 [(gogoproto.jsontag) = "jump_url", (gogoproto.moretags) = 'form:"jump_url"', json_name = "jump_url"];
    // 绑定奖池ID
    int64 lottery_id = 21 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
    // 奖励标签
    string reward_tag = 22 [(gogoproto.jsontag) = "reward_tag", (gogoproto.moretags) = 'form:"reward_tag"', json_name = "reward_tag"];
    // 详细预览图（奖励封面图点开之后展示
    string redeem_detail_image = 23 [(gogoproto.jsontag) = "redeem_detail_image", (gogoproto.moretags) = 'form:"redeem_detail_image"', json_name = "redeem_detail_image"];
    // 详细预览视频（奖励封面图点开之后展示
    repeated string redeem_detail_videos = 24 [(gogoproto.jsontag) = "redeem_detail_videos", (gogoproto.moretags) = 'form:"redeem_detail_videos"', json_name = "redeem_detail_videos"];
    int64 sort = 25 [(gogoproto.jsontag) = "sort", (gogoproto.moretags) = 'form:"sort"', json_name = "sort"];
    repeated string redeem_items_optional = 26 [deprecated = true, (gogoproto.jsontag) = "redeem_items_optional", (gogoproto.moretags) = 'form:"redeem_items_optional"', json_name = "redeem_items_optional"];
    // 领取信息
    RedeemCount redeem_count = 29 [(gogoproto.jsontag) = "redeem_count", (gogoproto.moretags) = 'form:"redeem_count"', json_name = "redeem_count"];
    // 领取次数
    message RedeemCount {
        // 已经领取的次数
        int64 has_redeem_count = 1 [(gogoproto.moretags) = 'form:"has_redeem_count"', (gogoproto.jsontag) = 'has_redeem_count', json_name = 'has_redeem_count'];
        // 总领取次数，当redeem_count_type=1时有意义
        int64 redeem_count = 2 [(gogoproto.moretags) = 'form:"redeem_count"', (gogoproto.jsontag) = 'redeem_count', json_name = 'redeem_count'];
        // 领取类型0=单次领取，1=多次领取，-1=无限制领取
        int64 redeem_count_type = 3 [(gogoproto.moretags) = 'form:"redeem_count_type"', (gogoproto.jsontag) = 'redeem_count_type', json_name = 'redeem_count_type'];
    }
}

message UserAssetBagListReq {
    // 活动ID
    int64 act_id = 1 [(gogoproto.jsontag) = "act_id", (gogoproto.moretags) = 'form:"act_id" validate:"required"', json_name = "act_id"];
    // 主态uid
    int64 mid = 2 [(gogoproto.moretags) = 'form:"mid"', (gogoproto.jsontag) = 'mid', json_name = 'mid'];
    // 被查看者mid
    int64 vmid = 3 [(gogoproto.moretags) = 'form:"vmid"', (gogoproto.jsontag) = 'vmid', json_name = 'vmid'];
    // 奖池ID
    int64 lottery_id = 4 [(gogoproto.moretags) = 'form:"lottery_id"', (gogoproto.jsontag) = 'lottery_id', json_name = 'lottery_id'];
    // 分页id
    string unique_id = 5 [(gogoproto.moretags) = 'form:"unique_id"', (gogoproto.jsontag) = 'unique_id', json_name = 'unique_id'];
    // 请求数据的方向，0向后，1向前，2向前向后
    int64 direction = 6 [(gogoproto.moretags) = 'form:"direction"', (gogoproto.jsontag) = 'direction', json_name = 'direction'];
    string ip = 7 [(gogoproto.moretags) = 'form:"ip"', (gogoproto.jsontag) = 'ip', json_name = 'ip'];
    // 设备信息
    DeviceInfo device = 8 [(gogoproto.moretags) = 'form:"device"', (gogoproto.jsontag) = 'device', json_name = 'device'];
    // 用户持有状态过滤，0：所有  1：已拥有 2：未拥有
    int64 owned_status = 9 [(gogoproto.moretags) = 'form:"owned_status"', (gogoproto.jsontag) = 'owned_status', json_name = 'owned_status'];
}

message UserAssetBagListResp {
    repeated AssetInfo card_list = 1 [(gogoproto.jsontag) = 'card_list', json_name = 'card_list'];
    Page front_page = 2 [(gogoproto.jsontag) = 'front_page', json_name = 'front_page'];
    Page back_page = 3 [(gogoproto.jsontag) = 'back_page', json_name = 'back_page'];
    int64 permanent_lottery_status = 4 [(gogoproto.jsontag) = 'permanent_lottery_status', json_name = 'permanent_lottery_status'];
    message Page {
        // 分页id
        string unique_id = 1 [(gogoproto.jsontag) = 'unique_id', json_name = 'unique_id'];
        // 是否还有更多数据
        bool has_more = 2 [(gogoproto.jsontag) = 'has_more', json_name = 'has_more'];
    }
    message AssetInfo {
        // 在背包页以什么维度展示资产，1卡牌，2收集度奖励
        int64 bag_asset_type = 1 [(gogoproto.jsontag) = 'bag_asset_type', json_name = 'bag_asset_type'];
        // 物品类型
        int64 item_type = 2 [(gogoproto.moretags) = 'form:"item_type"', (gogoproto.jsontag) = 'item_type', json_name = 'item_type'];
        // 物品归属，是卡池的稀有度卡牌1，或收集度奖励2
        int64 item_group_type = 3 [(gogoproto.moretags) = 'form:"item_group_type"', (gogoproto.jsontag) = 'item_group_type', json_name = 'item_group_type'];
        CardAssetInfo card = 4 [(gogoproto.jsontag) = 'card', json_name = 'card'];
        CollectRedeemInfo collect = 5 [(gogoproto.jsontag) = 'collect', json_name = 'collect'];
        // 是双向分页的锚点元素
        bool is_anchor_item = 6 [(gogoproto.jsontag) = 'is_anchor_item', json_name = 'is_anchor_item'];
        repeated Action action = 7 [(gogoproto.jsontag) = 'action', json_name = 'action'];
        repeated Module module = 8 [(gogoproto.jsontag) = 'module', json_name = 'module'];
    }
    message Action {
        string action_type = 1 [(gogoproto.jsontag) = 'action_type', json_name = 'action_type'];
        string title = 2 [(gogoproto.jsontag) = 'title', json_name = 'title'];
        map<string, string> data = 3 [(gogoproto.jsontag) = 'data', json_name = 'data'];
    }
    message Module {
        string module_type = 1 [(gogoproto.jsontag) = 'module_type', json_name = 'module_type'];
        map<string, string> data = 2 [(gogoproto.jsontag) = 'data', json_name = 'data'];
    }
    message CardAssetInfo {
        // 卡牌ID
        int64 card_type_id = 1 [(gogoproto.jsontag) = "card_type_id", (gogoproto.moretags) = 'form:"card_type_id"', json_name = "card_type_id"];
        // 卡牌名
        string card_name = 2 [(gogoproto.jsontag) = "card_name", (gogoproto.moretags) = 'form:"card_name"', json_name = "card_name"];
        // 图片地址
        string card_img = 3 [(gogoproto.jsontag) = "card_img", (gogoproto.moretags) = 'form:"card_img"', json_name = "card_img"];
        // 卡牌类型(枚举值 1图片 2视频)
        int64 card_type = 4 [(gogoproto.jsontag) = "card_type", (gogoproto.moretags) = 'form:"card_type"', json_name = "card_type"];
        // 卡片持有率, 前端展示万分比，预约期前端不展示
        int64 holding_rate = 8 [(gogoproto.jsontag) = "holding_rate", (gogoproto.moretags) = 'form:"holding_rate"', json_name = "holding_rate"];
        // 视频地址列表
        repeated string video_list = 9 [(gogoproto.jsontag) = "video_list", (gogoproto.moretags) = 'form:"video_list"', json_name = "video_list"];
        // 卡牌稀缺度枚举
        int64 card_scarcity = 11 [(gogoproto.jsontag) = "card_scarcity", (gogoproto.moretags) = 'form:"card_scarcity"', json_name = "card_scarcity"];
        // 是否为无声视频
        bool is_mute = 12 [(gogoproto.jsontag) = "is_mute", (gogoproto.moretags) = 'form:"is_mute"', json_name = "is_mute"];
        // 宽
        int64 width = 13 [(gogoproto.jsontag) = "width", (gogoproto.moretags) = 'form:"width"', json_name = "width"];
        // 高
        int64 height = 14 [(gogoproto.jsontag) = "height", (gogoproto.moretags) = 'form:"height"', json_name = "height"];
        // 卡牌描述文本，如：DLC圣诞限定款
        string card_ext_text = 15 [(gogoproto.jsontag) = "card_ext_text", (gogoproto.moretags) = 'form:"card_ext_text"', json_name = "card_ext_text"];
        // 图片下载地址
        string card_img_download = 16 [(gogoproto.jsontag) = "card_img_download", (gogoproto.moretags) = 'form:"card_img_download"', json_name = "card_img_download"];
        // 视频下载地址列表
        repeated string video_list_download = 17 [(gogoproto.jsontag) = "video_list_download", (gogoproto.moretags) = 'form:"video_list_download"', json_name = "video_list_download"];
        // 字幕链接
        string subtitles_url = 18 [(gogoproto.jsontag) = "subtitles_url", (gogoproto.moretags) = 'form:"subtitles_url"', json_name = "subtitles_url"];
        // 播放设置
        Play play = 19 [(gogoproto.jsontag) = "play", (gogoproto.moretags) = 'form:"play"', json_name = "play"];
        // 角标展示
        TagInfo tag = 20 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
        // 卡牌子类型
        int64 card_sub_type = 21 [(gogoproto.jsontag) = "card_sub_type", (gogoproto.moretags) = 'form:"card_sub_type"', json_name = "card_sub_type"];
        // 卡片ID
        int64 card_id = 24 [(gogoproto.jsontag) = "card_id", (gogoproto.moretags) = 'form:"card_id"', json_name = "card_id"];
        // 卡片编号
        int64 card_no = 25 [(gogoproto.moretags) = 'form:"card_no"', (gogoproto.jsontag) = 'card_no', json_name = 'card_no'];
        // 卡片状态（0未上链 1已上链 2转赠冻结 3转移中 4兑换中）
        int64 status = 26 [(gogoproto.jsontag) = "status", (gogoproto.moretags) = 'form:"status"', json_name = "status"];
        // 链上哈希
        string hash_code = 27 [(gogoproto.jsontag) = "hash_code", (gogoproto.moretags) = 'form:"hash_code"', json_name = "hash_code"];
        // 卡片权益
        CardRight card_right = 28 [(gogoproto.jsontag) = "card_right", (gogoproto.moretags) = 'form:"card_right"', json_name = "card_right"];
        // 卡片权益展示(前端专用)
        CardRightShow card_right_show = 29 [(gogoproto.jsontag) = "card_right_show", (gogoproto.moretags) = 'form:"card_right_show"', json_name = "card_right_show"];
        // 卡片编号颜色格式
        vas.common.FanNumColorFormat color_format = 30 [(gogoproto.jsontag) = "color_format", json_name = "color_format"];
        // 熔炼锁定状态 1未锁定 2已上锁 3锁死
        int64 smelt_lock_status = 31 [(gogoproto.jsontag) = "smelt_lock_status", json_name = "smelt_lock_status"];
        // 卡牌额外信息
        CardMetaInfo meta_info = 32 [(gogoproto.moretags) = 'form:"meta_info"', (gogoproto.jsontag) = 'meta_info', json_name = 'meta_info'];
        int64 owned_count = 33 [(gogoproto.moretags) = 'form:"owned_count"', (gogoproto.jsontag) = 'owned_count', json_name = 'owned_count'];
        int64 total_count = 34 [(gogoproto.moretags) = 'form:"total_count"', (gogoproto.jsontag) = 'total_count', json_name = 'total_count'];
    }
    message CollectRedeemInfo {
        // 收集奖励id
        int64 collect_id = 1 [(gogoproto.jsontag) = "collect_id", (gogoproto.moretags) = 'form:"collect_id"', json_name = "collect_id"];
        // 收集奖励的物品类型 1:数字周边卡牌 2：表情包， 3：头像挂件，4：装扮永久套装, 7：观影券 8: cdk 9：实物奖励 10:限时装扮散件
        int64 redeem_item_type = 7 [(gogoproto.jsontag) = "redeem_item_type", (gogoproto.moretags) = 'form:"redeem_item_type"', json_name = "redeem_item_type"];
        // 收集奖励的物品id
        string redeem_item_id = 8 [(gogoproto.jsontag) = "redeem_item_id", (gogoproto.moretags) = 'form:"redeem_item_id"', json_name = "redeem_item_id"];
        // 收集奖励的物品名称
        string redeem_item_name = 9 [(gogoproto.jsontag) = "redeem_item_name", (gogoproto.moretags) = 'form:"redeem_item_name"', json_name = "redeem_item_name"];
        // 收集奖励的物品图片
        string redeem_item_image = 10 [(gogoproto.jsontag) = "redeem_item_image", (gogoproto.moretags) = 'form:"redeem_item_image"', json_name = "redeem_item_image"];
        repeated string redeem_item_video = 11 [(gogoproto.moretags) = 'form:"redeem_item_video"', (gogoproto.jsontag) = 'redeem_item_video', json_name = 'redeem_item_video'];
        // 道具跳转链接
        string jump_url = 17 [(gogoproto.jsontag) = "jump_url", (gogoproto.moretags) = 'form:"jump_url"', json_name = "jump_url"];
        // 绑定奖池ID
        int64 lottery_id = 21 [(gogoproto.jsontag) = "lottery_id", (gogoproto.moretags) = 'form:"lottery_id"', json_name = "lottery_id"];
        // 奖励标签
        string reward_tag = 22 [(gogoproto.jsontag) = "reward_tag", (gogoproto.moretags) = 'form:"reward_tag"', json_name = "reward_tag"];
        int64 owned_count = 33 [(gogoproto.moretags) = 'form:"owned_count"', (gogoproto.jsontag) = 'owned_count', json_name = 'owned_count'];
        int64 start_time = 34 [(gogoproto.moretags) = 'form:"start_time"', (gogoproto.jsontag) = 'start_time', json_name = 'start_time'];
        int64 end_time = 35 [(gogoproto.moretags) = 'form:"end_time"', (gogoproto.jsontag) = 'end_time', json_name = 'end_time'];
        // 剩余库存：默认传-1
        int64 remain_stock = 19 [(gogoproto.jsontag) = "remain_stock", (gogoproto.moretags) = 'form:"remain_stock"', json_name = "remain_stock"];
        // 全部库存：默认传-1
        int64 total_stock = 20 [(gogoproto.jsontag) = "total_stock", (gogoproto.moretags) = 'form:"total_stock"', json_name = "total_stock"];
        // 卡牌数据
        message CardItem {
            // 抽中卡牌信息
            nft.CardTypeInfo card_type_info = 1 [(gogoproto.jsontag) = "card_type_info", (gogoproto.moretags) = 'form:"card_type_info"', json_name = "card_type_info"];
            // 播放设置
            Play play = 2 [(gogoproto.jsontag) = "play", (gogoproto.moretags) = 'form:"play"', json_name = "play"];
            // 角标展示
            TagInfo tag = 3 [(gogoproto.jsontag) = "tag", (gogoproto.moretags) = 'form:"tag"', json_name = "tag"];
            CardItemInfoInBag card_asset_info = 4 [(gogoproto.jsontag) = "card_asset_info", (gogoproto.moretags) = 'form:"card_asset_info"', json_name = "card_asset_info"];
        }
    }
}

message DLCAssetBagV2Req {
    // 当前用户mid
    int64 mid = 1 [(gogoproto.jsontag) = 'mid', json_name = 'mid'];
    // 被查看者mid
    int64 vmid = 2 [(gogoproto.moretags) = 'form:"vmid"', (gogoproto.jsontag) = 'vmid', json_name = 'vmid'];
    // 活动ID
    int64 act_id = 3 [(gogoproto.moretags) = 'form:"act_id"', (gogoproto.jsontag) = 'act_id', json_name = 'act_id'];
    // 奖池ID
    int64 lottery_id = 4 [(gogoproto.moretags) = 'form:"lottery_id"', (gogoproto.jsontag) = 'lottery_id', json_name = 'lottery_id'];
    // ip
    string ip = 5 [(gogoproto.moretags) = 'form:"ip"', (gogoproto.jsontag) = 'ip', json_name = 'ip'];
}

message DLCAssetBagV2Resp {
    // 图鉴页图片
    string act_y_img = 1 [(gogoproto.moretags) = 'form:"act_y_img"', (gogoproto.jsontag) = 'act_y_img', json_name = 'act_y_img'];
    // 卡牌总数
    int64 total_item_cnt = 2 [(gogoproto.moretags) = 'form:"total_item_cnt"', (gogoproto.jsontag) = 'total_item_cnt', json_name = 'total_item_cnt'];
    // 已拥有的卡牌总数
    int64 owned_item_cnt = 3 [(gogoproto.moretags) = 'form:"owned_item_cnt"', (gogoproto.jsontag) = 'owned_item_cnt', json_name = 'owned_item_cnt'];
    // 卡牌信息
    repeated CardItem item_list = 4 [(gogoproto.moretags) = 'form:"item_list"', (gogoproto.jsontag) = 'item_list', json_name = 'item_list'];
    // 收集度任务奖励信息
    repeated CollectItem collect_list = 5 [(gogoproto.moretags) = 'form:"collect_list"', (gogoproto.jsontag) = 'collect_list', json_name = 'collect_list'];
    // 奖池simple列表：奖池数>1时，包含【全部奖池】这一项
    repeated SimpleLottery lottery_simple_list = 6 [(gogoproto.moretags) = 'form:"lottery_simple_list"', (gogoproto.jsontag) = 'lottery_simple_list', json_name = 'lottery_simple_list'];
    message CardItem {
        // 卡牌ID
        int64 card_type_id = 1 [(gogoproto.moretags) = 'form:"card_type_id"', (gogoproto.jsontag) = 'card_type_id', json_name = 'card_type_id'];
        // 卡牌名
        string card_name = 2 [(gogoproto.moretags) = 'form:"card_name"', (gogoproto.jsontag) = 'card_name', json_name = 'card_name'];
        // 图片地址
        string card_img = 3 [(gogoproto.moretags) = 'form:"card_img"', (gogoproto.jsontag) = 'card_img', json_name = 'card_img'];
        // 卡牌类型(枚举值 1图片 2视频)
        int64 card_type = 4 [(gogoproto.moretags) = 'form:"card_type"', (gogoproto.jsontag) = 'card_type', json_name = 'card_type'];
        // 拥有数量
        int64 owned_count = 5 [(gogoproto.moretags) = 'form:"owned_count"', (gogoproto.jsontag) = 'owned_count', json_name = 'owned_count'];
        // 卡片持有率, 前端展示万分比，预约期前端不展示
        int64 holding_rate = 6 [(gogoproto.moretags) = 'form:"holding_rate"', (gogoproto.jsontag) = 'holding_rate', json_name = 'holding_rate'];
        // 卡牌稀缺度枚举
        int64 card_scarcity = 7 [(gogoproto.moretags) = 'form:"card_scarcity"', (gogoproto.jsontag) = 'card_scarcity', json_name = 'card_scarcity'];
        // 卡牌稀缺度等级枚举
        int64 card_scarcity_level = 8 [(gogoproto.moretags) = 'form:"card_scarcity_level"', (gogoproto.jsontag) = 'card_scarcity_level', json_name = 'card_scarcity_level'];
        // 角标展示
        TagInfo tag = 9 [(gogoproto.moretags) = 'form:"tag"', (gogoproto.jsontag) = 'tag', json_name = 'tag'];
        // 卡牌子类型
        int64 card_sub_type = 10 [(gogoproto.moretags) = 'form:"card_sub_type"', (gogoproto.jsontag) = 'card_sub_type', json_name = 'card_sub_type'];
        // 是否限量卡牌,1=是，0=否
        int64 is_limited_card = 11 [(gogoproto.moretags) = 'form:"is_limited_card"', (gogoproto.jsontag) = 'is_limited_card', json_name = 'is_limited_card'];
        CardStockInfo stock_info = 12 [(gogoproto.moretags) = 'form:"stock_info"', (gogoproto.jsontag) = 'stock_info', json_name = 'stock_info'];
        // 角标图
        string badge_url = 13 [(gogoproto.moretags) = 'form:"badge_url"', (gogoproto.jsontag) = 'badge_url', json_name = 'badge_url'];
    }
    message CollectItem {
        // 收集奖励id
        int64 collect_id = 1 [(gogoproto.moretags) = 'form:"collect_id"', (gogoproto.jsontag) = 'collect_id', json_name = 'collect_id'];
        // 收集奖励的物品id
        string redeem_item_id = 2 [(gogoproto.moretags) = 'form:"redeem_item_id"', (gogoproto.jsontag) = 'redeem_item_id', json_name = 'redeem_item_id'];
        int64 redeem_item_type = 3 [(gogoproto.moretags) = 'form:"redeem_item_type"', (gogoproto.jsontag) = 'redeem_item_type', json_name = 'redeem_item_type'];
        int64 card_type_id = 4 [(gogoproto.moretags) = 'form:"card_type_id"', (gogoproto.jsontag) = 'card_type_id', json_name = 'card_type_id'];
        // 收集奖励的物品名称
        string redeem_item_name = 5 [(gogoproto.moretags) = 'form:"redeem_item_name"', (gogoproto.jsontag) = 'redeem_item_name', json_name = 'redeem_item_name'];
        // 收集奖励的物品图片
        string redeem_item_image = 6 [(gogoproto.moretags) = 'form:"redeem_item_image"', (gogoproto.jsontag) = 'redeem_item_image', json_name = 'redeem_item_image'];
        // 拥有数量
        int64 owned_count = 7 [(gogoproto.moretags) = 'form:"owned_count"', (gogoproto.jsontag) = 'owned_count', json_name = 'owned_count'];
    }
    message SimpleLottery {
        // lottery_name【全部奖池】时，lottery_id=0
        int64 lottery_id = 1 [(gogoproto.moretags) = 'form:"lottery_id"', (gogoproto.jsontag) = 'lottery_id', json_name = 'lottery_id'];
        string lottery_name = 2 [(gogoproto.moretags) = 'form:"lottery_name"', (gogoproto.jsontag) = 'lottery_name', json_name = 'lottery_name'];
    }
}
