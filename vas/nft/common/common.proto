/* 卡牌前端接口 */

syntax = "proto3";
package vas.nft.common;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
option go_package = "buf.bilibili.co/bapis/bapis-gen/vas/nft.common;common";
option java_package = "com.bapis.vas.nft.common";
option java_multiple_files = true;

import "vas/common/collection.proto";

// 卡牌素材内容
message CardTypeContent {
    NFTImage image = 1 [(gogoproto.jsontag) = "image", (gogoproto.moretags) = 'form:"image"', json_name = "image"];
    Animation animation = 2 [(gogoproto.jsontag) = "animation", (gogoproto.moretags) = 'form:"animation"', json_name = "animation"];
}

// 卡牌稀有度枚举
enum Scarcity {
    Unknown = 0;
    N = 10;
    R = 20;
    SR = 30;
    SSR = 40;
    UR = 50;
}

// 卡牌类型枚举
enum MaterialType {
    UnknowNftType = 0;
    Image = 1;
    Video = 2;
}

message Animation {
    // NFT视频地址 - 视频云cdn链接
    repeated string animation_video_urls = 1 [(gogoproto.jsontag) = "animation_video_urls", json_name = "animation_video_urls"];
    // NFT视频兜底帧图片地址
    string animation_backup_image = 2 [(gogoproto.jsontag) = "animation_backup_image", json_name = "animation_backup_image"];
    // 视频第一帧（用于优化起播，不同于视频兜底图）
    string animation_first_frame = 3 [(gogoproto.jsontag) = "animation_first_frame", json_name = "animation_first_frame"];
    // 是否为无声视频
    bool is_mute = 4 [(gogoproto.jsontag) = "is_mute", json_name = "is_mute"];
    // NFT视频地址 - 视频云upos链接
    string animation_url = 5 [(gogoproto.jsontag) = "animation_url", json_name = "animation_url"];
    // 视频字幕链接
    string subtitles_url = 6 [(gogoproto.jsontag) = "subtitles_url", json_name = "subtitles_url"];
    // 视频总时长
    float duration = 7 [(gogoproto.jsontag) = "duration", json_name = "duration"];
    // width 视频-宽
    int64 width = 8 [(gogoproto.jsontag) = "width", (gogoproto.moretags) = 'form:"width"', json_name = "width"];
    // height 视频-高
    int64 height = 9 [(gogoproto.jsontag) = "height", (gogoproto.moretags) = 'form:"height"', json_name = "height"];
    // 裁剪坐标
    string location = 10 [(gogoproto.jsontag) = "location", (gogoproto.moretags) = 'form:"location"', json_name = "location"];
    // 压缩后的宽高
    string compressed_w_h = 11 [(gogoproto.jsontag) = "compressed_w_h", (gogoproto.moretags) = 'form:"compressed_w_h"', json_name = "compressed_w_h"];
}

message NFTImage {
    // 详情页默认展示图片
    string default_image = 1 [(gogoproto.jsontag) = "default_image", json_name = "default_image"];
    repeated GyroscopeEntity gyroscope = 3 [(gogoproto.jsontag) = "gyroscope", json_name = "gyroscope"];
    string location = 4 [(gogoproto.jsontag) = "location", json_name = "location"];
}

// 头像出框实体
message FaceOverflow {
    string face_url = 1 [(gogoproto.jsontag) = "face_url", json_name = "face_url"];
    repeated Overflow overflow = 2 [(gogoproto.jsontag) = "overflow", json_name = "overflow"];
    message Overflow {
        int64 layer_index = 1;                                 // 图层的层级，数值大的在上面，比如底图是0，两个叠加层分别是1、2
        string layer_type = 2;                                 // 图层的类型，"background"：底图。"overlay"：叠加层（出框）
        string file_type = 3;                                  // 素材文件的类型、"image": 图片、"video": 视频、"animation": 序列帧动画、"physical_orientation":陀螺仪
        string file_url = 4;                                   // 素材文件地址
        float scale = 5;                                       // 陀螺仪图片需要缩放后展示
        repeated PhysicalOrientation physical_orientation = 6; // 陀螺仪参数的地址、当  file_type="physical_orientation" 时不为空
    }
}

// 陀螺仪数据实体
message GyroscopeEntity {
    string trait_type = 1;
    string value = 2;
    int64 trait_count = 3;
    string display_type = 4;
    repeated GyroscopeContent contents = 5;
}

// 陀螺仪内容
message GyroscopeContent {
    string file_url = 1;
    float scale = 2;
    repeated PhysicalOrientation physical_orientation = 3;
}

// 陀螺仪偏转参数
message PhysicalOrientation {
    string type = 1;
    repeated int32 angle = 2;
    repeated PhysicalOrientationAnimation animations = 3;
}

// 陀螺仪偏转动画
message PhysicalOrientationAnimation {
    string type = 1;
    repeated float value = 2;
    string bezier = 3;
}

// 图片物料
message ImageMaterial {
    // 图片主视图，用于概览图、缩略图等
    string overview_image = 1;
    // 陀螺仪信息，可选项
    repeated GyroscopeEntity gyroscope = 2;
    // 图片属性
    repeated Attribute attributes = 5;
}

// 视频物料
message AnimationMaterial {
    // 视频关键帧，用于概览图、缩略图等
    string overview_image = 1;
    // 视频云upos链接
    string animation_url = 2;
    // 视频第一帧，用于优化起播
    string animation_first_frame = 3;
    // 是否是无声视频
    bool is_mute = 4;
    // 视频属性
    repeated Attribute attributes = 5;
    // 视频素材子类型 1-音声卡 0-老视频卡
    int64 animation_type = 6;
    // 视频时长 秒
    float duration = 7;
    // 字幕链接
    string subtitles_url = 8;
}

// 藏品属性值
message Attribute {
    // 属性名称
    string trait_type = 1;
    // 属性值
    string value = 2;
    // 属性数量
    int64 trait_count = 4;
}

message SplashInfo {
    // 闪屏资源，string枚举：normal、full_size
    map<string, vas.common.SplashResource> resource = 1 [(gogoproto.jsontag) = 'resource', json_name = 'resource'];
    // 闪屏模式  half半屏，full全屏
    string mode = 2 [(gogoproto.jsontag) = 'mode', json_name = 'mode'];
    // 闪屏logo模式 0不展示logo，1粉logo，2白logo
    int64 logo_mode = 3 [(gogoproto.jsontag) = 'logo_mode', json_name = 'logo_mode'];
}