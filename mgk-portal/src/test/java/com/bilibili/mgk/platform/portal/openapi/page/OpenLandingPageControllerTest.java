package com.bilibili.mgk.platform.portal.openapi.page;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.mgk.platform.api.form.service.IMgkFormService;
import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageService;
import com.bilibili.mgk.platform.common.page_bean.AppPackageBean;
import com.bilibili.mgk.platform.common.page_bean.PageConfig;
import com.bilibili.mgk.platform.portal.openapi.bean.OpenApiResponse;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.util.CollectionUtils;

import java.util.Collections;

public class OpenLandingPageControllerTest extends MockitoDefaultTest {

    @Mock
    private IMgkLandingPageService mgkLandingPageService;
    @Mock
    private IMgkFormService mgkFormService;

    @InjectMocks
    private OpenLandingPageController openLandingPageController;

    @Test
    public void testGetLandingPageConfig() throws ServiceException {
        Mockito.when(mgkLandingPageService.getLandingPageConfigForMobByPageId(Mockito.any()))
                .thenReturn(PageConfig.nullPageConfig());
        openLandingPageController.getLandingPageConfig(1L, "", "1", "",  1, "", "", "", "", "");

        Mockito.when(mgkLandingPageService.getLandingPageConfigForMobByPageId(Mockito.any()))
                .thenReturn(PageConfig.builder()
                        .template_style(1)
                        .show_urls(Collections.emptyList())
                        .config(Lists.newArrayList(new JSONObject()))
                        .open_whitelist(Lists.newArrayList("tbopen"))
                        .download_whitelist(Lists.newArrayList(AppPackageBean.builder()
                                .size(100)
                                .display_name("京东")
                                .apk_name("com.jingdong.app.mall")
                                .url("https://imtt.dd.qq.com/16891/apk/9F17E85E288D8D90478DA4E4DA4FC6B5.apk")
                                .md5("9F17E85E288D8D90478DA4E4DA4FC6B5")
                                .icon("https://uat-i0.hdslb.com/bfs/sycp/app_icon/202006/2a628c2489bc8f24b3c23b076d1b0e04.gif")
                                .bili_url("https://dl.hdslb.com/cm/test/633915910692545521031267871.apk")
                                .platform(2)
                                .build()))
                        .build());
        OpenApiResponse<Object> response = openLandingPageController.getLandingPageConfig(123456L, "", "1", "", 1, "", "", "", "", "");
        PageConfig pageConfig = (PageConfig) response.getData();
        Assert.assertFalse(CollectionUtils.isEmpty(pageConfig.getDownload_whitelist()));

        response = openLandingPageController.getLandingPageConfig(123456L, "android", "1", "", 1, "", "", "", "", "");
        pageConfig = (PageConfig) response.getData();
        Assert.assertFalse(CollectionUtils.isEmpty(pageConfig.getDownload_whitelist()));

        response = openLandingPageController.getLandingPageConfig(123456L, "ios", "1", "", 1, "", "", "", "", "");
        pageConfig = (PageConfig) response.getData();
        Assert.assertTrue(CollectionUtils.isEmpty(pageConfig.getDownload_whitelist()));
    }
}
