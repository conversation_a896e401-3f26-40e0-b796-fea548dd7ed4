package com.bilibili.mgk.platform.portal.webapi.enterprise.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @file: CollageArchiveVideoVo
 * @author: gaoming
 * @date: 2020/12/09
 * @version: 1.0
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CollageArchiveVideoVo {

    @ApiModelProperty(value = "稿件ID")
    private String aid;

    @ApiModelProperty(value = "稿件cid")
    private String cid;

    @ApiModelProperty(value = "稿件bvid")
    private String bvid;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "二级分区")
    private Integer tid;

    @ApiModelProperty(value = "开放时间")
    private Long pub_time;

    @ApiModelProperty(value = "创建时间")
    private Long ctime;

    @ApiModelProperty(value = "稿件状态")
    private Integer state;

    @ApiModelProperty(value = "稿件标签")
    private String tags;

    @ApiModelProperty(value = "投稿类型 1-自制、2-转载")
    private Integer copyright;

    @ApiModelProperty(value = "封面")
    private String cover;

    @ApiModelProperty(value = "宽度")
    private Long width;

    @ApiModelProperty(value = "高度")
    private Long height;

    @ApiModelProperty(value = "旋转")
    private Long rotate;

    @ApiModelProperty(value = "视频时长")
    private Long duration;

    @ApiModelProperty(value = "稿件简介")
    private String desc;

    @ApiModelProperty("昵称")
    private String nickname;
    @ApiModelProperty("头像")
    private String face;
    @ApiModelProperty("mid")
    private Long mid;
}
