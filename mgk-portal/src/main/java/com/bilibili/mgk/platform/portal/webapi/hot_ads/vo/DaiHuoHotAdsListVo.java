package com.bilibili.mgk.platform.portal.webapi.hot_ads.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DaiHuoHotAdsListVo {

    @ApiModelProperty(value = "创意ID")
    private String creativeId;

    @ApiModelProperty(value = "创意标题")
    private String creativeTitle;

    @ApiModelProperty(value = "封面图片 图片gif类型为图片链接，视频类型为封面图")
    private String imageUrl;

    @ApiModelProperty(value = "avid")
    private String avid;

    @ApiModelProperty(value = "bvid")
    private String bvid;

    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

    @ApiModelProperty(value = "账户ID")
    private String accountId;

    @ApiModelProperty(value = "曝光")
    private String pv;

    @ApiModelProperty(value = "点击")
    private String click;

    @ApiModelProperty(value = "转化数")
    private String convNum;

    @ApiModelProperty(value = "ctr")
    private Double ctr;

    @ApiModelProperty(value = "cvr")
    private Double cvr;

    @ApiModelProperty(value = "pctcvr")
    private Double pctcvr;

    @ApiModelProperty(value = "曝光等级 S A B C")
    private String pvRank;

    @ApiModelProperty(value = "ctr等级")
    private String ctrRank;

    @ApiModelProperty(value = "cvr等级")
    private String cvrRank;

    @ApiModelProperty(value = "pctcvr_rank等级")
    private String pctcvrRank;

    @ApiModelProperty(value = "统计日期类型,7d:最近7天，30d:最近30天")
    private Integer dayType;

    @ApiModelProperty(value = "统计日期")
    private String logDate;

    @ApiModelProperty(value = "是否收藏")
    private Integer isCollect;

}
