package com.bilibili.mgk.platform.portal.webapi.landing_page.vo;

import com.bilibili.mgk.platform.portal.annotation.ExcelResources;
import com.bilibili.mgk.platform.portal.webapi.game.vo.GameVo;
import com.bilibili.mgk.platform.portal.webapi.wechat.vo.WorkChatCustomerBaseInfoVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/1/19
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MgkLandingPageVo {
    @ApiModelProperty(value = "自增id")
    private Integer id;

    @ApiModelProperty(value = "账号ID")
    private Integer account_id;

    @ApiModelProperty(value = "账号名称")
    private String username;

    @ExcelResources(title = "落地页ID")
    @ApiModelProperty(value = "落地页ID")
    private String page_id;

    @ExcelResources(title = "落地页名称")
    @ApiModelProperty(value = "落地页名称")
    private String name;

    @ApiModelProperty(value = "页面标题")
    private String title;

    @ApiModelProperty(value = "模板样式")
    private Integer template_style;

    @ApiModelProperty(value = "模板样式描述：1-浮层样式 2-图文样式 3-橱窗样式 4-视频样式 100-自定义")
    private String template_style_desc;

    @ApiModelProperty(value = "访问URL")
    private String access_url;

    @ApiModelProperty(value = "生效开始时间")
    private String effective_start_time;

    @ApiModelProperty(value = "生效结束时间")
    private String effective_end_time;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ExcelResources(title = "状态描述")
    @ApiModelProperty(value = "状态描述：1-未发布 2-已发布 3-已下线 4-管理员驳回 5-已删除")
    private String status_desc;

    @ApiModelProperty(value = "影子落地页状态")
    private Integer shadow_status;

    @ApiModelProperty(value = "影子落地页状态")
    private String shadow_status_desc;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "下线原因")
    private String reason;

    @ApiModelProperty(value = "修改时间")
    private String mtime;

    @ApiModelProperty(value = "页面配置")
    private String config;

    @ApiModelProperty(value = "落地页类型 1:H5 2:原生 ")
    @NotNull(message = "落地页类型")
    private Integer page_type;

    /**
     * 为主page对应表单，影子逻辑不关心。
     */
    @ApiModelProperty(value = "表单ID")
    private List<String> form_ids;

    @ApiModelProperty(value = "稿件IDs")
    private List<String> av_ids;

    @ApiModelProperty(value = "稿件详情")
    private List<ArchiveInfoVo> archive_infos;

    @ApiModelProperty(value = "曝光监控链接")
    private List<String> show_urls;

    @ApiModelProperty(value = "客户名称")
    private String privacy_name;

    @ApiModelProperty(value = "隐私协议的url")
    private String privacy_url;

    @ApiModelProperty(value = "小游戏IDs")
    private List<Integer> mini_game_ids;

    @ApiModelProperty(value = "微信加粉手动切换开关")
    private Integer wechat_is_manual;

    @ApiModelProperty(value = "模板类型id")
    private String model_id;

    @ApiModelProperty(value = "模板类型名称")
    private String model_name;

    @ApiModelProperty(value = "是否模板")
    private Integer is_model;

    @ApiModelProperty(value = "是否支持PC")
    private Integer is_pc;

    @ApiModelProperty(value = "封面地址")
    private String page_cover;

    @ApiModelProperty(value = "pc发布地址")
    private String pc_launch_url;

    @ApiModelProperty(value = "移动发布地址")
    private String mobile_launch_url;

    @ApiModelProperty(value = "落地页背景颜色")
    private String page_bg_color;

    @ApiModelProperty(value = "落地页背景地址")
    private String page_bg_url;

    @ExcelResources(title = "消费数")
    @ApiModelProperty(value = "消费数,旧版:不展示,新版：展示消费数")
    private BigDecimal consume;

    @ExcelResources(title = "创意点击数")
    @ApiModelProperty(value = "旧版:七日创意点击数,新版:创意点击数")
    private String pv;

    @ExcelResources(title = "转化数")
    @ApiModelProperty(value = "旧版:七日转化数,新版:转化数")
    private String ctr;

    @ExcelResources(title = "转化率")
    @ApiModelProperty(value = "旧版:七日转化率 ctr/pv,新版:转化率 ctr/pv")
    private String pv_ctr_rate;

    @ApiModelProperty(value = "是否包含动态商品组件 0-不包含 1-包含")
    private Integer has_dpa_goods;

    @ApiModelProperty(value = "是否视频落地页 0-不是 1-是")
    private Integer is_video_page;

    @ApiModelProperty(value = "是否下载组件大小超过限制")
    private Integer is_download_button_over_limit;

    @ApiModelProperty(value = "下载按钮高度超限需要提示")
    private Integer download_button_over_limit_hint;

    @ApiModelProperty(value = "环境 1-测试 2-预发 3-uat 4-线上")
    private Integer env;

    @ApiModelProperty(value = "获客链接列表")
    private List<String> customer_acquisition_link_ids;

    @ApiModelProperty(value = "是否包含待审核的落地页")
    private Boolean contain_auditing_version;

    @ApiModelProperty(value = "游戏")
    private List<GameVo> games;

    @ApiModelProperty("复制来源账户id")
    private Integer copy_source_account_id;

    @ApiModelProperty("复制来源账户name")
    private String copy_source_account_name;

    @ApiModelProperty(value = "是否有欢迎词, 0-否 1-是")
    private Integer has_welcome_words;

    @ApiModelProperty(value = "欢迎词")
    private String welcome_words;

    @ApiModelProperty(value = "是否有留资组件, 0-否 1-是")
    private Integer is_leave_data;

    @ApiModelProperty(value = "是否有faq, 0-否 1-是")
    private Integer has_consult_faq;

    @ApiModelProperty(value = "faq")
    private String consult_faq;

    @ApiModelProperty(value = "三方客服code 1-商务通")
    private Integer customer_service_type;

    @ApiModelProperty(value = "三方客服name 1-商务通")
    private String customer_service_name;

    @ApiModelProperty(value = "客服头像")
    private String profile;

    @ApiModelProperty(value = "连接类型：onPage 进页建连， onMessage 开口建连")
    private String connect_type;
}