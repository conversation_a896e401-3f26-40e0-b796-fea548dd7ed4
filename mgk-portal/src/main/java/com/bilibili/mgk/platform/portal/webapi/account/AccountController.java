package com.bilibili.mgk.platform.portal.webapi.account;

import com.bapis.ad.account.label.AccountLabelListReply;
import com.bapis.ad.account.label.AccountLabelListReq;
import com.bapis.ad.account.label.AdAccountLabelServiceGrpc;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.bjcom.util.common.Arguments;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.agent.dto.AgentDto;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.soa.ISoaAccountLabelService;
import com.bilibili.crm.platform.soa.ISoaAgentService;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.mgk.platform.api.account.IMgkAccountService;
import com.bilibili.mgk.platform.api.account.dto.*;
import com.bilibili.mgk.platform.biz.service.MgkAccountService;
import com.bilibili.mgk.platform.common.MgkAccountLabelEnum;
import com.bilibili.mgk.platform.common.WhetherEnum;
import com.bilibili.mgk.platform.portal.common.BasicController;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.core.Response;
import com.bilibili.mgk.platform.portal.exception.WebApiExceptionCode;
import com.bilibili.mgk.platform.portal.service.WebLandingPageService;
import com.bilibili.mgk.platform.portal.util.SignUtils;
import com.bilibili.mgk.platform.portal.webapi.account.vo.*;
import com.bilibili.mgk.platform.portal.webapi.collage.vo.SimpleAccountVo;
import com.google.common.base.Throwables;
import io.grpc.Channel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/1/26
 **/

@Controller
@RequestMapping("/web_api/v1/account")
@Api(value = "/account", tags = "账户相关")
public class AccountController extends BasicController {
    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    @Autowired
    private ISoaQueryAccountService soaQueryAccountService;
    @Autowired
    private ISoaAgentService soaAgentService;
    @Autowired
    private IMgkAccountService mgkAccountService;
    @Autowired
    private SignUtils signUtils;
    @Autowired
    private ISoaAccountLabelService accountLabelService;
    @Resource(name = "crmChannel")
    private Channel crmChannel;
    @Resource
    private WebLandingPageService webLandingPageService;

    @Value("#{'${daihuo.department.id.list:311}'.split(',')}")
    private List<Integer> departmentIdList;

    @Value("${mgk.allow.jump.outer.link:true}")
    private Boolean allowJumpOuterLinkConfig;

    @Value("#{'${mgk.allow.game.account:3}'.split(',')}")
    private Set<Integer> supportGameAccountSet;

    @Value("#{'${mgk.awake.mini.app.id:20}'}")
    private Integer miniAppId;

    @Value("${platform.support.mgk.wechat.fan.label.id:420}")
    private int supportMgkWechatFanLabelId;

    @Value("${platform.support.mgk.wechat.person.account.black.list.label.id:422}")
    private int supportMgkWechatPersonAccountBlackListLabelId;

    @Value("${mgk.dpa.goods.labelId:276}")
    private Integer mgkDpaGoodsLabelId;

    @Value("${mgk.model.labelId:106}")
    private Integer mgkModelLabelId;

    @Value("${mgk.brand.live.reserve.labelId:1034}")
    private Integer mgkBrandLiveReserveLabelId;


    @ApiOperation(value = "查询账户信息")
    @GetMapping(value = "/get/info")
    @ResponseBody
    public Response<AccountInfoVo> getAccount(@ApiIgnore Context context) {

        final int accountId = context.getAccountId();

        Boolean forbidLogin = mgkAccountService.checkAccountLabelFromDB(accountId,
                MgkAccountLabelEnum.FORBID_LOGIN_MGK_LABEL_ID.getCode());
        if (forbidLogin) {
            return Response.FAIL(WebApiExceptionCode.BLACK_ACCOUNT);
        }

        AccountBaseDto accountDto = soaQueryAccountService.getAccountBaseDtoById(context.getAccountId());
        boolean allowJumpOuterLink = false;
        //是否是必选账户
        boolean isAdpAccount = true;
        //内容起飞账户
        if (WhetherEnum.YES.getCode().equals(accountDto.getIsSupportContent())) {
            allowJumpOuterLink = mgkAccountService.checkAccountLabelFromDB(accountId,
                    MgkAccountLabelEnum.ALLOW_JUMP_OUTER_LINK_FOR_CONTENT_FLY.getCode());
            isAdpAccount = false;
        }
        if (WhetherEnum.YES.getCode().equals(accountDto.getIsSupportPickup())) {
            allowJumpOuterLink = allowJumpOuterLink || mgkAccountService.checkAccountLabelFromDB(accountId,
                    MgkAccountLabelEnum.ALLOW_JUMP_OUTER_LINK_FOR_PICK_UP.getCode());
            isAdpAccount = false;
        }
        if (departmentIdList.contains(accountDto.getDepartmentId())) {
            allowJumpOuterLink = allowJumpOuterLink || mgkAccountService.checkAccountLabelFromDB(accountId,
                    MgkAccountLabelEnum.ALLOW_JUMP_OUTER_LINK_FOR_DAIHUO.getCode());
            isAdpAccount = false;
        }
        if (allowJumpOuterLinkConfig) {
            allowJumpOuterLink = true;
        }

        AccountLabelListReply labelListReply = AdAccountLabelServiceGrpc.newBlockingStub(crmChannel)
                .withDeadlineAfter(500, TimeUnit.MILLISECONDS)
                .withWaitForReady()
                .accountLabelList(AccountLabelListReq.newBuilder()
                        .setAccountId(accountId)
                        .build());
        final List<Integer> accountLabelIds = labelListReply.getLabelIdList();

        final boolean isSupportMgkModel = accountLabelIds.contains(mgkModelLabelId);
        final boolean isSupportMgkWechatFan = accountLabelIds.contains(supportMgkWechatFanLabelId);
        final boolean isSupportMgkWechatPersonAccountBlackList = accountLabelIds.contains(supportMgkWechatPersonAccountBlackListLabelId);
        final boolean isSupportMgkDpaGoods = accountLabelIds.contains(mgkDpaGoodsLabelId);
        final boolean isSupportMgkAuditPage = webLandingPageService.checkAccountIsForAudit(accountId);
        final boolean isSupportBrandLiveReserve = accountLabelIds.contains(mgkBrandLiveReserveLabelId);

        return Response.SUCCESS(AccountInfoVo.builder()
                .account_id(accountId)
                .username(accountDto.getUsername())
                .allow_call_up_daihuo_app(
                        mgkAccountService.checkAccountLabelFromDB(accountId,
                                MgkAccountLabelEnum.ACCOUNT_2_ALLOW_CALL_UP_DAIHUO_APP.getCode()))
                .support_customer_acquisition(true)
                .allow_jump_outer_link(allowJumpOuterLink || isAdpAccount)
                .allow_form_game_reserve(mgkAccountService.checkAccountLabelFromDB(accountId,
                        MgkAccountLabelEnum.ALLOW_FORM_GAME_RESERVE.getCode()))
                .is_support_game(supportGameAccountSet.contains(accountId))
                .is_support_hot_point_page(true)
                .business_account_has_mini_app_permission(mgkAccountService.queryAwakenAppMapping(accountId).contains(miniAppId))
                .is_support_seller(accountDto.getIsSupportSeller())
                .is_support_archive_content(accountDto.getIsSupportContent())
                .business_account_has_mini_app_permission(mgkAccountService.queryAwakenAppMapping(accountId).contains(miniAppId))
                .is_support_seller(accountDto.getIsSupportSeller())
                .is_support_archive_content(accountDto.getIsSupportContent())
                .is_support_lau_mini_game_config(true)
                .is_support_mgk_wechat_fan(isSupportMgkWechatFan)
                .is_support_mgk_wechat_person_account_black_list(isSupportMgkWechatPersonAccountBlackList)
                .is_support_mgk_model(isSupportMgkModel)
                .is_support_mgk_applets(true)
                .is_support_mgk_dpa_goods(isSupportMgkDpaGoods)
                .is_support_mgk_archive(true)
                .isSupportMgkLbsForm(true)
                .isSupportMgkAuditPage(isSupportMgkAuditPage)
                .is_support_mgk_archive(true)
                .isSupportMgkLbsForm(true)
                .isSupportMgkAuditPage(isSupportMgkAuditPage)
                .is_support_brand_live_reserve(isSupportBrandLiveReserve)
                .is_support_no_archive_upload(mgkAccountService.checkAccountLabelFromDB(accountId,
                        MgkAccountLabelEnum.IS_SUPPORT_NO_ARCHIVE_UPLOAD.getCode()))
                .build());
    }

    @ApiOperation(value = "查询账户信息")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<AccountVo> queryAccountInfo(@ApiIgnore Context context) throws ServiceException {
        AccountBaseDto accountDto = soaQueryAccountService.getAccountBaseDtoById(context.getAccountId());
        AgentDto agent = null;

        if (accountDto != null
                && accountDto.getDependencyAgentId() != null
                && accountDto.getDependencyAgentId() > 0) {
            try {
                agent = soaAgentService.getAgentById(accountDto.getDependencyAgentId());
            } catch (Exception e) {
                LOGGER.error("soaAgentService.getAgentById encounter an exception: " + Throwables.getStackTraceAsString(e));
            }
        }

        AccountVo vo = accountDto == null ? null : AccountVo.builder()
                .account_id(accountDto.getAccountId())
                .status(accountDto.getAccountStatus())
                .company_name(accountDto.getCompanyName())
                .brand_domain(accountDto.getBrandDomain())
                .username(accountDto.getUsername())
                .customer_name(accountDto.getName())
                .agent_name(agent == null ? "--" : agent.getName())
                .build();

        return Response.SUCCESS(vo);
    }

    @ApiOperation(value = "更新账户信息")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<String> updateAccountInfo(@ApiIgnore Context context,
                                       @RequestBody AccAccountVo accAccountVo) throws ServiceException {
        mgkAccountService.updateAccountInfo(getOperator(context), AccAccountDto.builder()
                .hasMgkForm(accAccountVo.getHas_mgk_form())
                .mgkFormPrivacyPolicy(accAccountVo.getMgk_form_privacy_policy())
                .build());
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "查询账户配置")
    @RequestMapping(value = "/profile", method = RequestMethod.GET)
    @ResponseBody
    public Response<AccountProfileVo> queryAccountProfile(@ApiIgnore Context context) {

        AccountProfileDto accountProfile = mgkAccountService.getAccountProfile(context.getAccountId());

        Integer allowAgentGetFormData = accountProfile == null ? IsValid.FALSE.getCode() : accountProfile.getAllowAgentGetFormData();

        return Response.SUCCESS(AccountProfileVo.builder().account_id(context.getAccountId())
                .allow_agent_get_form_data(allowAgentGetFormData)
                .build());
    }

    @ApiOperation(value = "更新账户配置")
    @RequestMapping(value = "/profile", method = RequestMethod.POST)
    @ResponseBody
    public Response updateAccountProfile(@ApiIgnore Context context,
                                         @RequestBody AccountProfileVo profileVo) {

        Arguments.of(context.getType()).in(MgkAccountService.ALLOW_FORM_DATA_OPERATORS, "您不修改授权配置");

        mgkAccountService.saveAccountProfile(AccountProfileDto.builder()
                .accountId(context.getAccountId())
                .allowAgentGetFormData(profileVo.getAllow_agent_get_form_data())
                .build(), getOperator(context));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "查询用户信息")
    @RequestMapping(value = "/card", method = RequestMethod.GET)
    @ResponseBody
    public Response<CardVo> getAccountCard(@ApiIgnore Context context,
                                           @ApiParam("用户mid") @RequestParam(value = "mid", defaultValue = "") String mid) {
        String url = signUtils.getUrl(mid);
        CardDto cardDto = mgkAccountService.getCard(url);
        LOGGER.info("card-url:" + url);
        return Response.SUCCESS(CardVo.builder()
                .name(cardDto.getName())
                .face(cardDto.getFace())
                .isBlueV(cardDto.getIsBlueV())
                .build());
    }

    @ApiModelProperty(value = "判断用户是否内广")
    @RequestMapping(value = "/inner", method = RequestMethod.GET)
    @ResponseBody
    public Response<Boolean> isInnerAccount(@ApiIgnore Context context) {
        Boolean isInnerAccount = mgkAccountService.isInnerAccount(context.getAccountId());
        return Response.SUCCESS(isInnerAccount);
    }

    @ApiOperation(value = "获取同集团同品牌的广告主账户列表")
    @RequestMapping(value = "/same_group_brand/accounts", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<SimpleAccountVo>> getSameGroupAndBrandAccounts(@ApiIgnore Context context,
                                                                        AccounQueryVo accounQueryVo) {
        AccountQueryDto accountQueryDto = AccountQueryDto.builder()
                .curAccountId(context.getAccountId())
                .searchAccountId(accounQueryVo.getAccount_id())
                .searchUsername(accounQueryVo.getUsername())
                .build();
        List<SimpleAccountDto> accountDtos =
                mgkAccountService.querySameGroupAndBrandAccounts(accountQueryDto);

        List<SimpleAccountVo> accountVos = accountDtos.stream().map(simpleAccountDto -> {
            SimpleAccountVo accountVo = new SimpleAccountVo();
            accountVo.setAccount_id(simpleAccountDto.getAccountId());
            accountVo.setUsername(simpleAccountDto.getUsername());
            return accountVo;
        }).collect(Collectors.toList());
        return Response.SUCCESS(accountVos);
    }
}