package com.bilibili.mgk.platform.portal.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.model.dto.*;
import com.bilibili.mgk.platform.biz.po.MgkModelPo;
import com.bilibili.mgk.platform.biz.service.ModelServiceDelegate;
import com.bilibili.mgk.platform.common.*;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.webapi.model.vo.*;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/07/08
 **/
@Service
public class WebModelService {
    @Autowired
    private WebLandingPageService webLandingPageService;
    @Autowired
    private ModelServiceDelegate modelServiceDelegate;

    public List<MgkModelVo> convertModelDtos2Vos(List<MgkModelDto> dtos) throws ServiceException {
        if (CollectionUtils.isEmpty(dtos)) return Collections.emptyList();
        return dtos.stream().map(this::convertModelDto2Vo).collect(Collectors.toList());
    }

    private MgkModelVo convertModelDto2Vo(MgkModelDto dto) {
        return MgkModelVo.builder()
                .model_id(dto.getModelId().toString())
                .page_id(dto.getPageId().toString())
                .name(dto.getModelName())
                .cover_url(dto.getCoverUrl())
                .status(dto.getModelStatus())
                .model_style(dto.getModelStyle())
                .model_style_name(MgkModelStyleEnum.getByCode(dto.getModelStyle()).getDesc())
                .status_desc(ModelStatusEnum.getByCode(dto.getModelStatus()).getDesc())
                .remark(dto.getRemark())
                .mtime(dto.getMtime())
                .trade_ids(CollectionUtils.isEmpty(dto.getTradeIds())?new ArrayList<>():dto.getTradeIds().stream().map(String::valueOf).collect(Collectors.toList()))
                .trade_names(dto.getTradeNames())
                .creator(dto.getCreator())
                .template_style(dto.getTemplateStyle())
                .template_style_name(dto.getTemplateStyleName())
                .model_type(dto.getModelType())
                .module_content_id(dto.getModuleContentId())
                .module_height(dto.getModuleHeight())
                .module_style_id(dto.getModuleStyleId())
                .module_weight(dto.getModuleWeight())
                .type(dto.getType())
                .parent_trade_ids(dto.getParentTradeIds().stream().map(String::valueOf).collect(Collectors.toList()))
                .parent_trade_names(dto.getParentTradeNames())
                .build();
    }

    public NewModelDto convertNewModelVo2Dto(Integer accountId, NewModelVo vo) {
        Assert.notNull(vo, "模板信息不可为空");
        return NewModelDto.builder()
                .accountId(accountId)
                .modelVersion(vo.getModel_version())
                .modelName(vo.getName())
                .modelStyle(vo.getModel_style())
                .modelType(vo.getModel_type())
                .newLandingPageDto(webLandingPageService.convertNewLandingPageVo2Dto(accountId, vo.getPage()))
                .coverUrl(vo.getCover_url())
                .remark(vo.getRemark())
                .tradeIds(vo.getTrade_ids().stream().map(Long::parseLong).collect(Collectors.toList()))
                .moduleWeight(vo.getModule_weight())
                .moduleHeight(vo.getModule_height())
                .moduleContentId(vo.getModule_content_id())
                .moduleStyleId(vo.getModule_style_id())
                .type(vo.getType() == null ? 0 : vo.getType())
                .isAdmin(vo.getIs_admin())
                .build();
    }

    public NewTradeDto convertNewTradeVo2Dto(NewTradeVo newTradeVo) {
        Assert.notNull(newTradeVo, "行业信息不可为空");
        return NewTradeDto.builder()
                .tradeLevel(newTradeVo.getLevel())
                .tradeName(newTradeVo.getName())
                .parentTradeId(Strings.isNotBlank(newTradeVo.getParent_trade_id()) ? Long.parseLong(newTradeVo.getParent_trade_id()) : 0L)
                .build();
    }

    public UpdateModelDto convertUpdateModelVo2Dto(UpdateModelVo updateModelVo) {
        Assert.notNull(updateModelVo, "模板设置不可为空");
        return UpdateModelDto.builder()
                .modelId(Long.parseLong(updateModelVo.getModel_id()))
                .modelName(updateModelVo.getName())
                .coverUrl(updateModelVo.getCover_url())
                .remark(updateModelVo.getRemark())
                .tradeIds(updateModelVo.getTrade_ids().stream().map(Long::parseLong).collect(Collectors.toList()))
                .moduleContentId(updateModelVo.getModule_content_id())
                .moduleHeight(updateModelVo.getModule_height())
                .moduleStyleId(updateModelVo.getModule_style_id())
                .moduleWeight(updateModelVo.getModule_weight())
                .build();
    }

    public void validatePermission(Context context, List<Long> modelIds) {
        Assert.notNull(context.getAccountId(), "账户ID不可为空");
        Assert.isTrue(!CollectionUtils.isEmpty(modelIds), "模板ID不可为空");
        List<MgkModelPo> mgkModelPos = modelServiceDelegate.getModelsByIds(modelIds);
        Assert.isTrue(!CollectionUtils.isEmpty(mgkModelPos), "模板不存在");
        mgkModelPos.forEach(mgkModelPo -> {
            this.validatePermission(context, mgkModelPo.getType(), mgkModelPo.getIsAdmin());
        });
    }

    public void validatePermission(Context context, Integer templateType, Integer isAdmin) {
        //自己账号可以收藏模块
        if(MgkTemplateType.MODULE.getCode().equals(templateType) && WhetherEnum.NO.getCode().equals(isAdmin)){
            return;
        }
        List<MgkRightLabelEnum> mgkRightLabelEnums = modelServiceDelegate.hasRights(context.getAccountId());
        List<String> rightCodes = mgkRightLabelEnums.stream().map(MgkRightLabelEnum::getCode).collect(Collectors.toList());
        Assert.isTrue(rightCodes.contains(MgkRightLabelEnum.MODEL_MANAGE_RIGHT.getCode()), "该账号没有权限");
    }

    public List<MgkTradeVo> convertTradeDtos2Vos(List<MgkTradeDto> tradeDtos) {
        if (CollectionUtils.isEmpty(tradeDtos)) return Collections.emptyList();
        return tradeDtos.stream().map(this::convertTradeDto2Vo).collect(Collectors.toList());
    }

    private MgkTradeVo convertTradeDto2Vo(MgkTradeDto dto) {
        return MgkTradeVo.builder()
                .parent_trade_id(dto.getParentTradeId().toString())
                .trade_id(dto.getTradeId().toString())
                .name(dto.getTradeName())
                .level(dto.getLevel())
                .build();
    }

    public List<MgkModelUsedVo> convertModelUsedDtos2Vos(List<MgkModelUsedDto> modelUsedDtos) {
        if (CollectionUtils.isEmpty(modelUsedDtos)) {
            return Collections.emptyList();
        }
        return modelUsedDtos.stream().map(this::convertModelUsedDto2Vo).collect(Collectors.toList());
    }

    private MgkModelUsedVo convertModelUsedDto2Vo(MgkModelUsedDto dto) {
        return MgkModelUsedVo.builder()
                .model_id(String.valueOf(dto.getModelId()))
                .model_name(dto.getModelName())
                .build();
    }
}
