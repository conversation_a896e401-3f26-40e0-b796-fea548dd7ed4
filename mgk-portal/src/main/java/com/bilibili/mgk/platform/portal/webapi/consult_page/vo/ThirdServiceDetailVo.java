package com.bilibili.mgk.platform.portal.webapi.consult_page.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * description: 
 * <AUTHOR>
 * @date 2025/2/27 14:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThirdServiceDetailVo {
    @ApiModelProperty(value = "第三方客服id")
    private Integer customer_service_id;

    @ApiModelProperty(value = "第三方客服code")
    private String customer_service_code;

    @ApiModelProperty(value = "第三方客服名称")
    private String customer_service_name;

    @ApiModelProperty(value = "第三方客服头像")
    private String customer_service_profile;

    @ApiModelProperty(value = "第三方客服识别码")
    private String customer_service_appId;

    @ApiModelProperty(value = "第三方客服地址")
    private String customer_service_url;

    @ApiModelProperty(value = "第三方客服授权需要入参")
    private List<String> customer_service_auth_param;


}
