package com.bilibili.mgk.platform.portal.openapi.form;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.passport.api.dto.SMSDto;
import com.bilibili.adp.passport.api.service.ISmsService;
import com.bilibili.mgk.platform.api.form.dto.MgkFormDto;
import com.bilibili.mgk.platform.api.form.dto.MgkFormItemDto;
import com.bilibili.mgk.platform.api.form.service.IMgkFormService;
import com.bilibili.mgk.platform.api.user.IUserInfoService;
import com.bilibili.mgk.platform.common.MgkFormPersonInfoOnOffEnum;
import com.bilibili.mgk.platform.common.MgkPhoneChannelEnum;
import com.bilibili.mgk.platform.portal.common.Context;
import com.bilibili.mgk.platform.portal.common.OpenApiBasicController;
import com.bilibili.mgk.platform.portal.openapi.bean.OpenApiResponse;
import com.bilibili.mgk.platform.portal.openapi.form.vo.OpenFormDataVo;
import com.bilibili.mgk.platform.portal.service.WebFormService;
import com.bilibili.mgk.platform.portal.webapi.form.vo.MgkFormItemVo;
import com.bilibili.mgk.platform.portal.webapi.form.vo.MgkFormVo;
import com.dianping.cat.Cat;
import com.google.common.base.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.bilibili.mgk.platform.common.MgkConstants.*;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-03-06
 **/
@Controller
@RequestMapping("/open_api/v1/form")
@Api(value = "/form", tags = "表单数据上报相关")
@Slf4j
public class FormDataController extends OpenApiBasicController {

    private final static String APPKEY = "fd";

    @Value("#{'${mgk.black.ip:*************,***********}'.split(',')}")
    private List<String> BLACK_IP_SET;

    @Value("${mgk.phone.identify.code.frequency:30}")
    private int identifyCodeFrequency;

    @Value("${mgk.identify.code.ip.frequency:3}")
    private int ipFrequency;

    @Value("#{'${mgk.phone.code.blacklist}'.split(',')}")
    private Set<String> phoneCodeBlackList;

    @Autowired
    private IMgkFormService mgkFormService;

    @Autowired
    private WebFormService webFormService;

    @Autowired
    private ISmsService smsService;

    @Autowired
    private IUserInfoService userInfoService;

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;


    @ApiOperation(value = "获取手机验证码")
    @RequestMapping(value = "/ttcode", method = RequestMethod.POST)
    public
    @ResponseBody
    OpenApiResponse<Object> getTelephone(
            HttpServletRequest request,
            @RequestBody OpenFormDataVo vo
    ) {
        Assert.isTrue(APPKEY.equals(vo.getAppkey()), "data is error");
        String phoneNumber = getPhoneNumber(vo.getPhone_channel(), vo.getMid(), vo.getPn());

        String ip = this.getIPAddress(request);
        Cat.logEvent("BIZ_before_getPhoneValidateCode", "0");
        log.info("phoneNumber: [{}], ip {}", phoneNumber, ip);
        if (!Strings.isNullOrEmpty(ip)) {
            String[] ipList = ip.split(",");
            for (String blackIp : ipList) {
                if (BLACK_IP_SET.contains(blackIp)) {
                    log.info("black_phoneNumber: [{}], ip {}", phoneNumber, ip);
                    return OpenApiResponse.SUCCESS(null);
                }
            }
        }

        if (phoneCodeBlackList.contains(phoneNumber)) {
            log.info("phoneNumber {} in phoneCodeBlackList", phoneNumber);
            return OpenApiResponse.SUCCESS(null);
        }

        Assert.isTrue(Utils.isPhoneNumber(phoneNumber), "请输入正确格式的手机号码");
        String tCode = stringRedisTemplate.opsForValue().get(SMS_LIMIT_IDENTIFY_CODE_PREFIX + phoneNumber);
        Assert.isTrue(Strings.isNullOrEmpty(tCode), "验证码已发送，请勿重复操作");

        Object ipCountStr = stringRedisTemplate.opsForValue().get(SMS_LIMIT_IP_PREFIX + ip);
        if (ipCountStr == null) {
            stringRedisTemplate.opsForValue().set(SMS_LIMIT_IP_PREFIX + ip, "1", 30, TimeUnit.SECONDS);
        } else {
            Integer ipCount = Integer.valueOf(ipCountStr.toString());
            if (ipCount > ipFrequency) {
                log.info("over biz_ipfrequency_limit with ip {} and ipcount {} and verify_code {}", ip, ipCount, vo.getVerify_code());
                stringRedisTemplate.opsForValue().set(SMS_LIMIT_IP_PREFIX + ip, ipCountStr.toString(), 30, TimeUnit.SECONDS);
                if (!StringUtils.isEmpty(vo.getVerify_code())) {
                    try {
                        String existVerifyCode = stringRedisTemplate.opsForValue().get(IMAGE_VERIFY_PREFIX + phoneNumber);
                        Assert.isTrue(vo.getVerify_code().equalsIgnoreCase(existVerifyCode), "验证失败");
                    } finally {
                        stringRedisTemplate.delete(IMAGE_VERIFY_PREFIX + phoneNumber);
                        log.info("delete value from redis with key {}", IMAGE_VERIFY_PREFIX + phoneNumber);
                    }
                } else {
                    return OpenApiResponse.FAIL(-1, "");
                }
            } else {
                log.info("ip {} count is {}", ip, ipCount);
                stringRedisTemplate.opsForValue().increment(SMS_LIMIT_IP_PREFIX + ip, 1L);
            }
        }

        stringRedisTemplate.opsForValue().set(SMS_LIMIT_IDENTIFY_CODE_PREFIX + phoneNumber, "2233", identifyCodeFrequency, TimeUnit.SECONDS);

        String validateCode = webFormService.getSixRandom();
        log.info("validateCode: [{}]", validateCode);
        smsService.send(SMSDto.builder()
                .mobile(phoneNumber)
                .tcode(SMS_IDENTIFY_TCODE)
                .tparam("{\"captcha\":\"" + validateCode + "\"}")
                .build());
        stringRedisTemplate.opsForValue().set(SMS_IDENTIFY_CODE_REDIS_PREFIX + phoneNumber, validateCode, SMS_IDENTIFY_CODE_EXPIRE_MINUTE, TimeUnit.MINUTES);

        Cat.logEvent("BIZ_getPhoneValidateCode", "0");
        return OpenApiResponse.SUCCESS(null);
    }

    @ApiOperation(value = "判断手机验证码是否正确")
    @RequestMapping(value = "/ttcode/validate", method = RequestMethod.POST)
    public
    @ResponseBody
    OpenApiResponse<Object> ttcodeValidate(
            HttpServletRequest request,
            @RequestBody OpenFormDataVo vo
    ) {

        Assert.isTrue(APPKEY.equals(vo.getAppkey()), "data is error");
        String phoneNumber = getPhoneNumber(vo.getPhone_channel(), vo.getMid(), vo.getPn());
        String ttcode = stringRedisTemplate.opsForValue().get(SMS_IDENTIFY_CODE_REDIS_PREFIX + phoneNumber);
        Assert.isTrue(!Strings.isNullOrEmpty(ttcode), "验证码已失效，请重新获取");
        Assert.isTrue(ttcode.equals(vo.getTtcode()), "验证码错误，请重新输入");
        return OpenApiResponse.SUCCESS(null);
    }

    private String getPhoneNumber(Integer phoneChannel, String mid, String pn){
        String phoneNumber;
        if(MgkPhoneChannelEnum.LOGIN_INFO.getCode().equals(phoneChannel)){
            Assert.notNull(mid, "获取不到您的登陆信息,请使用其他方式输入手机号");
            phoneNumber = userInfoService.queryUserSensitiveInfo(Long.parseLong(mid)).orElse(null);
        }else {
            Assert.isTrue(Base64.isBase64(pn), "data is error");
            phoneNumber = new String(Base64.decodeBase64(pn));
        }
        Assert.isTrue(!StringUtils.isEmpty(phoneNumber), "找不到对应的手机号,请您重新提交表单");
        return phoneNumber;
    }


    @ApiOperation(value = "根据表单ID获取表单详情")
    @RequestMapping(value = "/{form_id}", method = RequestMethod.GET)
    public
    @ResponseBody
    OpenApiResponse<MgkFormVo> getFormById(@ApiIgnore Context context, @PathVariable("form_id") String formId) throws ServiceException {
        MgkFormDto formDto = mgkFormService.getFormDtoByFormIdWithCache(Long.valueOf(formId), false);
        MgkFormVo formVo = webFormService.convertFormDto2Vo(formDto);
        return OpenApiResponse.SUCCESS(formVo);
    }

    @ApiOperation(value = "根据表单ID获取表单内下拉组件")
    @RequestMapping(value = "/dropdown", method = RequestMethod.GET)
    public
    @ResponseBody
    OpenApiResponse<MgkFormItemVo> getDropDownFormItemById(@ApiIgnore Context context, @RequestParam("form_id") String formId,
                                                           @RequestParam("form_item_id") String formItemId) throws ServiceException {
        MgkFormItemDto formItemDto = mgkFormService.getFormItemDtoByFormIdAndItemId(Long.valueOf(formId), Long.valueOf(formItemId));
        MgkFormItemVo mgkFormItemVo = webFormService.convertFormItemDto2Vo(formItemDto);
        return OpenApiResponse.SUCCESS(mgkFormItemVo);
    }

    @ApiOperation(value = "根据表单Id获取表单的线索数")
    @RequestMapping(value = "/count/{form_id}", method = RequestMethod.GET)
    public
    @ResponseBody
    OpenApiResponse<Integer> getFormCount(@ApiIgnore Context context, @PathVariable("form_id") String formId) {
        MgkFormDto formDto = mgkFormService.getFormDtoByFormIdWithCache(Long.valueOf(formId), false);
        Integer dataCount = Objects.isNull(formDto) ? 0 : formDto.getFormDataCount();
        return OpenApiResponse.SUCCESS(dataCount);
    }

    @ApiOperation(value = "获取个人信息保护开关")
    @RequestMapping(value = "/person_info/on_off", method = RequestMethod.GET)
    public
    @ResponseBody
    OpenApiResponse<Integer> getFormPersonInfoOnOff() {
        return OpenApiResponse.SUCCESS(MgkFormPersonInfoOnOffEnum.OPEN.getCode());
    }

}
