package com.bilibili.mgk.platform.portal.openapi.dynamic;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.dynamic.dto.MgkDynamicCreativeBizReplyDto;
import com.bilibili.mgk.platform.api.dynamic.dto.MgkDynamicLikeReplyDto;
import com.bilibili.mgk.platform.api.dynamic.dto.MgkDynamicLikeStatsReplyDto;
import com.bilibili.mgk.platform.api.dynamic.service.IMgkDynamicService;
import com.bilibili.mgk.platform.portal.common.OpenApiBasicController;
import com.bilibili.mgk.platform.portal.openapi.bean.OpenApiResponse;
import com.bilibili.mgk.platform.portal.openapi.dynamic.vo.*;
import com.bilibili.mgk.platform.portal.service.WebDynamicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @file: LikeController
 * @author: gaoming
 * @date: 2021/05/17
 * @version: 1.0
 * @description:
 **/
@Slf4j
@RestController
@RequestMapping("/open_api/v1/biz")
@Api(value = "/biz", description = "视频点赞相关")
public class DynamicController extends OpenApiBasicController {

    @Autowired
    private IMgkDynamicService dynamicService;

    @Autowired
    private WebDynamicService webDynamicService;

    @ApiOperation(value = "动态点赞")
    @RequestMapping(value = "/like", method = RequestMethod.POST)
    public
    @ResponseBody
    OpenApiResponse<MgkDynamicLikeReplyVo> dynamicLike(@ApiParam("动态点赞参数") @RequestBody MgkDynamicLikeReqVo vo) throws ServiceException {
        MgkDynamicLikeReplyDto likeReplyDto = dynamicService.dynamicLike(webDynamicService.convertLikeVo2Dto(vo));
        if (likeReplyDto == null) {
            return OpenApiResponse.SUCCESS(null);
        }
        return OpenApiResponse.SUCCESS(webDynamicService.convertLikeDto2Vo(likeReplyDto));
    }

//    @ApiOperation(value = "点踩数查询")
//    @RequestMapping(value = "/like", method = RequestMethod.GET)
//    public
//    @ResponseBody
//    OpenApiResponse<MgkDynamicLikeStatsReplyVo> getDynamicLike(@ApiParam("查询动态点赞参数") MgkDynamicLikeStatsReqVo vo) {
//        MgkDynamicLikeStatsReplyDto likeStatsReplyDto = dynamicService.dynamicLikeStats(webDynamicService.convertLikeStatsVo2Dto(vo));
//        if (likeStatsReplyDto == null) {
//            return OpenApiResponse.SUCCESS(null);
//        }
//        return OpenApiResponse.SUCCESS(webDynamicService.convertLikeStatsDto2Vo(likeStatsReplyDto));
//    }
}
