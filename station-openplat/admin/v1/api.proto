// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package main.archive.station.openplat.admin.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/station-openplat/admin.v1";
option java_package = "com.bapis.station.openplat.admin.v1";
option java_multiple_files = true;
option (gogoproto.goproto_getters_all) = false;
option (wdcli.appid) = "main.archive.station-openplat-admin";

service OpenplatAdmin {
  // BizCode信息查询
  rpc CompanyCoreBizList(CompanyCoreBizListReq) returns (CompanyCoreBizListResp);
  // 绑定用户查询
  rpc BizBindUserInfo(BizBindUserInfoReq) returns (BizBindUserInfoResp);
  // 通用奖励发放
  rpc BizSendReward(BizSendRewardReq) returns (BizSendRewardResp);
  // 奖励状态查询
  rpc BizRewardStatus(BizRewardStatusReq) returns (BizRewardStatusResp);
  // 任务列表查询
  rpc BizTaskList(BizTaskListReq) returns (BizTaskListResp);
  // 根据client id查询应用信息
  rpc GetClientInfo(GetClientInfoReq) returns(GetClientInfoResp);
}

message CompanyCoreBizInfo {
  int64 id = 1 [(gogoproto.jsontag) = 'id'];
  string app_name = 2 [(gogoproto.jsontag) = 'app_name'];
  string biz_code = 3 [(gogoproto.jsontag) = 'biz_code'];
  string coid = 4 [(gogoproto.jsontag) = 'coid'];
  string client_id = 5 [(gogoproto.jsontag) = 'client_id'];
  string url_user_bind_info = 11 [(gogoproto.jsontag) = 'url_user_bind_info'];
  string url_send_reward = 12 [(gogoproto.jsontag) = 'url_send_reward'];
  string url_reward_status = 13 [(gogoproto.jsontag) = 'url_reward_status'];
  string url_task_list = 14 [(gogoproto.jsontag) = 'url_task_list'];
  int64 ctime = 21 [(gogoproto.jsontag) = 'ctime'];
  int64 mtime = 22 [(gogoproto.jsontag) = 'mtime'];
}

message CompanyCoreBizListReq {
  // clientId
  string client_id = 1 [(gogoproto.jsontag) = 'client_id'];
}

message CompanyCoreBizListResp {
  // CompanyCoreBizInfo
  repeated CompanyCoreBizInfo list = 1 [(gogoproto.jsontag) = 'list'];
}

message BizBindUserInfoReq {
  // biz_code
  string biz_code = 1 [(gogoproto.jsontag) = 'biz_code'];
  // open_id
  string open_id = 2 [(gogoproto.jsontag) = 'open_id'];
}

message BizBindUserInfoResp {
  // code
  int64 code = 1 [(gogoproto.jsontag) = 'code'];
  // message
  string message = 2 [(gogoproto.jsontag) = 'message'];
  // request_id
  string request_id = 3 [(gogoproto.jsontag) = 'request_id'];
  // data
  Data data = 4 [(gogoproto.jsontag) = 'data'];

  message Data {
    // 是否绑定
    bool is_bind = 1 [(gogoproto.jsontag) = 'is_bind'];
    // 角色ID
    string role_id = 2 [(gogoproto.jsontag) = 'role_id'];
    // 角色名
    string role_name = 3 [(gogoproto.jsontag) = 'role_name'];
    // 分区名
    string area_name = 4 [(gogoproto.jsontag) = 'area_name'];
    // 平台
    string platform_name = 5 [(gogoproto.jsontag) = 'platform_name'];
  }
}

message BizSendRewardReq {
  // biz_code
  string biz_code = 1 [(gogoproto.jsontag) = 'biz_code'];
  // open_id
  string open_id = 2 [(gogoproto.jsontag) = 'open_id'];
  // transaction_id
  string transaction_id = 3 [(gogoproto.jsontag) = 'transaction_id'];
  // award_id
  string award_id = 4 [(gogoproto.jsontag) = 'award_id'];
  // num
  int64 num = 5 [(gogoproto.jsontag) = 'num'];
}

message BizSendRewardResp {
  // code
  int64 code = 1 [(gogoproto.jsontag) = 'code'];
  // message
  string message = 2 [(gogoproto.jsontag) = 'message'];
  // request_id
  string request_id = 3 [(gogoproto.jsontag) = 'request_id'];
  // data
  Data data = 4 [(gogoproto.jsontag) = "data"];

  message Data {
    // 预留字段
    string other_data = 1 [(gogoproto.jsontag) = "other_data"];
  }
}

message BizRewardStatusReq {
  // biz_code
  string biz_code = 1 [(gogoproto.jsontag) = 'biz_code'];
  // open_id
  string open_id = 2 [(gogoproto.jsontag) = 'open_id'];
  // transaction_id
  string transaction_id = 3 [(gogoproto.jsontag) = 'transaction_id'];
}

message BizRewardStatusResp {
  // code
  int64 code = 1 [(gogoproto.jsontag) = 'code'];
  // message
  string message = 2 [(gogoproto.jsontag) = 'message'];
  // request_id
  string request_id = 3 [(gogoproto.jsontag) = 'request_id'];
  // data
  Data data = 4 [(gogoproto.jsontag) = "data"];

  message Data {
    // 预留字段
    string other_data = 1 [(gogoproto.jsontag) = "other_data"];
  }
}

message BizTaskListReq {
  // biz_code
  string biz_code = 1 [(gogoproto.jsontag) = 'biz_code'];
  // open_id
  string open_id = 2 [(gogoproto.jsontag) = 'open_id'];
  // task_ids
  repeated string task_ids = 3 [(gogoproto.jsontag) = 'task_ids'];
}

message BizTaskListResp {
  // code
  int64 code = 1 [(gogoproto.jsontag) = 'code'];
  // message
  string message = 2 [(gogoproto.jsontag) = 'message'];
  // request_id
  string request_id = 3 [(gogoproto.jsontag) = 'request_id'];
  // data
  Data data = 4 [(gogoproto.jsontag) = 'data'];

  message Data {
    // key为task_id(string)
    map<string, TaskInfo> task_info_list = 1 [(gogoproto.jsontag) = "task_info_list"];
  }

  message TaskInfo {
    // task_id
    string task_id = 1 [(gogoproto.jsontag) = 'task_id'];
    // 当前值
    int64 current_value = 2 [(gogoproto.jsontag) = "current_value"];
    // 目标值
    int64 target_value = 3 [(gogoproto.jsontag) = "target_value"];
    // 任务完成状态:0=已完成;1=未完成;-1=状态异常（用来标识未约定的其他异常）
    int64 receive_status = 4 [(gogoproto.jsontag) = "receive_status"];
  }
}


message GetClientInfoReq{
  string client_id = 1 [(gogoproto.jsontag) = "client_id", (gogoproto.moretags) = 'form:"client_id"'];
}

message GetClientInfoResp{
  //应用名
  string app_name =1 [(gogoproto.jsontag) = "app_name", (gogoproto.moretags) = 'form:"app_name"'];
  //账号应用id
  string client_id =2 [(gogoproto.jsontag) = "client_id", (gogoproto.moretags) = 'form:"client_id"'];
  //账号应用sec
  string client_secret =3 [(gogoproto.jsontag) = "client_secret", (gogoproto.moretags) = 'form:"client_secret"'];
  //在线状态, 0未上线, 1上线
  int64 is_exist_online =4 [(gogoproto.jsontag) = "is_exist_online", (gogoproto.moretags) = 'form:"is_exist_online"'];
}