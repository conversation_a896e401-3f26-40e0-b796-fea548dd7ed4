## 编码风格
请在编写 proto 文件过程中严格遵循下列约定, 可以让 protocol buffer消息定义和他们对应的类保持一致并容易阅读. 为了给大家带来方便, 
请认真阅读如下内容, 感恩!

### 代码格式
* 保证每行 80 字符左右
* 请使用 2 个空格缩进

### 文件结构
* 文件命名: lower_snake_case.proto
* 所有文件内容应该按照如下顺序:
    * License header (if applicable)
    * File overview
    * Syntax
    * Package
    * Imports (sorted)
    * File options
    * Everything else
    
###语法
protobuf 统一使用 proto3
```protobuf
syntax = "proto3";
```
    
### 包名
包名必须小写, 并应与目录层次结构相对应. 例如: `my/package/` 包名应该为 `my.package`

`go_package` 所有 protobuf 都需要主动指定 `go_package` 命名格式 `git.bilibili.co/bapis/bapis-go/{文件目录}`

e.g. 例如 vip/card/service/api.proto 文件应该添加以下 `go_package`, 其中 `v1` 是生成的代码 package 名称**可以选择忽略**

```protobuf
option go_package = "git.bilibili.co/bapis/bapis-go/vip/card/service;v1";
```

`java_package/java_multiple_files` 所有 protobuf 需要添加 `java_package/java_multiple_files` 命名格式 `com.bapis.{文件路径}`  
路径中的 `-` 替换为`.`, `interface`替换为`interfaces`

e.g. 例如 vip/card/service/api.proto

```protobuf
option java_package = "com.bapis.vip.card.service";
option java_multiple_files = true;
```

### 消息和字段名
消息名使用驼峰法 - 例如, SongServerRequest.(**注:** 首字母大写). 字段名使用下划线分隔 - 例如, song_name.(**注:** 首字母小写)
```protobuf
message SongServerRequest {
  required string song_name = 1;
}
```
为字段名使用这种命名约定可以得到如下的访问器:

**C++:**
```c++
  const string& song_name() { ... }
  void set_song_name(const string& x) { ... }
```
**Java:**
```java
  public String getSongName() { ... }
  public Builder setSongName(String v) { ... }
```   
如果你的名称里面包含数字, 该数字应该直接加在字母后面而不是下划线, 例如: 正确 `song_name1`, 错误 `song_name_1`

### 枚举
枚举类型名使用驼峰法(首字母大写), 值的名字使用大写加下划线分隔:
```protobuf
enum Foo {
  FIRST_VALUE = 1;
  SECOND_VALUE = 2;
}
```
每个枚举值以分号(;)结束, 不要用逗号(,).

### 服务
如果.proto文件定义RPC服务, 服务名和任何rpc方法应该用驼峰法(首字母大写):
```protobuf
service FooService {
  rpc GetSomething(FooRequest) returns (FooResponse);
}
```
### Options
`json_name` 如果 protobuf 使用了 `gogoproto.jsontag` 则需要使用 `json_name` 指定相同名称, **如果没有原因，不要添加 `gogoproto.jsontag` 或 `json_name`**

```protobuf
message SubjectInfo {
    bool closed = 1 [(gogoproto.jsontag) = "closed", json_name = "closed"];
}
```

###备注
* 文档参考  [Style Guide](https://developers.google.com/protocol-buffers/docs/style)
* 编写文档  [Language Guide](https://developers.google.com/protocol-buffers/docs/overview)





