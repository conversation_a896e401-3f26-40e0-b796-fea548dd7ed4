syntax = "proto3";
package rpc.bindex;

import "cpm/bindex/ad_unit_essential.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/cpm/bindex;api";
option java_package = "com.bapis.cpm.bindex";
option java_multiple_files = true;

message AdCreativeStringInfo {
    string ad_logo_tag             = 1;
    string advertiser_desc         = 2; // story本地视频稿件化专用
    string advertiser_logo         = 3;
    string advertiser_name         = 4;
    string advertiser_page_url     = 5;
    string android_layout          = 6;
    string appstore_url            = 7;
    string callup_url              = 8;
    string click_url               = 9;
    string comment_click_url       = 10;
    string container_secondary_url = 11;
    string container_url           = 12;
    string description             = 13;
    string ext_description         = 14;
    string h5_fall_back_url        = 15;
    string image_md5               = 16;
    string iphone_layout           = 17;
    string like_count              = 18;
    string logo_md5                = 19;
    string logo_url                = 20;
    string nickname                = 21;
    string share_image_url         = 22;
    string share_sub_title         = 23;
    string share_title             = 24;
    string show_url                = 25;
    string story_url               = 26;
    string thumbnail_url           = 27;
    string thumbnail_url_md5       = 28;
    string title                   = 29;
    string trackadf                = 30;
    string tv_url                  = 31;
    string url                     = 32;
    string username                = 33; 
    string web_url                 = 34;
    string yellow_car_icon_text    = 35;
    string yellow_car_title        = 36;
    string play_monitor_url        = 37;
    string replace_conversion_url  = 38;
    string replace_app_jump_url    = 39;
}

message AdCreativeMaterial {
    int64 creative_id = 1;
	int32 cm_mark = 2;
	int32 first_business_category_id = 3;
    int32 second_business_category_id = 4;
	int32 third_business_category_id = 5;
    string image_md5 = 6;
	int64 image_md5_id = 7;
	string title = 8;
	int64 title_md5_id = 9;
    CreativeTypeEnum.Enum creative_type = 10;
	string description = 11;
	string url = 12;
	string thumbnail_url = 13;
	string thumbnail_url_md5 = 14;
	string ad_logo_tag = 15;
	string ext_description = 16;
	repeated string video_barrages = 17;
	repeated string image_urls = 18;
	string callup_url = 19;
	AdButton button = 20;
	int32 image_height = 21;
	int32 image_width = 22;
    int64 mgk_duration = 23;
    int32 animation_loop = 24; // 动图循环次数(0:无限循环)
    bool enable_share = 25;
    string share_title = 26;
    string share_sub_title = 27;
    string share_image_url = 28;
    repeated string title_token_list = 29; // 创意标注的标题
    repeated string desc_token_list = 30; // 创意标注的描述
    int64 template_id = 31;
    AutoPlayVideo auto_play_video = 32;
    string logo_url = 33;
    string logo_md5 = 34;
    string click_url = 35;
    string show_url = 36;
    string advertiser_logo = 37;
    string advertiser_name = 38;
    string advertiser_page_url = 39;
    int64 advertiser_account_id = 40;
    int64 video_id = 41;
    string username = 42;
    string android_layout = 43;
    string iphone_layout = 44;
    map<string, TemplateInfoMap> template_info_table_for_as= 45;
    int32 business_mark_id = 46;
    map<string, int64> cluster_info = 47;
    repeated string game_monitor_urls = 48;
    repeated MaterialInfo material_info_list = 49;
    int64 title_id = 50;
    int64 advertiser_mid = 51;
    repeated int32 mgk_template_ids = 52;
    repeated int64 mgk_media_ids = 53;
    int32 android_version_begin = 54;
    int32 android_version_end = 55;
    int32 ipad_version_begin = 56;
    int32 ipad_version_end = 57;
    int32 iphone_version_begin = 58;
    int32 iphone_version_end = 59;
    int32 card_type = 60;
    string style_ability = 61;
    int32 sub_tid = 62;
    int64 dynamic_id = 63;
    string like_count = 64;
    string nickname = 65;
    int64 mgk_page_id = 66; // 有转场动效的pageid
    string h5_fall_back_url = 67;
    int64 origin_mgk_page_id = 68; // 投放端建站落地页的pageid
    int32 origin_mgk_page_type = 69;
    JumpTypeEnum.Enum jump_type = 70;
    repeated ProgcreaTitle progcrea_title_list = 71;
    repeated TemplateInfo progcrea_template_list = 72;
    string advertiser_desc = 73; // story本地视频稿件化专用
    string appstore_url = 74;
    map<string, int64> material_cluster_info = 75;
    CreativeTab creative_tab = 76;
    int32 advertising_mode = 77;
    string story_url = 78;
    string yellow_car_title = 79;
    string yellow_car_icon_text = 80;
    bool support_yellow_car = 81;
    int64 container_page_id = 82;
    string container_url = 83;
    string container_secondary_url = 84;
    bool video_page = 85;
    int64 live_booking_id = 86;
    int64 video_up_mid = 87;
    int32 live_refresh_type = 88;
    string tv_url = 89;
    int32 template_style = 90;
    int64 template_page_id = 91;
    string web_url = 92;
    WxProgramInfo wx_program_game= 93;
    string trackadf = 94;
    CreativeTab under_frame_component = 95;
    bool prefer_direct_call_up = 96;
    StoryComponent story_component = 97;
    int32 conv_component_type = 98;
    int32 live_launch_type = 99;
    string comment_click_url = 100;
    ProgramLandpageInfoData program_landpage_info_data  =101;
    int64 program_landpage_group_id = 102;
    bool game_form_reserve = 103; //是否是建站落地页 0代表否 1代表是
    ForwardCommentComponent forward_comment_component = 104;
    int64 course_season_id = 105;
    int64 clustering_id = 106;
    NativeVideoInfo native_ad_video_info = 107;
    bool native_ad = 108;
    int64 season_avid = 109;
    int64 bili_space_mid = 110;
    int64 exp_clustering_id = 111;
    int64 second_show_mid = 112;
    string play_monitor_url = 113;
    int32 style_template_group_id = 114;
    VideoExtraInfo video_extra_info = 115;
    string specific_scenes = 116;
    bool use_smart_cut = 117;
    int64 episode_id = 118;
    repeated SourceTemplate  source_templates = 119;
    int32 is_replace_url = 120; // 替链
    string replace_conversion_url = 121;
    string replace_app_jump_url = 122;
    WxProgramInfo replace_wx_program_game= 123;
    int64 parent_creative_id = 124;
    MaterialComponent material_component = 125;
    repeated string dynamic_images = 126;
    repeated ProgcreaMaterial material_list = 127;  // 程序化创意物料信息，仅对新三连
    int32 ppc_type = 128;
    int64 wx_mini_game_base_id = 129;
    int64 replace_wx_mini_game_base_id = 130;
}

message MaterialComponent {
   int32 material_type = 1;
   int64 material_id = 2;
}

message VideoExtraInfo {
    // 第一次进入索引时间
    int64 enter_time = 1;
    // 累计播放
    int64 play_count = 2;
    // 当日播放数
    int64 day_play_count = 3;
    // 累计曝光数
    int64 show_count = 4;
    // 累计点击数
    int64 click_count = 5;
    // 累计花费，单位毫分
    int64 charged_cost_milli = 6;
    // 当日曝光数
    int64 day_show_count = 7;
    // 当日点击数
    int64 day_click_count = 8;
    // 当日花费,单位毫分
    int64 day_charged_cost_milli = 9;
}

message SourceTemplate {
    repeated int32 source_ids = 1;
    int32 template_id = 2;
    int32 business_mark_id = 3;
}

message NativeVideoInfo {
    int64 video_id = 1;
    uint32 audit_status = 2;
    uint32 creative_relation_status = 3;
    string title = 4;
    string cover = 5;
    string up_nick_name = 6;
}

message ForwardCommentComponent {
    int64 comment_id = 1;
    string black_text = 2;
    string blue_text = 3;
    string ios_schema_url = 4;
    string android_schema_url = 5;
    string ios_url = 6;
    string android_url = 7;
    string nickname = 8;
}

message AdUnitCreativeStrings {
    repeated AdCreativeStringInfo creative_list = 1;
}

message AdUnitMaterial {
    int64 unit_id = 1;
    repeated AdCreativeMaterial creative_list = 2;
    UnitSubjectInfo unit_subject_info = 3;
    // app package
    repeated AppPackage app_packages = 4;
    int32 enable_store_direct_launch = 5;
    int64 plan_id = 6;
    repeated int32 business_crowds = 7;
    int64 plan_budget = 8;
    BudgetTypeEnum.Enum plan_budget_type = 9;
    int64 unit_budget = 10;
    BudgetTypeEnum.Enum unit_budget_type = 11;
    WakeAppEnum.Enum wake_app = 12;
    int64 order_id = 13;
    int64 template_id = 14;
    int64 template_group_id = 15;
    GameQualityInfo game_quality_info = 16;
    int64 end_time = 17;
    int32 ad_channel_id = 18;
    int64 merchandise_id = 19;
    int64 account_id = 20;
    string sales_type = 21;
    string frequency_unit = 22;
    int32 frequency_limit = 23;
    int32 bid_price = 24;
    int64 up_mid = 25;
    int32 promotion_purpose_type = 26;
    BusinessTypeEnum.Enum business_type = 27;
    OcpcTargetType.Enum ocpc_target_type = 28; // 转换目标类型
    int64 enterprise_mid = 29; // story本地视频化专用
    repeated int32 source_ids = 30;
    int32 pay_style = 31; // 个人起飞用,20/21-激励金
    int32 game_sub_pkg = 32; // 游戏包 0-普通包,1-分发包
    int64 episode_id = 33;
    bool programmatic = 34;
    bool no_bid = 35; // nobid单元 当是nobid时没有bid_price
    int64 two_stage_bid = 36;
    repeated int32 resource_ids = 37;
    int32 smart_material = 38;
    PromotionPurposeTypeEnum.Enum plan_promotion_purpose = 39;
    AccelerateConfig accelerate_config = 40;
    double search_bid_coefficient = 41; // 搜索快投出价系数
    repeated int32 device_app_store = 42;
    bool doc_delete = 43;
    bool inner_ad = 44;
    bool support_auto = 45;
    int64 parent_unit_id = 46;
    int64 ad_product_id = 47;
    int32 library_type = 48;
    int64 product_first_category_id = 49;
    int64 product_second_category_id = 50;
    int64 product_third_category_id = 51;
    int32 search_first_price_coefficient = 52;
    string ad_mini_game_id = 53; // https://www.tapd.cn/67874887/prong/stories/view/1167874887004425631  
    int32 is_bili_native = 54;
}

// 一键起量强度
message AccelerateStrength {
    enum Enum {
        NONE = 0;
        WEAK = 1;
        STRONG = 2;
    }
}

// 一键起量配置
message AccelerateConfig {
    int64 id = 1; // 一键起量id
    AccelerateStrength.Enum strength = 2; // 强度
    int64 begin_time = 3; // 开始时间
    int64 end_time = 4; // 结束时间
    int64 budget = 5; // 预算
}