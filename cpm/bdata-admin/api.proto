syntax = "proto3";

package sycpb.cpm.bdata.admin.v1;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "cpm/bdata-admin/common.proto";
import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "sycpb.cpm.bdata-admin";
option go_package = "buf.bilibili.co/bapis/bapis-gen/cpm/bdata.admin;api";
option java_package = "com.bapis.cpm.bdata.admin";
option java_multiple_files = true;
option (gogoproto.goproto_getters_all) = false;


service BDataAdmin {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);
  // 品牌列表
  rpc BrandList(BrandListReq) returns (BrandListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/brand/list"
    };
  }
  // 品牌对应行业下的订单数量
  rpc BrandIndustryOrderNum(BrandIndustryOrderNumReq)
      returns (BrandIndustryOrderNumReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/industry/order/num"
    };
  }
  // 创建报告
  rpc CreateReport(CreateReportReq) returns (.google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/x/bdata-admin/create/report"
    };
  }
  // 报告列表
  rpc ReportList(ReportListReq) returns (ReportListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/report/list"
    };
  }
  // 品牌硬广列表
  rpc BrandHardAdList(BrandHardAdListReq) returns (BrandHardAdListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/brand/hard/ad/list"
    };
  }
  // 品牌硬广搜索列表
  rpc BrandHardAdSearchList(BrandHardAdSearchListReq)
      returns (BrandHardAdSearchListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/brand/hard/ad/search/list"
    };
  }
  // 效果广告列表
  rpc EffectAdList(EffectAdListReq) returns (EffectAdListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/effect/ad/list"
    };
  }
  // 效果广告搜索列表
  rpc EffectAdSearchList(EffectAdSearchListReq)
      returns (EffectAdSearchListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/effect/ad/search/list"
    };
  }
  // 花火商单列表
  rpc FireList(FireListReq) returns (FireListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/fire/list"
    };
  }
  // 花火商单搜索列表
  rpc FireSearchList(FireSearchListReq) returns (FireSearchListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/fire/search/list"
    };
  }
  // 品牌号列表
  rpc BrandOrgList(BrandOrgListReq) returns (BrandOrgListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/brand/org/list"
    };
  }
  // 品牌号搜索列表
  rpc BrandOrgSearchList(BrandOrgSearchListReq) returns (BrandOrgSearchListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/brand/org/search/list"
    };
  }
  // 流量分析数据
  rpc FlowAnalysisInfo(FlowAnalysisReq) returns (FlowAnalysisReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/flow/analysis"
    };
  }
  // 人群分析数据
  rpc CrowdAnalysisInfo(CrowdAnalysisInfoReq) returns (CrowdAnalysisInfoReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/crowd/analysis"
    };
  }
  // 触点列表接口
  rpc ContactPointList(.google.protobuf.Empty) returns (ContactPointListReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/contact/point/list"
    };
  }
  // 互动指标选项接口
  rpc InteractTarget(.google.protobuf.Empty) returns (InteractTargetReply) {
    option (google.api.http) = {
      get: "/x/bdata-admin/interact/target/list"
    };
  }
}

message BrandListReq {
  // 品牌名称
  int64 BrandName = 1 [
    (gogoproto.moretags) = 'form:"brand_name"',
    (gogoproto.jsontag) = 'brand_name'
  ];
  // 分页 from
  int64 From = 2 [
    (gogoproto.moretags) = 'form:"from" validate:"required"',
    (gogoproto.jsontag) = 'from'
  ];
  // 每页条数 limit
  int64 Limit = 3 [
    (gogoproto.moretags) = 'form:"limit" validate:"required,max=50"',
    (gogoproto.jsontag) = 'limit'
  ];
}

message BrandListReply {
repeated BrandInfo BrandInfos = 1 [(gogoproto.jsontag) = 'brand_infos'];
}

message BrandIndustryOrderNumReq {
// 品牌id
int64 BrandId = 1 [
(gogoproto.moretags) = 'form:"brand_id" validate:"required"',
(gogoproto.jsontag) = 'brand_id'
];
// 行业id
int64 IndustryId = 2 [
(gogoproto.moretags) = 'form:"industry_id" validate:"required"',
(gogoproto.jsontag) = 'industry_id'
];
// 开始时间
int64 StartTime = 3 [
(gogoproto.moretags) = 'form:"start_time" validate:"required"',
(gogoproto.jsontag) = 'start_time',
(gogoproto.casttype) = "go-common/library/time.Time"
];
// 结束时间
int64 EndTime = 4 [
(gogoproto.moretags) = 'form:"end_time" validate:"required"',
(gogoproto.jsontag) = 'end_time',
(gogoproto.casttype) = "go-common/library/time.Time"
];
}

message BrandIndustryOrderNumReply {
// 品牌硬广订单数量
int64 BrandHardAdvertisementNum = 1
[(gogoproto.jsontag) = 'brand_hard_advertisement_num'];
// 效果广告单元数量
int64 EffectAdvertisementNum = 2
[(gogoproto.jsontag) = 'effect_advertisement_num'];
// 花火商单数量
int64 FireAdvertisementNum = 3
[(gogoproto.jsontag) = 'fire_advertisement_num'];
// 品牌号数量
int64 BrandOrgNum = 4 [(gogoproto.jsontag) = 'brand_org_num'];
}

message CreateReportReq {
// 品牌id
int64 BrandId = 1 [
(gogoproto.moretags) = 'form:"brand_id" validate:"required"',
(gogoproto.jsontag) = 'brand_id'
];
// 行业id
int64 IndustryId = 2 [
(gogoproto.moretags) = 'form:"industry_id" validate:"required"',
(gogoproto.jsontag) = 'industry_id'
];
// 开始时间
int64 StartTime = 3 [
(gogoproto.moretags) = 'form:"start_time" validate:"required"',
(gogoproto.jsontag) = 'start_time',
(gogoproto.casttype) = "go-common/library/time.Time"
];
// 结束时间
int64 EndTime = 4 [
(gogoproto.moretags) = 'form:"end_time" validate:"required"',
(gogoproto.jsontag) = 'end_time',
(gogoproto.casttype) = "go-common/library/time.Time"
];
// 报告名称
string Name = 5 [
(gogoproto.moretags) = 'form:"name" validate:"required"',
(gogoproto.jsontag) = 'name'
];
// 报告计算规则
// {"brand_hard_advertisement":{"order_ids":[], // 订单id"all":-1,},"effect_advertisement":{"delivery_ids":[],//单元id"all":0,//数组为空all=0未选择//all=-1全选//all=0数组不为空为部分选择},"fire_advertisement":{"av_ids":[123]//稿件id},"brand_org":{"av_ids":[567,789]//稿件id}}
string Rules = 6 [
(gogoproto.moretags) = 'form:"rules" validate:"required"',
(gogoproto.jsontag) = 'rules'
];
// 是否保存草稿 0:草稿 1:开始计算
IsDraftEnum IsDraft = 7 [
(gogoproto.moretags) = 'form:"is_draft" validate:"required"',
(gogoproto.jsontag) = 'is_draft'
];
}

message ReportListReq {
// 品牌id
int64 BrandId = 1 [
(gogoproto.moretags) = 'form:"brand_id" validate:"required"',
(gogoproto.jsontag) = 'brand_id'
];
// 分页 from
int64 From = 2 [
(gogoproto.moretags) = 'form:"from" validate:"required"',
(gogoproto.jsontag) = 'from'
];
// 每页条数 limit
int64 Limit = 3 [
(gogoproto.moretags) = 'form:"limit" validate:"required,max=50"',
(gogoproto.jsontag) = 'limit'
];
}

message ReportListReply {
// 报告列表
repeated ReportInfo ReportInfos = 1 [(gogoproto.jsontag) = 'report_infos'];
// 总条数
int64 Total = 2 [(gogoproto.jsontag) = 'total'];
}

message ReportInfo {
// 报告id
string ReportId = 1 [(gogoproto.jsontag) = 'report_id'];
// 报告名称
string Name = 2 [(gogoproto.jsontag) = 'name'];
// 报告状态 0:草稿 1:排队中 2:计算中 3:计算完成 4:计算失败
ReportStateEnum State = 3 [(gogoproto.jsontag) = 'state'];
// 报告计算开始日期- 精确到秒的时间戳
int64 StartTime = 4 [(gogoproto.jsontag) = 'start_time'];
// 报告计算结束日期- 精确到秒的时间戳
int64 EndTime = 5 [(gogoproto.jsontag) = 'end_time'];
// 计算规则
string Rules = 6 [(gogoproto.jsontag) = 'rules'];
// 创建时间
int64 Ctime = 7 [(gogoproto.jsontag) = 'ctime'];
}

message BrandHardAdListReq {
// 品牌id
int64 BrandId = 1 [
(gogoproto.moretags) = 'form:"brand_id" validate:"required"',
(gogoproto.jsontag) = 'brand_id'
];
// 行业id
int64 IndustryId = 2 [
(gogoproto.moretags) = 'form:"industry_id" validate:"required"',
(gogoproto.jsontag) = 'industry_id'
];
// 订单id
int64 OrderId = 3 [
(gogoproto.moretags) = 'form:"order_id"',
(gogoproto.jsontag) = 'order_id'
];
// 合同号
int64 ContractNo = 4 [
(gogoproto.moretags) = 'form:"order_id"',
(gogoproto.jsontag) = 'order_id'
];
// 分页 from
int64 From = 5 [
(gogoproto.moretags) = 'form:"from" validate:"required"',
(gogoproto.jsontag) = 'from'
];
// 每页条数 limit
int64 Limit = 6 [
(gogoproto.moretags) = 'form:"limit" validate:"required,max=50"',
(gogoproto.jsontag) = 'limit'
];
}

message BrandHardAdListReply {
// 品牌硬广列表信息
repeated BrandHardAdInfo BrandHardAdInfos = 1
[(gogoproto.jsontag) = 'brand_hard_ad_infos'];
// 总条数
int64 Total = 2 [(gogoproto.jsontag) = 'total'];
}

message BrandHardAdInfo {
// 订单id
int64 OrderId = 1 [(gogoproto.jsontag) = 'order_id'];
// 订单名称
string OrderName = 2 [(gogoproto.jsontag) = 'order_name'];
// 订单开始时间
int64 StartTime = 3 [(gogoproto.jsontag) = 'start_time'];
// 订单结束时间
int64 EndTime = 4 [(gogoproto.jsontag) = 'end_time'];
// 合同号
string ContractNo = 5 [(gogoproto.jsontag) = 'contract_no'];
// 合同名称
string ContractName = 6 [(gogoproto.jsontag) = 'contract_name'];
}

message BrandHardAdSearchListReq {
// 订单id,多个以都好分割
repeated int64 OrderIds = 1 [
(gogoproto.jsontag) = 'order_ids',
(gogoproto.moretags) = 'form:"order_ids,split,max=100" validate:"required"'
];
}

message BrandHardAdSearchListReply {
// 品牌硬广列表信息
repeated BrandHardAdInfo BrandHardAdInfos = 1
[(gogoproto.jsontag) = 'brand_hard_ad_infos'];
}

message EffectAdListReq {
// 品牌id
int64 BrandId = 1 [
(gogoproto.moretags) = 'form:"brand_id" validate:"required"',
(gogoproto.jsontag) = 'brand_id'
];
// 行业id
int64 IndustryId = 2 [
(gogoproto.moretags) = 'form:"industry_id" validate:"required"',
(gogoproto.jsontag) = 'industry_id'
];
// 单元id
int64 DeliveryId = 3 [
(gogoproto.moretags) = 'form:"delivery_id"',
(gogoproto.jsontag) = 'delivery_id'
];
// 账号id
int64 AccountId = 4 [
(gogoproto.moretags) = 'form:"account_id"',
(gogoproto.jsontag) = 'account_id'
];
// 分页 from
int64 From = 5 [
(gogoproto.moretags) = 'form:"from" validate:"required"',
(gogoproto.jsontag) = 'from'
];
// 每页条数 limit
int64 Limit = 6 [
(gogoproto.moretags) = 'form:"limit" validate:"required,max=50"',
(gogoproto.jsontag) = 'limit'
];
}

message EffectAdListReply {
// 效果广告列表信息
repeated EffectAdInfo EffectAdInfos = 1
[(gogoproto.jsontag) = 'effect_ad_infos'];
// 总条数
int64 Total = 2 [(gogoproto.jsontag) = 'total'];
}

message EffectAdInfo {
// 单元id
int64 DeliveryId = 1 [(gogoproto.jsontag) = 'delivery_id'];
;
// 单元名称
string DeliveryName = 2 [(gogoproto.jsontag) = 'delivery_name'];
;
// 账号id
int64 AccountNo = 3 [(gogoproto.jsontag) = 'account_no'];
;
// 三连账号
string AccountName = 4 [(gogoproto.jsontag) = 'account_name'];
;
}

message EffectAdSearchListReq {
repeated int64 OrderIds = 1 [
(gogoproto.jsontag) = 'delivery_ids',
(gogoproto.moretags) =
'form:"delivery_ids,split,max=100" validate:"required"'
];
}

message EffectAdSearchListReply {
// 效果广告列表信息
repeated EffectAdInfo EffectAdInfos = 1
[(gogoproto.jsontag) = 'effect_ad_infos'];
}

message FireListReq {
// 品牌id
int64 BrandId = 1 [
(gogoproto.moretags) = 'form:"brand_id" validate:"required"',
(gogoproto.jsontag) = 'brand_id'
];
// 行业id
int64 IndustryId = 2 [
(gogoproto.moretags) = 'form:"industry_id" validate:"required"',
(gogoproto.jsontag) = 'industry_id'
];
// 单元id
int64 DeliveryId = 3 [
(gogoproto.moretags) = 'form:"delivery_id"',
(gogoproto.jsontag) = 'delivery_id'
];
// 账号id
int64 AccountId = 4 [
(gogoproto.moretags) = 'form:"account_id"',
(gogoproto.jsontag) = 'account_id'
];
// 分页 from
int64 From = 5 [
(gogoproto.moretags) = 'form:"from" validate:"required"',
(gogoproto.jsontag) = 'from'
];
// 每页条数 limit
int64 Limit = 6 [
(gogoproto.moretags) = 'form:"limit" validate:"required,max=50"',
(gogoproto.jsontag) = 'limit'
];
}

message FireListReply {
// 花火商单列表
repeated FireInfo FireInfos = 1 [(gogoproto.jsontag) = 'fire_infos'];
// 总条数
int64 Total = 2 [(gogoproto.jsontag) = 'total'];
}

message FireInfo {
// bvid
string Bvid = 1 [(gogoproto.jsontag) = 'bvid'];
// avid
int64 avid = 2 [(gogoproto.jsontag) = 'avid'];
// 标题
string Title = 3 [(gogoproto.jsontag) = 'title'];
// 昵称
string Nickname = 4 [(gogoproto.jsontag) = 'nickname'];
// mid
int64 mid = 5 [(gogoproto.jsontag) = 'mid'];
// 订单id
int64 OrderId = 6 [(gogoproto.jsontag) = 'order_id'];
// 任务名称
string TaskName = 7 [(gogoproto.jsontag) = 'task_name'];
}

message FireSearchListReq {
repeated int64 Avids = 1 [
(gogoproto.jsontag) = 'avids',
(gogoproto.moretags) = 'form:"avids,split,max=100" validate:"required"'
];
}

message FireSearchListReply {
// 花火商单列表
repeated FireInfo FireInfos = 1 [(gogoproto.jsontag) = 'fire_infos'];
}

message BrandOrgListReq {
// 品牌id
int64 BrandId = 1 [
(gogoproto.moretags) = 'form:"brand_id" validate:"required"',
(gogoproto.jsontag) = 'brand_id'
];
// 行业id
int64 IndustryId = 2 [
(gogoproto.moretags) = 'form:"industry_id" validate:"required"',
(gogoproto.jsontag) = 'industry_id'
];
// 单元id
int64 Mid = 3
[(gogoproto.moretags) = 'form:"mid"', (gogoproto.jsontag) = 'mid'];
// 账号id
string Bvid = 4
[(gogoproto.moretags) = 'form:"bvid"', (gogoproto.jsontag) = 'bvid'];
// 分页 from
int64 From = 5 [
(gogoproto.moretags) = 'form:"from" validate:"required"',
(gogoproto.jsontag) = 'from'
];
// 每页条数 limit
int64 Limit = 6 [
(gogoproto.moretags) = 'form:"limit" validate:"required,max=50"',
(gogoproto.jsontag) = 'limit'
];
}

message BrandOrgListReply {
// 品牌号列表
repeated BrandOrgInfo BrandOrgInfos = 1
[(gogoproto.jsontag) = 'brand_org_infos'];
// 总条数
int64 Total = 2 [(gogoproto.jsontag) = 'total'];
}

message BrandOrgInfo {
// bvid
string Bvid = 1 [(gogoproto.jsontag) = 'bvid'];
// avid
int64 avid = 2 [(gogoproto.jsontag) = 'avid'];
// 标题
string Title = 3 [(gogoproto.jsontag) = 'title'];
// 昵称
string Nickname = 4 [(gogoproto.jsontag) = 'nickname'];
// mid
int64 mid = 5 [(gogoproto.jsontag) = 'mid'];
// 开放浏览时间
int64 PubTime = 6 [(gogoproto.jsontag) = 'pub_time'];
}

message BrandOrgSearchListReq {
repeated int64 Avids = 1 [
(gogoproto.jsontag) = 'avids',
(gogoproto.moretags) = 'form:"avids,split,max=100" validate:"required"'
];
}

message BrandOrgSearchListReply {
// 品牌号列表
repeated BrandOrgInfo BrandOrgInfos = 1
[(gogoproto.jsontag) = 'brand_org_infos'];
}

message FlowAnalysisReq {
// 报告id
string ReportId = 1 [(gogoproto.jsontag) = 'report_id'];
// 一级触点id
int64 FirstPointId = 2 [(gogoproto.jsontag) = 'first_point_id'];
// 二级触点id
int64 SecondPointId = 3 [(gogoproto.jsontag) = 'second_point_id'];
// 流量分析类型 1:活动表现 2：触点分布 3:趋势分析
FlowAnalysisEnum Type = 4 [(gogoproto.jsontag) = 'type'];
}

message FlowAnalysisReply {
// 分析结果数据
string StatsResult = 1 [(gogoproto.jsontag) = 'stats_result'];
}

message CrowdAnalysisInfoReq {
// 维度
string Dim = 1 [(gogoproto.jsontag) = 'dim'];
// 一级触点id
int64 FirstPointId = 2 [(gogoproto.jsontag) = 'first_point_id'];
}

message CrowdAnalysisInfoReply {
repeated CrowdAnalysisInfo RatioStats = 1
[(gogoproto.jsontag) = 'ratio_stats'];
repeated CrowdAnalysisInfo TgiStats = 2 [(gogoproto.jsontag) = 'tgi_stats'];
}

message CrowdAnalysisInfo {
// 标签值
string TagValue = 1 [(gogoproto.jsontag) = 'tag_value'];
// 占比
float Ratio = 2 [(gogoproto.jsontag) = 'ratio'];
// tgi
float Tgi = 3 [(gogoproto.jsontag) = 'tgi'];
// 额外数据信息 比如 avid/ 分区关系等数据信息
string Extra = 4 [(gogoproto.jsontag) = 'extra'];
}

message ContactPointListReply {
// 触点列表
repeated PointInfo Points = 1 [(gogoproto.jsontag) = 'points'];
}

message PointInfo {
// 名称
string Name = 1 [(gogoproto.jsontag) = 'name'];
// 对应id
int64 ObjId = 2 [(gogoproto.jsontag) = 'obj_id'];
// 子结构数据信息
repeated PointInfo Children = 3 [(gogoproto.jsontag) = 'children'];
}

message InteractTargetReply {
// 效果指标列表
repeated PointInfo Targets = 1 [(gogoproto.jsontag) = 'targets'];
}

enum IsDraftEnum {
// 草稿
DraftEnum = 0;
// 非草稿
DraftNoEnum = 1;
}
