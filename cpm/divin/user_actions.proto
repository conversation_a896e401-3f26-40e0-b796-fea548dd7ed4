syntax = "proto3";

package rpc.user_profile;

option go_package = "buf.bilibili.co/bapis/bapis-gen/cpm/divin;api";
option java_package = "com.bapis.cpm.divin";
option java_multiple_files = true;

// 行为类型，每个行为类型有一个行为列表记录
enum ActionType {
    // 无意义
    ACTION_TYPE_UNSPECIFIED = 0;

    // 点击行为
    CLICK = 1;

    // 播放行为
    PLAY = 2;

    // 关注行为
    FOLLOW = 13;

    // 仅仅表达取关，不会作为一种actioin_type存储在UserActions中，同理取消点赞
    CANCEL_FOLLOW = 14;

    // 拉黑
    BLACK = 15;

    // 取消拉黑
    CANCEL_BLACK = 16;

}
message ActionInfo {
    int64 avid = 1;

    // 视频分区
    int64 tid = 2;

    // 视频子分区
    int64 sub_tid = 3;

    // 视频标签
    repeated int64 tags = 4;

    // 视频发布的up主mid
    int64 up_mid = 5;

    // up主的分区
    int64 up_tid = 6;

    // up主的子分区
    int64 up_sub_tid = 7;

    // up主90天活跃分区
    int64 up_activity_90_tid = 8;

    // up主90天活跃子分区
    int64 up_activity_90_sub_tid = 9;

    // 记录发生行为的时间戳，ms级别
    int64 ts = 10;

    // 来源，例如播放行为来自于天马或者其他
    int64 from_section = 11;

    // 自动播放
    int64 auto_play = 12;
}

message Actions {
    // 用户的行为列表
    repeated ActionInfo actions = 1;
}

// 用户行为数据
message UserActions{
    // 版本号，用最后一次行为的时间戳
    int64 version = 1;

    // map<ActionType, Actions> 用户内容行为记录
    map<int32, Actions> content_data = 2;

    //map<int32, Actions> ad_data = 2
    //map<int32, Actions> fly_data = 3
}