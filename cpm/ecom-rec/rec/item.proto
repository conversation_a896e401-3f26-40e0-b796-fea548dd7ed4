syntax="proto3";

package rec;

import "cpm/ecom-rec/goods/goods.proto";
import "cpm/ecom-rec/video/video.proto";
import "cpm/ecom-rec/game/game.proto";
import "cpm/ecom-rec/live/live.proto";
import "cpm/ecom-rec/image_text/image_text.proto";
import "cpm/ecom-rec/delivery/delivery.proto";
import "cpm/ecom-rec/material/material.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/cpm/ecom.rec.rec;api";
option java_package = "com.bapis.cpm.ecom.rec.rec";
option java_multiple_files = true;

// for bs trigger
message Doc {
    int64 doc_id = 1;
    float score = 2;
}

enum ItemType {
    ITEM_UNKNOWN = 0;
    ITEM_VIDEO = 1;
    ITEM_IMAGE_TEXT = 2;
    ITEM_GOODS = 3;
    ITEM_LIVE = 4;
    ITEM_GAME = 5;
    ITEM_DELIVERY = 10; // 投放卡
}

// Item 上面挂的品
message LinkedItem {
    int64 item_id = 1;
    // 3: 挂的是会员购商品,
    // 5: 挂的是游戏
    int32 item_type = 2; 
}

// for as/bs rank
message Item {
    // 1: VIDEO
    // 2: IMAGE_TEXT
    // 3: GOODS
    // 4: LIVE
    // 5: GAME
    // 10: DELIVERY
    int32 content_type = 1; // 内容类型，1:视频 / 2:图文 / 3:会员购商品 / 4:直播 / 5:游戏
    int64 content_id = 2; // 内容id

    int32 trigger_type = 3; // first trigger type in range 0-63
    int64 trigger_type_mask = 4; // trigger type bit mask

    float score = 5;

    // 该 item 的 up 主
    int64 up_mid = 7;

    // detail obj
    goods.Goods goods = 10;
    video.Video video = 11;
    game.Game game = 12;
    image_text.ImageText image_text = 13;
    live.Live live = 14;

    delivery.Delivery delivery = 19;

    // linked goods (deprecated)
    repeated goods.Goods linked_goods = 20;

    // linked item
    repeated LinkedItem linked_item = 21;

    repeated material.Material material_list = 22; //优选素材列表，需要走北极星上报
}

// for render
message Card {
    int32 card_type = 1;
    string title = 2;
    string description = 3;
    int64 up_mid = 4;
    repeated string image_url = 5;
    string trackid = 6;
    int64 circle_id = 7; // 所属圈子id

    goods.GoodsRender goods = 10;
    video.VideoRender video = 11;
    live.LiveRender live = 12;
    image_text.ImageTextRender image_text = 13;
}
