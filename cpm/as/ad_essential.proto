syntax="proto3";

package rpc.as;

option go_package = "buf.bilibili.co/bapis/bapis-gen/cpm/as;api";
option java_package = "com.bapis.cpm.as";
option java_multiple_files = true;

message AndroidTag {
    string text = 1;
    int32 type = 2;
}

message Module1 {
    string game_name = 1;
    string game_icon = 2;
    string developer_input_name = 3; // 发行商简介
    repeated AndroidTag tag_list = 4;  // 游戏核心标签，type见tapd，默认type为6
}

message QualityParmas {
    string first_line = 1;
    string second_line = 2;
    double grade = 3;
    string rank_icon = 4; // 榜单icon
    int32 quality_type = 5; // 1为grade，2为榜单，3为质量信息 
}

message Module3 {
    bool display = 1;
    repeated QualityParmas quality_params = 3;
}

message Module4 {
    bool display = 1;
    int32 gift_num = 2;
    string gift_name = 3;
    int32 gift_icon_num = 4; // 道具数量
    repeated string icon_urls = 5;
    repeated int64 gift_info_ids = 6; // 可领礼包id列表
}

message Module5 {
    bool display = 1;
    string game_summary = 2; // 游戏一句话介绍
}

message Module6 {
    bool display = 1;
    string game_desc = 2; // 游戏简介
}

message ScreenShots {
    string url = 1; // 五图url
    int32 height = 2;
    int32 width = 3;
    int32 seq = 4; // 五图顺序
}

message Module7 {
    bool display = 1;
    repeated ScreenShots screen_shots = 2; // 游戏五图
}

message Module8 {
    bool display = 1;
    repeated string tag_list = 2; // 游戏类别标签
}

message Module9 {
    bool display = 1;
    string dev_introduction = 2; // 开发者说
}

message Module10 {
    bool display = 1;
    string latest_update = 2; // 最近更新, 包括了更新日期，更新版本，更新内容
}
message Module11 {
    bool display = 1;
    repeated int32 star_number_list = 2; // 1-5星评价人数
    string comment_str = 3; // "300人评价"
    double grade = 4; //游戏评分
}
message Comment {
    int64 game_base_id = 1;
    string user_name = 2; // 评价用户昵称
    string user_face = 3; // 评价发表用户头像
    int32 user_level = 4; // 用户等级
    string comment_no = 5; // 评论号
    int32 grade = 6; // 评分
    string content = 7; // 评论内容
    int32 up_count = 8; // 点赞数
}
message Module12 {
    bool display = 1;
    repeated Comment comment_list = 2;
    string comment_num = 3;
    bool show_all_comment = 4;
}

message Module13 {
    int64 pkg_size = 1; // 安卓游戏包大小
    string customer_service = 2; // 客服信息
    string website = 3; // 官网链接
    string authority = 4; // 权限
    string privacy = 5; // 隐私政策
    string developer_name = 6; // 发行商
    string update_time = 7; // 更新日期
    string game_version = 8; // 游戏版本
    string android_pkg_name = 9; // 游戏包名
}
message IosGamePageRes {
    string logo = 1;
    string name = 2;
    string sub_titile = 3; // 一句话介绍
    repeated string image_url = 4;
    string desc = 5; 
    AdButton game_button = 6; //按钮
    double grade = 7; // 评分
    string rank_num = 8; // "No.1"
    string rank_name = 9; // "xx榜"
}

message AndroidGamePageRes {
    Module1 module1 = 1;
    Module3 module3 = 2;
    Module4 module4 = 3;
    Module5 module5 = 4;
    Module6 module6 = 5;
    Module7 module7 = 6;
    Module8 module8 = 7;
    Module9 module9 = 8;
    Module10 module10 = 9;
    Module11 module11 = 10;
    Module12 module12 = 11;
    Module13 module13 = 12; 
    repeated int32 module_seq = 13; //模块顺序数组，可支持调整顺序
    string background_color = 14; //背景色
}

//rtb_resp.proto
message AppPackage {
    int32 size = 1;
    string display_name = 2;
    string apk_name = 3;
    string url = 4;
    string md5 = 5;
    string icon = 6;
    string bili_url = 7;
    string developer_name = 8;
    string authority_url = 9;
    string auth_name = 10;
    string apk_update_time = 11;
    string version = 12;
    string privacy_name = 13;
    string privacy_url = 14;
}

message SecondFeedbackPanel {
    // 屏蔽理由id
    int32 id = 1;
    // 理由文案
    string text = 2;
}
// From dict
message FeedbackPanelModule {
    // 模块Id
    int32 module_id = 1;
    // 跳转类型
    int32 jump_type = 2;
    // 商业类型(广告 非广告)
    int32 business_type = 3;
    // icon地址
    string icon_url = 4;
    // 跳转地址
    string jump_url = 5;
    // 文案
    string text = 6;
    // 二级文案列表
    repeated SecondFeedbackPanel secondary_panels = 7;
    // 副标题
    string sub_text = 8;
}
message BusinessMark {
    int32 business_mark_id = 1;
    // 0-无 1-单列 2-双列
    int32 column_type = 2;
    // 商业标文案
    string copywrite = 3;
    // 商业标样式 0:不展示标 1:实心+文字 2:空心框+文字 3:纯文字标 4:纯图片标
    int32 mark_type = 4;
    // 商业标样式(0-默认 1-实心 2-空心 3-空白)
    int32 mark_style = 5;
    // 背景颜色
    string bg_color = 6;
    // 框颜色
    string border_color = 7;
    // 文案颜色
    string copywrite_color = 8;
    // 商业标图片链接
    string img_url = 9;
    // 图片高度
    int32 img_height = 10;
    // 图片宽度
    int32 img_width = 11;
    // 背景颜色 夜间
    string bg_color_night = 12;
    // 框颜色 夜间
    string border_color_night = 13;
    // 文案颜色 夜间
    string copywrite_color_night = 14;
}
message FeedbackPanel{
    // 面板类型(文本) 给ios做替换标题用
    string panel_type_text = 1;
    repeated FeedbackPanelModule feedback_panel_module = 2;
    string toast = 3;
    string close_rec_tips = 4;
    string open_rec_tips = 5;
}

message AdButton {
    int32 type = 1;
    string button_title = 2;
    string button_jump_url = 3;
    repeated string button_jump_report_url = 4;
    string dl_suc_call_up = 5;
    int64 btn_delay_time = 6;
    int32 game_id = 7;
    string game_monitor_param = 8;
    int64 extreme_team_score = 9;
    int64 extreme_config_id = 10;
    bool story_arrow = 11;
    int32 show_style = 12;
    int32 show_dynamic_time = 13;
    string btn_bg_color = 14;
    bool show_game_custom_text = 15;
    int32 btn_style = 16;
    int32 game_channel_id =  17;
    string game_channel_extra = 18;
    string callup_url = 19;
}