package com.bilibili.mgk.platform.job;

import com.bilibili.mgk.platform.api.form.service.IMgkFormService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @file: RefreshFormSubmitInfoJob
 * @author: gaoming
 * @date: 2021/07/17
 * @version: 1.0
 * @description:
 **/
@Component
@Slf4j
@JobHandler("RefreshFormSubmitInfoJob")
public class RefreshFormSubmitInfoJob extends IJobHandler {

    @Autowired
    private IMgkFormService mgkFormService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            mgkFormService.refreshFormSubmitInfo();
        } catch (Exception e) {
            log.info("RefreshFormSubmitInfoJob Failed e {}", e.getMessage());
        }
        log.info("RefreshFormSubmitInfoJob finished");
        return SUCCESS;
    }
}
