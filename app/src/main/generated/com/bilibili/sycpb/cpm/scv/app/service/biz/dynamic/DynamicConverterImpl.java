package com.bilibili.sycpb.cpm.scv.app.service.biz.dynamic;

import com.bilibili.sycpb.cpm.scv.app.service.biz.dynamic.bo.DynamicBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.dynamic.bo.DynamicResponseDto;
import com.bilibili.sycpb.cpm.scv.app.service.biz.dynamic.bo.MidDynamicOwnerDto;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-03-27T17:26:10+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 11.0.18 (Oracle Corporation)"
)
public class DynamicConverterImpl implements DynamicConverter {

    @Override
    public DynamicBo fromRawDto(DynamicResponseDto dto) {
        if ( dto == null ) {
            return null;
        }

        DynamicBo.DynamicBoBuilder dynamicBo = DynamicBo.builder();

        if ( dto.getDyn_id() != null ) {
            dynamicBo.id( dto.getDyn_id() );
        }
        if ( dto.getOwner() != null ) {
            dynamicBo.owner( fromRawDto( dto.getOwner() ) );
        }
        if ( dto.getType() != null ) {
            dynamicBo.type( dto.getType() );
        }
        if ( dto.getRid() != null ) {
            dynamicBo.rid( dto.getRid() );
        }
        if ( dto.getContent() != null ) {
            dynamicBo.content( dto.getContent() );
        }

        dynamicBo.imageUrls( java.util.Optional.ofNullable(dto.getPictures()).map(x -> x.stream().map(com.bilibili.sycpb.cpm.scv.app.service.biz.dynamic.bo.MidDynamicPictureDto::getImg_src).collect(java.util.stream.Collectors.toList())).orElse(java.util.Collections.emptyList()) );
        dynamicBo.url( DynamicConverter.genDynamicLink(dto.getDyn_id()) );

        return dynamicBo.build();
    }

    @Override
    public DynamicBo.DynamicOwnerBo fromRawDto(MidDynamicOwnerDto dto) {
        if ( dto == null ) {
            return null;
        }

        DynamicBo.DynamicOwnerBo.DynamicOwnerBoBuilder dynamicOwnerBo = DynamicBo.DynamicOwnerBo.builder();

        if ( dto.getUid() != null ) {
            dynamicOwnerBo.mid( dto.getUid() );
        }
        if ( dto.getName() != null ) {
            dynamicOwnerBo.name( dto.getName() );
        }
        if ( dto.getFace() != null ) {
            dynamicOwnerBo.face( dto.getFace() );
        }

        return dynamicOwnerBo.build();
    }
}
