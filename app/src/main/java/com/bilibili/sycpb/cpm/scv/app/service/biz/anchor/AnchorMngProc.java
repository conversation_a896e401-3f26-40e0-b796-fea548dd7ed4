/*
 * Copyright (c) 2015-2024 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.anchor;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.adp.component.BusinessToolTypeEnum;
import com.bapis.ad.audit.Operator;
import com.bapis.ad.component.BizCodeEnum;
import com.bapis.ad.component.CommentComponentAuditStatus;
import com.bapis.ad.scv.anchor.AnchorAuditStatus;
import com.bapis.ad.scv.anchor.AnchorSceneType;
import com.bapis.ad.scv.anchor.AnchorType;
import com.bapis.ad.scv.anchor.QueryAnchorReq;
import com.bapis.ad.scv.component_group.ComponentType;
import com.bapis.archive.service.Arc;
import com.bapis.archive.service.ArcReply;
import com.bapis.archive.service.ArcsReply;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos.*;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.convertor.ArchiveAnchorConvertor;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.AdDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.ComponentGroupService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.bos.ComponentGroupLabelBo;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.pojos.LauArchiveAnchorPointPo;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.*;
import com.bilibili.sycpb.cpm.scv.app.service.dao.account.generated.tables.records.*;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.repo.LauArchiveAnchorPointRepo;
import com.bilibili.sycpb.cpm.scv.app.service.es.index.AnchorIndexConfig;
import com.bilibili.sycpb.cpm.scv.app.service.es.repo.AnchorEsRepo;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.ArchiveRpcService;
import com.bilibili.sycpb.cpm.scv.app.service.es.ArchiveAnchorPointRepo;
import com.bilibili.sycpb.cpm.scv.app.service.es.EsPageResult;
import com.bilibili.sycpb.cpm.scv.app.service.es.QueryParams;
import com.bilibili.sycpb.cpm.scv.app.service.es.bos.archive_anchor_point.EsArchiveAnchorPointPo;
import com.bilibili.sycpb.cpm.scv.app.service.redis.RedisStringRepository;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.Pager;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.AnchorAppImgType;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.RedisSuffix;
import com.bilibili.sycpb.cpm.scv.app.utils.enums.anchor.AuditTypeEnum;
import com.google.common.collect.Lists;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.unit.Fuzziness;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.jooq.DSLContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.Tables.LAU_ARCHIVE_ANCHOR_POINT;
import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.TLauArchiveCommentConversionComponent.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT;

/**
 * 锚点管理处理(不含审核)
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AnchorMngProc {

    @Value("${anchor.android.opt.validate.switch:1}")
    private Integer anchorAndroidOptValidateSwitch;

    private final LauArchiveAnchorPointRepo lauArchiveAnchorPointRepo;
    private final AnchorDraftProc anchorDraftProc;

    private final AnchorLogProc anchorLogProc;
    private final ArchiveRpcService archiveRpcService;
    private final AnchorEsRepo anchorEsRepo;
    @Autowired
    @Lazy
    private ComponentGroupService componentGroupService;

    private final AnchorExtraJsonService anchorExtraJsonService;

    private final ArchiveAnchorPointRepo archiveAnchorPointRepo;

    @Resource(name = AdDataSourceConfig.AD_DSL_CONTEXT)
    private DSLContext ad;

    @Autowired
    private RedisStringRepository redisStringRepository;

    public static final List<Integer> APP_ANDROID_TYPES = Arrays.asList(AnchorType.APP_VALUE, AnchorType.GAME_DOWNLOAD_VALUE);

    /**
     * 创建单个锚点
     *
     * @param addBo
     * @return
     */
    @Deprecated
    @Transactional(transactionManager = AdDataSourceConfig.AD_TX_MGR, rollbackFor = Exception.class)
    public Long createAnchor(AnchorAddBo addBo) {
        log.info("createAnchor, addBo={}", JSON.toJSONString(addBo));

        // 校验
        Assert.isTrue(NumberUtils.isNonNegative(addBo.getAid()), "avid 不能为空");
        // 校验
        var anchorPointRecord = lauArchiveAnchorPointRepo.fetchAnchorByAidIncludeDeleted(addBo.getAid());
        if (anchorPointRecord != null && Objects.equals(anchorPointRecord.getIsDeleted(), 0)) {
            throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("该稿件aid=" + addBo.getAid() + " 锚点已经存在!accountId=" + anchorPointRecord.getAccountId()));
        }
        // 写死两种了
        addBo.setScenes(Arrays.asList(AnchorSceneType.UNDER_BOX_VALUE, AnchorSceneType.STORY_VALUE));

        AnchorValidateBo anchorValidateBo = ArchiveAnchorConvertor.MAPPER.convertValidateBo2Bo(addBo);
        validate(anchorValidateBo);

        // 设置图标
        LauArchiveAnchorTypeConfigRecord typeConfigRecord = lauArchiveAnchorPointRepo.queryAnchorTypeConfigsMapByType(addBo.getType());
        addBo.setIconTitle(typeConfigRecord.getIconTitle());

        Long anchorId = 0L;
        if (anchorPointRecord == null) {
            anchorId = lauArchiveAnchorPointRepo.createAnchor(addBo);
            AnchorOtherInfoBo otherInfoBo = new AnchorOtherInfoBo();
            BeanUtils.copyProperties(addBo, otherInfoBo);

            lauArchiveAnchorPointRepo.saveAnchorOtherInfo(anchorId, addBo.getAid(), otherInfoBo, false);
        } else {
            anchorId = anchorPointRecord.getId();
            lauArchiveAnchorPointRepo.createAnchorForDeleted(anchorPointRecord, addBo);

            AnchorOtherInfoBo otherInfoBo = new AnchorOtherInfoBo();
            BeanUtils.copyProperties(addBo, otherInfoBo);
            lauArchiveAnchorPointRepo.saveAnchorOtherInfo(anchorId, addBo.getAid(), otherInfoBo, false);
            anchorId = anchorPointRecord.getId();
        }
        // 操作日志(在三连落的)
        return anchorId;
    }

    /**
     * 创建多个锚点
     * 一个失败，则全部回滚
     *
     * @param anchorAddBo
     * @return
     */
    @Transactional(transactionManager = AdDataSourceConfig.AD_TX_MGR, rollbackFor = Exception.class)
    public List<AnchorAddResultBo> createAnchors(AnchorsAddBo anchorAddBo) {
        log.info("createAnchors, addBo={}", JSON.toJSONString(anchorAddBo));

        anchorAddBo.setScenes(anchorAddBo.getScenes());
        String scenes = anchorAddBo.getScenes().stream().map(t -> String.valueOf(t)).collect(Collectors.joining(","));
        String appLabels = anchorAddBo.getAppLabels().stream().collect(Collectors.joining(","));
        Timestamp nowTimestamp = new Timestamp(System.currentTimeMillis());

        // 基础校验
        Assert.isTrue(!CollectionUtils.isEmpty(anchorAddBo.getAvidList()), "稿件列表不能为空");
        for (AnchorAvidInfoBo anchorAvidInfoBo : anchorAddBo.getAvidList()) {
            Assert.isTrue(NumberUtils.isNonNegative(anchorAvidInfoBo.getAid()), "avid 不能为空");
            Assert.isTrue(NumberUtils.isNonNegative(anchorAvidInfoBo.getMid()), "mid 不能为空");
        }
        AnchorValidateBo anchorValidateBo = ArchiveAnchorConvertor.MAPPER.convertValidateBo2Bo(anchorAddBo);
        validate(anchorValidateBo);

        List<Long> avids = anchorAddBo.getAvidList().stream().map(t -> t.getAid()).distinct().collect(Collectors.toList());

        ArcsReply arcsReply = archiveRpcService.arcsReply(avids);
        Map<Long, Arc> arcsMap = arcsReply.getArcsMap();

        List<LauArchiveAnchorPointRecord> anchorPointRecords = lauArchiveAnchorPointRepo.queryListByAidsIncludeDeleted(avids);
        Map<Long, LauArchiveAnchorPointRecord> anchorPointRecordMap = anchorPointRecords.stream().collect(Collectors.toMap(t -> t.getAid(), t -> t, (t1, t2) -> t2));

        // 锚点是否已经存在
        for (AnchorAvidInfoBo anchorAvidInfoBo : anchorAddBo.getAvidList()) {
            LauArchiveAnchorPointRecord anchorPointRecord = anchorPointRecordMap.get(anchorAvidInfoBo.getAid());
            if (anchorPointRecord != null && Objects.equals(anchorPointRecord.getIsDeleted(), 0)) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("该稿件aid=" + anchorAvidInfoBo.getAid() + " 锚点已经存在!accountId=" + anchorPointRecord.getAccountId()));
            }
        }

        LauArchiveAnchorTypeConfigRecord typeConfigRecord = lauArchiveAnchorPointRepo.queryAnchorTypeConfig(anchorAddBo.getType(), anchorAddBo.getClueType(), anchorAddBo.getBizCode());

        List<LauArchiveAnchorPointRecord> resultList = new ArrayList<>();

        Integer index = 1;
        for (AnchorAvidInfoBo anchorAvidInfoBo : anchorAddBo.getAvidList()) {
            LauArchiveAnchorPointRecord oldAnchorPointRecord = anchorPointRecordMap.get(anchorAvidInfoBo.getAid());
            if (typeConfigRecord != null) {
                anchorAddBo.setIconTitle(typeConfigRecord.getIconTitle());
            }
            String anchorName = anchorAddBo.getName();
            if (index > 1) {
                anchorName = anchorName + "_" + index;
            }
            index++;

            String androidUrl = anchorAddBo.getAndroidUrl();
            String iosUrl = anchorAddBo.getIosUrl();
            if (Objects.equals(BizCodeEnum.BUSINESS_TOOL_VALUE, anchorAddBo.getBizCode())&&Objects.equals(anchorAddBo.getBusinessToolType(), BusinessToolTypeEnum.MESSAGE_VALUE)) {
                if (!androidUrl.contains("&source_bvid=")) {
                    androidUrl = androidUrl + "&source_bvid=" + BVIDUtils.avToBv(anchorAvidInfoBo.getAid());
                }
                if (!iosUrl.contains("&source_bvid=")) {
                    iosUrl = iosUrl + "&source_bvid=" + BVIDUtils.avToBv(anchorAvidInfoBo.getAid());
                }
                if (!androidUrl.contains("&source_up=")) {
                    androidUrl = androidUrl + "&source_up=" + anchorAvidInfoBo.getMid();
                }
                if (!iosUrl.contains("&source_up=")) {
                    iosUrl = iosUrl + "&source_up=" + anchorAvidInfoBo.getMid();
                }
            }

            String title = "";
            Arc arc = arcsMap.get(anchorAvidInfoBo.getAid());
            if (Objects.nonNull(arc)) {
                title = arc.getTitle();
            }
            // add target
//            AnchorExtraJsonBo extraJsonBo = anchorExtraJsonService.generateCommentComponentExtraJsonBo(anchorAddBo.getType(), anchorAddBo.getBiliMiniGameMid());
            AnchorExtraJsonBo extraJsonBo = anchorExtraJsonService.generateCommentComponentExtraJsonBo(anchorAddBo);

            // 是否已经存在
            if (oldAnchorPointRecord != null) {
                oldAnchorPointRecord
                        .setScenes(scenes)
                        // 默认待审核
                        .setAuditStatus(CommentComponentAuditStatus.AUDITING.getNumber())
                        .setStatus(CommentComponentAuditStatus.AUDITING.getNumber())
                        // 清空数据
                        .setEnterAuditTime(nowTimestamp)
                        .setReason("")
                        .setAuditPerson("")
                        .setIsDeleted(0)
                        .setCtime(nowTimestamp)
                        .setType(anchorAddBo.getType())
                        .setMid(anchorAvidInfoBo.getMid())
                        .setAccountId(anchorAddBo.getAccountId())
                        .setCustomerId(anchorAddBo.getCustomerId())
                        .setAgentId(anchorAddBo.getAgentId())
                        .setCampaignId(anchorAddBo.getCampaignId())
                        .setUnitId(anchorAddBo.getUnitId())
                        .setCreativeId(anchorAvidInfoBo.getCreativeId())
                        .setClueType(anchorAddBo.getClueType())
                        .setMainTitle(anchorAddBo.getMainTitle())
                        .setSubTitle(anchorAddBo.getSubTitle())
                        .setButtonText(anchorAddBo.getButtonText())
                        .setIconTitle(anchorAddBo.getIconTitle())
                        .setConversionUrlType(anchorAddBo.getConversionUrlType())
                        .setConversionUrlPageId(anchorAddBo.getConversionUrlPageId())
                        .setConversionUrl(anchorAddBo.getConversionUrl())
                        .setIosUrlType(anchorAddBo.getIosUrlType())
                        .setIosUrlPageId(anchorAddBo.getIosUrlPageId())
                        .setIosUrl(iosUrl)
                        .setAndroidUrlType(anchorAddBo.getAndroidUrlType())
                        .setAndroidUrlPageId(anchorAddBo.getAndroidUrlPageId())
                        .setAndroidUrl(androidUrl)
                        .setIosSchemaUrl(anchorAddBo.getIosSchemaUrl())
                        .setAndroidSchemaUrl(anchorAddBo.getAndroidSchemaUrl())
                        .setIosButtonSchemaUrl(anchorAddBo.getIosButtonSchemaUrl())
                        .setAndroidButtonSchemaUrl(anchorAddBo.getAndroidButtonSchemaUrl())
                        .setIsOpenMiniGame(anchorAddBo.getIsOpenMiniGame())
                        .setMiniGameId(anchorAddBo.getMiniGameId())
                        .setGameBaseId(anchorAddBo.getGameBaseId())
                        .setGamePlatformType(anchorAddBo.getGamePlatformType())
                        .setIosAppPackageId(anchorAddBo.getIosAppPackageId())
                        .setAndroidAppPackageId(anchorAddBo.getAndroidAppPackageId())
                        .setSubPkg(anchorAddBo.getSubPkg())
                        .setClueData(anchorAddBo.getClueData())
                        .setCustomizedImpUrl(anchorAddBo.getCustomizedImpUrl())
                        .setCustomizedClickUrl(anchorAddBo.getCustomizedClickUrl())
                        .setName(anchorName)
                        .setUpNickname(anchorAvidInfoBo.getUpNickName())
                        .setGuideText(anchorAddBo.getGuideText())
                        .setAppDetailText(anchorAddBo.getAppDetailText())
                        .setAppDetailType(anchorAddBo.getAppDetailType())
                        .setAppLabels(appLabels)
                        .setIsAndroidAppDirect(anchorAddBo.getIsAndroidAppDirect())
                        .setBizCode(anchorAddBo.getBizCode())
                        .setQualificationIds(anchorAddBo.getQualificationIds().stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .setTitle(title)
                        .setGroupId(anchorAddBo.getGroupId())
                        .setBusinessToolType(anchorAddBo.getBusinessToolType())
                        .setAuditType(AuditTypeEnum.INIT.getCode())
                        .setExtra(Objects.nonNull(extraJsonBo) ? JSON.toJSONString(extraJsonBo) : "");

                resultList.add(oldAnchorPointRecord);
            } else {
                LauArchiveAnchorPointRecord anchorPointRecord = ad.newRecord(LAU_ARCHIVE_ANCHOR_POINT)
                        .setScenes(scenes)
                        // 默认待审核
                        .setAuditStatus(CommentComponentAuditStatus.AUDITING.getNumber())
                        .setStatus(CommentComponentAuditStatus.AUDITING.getNumber())
                        .setEnterAuditTime(new Timestamp(System.currentTimeMillis()))
                        .setAid(anchorAvidInfoBo.getAid())
                        .setType(anchorAddBo.getType())
                        .setMid(anchorAvidInfoBo.getMid())
                        .setAccountId(anchorAddBo.getAccountId())
                        .setCustomerId(anchorAddBo.getCustomerId())
                        .setAgentId(anchorAddBo.getAgentId())
                        .setCampaignId(anchorAddBo.getCampaignId())
                        .setUnitId(anchorAddBo.getUnitId())
                        .setCreativeId(anchorAvidInfoBo.getCreativeId())
                        .setClueType(anchorAddBo.getClueType())
                        .setMainTitle(anchorAddBo.getMainTitle())
                        .setSubTitle(anchorAddBo.getSubTitle())
                        .setButtonText(anchorAddBo.getButtonText())
                        .setIconTitle(anchorAddBo.getIconTitle())
                        .setConversionUrlType(anchorAddBo.getConversionUrlType())
                        .setConversionUrlPageId(anchorAddBo.getConversionUrlPageId())
                        .setConversionUrl(anchorAddBo.getConversionUrl())
                        .setIosUrlType(anchorAddBo.getIosUrlType())
                        .setIosUrlPageId(anchorAddBo.getIosUrlPageId())
                        .setIosUrl(iosUrl)
                        .setAndroidUrlType(anchorAddBo.getAndroidUrlType())
                        .setAndroidUrlPageId(anchorAddBo.getAndroidUrlPageId())
                        .setAndroidUrl(androidUrl)
                        .setIosSchemaUrl(anchorAddBo.getIosSchemaUrl())
                        .setAndroidSchemaUrl(anchorAddBo.getAndroidSchemaUrl())
                        .setIosButtonSchemaUrl(anchorAddBo.getIosButtonSchemaUrl())
                        .setAndroidButtonSchemaUrl(anchorAddBo.getAndroidButtonSchemaUrl())
                        .setIsOpenMiniGame(anchorAddBo.getIsOpenMiniGame())
                        .setMiniGameId(anchorAddBo.getMiniGameId())
                        .setGameBaseId(anchorAddBo.getGameBaseId())
                        .setGamePlatformType(anchorAddBo.getGamePlatformType())
                        .setIosAppPackageId(anchorAddBo.getIosAppPackageId())
                        .setAndroidAppPackageId(anchorAddBo.getAndroidAppPackageId())
                        .setSubPkg(anchorAddBo.getSubPkg())
                        .setClueData(anchorAddBo.getClueData())
                        .setCustomizedImpUrl(anchorAddBo.getCustomizedImpUrl())
                        .setCustomizedClickUrl(anchorAddBo.getCustomizedClickUrl())
                        .setName(anchorName)
                        .setGuideText(anchorAddBo.getGuideText())
                        .setAppDetailText(anchorAddBo.getAppDetailText())
                        .setAppDetailType(anchorAddBo.getAppDetailType())
                        .setAppLabels(appLabels)
                        .setIsAndroidAppDirect(anchorAddBo.getIsAndroidAppDirect())
                        .setUpNickname(anchorAvidInfoBo.getUpNickName())
                        .setBizCode(anchorAddBo.getBizCode())
                        .setQualificationIds(anchorAddBo.getQualificationIds().stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .setTitle(title)
                        .setGroupId(anchorAddBo.getGroupId())
                        .setBusinessToolType(anchorAddBo.getBusinessToolType())
                        .setAuditType(AuditTypeEnum.INIT.getCode())
                        .setExtra(Objects.nonNull(extraJsonBo) ? JSON.toJSONString(extraJsonBo) : "");
                resultList.add(anchorPointRecord);
            }
        }
        //
        statusBuild(resultList);
        ad.batchStore(resultList).execute();

        // 再查一次获取avid的 id
        List<LauArchiveAnchorPointRecord> savedAnchorPointRecords = lauArchiveAnchorPointRepo.queryListByAidsExcludeDeleted(avids);
        List<AnchorAddResultBo> resultBoList = savedAnchorPointRecords.stream().map(this::convertAnchorAddResultBo).collect(Collectors.toList());

        List<Long> ids = savedAnchorPointRecords.stream().map(t -> t.getId()).collect(Collectors.toList());
        Map<Long, LauArchiveAnchorPointRecord> savedAnchorPointRecordMap = savedAnchorPointRecords.stream().collect(Collectors.toMap(t -> t.getAid(), t -> t, (t1, t2) -> t2));

        // 准备锚点的图标
        List<AnchorOtherInfoBo> anchorOtherInfoBos = new ArrayList<>();
        for (AnchorAvidInfoBo avidInfoBo : anchorAddBo.getAvidList()) {
            LauArchiveAnchorPointRecord anchorPointRecord = savedAnchorPointRecordMap.get(avidInfoBo.getAid());
            AnchorOtherInfoBo otherInfoBo = new AnchorOtherInfoBo();
            otherInfoBo.setAid(avidInfoBo.getAid());
            otherInfoBo.setAnchorId(anchorPointRecord != null ? anchorPointRecord.getId() : 0L);
            otherInfoBo.setTopImgUrls(anchorAddBo.getTopImgUrls());
            otherInfoBo.setAppImgUrls(anchorAddBo.getAppImgUrls());
            anchorOtherInfoBos.add(otherInfoBo);
        }

        // 批量保存稿件锚点图标
        lauArchiveAnchorPointRepo.saveAnchorsOtherInfo(anchorOtherInfoBos);

        if (anchorAddBo.getGroupId() != null && anchorAddBo.getGroupId() > 0) {
            updateGroupCount(anchorAddBo.getGroupId());
        }
        // 操作日志(在三连落的)
        return resultBoList;
    }

    private void statusBuild(List<LauArchiveAnchorPointRecord> resultList) {
        for (LauArchiveAnchorPointRecord record : resultList) {
            //花火来源的  直接审核通过
            if (record.getBizCode().equals(BizCodeEnum.COMMERCIAL_ANCHOR.getNumber())) {
                record.setStatus(CommentComponentAuditStatus.AUDIT_PASSED.getNumber());
                record.setAuditStatus(CommentComponentAuditStatus.AUDIT_PASSED_VALUE);
            }
        }
    }

    private void validate(AnchorValidateBo addBo) {

        if (!NumberUtils.isPositive(anchorAndroidOptValidateSwitch)) {
            return;
        }

        if (APP_ANDROID_TYPES.contains(addBo.getType())) {
            if (CollectionUtils.isEmpty(addBo.getAppLabels())) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("请填写app标签"));
            } else {
                if (addBo.getAppLabels().size() > 3) {
                    throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("标签最多3个"));
                }
                for (String appLabel : addBo.getAppLabels()) {
                    if (!StringUtils.isEmpty(appLabel) && appLabel.length() > 4) {
                        throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("单个标签最多4个字符"));
                    }
                }
            }

            if (CollectionUtils.isEmpty(addBo.getTopImgUrls())) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("请选择顶图"));
            } else {
                if (addBo.getTopImgUrls().size() != 1) {
                    throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("顶图最多一个"));
                }
            }
            if (CollectionUtils.isEmpty(addBo.getAppImgUrls())) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("请选择app图片"));
            } else {
                if (addBo.getAppImgUrls().size() < 3 || addBo.getAppImgUrls().size() > 8) {
                    throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("app图片最少3张，最多8张"));
                }
            }
            if (StringUtils.isEmpty(addBo.getGuideText())) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("请填写引导文案"));
            } else {
                if (addBo.getGuideText().length() > 15) {
                    throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("引导文案最多15个字符"));
                }
            }
            if (StringUtils.isEmpty(addBo.getAppDetailText())) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("请填写app详情"));
            } else {
                if (addBo.getAppDetailText().length() > 200) {
                    throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("app详情最多200个字符"));
                }
            }
        }
    }

    @Transactional(transactionManager = AdDataSourceConfig.AD_TX_MGR, rollbackFor = Exception.class)
    public AnchorUpdateResultBo updateAnchor(AnchorUpdateBo updateBo, Operator operator) {
        log.info("updateAnchor, updateBo{}", JSON.toJSONString(updateBo));

        AnchorValidateBo anchorValidateBo = ArchiveAnchorConvertor.MAPPER.convertValidateBo2Bo(updateBo);
        validate(anchorValidateBo);
        // 校验
        var anchorPointRecord = lauArchiveAnchorPointRepo.fetchAnchorById(updateBo.getId());
        if (anchorPointRecord == null) {
            throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription(MessageFormat.format("id={0,number,#}数据不存在", updateBo.getId())));
        }
        AnchorUpdateBo oldAnchorBo = ArchiveAnchorConvertor.MAPPER.convertAnchorPointPo2UpdateBo(anchorPointRecord);
        List<LauArchiveAnchorPointOtherInfoRecord> oldOtherInfoRecords = lauArchiveAnchorPointRepo.queryAnchorOtherInfos(updateBo.getAid());
        List<String> topImgUrls = oldOtherInfoRecords.stream().filter(t -> Objects.equals(t.getType(), AnchorAppImgType.TOP_IMAGE)).map(t -> t.getContent()).collect(Collectors.toList());
        List<String> appImgUrls = oldOtherInfoRecords.stream().filter(t -> Objects.equals(t.getType(), AnchorAppImgType.APP_IMAGE)).map(t -> t.getContent()).collect(Collectors.toList());
        oldAnchorBo.setTopImgUrls(topImgUrls);
        oldAnchorBo.setAppImgUrls(appImgUrls);

        // 稿件不能换，账户不能换
        Assert.isTrue(anchorPointRecord.getAccountId().equals(updateBo.getAccountId()), "不能操作其他账户数据");
        Assert.isTrue(anchorPointRecord.getAid().equals(updateBo.getAid()), "锚点aid参数错误");

        ArcReply arcReply = archiveRpcService.arcReply(updateBo.getAid());
        Arc arc = arcReply.getArc();
        anchorPointRecord.setTitle(Optional.of(arc.getTitle()).orElse(""));
        anchorPointRecord.setGroupId(updateBo.getGroupId());

        // 设置图标
        LauArchiveAnchorTypeConfigRecord typeConfigRecord = lauArchiveAnchorPointRepo.queryAnchorTypeConfigsMapByType(updateBo.getType());
        updateBo.setIconTitle(typeConfigRecord.getIconTitle());
//        updateBo.setScenes(Arrays.asList(AnchorSceneType.UNDER_BOX_VALUE, AnchorSceneType.STORY_VALUE));

        // 判断推审情况
        AnchorAuditStatusBo anchorAuditStatusBo = anchorDraftProc.judgeAuditStatus(updateBo, oldAnchorBo);
        log.info("updateAnchor, judgeAuditStatus, anchorAuditStatusBo{}", JSON.toJSONString(anchorAuditStatusBo));

        int count = 0;
        LauArchiveAnchorPointDraftRecord anchorPointDraftRecord = lauArchiveAnchorPointRepo.fetchAnchorDraft(updateBo.getId());

        // 需要推审
        if (anchorAuditStatusBo.isNeedPushAudit()) {
            anchorPointRecord.setAuditStatus(anchorAuditStatusBo.getToAuditStatus());
            anchorPointRecord.setStatus(anchorAuditStatusBo.getToStatus());
            anchorPointRecord.setEnterAuditTime(new Timestamp(System.currentTimeMillis()));
            anchorPointRecord.setAuditPerson("");
            anchorPointRecord.setReason("");
            anchorPointRecord.setAuditType(AuditTypeEnum.INIT.getCode());
            anchorPointRecord.setGroupId(updateBo.getGroupId());
            lauArchiveAnchorPointRepo.updateAnchor(anchorPointRecord);

            AnchorDraftBo anchorDraftBo = ArchiveAnchorConvertor.MAPPER.convertAnchorInfoBo2Draft(updateBo);

            if (anchorPointDraftRecord != null) {
                lauArchiveAnchorPointRepo.updateAnchorDraft(anchorPointDraftRecord, anchorDraftBo);
            } else {
                lauArchiveAnchorPointRepo.createAnchorDraft(anchorDraftBo, updateBo.getAid());
            }
        }
        // 不需要推审，直接修改锚点，删除草稿
        else {
            count = lauArchiveAnchorPointRepo.updateAnchor(anchorPointRecord, updateBo, anchorAuditStatusBo);
            AnchorOtherInfoBo otherInfoBo = new AnchorOtherInfoBo();
            BeanUtils.copyProperties(updateBo, otherInfoBo);
            lauArchiveAnchorPointRepo.saveAnchorOtherInfo(updateBo.getId(), updateBo.getAid(), otherInfoBo, true);
            if (anchorPointDraftRecord != null) {
                lauArchiveAnchorPointRepo.deleteAnchorDraft(updateBo.getId());
            }
        }

        if (updateBo.getGroupId() != null && updateBo.getGroupId() > 0 && !updateBo.getGroupId().equals(oldAnchorBo.getGroupId())) {
            updateGroupCount(updateBo.getGroupId());
            updateGroupCount(oldAnchorBo.getGroupId());
        }

        // 操作日志
//        anchorLogProc.addAnchorUpdateLog(oldAnchorBo, updateBo, operator);
        return AnchorUpdateResultBo.builder().isNeedSaveLog(anchorAuditStatusBo.isNeedPushAudit()).count(count).build();
    }


    @Transactional(transactionManager = AdDataSourceConfig.AD_TX_MGR, rollbackFor = Exception.class)
    public int delete(Long id) {
        log.info("delete, id={}", id);
        var anchorPointRecord = lauArchiveAnchorPointRepo.fetchAnchorById(id);
        if (anchorPointRecord == null) {
            throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription(MessageFormat.format("id={0,number,#}数据不存在", id)));
        }

        int count = lauArchiveAnchorPointRepo.deleteAnchor(id);
        lauArchiveAnchorPointRepo.deleteAnchorDraftIfExist(id);
        lauArchiveAnchorPointRepo.deleteAnchorOtherInfo(id);

        if (anchorPointRecord.getGroupId() != null && anchorPointRecord.getGroupId() > 0) {
            updateGroupCount(anchorPointRecord.getGroupId());
        }

        // 操作日志(在三连落的)
        return count;
    }


    public AnchorInfoBo fetch(Long id) {
        var anchorPointRecord = lauArchiveAnchorPointRepo.fetchAnchorById(id);
        if (anchorPointRecord == null) {
            throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription(MessageFormat.format("id={0,number,#}数据不存在", id)));
        }

        AnchorInfoBo anchorInfoBo = ArchiveAnchorConvertor.MAPPER.convertAnchorPointPo2Bo(anchorPointRecord);
        //extra build
        buildExtra(anchorPointRecord, anchorInfoBo);

        LauComponentGroupLabelRecord lauComponentGroupLabelRecord = componentGroupService.queryById(anchorInfoBo.getGroupId(), anchorInfoBo.getAccountId());
        if (lauComponentGroupLabelRecord != null) {
            anchorInfoBo.setGroupName(lauComponentGroupLabelRecord.getGroupName());
        }

        LauArchiveAnchorPointDraftRecord draftRecord = lauArchiveAnchorPointRepo.fetchAnchorDraft(id);

        // 其他信息
        List<LauArchiveAnchorPointOtherInfoRecord> otherInfoRecords = lauArchiveAnchorPointRepo.queryAnchorOtherInfos(id);
        if (!CollectionUtils.isEmpty(otherInfoRecords)) {
            List<String> topImgUrls = otherInfoRecords.stream().filter(t -> Objects.equals(t.getType(), AnchorAppImgType.TOP_IMAGE)).map(t -> t.getContent()).collect(Collectors.toList());
            List<String> appImgUrls = otherInfoRecords.stream().filter(t -> Objects.equals(t.getType(), AnchorAppImgType.APP_IMAGE)).map(t -> t.getContent()).collect(Collectors.toList());
            anchorInfoBo.setTopImgUrls(topImgUrls);
            anchorInfoBo.setAppImgUrls(appImgUrls);
        }

        // 草稿内容替换
        anchorDraftProc.replacePartFields(draftRecord, anchorInfoBo);
        return anchorInfoBo;
    }

    public static void buildExtra(LauArchiveAnchorPointRecord anchorPointRecord, AnchorInfoBo anchorInfoBo) {
        if (StringUtils.isNotBlank(anchorPointRecord.getExtra())) {
            try {
                AnchorExtraJsonBo extraJsonBo = JSON.parseObject(anchorPointRecord.getExtra(), AnchorExtraJsonBo.class);
                if (extraJsonBo != null) {
                    anchorInfoBo.setBiliAppletUrl(extraJsonBo.getBiliAppletUrl());
                    anchorInfoBo.setNotShowInNatureFlow(extraJsonBo.getNotShowInNatureFlow());
                }
            } catch (Exception e) {
                log.error("buildExtra, extraJsonBo parse error, extra={}", anchorPointRecord.getExtra(), e);
            }
        }
    }

    public Pager<AnchorInfoBo> queryAnchorPageList(AnchorPageQueryBo queryBo) {
        log.info("queryAnchorPageList, query={}", queryBo.toString());
        if (StringUtils.isNotEmpty(queryBo.getArchiveTitle())) {
            Assert.isTrue(!CollectionUtils.isEmpty(queryBo.getAccountIds()), "根据稿件名称查询时，必须传递账户id");
            List<LauArchiveAnchorPointPo> query = anchorEsRepo.query(queryBo, LauArchiveAnchorPointPo.class);
            if (!CollectionUtils.isEmpty(query)) {
                List<Long> aids = query.stream().map(LauArchiveAnchorPointPo::getAid).collect(Collectors.toList());
                log.info("queryAnchorPageList, 模糊查询 aids={}", aids);
                queryBo.setAids(aids);
            } else {
                return new Pager<>(1, 0, Collections.emptyList());
            }
        }
        Pager<AnchorInfoBo> anchorInfoBoPager = lauArchiveAnchorPointRepo.queryAnchorPageList(queryBo);

        // 查询草稿
        List<AnchorInfoBo> anchorInfoBos = anchorInfoBoPager.getData();
        // 用草稿更新
        anchorDraftProc.replacePartFieldsFromDraft(anchorInfoBos);
        setGroupName(anchorInfoBos);
        return anchorInfoBoPager;
    }

    public Pager<AnchorInfoBo> queryAnchorPageListByEs(AnchorPageQueryBo queryBo) {
        log.info("queryAnchorPageList, query={}", queryBo.toString());

        QueryParams queryParams = QueryParams.newInstance().addTerm("accountId", queryBo.getAccountIds()).addTerm("customerId", queryBo.getCustomerIds())
                .addTerm("agentId", queryBo.getAgentIds()).addRangeNullable("ctime", queryBo.getFromTime(), queryBo.getToTime())
                .addRangeNullable("enterAuditTime", queryBo.getEnterAuditTimeStart(), queryBo.getEnterAuditTimeEnd())
                .addRangeNullable("auditTime", queryBo.getAuditTimeStart(), queryBo.getAuditTimeEnd()).addTerm("status", queryBo.getStatusList())
                .addTerm("auditStatus", queryBo.getAuditStatusList()).addTerm("isDeleted", queryBo.getIsDeleted()).addTerm("id", queryBo.getIds())
                .addTerm("aid", queryBo.getAids()).addTerm("mid", queryBo.getMids()).addMatch("name", queryBo.getAnchorName())
                .addTerm("auditPerson", queryBo.getAuditPersons()).addTerm("bizCode", queryBo.getBizCodeList()).addTerm("auditType", queryBo.getAuditTypeList())
                .addMatch("mainTitle", queryBo.getMainTitle()).addMatch("subTitle", queryBo.getSubTitle()).addMatch("buttonText", queryBo.getButtonText());

        boolean isQueryToAudit = false;
        if (!CollectionUtils.isEmpty(queryBo.getStatusList()) && queryBo.getStatusList().size() == 1 && queryBo.getStatusList().contains(AnchorAuditStatus.AUDITING_VALUE)) {
            isQueryToAudit = true;
        }

        final var searchSourceBuilder = new SearchSourceBuilder().query(buildSearchQuery(queryParams));

        searchSourceBuilder.from((queryBo.getPage()-1) * queryBo.getSize());
        searchSourceBuilder.size(queryBo.getSize());
        if(isQueryToAudit){
            searchSourceBuilder.sort("enterAuditTime", SortOrder.ASC);
        } else {
            searchSourceBuilder.sort("mtime", SortOrder.DESC);
        }

        EsPageResult<EsArchiveAnchorPointPo> anchorInfoBoPager = archiveAnchorPointRepo.search(searchSourceBuilder);

        Map<Integer, LauArchiveAnchorTypeConfigRecord> typeConfigsMap = lauArchiveAnchorPointRepo.queryAnchorTypeConfigsMap();

        if(null != anchorInfoBoPager && !CollectionUtils.isEmpty(anchorInfoBoPager.getList())){
            List<AnchorInfoBo> boList = ArchiveAnchorConvertor.MAPPER.convertEsAnchorPointPos2Bos(anchorInfoBoPager.getList());
            boList = filterAnchorInfoBo(boList);
            boList.forEach(bo -> {
                LauArchiveAnchorTypeConfigRecord typeConfigRecord = typeConfigsMap.get(bo.getType());
                if (typeConfigRecord != null) {
                    bo.setUnderBoxDayIcon(typeConfigRecord.getUnderBoxDayIcon());
                    bo.setStoryDayIcon(typeConfigRecord.getStoryDayIcon());
                }
            });

            return Pager.<AnchorInfoBo>builder().data(boList).page(queryBo.getPage()).total((int) anchorInfoBoPager.getTotal()).build();
        }

        return Pager.<AnchorInfoBo>builder().page(queryBo.getPage()).total((int) anchorInfoBoPager.getTotal()).data(Lists.newArrayList()).build();
    }

    private List<AnchorInfoBo> filterAnchorInfoBo(List<AnchorInfoBo> anchorInfoBos) {
        if (CollectionUtils.isEmpty(anchorInfoBos) || anchorInfoBos.size() > 200) {
            return anchorInfoBos;
        }

        List<String> ids = anchorInfoBos.stream().map(e -> RedisSuffix.ANCHOR_STATUS_KEY + e.getId()).collect(Collectors.toList());
        Map<String, String> idStatusMap = redisStringRepository.mGet(ids);
        return anchorInfoBos.stream()
                .filter(e -> !idStatusMap.containsKey(RedisSuffix.ANCHOR_STATUS_KEY + e.getId()) ||
                        Objects.equals(null == e.getStatus() ? null : e.getStatus().toString()
                        , idStatusMap.get(RedisSuffix.ANCHOR_STATUS_KEY + e.getId())))
                .collect(Collectors.toList());
    }

    public Pager<AnchorInfoBo> queryAnchorPageListForAutoAudit(AutoAuditAnchorQueryBo queryBo){
        QueryParams queryParams = QueryParams.newInstance().addTerm("status", queryBo.getStatusList())
                .addTerm("auditStatus", queryBo.getAuditStatusList()).addTerm("isDeleted", queryBo.getIsDeleted())
                .addTerm("auditType", queryBo.getAuditTypeList()).addTerm("mainTitle.keyword", queryBo.getMainTitle())
                .addTerm("subTitle.keyword", queryBo.getSubTitle()).addTerm("buttonText.keyword", queryBo.getButtonText()).addTerm("iosAppPackageId", queryBo.getIosAppPackageId())
                .addTerm("androidAppPackageId", queryBo.getAndroidAppPackageId()).addTerm("androidUrlPageId", queryBo.getAndroidUrlPageId())
                .addTerm("iosUrlPageId", queryBo.getIosUrlPageId()).addTerm("iosUrlPrefix", queryBo.getIosUrl()).addTerm("androidUrlPrefix", queryBo.getAndroidUrl())
                .addTerm("conversionUrlPrefix", queryBo.getConversionUrl()).addTerm("type", queryBo.getType())
                .addTerm("gameBaseId", queryBo.getGameBaseId()).addTerm("miniGameId", queryBo.getMiniGameId())
                .addMustNot("id", queryBo.getIds());

        final var searchSourceBuilder = new SearchSourceBuilder().query(buildSearchQuery(queryParams));

        searchSourceBuilder.size(1);
        searchSourceBuilder.sort("mtime", SortOrder.DESC);
        log.info("queryAnchorPageListForAutoAudit, searchSourceBuilder={}", searchSourceBuilder);
        EsPageResult<EsArchiveAnchorPointPo> anchorInfoBoPager = archiveAnchorPointRepo.search(searchSourceBuilder);

        if(null != anchorInfoBoPager && !CollectionUtils.isEmpty(anchorInfoBoPager.getList())){
            List<AnchorInfoBo> boList = ArchiveAnchorConvertor.MAPPER.convertEsAnchorPointPos2Bos(anchorInfoBoPager.getList());
            return Pager.<AnchorInfoBo>builder().data(boList).total((int) anchorInfoBoPager.getTotal()).build();
        }

        return Pager.<AnchorInfoBo>builder().total((int) anchorInfoBoPager.getTotal()).data(Lists.newArrayList()).build();
    }

    private QueryBuilder buildSearchQuery(QueryParams queryParams) {
        if (queryParams.isMatchAll()) return QueryBuilders.matchAllQuery();

        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        queryParams.getRanges().forEach((k, v) -> {
            final RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(k);
            if(null != v._1){
                rangeQueryBuilder.from(v._1, true);
            }
            if(null != v._2){
                rangeQueryBuilder.to(v._2, false);
            }
            boolQueryBuilder.filter(rangeQueryBuilder);
        });
        queryParams.getTerms().forEach((k, v) -> {
            if(v instanceof Collection){
                boolQueryBuilder.filter(QueryBuilders.termsQuery(k, (Collection)v));
            } else {
                boolQueryBuilder.filter(QueryBuilders.termQuery(k, v));
            }
        });
        queryParams.getMustNot().forEach((k, v) -> {
            if(v instanceof Collection){
                boolQueryBuilder.mustNot(QueryBuilders.termsQuery(k, (Collection)v));
            } else {
                boolQueryBuilder.mustNot(QueryBuilders.termQuery(k, v));
            }
        });
        queryParams.getMatches().forEach((k, v) -> {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(k, v));
        });
        return boolQueryBuilder;
    }
    public void setGroupName(List<AnchorInfoBo> anchorInfoBos) {
        if (CollectionUtils.isEmpty(anchorInfoBos)) {
            return;
        }
        List<Integer> groupIds = anchorInfoBos.stream().map(AnchorInfoBo::getGroupId).collect(Collectors.toList());
        Map<Integer, LauComponentGroupLabelRecord> map = componentGroupService.getMap(groupIds, anchorInfoBos.get(0).getAccountId());
        anchorInfoBos.forEach(x -> {
            LauComponentGroupLabelRecord record = map.get(x.getGroupId());
            if (record != null) {
                x.setGroupName(record.getGroupName());
            }
        });
    }

    public List<AnchorInfoBo> queryAnchorList(QueryAnchorReq request) {
        List<AnchorInfoBo> anchorInfoBos = lauArchiveAnchorPointRepo.queryAnchorList(request);

        anchorDraftProc.replacePartFieldsFromDraft(anchorInfoBos);
        setGroupName(anchorInfoBos);
        return anchorInfoBos;
    }

    public List<AnchorDraftBo> queryAnchorDraftList(List<Long> anchorIdList) {

        List<LauArchiveAnchorPointDraftRecord> draftRecords = lauArchiveAnchorPointRepo.queryAnchorDraftList(anchorIdList);

        List<AnchorDraftBo> anchorDraftBos = new ArrayList<>();
        for (LauArchiveAnchorPointDraftRecord draftRecord : draftRecords) {
            AnchorDraftBo anchorDraftBo = new AnchorDraftBo();
            anchorDraftBo.setAnchorId(draftRecord.getAnchorId());

            if (!StringUtils.isEmpty(draftRecord.getContent())) {
                anchorDraftBo = JSON.parseObject(draftRecord.getContent(), AnchorDraftBo.class);
            }

            anchorDraftBos.add(anchorDraftBo);
        }
        return anchorDraftBos;
    }

    public Integer updateAvidByAnchorId(Long anchorId, Long avid) {
        log.info("updateAvidByAnchorId, anchorId={}, avid={}", anchorId, avid);
        return lauArchiveAnchorPointRepo.updateAvidByAnchorId(anchorId, avid);
    }

    private AnchorAddResultBo convertAnchorAddResultBo(LauArchiveAnchorPointRecord anchorPointRecord) {
        AnchorAddResultBo resultBo = new AnchorAddResultBo();
        resultBo.setId(anchorPointRecord.getId());
        resultBo.setAvid(anchorPointRecord.getAid());
        resultBo.setCreativeId(anchorPointRecord.getCreativeId());
        return resultBo;
    }

    @Transactional(transactionManager = AdDataSourceConfig.AD_TX_MGR, rollbackFor = Exception.class)
    public Integer updateAnchorGroup(List<Long> ids, Integer groupId) {
        if (CollectionUtils.isEmpty(ids) || groupId == null) {
            return 0;
        }
//        List<LauArchiveAnchorPointRecord> lauArchiveAnchorPointRecords = lauArchiveAnchorPointRepo.fetchAnchorByIds(ids);
//        List<Integer> groupIdsNeedUpdate = lauArchiveAnchorPointRecords.stream()
//                .map(LauArchiveAnchorPointRecord::getGroupId).filter(x -> x != 0).collect(Collectors.toList());

        Integer count = lauArchiveAnchorPointRepo.updateAnchorGroup(ids, groupId);
        List<LauComponentGroupLabelRecord> lauComponentGroupLabelRecords = componentGroupService.queryByIds(List.of(groupId), 0);
        Assert.isTrue(!CollectionUtils.isEmpty(lauComponentGroupLabelRecords), "分组不存在");
        Assert.isTrue(lauComponentGroupLabelRecords.get(0).getComponentType() == ComponentType.COMPONENT_TYPE_ANCHOR_VALUE, "锚点分组不存在");
//        groupIdsNeedUpdate.add(groupId);

        updateGroupCount(groupId);
        return count;
    }

    public void updateGroupCount(Integer groupId) {
        AnchorPageQueryBo queryBo = AnchorPageQueryBo.builder()
                .groupId(groupId)
                .page(1)
                .isDeleted(Collections.singletonList(0))
                .size(1).build();
        Pager<AnchorInfoBo> anchorInfoBoPager = lauArchiveAnchorPointRepo.queryAnchorPageList(queryBo);
        componentGroupService.updateCount(ComponentGroupLabelBo.builder()
                .id(groupId)
                .count(anchorInfoBoPager.getTotal()).build());
    }

    public Integer updateTitle(Long id) {
        if (id == null) {
            return 0;
        }
        boolean isArchive = true;
        List<LauArchiveAnchorPointRecord> lauArchiveAnchorPointRecords = lauArchiveAnchorPointRepo.fetchAnchorByGtId(id, isArchive);
        int count = 0;
        while (!lauArchiveAnchorPointRecords.isEmpty()) {
            List<Long> aids = lauArchiveAnchorPointRecords.stream().map(LauArchiveAnchorPointRecord::getAid)
                    .distinct()
                    .collect(Collectors.toList());
            ArcsReply arcsReply = archiveRpcService.arcsReply(aids);
            for (LauArchiveAnchorPointRecord lauArchiveAnchorPointRecord : lauArchiveAnchorPointRecords) {
                Arc arc = arcsReply.getArcsMap().get(lauArchiveAnchorPointRecord.getAid());
                if (arc != null) {
                    count++;
                    lauArchiveAnchorPointRepo.updateTitle(lauArchiveAnchorPointRecord.getId(), arc.getTitle());
                }
            }
            try {
                Thread.sleep(400);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            id = lauArchiveAnchorPointRecords.get(lauArchiveAnchorPointRecords.size() - 1).getId();
            log.info("anchor updateTitle id={}", id);
            lauArchiveAnchorPointRecords = lauArchiveAnchorPointRepo.fetchAnchorByGtId(id, isArchive);
        }

        return count;
    }
}
