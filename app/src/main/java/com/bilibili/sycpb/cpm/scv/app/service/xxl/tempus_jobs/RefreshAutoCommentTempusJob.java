/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.bapis.archive.service.Arc;
import com.bilibili.sycpb.cpm.scv.app.service.bili.game.BiliGameService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.BiliUrlService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.account.AccountService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.*;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveAutoCommentRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveCreativeRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.http.exceptions.BiliInternalHttpException;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.AccountWalletRpcService;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.ArchiveRpcService;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.CommunityRpcService;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.PlatformRpcService;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.bos.CrmWalletBo;
import com.bilibili.sycpb.cpm.scv.app.utils.FunctionUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.*;
import com.google.common.collect.Lists;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import pleiades.component.ecode.exception.ServerException;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 刷新自动评论任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefreshAutoCommentTempusJob implements BasicProcessor {

    public static final String ID = "RefreshAutoCommentTempusJob";
    private static final String PAGE_404_URL = "https://gaoneng.bilibili.com/tetris/page/?pageId=639924941710897152";
    private static final BigDecimal BIG_DECIMAL_COST_INSUFFICIENT_THRESHOLD = BigDecimal.ZERO;

    private final LaunchArchiveService launchArchiveService;
    private final CommunityRpcService communityRpcService;
    private final AccountService accountService;
    private final LaunchUnitService launchUnitService;
    private final AccountWalletService accountWalletService;
    private final LaunchCreativeService launchCreativeService;
    private final MgkCmArchiveDaoService mgkCmArchiveDaoService;
    private final PlatformRpcService platformRpcService;
    private final IdGeneratorService idGeneratorService;
    private final BiliGameService biliGameService;
    private final ArchiveRpcService archiveRpcService;
    private final AccountWalletRpcService accountWalletRpcService;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String jobParams = Optional.ofNullable(context.getJobParams()).orElse("");

        logger.info("RefreshAutoCommentTempusJob 开始执行, 参数: {}", jobParams);
        log.info("RefreshAutoCommentTempusJob 开始执行, 参数: {}", jobParams);

        try {
            final int delayMins;
            if (StringUtils.hasText(jobParams)) {
                delayMins = Integer.parseInt(jobParams);
                logger.info("使用参数指定的延迟分钟数: {}", delayMins);
            } else {
                delayMins = 5;
                logger.info("使用默认延迟分钟数: {}", delayMins);
            }

            logger.info("开始处理增量数据");
            handleIncremental(delayMins, logger);

            TimeUnit.SECONDS.sleep(5);

            logger.info("开始处理未精选的评论");
            handleNotSelected(logger);

            logger.info("开始处理存量评论");
            handleExisting(logger);

            logger.info("RefreshAutoCommentTempusJob 执行成功");
            log.info("RefreshAutoCommentTempusJob 执行成功");
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            logger.error("RefreshAutoCommentTempusJob 执行失败", t);
            log.error("RefreshAutoCommentTempusJob 执行失败", t);
            return new ProcessResult(false, "执行失败: " + t.getMessage());
        }
    }

    private void handleExisting(OmsLogger logger) {
        logger.info("开始处理存量评论");
        // 处理存量评论
        final var existingArchiveAutoComments = launchArchiveService.fetchExistingArchiveAutoComments();
        if (CollectionUtils.isEmpty(existingArchiveAutoComments)) {
            logger.info("没有找到存量评论");
            return;
        }

        logger.info("找到存量评论数量: {}", existingArchiveAutoComments.size());
        var existingAvids = existingArchiveAutoComments.stream()
            .map(LauArchiveAutoCommentRecord::getAvid)
            .collect(Collectors.toList());

        final Map<Long, Arc> arcMap = new HashMap<>();
        for (List<Long> avids : Lists.partition(existingAvids, 100)) {
            final var arcsReply = archiveRpcService.arcsReply(avids);
            arcMap.putAll(arcsReply.getArcsMap());
        }

        existingAvids = new ArrayList<>();
        final List<LauArchiveAutoCommentRecord> validExistingArchiveAutoComments = new ArrayList<>();
        int invalidCount = 0;

        for (var existingArchiveAutoComment : existingArchiveAutoComments) {
            final var avid = existingArchiveAutoComment.getAvid();
            final var arc = arcMap.get(avid);
            if (Objects.isNull(arc) || arc.getState() < 0) {
                existingArchiveAutoComment.setStatus(ArchiveAutoCommentStatus.ARCHIVE_NOT_AVAILABLE)
                    .store();
                invalidCount++;
            } else if (!communityRpcService.isSelectiveCommentEnabled(avid)) {
                existingArchiveAutoComment.setStatus(ArchiveAutoCommentStatus.NO_PRIVILEGE_TO_COMMENT)
                    .store();
                invalidCount++;
            } else {
                existingAvids.add(avid);
                validExistingArchiveAutoComments.add(existingArchiveAutoComment);
            }
        }

        logger.info("有效存量评论数量: {}, 无效数量: {}", validExistingArchiveAutoComments.size(), invalidCount);

        var lauArchiveCreativeRecords = launchArchiveService.fetchAllArchiveCreatives(existingAvids);
        final var accountIds = lauArchiveCreativeRecords.stream()
            .map(LauArchiveCreativeRecord::getAccountId)
            .distinct()
            .collect(Collectors.toList());

        final var balanceInactiveAccountIds = fetchBalanceEmptyAccountIds(accountIds);
        if (!CollectionUtils.isEmpty(balanceInactiveAccountIds)) {
            launchArchiveService.deleteArchiveCreativeMappingByAccountIds(balanceInactiveAccountIds);
            logger.info("余额不足取消置顶账户数量: {}", balanceInactiveAccountIds.size());
            log.info("余额不足取消置顶账户数量: {}", balanceInactiveAccountIds.size());
            lauArchiveCreativeRecords = lauArchiveCreativeRecords.stream()
                .filter(x -> !balanceInactiveAccountIds.contains(x.getAccountId()))
                .collect(Collectors.toList());
        }

        // 继续处理其他验证逻辑...
        final var unitIds = lauArchiveCreativeRecords.stream()
            .map(LauArchiveCreativeRecord::getUnitId)
            .distinct()
            .collect(Collectors.toList());
        final var invalidUnitIds = launchUnitService.fetchInvalidUnitIds(unitIds);
        if (!CollectionUtils.isEmpty(invalidUnitIds)) {
            logger.info("非法单元数量: {}", invalidUnitIds.size());
            log.info("非法单元数量: {}", invalidUnitIds.size());
            launchArchiveService.deleteArchiveCreativeMappingByUnitIds(invalidUnitIds);
            lauArchiveCreativeRecords = lauArchiveCreativeRecords.stream()
                .filter(x -> !invalidUnitIds.contains(x.getUnitId()))
                .collect(Collectors.toList());
        }

        final var creativeIds = lauArchiveCreativeRecords.stream()
            .map(LauArchiveCreativeRecord::getCreativeId)
            .collect(Collectors.toList());
        final var invalidCreativeIds = launchCreativeService.fetchInValidCreativeIds(creativeIds);
        if (!CollectionUtils.isEmpty(invalidCreativeIds)) {
            logger.info("非法创意数量: {}", invalidCreativeIds.size());
            log.info("非法创意数量: {}", invalidCreativeIds.size());
            launchArchiveService.deleteArchiveCreativeMappingByCreativeIds(invalidCreativeIds);
            lauArchiveCreativeRecords = lauArchiveCreativeRecords.stream()
                .filter(x -> !invalidCreativeIds.contains(x.getCreativeId()))
                .collect(Collectors.toList());
        }

        final var ids = lauArchiveCreativeRecords.stream()
            .filter(x -> Objects.equals(x.getIsDeleted(), 1))
            .map(LauArchiveCreativeRecord::getId)
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ids)) {
            logger.info("恢复数据数量: {}", ids.size());
            log.info("恢复数据数量: {}", ids.size());
            launchArchiveService.revertArchiveCreativeMapping(ids);
        }

        final var remainingAvids = lauArchiveCreativeRecords.stream()
            .map(LauArchiveCreativeRecord::getAvid)
            .collect(Collectors.toSet());

        int processedCount = 0;
        for (var record : validExistingArchiveAutoComments) {
            try {
                final var isTop = Objects.equals(record.getStatus(), ArchiveAutoCommentStatus.TOP);
                final var isUnTop = Objects.equals(record.getStatus(), ArchiveAutoCommentStatus.UN_TOP);
                final var available = remainingAvids.contains(record.getAvid());

                if (available && isUnTop) {
                    final var topCommentId = communityRpcService.fetchTopCommentId(record.getAvid());
                    if (!NumberUtils.isPositive(topCommentId)) {
                        communityRpcService.topComment(record.getAvid(), record.getCommentId());
                        record.setStatus(ArchiveAutoCommentStatus.TOP)
                            .store();
                        processedCount++;
                    }
                } else if (!available && isTop) {
                    final var topCommentId = communityRpcService.fetchTopCommentId(record.getAvid());
                    if (Objects.equals(topCommentId, record.getCommentId())) {
                        communityRpcService.unTopComment(record.getAvid(), record.getCommentId());
                        record.setStatus(ArchiveAutoCommentStatus.UN_TOP)
                            .store();
                        processedCount++;
                    }
                }
            } catch (Throwable t) {
                logger.warn("处理存量评论失败 - avid: {}, 错误: {}", record.getAvid(), t.getMessage());
                log.error("处理存量评论失败 - avid: {}, 错误: {}", record.getAvid(), t.getMessage());
            }
        }

        logger.info("存量评论处理完成, 处理数量: {}", processedCount);
    }

    private void handleNotSelected(OmsLogger logger) {
        logger.info("开始处理未精选的评论");
        // 创建评论和精选评论都是异步的过程, 所以需要等一会儿才开始精选
        final var notSelectRecords = launchArchiveService.fetchNotSelectArchiveAutoComments();
        logger.info("找到未精选评论数量: {}", notSelectRecords.size());

        int processedCount = 0;
        for (var record : notSelectRecords) {
            // 精选评论
            try {
                if (communityRpcService.isSelectiveCommentEnabled(record.getAvid())) {
                    final var topCommentId = communityRpcService.fetchTopCommentId(record.getAvid());
                    if (Objects.equals(topCommentId, record.getCommentId())) {
                        // 能查到置顶评论说明评论已经写入成功, 可以执行精选
                        communityRpcService.selectComment(record.getMid(), record.getCommentId());
                        record.setSelectStatus(SelectStatus.SELECTED)
                            .store();
                        logger.info("开启精选评论 - avid: {}, commentId: {}", record.getAvid(), record.getCommentId());
                        processedCount++;
                    } else if (NumberUtils.isPositive(topCommentId)) {
                        // 不知道什么原因, 有其他评论已经置顶了, 放弃这条数据
                        record.setStatus(ArchiveAutoCommentStatus.TOP_COMMENT_EXISTING)
                            .store();
                        logger.info("已存在精选评论 - avid: {}, commentId: {}", record.getAvid(), record.getCommentId());
                        processedCount++;
                    }
                }
            } catch (Throwable t) {
                logger.warn("精选评论失败 - avid: {}, 错误: {}", record.getAvid(), t.getMessage());
                log.error("精选评论失败 - avid: {}, 错误: {}", record.getAvid(), t.getMessage());
            }
        }

        logger.info("未精选评论处理完成, 处理数量: {}", processedCount);
    }

    private void handleIncremental(int delayMins, OmsLogger logger) {
        logger.info("开始处理增量数据, 延迟分钟数: {}", delayMins);

        final var gameName2IdMap = FunctionUtils.execWithRetires(ID, biliGameService::name2IdMap, 3, Duration.ofMillis(500));
        if (Objects.isNull(gameName2IdMap)) {
            logger.error("获取游戏映射失败, 流程结束");
            log.error("获取游戏映射失败, 流程结束");
            throw new ServerException(MoreServerCodes.BAD_ENV);
        }
        logger.info("获取游戏映射成功, 游戏数量: {}", gameName2IdMap.size());

        // 处理新增评论
        final var lauArchiveAutoCommentRecords = launchArchiveService.fetchNewArchiveAutoComments(delayMins);
        logger.info("找到新增评论数量: {}", lauArchiveAutoCommentRecords.size());

        final var avids = lauArchiveAutoCommentRecords.stream()
            .map(LauArchiveAutoCommentRecord::getAvid)
            .collect(Collectors.toList());
        final var filteredAvids = mgkCmArchiveDaoService.fetchCmAvids(avids);
        logger.info("过滤后的稿件数量: {}", filteredAvids.size());

        int processedCount = 0;
        int skippedCount = 0;

        for (var record : lauArchiveAutoCommentRecords) {
            try {
                var commentId = communityRpcService.fetchTopCommentId(record.getAvid());
                if (NumberUtils.isPositive(commentId)) {
                    // 已存在置顶评论, 跳过
                    logger.info("稿件已存在置顶评论 - avid: {}", record.getAvid());
                    record.setStatus(ArchiveAutoCommentStatus.TOP_COMMENT_EXISTING)
                        .store();
                    skippedCount++;
                    continue;
                }

                final var accountRecord = accountService.fetch(record.getAccountId());
                final var unitRecord = launchUnitService.fetch(record.getUnitId());
                final var ppt = unitRecord.getPromotionPurposeType();
                final Tuple2<String, String> tuple2;

                if (accountService.isGameIndustry(accountRecord.getCommerceCategoryFirstId())) {
                    tuple2 = launchArchiveService.fetchGameMsg(ppt, unitRecord.getOcpcTarget());
                } else {
                    tuple2 = launchArchiveService.fetchOtherMsg(ppt, accountRecord.getCommerceCategoryFirstId(), accountRecord.getCommerceCategorySecondId());
                }

                if (Objects.isNull(tuple2)) {
                    logger.warn("获取评论内容失败 - avid: {}, creative_id: {}, ppt: {}",
                               record.getAvid(), record.getCreativeId(), ppt);
                    record.delete();
                    skippedCount++;
                    continue;
                }

                final String url;
                if (Objects.equals(ppt, LaunchType.SALE_GOODS)) {
                    url = PAGE_404_URL;
                } else if (Objects.equals(ppt, LaunchType.APP_DOWNLOAD)) {
                    final var resAppPackageRecord = launchUnitService.fetchAppInfo(unitRecord.getAppPackageId());
                    if (Objects.isNull(resAppPackageRecord)) {
                        url = PAGE_404_URL;
                    } else if (gameName2IdMap.containsKey(resAppPackageRecord.getAppName())) {
                        final var gameBaseId = gameName2IdMap.get(resAppPackageRecord.getAppName());
                        logger.info("游戏匹配成功 - {} -> {}", resAppPackageRecord.getAppName(), gameBaseId);
                        url = BiliUrlService.buildGameCenterH5Url(gameBaseId);
                    } else if (BiliUrlService.isIosAppStoreUrl(resAppPackageRecord.getUrl())) {
                        url = resAppPackageRecord.getUrl();
                    } else {
                        url = PAGE_404_URL;
                    }
                } else if (Objects.equals(ppt, LaunchType.ANDROID_GAME)) {
                    final var gameBaseId = launchUnitService.fetchGameBaseId(unitRecord.getUnitId());
                    if (Objects.isNull(gameBaseId)) {
                        url = PAGE_404_URL;
                    } else {
                        url = BiliUrlService.buildGameCenterH5Url(gameBaseId);
                    }
                } else if (Objects.equals(ppt, LaunchType.LANDING_PAGE)) {
                    final var pageId = BiliUrlService.fetchMiniAppPageId(record.getRawUrl());
                    if (Objects.isNull(pageId)) {
                        url = record.getRawUrl();
                    } else {
                        url = BiliUrlService.buildMiniAppH5Url(pageId);
                    }
                } else {
                    url = PAGE_404_URL;
                }

                // 评论已关闭, 无需注册
                if (communityRpcService.isCommentDisabled(record.getAvid())) {
                    logger.info("稿件评论区已关闭 - avid: {}", record.getAvid());
                    record.setStatus(ArchiveAutoCommentStatus.COMMENT_AREA_NOT_AVAILABLE)
                        .store();
                    skippedCount++;
                    continue;
                }

                final Long commentMid;
                if (filteredAvids.contains(record.getAvid())) {
                    // 子账号优先取单元绑定mid, 然后取授权mid, 最后使用自己的mid
                    if (NumberUtils.isPositive(unitRecord.getEnterpriseMid())) {
                        commentMid = unitRecord.getEnterpriseMid();
                    } else {
                        final var lauAccountInfoRecord = launchUnitService.fetchBrandInfoById(unitRecord.getBrandInfoId());
                        if (Objects.nonNull(lauAccountInfoRecord) && NumberUtils.isPositive(lauAccountInfoRecord.getMid())) {
                            commentMid = lauAccountInfoRecord.getMid();
                        } else {
                            final var lauAccountInfoRecords = launchUnitService.fetchBrandInfoByAccountId(record.getAccountId());
                            final var optional = lauAccountInfoRecords.stream()
                                .filter(x -> NumberUtils.isPositive(x.getMid()))
                                .min((x, y) -> -x.getCtime().compareTo(y.getCtime()));
                            if (optional.isPresent()) {
                                commentMid = optional.get().getMid();
                            } else {
                                commentMid = record.getMid();
                            }
                        }
                    }
                } else {
                    // 真实up主使用自己的mid
                    commentMid = record.getMid();
                }

                // 注册短链
                final var shortUrl = platformRpcService.genShortUrl(url, genAutoShortUrl());
                final var commentMsg = tuple2._1.replace(tuple2._2, shortUrl);

                // 注册评论
                commentId = communityRpcService.createComment(record.getAvid(), commentMid, commentMsg);
                record.setRawUrl(url)
                    .setLinkUrl(shortUrl)
                    .setLinkMsg(tuple2._2)
                    .setCommentId(commentId)
                    .setCommentMid(commentMid)
                    .setCommentMsg(commentMsg)
                    .setStatus(ArchiveAutoCommentStatus.TOP)
                    .store();

                processedCount++;
                logger.info("成功创建评论 - avid: {}, commentId: {}", record.getAvid(), commentId);

            } catch (Throwable t) {
                logger.warn("处理新增评论失败 - avid: {}, 错误: {}", record.getAvid(), t.getMessage());
                log.error("处理新增评论失败 - avid: {}, 错误: {}", record.getAvid(), t.getMessage());

                if (t instanceof BiliInternalHttpException) {
                    final var internalExp = (BiliInternalHttpException) t;
                    if (CommentErrors.isKnownError(internalExp.getCode())) {
                        record.setStatus(ArchiveAutoCommentStatus.NO_PRIVILEGE_TO_COMMENT)
                            .store();
                    }
                }
                skippedCount++;
            }
        }

        logger.info("增量数据处理完成, 成功处理: {}, 跳过: {}", processedCount, skippedCount);
    }

    private String genAutoShortUrl() {
        return "cm-cmt-0-" + NumberUtils.radix62(idGeneratorService.next());
    }

    public Set<Integer> fetchBalanceEmptyAccountIds(Collection<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptySet();
        }

        // accountIds100个一批查询钱包余额
        Set<Integer> balanceEmptyAccountIds = new HashSet<>();
        for (List<Integer> subAccountIdList : Lists.partition(new ArrayList<>(accountIds), 100)) {
            final var accountWallets = accountWalletRpcService.queryWalletBoByAccIds(subAccountIdList);
            final var subBalanceEmptyAccountIds = accountWallets.stream()
                    .filter(this::isNotEnoughMoney)
                    .map(CrmWalletBo::getAccountId)
                    .collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(subBalanceEmptyAccountIds)) {
                balanceEmptyAccountIds.addAll(subBalanceEmptyAccountIds);
            }
        }

        return accountWalletService.fetchBalanceInactiveAccountIds(balanceEmptyAccountIds);
    }

    private boolean isNotEnoughMoney(CrmWalletBo walletBo) {
        if (walletBo == null) {
            return false;
        }

        return Stream.of(
                        Optional.ofNullable(walletBo.getCash()),
                        Optional.ofNullable(walletBo.getRedPacket()),
                        Optional.ofNullable(walletBo.getSpecialRedPacket()),
                        Optional.ofNullable(walletBo.getCredit())
                )
                .map(opt -> opt.orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .compareTo(BIG_DECIMAL_COST_INSUFFICIENT_THRESHOLD) <= 0;
    }
}
