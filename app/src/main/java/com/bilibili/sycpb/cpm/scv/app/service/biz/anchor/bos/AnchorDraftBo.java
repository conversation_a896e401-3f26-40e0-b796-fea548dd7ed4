/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnchorDraftBo {

    private Long anchorId;


    private Integer type;
    /**
     * 触审字段
     */
    private Integer clueType;
    private String name;
    private List<Integer> scenes;
    private String clueData;

    private String mainTitle;
    private String subTitle;
    private String buttonText;

    private Integer iosUrlType;
    private Long iosUrlPageId;
    private String iosUrl;
    private Integer androidUrlType;
    private Long androidUrlPageId;
    private String androidUrl;
    private Integer conversionUrlType;
    private Long conversionUrlPageId;
    private String conversionUrl;

    private String iosSchemaUrl;
    private String androidSchemaUrl;
    private String iosButtonSchemaUrl;
    private String androidButtonSchemaUrl;

    private Integer isOpenMiniGame;
    private Integer miniGameId;
    private Integer gameBaseId;
    private Long biliMiniGameMid;
    private Integer gamePlatformType;
    private Integer iosAppPackageId;
    private Integer androidAppPackageId;

    private Integer subPkg;
    private Integer wechatPackageId;
    private Long formId;
    private String customizedImpUrl;
    private String customizedClickUrl;

    private String guideText;
    private String appDetailText;
    // APP标签
    private List<String> appLabels;
    // app详情类型: 1横图2竖图
    private Integer appDetailType;
    // 顶图
    private List<String> topImgUrls;
    // app 图片列表
    private List<String> appImgUrls;
    private Integer isAndroidAppDirect;

    private Integer groupId;

    private String biliAppletUrl;

    private Integer notShowInNatureFlow;
}
