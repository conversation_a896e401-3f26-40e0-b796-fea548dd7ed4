/*
 * Copyright (c) 2015-2023 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.rpc.comment_goods;

import com.bapis.ad.cmc.cidgoods.AuthedAccountQueryReq;
import com.bapis.ad.cmc.cidgoods.AuthedAccountQueryResp;
import com.bapis.ad.cmc.cidgoods.CidGoodsServiceGrpc;
import com.bapis.ad.cmc.comment_goods.CommentGoodsGrpc;
import com.bapis.ad.cmc.comment_goods.QueryProductReq;
import com.bapis.ad.cmc.dynamic.AsyncBatchCallBackMsg;
import com.bapis.ad.cmc.dynamic.AsyncBatchPublishCmcCommentsReq;
import com.bapis.ad.cmc.dynamic.DynamicGoodsGrpc;
import com.bapis.ad.cmc.dynamic.PublishCmcCommentReq;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.comment_goods.bos.CommentContainerEntityBo;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.comment_goods.bos.CommentGoodsProductBo;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.comment_goods.bos.DynamicGoodsReqBo;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.comment_goods.bos.DynamicGoodsRespBo;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pleiades.component.env.Environment;
import pleiades.component.env.EnvironmentKeys;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommentGoodsRpcService {
    @RPCClient("sycpb.cpm.tavern-platform")
    private CommentGoodsGrpc.CommentGoodsBlockingStub commentGoodsBlockingStub;
    @RPCClient("mall.ecommerce.mall-cbp")
    private DynamicGoodsGrpc.DynamicGoodsBlockingStub dynamicGoodsBlockingStub;
    @RPCClient(value = "sycpb.cpm.tavern-platform")
    private CidGoodsServiceGrpc.CidGoodsServiceBlockingStub cidGoodsServiceBlockingStub;

    public CommentGoodsProductBo getProduct(Long productId) {
        final var resp = commentGoodsBlockingStub.withDeadlineAfter(5, TimeUnit.SECONDS).queryProductByIdList(QueryProductReq.newBuilder()
            .addItemIds(productId)
            .build());
        if (resp.getProductsCount() == 0) throw new IllegalArgumentException("商品id="+productId+"对应的商品不存在");

        return CommentGoodsConverter.INSTANCE.fromRpcBo(resp.getProducts(0));
    }

    public void batchCreateGoodsCommentAsync(List<CommentContainerEntityBo> containerEntityBos, DynamicGoodsReqBo templateBo) {
        final List<PublishCmcCommentReq> reqList = new ArrayList<>();
        for (var containerEntityBo : containerEntityBos) {
            final var bo = CommentGoodsConverter.INSTANCE.copy(templateBo);
            bo.setUniqueKey(CommentGoodsConverter.genFakeUk(containerEntityBo.getAvid(), containerEntityBo.getDynamicId()));
            bo.setMid(containerEntityBo.getMid());
            bo.setOldDynamicId(containerEntityBo.getOldDynamicId());
            final var tuple2 = CommentGoodsConverter.toContainerValues(containerEntityBo.getAvid(), containerEntityBo.getDynamicId(), containerEntityBo.getOldDynamicId());
            bo.setContainerId(tuple2._2);
            bo.setContainerType(tuple2._1);
            reqList.add(CommentGoodsConverter.INSTANCE.toRpcBo(bo));
        }
        final var req = AsyncBatchPublishCmcCommentsReq.newBuilder()
            .addAllReqs(reqList)
            .setCallbackMsg(AsyncBatchCallBackMsg.newBuilder()
                .setCallBackUrl("http://" + determinePrefix() + "cm-mng.bilibili.co/scv/api/open/goods/comment/call_back")
                .build())
            .build();
        try {
            dynamicGoodsBlockingStub.withDeadlineAfter(4, TimeUnit.SECONDS)
                .asyncBatchPublishCmcComments(req);
            log.info("batchCreateGoodsCommentAsync success: req={}", req);
        } catch (Throwable t) {
            log.error("batchCreateGoodsCommentAsync failed: req={}, err={}", req, t.getMessage());
            throw new StatusRuntimeException(Status.INTERNAL.withDescription("调用带货创建评论失败"));
        }
    }

    /**
     * 创建稿件/动态的商品评论
     *
     * @param reqBo
     * @return
     */
    public DynamicGoodsRespBo createGoodsComment(DynamicGoodsReqBo reqBo) {
        try {
            // todo batch
            final var req = CommentGoodsConverter.INSTANCE.toRpcBo(reqBo);
            final var resp = dynamicGoodsBlockingStub.withDeadlineAfter(5, TimeUnit.SECONDS).publishCmcComment(req);
            final var respBo = CommentGoodsConverter.INSTANCE.fromRpcBo(resp);
            log.info("createGoodsComment success: req={}, resp={}", reqBo, respBo);
            return respBo;
        } catch (Throwable t) {
            log.error("createGoodsComment failed: req={}, err={}", reqBo, t.getMessage());
            throw new StatusRuntimeException(Status.INTERNAL.withDescription("调用带货创建评论失败"));
        }
    }

    public List<Integer> queryAuthAccountIds(Long productId) {
        AuthedAccountQueryReq authedAccountQueryReq = AuthedAccountQueryReq.newBuilder()
            .setItemId(productId)
            .build();
        // 带货商品的授权列表
        AuthedAccountQueryResp authedAccountQueryResp = cidGoodsServiceBlockingStub.withDeadlineAfter(5, TimeUnit.SECONDS)
                .queryAuthedAccount(authedAccountQueryReq);
        List<Integer> accountIdList = authedAccountQueryResp.getAccountIdList();
        return accountIdList;
    }

    private String determinePrefix() {
        final var optionalEnv = Environment.ofNullable(EnvironmentKeys.DEPLOY_ENV);
        if (optionalEnv.isPresent()) {
            final var env = optionalEnv.get();
            if (Objects.equals(env, "prod")) return "";

            if (Objects.equals(env, "pre")) return "pre-";
        }
        return "uat-";
    }
}
