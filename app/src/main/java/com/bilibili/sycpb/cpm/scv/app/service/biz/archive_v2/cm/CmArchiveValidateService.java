package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm;

import com.bapis.ad.adp.archive.AdpArchiveServiceGrpc;
import com.bapis.ad.adp.archive.CheckAdpBiliAuthedMidReply;
import com.bapis.ad.adp.archive.CheckAdpBiliAuthedMidReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.concurrent.TimeUnit;

/**
 * @ClassName CmArchiveValidateService
 * <AUTHOR>
 * @Date 2025/5/16 12:13 上午
 * @Version 1.0
 **/
@Service
@Slf4j
public class CmArchiveValidateService {

    @RPCClient("sycpb.cpm.cpm-adp")
    private AdpArchiveServiceGrpc.AdpArchiveServiceBlockingStub adpArchiveServiceBlockingStub;

    public void checkMidAccountBiliAuthRelation(Integer accountId, Long mid) {
        CheckAdpBiliAuthedMidReq req = CheckAdpBiliAuthedMidReq.newBuilder()
                .setAccountId(accountId)
                .setMid(mid)
                .build();
        CheckAdpBiliAuthedMidReply reply = adpArchiveServiceBlockingStub.withWaitForReady()
                .withDeadlineAfter(1000, TimeUnit.MILLISECONDS)
                .checkAdpBiliAuthMid(req);

        Assert.isTrue(reply.getValid(),
                "账户:" + accountId + " 与mid:" + mid + " 没有授权绑定关系,不允许进行真实号投稿");
    }

}
