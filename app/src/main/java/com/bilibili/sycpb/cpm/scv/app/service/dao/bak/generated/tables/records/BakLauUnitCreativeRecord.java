/*
 * This file is generated by jOOQ.
 */
package com.bilibili.sycpb.cpm.scv.app.service.dao.bak.generated.tables.records;


import com.bilibili.sycpb.cpm.scv.app.service.dao.bak.generated.tables.TBakLauUnitCreative;
import com.bilibili.sycpb.cpm.scv.app.service.dao.bak.generated.tables.pojos.BakLauUnitCreativePo;

import java.sql.Date;
import java.sql.Timestamp;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 创意备份表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BakLauUnitCreativeRecord extends UpdatableRecordImpl<BakLauUnitCreativeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>bak_lau_unit_creative.creative_id</code>. 创意ID
     */
    public BakLauUnitCreativeRecord setCreativeId(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.creative_id</code>. 创意ID
     */
    public Integer getCreativeId() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.account_id</code>. 账户id
     */
    public BakLauUnitCreativeRecord setAccountId(Integer value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.account_id</code>. 账户id
     */
    public Integer getAccountId() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.campaign_id</code>. 计划id
     */
    public BakLauUnitCreativeRecord setCampaignId(Integer value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.campaign_id</code>. 计划id
     */
    public Integer getCampaignId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.creative_type</code>.
     * 1-图片，2-视频，3-feeds图片，4-feeds视频(冗余字段，从模板中获取)
     */
    public BakLauUnitCreativeRecord setCreativeType(Integer value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.creative_type</code>.
     * 1-图片，2-视频，3-feeds图片，4-feeds视频(冗余字段，从模板中获取)
     */
    public Integer getCreativeType() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.unit_id</code>. 单位ID
     */
    public BakLauUnitCreativeRecord setUnitId(Integer value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.unit_id</code>. 单位ID
     */
    public Integer getUnitId() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.creative_name</code>. 创意名称
     */
    public BakLauUnitCreativeRecord setCreativeName(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.creative_name</code>. 创意名称
     */
    public String getCreativeName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.promotion_purpose_content</code>.
     * 推广目的内容
     */
    public BakLauUnitCreativeRecord setPromotionPurposeContent(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.promotion_purpose_content</code>.
     * 推广目的内容
     */
    public String getPromotionPurposeContent() {
        return (String) get(6);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.customized_imp_url</code>. 展示监控链接
     */
    public BakLauUnitCreativeRecord setCustomizedImpUrl(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.customized_imp_url</code>. 展示监控链接
     */
    public String getCustomizedImpUrl() {
        return (String) get(7);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.customized_click_url</code>.
     * 点击监控链接
     */
    public BakLauUnitCreativeRecord setCustomizedClickUrl(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.customized_click_url</code>.
     * 点击监控链接
     */
    public String getCustomizedClickUrl() {
        return (String) get(8);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.title</code>. 标题
     */
    public BakLauUnitCreativeRecord setTitle(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.title</code>. 标题
     */
    public String getTitle() {
        return (String) get(9);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.description</code>. 描述
     */
    public BakLauUnitCreativeRecord setDescription(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.description</code>. 描述
     */
    public String getDescription() {
        return (String) get(10);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.ext_description</code>.
     * 扩展的描述(广告主名称等)
     */
    public BakLauUnitCreativeRecord setExtDescription(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.ext_description</code>.
     * 扩展的描述(广告主名称等)
     */
    public String getExtDescription() {
        return (String) get(11);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.image_url</code>. 图片URL
     */
    public BakLauUnitCreativeRecord setImageUrl(String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.image_url</code>. 图片URL
     */
    public String getImageUrl() {
        return (String) get(12);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.image_md5</code>. 图片的MD5值
     */
    public BakLauUnitCreativeRecord setImageMd5(String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.image_md5</code>. 图片的MD5值
     */
    public String getImageMd5() {
        return (String) get(13);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.video_id</code>. 视频ID(如AVID等)
     */
    public BakLauUnitCreativeRecord setVideoId(Long value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.video_id</code>. 视频ID(如AVID等)
     */
    public Long getVideoId() {
        return (Long) get(14);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.video_url</code>. 视频URL
     */
    public BakLauUnitCreativeRecord setVideoUrl(String value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.video_url</code>. 视频URL
     */
    public String getVideoUrl() {
        return (String) get(15);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.ext_image_url</code>.
     * 扩展图片URL(缩略图、logo等的URL)
     */
    public BakLauUnitCreativeRecord setExtImageUrl(String value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.ext_image_url</code>.
     * 扩展图片URL(缩略图、logo等的URL)
     */
    public String getExtImageUrl() {
        return (String) get(16);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.ext_image_md5</code>. 扩展图片MD5值
     */
    public BakLauUnitCreativeRecord setExtImageMd5(String value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.ext_image_md5</code>. 扩展图片MD5值
     */
    public String getExtImageMd5() {
        return (String) get(17);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.creative_json</code>. 创意相关JSON串
     */
    public BakLauUnitCreativeRecord setCreativeJson(String value) {
        set(18, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.creative_json</code>. 创意相关JSON串
     */
    public String getCreativeJson() {
        return (String) get(18);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.reason</code>. 原因（审核拒绝时填写）
     */
    public BakLauUnitCreativeRecord setReason(String value) {
        set(19, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.reason</code>. 原因（审核拒绝时填写）
     */
    public String getReason() {
        return (String) get(19);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.template_id</code>. 创意模板ID
     */
    public BakLauUnitCreativeRecord setTemplateId(Integer value) {
        set(20, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.template_id</code>. 创意模板ID
     */
    public Integer getTemplateId() {
        return (Integer) get(20);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.audit_status</code>.
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    public BakLauUnitCreativeRecord setAuditStatus(Integer value) {
        set(21, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.audit_status</code>.
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    public Integer getAuditStatus() {
        return (Integer) get(21);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.status</code>. 状态（1-有效，2-暂停, 3-删除
     * 4-结束 5-修改待下线）
     */
    public BakLauUnitCreativeRecord setStatus(Integer value) {
        set(22, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.status</code>. 状态（1-有效，2-暂停, 3-删除
     * 4-结束 5-修改待下线）
     */
    public Integer getStatus() {
        return (Integer) get(22);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.version</code>. 版本号
     */
    public BakLauUnitCreativeRecord setVersion(Integer value) {
        set(23, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.version</code>. 版本号
     */
    public Integer getVersion() {
        return (Integer) get(23);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public BakLauUnitCreativeRecord setIsDeleted(Integer value) {
        set(24, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_deleted</code>. 软删除，0是有效，1是删除
     */
    public Integer getIsDeleted() {
        return (Integer) get(24);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.ctime</code>. 添加时间
     */
    public BakLauUnitCreativeRecord setCtime(Timestamp value) {
        set(25, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.ctime</code>. 添加时间
     */
    public Timestamp getCtime() {
        return (Timestamp) get(25);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.mtime</code>. 更新时间
     */
    public BakLauUnitCreativeRecord setMtime(Timestamp value) {
        set(26, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.mtime</code>. 更新时间
     */
    public Timestamp getMtime() {
        return (Timestamp) get(26);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.order_id</code>. 订单ID
     */
    public BakLauUnitCreativeRecord setOrderId(Integer value) {
        set(27, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.order_id</code>. 订单ID
     */
    public Integer getOrderId() {
        return (Integer) get(27);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.sales_type</code>. 售卖类型 11-CPM,
     * 12-CPC, 21-GD
     */
    public BakLauUnitCreativeRecord setSalesType(Integer value) {
        set(28, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.sales_type</code>. 售卖类型 11-CPM,
     * 12-CPC, 21-GD
     */
    public Integer getSalesType() {
        return (Integer) get(28);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.cm_mark</code>. 广告角标(默认为广告)
     */
    public BakLauUnitCreativeRecord setCmMark(Short value) {
        set(29, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.cm_mark</code>. 广告角标(默认为广告)
     */
    public Short getCmMark() {
        return (Short) get(29);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.button_copy</code>. 按钮文案
     */
    public BakLauUnitCreativeRecord setButtonCopy(String value) {
        set(30, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.button_copy</code>. 按钮文案
     */
    public String getButtonCopy() {
        return (String) get(30);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.category_first_id</code>. 创意一级分类id
     */
    public BakLauUnitCreativeRecord setCategoryFirstId(Integer value) {
        set(31, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.category_first_id</code>. 创意一级分类id
     */
    public Integer getCategoryFirstId() {
        return (Integer) get(31);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.category_second_id</code>.
     * 创意二级分类id
     */
    public BakLauUnitCreativeRecord setCategorySecondId(Integer value) {
        set(32, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.category_second_id</code>.
     * 创意二级分类id
     */
    public Integer getCategorySecondId() {
        return (Integer) get(32);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.tags</code>. 创意分类
     */
    public BakLauUnitCreativeRecord setTags(String value) {
        set(33, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.tags</code>. 创意分类
     */
    public String getTags() {
        return (String) get(33);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.creative_status</code>.
     * 创意状态:1-有效,2-已暂停,3-已结束,4-已删除,5-审核中,6-审核拒绝
     */
    public BakLauUnitCreativeRecord setCreativeStatus(Integer value) {
        set(34, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.creative_status</code>.
     * 创意状态:1-有效,2-已暂停,3-已结束,4-已删除,5-审核中,6-审核拒绝
     */
    public Integer getCreativeStatus() {
        return (Integer) get(34);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.begin_time</code>. 投放开始时间
     */
    public BakLauUnitCreativeRecord setBeginTime(Date value) {
        set(35, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.begin_time</code>. 投放开始时间
     */
    public Date getBeginTime() {
        return (Date) get(35);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.end_time</code>. 投放结束时间
     */
    public BakLauUnitCreativeRecord setEndTime(Date value) {
        set(36, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.end_time</code>. 投放结束时间
     */
    public Date getEndTime() {
        return (Date) get(36);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_mark</code>. 校验创意分类 0:未校验 1:准确
     * 2:不准确
     */
    public BakLauUnitCreativeRecord setIsMark(Integer value) {
        set(37, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_mark</code>. 校验创意分类 0:未校验 1:准确
     * 2:不准确
     */
    public Integer getIsMark() {
        return (Integer) get(37);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_history</code>. 是否是历史创意: 0-否
     * 1-是
     */
    public BakLauUnitCreativeRecord setIsHistory(Integer value) {
        set(38, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_history</code>. 是否是历史创意: 0-否
     * 1-是
     */
    public Integer getIsHistory() {
        return (Integer) get(38);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_tag</code>. 是否标签标注 0:未标注 1:已标注
     */
    public BakLauUnitCreativeRecord setIsTag(Integer value) {
        set(39, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_tag</code>. 是否标签标注 0:未标注 1:已标注
     */
    public Integer getIsTag() {
        return (Integer) get(39);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.scheme_url</code>. 唤起scheme链接
     */
    public BakLauUnitCreativeRecord setSchemeUrl(String value) {
        set(40, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.scheme_url</code>. 唤起scheme链接
     */
    public String getSchemeUrl() {
        return (String) get(40);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.jump_type</code>. 跳转类型(1-链接 2-移动视频
     * 3-游戏 4-Web视频 5-页面ID）
     */
    public BakLauUnitCreativeRecord setJumpType(Integer value) {
        set(41, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.jump_type</code>. 跳转类型(1-链接 2-移动视频
     * 3-游戏 4-Web视频 5-页面ID）
     */
    public Integer getJumpType() {
        return (Integer) get(41);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.bilibili_user_id</code>.
     * 没有用的字段过几天删除
     */
    public BakLauUnitCreativeRecord setBilibiliUserId(Integer value) {
        set(42, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.bilibili_user_id</code>.
     * 没有用的字段过几天删除
     */
    public Integer getBilibiliUserId() {
        return (Integer) get(42);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.ad_version_controll_id</code>.
     * 广告版本控制ID
     */
    public BakLauUnitCreativeRecord setAdVersionControllId(Integer value) {
        set(43, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.ad_version_controll_id</code>.
     * 广告版本控制ID
     */
    public Integer getAdVersionControllId() {
        return (Integer) get(43);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.mgk_page_id</code>. 万花筒页面ID
     */
    public BakLauUnitCreativeRecord setMgkPageId(Long value) {
        set(44, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.mgk_page_id</code>. 万花筒页面ID
     */
    public Long getMgkPageId() {
        return (Long) get(44);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.ad_mark</code>. 广告业务标
     */
    public BakLauUnitCreativeRecord setAdMark(String value) {
        set(45, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.ad_mark</code>. 广告业务标
     */
    public String getAdMark() {
        return (String) get(45);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.modify_offline_creative_id</code>.
     * 修改待下线的创意ID
     */
    public BakLauUnitCreativeRecord setModifyOfflineCreativeId(Integer value) {
        set(46, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.modify_offline_creative_id</code>.
     * 修改待下线的创意ID
     */
    public Integer getModifyOfflineCreativeId() {
        return (Integer) get(46);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.flow_weight_state</code>. 流量权重状态
     * 1-正常，2-即将降权，3-已降权
     */
    public BakLauUnitCreativeRecord setFlowWeightState(Integer value) {
        set(47, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.flow_weight_state</code>. 流量权重状态
     * 1-正常，2-即将降权，3-已降权
     */
    public Integer getFlowWeightState() {
        return (Integer) get(47);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.bus_mark_id</code>. 商业标id
     */
    public BakLauUnitCreativeRecord setBusMarkId(Integer value) {
        set(48, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.bus_mark_id</code>. 商业标id
     */
    public Integer getBusMarkId() {
        return (Integer) get(48);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.style_ability</code>. 创意形态 1-静态图文
     * 2-动态图文 3-静态视频 4-广告位播放视频
     */
    public BakLauUnitCreativeRecord setStyleAbility(Integer value) {
        set(49, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.style_ability</code>. 创意形态 1-静态图文
     * 2-动态图文 3-静态视频 4-广告位播放视频
     */
    public Integer getStyleAbility() {
        return (Integer) get(49);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.adp_version</code>. 广告平台版本号 0-老版
     * 1-新版
     */
    public BakLauUnitCreativeRecord setAdpVersion(Integer value) {
        set(50, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.adp_version</code>. 广告平台版本号 0-老版
     * 1-新版
     */
    public Integer getAdpVersion() {
        return (Integer) get(50);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.auto_audit_flag</code>. 是否被机审 0否
     * 1是
     */
    public BakLauUnitCreativeRecord setAutoAuditFlag(Integer value) {
        set(51, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.auto_audit_flag</code>. 是否被机审 0否
     * 1是
     */
    public Integer getAutoAuditFlag() {
        return (Integer) get(51);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_programmatic</code>. 是否程序化创意(0
     * - 否; 1 - 是)
     */
    public BakLauUnitCreativeRecord setIsProgrammatic(Integer value) {
        set(52, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_programmatic</code>. 是否程序化创意(0
     * - 否; 1 - 是)
     */
    public Integer getIsProgrammatic() {
        return (Integer) get(52);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.material_id</code>. 物料ID(0-未知)
     */
    public BakLauUnitCreativeRecord setMaterialId(Long value) {
        set(53, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.material_id</code>. 物料ID(0-未知)
     */
    public Long getMaterialId() {
        return (Long) get(53);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.title_id</code>. 标题ID(0-未知)
     */
    public BakLauUnitCreativeRecord setTitleId(Long value) {
        set(54, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.title_id</code>. 标题ID(0-未知)
     */
    public Long getTitleId() {
        return (Long) get(54);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_new_fly</code>. 新起飞：0-否 1-是
     */
    public BakLauUnitCreativeRecord setIsNewFly(Integer value) {
        set(55, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_new_fly</code>. 新起飞：0-否 1-是
     */
    public Integer getIsNewFly() {
        return (Integer) get(55);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.prog_audit_status</code>.
     * 程序化创意审核状态: 0-审核完成 1-待审
     */
    public BakLauUnitCreativeRecord setProgAuditStatus(Integer value) {
        set(56, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.prog_audit_status</code>.
     * 程序化创意审核状态: 0-审核完成 1-待审
     */
    public Integer getProgAuditStatus() {
        return (Integer) get(56);
    }

    /**
     * Setter for
     * <code>bak_lau_unit_creative.prog_misc_elem_audit_status</code>.
     * 程序化创意额外元素审核状态: 0-审核通过 1-待审 2-审核拒绝
     */
    public BakLauUnitCreativeRecord setProgMiscElemAuditStatus(Integer value) {
        set(57, value);
        return this;
    }

    /**
     * Getter for
     * <code>bak_lau_unit_creative.prog_misc_elem_audit_status</code>.
     * 程序化创意额外元素审核状态: 0-审核通过 1-待审 2-审核拒绝
     */
    public Integer getProgMiscElemAuditStatus() {
        return (Integer) get(57);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.prefer_scene</code>. 优选广告位 0-否 1-是
     */
    public BakLauUnitCreativeRecord setPreferScene(Integer value) {
        set(58, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.prefer_scene</code>. 优选广告位 0-否 1-是
     */
    public Integer getPreferScene() {
        return (Integer) get(58);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.template_group_id</code>. 模板组ID
     */
    public BakLauUnitCreativeRecord setTemplateGroupId(Integer value) {
        set(59, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.template_group_id</code>. 模板组ID
     */
    public Integer getTemplateGroupId() {
        return (Integer) get(59);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_recheck</code>. 是否被质检 0否 1是
     */
    public BakLauUnitCreativeRecord setIsRecheck(Integer value) {
        set(60, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_recheck</code>. 是否被质检 0否 1是
     */
    public Integer getIsRecheck() {
        return (Integer) get(60);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.flag</code>. 多种类型、枚举等通用字段。 0~100
     * 来源
     */
    public BakLauUnitCreativeRecord setFlag(Integer value) {
        set(61, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.flag</code>. 多种类型、枚举等通用字段。 0~100
     * 来源
     */
    public Integer getFlag() {
        return (Integer) get(61);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.material_video_id</code>. 素材视频id
     */
    public BakLauUnitCreativeRecord setMaterialVideoId(Long value) {
        set(62, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.material_video_id</code>. 素材视频id
     */
    public Long getMaterialVideoId() {
        return (Long) get(62);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.under_frame_audit_flag</code>.
     * 框下位置是否允许投放 0不允许 1允许
     */
    public BakLauUnitCreativeRecord setUnderFrameAuditFlag(Integer value) {
        set(63, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.under_frame_audit_flag</code>.
     * 框下位置是否允许投放 0不允许 1允许
     */
    public Integer getUnderFrameAuditFlag() {
        return (Integer) get(63);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_auto_fill</code>. 是否自动填写0-手动填写
     * 1-自动填写
     */
    public BakLauUnitCreativeRecord setIsAutoFill(Integer value) {
        set(64, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_auto_fill</code>. 是否自动填写0-手动填写
     * 1-自动填写
     */
    public Integer getIsAutoFill() {
        return (Integer) get(64);
    }

    /**
     * Setter for
     * <code>bak_lau_unit_creative.promotion_purpose_content_secondary</code>.
     * 推广目的降级内容
     */
    public BakLauUnitCreativeRecord setPromotionPurposeContentSecondary(String value) {
        set(65, value);
        return this;
    }

    /**
     * Getter for
     * <code>bak_lau_unit_creative.promotion_purpose_content_secondary</code>.
     * 推广目的降级内容
     */
    public String getPromotionPurposeContentSecondary() {
        return (String) get(65);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_managed</code>. 是否是专业托管的创意
     */
    public BakLauUnitCreativeRecord setIsManaged(Integer value) {
        set(66, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_managed</code>. 是否是专业托管的创意
     */
    public Integer getIsManaged() {
        return (Integer) get(66);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_gd_plus</code>. 是否gd+：0-否 1-是
     */
    public BakLauUnitCreativeRecord setIsGdPlus(Integer value) {
        set(67, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_gd_plus</code>. 是否gd+：0-否 1-是
     */
    public Integer getIsGdPlus() {
        return (Integer) get(67);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.advertising_mode</code>.
     * 投放模式(0-普通投放，1-原生内容投放)
     */
    public BakLauUnitCreativeRecord setAdvertisingMode(Integer value) {
        set(68, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.advertising_mode</code>.
     * 投放模式(0-普通投放，1-原生内容投放)
     */
    public Integer getAdvertisingMode() {
        return (Integer) get(68);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_middle_ad</code>. 是新中台广告：0-否
     * 1-是
     */
    public BakLauUnitCreativeRecord setIsMiddleAd(Integer value) {
        set(69, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_middle_ad</code>. 是新中台广告：0-否
     * 1-是
     */
    public Integer getIsMiddleAd() {
        return (Integer) get(69);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.is_video_bind</code>.
     * 是否视频已授权，0：否，1：是
     */
    public BakLauUnitCreativeRecord setIsVideoBind(Integer value) {
        set(70, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.is_video_bind</code>.
     * 是否视频已授权，0：否，1：是
     */
    public Integer getIsVideoBind() {
        return (Integer) get(70);
    }

    /**
     * Setter for <code>bak_lau_unit_creative.trackadf</code>. trackadf
     */
    public BakLauUnitCreativeRecord setTrackadf(String value) {
        set(71, value);
        return this;
    }

    /**
     * Getter for <code>bak_lau_unit_creative.trackadf</code>. trackadf
     */
    public String getTrackadf() {
        return (String) get(71);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached BakLauUnitCreativeRecord
     */
    public BakLauUnitCreativeRecord() {
        super(TBakLauUnitCreative.BAK_LAU_UNIT_CREATIVE);
    }

    /**
     * Create a detached, initialised BakLauUnitCreativeRecord
     */
    public BakLauUnitCreativeRecord(Integer creativeId, Integer accountId, Integer campaignId, Integer creativeType, Integer unitId, String creativeName, String promotionPurposeContent, String customizedImpUrl, String customizedClickUrl, String title, String description, String extDescription, String imageUrl, String imageMd5, Long videoId, String videoUrl, String extImageUrl, String extImageMd5, String creativeJson, String reason, Integer templateId, Integer auditStatus, Integer status, Integer version, Integer isDeleted, Timestamp ctime, Timestamp mtime, Integer orderId, Integer salesType, Short cmMark, String buttonCopy, Integer categoryFirstId, Integer categorySecondId, String tags, Integer creativeStatus, Date beginTime, Date endTime, Integer isMark, Integer isHistory, Integer isTag, String schemeUrl, Integer jumpType, Integer bilibiliUserId, Integer adVersionControllId, Long mgkPageId, String adMark, Integer modifyOfflineCreativeId, Integer flowWeightState, Integer busMarkId, Integer styleAbility, Integer adpVersion, Integer autoAuditFlag, Integer isProgrammatic, Long materialId, Long titleId, Integer isNewFly, Integer progAuditStatus, Integer progMiscElemAuditStatus, Integer preferScene, Integer templateGroupId, Integer isRecheck, Integer flag, Long materialVideoId, Integer underFrameAuditFlag, Integer isAutoFill, String promotionPurposeContentSecondary, Integer isManaged, Integer isGdPlus, Integer advertisingMode, Integer isMiddleAd, Integer isVideoBind, String trackadf) {
        super(TBakLauUnitCreative.BAK_LAU_UNIT_CREATIVE);

        setCreativeId(creativeId);
        setAccountId(accountId);
        setCampaignId(campaignId);
        setCreativeType(creativeType);
        setUnitId(unitId);
        setCreativeName(creativeName);
        setPromotionPurposeContent(promotionPurposeContent);
        setCustomizedImpUrl(customizedImpUrl);
        setCustomizedClickUrl(customizedClickUrl);
        setTitle(title);
        setDescription(description);
        setExtDescription(extDescription);
        setImageUrl(imageUrl);
        setImageMd5(imageMd5);
        setVideoId(videoId);
        setVideoUrl(videoUrl);
        setExtImageUrl(extImageUrl);
        setExtImageMd5(extImageMd5);
        setCreativeJson(creativeJson);
        setReason(reason);
        setTemplateId(templateId);
        setAuditStatus(auditStatus);
        setStatus(status);
        setVersion(version);
        setIsDeleted(isDeleted);
        setCtime(ctime);
        setMtime(mtime);
        setOrderId(orderId);
        setSalesType(salesType);
        setCmMark(cmMark);
        setButtonCopy(buttonCopy);
        setCategoryFirstId(categoryFirstId);
        setCategorySecondId(categorySecondId);
        setTags(tags);
        setCreativeStatus(creativeStatus);
        setBeginTime(beginTime);
        setEndTime(endTime);
        setIsMark(isMark);
        setIsHistory(isHistory);
        setIsTag(isTag);
        setSchemeUrl(schemeUrl);
        setJumpType(jumpType);
        setBilibiliUserId(bilibiliUserId);
        setAdVersionControllId(adVersionControllId);
        setMgkPageId(mgkPageId);
        setAdMark(adMark);
        setModifyOfflineCreativeId(modifyOfflineCreativeId);
        setFlowWeightState(flowWeightState);
        setBusMarkId(busMarkId);
        setStyleAbility(styleAbility);
        setAdpVersion(adpVersion);
        setAutoAuditFlag(autoAuditFlag);
        setIsProgrammatic(isProgrammatic);
        setMaterialId(materialId);
        setTitleId(titleId);
        setIsNewFly(isNewFly);
        setProgAuditStatus(progAuditStatus);
        setProgMiscElemAuditStatus(progMiscElemAuditStatus);
        setPreferScene(preferScene);
        setTemplateGroupId(templateGroupId);
        setIsRecheck(isRecheck);
        setFlag(flag);
        setMaterialVideoId(materialVideoId);
        setUnderFrameAuditFlag(underFrameAuditFlag);
        setIsAutoFill(isAutoFill);
        setPromotionPurposeContentSecondary(promotionPurposeContentSecondary);
        setIsManaged(isManaged);
        setIsGdPlus(isGdPlus);
        setAdvertisingMode(advertisingMode);
        setIsMiddleAd(isMiddleAd);
        setIsVideoBind(isVideoBind);
        setTrackadf(trackadf);
    }

    /**
     * Create a detached, initialised BakLauUnitCreativeRecord
     */
    public BakLauUnitCreativeRecord(BakLauUnitCreativePo value) {
        super(TBakLauUnitCreative.BAK_LAU_UNIT_CREATIVE);

        if (value != null) {
            setCreativeId(value.getCreativeId());
            setAccountId(value.getAccountId());
            setCampaignId(value.getCampaignId());
            setCreativeType(value.getCreativeType());
            setUnitId(value.getUnitId());
            setCreativeName(value.getCreativeName());
            setPromotionPurposeContent(value.getPromotionPurposeContent());
            setCustomizedImpUrl(value.getCustomizedImpUrl());
            setCustomizedClickUrl(value.getCustomizedClickUrl());
            setTitle(value.getTitle());
            setDescription(value.getDescription());
            setExtDescription(value.getExtDescription());
            setImageUrl(value.getImageUrl());
            setImageMd5(value.getImageMd5());
            setVideoId(value.getVideoId());
            setVideoUrl(value.getVideoUrl());
            setExtImageUrl(value.getExtImageUrl());
            setExtImageMd5(value.getExtImageMd5());
            setCreativeJson(value.getCreativeJson());
            setReason(value.getReason());
            setTemplateId(value.getTemplateId());
            setAuditStatus(value.getAuditStatus());
            setStatus(value.getStatus());
            setVersion(value.getVersion());
            setIsDeleted(value.getIsDeleted());
            setCtime(value.getCtime());
            setMtime(value.getMtime());
            setOrderId(value.getOrderId());
            setSalesType(value.getSalesType());
            setCmMark(value.getCmMark());
            setButtonCopy(value.getButtonCopy());
            setCategoryFirstId(value.getCategoryFirstId());
            setCategorySecondId(value.getCategorySecondId());
            setTags(value.getTags());
            setCreativeStatus(value.getCreativeStatus());
            setBeginTime(value.getBeginTime());
            setEndTime(value.getEndTime());
            setIsMark(value.getIsMark());
            setIsHistory(value.getIsHistory());
            setIsTag(value.getIsTag());
            setSchemeUrl(value.getSchemeUrl());
            setJumpType(value.getJumpType());
            setBilibiliUserId(value.getBilibiliUserId());
            setAdVersionControllId(value.getAdVersionControllId());
            setMgkPageId(value.getMgkPageId());
            setAdMark(value.getAdMark());
            setModifyOfflineCreativeId(value.getModifyOfflineCreativeId());
            setFlowWeightState(value.getFlowWeightState());
            setBusMarkId(value.getBusMarkId());
            setStyleAbility(value.getStyleAbility());
            setAdpVersion(value.getAdpVersion());
            setAutoAuditFlag(value.getAutoAuditFlag());
            setIsProgrammatic(value.getIsProgrammatic());
            setMaterialId(value.getMaterialId());
            setTitleId(value.getTitleId());
            setIsNewFly(value.getIsNewFly());
            setProgAuditStatus(value.getProgAuditStatus());
            setProgMiscElemAuditStatus(value.getProgMiscElemAuditStatus());
            setPreferScene(value.getPreferScene());
            setTemplateGroupId(value.getTemplateGroupId());
            setIsRecheck(value.getIsRecheck());
            setFlag(value.getFlag());
            setMaterialVideoId(value.getMaterialVideoId());
            setUnderFrameAuditFlag(value.getUnderFrameAuditFlag());
            setIsAutoFill(value.getIsAutoFill());
            setPromotionPurposeContentSecondary(value.getPromotionPurposeContentSecondary());
            setIsManaged(value.getIsManaged());
            setIsGdPlus(value.getIsGdPlus());
            setAdvertisingMode(value.getAdvertisingMode());
            setIsMiddleAd(value.getIsMiddleAd());
            setIsVideoBind(value.getIsVideoBind());
            setTrackadf(value.getTrackadf());
        }
    }
}
