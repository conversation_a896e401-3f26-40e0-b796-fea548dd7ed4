/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnchorInfoBo {

    private Long id;
    private Long aid;
    private Integer type;
    private Integer clueType;
    private String name;
    private List<Integer> scenes;
    private List<String> sceneNames;

    private Integer auditStatus;
    private Integer status;
    private String reason;
    private Long mid;
    private String upNickname;
    private Integer accountId;
    private Integer customerId;
    private Integer agentId;
    private Integer campaignId;
    private Integer unitId;
    private Integer creativeId;

    private String mainTitle;
    private String subTitle;
    private String buttonText;
    /**
     * 白天图标和夜晚图标
     */
    private String underBoxDayIcon;
    private String storyDayIcon;
    private String iconTitle;

    private Integer conversionUrlType;
    private Long conversionUrlPageId;
    private String conversionUrl;

    private Integer isOpenMiniGame;
    private Integer miniGameId;
    private Integer gameBaseId;
    private Integer gamePlatformType;
    private Long biliMiniGameMid;
    private Integer iosUrlType;
    private Long iosUrlPageId;
    private String iosUrl;
    private Integer androidUrlType;
    private Long androidUrlPageId;
    private String androidUrl;
    private String iosSchemaUrl;
    private String androidSchemaUrl;
    private String iosButtonSchemaUrl;
    private String androidButtonSchemaUrl;

    private Integer iosAppPackageId;
    private Integer androidAppPackageId;
    private Integer subPkg;
    private String clueData;
    private String customizedImpUrl;
    private String customizedClickUrl;

    private String guideText;
    private String appDetailText;
    // APP标签
    private List<String> appLabels;
    // app详情类型: 1横图2竖图
    private Integer appDetailType;
    // 顶图
    private List<String> topImgUrls;
    // app 图片列表
    private List<String> appImgUrls;

    private Integer isDeleted;
    private Timestamp ctime;
    private Timestamp mtime;

    private String auditPerson;
    private Timestamp enterAuditTime;
    private Timestamp auditTime;
    private Integer wechatPackageId;
    private Integer isAndroidAppDirect;
    private Long formId;

    private Integer bizCode;

    private List<Integer> qualificationIds;

    private Integer auditType;

    private Integer groupId;
    private String title;
    private String groupName;
    private String biliAppletUrl;
    private Integer notShowInNatureFlow;
}
