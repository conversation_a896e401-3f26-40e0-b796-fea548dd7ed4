package com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm;

import com.bilibili.databus.base.Message;
import com.bilibili.databus.core.DataBusClient;
import com.bapis.ad.adp.archive.*;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.CmArchivePipelineReqBo;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.SaveArchiveReqBo;
import com.bilibili.sycpb.cpm.scv.app.service.bfs.BfsService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineServerCodes;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.ArchivePipelineStatus;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.WatermarkErasePipelineService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.WatermarkEraseConfiguration.AlgoVideoCoverConfig;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.WatermarkEraseConfiguration.WatermarkEraseConfig;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.CmArchiveContextBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.CmArchiveStage0CompleteEvent;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.WatermarkErasePipelineInfo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.WatermarkErasePipelineStage;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.CmArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.CmSpaceService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CoverBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.PreUploadBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.SaveArchiveRespBo;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmArchiveDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.service.http.algo.AlgoVideoCoverRemoteHttpService;
import com.bilibili.sycpb.cpm.scv.app.service.http.algo.WatermarkEraseRemoteHttpService;
import com.bilibili.sycpb.cpm.scv.app.service.http.algo.model.WatermarkEraseProgressResult;
import com.bilibili.sycpb.cpm.scv.app.service.http.algo.model.WatermarkEraseSubmitResult;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.PipelineUniqueIdUtil;
import com.bilibili.sycpb.cpm.scv.app.utils.VideoTools;
import com.bilibili.sycpb.cpm.scv.app.utils.VideoTools.FetchCoverResult;
import com.bilibili.sycpb.cpm.scv.app.utils.VideoTools.VideoDownloadResult;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.ArchiveMode;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.ArchivePipelineType;
import com.bilibili.sycpb.cpm.scv.app.utils.enums.ArchiveCoverOriginEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.awt.image.BufferedImage;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import pleiades.component.ecode.ServerCode;
import pleiades.venus.starter.rpc.client.RPCClient;

/**
 * 该流水线的调度不再基于本地线程池和内存，而是基于延迟消息队列
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CmArchivePipelineServiceWatermarkEraseImpl implements WatermarkErasePipelineService {

    private final CmArchivePipelineService taskDelegate;

    private final CmArchiveService probeDelegate;

    private final VideoTools videoTools;

    private final RedissonClient redissonClient;

    private final WatermarkEraseConfig watermarkEraseConfig;

    private final WatermarkEraseRemoteHttpService algoHttpService;

    private final WatermarkEraseJobRedisRepository eraseJobRedisRepository;

    private final MgkCmArchiveDaoService mgkCmArchiveDaoService;

    private final DataBusClient watermarkErasePub;

    private final CmSpaceService cmSpaceService;

    private final AlgoVideoCoverConfig algoVideoCoverConfig;

    private final AlgoVideoCoverRemoteHttpService algoVideoCoverRemoteHttpService;

    private final BfsService bfsService;

    private final AlgoVideoCoverLocalScheduleService algoVideoCoverLocalScheduleService;

    private final CmArchiveValidateService cmArchiveValidateService;

    @Value("${scv.archive.upload.max-size-kb:614400}")
    private Integer maxUploadArchiveSize;

    private final ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);


    @Override
    public String triggerCmArchivePipelineAsync(CmArchivePipelineReqBo req) {

        int archiveMode = NumberUtils.isPositive(req.getMid()) ? ArchiveMode.USER_SPACE : ArchiveMode.CM_SPACE;

        if (NumberUtils.isPositive(req.getMid())) {
            cmArchiveValidateService.checkMidAccountBiliAuthRelation(req.getAccountId(), req.getMid());
        }

        if (!Optional.ofNullable(req.getNeedEraseWatermark()).orElse(false)) {
            CmArchiveContextBo ctx = CmArchiveConverter.MAPPER.fromWebApiBo(req);
            ctx.setArchiveMode(archiveMode);
            ctx.setStage(0);
            ctx.setIsWatermarkErase(false);
            ctx.setIsUsingAlgoCover(true);
            return taskDelegate.triggerCmArchivePipelineAsync(ctx);
        }

        // 1. build
        String wmPipelineId = PipelineUniqueIdUtil.buildWatermarkErasePipelineUniqueId(archiveMode,
                req.getAccountId(), req.getVideoMd5());
        WatermarkErasePipelineInfo pipelineInfo = new WatermarkErasePipelineInfo()
                .setArchiveMode(archiveMode)
                .setPipelineId(wmPipelineId)
                .setStage(WatermarkErasePipelineStage.complete_submit)
                .setScheduleDelaySeconds(0)
                .setReq(CmArchiveConverter.MAPPER.reqBo2Info(req))
                .setAttempts(0)
                .setMaxAttempts(watermarkEraseConfig.getScheduleMaxAttempts());

        // 2. 插入record，用于分页展示，避免客户焦虑，水印清除特供的record，注意！这个数据不应用于后面的处理流程，仅用于page分页的展示，
        MgkCmArchiveRecord record = mgkCmArchiveDaoService.createIfNotExists(
                archiveMode, req.getAccountId(),
                archiveMode == ArchiveMode.CM_SPACE ? cmSpaceService.fetchMid(req.getAccountId()) : req.getMid(),
                videoTools.videoMd5ToDownloadUrl(req.getVideoMd5()),
                req.getVideoMd5(),
                wmPipelineId,
                null, null,
                160, 21,
                List.of("广告"),
                null, ArchivePipelineType.WATERMARK_ERASE, false);


        if (Optional.ofNullable(record.getIsDeleted()).orElse(0) == 1) {

            ServerCode erasingWatermarkCode = ArchivePipelineServerCodes.PROCESSING;
            // recover deleted
            record.setIsDeleted(0)
                    .setPipelineStatus(ArchivePipelineStatus.PROCESSING)
                    .setAuditStatus(erasingWatermarkCode.getCode())
                    .setAuditReason(erasingWatermarkCode.getMessage())
                    .store();
        }

        ServerCode erasingWatermarkCode = ArchivePipelineServerCodes.ERASING_WATERMARK;
        record.setPipelineStatus(ArchivePipelineStatus.PROCESSING)
                .setAuditStatus(erasingWatermarkCode.getCode())
                .setAuditReason(erasingWatermarkCode.getMessage())
                .store();


        // 3. 注册redis，真正的流水线信息保存的地方，之前的逻辑有点混用archive产物和流水线
        eraseJobRedisRepository.insertWatermarkErasePipelineInfo(pipelineInfo);

        // 4. schedule
        this.doScheduleWatermarkEraseJobNextRound(pipelineInfo);

        return wmPipelineId;

    }

    @Override
    public SaveArchiveRespBo saveArchive(SaveArchiveReqBo saveArchiveReqBo) {

        // 0.正常视频
        if (StringUtils.isEmpty(saveArchiveReqBo.getPipelineId()) ||
                PipelineUniqueIdUtil.pipelineType(saveArchiveReqBo.getPipelineId())
                        != PipelineUniqueIdUtil.WATERMARK_ERASE_PRE_PIPELINE) {

            return probeDelegate.saveArchive(saveArchiveReqBo);
        }

        // 1.水印视频
        WatermarkErasePipelineInfo pipeline = eraseJobRedisRepository
                .selectByPipelineId(saveArchiveReqBo.getPipelineId())
                .orElseThrow(() -> new RuntimeException(
                        "找不到对应的水印清除流水线信息[0], pipelineId=" + saveArchiveReqBo.getPipelineId()));

        // 更新wmpipeline记录的cover，没有实际意义，仅让客户乐一下
        Try.run(() -> this.doUpdateCoverOfWmPipelineRecord(saveArchiveReqBo.getPipelineId(), saveArchiveReqBo));



        // save 请求和stage0有一个先后问题，
        // （0）-> stage0 提交->(1)->  stage0 完成->(2)
        // save请求可以发生在
        if (StringUtils.isEmpty(pipeline.getRedirectPipelineId())) {

            // (0) 时刻， 仅寄存，等待完成
            pipeline.setSaveReq(saveArchiveReqBo);
            eraseJobRedisRepository.updateWatermarkErasePipelineInfo(pipeline);
            return new SaveArchiveRespBo();

        } else {

            // (1) 或者（2）
            return this.doUpdateCidAndSaveReqAndThenCheckDoSaveArchive(
                    pipeline.getRedirectPipelineId(), pipeline.getCid(), saveArchiveReqBo);
        }
    }


    /**
     * 轮询视频上传进度，直到获取封面，注意不需要整个流水线完结
     *
     * @param pipelineId
     * @return
     */
    @Override
    public PreUploadBo fetchPipelineStage0Result(String pipelineId) {

        // 1. 普通请求
        if (PipelineUniqueIdUtil.pipelineType(pipelineId) != PipelineUniqueIdUtil.WATERMARK_ERASE_PRE_PIPELINE) {
            return probeDelegate.fetchPipelineStage0Result(pipelineId);
        }

        // 1. 水印请求
        WatermarkErasePipelineInfo pipeline = eraseJobRedisRepository.selectByPipelineId(pipelineId)
                .orElseThrow(() -> {
                    return new IllegalArgumentException("找不到对应的水印清除流水线信息[2], pipelineId=" + pipelineId);
                });
        if (pipeline.getStage() == WatermarkErasePipelineStage.pipeline_error) {
            throw new IllegalArgumentException("水印清除流水线失败, " + pipeline.getErrMsg());
        }

        // 2. 图片未出
        if (pipeline.getStage().getCode() < WatermarkErasePipelineStage.complete_download_fetch_cover.getCode()) {
            return null;
        }

        return pipeline.toPreUploadBo();
    }

    @EventListener(CmArchiveStage0CompleteEvent.class)
    public void onNormalVideoPipelineStage0Completed(CmArchiveStage0CompleteEvent event) {

        // 是否可能save 和完成同时？
        Try.run(() -> {
            MgkCmArchiveRecord record = event.getArchiveRecord();

            this.doUpdateCidAndSaveReqAndThenCheckDoSaveArchive(
                    record.getUniqueId(), record.getCid(), null);


        }).onFailure(t -> {

            if (t instanceof IllegalArgumentException &&
                    t.getMessage() != null && t.getMessage().startsWith("找不到对应的水印清除流水线信息")) {
                return;
            }

            log.error("Fail to handle onNormalVideoPipelineStage0Completed, recordId={}",
                    event.getArchiveRecord().getId(), t);
        });

    }


    /**
     * 目前的流水线为线性的。
     * TODO 有一个优化点是提取封面时可以直接并行清除水印，暂时没有必要
     */
    @Override
    public void runWatermarkEraseJob(String msg) {

        log.info("Start to handle scheduleWatermarkEraseJob, msg={}", msg);

        WatermarkErasePipelineInfo pipeline = Try.of(
                        () -> {
                            WatermarkErasePipelineInfo decode = objectMapper.readValue(msg,
                                    WatermarkErasePipelineInfo.class);

                            // 由于saveReq的存在decode 的数据可能和最新的有一定的不一致， 直接reload算了，从这个角度说，调度信息中只有pipelineId是有意义的。先不做改动了
                            return eraseJobRedisRepository.selectByPipelineId(decode.getPipelineId())
                                    .orElse(decode);
                        })
                .getOrElseThrow(t -> {
                    log.error("Fail to deserialize WatermarkErasePipelineInfo, msg={}", msg, t);
                    throw new RuntimeException("Fail to deserialize WatermarkErasePipelineInfo", t);
                });

        Try.run(() -> {
            switch (pipeline.getStage()) {

                case complete_submit: {
                    this.doDownloadAndFetchCover(pipeline);
                    break;
                }
                // 此时封面存在，允许提交保存{@link saveReq}
                case complete_download_fetch_cover: {
                    this.doStartEraseWatermarkTask(pipeline);
                    break;
                }
                case erasing_watermark: {
                    this.doProbeEraseWatermarkTaskProgress(pipeline);
                    break;
                }
                case complete_erase_watermark: {
                    this.doSubmitToNormalCmArchivePipeline(pipeline);
                    break;
                }
                case complete_submit_normal_video_pipeline: {
                    // do nothing 静静等待普通流水先处理完成
                    break;
                }
                case pipeline_error:
                default: {

                    // nothing to do 处理这个状态的任务直接嫁接到了正常视频处理流水线，无需额外的处理

                    // drop it
                    break;
                }

            }
        }).onFailure(t -> {
            log.error("Fail to runWatermarkEraseJob,stage={}, id={}  pipeline={}", pipeline.getStage(),
                    pipeline.getPipelineId(), pipeline, t);
            pipeline.setAttempts(pipeline.getAttempts() + 1);
            // attempts ++

            // maxAttemps 表示总共三次， 重试2次
            if (pipeline.getAttempts() >= pipeline.getMaxAttempts()) {

                // 不在允许重试，
                String errMsg = String.format("调度执行水印清除任务失败，当前阶段:%s, 失败原因:%s ",
                        pipeline.getStage().name(), t.getMessage());

                pipeline.setStage(WatermarkErasePipelineStage.pipeline_error)
                        .setErrMsg(errMsg);

                this.doUpdateFailureStatusOfWmPipelineRecord(pipeline);

            } else {
                // 什么都不做，以当前的间隔，等待下次调度
                log.warn("Retry allowed, wait for next round schedule, pipeline={}", pipeline);
            }

        });


        eraseJobRedisRepository.updateWatermarkErasePipelineInfo(pipeline);

        this.doScheduleWatermarkEraseJobNextRound(pipeline);

    }


    private void doUpdateCoverOfWmPipelineRecord(String pipelineId, SaveArchiveReqBo saveArchiveReqBo) {

        final var record = mgkCmArchiveDaoService.fetchExistingRecord(pipelineId);

        Assert.notNull(record, "找不到对应的稿件数据");
        final var cover = saveArchiveReqBo.getCover();
        if (Objects.nonNull(cover) && org.springframework.util.StringUtils.hasText(cover.getUrl())) {
            Assert.isTrue(org.springframework.util.StringUtils.hasText(cover.getMd5()), "封面md5不能为空");
            record.setCoverUrl(cover.getUrl())
                    .setCoverMd5(cover.getMd5())
                    .setCoverOrigin(ArchiveCoverOriginEnum.CUSTOM.getCode());
        }

        CmArchivePipelineService.validateTitle(saveArchiveReqBo.getTitle());
        record.setTitle(saveArchiveReqBo.getTitle());
        record.store();
    }

    /**
     *
     */
    private SaveArchiveRespBo doUpdateCidAndSaveReqAndThenCheckDoSaveArchive(
            String normalPipelineId, Long cid, SaveArchiveReqBo saveArchiveReqBo) {

        RLock lock = redissonClient.getLock(
                String.format(watermarkEraseConfig.getWatermarkEraseLockRedisKeyTpl(), normalPipelineId));

        if (Try.of(() -> lock.tryLock(1000, 1000, TimeUnit.MILLISECONDS)).getOrElse(false)) {

            WatermarkErasePipelineInfo pipelineInfo = null;
            try {

                pipelineInfo = eraseJobRedisRepository.selectByPipelineId(normalPipelineId)
                        .orElseThrow(() -> {
                            throw new IllegalArgumentException(
                                    "找不到对应的水印清除流水线信息[1], pipelineId=" + normalPipelineId);
                        });

                if (cid != null) {

                    pipelineInfo.setCid(cid);
                }
                if (saveArchiveReqBo != null) {

                    pipelineInfo.setSaveReq(saveArchiveReqBo);

                }

                eraseJobRedisRepository.updateWatermarkErasePipelineInfo(pipelineInfo);
            } finally {
                lock.unlock();
            }

            if (pipelineInfo.getSaveReq() != null && pipelineInfo.getCid() != null) {

                SaveArchiveReqBo saveReq = pipelineInfo.getSaveReq();
                saveReq.setCid(pipelineInfo.getCid());

                SaveArchiveRespBo rsp = probeDelegate.saveArchive(saveReq);
                log.info("Success to save watermark erased archive, pipelineId={}, saveArchiveRespBo={}",
                        pipelineInfo.getPipelineId(), rsp);

                return rsp;
            } else {
                return new SaveArchiveRespBo();
            }

        } else {
            log.error("Fail to acquire lock, pipelineId={}", normalPipelineId);
            throw new RuntimeException("Fail to acquire lock, pipelineId=" + normalPipelineId);
        }


    }


    private void doScheduleWatermarkEraseJobNextRound(WatermarkErasePipelineInfo pipelineInfo) {

        if (pipelineInfo.getStage().getCode()
                >= WatermarkErasePipelineStage.complete_submit_normal_video_pipeline.getCode()) {
            log.info("WatermarkErasePipelineInfo is already done, pipelineId={} , skip schedule ", pipelineInfo);
            return;
        }

        String msg = Try.of(() -> objectMapper.writeValueAsString(pipelineInfo))
                .getOrElseThrow(t -> {
                    log.error("Fail to serialize WatermarkErasePipelineInfo, pipelineId={}",
                            pipelineInfo.getPipelineId());
                    throw new RuntimeException("Fail to serialize WatermarkErasePipelineInfo");
                });
        // 使用databus延迟队列进行调度；
        Message message = new Message(pipelineInfo.getPipelineId(), msg.getBytes(),
                new HashMap<>());
        if (pipelineInfo.getScheduleDelaySeconds() > 0) {
            message.setDelayAfter(Duration.of(pipelineInfo.getScheduleDelaySeconds(), ChronoUnit.SECONDS));
        }

        Try.run(() -> watermarkErasePub.pub(message)
        ).onFailure(t -> {
            log.error("img2imgProgressSchedulePub pub error", t);
        }).onSuccess(r -> {
            log.info("Success to probeTaskProgressBackgroundAtFixDelay, record={}, taskId={}", pipelineInfo,
                    pipelineInfo.getEraseTaskId());
        });

    }




    /**
     * watermark-earse pipeline stage handler
     *
     * @param pipeline
     */
    private void doSubmitToNormalCmArchivePipeline(WatermarkErasePipelineInfo pipeline) {

        // TODO 要改成基于erasedVideoUrl驱动
        CmArchivePipelineReqBo req = CmArchiveConverter.MAPPER.toReqBo(pipeline.getReq());

        final var ctx = CmArchiveConverter.MAPPER.fromWebApiBo(req);

        ctx.setArchiveMode(pipeline.getArchiveMode());
        ctx.setStage(0);
        ctx.setVideoUrl(pipeline.getErasedVideoUrl());
        ctx.setVideoMd5(pipeline.getErasedVideoMd5());
        ctx.setSpecificIsDownloadFromMd5(false);
        ctx.setIsWatermarkErase(true);
        // 取否是因为，水印梳理后视频的上传，使用水印处理前已经选择的视频封面，无需额外再进行选择
        ctx.setIsUsingAlgoCover(false);

        Try.of(() -> {
            // 插入redirect数据

            String normalPipelineId = taskDelegate.triggerCmArchivePipelineAsync(ctx);


            pipeline.setRedirectPipelineId(normalPipelineId)
                    .setStage(WatermarkErasePipelineStage.complete_submit_normal_video_pipeline);

            // 不能直接save而是将其寄存到normalPipelineId的redis中，等待后续普通流水线执行完后执行save，而且与是否有save请求无关，一律需要保存
            WatermarkErasePipelineInfo newNormalPipelineInfo = new WatermarkErasePipelineInfo();
            BeanUtils.copyProperties(pipeline, newNormalPipelineInfo);
            newNormalPipelineInfo.setPipelineId(normalPipelineId);
            eraseJobRedisRepository.insertWatermarkErasePipelineInfo(newNormalPipelineInfo);
            log.info("Success to copy watermark pipeline info as normal one, pipelineId={}, normalPipelineId={}",
                    pipeline.getPipelineId(), normalPipelineId);

            return normalPipelineId;
        }).map(normalPipelineId -> {

            // 如果用户已经更新过封面，那么先提前更新下封面，避免用户困惑
            if (pipeline.getSaveReq() != null) {
                this.doUpdateCoverOfWmPipelineRecord(
                        normalPipelineId, pipeline.getSaveReq());
            }
            return normalPipelineId;
        }).map(normalPipelineId -> {

            // 删除就的水印擦除数据，避免用户困惑

            // 此时短暂有两条记录，将老的进行删去
            MgkCmArchiveRecord existedWatermarkEraseRecord = mgkCmArchiveDaoService.fetchExistingRecord(
                    pipeline.getPipelineId());
            ServerCode watermarkCode = ArchivePipelineServerCodes.SUCCESS_TO_ERASE_WATERMARK;

            if (existedWatermarkEraseRecord != null) {
                existedWatermarkEraseRecord.setPipelineStatus(ArchivePipelineStatus.FAILED)
                        .setAuditStatus(watermarkCode.getCode())
                        .setAuditReason(watermarkCode.getMessage())
                        .setIsDeleted(1)
                        .store();
            }
            return normalPipelineId;
        }).getOrElseGet(t -> {

            // 无论哪种失败都尝试重试，且调整重试间隔目前为0，调整为3，
            pipeline.setScheduleDelaySeconds(watermarkEraseConfig.getEraseTaskProgressProbeIntervalSeconds());

            if (t instanceof RuntimeException && "当前无可用资源, 请稍后重试".equals(t.getMessage())) {

                // 只针对线程池资源不足的情况, 增加最大重试次数，延长等待时间
                pipeline.setMaxAttempts(watermarkEraseConfig.getSubmitToNormalPipelineMaxAttempts());

            }
            throw new RuntimeException("提交到普通流水线失败: " + t.getMessage(), t);

        });




    }

    private void doProbeEraseWatermarkTaskProgress(WatermarkErasePipelineInfo pipeline) {

        Try.run(() -> {

            WatermarkEraseProgressResult progress = algoHttpService.progress(
                    pipeline.getEraseTaskId(), pipeline.getAlgoHttpHost());

            if (progress == null || progress.isFailed()) {
                throw new RuntimeException(
                        "轮询算法水印清除任务进度失败:" +
                                Optional.ofNullable(progress).map(WatermarkEraseProgressResult::getDetail).orElse(""));
            } else if (progress.isSuccess()) {

                pipeline.setStage(WatermarkErasePipelineStage.complete_erase_watermark)
                        .setScheduleDelaySeconds(0)
                        .setErasedVideoMd5(progress.getResults().getMd5())
                        .setErasedVideoUrl(progress.getResults().getUrl())
                ;
            } else {

                // 继续轮询 ... to be continue
                pipeline.setStage(WatermarkErasePipelineStage.erasing_watermark)
                        .setScheduleDelaySeconds(watermarkEraseConfig.getEraseTaskProgressProbeIntervalSeconds());
            }


        }).getOrElseThrow(t -> {

            throw new RuntimeException("轮询算法水印清除任务进度失败: " + t.getMessage(), t);

        });


    }


    private void doStartEraseWatermarkTask(WatermarkErasePipelineInfo pipeline) {

        Try.of(() -> {

            WatermarkEraseSubmitResult submitRest = algoHttpService.submit(

                    videoTools.videoMd5ToDownloadUrl(pipeline.getReq().getVideoMd5())
            );

            if (submitRest == null || StringUtils.isEmpty(submitRest.getTaskId())) {
                throw new RuntimeException(
                        "请求算法水印清除任务失败:" + Optional.ofNullable(submitRest.getMsg()).orElse(""));
            }

            return submitRest;


        }).onSuccess(task -> {

            pipeline.setEraseTaskId(task.getTaskId())
                    .setAlgoHttpHost(task.getHost())
                    .setStage(WatermarkErasePipelineStage.erasing_watermark)
                    .setScheduleDelaySeconds(watermarkEraseConfig.getEraseTaskProgressProbeIntervalSeconds());
        }).getOrElseThrow(t -> {
            return new RuntimeException("请求算法水印清除任务失败: " + t.getMessage(), t);
        });

    }

    private void doUpdateFailureStatusOfWmPipelineRecord(WatermarkErasePipelineInfo pipelineInfo) {

        if (pipelineInfo.getStage() != WatermarkErasePipelineStage.pipeline_error) {
            return;
        }

        log.error("Fail to handle watermark erase pipeline, pipeline={}, errMsg={}",
                pipelineInfo, pipelineInfo.getErrMsg());

        // 仅用于更新错误状态
        final var record = mgkCmArchiveDaoService.fetchExistingRecord(pipelineInfo.getPipelineId());
        Assert.notNull(record, "找不到对应的稿件数据");

        ServerCode watermarkFailedCode = ArchivePipelineServerCodes.FAIL_TO_ERASE_WATERMARK;

        record.setPipelineStatus(ArchivePipelineStatus.FAILED)
                .setAuditStatus(watermarkFailedCode.getCode())
                .setAuditReason(watermarkFailedCode.getMessage())
                .store();


    }

    private void doDownloadAndFetchCover(

            WatermarkErasePipelineInfo pipelineInfo
    ) {

        CompletableFuture<CoverBo> future = algoVideoCoverLocalScheduleService.schedule(
                videoTools.videoMd5ToDownloadUrl(pipelineInfo.getReq().getVideoMd5()),
                pipelineInfo.getReq().getAccountId());

        CmArchivePipelineReqBo req = pipelineInfo.getReq();

        boolean downloadFromMd5 = true;

        VideoDownloadResult download = videoTools.download(
                downloadFromMd5,
                req.getVideoMd5(),
                null,
                "",
                maxUploadArchiveSize
        );

        FetchCoverResult cover = videoTools.fetchFirstFrameAsCover(

                null, downloadFromMd5,
                req.getVideoMd5(), null,
                req.getVideoFileName(),
                download.getLocalStorageBo(),
                null);


        Tuple2<Integer, Integer> widthHeight = Try.of(() -> {

            BufferedImage image = videoTools.readFirstFrame(download.getLocalStorageBo());
            return Tuple.of(image.getWidth(), image.getHeight());

        }).onFailure(t -> {
            log.error("Fail to fetch video width and height, coverUrl={}, video={}",
                    cover.getCoverUrl(), req.getVideoMd5());
        }).getOrElse(Tuple.of(null, null));

        pipelineInfo.setCover(CoverBo.builder()
                        .url(cover.getCoverUrl())
                        .md5(cover.getCoverMd5())
                        .source(cover.getCoverOrigin())
                        .build())
                .setWidth(widthHeight._1)
                .setHeight(widthHeight._2)
                .setStage(WatermarkErasePipelineStage.complete_download_fetch_cover);

        CoverBo algoCover = Try.of(
                () -> future.get(algoVideoCoverConfig.getAlgoCoverWaitTimeoutSeconds(), TimeUnit.SECONDS)).get();

        // 使用的原视频推荐封面
        pipelineInfo.setAlgoCoverUrl(algoCover.getUrl())
                .setAlgoCoverMd5(algoCover.getMd5());


        // todo save cover

        MgkCmArchiveRecord record = mgkCmArchiveDaoService.fetchExistingRecord(pipelineInfo.getPipelineId());

        record.setCoverUrl(cover.getCoverUrl())
                .setCoverMd5(cover.getCoverMd5())
                .setPipelineStage(CmArchivePipelineStage.FETCH_COVER)
                .setCoverOrigin(cover.getCoverOrigin())
                .store();


    }






}
