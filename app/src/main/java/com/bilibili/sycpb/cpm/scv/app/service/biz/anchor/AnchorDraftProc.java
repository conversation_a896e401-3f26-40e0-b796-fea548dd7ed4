/*
 * Copyright (c) 2015-2024 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.anchor;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.component.CommentComponentAuditStatus;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos.AnchorAuditStatusBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos.AnchorDraftBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos.AnchorInfoBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos.AnchorUpdateBo;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveAnchorPointDraftRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveAnchorPointOtherInfoRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveAnchorPointRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.repo.LauArchiveAnchorPointRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/17 18:22
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AnchorDraftProc {

    private final LauArchiveAnchorPointRepo lauArchiveAnchorPointRepo;

    /**
     * 判断推审情况
     * 场景和名称不需要推审，其他核心字段都需要
     *
     * @param updateBo
     * @param anchorPointRecord
     * @return
     */
    public AnchorAuditStatusBo judgeAuditStatus(AnchorUpdateBo updateBo, AnchorUpdateBo anchorPointRecord) {
        AnchorAuditStatusBo anchorAuditStatusBo = AnchorAuditStatusBo.builder()
            .id(updateBo.getId())
            .aid(updateBo.getAid())
            .isNeedPushAudit(false).build();
        // 锚点如果是待审核，不变
        if (Objects.equals(CommentComponentAuditStatus.AUDITING_VALUE, anchorPointRecord.getAuditStatus())) {
            // 两个状态仍然都是待审核
            anchorAuditStatusBo.setNeedPushAudit(true);
            anchorAuditStatusBo.setToAuditStatus(CommentComponentAuditStatus.AUDITING_VALUE);
            anchorAuditStatusBo.setToStatus(CommentComponentAuditStatus.AUDITING_VALUE);
            return anchorAuditStatusBo;
        }

        // 锚点如果是驳回，变成待审核
        if (Objects.equals(CommentComponentAuditStatus.AUDIT_REJECTED_VALUE, anchorPointRecord.getAuditStatus())) {
            // 两个状态都变成待审核
            anchorAuditStatusBo.setNeedPushAudit(true);
            anchorAuditStatusBo.setToAuditStatus(CommentComponentAuditStatus.AUDITING_VALUE);
            anchorAuditStatusBo.setToStatus(CommentComponentAuditStatus.AUDITING_VALUE);
            return anchorAuditStatusBo;
        }

        // 修改场景不触审
        // 锚点如果是审核通过，触审字段修改，则变成待审核；否则不变
        Boolean isMainTitleChanged = !Objects.equals(updateBo.getMainTitle(), anchorPointRecord.getMainTitle());
        Boolean isSubTitleChanged = !Objects.equals(updateBo.getSubTitle(), anchorPointRecord.getSubTitle());
        Boolean isButtonChanged = !Objects.equals(updateBo.getButtonText(), anchorPointRecord.getButtonText());
//        Boolean isCustomizedImpUrlChanged = !Objects.equals(updateBo.getCustomizedImpUrl(), anchorPointRecord.getCustomizedImpUrl());
//        Boolean isCustomizedClickUrlChanged = !Objects.equals(updateBo.getCustomizedClickUrl(), anchorPointRecord.getCustomizedClickUrl());
        Boolean isClueTypeChanged = !Objects.equals(updateBo.getClueType(), anchorPointRecord.getClueType());
        Boolean isConUrlPageIdChanged = !Objects.equals(updateBo.getConversionUrlPageId(), anchorPointRecord.getConversionUrlPageId());
        Boolean isConUrlChanged = !Objects.equals(updateBo.getConversionUrl(), anchorPointRecord.getConversionUrl());
        Boolean isAndroidAppPackageIdChanged = !Objects.equals(updateBo.getAndroidAppPackageId(), anchorPointRecord.getAndroidAppPackageId());
        Boolean isIosAppPackageIdChanged = !Objects.equals(updateBo.getIosAppPackageId(), anchorPointRecord.getIosAppPackageId());
        Boolean isGameBaseIdChanged = !Objects.equals(updateBo.getGameBaseId(), anchorPointRecord.getGameBaseId());
        Boolean isMiniGameIdChanged = !Objects.equals(updateBo.getMiniGameId(), anchorPointRecord.getMiniGameId());
        Boolean isOpenMiniGameChanged = !Objects.equals(updateBo.getIsOpenMiniGame(), anchorPointRecord.getIsOpenMiniGame());
        Boolean isAndroidAppDirect = !Objects.equals(updateBo.getIsAndroidAppDirect(), anchorPointRecord.getIsAndroidAppDirect());
        Boolean isGuideTextChanged = !Objects.equals(updateBo.getGuideText(), anchorPointRecord.getGuideText());
        Boolean isAppDetailTextChanged = !Objects.equals(updateBo.getAppDetailText(), anchorPointRecord.getAppDetailText());
        Boolean isAppDetailTypeChanged = !Objects.equals(updateBo.getAppDetailType(), anchorPointRecord.getAppDetailType());
        Boolean isLabelsChanged = isListChanged(updateBo.getAppLabels(), anchorPointRecord.getAppLabels());
        Boolean isTopImgsChanged = isListChanged(updateBo.getTopImgUrls(), anchorPointRecord.getTopImgUrls());
        Boolean isAppImgsChanged = isListChanged(updateBo.getAppImgUrls(), anchorPointRecord.getAppImgUrls());
        Boolean isSchemaUrlChanged = !Objects.equals(updateBo.getAndroidSchemaUrl(), anchorPointRecord.getAndroidSchemaUrl());

        if (isMainTitleChanged || isSubTitleChanged || isButtonChanged || isClueTypeChanged || isConUrlPageIdChanged || isConUrlChanged || isAndroidAppPackageIdChanged || isIosAppPackageIdChanged
                || isGameBaseIdChanged || isMiniGameIdChanged || isOpenMiniGameChanged || isAndroidAppDirect || isGuideTextChanged || isAppDetailTextChanged || isAppDetailTypeChanged || isLabelsChanged || isTopImgsChanged || isAppImgsChanged
                || isSchemaUrlChanged) {
            // auditStatus 仍然是审核通过，status 变成待审核
            anchorAuditStatusBo.setNeedPushAudit(true);
            anchorAuditStatusBo.setToAuditStatus(CommentComponentAuditStatus.AUDIT_PASSED_VALUE);
            anchorAuditStatusBo.setToStatus(CommentComponentAuditStatus.AUDITING_VALUE);
            return anchorAuditStatusBo;
        }

        // 两个状态仍然审核通过
        anchorAuditStatusBo.setToAuditStatus(CommentComponentAuditStatus.AUDIT_PASSED_VALUE);
        anchorAuditStatusBo.setToStatus(CommentComponentAuditStatus.AUDIT_PASSED_VALUE);
        return anchorAuditStatusBo;
    }

    public Boolean isListChanged(List<String> newList, List<String> oldList) {

        // 都为空
        if (newList == null && oldList == null) {
            return false;
        }
        if (newList == null && oldList != null) {
            return true;
        }
        if (newList != null && oldList == null) {
            return true;
        }

        Set<String> set1 = newList.stream().collect(Collectors.toSet());
        Set<String> set2 = oldList.stream().collect(Collectors.toSet());
        return set1.equals(set2);
    }

    public void replacePartFieldsFromDraft(List<AnchorInfoBo> anchorInfoBos) {
        List<Long> anchorIds = anchorInfoBos.stream().map(anchor -> anchor.getId()).collect(Collectors.toList());
        Map<Long, LauArchiveAnchorPointDraftRecord> draftMap = lauArchiveAnchorPointRepo.queryAnchorDraftMap(anchorIds);
        for (AnchorInfoBo anchorInfoBo : anchorInfoBos) {
            LauArchiveAnchorPointDraftRecord draftRecord = draftMap.get(anchorInfoBo.getId());
            replacePartFields(draftRecord, anchorInfoBo);
        }
    }

    public void replacePartFields(LauArchiveAnchorPointDraftRecord draftRecord, AnchorInfoBo anchorInfoBo) {

        // 草稿内容替换
        if (draftRecord != null && !StringUtils.isEmpty(draftRecord.getContent())) {
            AnchorDraftBo anchorDraftBo = JSON.parseObject(draftRecord.getContent(), AnchorDraftBo.class);

            updateIfNotNull(anchorDraftBo.getClueType(), anchorInfoBo::setClueType);
            updateIfNotNull(anchorDraftBo.getName(), anchorInfoBo::setName);
            anchorInfoBo.setScenes(anchorDraftBo.getScenes());

            updateIfNotNull(anchorDraftBo.getMainTitle(), anchorInfoBo::setMainTitle);
            updateIfNotNull(anchorDraftBo.getSubTitle(), anchorInfoBo::setSubTitle);
            updateIfNotNull(anchorDraftBo.getButtonText(), anchorInfoBo::setButtonText);
            updateIfNotNull(anchorDraftBo.getIosUrlType(), anchorInfoBo::setIosUrlType);
            updateIfNotNull(anchorDraftBo.getIosUrlPageId(), anchorInfoBo::setIosUrlPageId);
            updateIfNotNull(anchorDraftBo.getIosUrl(), anchorInfoBo::setIosUrl);
            updateIfNotNull(anchorDraftBo.getAndroidUrlType(), anchorInfoBo::setAndroidUrlType);
            updateIfNotNull(anchorDraftBo.getAndroidUrlPageId(), anchorInfoBo::setAndroidUrlPageId);
            updateIfNotNull(anchorDraftBo.getAndroidUrl(), anchorInfoBo::setAndroidUrl);
            updateIfNotNull(anchorDraftBo.getConversionUrlType(), anchorInfoBo::setConversionUrlType);
            updateIfNotNull(anchorDraftBo.getConversionUrlPageId(), anchorInfoBo::setConversionUrlPageId);
            updateIfNotNull(anchorDraftBo.getConversionUrl(), anchorInfoBo::setConversionUrl);
            updateIfNotNull(anchorDraftBo.getIosSchemaUrl(), anchorInfoBo::setIosSchemaUrl);
            updateIfNotNull(anchorDraftBo.getAndroidSchemaUrl(), anchorInfoBo::setAndroidSchemaUrl);
            updateIfNotNull(anchorDraftBo.getIosButtonSchemaUrl(), anchorInfoBo::setIosButtonSchemaUrl);
            updateIfNotNull(anchorDraftBo.getAndroidButtonSchemaUrl(), anchorInfoBo::setAndroidButtonSchemaUrl);
            updateIfNotNull(anchorDraftBo.getIsOpenMiniGame(), anchorInfoBo::setIsOpenMiniGame);
            updateIfNotNull(anchorDraftBo.getMiniGameId(), anchorInfoBo::setMiniGameId);
            updateIfNotNull(anchorDraftBo.getGameBaseId(), anchorInfoBo::setGameBaseId);
            updateIfNotNull(anchorDraftBo.getGamePlatformType(), anchorInfoBo::setGamePlatformType);
            updateIfNotNull(anchorDraftBo.getIosAppPackageId(), anchorInfoBo::setIosAppPackageId);
            updateIfNotNull(anchorDraftBo.getAndroidAppPackageId(), anchorInfoBo::setAndroidAppPackageId);
            updateIfNotNull(anchorDraftBo.getSubPkg(), anchorInfoBo::setSubPkg);
            updateIfNotNull(anchorDraftBo.getWechatPackageId(), anchorInfoBo::setWechatPackageId);
            updateIfNotNull(anchorDraftBo.getFormId(), anchorInfoBo::setFormId);
            updateIfNotNull(anchorDraftBo.getGuideText(), anchorInfoBo::setGuideText);
            updateIfNotNull(anchorDraftBo.getAppDetailText(), anchorInfoBo::setAppDetailText);
            updateIfNotNull(anchorDraftBo.getAppDetailType(), anchorInfoBo::setAppDetailType);

            updateIfCollectionNotEmpty(anchorDraftBo.getAppLabels(), anchorInfoBo::setAppLabels);
            updateIfCollectionNotEmpty(anchorDraftBo.getTopImgUrls(), anchorInfoBo::setTopImgUrls);
            updateIfCollectionNotEmpty(anchorDraftBo.getAppImgUrls(), anchorInfoBo::setAppImgUrls);

            updateIfNotNull(anchorDraftBo.getIsAndroidAppDirect(), anchorInfoBo::setIsAndroidAppDirect);
            updateIfNotNull(anchorDraftBo.getBiliMiniGameMid(), anchorInfoBo::setBiliMiniGameMid);
            updateIfNotNull(anchorDraftBo.getGroupId(), anchorInfoBo::setGroupId);
            updateIfNotNull(anchorDraftBo.getBiliAppletUrl(), anchorInfoBo::setBiliAppletUrl);
            updateIfNotNull(anchorDraftBo.getNotShowInNatureFlow(), anchorInfoBo::setNotShowInNatureFlow);
        }
    }

    /**
     * 如果值不为空，则执行更新操作
     *
     * @param value       要设置的值
     * @param setter      设置值的方法
     * @param <T>         值的类型
     */
    public static <T> void updateIfNotNull(T value, Consumer<T> setter) {
        Optional.ofNullable(value).ifPresent(setter);
    }

    /**
     * 如果集合不为空，则执行更新操作
     *
     * @param collection  要设置的集合
     * @param setter      设置集合的方法
     * @param <T>         集合中元素的类型
     */
    public static <T> void updateIfCollectionNotEmpty(List<T> collection, Consumer<List<T>> setter) {
        if (collection != null && !collection.isEmpty()) {
            setter.accept(collection);
        }
    }

}
