package com.bilibili.sycpb.cpm.scv.app.service.xxl.tempus_jobs;

import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.AdDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.LaunchArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveRecord;
import com.bilibili.sycpb.cpm.scv.app.service.rpc.ArchiveRpcService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.Tables.LAU_ARCHIVE;

/**
 * 刷新稿件任务
 * <AUTHOR>
 */
@Slf4j
@Service
public class RefreshArchiveTempusJob implements BasicProcessor {
    
    public static final String ID = "RefreshArchiveTempusJob";
    private final DSLContext ad;
    private final LaunchArchiveService launchArchiveService;
    private final ArchiveRpcService archiveRpcService;

    public RefreshArchiveTempusJob(@Qualifier(AdDataSourceConfig.AD_DSL_CONTEXT) DSLContext ad, 
                                   LaunchArchiveService launchArchiveService, 
                                   ArchiveRpcService archiveRpcService) {
        this.ad = ad;
        this.launchArchiveService = launchArchiveService;
        this.archiveRpcService = archiveRpcService;
    }

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger logger = context.getOmsLogger();
        String jobParams = Optional.ofNullable(context.getJobParams()).orElse("");
        
        logger.info("RefreshArchiveTempusJob 开始执行, 参数: {}", jobParams);
        log.info("RefreshArchiveTempusJob 开始执行, 参数: {}", jobParams);
        
        try {
            // 5分钟执行一次 一次1w 一共200w 每天更新一次
            long limit = 10000;
            if(StringUtils.isNotBlank(jobParams)) {
                limit = Long.parseLong(jobParams);
                logger.info("使用参数指定的限制数量: {}", limit);
            } else {
                logger.info("使用默认限制数量: {}", limit);
            }
            
            // 获取分片信息 (PowerJob 中可以通过 context 获取分片信息)
            int shardIndex = context.getSubTask() != null ? context.getSubTask().getSubTaskId().intValue() % 10 : 0;
            int shardTotal = 10; // 假设分10片
            
            logger.info("分片信息 - total: {}, index: {}", shardTotal, shardIndex);
            
            //这一批需要处理的记录
            final var lauArcRecords = ad
                .select()
                .from(LAU_ARCHIVE)
                .where(LAU_ARCHIVE.MTIME.lessOrEqual(Timestamp.valueOf(LocalDateTime.of(LocalDate.now(), LocalTime.MIDNIGHT))))
                .limit(limit)
                .fetchInto(LauArchiveRecord.class);
                
            logger.info("查询到的记录总数: {}", lauArcRecords.size());
            
            final var sharding = lauArcRecords
                .stream()
                .filter(record -> record.get(LAU_ARCHIVE.ID) % shardTotal == shardIndex)
                .collect(Collectors.toList());
                
            logger.info("分片后的记录数: {}", sharding.size());
            log.info("分片后的记录数: {}", sharding.size());
            
            final var partition = Lists.partition(sharding, 100);
            int processedBatches = 0;
            int totalProcessed = 0;
            
            for (var list : partition) {
                final var start = System.currentTimeMillis();
                var aidRecordMap = list
                    .stream()
                    .collect(Collectors.toMap(record -> record.get(LAU_ARCHIVE.AID), Function.identity()));
                    
                var arcsMap = archiveRpcService.arcsReply(aidRecordMap.keySet()).getArcsMap();
                final var arcRecords = arcsMap
                    .values()
                    .stream()
                    .map(arc -> launchArchiveService.insertOrUpdateLauArchive(arc, aidRecordMap.get(arc.getAid())))
                    .collect(Collectors.toList());
                    
                ad.batchStore(arcRecords).execute();
                final var end = System.currentTimeMillis();
                
                processedBatches++;
                totalProcessed += list.size();
                
                logger.info("处理第 {} 批, 耗时: {} ms, 处理记录数: {}", processedBatches, (end - start), list.size());
                if (processedBatches % 10 == 0) {
                    log.info("已处理 {} 批, 累计处理: {} 条记录", processedBatches, totalProcessed);
                }
            }
            
            logger.info("RefreshArchiveTempusJob 执行成功, 总共处理 {} 批, {} 条记录", processedBatches, totalProcessed);
            log.info("RefreshArchiveTempusJob 执行成功, 总共处理 {} 批, {} 条记录", processedBatches, totalProcessed);
            return new ProcessResult(true, "success");
        } catch (Throwable t) {
            logger.error("RefreshArchiveTempusJob 执行失败", t);
            log.error("RefreshArchiveTempusJob 执行失败", t);
            return new ProcessResult(false, "执行失败: " + t.getMessage());
        }
    }
}
