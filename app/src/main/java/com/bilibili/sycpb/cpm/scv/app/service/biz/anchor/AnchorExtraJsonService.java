package com.bilibili.sycpb.cpm.scv.app.service.biz.anchor;

import com.bapis.ad.component.BizCodeEnum;
import com.bapis.ad.component.ComponentType;
import com.bapis.ad.scv.anchor.AnchorType;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos.AnchorExtraJsonBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos.AnchorUpdateBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos.AnchorsAddBo;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.bos.CommentComponentExtraJsonBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class AnchorExtraJsonService {

    public AnchorExtraJsonBo generateCommentComponentExtraJsonBo(AnchorUpdateBo bo) {

        if (Objects.equals(bo.getType(), AnchorType.BILI_MINI_GAME_VALUE)) {
            return AnchorExtraJsonBo.builder()
                    .biliMiniGameMid(bo.getBiliMiniGameMid())
                    .notShowInNatureFlow(bo.getNotShowInNatureFlow())
                    .build();
        }

        if (Objects.equals(bo.getType(), AnchorType.BILI_APPLET_VALUE)) {
            return AnchorExtraJsonBo.builder()
                    .biliAppletUrl(bo.getBiliAppletUrl())
                    .notShowInNatureFlow(bo.getNotShowInNatureFlow())
                    .build();
        }
        return AnchorExtraJsonBo.builder()
                .notShowInNatureFlow(bo.getNotShowInNatureFlow())
                .build();
    }

    public AnchorExtraJsonBo generateCommentComponentExtraJsonBo(AnchorsAddBo anchorAddBo) {
        Integer anchorType = anchorAddBo.getType();
        Long biliMiniGameMid = anchorAddBo.getBiliMiniGameMid();
        if (anchorAddBo.getBizCode().equals(BizCodeEnum.COMMERCIAL_ANCHOR.getNumber())) {
            return AnchorExtraJsonBo.builder()
                    .biliMiniGameMid(biliMiniGameMid)
                    .osTarget(anchorAddBo.getOsTarget())
                    .ageTarget(anchorAddBo.getAgeTarget())
                    .biliAppletUrl(anchorAddBo.getBiliAppletUrl())
                    .notShowInNatureFlow(anchorAddBo.getNotShowInNatureFlow())
                    .build();
        }

        if (Objects.equals(anchorType, AnchorType.BILI_MINI_GAME_VALUE)) {
            return AnchorExtraJsonBo.builder()
                    .biliMiniGameMid(biliMiniGameMid)
                    .notShowInNatureFlow(anchorAddBo.getNotShowInNatureFlow())
                    .build();
        }

        if (Objects.equals(anchorType, AnchorType.BILI_APPLET_VALUE)) {
            return AnchorExtraJsonBo.builder()
                    .biliAppletUrl(anchorAddBo.getBiliAppletUrl())
                    .notShowInNatureFlow(anchorAddBo.getNotShowInNatureFlow())
                    .build();
        }
        return AnchorExtraJsonBo.builder()
                .notShowInNatureFlow(anchorAddBo.getNotShowInNatureFlow())
                .build();
    }
}
