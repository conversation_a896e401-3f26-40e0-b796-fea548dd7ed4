# Job 迁移说明文档

## 迁移概述

本次将 `app/src/main/java/com/bilibili/sycpb/cpm/scv/app/service/xxl/jobs` 目录下的所有 job 从旧的 xxl-job 框架迁移到新的 PowerJob (tempus) 框架。

## 迁移的主要变化

### 1. 框架变更
- **旧框架**: xxl-job
- **新框架**: PowerJob (tech.powerjob)

### 2. 代码结构变更
- **继承关系**: `extends IJobHandler` → `implements BasicProcessor`
- **注解变更**: `@JobHandler` → 移除，保留 `@Service`
- **方法签名**: `execute(String param)` → `process(TaskContext context)`
- **返回类型**: `ReturnT<String>` → `ProcessResult`

### 3. 参数和日志获取方式
- **参数获取**: `param` → `context.getJobParams()`
- **日志记录**: 新增 `context.getOmsLogger()` 用于 PowerJob 平台日志

### 4. 日志增强
- 在每个 job 的开始和结束添加了详细的日志记录
- 记录入参信息
- 记录处理过程中的关键信息
- 记录处理结果统计

## 已迁移的 Job 列表

| 序号 | 原始 Job 名称 | 新 Job 名称 | 功能描述 | 状态 |
|------|---------------|-------------|----------|------|
| 1 | AdShieldRefreshJob | AdShieldRefreshTempusJob | 广告屏蔽刷新 | ✅ 已存在 |
| 2 | RefreshCreativeArchiveInfoJob | RefreshCreativeArchiveInfoTempusJob | 刷新创意稿件信息 | ✅ 完成 |
| 3 | DisableCommentJob | DisableCommentTempusJob | 禁用评论 | ✅ 完成 |
| 4 | CidExpirationJob | CidExpirationTempusJob | CID过期处理 | ✅ 完成 |
| 5 | DeprecatingOperationLogJob | DeprecatingOperationLogTempusJob | 操作日志清理 | ✅ 完成 |
| 6 | EnableSelectiveCommentJob | EnableSelectiveCommentTempusJob | 启用精选评论 | ✅ 完成 |
| 7 | RefreshCommentComponentDynamicJob | RefreshCommentComponentDynamicTempusJob | 刷新评论组件动态 | ✅ 完成 |
| 8 | RefreshArchiveJob | RefreshArchiveTempusJob | 刷新稿件 | ✅ 完成 |
| 9 | RefreshGoodsArchiveCommentJob | RefreshGoodsArchiveCommentTempusJob | 刷新商品稿件评论 | ✅ 完成 |
| 10 | RefreshAutoCommentJob | RefreshAutoCommentTempusJob | 刷新自动评论 | ✅ 完成 |
| 11 | RefreshArchiveStateJob | RefreshArchiveStateTempusJob | 刷新稿件状态 | ✅ 完成 |
| 12 | ArchiveAnchorPointErrorData | ArchiveAnchorPointErrorDataTempusJob | 锚点错误数据处理 | ✅ 完成 |
| 13 | AnchorBusinessMessageFixUrlRefresher | AnchorBusinessMessageFixUrlRefresherTempusJob | 锚点业务消息URL修复 | ✅ 完成 |
| 14 | RefreshBusinessToolArchiveCommentJob | RefreshBusinessToolArchiveCommentTempusJob | 刷新经营工具稿件评论 | ✅ 完成 |
| 15 | RefreshCommentComponentShadowAuditStatusJob | RefreshCommentComponentShadowAuditStatusTempusJob | 刷新评论组件影子审核状态 | ✅ 完成 |

## 迁移完成情况

✅ **全部完成！** 所有 15 个 job 都已成功迁移到 PowerJob 框架。

## 使用说明

### 1. 配置 PowerJob
确保项目中已正确配置 PowerJob 相关依赖和配置。

### 2. 注册 Job
在 PowerJob 管理平台中注册新的 job，使用对应的类名作为处理器。

### 3. 参数传递
- 参数格式保持与原 job 兼容
- 通过 `context.getJobParams()` 获取参数
- 支持 JSON 格式的复杂参数

### 4. 监控和日志
- 使用 `context.getOmsLogger()` 记录 PowerJob 平台日志
- 保留原有的 `log` 记录应用日志
- 关键操作都有详细的日志记录

## 注意事项

1. **分片处理**: 对于需要分片的 job（如 RefreshArchiveTempusJob），需要根据 PowerJob 的分片机制调整代码
2. **异常处理**: 统一使用 `ProcessResult` 返回处理结果
3. **性能优化**: 保持原有的批处理和限流逻辑
4. **向下兼容**: 参数格式尽量保持与原 job 兼容

## 测试建议

1. **单元测试**: 为每个迁移的 job 编写单元测试
2. **集成测试**: 在测试环境验证 job 的完整执行流程
3. **性能测试**: 确保迁移后的性能不低于原 job
4. **监控验证**: 验证日志记录和监控指标的正确性

## 后续工作

1. 完成剩余 job 的迁移
2. 在测试环境验证所有迁移的 job
3. 逐步在生产环境替换旧的 job
4. 监控迁移后的运行状况
5. 优化和调整基于实际运行情况
