/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive;

import com.bapis.ad.crm.account.*;
import com.bilibili.sycpb.cpm.scv.app.service.bili.space.BiliSpaceService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.config.CmSpaceProperties;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.MgkCmSpaceDaoService;
import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmSpaceRecord;
import com.bilibili.sycpb.cpm.scv.app.service.http.internal.bos.RegisterResultBo;
import com.bilibili.sycpb.cpm.scv.app.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

import static com.bapis.ad.crm.account.ListAccountIdsByTypeReq.Type.SAME_CUSTOMER_SAME_AGENT_VALUE;

@Slf4j
@Service
@RequiredArgsConstructor
public class CmSpaceService {
    private final BiliSpaceService biliSpaceService;
    private final MgkCmSpaceDaoService mgkCmSpaceDaoService;
    private final CmSpaceProperties cmSpaceProperties;
    @RPCClient(value = "sycpb.cpm.crm-portal")
    private AccountReadServiceGrpc.AccountReadServiceBlockingStub accountReadServiceBlockingStub;


    public Long fetchMid(Integer accountId) {
        final var rec = mgkCmSpaceDaoService.fetchSpace(accountId);
        if (Objects.nonNull(rec)) return rec.getMid();

        RegisterResultBo registerResultBo = null;
        //代理维度灰度
        AccountBaseSingleReply accountBaseSingleReply = accountReadServiceBlockingStub.queryAccountBase(AccountIdReq.newBuilder().setAccountId(accountId).build());
        if (null != cmSpaceProperties.getFetchMidGrey() && Arrays.asList(cmSpaceProperties.getFetchMidGrey().split(",")).contains(String.valueOf(accountBaseSingleReply.getData().getDependencyAgentId()))) {
            registerResultBo = registerBiliAccount(accountId);
        } else {
            registerResultBo = registerBiliAccount();
        }

        mgkCmSpaceDaoService.saveSpace(accountId, registerResultBo.getMid(), registerResultBo.getPassword(), registerResultBo.getNickName());
        return registerResultBo.getMid();
    }

    public RegisterResultBo registerBiliAccount() {
        return biliSpaceService.registerBiliAccount(cmSpaceProperties);
    }

    public RegisterResultBo registerBiliAccount(Integer accountId) {
        try {
            ListAccountIdsByTypeReq req = ListAccountIdsByTypeReq.newBuilder()
                    .setAccountId(accountId)
                    .setType(ListAccountIdsByTypeReq.Type.SAME_CUSTOMER_SAME_AGENT)
                    .build();
            ListAccountIdsByTypeResp resp = accountReadServiceBlockingStub.listAccountIdsByType(req);
            if (CollectionUtils.isEmpty(resp.getAccountIdsList())) {
                return biliSpaceService.registerBiliAccount(cmSpaceProperties);
            }

            List<Integer> accountIds = new ArrayList<>(resp.getAccountIdsList());
            List<MgkCmSpaceRecord> mgkCmSpaceRecords = mgkCmSpaceDaoService.fetchSpaceRecords(accountIds);
            mgkCmSpaceRecords.removeIf(mgkCmSpaceRecord -> Objects.equals(mgkCmSpaceRecord.getAccountId(), accountId));
            if (CollectionUtils.isEmpty(mgkCmSpaceRecords)) {
                return biliSpaceService.registerBiliAccount(cmSpaceProperties);
            }

            int random = ThreadLocalRandom.current().nextInt(mgkCmSpaceRecords.size());
            MgkCmSpaceRecord mgkCmSpaceRecord = mgkCmSpaceRecords.get(random);
            log.info("根据账户id获取关联账号id 列表成功，accountId: {}, mid: {}, random mgkCmSpaceRecord {}", accountId, mgkCmSpaceRecord.getMid(), mgkCmSpaceRecord);
            return RegisterResultBo.builder()
                    .mid(mgkCmSpaceRecord.getMid())
                    .nickName(mgkCmSpaceRecord.getNickName())
                    .password(mgkCmSpaceRecord.getPassword())
                    .build();

        } catch (Exception e) {
            log.error("新增小号失败，accountId: {}", accountId);
            log.error("新增小号失败，error ", e);
            throw new RuntimeException("根据账户id获取关联账号id 列表失败", e);
        }

    }
}
