/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive;

import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.ArchivePushBo;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.ArchivePushResultV2Bo;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.CmArchiveOpenApiPipelineReqBo;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.bos.CmArchiveOpenApiPipelineRespBo;
import com.bilibili.sycpb.cpm.scv.app.controller.open.lib.archive.vos.ArchivePushV2Bo;
import com.bilibili.sycpb.cpm.scv.app.service.bili.archive.BiliArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.CmArchiveConverter;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.CmArchivePipelineService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.archive_v2.cm.bos.CmArchiveContextBo;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.CmArchivePushSyncer;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.CmArchiveService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.cm_archive.bos.CmArchiveProgressBo;
import com.bilibili.sycpb.cpm.scv.app.service.http.internal.bos.CategoryNodeBo;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.ArchiveMode;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import pleiades.component.http.server.response.JSONData;

@RestController
@RequiredArgsConstructor
@RequestMapping("/open/mapi")
public class MapiController {
    private final CmArchivePipelineService cmArchivePipelineService;
    private final CmArchiveService cmArchiveService;
    private final BiliArchiveService biliArchiveService;
    private final CmArchivePushSyncer cmArchivePushSyncer;

    @Operation(summary = "触发投稿流程")
    @PostMapping("/v1/trigger_cm_pipeline")
    public JSONData<CmArchiveOpenApiPipelineRespBo> triggerCmPipelineV1(@RequestBody CmArchiveOpenApiPipelineReqBo reqBo) {
        final var archiveMode = NumberUtils.isPositive(reqBo.getMid()) ? ArchiveMode.USER_SPACE : ArchiveMode.CM_SPACE;
        CmArchiveContextBo ctx = CmArchiveConverter.MAPPER.fromOpenApiBo(reqBo, archiveMode);
        ctx.setIsUsingAlgoCover(true);

        final var pipelineId = cmArchivePipelineService.triggerCmArchivePipelineAsync(ctx);
        return JSONData.success(CmArchiveOpenApiPipelineRespBo.builder()
            .pipelineId(pipelineId)
            .videoMd5(reqBo.getVideoMd5())
            .build());
    }

    @Operation(summary = "预下发视频上传的url")
    @GetMapping("/signed_url")
    public JSONData<String> genSignedUrl(@RequestParam String key) {
        return JSONData.success(cmArchiveService.genVideoUploadUrl(key));
    }

    @Operation(summary = "通过流水线id查询投稿进度")
    @GetMapping("/progress")
    public JSONData<CmArchiveProgressBo> progress(@RequestParam("pipeline_id") String pipelineId) {
        return JSONData.success(cmArchiveService.progress(pipelineId));
    }

    @Operation(summary = "通过视频md5查询投稿状态")
    @GetMapping("/v1/progress")
    public JSONData<CmArchiveProgressBo> progress(@RequestParam("account_id") Integer accountId, @RequestParam("video_md5") String videoMd5) {
        return JSONData.success(cmArchiveService.progress(accountId, videoMd5));
    }

    @Operation(summary = "稿件分区信息")
    @GetMapping("/bili_category")
    public JSONData<List<CategoryNodeBo>> biliCategory(@RequestParam Long mid) {
        return JSONData.success(biliArchiveService.getBiliCategory(mid));
    }

    @Operation(summary = "稿件推送")
    @PostMapping("/batch_push")
    public JSONData<ArchivePushResultV2Bo> pushV2(@RequestBody ArchivePushV2Bo pushVo) {

        List<Long> avidList = pushVo.getVideoIds().stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

        ArchivePushBo pushBo = ArchivePushBo.builder()
                .curAccountId(pushVo.getAccountId())
                .needSyncAccountIds(pushVo.getTargetAdvertiserIds())
                .needSyncIds(avidList)
                .isOpenApi(true)
                .build();
        return JSONData.success(cmArchivePushSyncer.batchPush(pushBo));
    }
}
