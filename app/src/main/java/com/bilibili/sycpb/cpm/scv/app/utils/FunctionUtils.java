/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.utils;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
public class FunctionUtils {
    /**
     * 重试
     *
     * @param id
     * @param supplier
     * @param retries
     * @param waitDuration
     * @param <T>
     * @return
     */
    @SneakyThrows
    public static <T> T execWithRetires(String id, Supplier<T> supplier, int retries, Duration waitDuration) {
        log.info("{}: 尝试{}次, 等待时长{}ms", id, retries, waitDuration.toMillis());

        for (var i = 0; i < retries; i++) {
            try {
                final var result = supplier.get();
                if (Objects.nonNull(result)) {
                    log.info("{}: 第{}次成功", id, i+1);
                    return result;
                }

                TimeUnit.MILLISECONDS.sleep(waitDuration.toMillis());
            } catch (Throwable t) {
                log.error(MessageFormat.format("{0}: 第{1}次失败", id, i+1), t);
                TimeUnit.MILLISECONDS.sleep(waitDuration.toMillis());
            }
        }
        return null;
    }

    public static<T, K> RecDiffResult<T, K> recDiff(Collection<T> existingRecs, Collection<T> newRecs, Function<T, String> getUK, Function<T, K> getKey, BiConsumer<T, K> setKey) {
        final RecDiffResult<T, K> result = new RecDiffResult<>();
        if (CollectionUtils.isEmpty(newRecs)) {
            result.setNewRecords(Collections.emptyList());
            result.setChangedRecords(Collections.emptyList());
            final List<K> offlineKeys = existingRecs.stream()
                .map(getKey)
                .collect(Collectors.toList());
            result.setOfflineRecordKeys(offlineKeys);
        } else if (CollectionUtils.isEmpty(existingRecs)) {
            result.setNewRecords(newRecs);
            result.setChangedRecords(Collections.emptyList());
            result.setOfflineRecordKeys(Collections.emptyList());
        } else {
            final Set<String> newUKSet = newRecs.stream()
                .map(getUK)
                .collect(Collectors.toSet());
            final List<K> offlineKeys = existingRecs.stream()
                .filter(t -> !newUKSet.contains(getUK.apply(t)))
                .map(getKey)
                .collect(Collectors.toList());
            result.setOfflineRecordKeys(offlineKeys);
            final List<T> changedRecords = new LinkedList<>();
            final List<T> newRecords = new LinkedList<>();
            final Map<String, T> existingUKMap = existingRecs.stream()
                .collect(Collectors.toMap(getUK, Function.identity()));
            for (T newRec : newRecs) {
                final String uk = getUK.apply(newRec);
                final T existingRecord = existingUKMap.get(uk);
                if (Objects.isNull(existingRecord)) {
                    newRecords.add(newRec);
                } else {
                    if (Objects.nonNull(setKey)) {
                        setKey.accept(newRec, getKey.apply(existingRecord));
                    }
                    changedRecords.add(newRec);
                }
            }
            result.setNewRecords(newRecords);
            result.setChangedRecords(changedRecords);
        }
        return result;
    }
}
