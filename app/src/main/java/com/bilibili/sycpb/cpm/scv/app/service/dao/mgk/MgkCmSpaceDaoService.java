/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.dao.mgk;

import com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.tables.records.MgkCmSpaceRecord;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.mgk.generated.Tables.MGK_CM_SPACE;

@Service
public class MgkCmSpaceDaoService {
    private final DSLContext mgk;

    public MgkCmSpaceDaoService(@Qualifier(MgkDataSourceConfig.MGK_DSL_CONTEXT) DSLContext mgk) {
        this.mgk = mgk;
    }

    /**
     * 保存空间数据 mgk_cm_space
     *
     * @param accountId
     * @param mid
     * @param password
     * @param nickName
     */
    public void saveSpace(Integer accountId, Long mid, String password, String nickName) {
        mgk.newRecord(MGK_CM_SPACE)
            .setAccountId(accountId)
            .setNickName(nickName)
            .setMid(mid)
            .setPassword(password)
            .store();
    }

    /**
     * 根据账号 id 商业账号空间
     *
     * @param accountId
     * @return
     */
    public MgkCmSpaceRecord fetchSpace(Integer accountId) {
        // 表: mgk_cm_space
        return mgk.fetchOne(MGK_CM_SPACE, MGK_CM_SPACE.ACCOUNT_ID.eq(accountId));
    }

    public MgkCmSpaceRecord fetchSpaceByMid(Long mid) {
        return mgk.fetchOne(MGK_CM_SPACE, MGK_CM_SPACE.MID.eq(mid));
    }

    /**
     * 根据账号 id 批量查询商业账号空间
     * @param accountIds
     * @return
     */
    public List<MgkCmSpaceRecord> fetchSpaceRecords(List<Integer> accountIds) {
        return mgk.fetch(MGK_CM_SPACE, MGK_CM_SPACE.ACCOUNT_ID.in(accountIds));
    }
}
