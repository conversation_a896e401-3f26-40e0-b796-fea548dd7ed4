/*
 * Copyright (c) 2015-2022 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnchorsAddBo {

    /**
     * 批量创建时稿件信息
     */
    private List<AnchorAvidInfoBo> avidList;

    // 锚点名称
    private String name;

    /**
     * 锚点类型
     */
    private Integer type;
    private Integer clueType;
    private String clueData;
    private List<Integer> scenes;

    private String mainTitle;
    private String subTitle;
    private String buttonText;
    private String iconTitle;

    private Integer iosUrlType;
    private Long iosUrlPageId;
    private String iosUrl;
    private Integer androidUrlType;
    private Long androidUrlPageId;
    private String androidUrl;
    private Integer conversionUrlType;
    private Long conversionUrlPageId;
    private String conversionUrl;
    private String iosSchemaUrl;
    private String androidSchemaUrl;
    private String iosButtonSchemaUrl;
    private String androidButtonSchemaUrl;

    private Integer isOpenMiniGame;
    private Integer miniGameId;

    private Integer gameBaseId;
    private Integer gamePlatformType;
    private Integer iosAppPackageId;
    private Integer androidAppPackageId;
    private Integer subPkg;
    //    private Long formId;
//    private Integer wechatPackageId;
    private Boolean isMapiRequest;
    private String customizedImpUrl;
    private String customizedClickUrl;

    private String guideText;
    private String appDetailText;
    // APP标签
    private List<String> appLabels;
    // app详情类型: 1横图2竖图
    private Integer appDetailType;
    // 顶图
    private List<String> topImgUrls;
    // app 图片列表
    private List<String> appImgUrls;

    private Integer accountId;
    private Integer customerId;
    private Integer agentId;

    // 多个创意对应同一个单元
    private Integer campaignId;
    private Integer unitId;

    private Integer isAndroidAppDirect;

    private Integer bizCode;
    private List<Integer> qualificationIds;

    private Integer businessToolType;

    private Integer groupId;
    private Long biliMiniGameMid;

    private String icon;

    private List<Integer> osTarget;

    private List<Integer> ageTarget;

    private String biliAppletUrl;

    private Integer notShowInNatureFlow;
}
