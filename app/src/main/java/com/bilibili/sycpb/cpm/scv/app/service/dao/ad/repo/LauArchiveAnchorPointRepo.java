/*
 * Copyright (c) 2015-2024 BiliBili Inc.
 */

package com.bilibili.sycpb.cpm.scv.app.service.dao.ad.repo;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.component.CommentComponentAuditStatus;
import com.bapis.ad.scv.anchor.AnchorAuditStatus;
import com.bapis.ad.scv.anchor.QueryAnchorReq;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.AnchorExtraJsonService;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.AnchorMngProc;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.bos.*;
import com.bilibili.sycpb.cpm.scv.app.service.biz.anchor.convertor.ArchiveAnchorConvertor;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.AdDataSourceConfig;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveAnchorPointDraftRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveAnchorPointOtherInfoRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveAnchorPointRecord;
import com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.tables.records.LauArchiveAnchorTypeConfigRecord;
import com.bilibili.sycpb.cpm.scv.app.utils.NumberUtils;
import com.bilibili.sycpb.cpm.scv.app.utils.Pager;
import com.bilibili.sycpb.cpm.scv.app.utils.constants.AnchorAppImgType;
import com.bilibili.sycpb.cpm.scv.app.utils.enums.anchor.AuditTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.sycpb.cpm.scv.app.service.dao.ad.generated.Tables.*;
/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class LauArchiveAnchorPointRepo {

    @Value("${anchor.list.queryMaxSize:100}")
    private Integer anchorListQueryMaxSize;

    @Resource(name = AdDataSourceConfig.AD_DSL_CONTEXT)
    private DSLContext ad;
    @Autowired
    private AnchorExtraJsonService anchorExtraJsonService;

    public LauArchiveAnchorPointRecord fetchAnchorById(Long id) {
        return fetchAnchorByIdAndDeleted(id, false);
    }

    public LauArchiveAnchorPointRecord fetchAnchorByIdAndDeleted(Long id, Boolean deletedFlag) {
        SelectConditionStep<LauArchiveAnchorPointRecord> selectConditionStep = ad.selectFrom(LAU_ARCHIVE_ANCHOR_POINT)
                .where(LAU_ARCHIVE_ANCHOR_POINT.ID.eq(id));

        if(null != deletedFlag){
            selectConditionStep = selectConditionStep.and(LAU_ARCHIVE_ANCHOR_POINT.IS_DELETED.eq(deletedFlag ? 1 : 0));
        }

        Result<LauArchiveAnchorPointRecord> anchorPointRecords = selectConditionStep.fetch();
        if (CollectionUtils.isEmpty(anchorPointRecords)) {
            return null;
        }
        return anchorPointRecords.get(0);
    }

    public List<LauArchiveAnchorPointRecord> fetchAnchorByIds(List<Long> ids) {
        Result<LauArchiveAnchorPointRecord> records = ad.selectFrom(LAU_ARCHIVE_ANCHOR_POINT)
                .where(LAU_ARCHIVE_ANCHOR_POINT.ID.in(ids))
                .and(LAU_ARCHIVE_ANCHOR_POINT.IS_DELETED.eq(0))
                .fetch();

        if (CollectionUtils.isEmpty(records)) {
            return null;
        }
        return records;
    }

    public List<LauArchiveAnchorPointRecord> fetchAnchorByGtId(Long gtId, boolean isArchive) {
        if (gtId == null) {
            return Collections.emptyList();
        }
        // 条件
        Condition condition = DSL.trueCondition();
        condition = condition.and(LAU_ARCHIVE_ANCHOR_POINT.ID.gt(gtId))
                .and(LAU_ARCHIVE_ANCHOR_POINT.IS_DELETED.eq(0))
                .and(LAU_ARCHIVE_ANCHOR_POINT.TITLE.eq(""));
        if (isArchive) {
            condition = condition.and(LAU_ARCHIVE_ANCHOR_POINT.AID.ne(0L));
        }
        final var records = ad
                .selectFrom(LAU_ARCHIVE_ANCHOR_POINT)
                .where(condition)
                .orderBy(LAU_ARCHIVE_ANCHOR_POINT.ID.asc())
                .limit(20)
                .fetch();

        return records;
    }

    public LauArchiveAnchorPointRecord fetchAnchorByAidIncludeDeleted(Long aid) {
        Result<LauArchiveAnchorPointRecord> anchorPointRecords = ad.selectFrom(LAU_ARCHIVE_ANCHOR_POINT)
                .where(LAU_ARCHIVE_ANCHOR_POINT.AID.eq(aid))
//            .and(LAU_ARCHIVE_ANCHOR_POINT.IS_DELETED.eq(0))
                .fetch();

        if (CollectionUtils.isEmpty(anchorPointRecords)) {
            return null;
        }
        return anchorPointRecords.get(0);
    }

    public List<LauArchiveAnchorPointRecord> queryListByAidsIncludeDeleted(List<Long> aids) {

        if (CollectionUtils.isEmpty(aids)) {
            return Collections.emptyList();
        }

        Result<LauArchiveAnchorPointRecord> anchorPointRecords = ad.selectFrom(LAU_ARCHIVE_ANCHOR_POINT)
                .where(LAU_ARCHIVE_ANCHOR_POINT.AID.in(aids))
//            .and(LAU_ARCHIVE_ANCHOR_POINT.IS_DELETED.eq(0))
                .fetch();

        if (CollectionUtils.isEmpty(anchorPointRecords)) {
            return Collections.emptyList();
        }
        return anchorPointRecords;
    }

    public List<LauArchiveAnchorPointRecord> queryListByAidsExcludeDeleted(List<Long> aids) {

        if (CollectionUtils.isEmpty(aids)) {
            return Collections.emptyList();
        }

        Result<LauArchiveAnchorPointRecord> anchorPointRecords = ad.selectFrom(LAU_ARCHIVE_ANCHOR_POINT)
                .where(LAU_ARCHIVE_ANCHOR_POINT.AID.in(aids))
                .and(LAU_ARCHIVE_ANCHOR_POINT.IS_DELETED.eq(0))
                .fetch();

        if (CollectionUtils.isEmpty(anchorPointRecords)) {
            return null;
        }
        return anchorPointRecords;
    }

    public int deleteAnchor(Long id) {
        log.info("deleteAnchor, id={}", id);

        int count = ad.update(LAU_ARCHIVE_ANCHOR_POINT)
                .set(LAU_ARCHIVE_ANCHOR_POINT.IS_DELETED, 1)
                .where(LAU_ARCHIVE_ANCHOR_POINT.ID.eq(id))
                .execute();
        return count;
    }

    public int updateTitle(Long id, String title) {
        log.info("updateTitle, id={}, title={}", id, title);

        int count = ad.update(LAU_ARCHIVE_ANCHOR_POINT)
                .set(LAU_ARCHIVE_ANCHOR_POINT.TITLE, title)
                .where(LAU_ARCHIVE_ANCHOR_POINT.ID.eq(id))
                .execute();
        return count;
    }


    public Long createAnchor(AnchorAddBo anchorAddBo) {

        String scenes = anchorAddBo.getScenes().stream().map(t -> String.valueOf(t)).collect(Collectors.joining(","));
        String appLabels = anchorAddBo.getAppLabels().stream().collect(Collectors.joining(","));

        LauArchiveAnchorPointRecord anchorPointRecord = ad.newRecord(LAU_ARCHIVE_ANCHOR_POINT)
                .setScenes(scenes)
                // 默认待审核
                .setAuditStatus(CommentComponentAuditStatus.AUDITING.getNumber())
                .setStatus(CommentComponentAuditStatus.AUDITING.getNumber())
                .setEnterAuditTime(new Timestamp(System.currentTimeMillis()))
                .setAid(anchorAddBo.getAid())
                .setType(anchorAddBo.getType())
                .setMid(anchorAddBo.getMid())
                .setAccountId(anchorAddBo.getAccountId())
                .setCustomerId(anchorAddBo.getCustomerId())
                .setAgentId(anchorAddBo.getAgentId())
                .setCampaignId(anchorAddBo.getCampaignId())
                .setUnitId(anchorAddBo.getUnitId())
                .setCreativeId(anchorAddBo.getCreativeId())
                .setClueType(anchorAddBo.getClueType())
                .setMainTitle(anchorAddBo.getMainTitle())
                .setSubTitle(anchorAddBo.getSubTitle())
                .setButtonText(anchorAddBo.getButtonText())
                .setIconTitle(anchorAddBo.getIconTitle())
                .setConversionUrlType(anchorAddBo.getConversionUrlType())
                .setConversionUrlPageId(anchorAddBo.getConversionUrlPageId())
                .setConversionUrl(anchorAddBo.getConversionUrl())
                .setIosUrlType(anchorAddBo.getIosUrlType())
                .setIosUrlPageId(anchorAddBo.getIosUrlPageId())
                .setIosUrl(anchorAddBo.getIosUrl())
                .setAndroidUrlType(anchorAddBo.getAndroidUrlType())
                .setAndroidUrlPageId(anchorAddBo.getAndroidUrlPageId())
                .setAndroidUrl(anchorAddBo.getAndroidUrl())
                .setIosSchemaUrl(anchorAddBo.getIosSchemaUrl())
                .setAndroidSchemaUrl(anchorAddBo.getAndroidSchemaUrl())
                .setIosButtonSchemaUrl(anchorAddBo.getIosButtonSchemaUrl())
                .setAndroidButtonSchemaUrl(anchorAddBo.getAndroidButtonSchemaUrl())
                .setIsOpenMiniGame(anchorAddBo.getIsOpenMiniGame())
                .setMiniGameId(anchorAddBo.getMiniGameId())
                .setGameBaseId(anchorAddBo.getGameBaseId())
                .setGamePlatformType(anchorAddBo.getGamePlatformType())
                .setIosAppPackageId(anchorAddBo.getIosAppPackageId())
                .setAndroidAppPackageId(anchorAddBo.getAndroidAppPackageId())
                .setSubPkg(anchorAddBo.getSubPkg())
                .setClueData(anchorAddBo.getClueData())
                .setCustomizedImpUrl(anchorAddBo.getCustomizedImpUrl())
                .setCustomizedClickUrl(anchorAddBo.getCustomizedClickUrl())
                .setName(anchorAddBo.getName())
                .setGuideText(anchorAddBo.getGuideText())
                .setAppDetailText(anchorAddBo.getAppDetailText())
                .setAppDetailType(anchorAddBo.getAppDetailType())
                .setAppLabels(appLabels)
                .setIsAndroidAppDirect(anchorAddBo.getIsAndroidAppDirect())
                .setAuditType(AuditTypeEnum.INIT.getCode())
                .setUpNickname(anchorAddBo.getUpNickName());
        anchorPointRecord.store();
        return anchorPointRecord.getId();
    }

    public Integer updateAnchorGroup(List<Long> ids, Integer groupId) {
        if (CollectionUtils.isEmpty(ids) || groupId == null) {
            return 0;
        }
        return ad.update(LAU_ARCHIVE_ANCHOR_POINT)
                .set(LAU_ARCHIVE_ANCHOR_POINT.GROUP_ID, groupId)
                .where(LAU_ARCHIVE_ANCHOR_POINT.ID.in(ids))
                .execute();
    }
    public Integer saveAnchorOtherInfo(Long anchorId, Long aid, AnchorOtherInfoBo anchorOtherInfoBo, Boolean isUpdate) {

        List<String> topImgUrls = anchorOtherInfoBo.getTopImgUrls();
        List<String> appImgUrls = anchorOtherInfoBo.getAppImgUrls();

        // 先删除
        if (isUpdate) {
            deleteAnchorOtherInfo(anchorId);
        }

        // 重新插入顶图和app图片
        List<LauArchiveAnchorPointOtherInfoRecord> otherInfoRecords = new ArrayList<>();

        for (String appImgUrl : topImgUrls) {
            LauArchiveAnchorPointOtherInfoRecord otherInfoRecord = ad.newRecord(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO)
                    .setAnchorId(anchorId)
                    .setAid(aid)
                    .setContent(appImgUrl)
                    .setType(AnchorAppImgType.TOP_IMAGE);
            otherInfoRecords.add(otherInfoRecord);
        }

        for (String appImgUrl : appImgUrls) {
            LauArchiveAnchorPointOtherInfoRecord otherInfoRecord = ad.newRecord(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO)
                    .setAnchorId(anchorId)
                    .setAid(aid)
                    .setContent(appImgUrl)
                    .setType(AnchorAppImgType.APP_IMAGE);
            otherInfoRecords.add(otherInfoRecord);
        }

        if (!CollectionUtils.isEmpty(otherInfoRecords)) {
            ad.batchStore(otherInfoRecords).execute();
        }
        return 1;
    }

    public Integer saveAnchorsOtherInfo(List<AnchorOtherInfoBo> anchorOtherInfoBos) {

        if (CollectionUtils.isEmpty(anchorOtherInfoBos)) {
            return 0;
        }

        List<LauArchiveAnchorPointOtherInfoRecord> otherInfoRecords = new ArrayList<>();
        for (AnchorOtherInfoBo anchorOtherInfoBo : anchorOtherInfoBos) {

            List<String> topImgUrls = anchorOtherInfoBo.getTopImgUrls();
            List<String> appImgUrls = anchorOtherInfoBo.getAppImgUrls();

            // 重新插入顶图和app图片

            for (String appImgUrl : topImgUrls) {
                LauArchiveAnchorPointOtherInfoRecord otherInfoRecord = ad.newRecord(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO)
                        .setAnchorId(anchorOtherInfoBo.getAnchorId())
                        .setAid(anchorOtherInfoBo.getAid())
                        .setContent(appImgUrl)
                        .setType(AnchorAppImgType.TOP_IMAGE);
                otherInfoRecords.add(otherInfoRecord);
            }

            for (String appImgUrl : appImgUrls) {
                LauArchiveAnchorPointOtherInfoRecord otherInfoRecord = ad.newRecord(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO)
                        .setAnchorId(anchorOtherInfoBo.getAnchorId())
                        .setAid(anchorOtherInfoBo.getAid())
                        .setContent(appImgUrl)
                        .setType(AnchorAppImgType.APP_IMAGE);
                otherInfoRecords.add(otherInfoRecord);
            }
        }


        List<List<LauArchiveAnchorPointOtherInfoRecord>> partitions = Lists.partition(otherInfoRecords, 50);
        for (List<LauArchiveAnchorPointOtherInfoRecord> partition : partitions) {
            ad.batchStore(partition).execute();
        }
        return 1;
    }

    public void deleteAnchorOtherInfo(Long anchorId) {
        ad.delete(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO)
                .where(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO.ANCHOR_ID.eq(anchorId))
                .execute();
    }

    /**
     * @param oldAnchorPointRecord
     * @param newAnchorUpdateBo
     * @param anchorAuditStatusBo  审核状态
     * @return
     */
    public int updateAnchor(LauArchiveAnchorPointRecord oldAnchorPointRecord, AnchorUpdateBo newAnchorUpdateBo, AnchorAuditStatusBo anchorAuditStatusBo) {
        if (newAnchorUpdateBo.getScenes() == null) {
            newAnchorUpdateBo.setScenes(new ArrayList<>());
        }
        String scenes = newAnchorUpdateBo.getScenes().stream().map(t -> String.valueOf(t)).collect(Collectors.joining(","));
//        AnchorExtraJsonBo extraJsonBo = anchorExtraJsonService.generateCommentComponentExtraJsonBo(newAnchorUpdateBo.getType(), newAnchorUpdateBo.getBiliMiniGameMid());
        AnchorExtraJsonBo extraJsonBo = anchorExtraJsonService.generateCommentComponentExtraJsonBo(newAnchorUpdateBo);

        return oldAnchorPointRecord
                .setAuditStatus(anchorAuditStatusBo.getToAuditStatus())
                .setStatus(anchorAuditStatusBo.getToStatus())
                .setScenes(scenes)
                .setName(newAnchorUpdateBo.getName())
                // type 不能变更
//            .setType(newAnchorUpdateBo.getType())
                .setAid(newAnchorUpdateBo.getAid())
                .setMid(newAnchorUpdateBo.getMid())
                .setClueType(newAnchorUpdateBo.getClueType())
                .setMainTitle(newAnchorUpdateBo.getMainTitle())
                .setSubTitle(newAnchorUpdateBo.getSubTitle())
                .setButtonText(newAnchorUpdateBo.getButtonText())
                .setIconTitle(newAnchorUpdateBo.getIconTitle())
                .setConversionUrlType(newAnchorUpdateBo.getConversionUrlType())
                .setConversionUrlPageId(newAnchorUpdateBo.getConversionUrlPageId())
                .setConversionUrl(newAnchorUpdateBo.getConversionUrl())
                .setIosUrlType(newAnchorUpdateBo.getIosUrlType())
                .setIosUrlPageId(newAnchorUpdateBo.getIosUrlPageId())
                .setIosUrl(newAnchorUpdateBo.getIosUrl())
                .setAndroidUrlType(newAnchorUpdateBo.getAndroidUrlType())
                .setAndroidUrlPageId(newAnchorUpdateBo.getAndroidUrlPageId())
                .setAndroidUrl(newAnchorUpdateBo.getAndroidUrl())
                .setIosSchemaUrl(newAnchorUpdateBo.getIosSchemaUrl())
                .setAndroidSchemaUrl(newAnchorUpdateBo.getAndroidSchemaUrl())
                .setIosButtonSchemaUrl(newAnchorUpdateBo.getIosButtonSchemaUrl())
                .setAndroidButtonSchemaUrl(newAnchorUpdateBo.getAndroidButtonSchemaUrl())
                .setIsOpenMiniGame(newAnchorUpdateBo.getIsOpenMiniGame())
                .setMiniGameId(newAnchorUpdateBo.getMiniGameId())
                .setGameBaseId(newAnchorUpdateBo.getGameBaseId())
                .setGamePlatformType(newAnchorUpdateBo.getGamePlatformType())
                .setIosAppPackageId(newAnchorUpdateBo.getIosAppPackageId())
                .setAndroidAppPackageId(newAnchorUpdateBo.getAndroidAppPackageId())
                .setSubPkg(newAnchorUpdateBo.getSubPkg())
                .setClueData(newAnchorUpdateBo.getClueData())
                .setCustomizedImpUrl(newAnchorUpdateBo.getCustomizedImpUrl())
                .setCustomizedClickUrl(newAnchorUpdateBo.getCustomizedClickUrl())
                .setUpNickname(newAnchorUpdateBo.getUpNickName())
                .setReason("")
                .setIsAndroidAppDirect(newAnchorUpdateBo.getIsAndroidAppDirect())
                .setGroupId(Optional.ofNullable(newAnchorUpdateBo.getGroupId()).orElse(0))
                .setExtra(Objects.nonNull(extraJsonBo) ? JSON.toJSONString(extraJsonBo) : "")
                .store();
    }

    public int createAnchorForDeleted(LauArchiveAnchorPointRecord oldAnchorPointRecord, AnchorAddBo anchorAddBo) {
        if (anchorAddBo.getScenes() == null) {
            anchorAddBo.setScenes(new ArrayList<>());
        }
        String scenes = anchorAddBo.getScenes().stream().map(t -> String.valueOf(t)).collect(Collectors.joining(","));
        String appLabels = anchorAddBo.getAppLabels().stream().collect(Collectors.joining(","));

        Timestamp nowTimestamp = new Timestamp(System.currentTimeMillis());
        return oldAnchorPointRecord
                .setScenes(scenes)
                // 默认待审核
                .setAuditStatus(CommentComponentAuditStatus.AUDITING.getNumber())
                .setStatus(CommentComponentAuditStatus.AUDITING.getNumber())
                // 清空数据
                .setEnterAuditTime(nowTimestamp)
                .setReason("")
                .setAuditPerson("")
                .setIsDeleted(0)
                .setCtime(nowTimestamp)
                .setAid(anchorAddBo.getAid())
                .setType(anchorAddBo.getType())
                .setMid(anchorAddBo.getMid())
                .setAccountId(anchorAddBo.getAccountId())
                .setCustomerId(anchorAddBo.getCustomerId())
                .setAgentId(anchorAddBo.getAgentId())
                .setCampaignId(anchorAddBo.getCampaignId())
                .setUnitId(anchorAddBo.getUnitId())
                .setCreativeId(anchorAddBo.getCreativeId())
                .setClueType(anchorAddBo.getClueType())
                .setMainTitle(anchorAddBo.getMainTitle())
                .setSubTitle(anchorAddBo.getSubTitle())
                .setButtonText(anchorAddBo.getButtonText())
                .setIconTitle(anchorAddBo.getIconTitle())
                .setConversionUrlType(anchorAddBo.getConversionUrlType())
                .setConversionUrlPageId(anchorAddBo.getConversionUrlPageId())
                .setConversionUrl(anchorAddBo.getConversionUrl())
                .setIosUrlType(anchorAddBo.getIosUrlType())
                .setIosUrlPageId(anchorAddBo.getIosUrlPageId())
                .setIosUrl(anchorAddBo.getIosUrl())
                .setAndroidUrlType(anchorAddBo.getAndroidUrlType())
                .setAndroidUrlPageId(anchorAddBo.getAndroidUrlPageId())
                .setAndroidUrl(anchorAddBo.getAndroidUrl())
                .setIosSchemaUrl(anchorAddBo.getIosSchemaUrl())
                .setAndroidSchemaUrl(anchorAddBo.getAndroidSchemaUrl())
                .setIosButtonSchemaUrl(anchorAddBo.getIosButtonSchemaUrl())
                .setAndroidButtonSchemaUrl(anchorAddBo.getAndroidButtonSchemaUrl())
                .setIsOpenMiniGame(anchorAddBo.getIsOpenMiniGame())
                .setMiniGameId(anchorAddBo.getMiniGameId())
                .setGameBaseId(anchorAddBo.getGameBaseId())
                .setGamePlatformType(anchorAddBo.getGamePlatformType())
                .setIosAppPackageId(anchorAddBo.getIosAppPackageId())
                .setAndroidAppPackageId(anchorAddBo.getAndroidAppPackageId())
                .setSubPkg(anchorAddBo.getSubPkg())
                .setClueData(anchorAddBo.getClueData())
                .setCustomizedImpUrl(anchorAddBo.getCustomizedImpUrl())
                .setCustomizedClickUrl(anchorAddBo.getCustomizedClickUrl())
                .setName(anchorAddBo.getName())
                .setUpNickname(anchorAddBo.getUpNickName())
                .setGuideText(anchorAddBo.getGuideText())
                .setAppDetailText(anchorAddBo.getAppDetailText())
                .setAppDetailType(anchorAddBo.getAppDetailType())
                .setAppLabels(appLabels)
                .setIsAndroidAppDirect(anchorAddBo.getIsAndroidAppDirect())
                .setAuditType(AuditTypeEnum.INIT.getCode())
                .store();
    }

    /**
     * 用草稿内容覆盖(审核通过时才会出现这种情况)
     *
     * @param oldAnchorPointRecord
     * @param newAnchorUpdateBo
     * @return
     */
    public int updateAnchorFromDraft(LauArchiveAnchorPointRecord oldAnchorPointRecord, AnchorUpdateBo newAnchorUpdateBo, AnchorAuditBo auditPassBo) {
        String scenes = newAnchorUpdateBo.getScenes().stream().map(t -> String.valueOf(t)).collect(Collectors.joining(","));
        String appLabels = newAnchorUpdateBo.getAppLabels().stream().collect(Collectors.joining(","));

//        AnchorExtraJsonBo extraJsonBo = anchorExtraJsonService.generateCommentComponentExtraJsonBo(newAnchorUpdateBo.getType(), newAnchorUpdateBo.getBiliMiniGameMid());
        AnchorExtraJsonBo extraJsonBo = anchorExtraJsonService.generateCommentComponentExtraJsonBo(newAnchorUpdateBo);

        return oldAnchorPointRecord
                // 直接审核通过
                .setAuditStatus(CommentComponentAuditStatus.AUDIT_PASSED.getNumber())
                .setStatus(CommentComponentAuditStatus.AUDIT_PASSED.getNumber())
                .setReason("")
                .setAuditTime(new Timestamp(System.currentTimeMillis()))
                .setAuditPerson(auditPassBo.getOperatorName())
                .setScenes(scenes)
                .setName(newAnchorUpdateBo.getName())
                // type,aid 不能变更
//            .setType(newAnchorUpdateBo.getType())
                .setClueType(newAnchorUpdateBo.getClueType())
                .setMainTitle(newAnchorUpdateBo.getMainTitle())
                .setSubTitle(newAnchorUpdateBo.getSubTitle())
                .setButtonText(newAnchorUpdateBo.getButtonText())
//            .setIconTitle(newAnchorUpdateBo.getIconTitle())
                .setConversionUrlType(newAnchorUpdateBo.getConversionUrlType())
                .setConversionUrlPageId(newAnchorUpdateBo.getConversionUrlPageId())
                .setConversionUrl(newAnchorUpdateBo.getConversionUrl())
                .setIosUrlType(newAnchorUpdateBo.getIosUrlType())
                .setIosUrlPageId(newAnchorUpdateBo.getIosUrlPageId())
                .setIosUrl(newAnchorUpdateBo.getIosUrl())
                .setAndroidUrlType(newAnchorUpdateBo.getAndroidUrlType())
                .setAndroidUrlPageId(newAnchorUpdateBo.getAndroidUrlPageId())
                .setAndroidUrl(newAnchorUpdateBo.getAndroidUrl())
                .setIosSchemaUrl(newAnchorUpdateBo.getIosSchemaUrl())
                .setAndroidSchemaUrl(newAnchorUpdateBo.getAndroidSchemaUrl())
                .setIosButtonSchemaUrl(newAnchorUpdateBo.getIosButtonSchemaUrl())
                .setAndroidButtonSchemaUrl(newAnchorUpdateBo.getAndroidButtonSchemaUrl())
                .setIsOpenMiniGame(newAnchorUpdateBo.getIsOpenMiniGame())
                .setMiniGameId(newAnchorUpdateBo.getMiniGameId())
                .setGameBaseId(newAnchorUpdateBo.getGameBaseId())
                .setGamePlatformType(newAnchorUpdateBo.getGamePlatformType())
                .setIosAppPackageId(newAnchorUpdateBo.getIosAppPackageId())
                .setAndroidAppPackageId(newAnchorUpdateBo.getAndroidAppPackageId())
                .setSubPkg(newAnchorUpdateBo.getSubPkg())
                .setClueData(newAnchorUpdateBo.getClueData())
                .setIsAndroidAppDirect(newAnchorUpdateBo.getIsAndroidAppDirect())
                .setCustomizedImpUrl(newAnchorUpdateBo.getCustomizedImpUrl())
                .setCustomizedClickUrl(newAnchorUpdateBo.getCustomizedClickUrl())
//            .setUpNickname(newAnchorUpdateBo.getUpNickName())
                .setGuideText(newAnchorUpdateBo.getGuideText())
                .setAppDetailText(newAnchorUpdateBo.getAppDetailText())
                .setAppDetailType(newAnchorUpdateBo.getAppDetailType())
                .setAppLabels(appLabels)
                .setExtra(Objects.nonNull(extraJsonBo) ? JSON.toJSONString(extraJsonBo) : "")
                .setAuditType(Optional.ofNullable(auditPassBo.getAuditType()).orElse(AuditTypeEnum.MANUAL.getCode()))
                .store();
    }

    public int updateAnchor(LauArchiveAnchorPointRecord anchorPointRecord) {
        return anchorPointRecord
                .store();
    }


    public LauArchiveAnchorPointDraftRecord fetchAnchorDraft(Long anchorId) {
        Result<LauArchiveAnchorPointDraftRecord> draftRecords = ad.selectFrom(LAU_ARCHIVE_ANCHOR_POINT_DRAFT)
                .where(LAU_ARCHIVE_ANCHOR_POINT_DRAFT.ANCHOR_ID.eq(anchorId))
                .and(LAU_ARCHIVE_ANCHOR_POINT_DRAFT.IS_DELETED.eq(0))
                .fetch();

        if (CollectionUtils.isEmpty(draftRecords)) {
            return null;
        }
        return draftRecords.get(0);
    }

    public List<LauArchiveAnchorPointDraftRecord> queryAnchorDraftList(List<Long> anchorIds) {
        Result<LauArchiveAnchorPointDraftRecord> draftRecords = ad.selectFrom(LAU_ARCHIVE_ANCHOR_POINT_DRAFT)
                .where(LAU_ARCHIVE_ANCHOR_POINT_DRAFT.ANCHOR_ID.in(anchorIds))
                .and(LAU_ARCHIVE_ANCHOR_POINT_DRAFT.IS_DELETED.eq(0))
                .fetch();
        return draftRecords;
    }

    public List<LauArchiveAnchorPointOtherInfoRecord> queryAnchorOtherInfos(Long anchorId) {

        if (anchorId == null) {
            return Collections.emptyList();
        }

        Result<LauArchiveAnchorPointOtherInfoRecord> records = ad.selectFrom(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO)
                .where(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO.ANCHOR_ID.eq(anchorId))
                .and(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO.IS_DELETED.eq(0))
                .fetch();
        return records;
    }

    public List<LauArchiveAnchorPointOtherInfoRecord> queryAnchorOtherInfos(List<Long> anchorIds) {

        if (CollectionUtils.isEmpty(anchorIds)) {
            return Collections.emptyList();
        }

        Result<LauArchiveAnchorPointOtherInfoRecord> records = ad.selectFrom(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO)
                .where(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO.ANCHOR_ID.in(anchorIds))
                .and(LAU_ARCHIVE_ANCHOR_POINT_OTHER_INFO.IS_DELETED.eq(0))
                .fetch();
        return records;
    }

    public Map<Long, LauArchiveAnchorPointDraftRecord> queryAnchorDraftMap(List<Long> anchorIds) {
        List<LauArchiveAnchorPointDraftRecord> draftRecords = this.queryAnchorDraftList(anchorIds);

        return draftRecords.stream().collect(Collectors.toMap(t -> t.getAnchorId(), t -> t, (t1, t2) -> t2));
    }

    public void deleteAnchorDraftIfExist(Long anchorId) {
        LauArchiveAnchorPointDraftRecord anchorPointDraftRecord = this.fetchAnchorDraft(anchorId);
        if (anchorPointDraftRecord != null) {
            this.deleteAnchorDraft(anchorId);
        }
    }

    public int deleteAnchorDraft(Long anchorId) {
        log.info("deleteAnchorDraft, anchorId={}", anchorId);
        int count = ad.delete(LAU_ARCHIVE_ANCHOR_POINT_DRAFT)
                .where(LAU_ARCHIVE_ANCHOR_POINT_DRAFT.ANCHOR_ID.eq(anchorId))
                .execute();
        return count;
    }

    public Long createAnchorDraft(AnchorDraftBo anchorDraftBo, Long aid) {

        LauArchiveAnchorPointDraftRecord anchorPointDraftRecord = ad.newRecord(LAU_ARCHIVE_ANCHOR_POINT_DRAFT)
                .setAnchorId(anchorDraftBo.getAnchorId())
                .setAid(aid)
                .setContent(JSON.toJSONString(anchorDraftBo));
        anchorPointDraftRecord.store();
        return anchorPointDraftRecord.getId();
    }

    public int updateAnchorDraft(LauArchiveAnchorPointDraftRecord archiveAnchorPointDraftRecord, AnchorDraftBo anchorDraftBo) {

        return archiveAnchorPointDraftRecord
                .setAnchorId(anchorDraftBo.getAnchorId())
                .setContent(JSON.toJSONString(anchorDraftBo))
                .store();
    }

    public Pager<AnchorInfoBo> queryAnchorPageList(AnchorPageQueryBo req) {

        // 条件
        Condition condition = DSL.trueCondition();
        // 账户
        condition = condition.and(!CollectionUtils.isEmpty(req.getAccountIds()) ? LAU_ARCHIVE_ANCHOR_POINT.ACCOUNT_ID.in(req.getAccountIds()) : DSL.noCondition());
        condition = condition.and(!CollectionUtils.isEmpty(req.getCustomerIds()) ? LAU_ARCHIVE_ANCHOR_POINT.CUSTOMER_ID.in(req.getCustomerIds()) : DSL.noCondition());
        condition = condition.and(!CollectionUtils.isEmpty(req.getAgentIds()) ? LAU_ARCHIVE_ANCHOR_POINT.AGENT_ID.in(req.getAgentIds()) : DSL.noCondition());
        // ctime
        condition = condition.and(NumberUtils.isPositive(req.getFromTime()) ? LAU_ARCHIVE_ANCHOR_POINT.CTIME.greaterOrEqual(new Timestamp(req.getFromTime())) : DSL.noCondition());
        condition = condition.and(NumberUtils.isPositive(req.getToTime()) ? LAU_ARCHIVE_ANCHOR_POINT.CTIME.lessOrEqual(new Timestamp(req.getToTime())) : DSL.noCondition());
        // group_id
        condition = condition.and(NumberUtils.isPositive(req.getGroupId()) ? LAU_ARCHIVE_ANCHOR_POINT.GROUP_ID.eq(req.getGroupId()) : DSL.noCondition());
        // 提审时间
        condition = condition.and(NumberUtils.isPositive(req.getEnterAuditTimeStart()) ? LAU_ARCHIVE_ANCHOR_POINT.ENTER_AUDIT_TIME.greaterOrEqual(new Timestamp(req.getEnterAuditTimeStart())) : DSL.noCondition());
        condition = condition.and(NumberUtils.isPositive(req.getEnterAuditTimeEnd()) ? LAU_ARCHIVE_ANCHOR_POINT.ENTER_AUDIT_TIME.lessOrEqual(new Timestamp(req.getEnterAuditTimeEnd())) : DSL.noCondition());
        // 审核时间
        condition = condition.and(NumberUtils.isPositive(req.getAuditTimeStart()) ? LAU_ARCHIVE_ANCHOR_POINT.AUDIT_TIME.greaterOrEqual(new Timestamp(req.getAuditTimeStart())) : DSL.noCondition());
        condition = condition.and(NumberUtils.isPositive(req.getAuditTimeEnd()) ? LAU_ARCHIVE_ANCHOR_POINT.AUDIT_TIME.lessOrEqual(new Timestamp(req.getAuditTimeEnd())) : DSL.noCondition());
        // 审核状态
        condition = condition.and(!CollectionUtils.isEmpty(req.getStatusList()) ? LAU_ARCHIVE_ANCHOR_POINT.STATUS.in(req.getStatusList()) : DSL.noCondition());
        condition = condition.and(!CollectionUtils.isEmpty(req.getAuditStatusList()) ? LAU_ARCHIVE_ANCHOR_POINT.AUDIT_STATUS.in(req.getAuditStatusList()) : DSL.noCondition());
        condition = condition.and(!CollectionUtils.isEmpty(req.getIsDeleted()) ? LAU_ARCHIVE_ANCHOR_POINT.IS_DELETED.in(req.getIsDeleted()) : DSL.noCondition());

        condition = condition.and(!CollectionUtils.isEmpty(req.getIds()) ? LAU_ARCHIVE_ANCHOR_POINT.ID.in(req.getIds()) : DSL.noCondition());
        condition = condition.and(!CollectionUtils.isEmpty(req.getAids()) ? LAU_ARCHIVE_ANCHOR_POINT.AID.in(req.getAids()) : DSL.noCondition());
        condition = condition.and(!CollectionUtils.isEmpty(req.getMids()) ? LAU_ARCHIVE_ANCHOR_POINT.MID.in(req.getMids()) : DSL.noCondition());
        // 锚点名称
        condition = condition.and(!StringUtils.isEmpty(req.getAnchorName()) ? LAU_ARCHIVE_ANCHOR_POINT.NAME.like("%" + req.getAnchorName() + "%") : DSL.noCondition());
        // 审核人
        condition = condition.and(!CollectionUtils.isEmpty(req.getAuditPersons()) ? LAU_ARCHIVE_ANCHOR_POINT.AUDIT_PERSON.in(req.getAuditPersons()) : DSL.noCondition());

        condition = condition.and(!CollectionUtils.isEmpty(req.getBizCodeList()) ? LAU_ARCHIVE_ANCHOR_POINT.BIZ_CODE.in(req.getBizCodeList()) : DSL.noCondition());

        boolean isQueryToAudit = false;
        if (!CollectionUtils.isEmpty(req.getStatusList()) && req.getStatusList().size() == 1 && req.getStatusList().contains(AnchorAuditStatus.AUDITING_VALUE)) {
            isQueryToAudit = true;
        }

        // total
        final var total = ad.fetchCount(LAU_ARCHIVE_ANCHOR_POINT, condition);

        // list
        final var records = ad
                .selectFrom(LAU_ARCHIVE_ANCHOR_POINT)
                .where(condition)
                // 查询待审核的，按提审时间升序；否则按mtime倒序
                .orderBy(isQueryToAudit ? LAU_ARCHIVE_ANCHOR_POINT.ENTER_AUDIT_TIME.asc() : LAU_ARCHIVE_ANCHOR_POINT.MTIME.desc())
                .offset((req.getPage() - 1) * req.getSize())
                .limit(req.getSize())
                .fetch();
        List<Long> anchorIds = records.stream().map(t -> t.getId()).collect(Collectors.toList());

        Map<Long, LauArchiveAnchorPointRecord> anchorPointRecordMap = records.stream().collect(Collectors.toMap(LauArchiveAnchorPointRecord::getId, t -> t, (t1, t2) -> t2));
        Map<Integer, LauArchiveAnchorTypeConfigRecord> typeConfigsMap = this.queryAnchorTypeConfigsMap();
        List<LauArchiveAnchorPointOtherInfoRecord> otherInfoRecords = this.queryAnchorOtherInfos(anchorIds);
        Map<Long, List<LauArchiveAnchorPointOtherInfoRecord>> otherInfosMap = otherInfoRecords.stream().collect(Collectors.groupingBy(t -> t.getAid()));

        List<AnchorInfoBo> anchorInfoBos = ArchiveAnchorConvertor.MAPPER.convertAnchorPointPos2Bos(records);
        for (AnchorInfoBo anchorInfoBo : anchorInfoBos) {
            AnchorMngProc.buildExtra(anchorPointRecordMap.get(anchorInfoBo.getId()), anchorInfoBo);
            LauArchiveAnchorTypeConfigRecord typeConfigRecord = typeConfigsMap.get(anchorInfoBo.getType());
            if (typeConfigRecord != null) {
                anchorInfoBo.setUnderBoxDayIcon(typeConfigRecord.getUnderBoxDayIcon());
                anchorInfoBo.setStoryDayIcon(typeConfigRecord.getStoryDayIcon());
            }

            List<LauArchiveAnchorPointOtherInfoRecord> otherInfosOfAid = otherInfosMap.getOrDefault(anchorInfoBo.getAid(), Collections.emptyList());
            List<String> topImgUrls = otherInfosOfAid.stream().filter(t -> Objects.equals(t.getType(), AnchorAppImgType.TOP_IMAGE)).map(t -> t.getContent()).collect(Collectors.toList());
            List<String> appImgUrls = otherInfosOfAid.stream().filter(t -> Objects.equals(t.getType(), AnchorAppImgType.APP_IMAGE)).map(t -> t.getContent()).collect(Collectors.toList());
            anchorInfoBo.setTopImgUrls(topImgUrls);
            anchorInfoBo.setAppImgUrls(appImgUrls);
        }
        return new Pager(req.getPage(), total, anchorInfoBos);
    }

    public List<AnchorInfoBo> queryAnchorList(QueryAnchorReq req) {
        if (CollectionUtils.isEmpty(req.getAidList())) {
            return Collections.EMPTY_LIST;
        }
        Assert.isTrue(req.getAidCount() <= anchorListQueryMaxSize, "aid size 不能超过" + anchorListQueryMaxSize);

        // 条件
        Condition condition = DSL.trueCondition();
        condition = condition.and(!CollectionUtils.isEmpty(req.getAidList()) ? LAU_ARCHIVE_ANCHOR_POINT.AID.in(req.getAidList()) : DSL.noCondition());
        condition = condition.and(LAU_ARCHIVE_ANCHOR_POINT.IS_DELETED.eq(0));

        final var records = ad
                .selectFrom(LAU_ARCHIVE_ANCHOR_POINT)
                .where(condition)
                .orderBy(LAU_ARCHIVE_ANCHOR_POINT.MTIME.desc())
                .fetch();
        List<Long> anchorIds = records.stream().map(t -> t.getId()).collect(Collectors.toList());

        Map<Integer, LauArchiveAnchorTypeConfigRecord> typeConfigsMap = this.queryAnchorTypeConfigsMap();
        List<LauArchiveAnchorPointOtherInfoRecord> otherInfoRecords = this.queryAnchorOtherInfos(anchorIds);
        Map<Long, List<LauArchiveAnchorPointOtherInfoRecord>> otherInfosMap = otherInfoRecords.stream().collect(Collectors.groupingBy(t -> t.getAid()));

        List<AnchorInfoBo> anchorInfoBos = ArchiveAnchorConvertor.MAPPER.convertAnchorPointPos2Bos(records);
        for (AnchorInfoBo anchorInfoBo : anchorInfoBos) {
            LauArchiveAnchorTypeConfigRecord typeConfigRecord = typeConfigsMap.get(anchorInfoBo.getType());
            if (typeConfigRecord != null) {
                anchorInfoBo.setUnderBoxDayIcon(typeConfigRecord.getUnderBoxDayIcon());
                anchorInfoBo.setStoryDayIcon(typeConfigRecord.getStoryDayIcon());
            }

            List<LauArchiveAnchorPointOtherInfoRecord> otherInfosOfAid = otherInfosMap.getOrDefault(anchorInfoBo.getAid(), Collections.emptyList());
            List<String> topImgUrls = otherInfosOfAid.stream().filter(t -> Objects.equals(t.getType(), AnchorAppImgType.TOP_IMAGE)).map(t -> t.getContent()).collect(Collectors.toList());
            List<String> appImgUrls = otherInfosOfAid.stream().filter(t -> Objects.equals(t.getType(), AnchorAppImgType.APP_IMAGE)).map(t -> t.getContent()).collect(Collectors.toList());
            anchorInfoBo.setTopImgUrls(topImgUrls);
            anchorInfoBo.setAppImgUrls(appImgUrls);
        }
        return anchorInfoBos;
    }

    public Result<LauArchiveAnchorTypeConfigRecord> queryAnchorTypeConfigs() {

        Result<LauArchiveAnchorTypeConfigRecord> typeConfigRecords = ad.selectFrom(LAU_ARCHIVE_ANCHOR_TYPE_CONFIG)
                .where(LAU_ARCHIVE_ANCHOR_TYPE_CONFIG.IS_DELETED.eq(0))
                .fetch();
        return typeConfigRecords;
    }

    public Map<Integer, LauArchiveAnchorTypeConfigRecord> queryAnchorTypeConfigsMap() {
        Result<LauArchiveAnchorTypeConfigRecord> anchorTypeConfigRecords = this.queryAnchorTypeConfigs();
        Map<Integer, LauArchiveAnchorTypeConfigRecord> typeConfigRecordMap = anchorTypeConfigRecords.stream().collect(Collectors.toMap(t -> t.getType(), t -> t, (t1, t2) -> t2));
        return typeConfigRecordMap;
    }

    public LauArchiveAnchorTypeConfigRecord queryAnchorTypeConfigsMapByType(Integer type) {
        Map<Integer, LauArchiveAnchorTypeConfigRecord> typeConfigRecordMap = this.queryAnchorTypeConfigsMap();
        return typeConfigRecordMap.get(type);
    }

    public LauArchiveAnchorTypeConfigRecord queryAnchorTypeConfig(Integer type,Integer clueType,Integer bizCode){
        return ad.selectFrom(LAU_ARCHIVE_ANCHOR_TYPE_CONFIG)
                .where(LAU_ARCHIVE_ANCHOR_TYPE_CONFIG.TYPE.eq(type))
                .and(LAU_ARCHIVE_ANCHOR_TYPE_CONFIG.CLUE_TYPE.eq(clueType))
                .and(LAU_ARCHIVE_ANCHOR_TYPE_CONFIG.BIZ_CODE.eq(bizCode))
                .fetchOne();
    }

    public Integer updateAvidByAnchorId(Long anchorId, Long avid) {
        return ad.update(LAU_ARCHIVE_ANCHOR_POINT)
                .set(LAU_ARCHIVE_ANCHOR_POINT.AID, avid)
                .where(LAU_ARCHIVE_ANCHOR_POINT.ID.eq(anchorId))
                .execute();
    }
    public List<LauArchiveAnchorPointRecord> queryAfterId(Long id, List<Long> idList, Integer auditType, Integer size) {
        SelectConditionStep<LauArchiveAnchorPointRecord> resultQuery = ad.selectFrom(LAU_ARCHIVE_ANCHOR_POINT)
                .where(LAU_ARCHIVE_ANCHOR_POINT.ID.gt(id));

        if(!CollectionUtils.isEmpty(idList)){
            resultQuery.and(LAU_ARCHIVE_ANCHOR_POINT.ID.in(idList));
        }
        if(null != auditType){
            resultQuery.and(LAU_ARCHIVE_ANCHOR_POINT.AUDIT_TYPE.eq(auditType));
        }

        return resultQuery.orderBy(LAU_ARCHIVE_ANCHOR_POINT.ID.asc())
                .limit(size).fetch();
    }

    public void batchUpdate(List<LauArchiveAnchorPointRecord> updateList){
        ad.batchUpdate(updateList).execute();
    }
}
