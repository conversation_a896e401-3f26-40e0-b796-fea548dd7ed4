syntax = "proto3";

package bilibili.app.card.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/app/card;api";
option java_multiple_files = true;
option java_package = "com.bapis.app.card";
option objc_class_prefix = "BAPICard";

//重新生成此proto文件时需要修改下面的import为:import "ad.proto";
//生成完毕后记得修改回来哦~
import "app/card/ad.proto";
// import "github.com/gogo/protobuf/gogoproto/gogo.proto";

message Base {
    string card_type                     = 1;
    string card_goto                     = 2;
    string goto                          = 3;
    string param                         = 4;
    string cover                         = 5;
    string title                         = 6;
    string uri                           = 7;
    ThreePoint three_point               = 8;
    Args args                            = 9;
    PlayerArgs player_args               = 10;
    int64 idx                            = 11;
    AdInfo ad_info                       = 12;
    Mask mask                            = 13;
    string from_type                     = 14;
    repeated ThreePointV2 three_point_v2 = 15;
    repeated ThreePointV3 three_point_v3 = 16;
    Button desc_button                   = 17;
    ThreePointV4 three_point_v4          = 18;
    UpArgs up_args                       = 19;
    string track_id = 20;
}

message UpArgs {
    int64 up_id    = 1;
    string up_name = 2;
    string up_face = 3;
    int64 selected = 4;
}

message Button {
    string text     = 1;
    string param    = 2;
    string uri      = 3;
    string event    = 4;
    int32 selected  = 5;
    int32 type      = 6;
    string event_v2 = 7;
    // 新关注组件
    Relation relation = 8;
}

message ThreePoint {
    repeated DislikeReason dislike_reasons = 1;
    repeated DislikeReason feedbacks       = 2;
    int32 watch_later                      = 3;
}

message DislikeReason {
    int64 id    = 1;
    string name = 2;
}

message Args {
    int32 type          = 1;
    int64 up_id         = 2;
    string up_name      = 3;
    int32 rid           = 4;
    string rname        = 5;
    int64 tid           = 6;
    string tname        = 7;
    string track_id     = 8;
    string state        = 9;
    int32 converge_type = 10;
    int64 aid           = 11;
}

message PlayerArgs {
    int32 is_live    = 1;
    int64 aid        = 2;
    int64 cid        = 3;
    int32 sub_type   = 4;
    int64 room_id    = 5;
    int64 ep_id      = 7;
    int32 is_preview = 8;
    string type      = 9;
    int64 duration   = 10;
    int64 season_id  = 11;
}

message Mask {
    Avatar avatar = 1;
    Button button = 2;
}

message Avatar {
    string cover        = 1;
    string text         = 2;
    string uri          = 3;
    int32 type          = 4;
    string event        = 5;
    string event_v2     = 6;
    int32 defalut_cover = 7;
}

message ThreePointV2 {
    string title                   = 1;
    string subtitle                = 2;
    repeated DislikeReason reasons = 3;
    string type                    = 4;
    int64 id                       = 5;
}

message ThreePointV3 {
    string title                   = 1;
    string selected_title          = 2;
    string subtitle                = 3;
    repeated DislikeReason reasons = 4;
    string type                    = 5;
    int64 id                       = 6;
    int32 selected                 = 7;
    string icon                    = 8;
    string selected_icon           = 9;
    string url                     = 10;
    int32 default_id               = 11;
}

message ThreePointV4 {
    SharePlane share_plane = 1;
    WatchLater watch_later = 2;
}

message SharePlane {
    string title          = 1;
    string share_subtitle = 2;
    string desc           = 3;
    string cover          = 4;
    int64 aid             = 5;
    string bvid           = 6;
    // 分享的渠道如："weibo": true
    map<string, bool> share_to = 7;
    string author              = 8;
    int64 author_id            = 9;
    string short_link          = 10;
    string play_number         = 11;
    int64 first_cid            = 12;
}

message InlineProgressBar {
    string icon_drag      = 1;
    string icon_drag_hash = 2;
    string icon_stop      = 3;
    string icon_stop_hash = 4;
}

message WatchLater {
    int64 aid   = 1;
    string bvid = 2;
}

message ReasonStyle {
    string text               = 1;
    string text_color         = 2;
    string bg_color           = 3;
    string border_color       = 4;
    string icon_url           = 5;
    string text_color_night   = 6;
    string bg_color_night     = 7;
    string border_color_night = 8;
    string icon_night_url     = 9;
    int32 bg_style            = 10;
    string uri                = 11;
    string icon_bg_url        = 12;
    string event              = 13;
    string event_v2           = 14;
    int32 right_icon_type     = 15;
    string left_icon_type     = 16;
}

message LikeButton {
    int64 Aid       = 1;
    int32 count     = 2;
    bool show_count = 3;
    string event    = 4;
    int32 selected  = 5;
    string event_v2 = 6;
}

message Up {
    int64 id            = 1;
    string name         = 2;
    string desc         = 3;
    Avatar avatar       = 4;
    int32 official_icon = 5;
    Button desc_button  = 6;
    string cooperation  = 7;
}

message ChannelBadge {
    string text        = 1;
    string icon_bg_url = 2;
}

// 新关注组件
message Relation {
    // 按钮展示文案：0或者字段不存在：未关注、1：已关注、2：被关注、3：互相关注
    int32 status = 1;
    // 用户关注UP主：0或者字段不存在：未关注、1：已关注
    int32 is_follow = 2;
    // UP主关注用户：0或者字段不存在：未被关注、1：被关注
    int32 is_followed = 3;
}

message PanelMeta {
    int32 panel_type                             = 1;
    string share_origin                          = 2;
    string share_id                              = 3;
    repeated FunctionalButton functional_buttons = 4;
}

message FunctionalButton {
    int32 type                                 = 1;
    repeated FunctionalButtonMeta button_metas = 2;
}

message FunctionalButtonMeta {
    string icon          = 1;
    string text          = 2;
    string button_status = 3;
    string toast         = 4;
}