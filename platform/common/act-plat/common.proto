syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

package actplat.service.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/platform/common.act.plat;common";
option (gogoproto.goproto_getters_all) = false;
option java_package = "com.bapis.platform.common.act.plat";
option java_multiple_files = true;

// activity 活动
message Activity {
    // name 活动名
    string name = 1 [(gogoproto.jsontag) = 'name', json_name = "name"];
    // description 活动描述
    string description = 2 [(gogoproto.jsontag) = 'description', json_name = "description"];
    // contact string seperated by "," 活动联系人，逗号分隔
    string contact = 3 [(gogoproto.jsontag) = 'contact', json_name = "contact"];
    // start time of activity 活动开始时间，开始后才会开始积分
    int64 startTime = 4 [(gogoproto.jsontag) = 'start_time', json_name = "start_time"];
    // end time of activity 活动结束时间，结束后停止积分
    int64 endTime = 5 [(gogoproto.jsontag) = 'end_time', json_name = "end_time"];
    // token for authorization 暂时未使用
    string token = 6 [(gogoproto.jsontag) = 'token', json_name = "token"];
    // rawContent 活动额外配置，暂时未使用
    string rawContent = 7 [(gogoproto.jsontag) = 'content', json_name = "content"];
    // activity id 活动id
    int64 id = 8 [(gogoproto.jsontag) = 'id', json_name = "id"];
    // closed 活动是否冻结，0 - 未冻结，1 - 已冻结，活动冻结期间不积分
    int64 closed = 9 [(gogoproto.jsontag) = 'closed', json_name = "closed"];
}

// Counter 计数器，一个计数器对应一条行为数据流，通常对应一个活动任务
message Counter {
    // 计数器名称
    string name = 1 [(gogoproto.jsontag) = 'name', json_name = "name"];
    // 所属活动名
    string activity = 2 [(gogoproto.jsontag) = 'activity', json_name = "activity"];
    // 关联databus数据源
    string dataSource = 3 [(gogoproto.jsontag) = 'data_source', json_name = "data_source"];
    // Deprecate 是否存储计数记录，已废弃，均记录
    bool needHistory = 4 [(gogoproto.jsontag) = 'need_history', json_name = "need_history"];
    // type可定制特殊counter
    // 对应列表见：common/common.go: CounterType
    CounterType type = 5 [(gogoproto.jsontag) = 'type', json_name = "type"];
    // 计数器内容，使用json串存储，不超过2048字符
    string rawContent = 6 [(gogoproto.jsontag) = 'content', json_name = "content"];
    // id
    int64 id = 7 [(gogoproto.jsontag) = 'id', json_name = "id"];
    // closed 计数器是否冻结，0 - 未冻结，1 - 已冻结，活动冻结期间不积分
    int64 closed = 8 [(gogoproto.jsontag) = 'closed', json_name = "closed"];
}

// 过滤器，若计数器counter配置了过滤器，则只有满足过滤器条件的行为才会积分
message Filter {
    // 名称
    string name = 1 [(gogoproto.jsontag) = 'name', json_name = "name"];
    // 所属活动
    string activity = 2 [(gogoproto.jsontag) = 'activity', json_name = "activity"];
    // 所属计数器
    string counter = 3 [(gogoproto.jsontag) = 'counter', json_name = "counter"];
    // 绑定的字段名称
    string field = 4 [(gogoproto.jsontag) = 'field', json_name = "field"];
    // 过滤器种类
    FilterType type = 5 [(gogoproto.jsontag) = 'type', json_name = "type"];
    // 过滤器内容，数据库中使用json存储， 不能超过2048字节
    string rawContent = 6 [(gogoproto.jsontag) = 'content', json_name = "content"];
    // id
    int64 id = 7 [(gogoproto.jsontag) = 'id', json_name = "id"];
    // closed
    int64 closed = 8 [(gogoproto.jsontag) = 'closed', json_name = "closed"];
}

// 数据集合，关联多个filter，提供集合功能
message Set{
    //id
    int64 id = 1 [(gogoproto.jsontag) = 'id', json_name = "id"];
    // 名称
    string name = 2 [(gogoproto.jsontag) = 'name', json_name = "name"];
    // activity
    string activity = 3 [(gogoproto.jsontag) = 'activity', json_name = "activity"];
    //存储value的地方，目前指定"taishan"和"tidb"
    SetDbType dbType = 4 [(gogoproto.jsontag) = 'db_type', json_name = "db_type"];
    //描述信息
    string description = 5 [(gogoproto.jsontag) = 'description', json_name = "description"];
    // 是否删除
    int32 deleted = 6 [(gogoproto.jsontag) = 'deleted', json_name = "deleted"];
}
message FilterContent {
    repeated SetContentInFilter sets = 1 [(gogoproto.jsontag) = 'sets', json_name = "sets"];
}
message SetContentInFilter {
    //set name
    string name = 1 [(gogoproto.jsontag) = 'name', json_name = "name"];
    // activity
    string activity = 2 [(gogoproto.jsontag) = 'activity', json_name = "activity"];
    //describe 描述信息
    string description = 3 [(gogoproto.jsontag) = 'description', json_name = "description"];
    //dbType, set的值要存储到哪里，目前有tidb和taishan可以选择。
    SetDbType dbType = 4 [(gogoproto.jsontag) = 'db_type', json_name = "db_type"];
    // 是否删除
    int32 deleted = 5 [(gogoproto.jsontag) = 'deleted', json_name = "deleted"];
}

// Formula 公式
message Formula {
    // 名称
    string name = 1 [(gogoproto.jsontag) = 'name', json_name = "name"];
    // 所属活动
    string activity = 2 [(gogoproto.jsontag) = 'activity', json_name = "activity"];
    // 公式种类
    FormulaType type = 3 [(gogoproto.jsontag) = 'type', json_name = "type"];
    // 公式内容，数据库中使用json存储，不能超过2048字节
    string rawContent = 4 [(gogoproto.jsontag) = 'content', json_name = "content"];
    // id
    int64 id = 5 [(gogoproto.jsontag) = 'id', json_name = "id"];
    // closed
    int64 closed = 6 [(gogoproto.jsontag) = 'closed', json_name = "closed"];
}

// Notifier 通知
message Notifier {
    // 名称
    string name = 1 [(gogoproto.jsontag) = 'name', json_name = "name"];
    // 所属活动
    string activity = 2 [(gogoproto.jsontag) = 'activity', json_name = "activity"];
    // 所属计数器
    string counter = 3 [(gogoproto.jsontag) = 'counter', json_name = "counter"];
    // Notifier种类
    NotifierType type = 4 [(gogoproto.jsontag) = 'type', json_name = "type"];
    // 过滤器内容，数据库中使用json存储，不能超过2048字节
    string rawContent = 5 [(gogoproto.jsontag) = 'content', json_name = "content"];
    // id
    int64 id = 6 [(gogoproto.jsontag) = 'id', json_name = "id"];
    // closed
    int64 closed = 7 [(gogoproto.jsontag) = 'closed', json_name = "closed"];
}

// 计数器的类别
enum CounterType {
    CounterUnknown = 0;
    // 基础计数，content：BasicCounterContent
    BasicCounter = 1;
    // 基础累加, content: BasicAdderContent
    BasicAdder = 2;
    // 观看时长累加，content：BasicCounterContent
    HeartbeatAdder = 3;
    // 计数器总值，不计history, content: BasicAdderContent
    SumAdder = 4;
    // 计算指定int64字段不重复值出现过多少次, content: UniqCounterContent
    UniqCounter = 5;
    // 带有规则公式的counter: formula adder
    FormulaAdder = 6;
    //条件判断的counter
    FormulaCounter = 7;
    // mid维度累加无去重
    MidAdder = 8;
    // mid维度累加唯一计数
    MidUniq = 9;
}

// 过滤器类型，数字越大执行成本越高，实际执行时按类型排序后执行
// 1-999     仅需要内存访问
// 1001-1999 需要访问缓存，大概率缓存命中的数据，速度较快的接口，平均响应时间<1ms
// 2001-2999 需要访问较慢的接口或者数据库，平均响应时间<10ms
// 3001-3999 活动定制过滤器，通常业务强相关接口，平均响应时间<250ms
enum FilterType {
    FilterUnknown = 0;
    // 范围过滤器，content为IntRange
    RangeFilterInt = 1;
    // 范围过滤器，content为StrRange
    RangeFilterStr = 2;
    // 匹配过滤器，content为[]int64
    MemSetFilterInt = 3;
    // 匹配过滤器，content为[]string
    MemSetFilterStr = 4;
    // 反向匹配过滤器，content为[]int64
    NotInMemSetFilterInt = 5;
    // 反向匹配过滤器，content为[]string
    NotInMemSetFilterStr = 6;
    // Deprecate 列表过滤器，content为空，数量1000以内
    SetFilterInt = 1001;
    // 列表过滤器，content为空
    SetFilterStr = 1002;
    // 列表过滤器，content为空，可接受>1W数量；可用于过滤参与活动用户mid、过滤参与积分的稿件aid等
    BigSetFilterInt = 1003;
    //替代filterSet,列表过滤器，content为空，可接受>1W数量；可用于过滤参与活动用户mid、过滤参与积分的稿件aid等
    SetInt = 1004;
    // 风控过滤，content为空
    AccountControlFilter = 2001;
    // 普通tag过滤，content为[]int64；可支持有用户范围的点赞/投稿/分享等
    TagFilter = 2002;
    // 手机号过滤，content为空；账号绑定手机号的
    PhoneFilter = 2003;
    // 开学季活动——判断用户是否发布了自己学校的稿件，content为空
    CollegeAidIsActivityFilter = 3001;
    // S10 分享过滤，直播间 share_id = 86, 稿件无限制
    S10ShareFilter = 3002;
    // s10 是否加分filter
    S10AccountControlFilter = 3003;
    // 春节卡片春节虚拟形象投稿
    BCutArchiveAvatarFilter = 3004;
    // 必剪虚拟形象稿件
    BCutVUpFilter = 3005;
    // 邀请好友过滤器
    InviteFilter = 3006;

}

// Notifier种类
enum NotifierType {
    NotifierUnknown = 0;
    // 当前周期值大于等于x时，发送通知
    LargerThan = 1;
    // 每一条积分记录均发送通知
    Any = 2;
    // 发送 diff，可以是mid
    TotalDiffAndKeyMid = 3;
    //发送 diff，key是counter_id
    TotalDiffAndKeyCounterId = 4;
    //用户总积分大于x
    LargerThanTypeTotal = 5;
    // 每x次发送通知
    EachLargerThan = 6;
}
// 带有规则公式的多counter 计算规则
enum FormulaMultiAdderCalcType {
    FormulaMultiAdderSet = 0; // 直接覆盖
    FormulaMultiAdderAdd = 1; // 累加
}

// Formula类型
enum FormulaType {
    FormulaUnknown = 0;
    // 线性公式
    LinearFormula = 1;
}
// set的db type类型
enum SetDbType {
    SetDbUnknown = 0;
    //tidb
    TIDB = 1;
    //泰山
    TAISHAN = 2;
}

// 计数器的计数周期配置，最常用为 天级别周期，即会记录每天的counter计数值
message CounterRefresh {
    //cron standard 字符串， 接受两种表述：
    //- Standard crontab specs, e.g. "* * * * ?"
    //- Descriptors, e.g. "@midnight", "@every 1h30m"
    // ┌───────────── min (0 - 59)
    // │ ┌────────────── hour (0 - 23)
    // │ │ ┌─────────────── day of month (1 - 31)
    // │ │ │ ┌──────────────── month (1 - 12)
    // │ │ │ │ ┌───────────────── day of week (0 - 6) (0 to 6 are Sunday to
    // │ │ │ │ │                  Saturday, or use names; 7 is also Sunday)
    // * * * * *
    // e.g. 天级别："0 0 * * *"
    string cron = 1 [(gogoproto.jsontag) = 'refresh_cron', json_name = "refresh_cron"];
    // 是否在周期内查重
    bool duplicateCheck = 2 [(gogoproto.jsontag) = 'duplicate_check', json_name = "duplicate_check"];
    // 查重的字段
    string duplicateCheckField = 3 [(gogoproto.jsontag) = 'duplicate_check_field', json_name = "duplicate_check_field"];
    // 是否在周期内限制计数器上限
    int64 limit = 4 [(gogoproto.jsontag) = 'limit', json_name = "limit"];
}

// 计数器的暴击功能，常用场景：邀请用户每次1积分，
message CounterCrit {
    // 第N次提供暴击
    int64 critCount = 1 [(gogoproto.jsontag) = 'crit_count', json_name = "crit_count"];
    // 暴击倍数的范围
    IntRange randRange = 2 [(gogoproto.jsontag) = 'rand_range', json_name = "rand_range"];
}

// range of int [min, max]，左闭右闭，在范围内的才积分
message IntRange {
    int64 min = 1 [(gogoproto.jsontag) = 'min', json_name = "min"];
    int64 max = 2 [(gogoproto.jsontag) = 'max', json_name = "max"];
}

// range of float [min, max]，左闭右闭，在范围内的才积分
message FloatRange {
    float min = 1 [(gogoproto.jsontag) = 'min', json_name = "min"];
    float max = 2 [(gogoproto.jsontag) = 'max', json_name = "max"];
}

// range of string [min, max]，左闭右闭，在范围内的才积分
message StrRange {
    string min = 1 [(gogoproto.jsontag) = 'min', json_name = "min"];
    string max = 2 [(gogoproto.jsontag) = 'max', json_name = "max"];
}

// 历史内容，存入泰山时序列化为json
message HistoryContent {
    // 计数器实际增加的数量（可能有随机或者暴击）
    int64 count = 1 [(gogoproto.jsontag) = 'count', json_name = "count"];
    // 行为的时间戳，用于确认幂等
    int64 timestamp = 2 [(gogoproto.jsontag) = 'timestamp', json_name = "timestamp"];
    // 输入的数据
    string source = 3 [(gogoproto.jsontag) = 'source', json_name = "source"];
    // 字符串形式的原始泰山的key
    RawTaiShanKey RawTsKey = 4 [(gogoproto.jsontag) = "raw_ts_key,omitempty", json_name = "raw_ts_key"];
}

// 字符串形式的原始泰山的key
message RawTaiShanKey {
    // 用户uid
    int64 Mid        = 1 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
    // 计数器ID
    int64 CounterID    = 2 [(gogoproto.jsontag) = 'counter_id', json_name = "counter_id"];
    // 时间戳
    int64 TimeStamp  = 3 [(gogoproto.jsontag) = 'timestamp', json_name = "timestamp"];
    // 其他拓展字段
    repeated string Extra = 4 [(gogoproto.jsontag) = 'extra,omitempty', json_name = "extra"];
    // 泰山key的业务熟悉类型（主要用于区分act_plat_counter表混用的问题）
    string BizType        = 5 [(gogoproto.jsontag) = 'biz_type,omitempty', json_name = "biz_type"];
}

// counter内容，存入taishan时以8或16位存储
message CounterContent {
    // Counter值
    int64 value = 1 [(gogoproto.jsontag) = 'count', json_name = "count"];
    // 时间戳，消费时间
    int64 consumeTs = 2 [(gogoproto.jsontag) = 'consume_ts', json_name = "consume_ts"];
    // 数据时间，最后一次加分时间
    int64 lastTs = 3 [(gogoproto.jsontag) = 'last_ts', json_name = "last_ts"];
    // extra 用于记录额外信息 例如formulaMultiAdder 类型的counter需要记录每个指标的值
    string extra = 4 [(gogoproto.jsontag) = 'extra', json_name = "extra"];
    // 字符串形式的原始泰山的key
    RawTaiShanKey RawTsKey = 5 [(gogoproto.jsontag) = "raw_ts_key,omitempty", json_name = "raw_ts_key"];
}

// redis中的history， 用于补全taishan的
message RedisRecords {
    // 可选项，在counter定义中指定，在时长统计中使用startts，用以给心跳包去重
    string uniqKey = 1 [(gogoproto.jsontag) = 'uniq_key', json_name = "uniq_key"];
    // 该uniqkey需要累加的值
    int64 counterVal = 2 [(gogoproto.jsontag) = 'counter_val', json_name = "counter_val"];
    // 时间戳，最后一条记录
    int64 consumeTs = 3 [(gogoproto.jsontag) = 'consume_ts', json_name = "consume_ts"];
    // 数据时间，最后一次加分时间
    int64 lastTs = 4 [(gogoproto.jsontag) = 'last_ts', json_name = "last_ts"];
}