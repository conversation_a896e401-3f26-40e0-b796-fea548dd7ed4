<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.material.center.repository.mysql_business.LauDynamicBluelinkResolveContentMapper">

  <resultMap id="BaseResultMap"
    type="com.bilibili.mgk.material.center.service.bluelink.model.DynamicBluelinkResolveContent">
    <id property="id" column="id" jdbcType="BIGINT"/>
    <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
    <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    <result property="accountId" column="account_id" jdbcType="INTEGER"/>
    <result property="customerId" column="customer_id" jdbcType="INTEGER"/>
    <result property="agentId" column="agent_id" jdbcType="INTEGER"/>
    <result property="campaignId" column="campaign_id" jdbcType="INTEGER"/>
    <result property="unitId" column="unit_id" jdbcType="INTEGER"/>
    <result property="creativeId" column="creative_id" jdbcType="INTEGER"/>
    <result property="upNickname" column="up_nickname" jdbcType="VARCHAR"/>
    <result property="upFace" column="up_face" jdbcType="VARCHAR"/>
    <result property="mid" column="mid" jdbcType="BIGINT"/>
    <result property="bluelinkSeqId" column="bluelink_seq_id" jdbcType="INTEGER"/>
    <result property="rawText" column="raw_text" jdbcType="VARCHAR"/>
    <result property="textSegmentId" column="text_segment_id" jdbcType="INTEGER"/>
    <result property="status" column="status" jdbcType="TINYINT"/>
    <result property="auditStatus" column="audit_status" jdbcType="TINYINT"/>
    <result property="reason" column="reason" jdbcType="VARCHAR"/>
    <result property="dynamicId" column="dynamic_id" jdbcType="VARCHAR"/>
    <result property="dynamicBizId" column="dynamic_biz_id" jdbcType="VARCHAR"/>
    <result property="dynamicStatus" column="dynamic_status" jdbcType="INTEGER"/>
    <result property="dynamicContent" column="dynamic_content" jdbcType="VARCHAR"/>
    <result property="auditRecordId" column="audit_record_id" jdbcType="BIGINT"/>
    <result property="componentType" column="component_type" jdbcType="TINYINT"/>
    <result property="conversionUrlType" column="conversion_url_type" jdbcType="TINYINT"/>
    <result property="conversionUrlPageId" column="conversion_url_page_id" jdbcType="BIGINT"/>
    <result property="conversionUrl" column="conversion_url" jdbcType="VARCHAR"/>
    <result property="conversionUrlText" column="conversion_url_text" jdbcType="VARCHAR"/>
    <result property="conversionShortUrl" column="conversion_short_url" jdbcType="VARCHAR"/>
    <result property="gameBaseId" column="game_base_id" jdbcType="INTEGER"/>
    <result property="gamePlatformType" column="game_platform_type" jdbcType="TINYINT"/>
    <result property="iosUrlType" column="ios_url_type" jdbcType="TINYINT"/>
    <result property="iosUrlPageId" column="ios_url_page_id" jdbcType="BIGINT"/>
    <result property="iosUrl" column="ios_url" jdbcType="VARCHAR"/>
    <result property="androidUrlType" column="android_url_type" jdbcType="TINYINT"/>
    <result property="androidUrlPageId" column="android_url_page_id" jdbcType="BIGINT"/>
    <result property="androidUrl" column="android_url" jdbcType="VARCHAR"/>
    <result property="iosSchemaUrl" column="ios_schema_url" jdbcType="VARCHAR"/>
    <result property="androidSchemaUrl" column="android_schema_url" jdbcType="VARCHAR"/>
    <result property="triggerTime" column="trigger_time" jdbcType="TIMESTAMP"/>
    <result property="iosAppPackageId" column="ios_app_package_id" jdbcType="INTEGER"/>
    <result property="androidAppPackageId" column="android_app_package_id" jdbcType="INTEGER"/>
    <result property="qualificationIds" column="qualification_ids" jdbcType="VARCHAR"/>
    <result property="subPkg" column="sub_pkg" jdbcType="TINYINT"/>
    <result property="clueType" column="clue_type" jdbcType="TINYINT"/>
    <result property="clueData" column="clue_data" jdbcType="VARCHAR"/>
    <result property="customizedImpUrl" column="customized_imp_url" jdbcType="VARCHAR"/>
    <result property="customizedClickUrl" column="customized_click_url" jdbcType="VARCHAR"/>
    <result property="autoFillText" column="auto_fill_text" jdbcType="VARCHAR"/>
    <result property="autoFillLink" column="auto_fill_link" jdbcType="VARCHAR"/>
    <result property="productId" column="product_id" jdbcType="BIGINT"/>
    <result property="productShortUrl" column="product_short_url" jdbcType="VARCHAR"/>
    <result property="bizCode" column="biz_code" jdbcType="TINYINT"/>
    <result property="contactType" column="contact_type" jdbcType="TINYINT"/>
    <result property="appSubType" column="app_sub_type" jdbcType="TINYINT"/>
    <result property="isAndroidAppDirect" column="is_android_app_direct" jdbcType="TINYINT"/>
    <result property="contentInEdit" column="content_in_edit" jdbcType="VARCHAR"/>
    <result property="auditStatusInEdit" column="audit_status_in_edit" jdbcType="TINYINT"/>
    <result property="linkIcon" column="link_icon" jdbcType="VARCHAR"/>
    <result property="capsuleIcon" column="capsule_icon" jdbcType="VARCHAR"/>
    <result property="capsuleText" column="capsule_text" jdbcType="VARCHAR"/>
  </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>


  <sql id="Base_Column_List">
    id,deleted,ctime,
        mtime,account_id,customer_id,
        agent_id,campaign_id,unit_id,
        creative_id,up_nickname,up_face,
        mid,bluelink_seq_id,raw_text,
        text_segment_id,status,audit_status,
        reason,dynamic_id,dynamic_biz_id,
        dynamic_status,dynamic_content,audit_record_id,
        component_type,conversion_url_type,conversion_url_page_id,
        conversion_url,conversion_url_text,conversion_short_url,
        game_base_id,game_platform_type,ios_url_type,
        ios_url_page_id,ios_url,android_url_type,
        android_url_page_id,android_url,ios_schema_url,
        android_schema_url,trigger_time,ios_app_package_id,
        android_app_package_id,qualification_ids,sub_pkg,
        clue_type,clue_data,customized_imp_url,
        customized_click_url,auto_fill_text,auto_fill_link,
        product_id,product_short_url,biz_code,
        contact_type,app_sub_type,is_android_app_direct,
        content_in_edit,audit_status_in_edit,
        capsule_icon,capsule_text
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from lau_dynamic_bluelink_resolve_content
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="selectByDynamicId"
    resultType="java.util.ArrayList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from lau_dynamic_bluelink_resolve_content
    where dynamic_id = #{dynamicId,jdbcType=VARCHAR}
  </select>

  <select id="selectByDynamicIds"
    resultType="java.util.ArrayList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from lau_dynamic_bluelink_resolve_content
    where dynamic_id in
    <foreach close=")" collection="dynamicIds" item="listItem" open="(" separator=",">
      #{listItem}
    </foreach>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from lau_dynamic_bluelink_resolve_content
    where id = #{id,jdbcType=BIGINT}
  </delete>


  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="com.bilibili.mgk.material.center.service.bluelink.model.DynamicBluelinkResolveContent"
    useGeneratedKeys="true">
    insert into lau_dynamic_bluelink_resolve_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="deleted != null">deleted,</if>
      <if test="ctime != null">ctime,</if>
      <if test="mtime != null">mtime,</if>
      <if test="accountId != null">account_id,</if>
      <if test="customerId != null">customer_id,</if>
      <if test="agentId != null">agent_id,</if>
      <if test="campaignId != null">campaign_id,</if>
      <if test="unitId != null">unit_id,</if>
      <if test="creativeId != null">creative_id,</if>
      <if test="upNickname != null">up_nickname,</if>
      <if test="upFace != null">up_face,</if>
      <if test="mid != null">mid,</if>
      <if test="bluelinkSeqId != null">bluelink_seq_id,</if>
      <if test="rawText != null">raw_text,</if>
      <if test="textSegmentId != null">text_segment_id,</if>
      <if test="status != null">status,</if>
      <if test="auditStatus != null">audit_status,</if>
      <if test="reason != null">reason,</if>
      <if test="dynamicId != null">dynamic_id,</if>
      <if test="dynamicBizId != null">dynamic_biz_id,</if>
      <if test="dynamicStatus != null">dynamic_status,</if>
      <if test="dynamicContent != null">dynamic_content,</if>
      <if test="auditRecordId != null">audit_record_id,</if>
      <if test="componentType != null">component_type,</if>
      <if test="conversionUrlType != null">conversion_url_type,</if>
      <if test="conversionUrlPageId != null">conversion_url_page_id,</if>
      <if test="conversionUrl != null">conversion_url,</if>
      <if test="conversionUrlText != null">conversion_url_text,</if>
      <if test="conversionShortUrl != null">conversion_short_url,</if>
      <if test="gameBaseId != null">game_base_id,</if>
      <if test="gamePlatformType != null">game_platform_type,</if>
      <if test="iosUrlType != null">ios_url_type,</if>
      <if test="iosUrlPageId != null">ios_url_page_id,</if>
      <if test="iosUrl != null">ios_url,</if>
      <if test="androidUrlType != null">android_url_type,</if>
      <if test="androidUrlPageId != null">android_url_page_id,</if>
      <if test="androidUrl != null">android_url,</if>
      <if test="iosSchemaUrl != null">ios_schema_url,</if>
      <if test="androidSchemaUrl != null">android_schema_url,</if>
      <if test="triggerTime != null">trigger_time,</if>
      <if test="iosAppPackageId != null">ios_app_package_id,</if>
      <if test="androidAppPackageId != null">android_app_package_id,</if>
      <if test="qualificationIds != null">qualification_ids,</if>
      <if test="subPkg != null">sub_pkg,</if>
      <if test="clueType != null">clue_type,</if>
      <if test="clueData != null">clue_data,</if>
      <if test="customizedImpUrl != null">customized_imp_url,</if>
      <if test="customizedClickUrl != null">customized_click_url,</if>
      <if test="autoFillText != null">auto_fill_text,</if>
      <if test="autoFillLink != null">auto_fill_link,</if>
      <if test="productId != null">product_id,</if>
      <if test="productShortUrl != null">product_short_url,</if>
      <if test="bizCode != null">biz_code,</if>
      <if test="contactType != null">contact_type,</if>
      <if test="appSubType != null">app_sub_type,</if>
      <if test="isAndroidAppDirect != null">is_android_app_direct,</if>
      <if test="contentInEdit != null">content_in_edit,</if>
      <if test="auditStatusInEdit != null">audit_status_in_edit,</if>
      <if test="linkIcon != null">link_icon,</if>
      <if test="capsuleIcon != null">capsule_icon,</if>
      <if test="capsuleText != null">capsule_text,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id,jdbcType=BIGINT},</if>
      <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
      <if test="ctime != null">#{ctime,jdbcType=TIMESTAMP},</if>
      <if test="mtime != null">#{mtime,jdbcType=TIMESTAMP},</if>
      <if test="accountId != null">#{accountId,jdbcType=INTEGER},</if>
      <if test="customerId != null">#{customerId,jdbcType=INTEGER},</if>
      <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
      <if test="campaignId != null">#{campaignId,jdbcType=INTEGER},</if>
      <if test="unitId != null">#{unitId,jdbcType=INTEGER},</if>
      <if test="creativeId != null">#{creativeId,jdbcType=INTEGER},</if>
      <if test="upNickname != null">#{upNickname,jdbcType=VARCHAR},</if>
      <if test="upFace != null">#{upFace,jdbcType=VARCHAR},</if>
      <if test="mid != null">#{mid,jdbcType=BIGINT},</if>
      <if test="bluelinkSeqId != null">#{bluelinkSeqId,jdbcType=INTEGER},</if>
      <if test="rawText != null">#{rawText,jdbcType=VARCHAR},</if>
      <if test="textSegmentId != null">#{textSegmentId,jdbcType=INTEGER},</if>
      <if test="status != null">#{status,jdbcType=TINYINT},</if>
      <if test="auditStatus != null">#{auditStatus,jdbcType=TINYINT},</if>
      <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
      <if test="dynamicId != null">#{dynamicId,jdbcType=VARCHAR},</if>
      <if test="dynamicBizId != null">#{dynamicBizId,jdbcType=VARCHAR},</if>
      <if test="dynamicStatus != null">#{dynamicStatus,jdbcType=INTEGER},</if>
      <if test="dynamicContent != null">#{dynamicContent,jdbcType=VARCHAR},</if>
      <if test="auditRecordId != null">#{auditRecordId,jdbcType=BIGINT},</if>
      <if test="componentType != null">#{componentType,jdbcType=TINYINT},</if>
      <if test="conversionUrlType != null">#{conversionUrlType,jdbcType=TINYINT},</if>
      <if test="conversionUrlPageId != null">#{conversionUrlPageId,jdbcType=BIGINT},</if>
      <if test="conversionUrl != null">#{conversionUrl,jdbcType=VARCHAR},</if>
      <if test="conversionUrlText != null">#{conversionUrlText,jdbcType=VARCHAR},</if>
      <if test="conversionShortUrl != null">#{conversionShortUrl,jdbcType=VARCHAR},</if>
      <if test="gameBaseId != null">#{gameBaseId,jdbcType=INTEGER},</if>
      <if test="gamePlatformType != null">#{gamePlatformType,jdbcType=TINYINT},</if>
      <if test="iosUrlType != null">#{iosUrlType,jdbcType=TINYINT},</if>
      <if test="iosUrlPageId != null">#{iosUrlPageId,jdbcType=BIGINT},</if>
      <if test="iosUrl != null">#{iosUrl,jdbcType=VARCHAR},</if>
      <if test="androidUrlType != null">#{androidUrlType,jdbcType=TINYINT},</if>
      <if test="androidUrlPageId != null">#{androidUrlPageId,jdbcType=BIGINT},</if>
      <if test="androidUrl != null">#{androidUrl,jdbcType=VARCHAR},</if>
      <if test="iosSchemaUrl != null">#{iosSchemaUrl,jdbcType=VARCHAR},</if>
      <if test="androidSchemaUrl != null">#{androidSchemaUrl,jdbcType=VARCHAR},</if>
      <if test="triggerTime != null">#{triggerTime,jdbcType=TIMESTAMP},</if>
      <if test="iosAppPackageId != null">#{iosAppPackageId,jdbcType=INTEGER},</if>
      <if test="androidAppPackageId != null">#{androidAppPackageId,jdbcType=INTEGER},</if>
      <if test="qualificationIds != null">#{qualificationIds,jdbcType=VARCHAR},</if>
      <if test="subPkg != null">#{subPkg,jdbcType=TINYINT},</if>
      <if test="clueType != null">#{clueType,jdbcType=TINYINT},</if>
      <if test="clueData != null">#{clueData,jdbcType=VARCHAR},</if>
      <if test="customizedImpUrl != null">#{customizedImpUrl,jdbcType=VARCHAR},</if>
      <if test="customizedClickUrl != null">#{customizedClickUrl,jdbcType=VARCHAR},</if>
      <if test="autoFillText != null">#{autoFillText,jdbcType=VARCHAR},</if>
      <if test="autoFillLink != null">#{autoFillLink,jdbcType=VARCHAR},</if>
      <if test="productId != null">#{productId,jdbcType=BIGINT},</if>
      <if test="productShortUrl != null">#{productShortUrl,jdbcType=VARCHAR},</if>
      <if test="bizCode != null">#{bizCode,jdbcType=TINYINT},</if>
      <if test="contactType != null">#{contactType,jdbcType=TINYINT},</if>
      <if test="appSubType != null">#{appSubType,jdbcType=TINYINT},</if>
      <if test="isAndroidAppDirect != null">#{isAndroidAppDirect,jdbcType=TINYINT},</if>
      <if test="contentInEdit != null">#{contentInEdit,jdbcType=VARCHAR},</if>
      <if test="auditStatusInEdit != null">#{auditStatusInEdit,jdbcType=TINYINT},</if>
      <if test="linkIcon != null">#{linkIcon,jdbcType=VARCHAR},</if>
      <if test="capsuleIcon != null">#{capsuleIcon,jdbcType=VARCHAR},</if>
      <if test="capsuleText != null">#{capsuleText,jdbcType=VARCHAR},</if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="com.bilibili.mgk.material.center.service.bluelink.model.DynamicBluelinkResolveContent">
    update lau_dynamic_bluelink_resolve_content
    <set>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="agentId != null">
        agent_id = #{agentId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="upNickname != null">
        up_nickname = #{upNickname,jdbcType=VARCHAR},
      </if>
      <if test="upFace != null">
        up_face = #{upFace,jdbcType=VARCHAR},
      </if>
      <if test="mid != null">
        mid = #{mid,jdbcType=BIGINT},
      </if>
      <if test="bluelinkSeqId != null">
        bluelink_seq_id = #{bluelinkSeqId,jdbcType=INTEGER},
      </if>
      <if test="rawText != null">
        raw_text = #{rawText,jdbcType=VARCHAR},
      </if>
      <if test="textSegmentId != null">
        text_segment_id = #{textSegmentId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="dynamicId != null">
        dynamic_id = #{dynamicId,jdbcType=VARCHAR},
      </if>
      <if test="dynamicBizId != null">
        dynamic_biz_id = #{dynamicBizId,jdbcType=VARCHAR},
      </if>
      <if test="dynamicStatus != null">
        dynamic_status = #{dynamicStatus,jdbcType=INTEGER},
      </if>
      <if test="dynamicContent != null">
        dynamic_content = #{dynamicContent,jdbcType=VARCHAR},
      </if>
      <if test="auditRecordId != null">
        audit_record_id = #{auditRecordId,jdbcType=BIGINT},
      </if>
      <if test="componentType != null">
        component_type = #{componentType,jdbcType=TINYINT},
      </if>
      <if test="conversionUrlType != null">
        conversion_url_type = #{conversionUrlType,jdbcType=TINYINT},
      </if>
      <if test="conversionUrlPageId != null">
        conversion_url_page_id = #{conversionUrlPageId,jdbcType=BIGINT},
      </if>
      <if test="conversionUrl != null">
        conversion_url = #{conversionUrl,jdbcType=VARCHAR},
      </if>
      <if test="conversionUrlText != null">
        conversion_url_text = #{conversionUrlText,jdbcType=VARCHAR},
      </if>
      <if test="conversionShortUrl != null">
        conversion_short_url = #{conversionShortUrl,jdbcType=VARCHAR},
      </if>
      <if test="gameBaseId != null">
        game_base_id = #{gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="gamePlatformType != null">
        game_platform_type = #{gamePlatformType,jdbcType=TINYINT},
      </if>
      <if test="iosUrlType != null">
        ios_url_type = #{iosUrlType,jdbcType=TINYINT},
      </if>
      <if test="iosUrlPageId != null">
        ios_url_page_id = #{iosUrlPageId,jdbcType=BIGINT},
      </if>
      <if test="iosUrl != null">
        ios_url = #{iosUrl,jdbcType=VARCHAR},
      </if>
      <if test="androidUrlType != null">
        android_url_type = #{androidUrlType,jdbcType=TINYINT},
      </if>
      <if test="androidUrlPageId != null">
        android_url_page_id = #{androidUrlPageId,jdbcType=BIGINT},
      </if>
      <if test="androidUrl != null">
        android_url = #{androidUrl,jdbcType=VARCHAR},
      </if>
      <if test="iosSchemaUrl != null">
        ios_schema_url = #{iosSchemaUrl,jdbcType=VARCHAR},
      </if>
      <if test="androidSchemaUrl != null">
        android_schema_url = #{androidSchemaUrl,jdbcType=VARCHAR},
      </if>
      <if test="triggerTime != null">
        trigger_time = #{triggerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="iosAppPackageId != null">
        ios_app_package_id = #{iosAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="androidAppPackageId != null">
        android_app_package_id = #{androidAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="qualificationIds != null">
        qualification_ids = #{qualificationIds,jdbcType=VARCHAR},
      </if>
      <if test="subPkg != null">
        sub_pkg = #{subPkg,jdbcType=TINYINT},
      </if>
      <if test="clueType != null">
        clue_type = #{clueType,jdbcType=TINYINT},
      </if>
      <if test="clueData != null">
        clue_data = #{clueData,jdbcType=VARCHAR},
      </if>
      <if test="customizedImpUrl != null">
        customized_imp_url = #{customizedImpUrl,jdbcType=VARCHAR},
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url = #{customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="autoFillText != null">
        auto_fill_text = #{autoFillText,jdbcType=VARCHAR},
      </if>
      <if test="autoFillLink != null">
        auto_fill_link = #{autoFillLink,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productShortUrl != null">
        product_short_url = #{productShortUrl,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=TINYINT},
      </if>
      <if test="contactType != null">
        contact_type = #{contactType,jdbcType=TINYINT},
      </if>
      <if test="appSubType != null">
        app_sub_type = #{appSubType,jdbcType=TINYINT},
      </if>
      <if test="isAndroidAppDirect != null">
        is_android_app_direct = #{isAndroidAppDirect,jdbcType=TINYINT},
      </if>
      <if test="contentInEdit != null">
        content_in_edit = #{contentInEdit,jdbcType=VARCHAR},
      </if>
      <if test="auditStatusInEdit != null">
        audit_status_in_edit = #{auditStatusInEdit,jdbcType=TINYINT},
      </if>
      <if test="linkIcon != null">
        link_icon = #{linkIcon,jdbcType=VARCHAR},
      </if>
      <if test="capsuleIcon != null">
        capsule_icon = #{capsuleIcon,jdbcType=VARCHAR},
      </if>
      <if test="capsuleText != null">
        capsule_text = #{capsuleText,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectByExample"
    parameterType="com.bilibili.mgk.material.center.repository.model.LauDynamicBluelinkResolveContentExample"
    resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List"/>
    from lau_dynamic_bluelink_resolve_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample"
    parameterType="com.bilibili.mgk.material.center.repository.model.LauDynamicBluelinkResolveContentExample">
    delete from lau_dynamic_bluelink_component_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
  </delete>

  <select id="countByExample"
    parameterType="com.bilibili.mgk.material.center.repository.model.LauDynamicBluelinkResolveContentExample"
    resultType="java.lang.Long">
    select count(*) from lau_dynamic_bluelink_component_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
  </select>


  <update id="updateByExampleSelective" parameterType="map">
    update lau_dynamic_bluelink_resolve_content
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.agentId != null">
        agent_id = #{record.agentId,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.upNickname != null">
        up_nickname = #{record.upNickname,jdbcType=VARCHAR},
      </if>
      <if test="record.upFace != null">
        up_face = #{record.upFace,jdbcType=VARCHAR},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=BIGINT},
      </if>
      <if test="record.bluelinkSeqId != null">
        bluelink_seq_id = #{record.bluelinkSeqId,jdbcType=INTEGER},
      </if>
      <if test="record.rawText != null">
        raw_text = #{record.rawText,jdbcType=VARCHAR},
      </if>
      <if test="record.textSegmentId != null">
        text_segment_id = #{record.textSegmentId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.dynamicId != null">
        dynamic_id = #{record.dynamicId,jdbcType=VARCHAR},
      </if>
      <if test="record.dynamicBizId != null">
        dynamic_biz_id = #{record.dynamicBizId,jdbcType=VARCHAR},
      </if>
      <if test="record.dynamicStatus != null">
        dynamic_status = #{record.dynamicStatus,jdbcType=INTEGER},
      </if>
      <if test="record.dynamicContent != null">
        dynamic_content = #{record.dynamicContent,jdbcType=VARCHAR},
      </if>
      <if test="record.auditRecordId != null">
        audit_record_id = #{record.auditRecordId,jdbcType=BIGINT},
      </if>
      <if test="record.componentType != null">
        component_type = #{record.componentType,jdbcType=TINYINT},
      </if>
      <if test="record.conversionUrlType != null">
        conversion_url_type = #{record.conversionUrlType,jdbcType=TINYINT},
      </if>
      <if test="record.conversionUrlPageId != null">
        conversion_url_page_id = #{record.conversionUrlPageId,jdbcType=BIGINT},
      </if>
      <if test="record.conversionUrl != null">
        conversion_url = #{record.conversionUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.conversionUrlText != null">
        conversion_url_text = #{record.conversionUrlText,jdbcType=VARCHAR},
      </if>
      <if test="record.conversionShortUrl != null">
        conversion_short_url = #{record.conversionShortUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.gameBaseId != null">
        game_base_id = #{record.gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="record.gamePlatformType != null">
        game_platform_type = #{record.gamePlatformType,jdbcType=TINYINT},
      </if>
      <if test="record.iosUrlType != null">
        ios_url_type = #{record.iosUrlType,jdbcType=TINYINT},
      </if>
      <if test="record.iosUrlPageId != null">
        ios_url_page_id = #{record.iosUrlPageId,jdbcType=BIGINT},
      </if>
      <if test="record.iosUrl != null">
        ios_url = #{record.iosUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.androidUrlType != null">
        android_url_type = #{record.androidUrlType,jdbcType=TINYINT},
      </if>
      <if test="record.androidUrlPageId != null">
        android_url_page_id = #{record.androidUrlPageId,jdbcType=BIGINT},
      </if>
      <if test="record.androidUrl != null">
        android_url = #{record.androidUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.iosSchemaUrl != null">
        ios_schema_url = #{record.iosSchemaUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.androidSchemaUrl != null">
        android_schema_url = #{record.androidSchemaUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerTime != null">
        trigger_time = #{record.triggerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.iosAppPackageId != null">
        ios_app_package_id = #{record.iosAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.androidAppPackageId != null">
        android_app_package_id = #{record.androidAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.qualificationIds != null">
        qualification_ids = #{record.qualificationIds,jdbcType=VARCHAR},
      </if>
      <if test="record.subPkg != null">
        sub_pkg = #{record.subPkg,jdbcType=TINYINT},
      </if>
      <if test="record.clueType != null">
        clue_type = #{record.clueType,jdbcType=TINYINT},
      </if>
      <if test="record.clueData != null">
        clue_data = #{record.clueData,jdbcType=VARCHAR},
      </if>
      <if test="record.customizedImpUrl != null">
        customized_imp_url = #{record.customizedImpUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.customizedClickUrl != null">
        customized_click_url = #{record.customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.autoFillText != null">
        auto_fill_text = #{record.autoFillText,jdbcType=VARCHAR},
      </if>
      <if test="record.autoFillLink != null">
        auto_fill_link = #{record.autoFillLink,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productShortUrl != null">
        product_short_url = #{record.productShortUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=TINYINT},
      </if>
      <if test="record.contactType != null">
        contact_type = #{record.contactType,jdbcType=TINYINT},
      </if>
      <if test="record.appSubType != null">
        app_sub_type = #{record.appSubType,jdbcType=TINYINT},
      </if>
      <if test="record.isAndroidAppDirect != null">
        is_android_app_direct = #{record.isAndroidAppDirect,jdbcType=TINYINT},
      </if>
      <if test="record.contentInEdit != null">
        content_in_edit = #{record.contentInEdit,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatusInEdit != null">
        audit_status_in_edit = #{record.auditStatusInEdit,jdbcType=TINYINT},
      </if>
      <if test="record.linkIcon != null">
        link_icon = #{record.linkIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.capsuleIcon != null">
        capsule_icon = #{record.capsuleIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.capsuleText != null">
        capsule_text = #{record.capsuleText,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_dynamic_bluelink_resolve_content
    set id = #{record.id,jdbcType=BIGINT},
    deleted = #{record.deleted,jdbcType=TINYINT},
    ctime = #{record.ctime,jdbcType=TIMESTAMP},
    mtime = #{record.mtime,jdbcType=TIMESTAMP},
    account_id = #{record.accountId,jdbcType=INTEGER},
    customer_id = #{record.customerId,jdbcType=INTEGER},
    agent_id = #{record.agentId,jdbcType=INTEGER},
    campaign_id = #{record.campaignId,jdbcType=INTEGER},
    unit_id = #{record.unitId,jdbcType=INTEGER},
    creative_id = #{record.creativeId,jdbcType=INTEGER},
    up_nickname = #{record.upNickname,jdbcType=VARCHAR},
    up_face = #{record.upFace,jdbcType=VARCHAR},
    mid = #{record.mid,jdbcType=BIGINT},
    bluelink_seq_id = #{record.bluelinkSeqId,jdbcType=INTEGER},
    raw_text = #{record.rawText,jdbcType=VARCHAR},
    text_segment_id = #{record.textSegmentId,jdbcType=INTEGER},
    `status` = #{record.status,jdbcType=TINYINT},
    audit_status = #{record.auditStatus,jdbcType=TINYINT},
    reason = #{record.reason,jdbcType=VARCHAR},
    dynamic_id = #{record.dynamicId,jdbcType=VARCHAR},
    dynamic_biz_id = #{record.dynamicBizId,jdbcType=VARCHAR},
    dynamic_status = #{record.dynamicStatus,jdbcType=INTEGER},
    dynamic_content = #{record.dynamicContent,jdbcType=VARCHAR},
    audit_record_id = #{record.auditRecordId,jdbcType=BIGINT},
    component_type = #{record.componentType,jdbcType=TINYINT},
    conversion_url_type = #{record.conversionUrlType,jdbcType=TINYINT},
    conversion_url_page_id = #{record.conversionUrlPageId,jdbcType=BIGINT},
    conversion_url = #{record.conversionUrl,jdbcType=VARCHAR},
    conversion_url_text = #{record.conversionUrlText,jdbcType=VARCHAR},
    conversion_short_url = #{record.conversionShortUrl,jdbcType=VARCHAR},
    game_base_id = #{record.gameBaseId,jdbcType=INTEGER},
    game_platform_type = #{record.gamePlatformType,jdbcType=TINYINT},
    ios_url_type = #{record.iosUrlType,jdbcType=TINYINT},
    ios_url_page_id = #{record.iosUrlPageId,jdbcType=BIGINT},
    ios_url = #{record.iosUrl,jdbcType=VARCHAR},
    android_url_type = #{record.androidUrlType,jdbcType=TINYINT},
    android_url_page_id = #{record.androidUrlPageId,jdbcType=BIGINT},
    android_url = #{record.androidUrl,jdbcType=VARCHAR},
    ios_schema_url = #{record.iosSchemaUrl,jdbcType=VARCHAR},
    android_schema_url = #{record.androidSchemaUrl,jdbcType=VARCHAR},
    trigger_time = #{record.triggerTime,jdbcType=TIMESTAMP},
    ios_app_package_id = #{record.iosAppPackageId,jdbcType=INTEGER},
    android_app_package_id = #{record.androidAppPackageId,jdbcType=INTEGER},
    qualification_ids = #{record.qualificationIds,jdbcType=VARCHAR},
    sub_pkg = #{record.subPkg,jdbcType=TINYINT},
    clue_type = #{record.clueType,jdbcType=TINYINT},
    clue_data = #{record.clueData,jdbcType=VARCHAR},
    customized_imp_url = #{record.customizedImpUrl,jdbcType=VARCHAR},
    customized_click_url = #{record.customizedClickUrl,jdbcType=VARCHAR},
    auto_fill_text = #{record.autoFillText,jdbcType=VARCHAR},
    auto_fill_link = #{record.autoFillLink,jdbcType=VARCHAR},
    product_id = #{record.productId,jdbcType=BIGINT},
    product_short_url = #{record.productShortUrl,jdbcType=VARCHAR},
    biz_code = #{record.bizCode,jdbcType=TINYINT},
    contact_type = #{record.contactType,jdbcType=TINYINT},
    app_sub_type = #{record.appSubType,jdbcType=TINYINT},
    is_android_app_direct = #{record.isAndroidAppDirect,jdbcType=TINYINT},
    content_in_edit = #{record.contentInEdit,jdbcType=VARCHAR},
    audit_status_in_edit = #{record.auditStatusInEdit,jdbcType=TINYINT},
    link_icon = #{record.linkIcon,jdbcType=VARCHAR},
    capsule_icon = #{record.capsuleIcon,jdbcType=VARCHAR},
    capsule_text = #{record.capsuleText,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>

</mapper>
