package com.bilibili.mgk.material.center.service.creative.vo;

import java.util.Arrays;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/24
 */
@Getter
public enum DaihuoMethod {

    bluelink("蓝链"),


    non_bluelink("非蓝链"),

    ;


    private String desc;

    public static <PERSON>huoMethod fromPlaceType(String placeType){
        return Arrays.stream(DaihuoMethod.values())
                .filter(item -> item.desc.equals(placeType))
                .findFirst()
                .orElse(null);
    }

    DaihuoMethod(String desc) {
        this.desc = desc;
    }
}
