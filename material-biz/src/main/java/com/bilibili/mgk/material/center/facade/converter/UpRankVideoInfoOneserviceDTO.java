package com.bilibili.mgk.material.center.facade.converter;

import com.bilibili.mgk.material.center.service.creative.model.UpRankVideoInfo;
import com.bilibili.mgk.material.center.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/30
 */
@Data
@Accessors(chain = true)
public class UpRankVideoInfoOneserviceDTO {

    @ApiModelProperty(value = "UP主MID", required = true, dataType = "Long", example = "123456789")
    private Long upMid;

    @ApiModelProperty(value = "UP主昵称", required = true, dataType = "String", example = "某某UP主")
    private String upName;

    @ApiModelProperty(value = "稿件ID", required = true, dataType = "Long", example = "987654321")
    private Long avid;

    @ApiModelProperty(value = "稿件发布时间", dataType = "String", example = "2023-08-21T15:30:00Z")
    private String avidPubtime;

    @ApiModelProperty(value = "稿件标题", dataType = "String", example = "这是一个标题")
    private String avidTitle;

    @ApiModelProperty(value = "封面地址", dataType = "String", example = "http://example.com/cover.jpg")
    private String avidCover;

    @ApiModelProperty(value = "标签", dataType = "String", example = "科技,编程")
    private String avidTag;

    @ApiModelProperty(value = "一级分区", dataType = "Integer", example = "123")
    @JsonProperty("avid_tid_name_new")
    private String avidTidName;

    @ApiModelProperty(value = "时长，单位秒", dataType = "Integer", example = "360")
    private Integer avidDuration;

    @ApiModelProperty(value = "总播放数", dataType = "Integer", example = "100000")
    private Integer play;

    @ApiModelProperty(value = "总点赞数", dataType = "Integer", example = "5000")
    private Integer likes;

    @ApiModelProperty(value = "总评论数", dataType = "Integer", example = "1500")
    private Integer reply;

    @ApiModelProperty(value = "总弹幕数", dataType = "Integer", example = "800")
    private Integer danmu;

    @ApiModelProperty(value = "总投币数", dataType = "Integer", example = "200")
    private Integer coin;

    @ApiModelProperty(value = "总收藏数", dataType = "Integer", example = "300")
    private Integer fav;

    @ApiModelProperty(value = "总分享数", dataType = "Integer", example = "100")
    private Integer share;


    private String dataReadyTime;


    public static UpRankVideoInfoOneserviceDTO fromMapValue(Map<String, String> mapValue) {

        return JsonUtil.fromJson(mapValue, UpRankVideoInfoOneserviceDTO.class);

    }


    public UpRankVideoInfo toBO() {

        return OneServiceDTOConverter.converter.toBO(this);
    }


}
