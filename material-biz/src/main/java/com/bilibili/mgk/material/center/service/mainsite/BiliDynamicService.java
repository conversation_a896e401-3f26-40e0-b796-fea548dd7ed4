package com.bilibili.mgk.material.center.service.mainsite;

import cn.hutool.core.collection.ListUtil;
import com.bapis.dynamic.admin.feed.CommerceDyn;
import com.bapis.dynamic.admin.feed.FeedAdminGrpc;
import com.bapis.dynamic.admin.feed.FetchDynForCommerceReq;
import com.bapis.dynamic.admin.feed.FetchDynForCommerceRsp;
import com.bapis.dynamic.admin.feed.FetchDynListForCommerceReq;
import com.bapis.dynamic.admin.feed.FetchDynListForCommerceReq.Builder;
import com.bapis.dynamic.admin.feed.FetchDynListForCommerceRsp;
import com.bapis.dynamic.common.CreatePic;
import com.bapis.dynamic.common.MetaDataCtrl;
import com.bapis.dynamic.service.publish.ICreateDrawReq;
import com.bapis.dynamic.service.publish.ICreateResp;
import com.bapis.dynamic.service.publish.PublishGrpc;
import com.bilibili.mgk.material.center.service.asset.model.DynamicPublishStatus;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicId;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicDrawPublishReq;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.mainsite.model.DynamicFilterType;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 图文动态服务
 * <AUTHOR>
 * @desc
 * @date 2024/7/8
 */
@Slf4j
@Service
public class BiliDynamicService {


    @Resource
    private PublishGrpc.PublishBlockingStub dynamicPublishStub;


    @Resource
    private FeedAdminGrpc.FeedAdminBlockingStub dynamicFeedAdminStub;



    @Value("${material.dynamic.params.from:create.ad}")
    private String from;


    @Value("${material.dynamic.params.content-sub-type:1}")
    private Long dynamicSubType;


    public Pagination<List<CommerceDyn>> pageWithIdSearch(
            Integer pn, Integer ps, String keyword, Integer publishStatus, Long uid) {


        Optional<CommerceDyn> idQueryResult = Optional.empty();

        Long idQuery = Try.of(() -> Long.valueOf(keyword)).getOrNull();

        // id查询结果只补充在第一页中，防止翻页出现问题
        if (idQuery != null && pn == 1) {

            idQueryResult = Optional.ofNullable(
                    this.fetchById(Lists.newArrayList(idQuery), publishStatus, uid).get(idQuery));
        }

        Pagination<List<CommerceDyn>> page;
        if (idQueryResult.isPresent()) {

            // id搜索结果作为兜底
            page = Try.of(() -> this.page(pn, ps, keyword, publishStatus, uid))
                    .getOrElse(Pagination.emptyPagination());
        } else {

            // 需要抛出
            page = this.page(pn, ps, keyword, publishStatus, uid);
        }

        Pagination<List<CommerceDyn>> result = combineSearchResultAndIdResult(page, idQueryResult);

        return result;
    }


    /**
     * 合并， 有可能两个结果中都包含同一条dyn，仅做本页内的去重
     *
     * @param pageResult
     * @param idQueryResult
     * @return
     */
    private Pagination<List<CommerceDyn>> combineSearchResultAndIdResult(
            Pagination<List<CommerceDyn>> pageResult, Optional<CommerceDyn> idQueryResult) {

        if (!idQueryResult.isPresent()) {
            return pageResult;
        }
        // 存在id查询结果，则补充到第一页中
        List<CommerceDyn> data = new ArrayList<>();
        data.add(idQueryResult.get());

        if (CollectionUtils.isNotEmpty(pageResult.getData())) {

            //并简单的只对第一页进行去重
            data.addAll(pageResult.getData()
                    .stream()
                    .filter(item -> !Objects.equals(idQueryResult.get().getDynId(), item.getDynId()))
                    .collect(Collectors.toList())
            );

        }

        return new Pagination<>(
                pageResult.getPage(),
                pageResult.getTotal_count() + 1,
                data
        );

    }


    public Pagination<List<CommerceDyn>> page(

            Integer pn, Integer ps, String keyword, Integer publishStatus, Long uid
    ) {

        Builder req = FetchDynListForCommerceReq.newBuilder()
                .setMid(uid)
                .setPn(pn - 1)
                .setPs(ps)
                // // 状态删选项；0-全部，1-审核中，2-已通过，3-未通过（审核拒绝）
                .setFilterType(DynamicFilterType.publishStatus2FilterType(publishStatus));

        if (StringUtils.isNotEmpty(keyword)) {
            req.setKeywords(keyword);
        }

        FetchDynListForCommerceRsp rsp = dynamicFeedAdminStub.fetchDynListForCommerce(
                req.build()
        );

        return new Pagination<>(
                pn,
                (int) rsp.getTotal(),
                rsp.getDynInfoListList().stream()
                        .map(item -> item)
                        .collect(Collectors.toList())
        );


    }


    /**
     *
     * @param dynamicIds
     * @param uid 如果提供，那么需要对uid进行过滤，如果uid为空，则不进行过滤
     * @return
     */
    public Map<Long, CommerceDyn> fetchById(List<Long> dynamicIds, Integer publishStatus, @Nullable Long uid) {

        if (publishStatus != DynamicPublishStatus.audit_pass.getCode()) {
            // FIXME fetchById接口主站仅支持查询审核通过的动态
            return new HashMap<>();
        }


        if(CollectionUtils.isEmpty(dynamicIds)){
            return new HashMap<>();
        }

        return ListUtil.partition(dynamicIds, 20)
                .stream()
                .flatMap(partitionIds -> {
                    FetchDynForCommerceRsp resp = dynamicFeedAdminStub.fetchDynForCommerce(
                            FetchDynForCommerceReq.newBuilder()
                                    .addAllDynIds(partitionIds)
                                    .setNeedShadowInfo(false).build()
                    );

                    return resp.getDynInfosList().stream();
                })
                .filter(dyn -> {

                    if (uid == null) {
                        return true;
                    }

                    return dyn.getOwner().getUid() == uid;
                })
                .collect(Collectors.toMap(
                        dyn -> dyn.getDynId(),
                        dyn -> dyn,
                        (a, b) -> b
                ));

    }


    public MaterialDynamicId createDraw(MaterialDynamicDrawPublishReq req) {

        return Try.of(() -> {

            ICreateDrawReq grpcReq = ICreateDrawReq.newBuilder()
                    .setUid(req.getUid())
                    .setMeta(MetaDataCtrl.newBuilder()
                            .setFrom(from)
                            .build())
                    .setContent(req.toGrpcCreateContentWithRichText(dynamicSubType))
                    .addAllPics(
                            req.getPics().stream().map(pic -> {
                                return CreatePic.newBuilder()
                                        .setImgSrc(pic.getUrl())
                                        .setImgHeight(pic.getHeight())
                                        .setImgSize(Try.of(() -> pic.getSize() * 1.0f / 1024).getOrElse(0.0f))
                                        .setImgWidth(pic.getWidth())
                                        // TODO 图片标签
                                        .build();
                            }).collect(Collectors.toList())

                    )

                    .build();

            ICreateResp resp = dynamicPublishStub.iCreateDraw(grpcReq);

            return new MaterialDynamicId()
                    .setDynId(String.valueOf(resp.getDynId()))
                    .setDynRid(String.valueOf(resp.getDynRid()))
                    .setDynType(String.valueOf(resp.getDynType()));

        }).getOrElseThrow(t -> {

            log.error("Fail to create Draw, req={}", req, t);
            return new IllegalArgumentException("创建动态失败: " + t.getMessage());
        });

    }





}
