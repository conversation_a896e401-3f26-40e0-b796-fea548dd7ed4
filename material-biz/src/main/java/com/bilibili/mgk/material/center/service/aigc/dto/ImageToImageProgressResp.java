package com.bilibili.mgk.material.center.service.aigc.dto;

import com.bilibili.mgk.material.center.service.aigc.model.AIGeneratedImage;
import com.bilibili.mgk.material.center.service.aigc.model.Img2ImgProgressStatus;
import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/18
 */
@Data
@Accessors(chain = true)
public class ImageToImageProgressResp implements SnakeCaseBody {


    @ApiModelProperty(value = "任务记录id")
    private Long recordId;

    @ApiModelProperty(value = "进度id")
    private String taskId;


    @ApiModelProperty(value = "进度状态")
    private Img2ImgProgressStatus progressStatus;


    @ApiModelProperty("进度百分比， 100为完成")
    private Integer progressPercent;


    @ApiModelProperty(value = "任务总数量")
    private Integer totalCnt;


    @ApiModelProperty(value = "已完成数量")
    private Integer completeCnt;


    @ApiModelProperty(value = "任务状态")
    private List<AIGeneratedImage> generatedImgs;


    /**
     * 是否结束， 失败也是结束
     */

    @ApiModelProperty(value = "是否结束")
    private Boolean isComplete;

    /**
     * 是否成功，只有全部成功才叫成功
     */
    @ApiModelProperty(value = "是否成功")
    private Boolean isSuccess;


    /**
     * 错误信息， 暂时不考虑部分失败，且部分失败的结果还不同的情况，
     */
    @ApiModelProperty(value = "错误信息")
    private Integer errCode;


    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errMsg;


    public ImageToImageProgressResp hideAuditDeniedImg(Boolean errorSeenAsDeny) {

        Optional.ofNullable(generatedImgs)
                .orElse(new ArrayList<>())
                .forEach(img -> {
                    if (img.fetchAuditDeniedAndInterceptAuditError(errorSeenAsDeny)) {
                        img.setGenImgUrl("");
                    }
                });

        return this;
    }


    public static ImageToImageProgressResp initialProgress(String taskId, Long recordId, ImageToImageSubmitReq req) {
        return new ImageToImageProgressResp()
                .setRecordId(recordId)
                .setTaskId(taskId)
                .setProgressPercent(0)
                .setCompleteCnt(0)
                .setTotalCnt(req.getQuantity())
                .setGeneratedImgs(new ArrayList<>());

    }


}
