package com.bilibili.mgk.material.center.service.asset;

import com.bapis.dynamic.admin.feed.CommerceDyn;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.mgk.material.center.config.DynamicAssetPageConfig;
import com.bilibili.mgk.material.center.repository.mysql.DynamicScheduleJobRepository;
import com.bilibili.mgk.material.center.service.asset.model.DynamicCursor;
import com.bilibili.mgk.material.center.service.asset.model.DynamicDataset;
import com.bilibili.mgk.material.center.service.asset.model.DynamicPublishStatus;
import com.bilibili.mgk.material.center.service.asset.model.DynamicScheduleJob;
import com.bilibili.mgk.material.center.service.asset.model.JobScheduleStatus;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicId;
import com.bilibili.mgk.material.center.service.asset.model.MaterialDynamicInfo;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicDrawDeleteReq;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicDrawPublishReq;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicPageQuery;
import com.bilibili.mgk.material.center.service.asset.vo.MaterialDynamicWaterfallPageQuery;
import com.bilibili.mgk.material.center.service.asset.vo.PublishType;
import com.bilibili.mgk.material.center.service.asset.vo.RichTextDTO;
import com.bilibili.mgk.material.center.service.bluelink.DynamicBluelinkAuditService;
import com.bilibili.mgk.material.center.service.bluelink.DynamicBluelinkComponentService;
import com.bilibili.mgk.material.center.service.bluelink.dto.RichTextType;
import com.bilibili.mgk.material.center.service.bluelink.model.BluelinkAuditStatus;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicBlueLinkQueryBo;
import com.bilibili.mgk.material.center.service.bluelink.model.DynamicComponentAuditWithBluelink;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.bilibili.mgk.material.center.service.creative.vo.WaterfallPage;
import com.bilibili.mgk.material.center.service.mainsite.BiliDynamicService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import io.grpc.Status;
import io.grpc.Status.Code;
import io.grpc.StatusException;
import io.vavr.Tuple;
import io.vavr.Tuple4;
import io.vavr.control.Either;
import io.vavr.control.Try;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/18
 */
@Slf4j
@Service
public class DynamicAssetServiceImpl implements DynamicAssetService {
    @Resource
    private BiliDynamicService biliDynamicService;
    @Resource
    private DynamicScheduleJobRepository publishScheduledDynamicRepository;
    @Resource
    private DynamicBluelinkComponentService bluelinkService;
    @Resource
    private DynamicBluelinkAuditService dynamicBluelinkAuditService;
    @Resource
    private DynamicAssetPageConfig dynamicAssetPageConfig;


    @Override
    public List<MaterialDynamicId> triggerSchedulePublishTask() {

        //如果不止limit数量个， 等待下轮调度将其消费；
        List<DynamicScheduleJob> schedulingTasks = publishScheduledDynamicRepository
                .selectTopSchedulingTaskAndScheduleTimeLteOrderByMtimeAsc(
                        JobScheduleStatus.scheduling,

                        LocalDateTime.now(),
                        200
                );

        if (CollectionUtils.isEmpty(schedulingTasks)) {

            log.info("No scheduling dynamic need to publish! ");

            return new ArrayList<>();
        }

        List<Either<MaterialDynamicId, Throwable>> jobResult = schedulingTasks.stream().map(task -> {

            Either<MaterialDynamicId, Throwable> result = Try.of(() -> {

                MaterialDynamicDrawPublishReq req = task.toPublishReq(PublishType.PUBLISH_NOW);
                req.validateAndTransformAsRichTextReq();
                MaterialDynamicId r = this.publishRightNow(req, task.getOperator());
                return Either.<MaterialDynamicId, Throwable>left(r);

            }).getOrElseGet(t -> {

                log.error("Fail schedule to publish dynamic, task={}, ", task, t);
                return Either.<MaterialDynamicId, Throwable>right(t);

            });

            // update schedule status
            Try.run(() -> {

                DynamicScheduleJob selective = result.fold(l -> {
                    return new DynamicScheduleJob()
                            .setId(task.getId())
                            .setScheduleStatus(JobScheduleStatus.success)
                            .setErrCode(0)
                            .setErrMsg("success")
                            .setDynId(Long.valueOf(l.getDynId()))
                            .setDynRid(Long.valueOf(l.getDynRid()))
                            .setDynType(Long.valueOf(l.getDynType()));
                }, t -> {
                    Integer code = -1;

                    if (t instanceof StatusException) {
                        code = Optional.ofNullable(
                                        ((StatusException) t).getStatus()
                                ).map(Status::getCode)
                                .map(Code::value)
                                .orElse(-1);

                    }

                    return new DynamicScheduleJob()
                            .setId(task.getId())
                            .setScheduleStatus(JobScheduleStatus.fail)
                            .setErrCode(code)
                            .setErrMsg(Optional.ofNullable(t.getMessage()).orElse("")
                                    .substring(0, 100))
                            .setDynId(0L)
                            .setDynRid(0L)
                            .setDynType(0L);
                });

                publishScheduledDynamicRepository.updateByPrimaryKeySelective(selective);

            }).onFailure(t -> {
                log.error("Fail to update schedule dynamic status, task={}", task, t);
            });

            return result;

        }).collect(Collectors.toList());

        log.info("Success to process dynamic schedule publish job, tasks={}, publishedIds={}",
                schedulingTasks.stream().map(DynamicScheduleJob::getId),
                jobResult.stream().filter(Either::isLeft)
                        .map(job -> Optional
                                .ofNullable(job.getLeft())
                                .map(MaterialDynamicId::getDynId).orElse(""))
                        .collect(Collectors.toList()));

        return jobResult.stream().filter(Either::isLeft).map(Either::getLeft).collect(Collectors.toList());
    }

    @Override
    public MaterialDynamicId publish(MaterialDynamicDrawPublishReq req, Operator operator) {

        // accountId, uid授权校验
        // 内部接口经完成授权校验
        req.validateAndTransformAsRichTextReq();


        if (req.getPublishType() == PublishType.PUBLISH_NOW) {

            return this.publishRightNow(req, operator);

        } else {
            return this.submitPublishScheduleTask(req, operator);
        }

    }

    @Override
    public MaterialDynamicId delete(MaterialDynamicDrawDeleteReq deleteReq) {

        if (!deleteReq.isScheduleJob()) {
            throw new IllegalArgumentException("Unsupported in current version");
        }

        DynamicScheduleJob task = Optional.ofNullable(
                        publishScheduledDynamicRepository.selectByPrimaryKey(deleteReq.getScheduleId()))
                .filter(job -> job.getScheduleStatus() == JobScheduleStatus.scheduling)
                .filter(job -> job.getUid().equals(deleteReq.getUid()))
                .orElseThrow(() -> new IllegalArgumentException("No such schedule job"));

        DynamicScheduleJob update = new DynamicScheduleJob()
                .setId(task.getId())
                .setScheduleStatus(JobScheduleStatus.cancel)
                .setErrCode(-1)
                .setErrMsg("cancelled by user")
                .setDynId(0L)
                .setDynRid(0L)
                .setDynType(0L);

        publishScheduledDynamicRepository.updateByPrimaryKeySelective(update);

        return new MaterialDynamicId()
                .setScheduleId(task.getId());

    }

    private MaterialDynamicId submitPublishScheduleTask(MaterialDynamicDrawPublishReq req, Operator operator) {

        DynamicScheduleJob insert = DynamicScheduleJob.fromPublishReq(req, JobScheduleStatus.scheduling, operator);

        publishScheduledDynamicRepository.insertSelective(insert);

        return new MaterialDynamicId()
                .setScheduleId(insert.getId());

    }


    private MaterialDynamicId publishRightNow(MaterialDynamicDrawPublishReq req, Operator operator) {

        return bluelinkService.createDrawWithRichText(req, operator);

    }


    /**
     * @param query
     * @return
     * @deprecated 无法满足审核中存在两种数据源的时候的需要，只能改用2个page页，然后拼页做成瀑布页
     */
    @Override
    @Deprecated
    public Pagination<List<MaterialDynamicInfo>> list(MaterialDynamicPageQuery query) {

        query.validate();

        if (query.fetchPublishStatusEnum().isScheduleState()) {
            return pageSchedulingDynamic(
                    query.getPn(),
                    query.getPs(),
                    query.getUid(),
                    query.getPublishStatus(),
                    query.getKeyword()
            );
        }
        else {
            // 当前仅支持查询审核通过的状态
            return this.pagePublishedDynamic(
                    query.getPn(),
                    query.getPs(),
                    query.getKeyword(),
                    query.getPublishStatus(),
                    query.getUid(), (auditMap, dynamic) -> {
                        Optional.ofNullable(auditMap.get(dynamic.getDynId()))
                                .map(audit -> audit.getAudit().getAuditStatus())
                                .map(status -> {

                                    dynamic.setCommerceAuditStatus(status);

                                    if (status == BluelinkAuditStatus.AUDIT_PASSED.getCode()) {
                                        dynamic.setCommerceAuditStatusDesc("商业审核通过");
                                    } else if (status == BluelinkAuditStatus.AUDIT_REJECTED.getCode()) {
                                        dynamic.setCommerceAuditStatusDesc("商业审核驳回");
                                    } else if (status == BluelinkAuditStatus.AUDITING.getCode()) {
                                        dynamic.setCommerceAuditStatusDesc("商业审核中");
                                    }
                                    return true;
                                });
                        return true;
                    }
            );
        }
    }


    @Override
    public WaterfallPage<MaterialDynamicInfo> list(MaterialDynamicWaterfallPageQuery query) {

        query.validate();

        DynamicCursor cursor = Try.of(() ->
                        DynamicCursor.fromCursorString(query.getCursor(), dynamicAssetPageConfig.getDefaultWaterfallPageSize())
                                .validate())
                .getOrElseThrow(t -> {
                    return new IllegalArgumentException("Invalid cursor: " + t.getMessage(), t);
                });

        DynamicPublishStatus dynamicPublishStatus = DynamicPublishStatus.fromCode(query.getPublishStatus());

        List<MaterialDynamicInfo> page = new ArrayList<>();
        boolean hasMore = true;
        DynamicDataset nextDataset = cursor.getDataset();
        Integer nextPn = cursor.getPn() + 1;

        switch (dynamicPublishStatus) {
            case audit:
            case audit_deny: {

                Tuple4<List<MaterialDynamicInfo>, DynamicDataset, Boolean, Integer> result = this
                        .combineCommercePageAndMainSitePageByWaterfall(query, cursor, dynamicPublishStatus);

                page = result._1;
                nextDataset = result._2;
                hasMore = result._3;
                nextPn = result._4;
                break;
            }

            case audit_pass: {
                page = this.pagePublishedDynamic(
                                cursor.getPn(),
                                cursor.getPs(),
                                query.getKeyword(),
                                query.getPublishStatus(),
                                query.getUid(), (auditMap, dynamic) -> Optional
                                        .ofNullable(auditMap.get(dynamic.getDynId()))
                                        .map(commerceAudit -> {

                                            // 和{#combineCommercePageAndMainSitePageByWaterfall} 一样的逻辑
                                            // 要合起来看
                                            if (commerceAudit.getAudit().getAuditStatus()
                                                    == BluelinkAuditStatus.AUDIT_PASSED.getCode()) {
                                                return true;
                                            }
                                            // 只要在商业列表中存在，就不要
                                            return false;
                                        })
                                        .orElse(true))
                        .getData();
                break;
            }
            case scheduling:
            case schedule_fail: {

                page = pageSchedulingDynamic(
                        cursor.getPn(),
                        cursor.getPs(),
                        query.getUid(),
                        query.getPublishStatus(),
                        query.getKeyword()
                ).getData();
                break;
            }
            case reserved:
            default: {
                throw new IllegalArgumentException("Unsupported publish status");
            }
        }

        return new WaterfallPage<MaterialDynamicInfo>()

                .setData(page)
                .setHasMore(hasMore)
                .setNextCursor(new DynamicCursor()
                        .setDataset(nextDataset)
                        .setPn(nextPn)
                        .setPs(cursor.getPs())
                        .asCursor()
                );

    }


    /**
     * @return data, dataset, hasMore, pn, ps,
     */
    private Tuple4<List<MaterialDynamicInfo>, DynamicDataset, Boolean, Integer> combineCommercePageAndMainSitePageByWaterfall(
            MaterialDynamicWaterfallPageQuery query, DynamicCursor cursor, DynamicPublishStatus dynamicPublishStatus
    ) {

        List<MaterialDynamicInfo> page = new ArrayList<>();

        boolean hasMore = true;
        DynamicDataset nextDataset = cursor.getDataset();
        Integer nextPn = cursor.getPn() + 1;

        boolean needFetchNextDataset = false;
        if (cursor.getDataset() == DynamicDataset.COMMERCE_BLUELINK) {

            Pagination<List<DynamicComponentAuditWithBluelink>> audits = dynamicBluelinkAuditService.getDynamicBlueLinks(
                    new DynamicBlueLinkQueryBo()
                            .setMids(Lists.newArrayList(query.getUid()))
                            .setAuditStatus(Lists.newArrayList(
                                    dynamicPublishStatus == DynamicPublishStatus.audit ?
                                            BluelinkAuditStatus.AUDITING.getCode() :
                                            BluelinkAuditStatus.AUDIT_REJECTED.getCode()
                            ))
                            .setPage(cursor.getPn())
                            .setSize(cursor.getPs())
                            .setSearchTitleLike(query.getKeyword())
                            .setSearchDynamicIdEq(Try.of(() -> Long.valueOf(query.getKeyword()))
                                    .map(String::valueOf)
                                    .getOrNull()));

            page.addAll(audits.getData().stream()
                    .map(auditInfo -> MaterialDynamicInfo.fromCommerceAuditInfo(auditInfo))
                    .collect(Collectors.toList()));

            if (page.isEmpty() || (dynamicAssetPageConfig.getNotFullAsNoMore()
                    && page.size() < cursor.getPs())) {

                needFetchNextDataset = dynamicAssetPageConfig.getCombineNextDataset();
                // 暂时设置false，如果选择fetch-more，那么会根据下一轮查询的二级果进行覆盖
                hasMore = false;
                nextPn = 1;
                nextDataset = DynamicDataset.MAINSITE_DYNAMIC;
            } else {
                // 认为剩下还有
                needFetchNextDataset = false;
                hasMore = true;
                nextPn = cursor.getPn() + 1;
                nextDataset = cursor.getDataset();
            }

        }

        if (needFetchNextDataset || cursor.getDataset() == DynamicDataset.MAINSITE_DYNAMIC) {

            Integer pn = needFetchNextDataset ? nextPn : cursor.getPn();
            Pagination<List<MaterialDynamicInfo>> data2 = this.pagePublishedDynamic(pn,
                    cursor.getPs(),
                    query.getKeyword(),
                    query.getPublishStatus(),
                    query.getUid(), (auditMap, dynamic) -> {
                        return Optional
                                .ofNullable(auditMap.get(dynamic.getDynId()))
                                .map(commerceAudit -> {

                                    // 如果存在商业拒审理由则进行赋值
                                    dynamic.setCommerceAuditReason(
                                            Optional.ofNullable(commerceAudit.getAudit())
                                                    .map(audit -> audit.getReason())
                                                    .orElse("")
                                    );

                                    // 只要在商业列表中存在，就不要
                                    // 尽量不要动这里了， 这是傻逼产品的设计， 我已经一万次拒绝了
                                    // FIXME {@see https://info.bilibili.co/pages/viewpage.action?pageId=938745650}
                                    if (dynamicPublishStatus == DynamicPublishStatus.audit) {

                                        if (commerceAudit.getAudit().getAuditStatus()
                                                == BluelinkAuditStatus.AUDITING.getCode()) {

                                            // 同时是审核中的，不要， 通过商业列展示
                                            return false;
                                        }
                                        if (commerceAudit.getAudit().getAuditStatus()
                                                == BluelinkAuditStatus.AUDIT_PASSED.getCode()) {
                                            return true;
                                        }
                                        if (commerceAudit.getAudit().getAuditStatus()
                                                == BluelinkAuditStatus.AUDIT_REJECTED.getCode()) {
                                            // 主站审，商业拒
                                            return false;
                                        }
                                    }
                                    if (dynamicPublishStatus == DynamicPublishStatus.audit_deny) {

                                        if (commerceAudit.getAudit().getAuditStatus()
                                                == BluelinkAuditStatus.AUDIT_REJECTED.getCode()) {
                                            // 拒+ 拒绝= 拒绝，显示商业的。
                                            return false;
                                        }
                                        if (commerceAudit.getAudit().getAuditStatus()
                                                == BluelinkAuditStatus.AUDIT_PASSED.getCode()) {

                                            // 拒+ 通过= 拒，显示主站的
                                            return true;
                                        }

                                        if (commerceAudit.getAudit().getAuditStatus()
                                                == BluelinkAuditStatus.AUDITING.getCode()) {
                                            // 拒+ 审核中= 拒，显示拒绝的
                                            return true;
                                        }

                                    }
                                    return false;

                                }).orElse(true);
                    });

            page.addAll(data2.getData());

            if (data2.getData().isEmpty()
//                    // 因为存在filterOut，所以不能用这个条件
//                    || (dynamicAssetPageConfig.getNotFullAsNoMore() && data2.size() < cursor.getPs())
                    || data2.getTotal_count() <= pn * cursor.getPs()
            ) {

                hasMore = false;
            } else {
                // 认为剩下还有
                needFetchNextDataset = false;
                hasMore = true;
                nextPn = cursor.getPn() + 1;
                nextDataset = DynamicDataset.MAINSITE_DYNAMIC;
            }
        }

        return Tuple.of(page, nextDataset, hasMore, nextPn);

    }





    public Pagination<List<MaterialDynamicInfo>> pageSchedulingDynamic(
            Integer pn, Integer ps, Long uid, Integer publishStatus, String keyword) {

        Page<DynamicScheduleJob> page = PageHelper.startPage(pn, ps)
                .doSelectPage(() -> {

                    publishScheduledDynamicRepository.selectByUidAndScheduleStatus(

                            uid,

                            Try.of(() -> {
                                if (DynamicPublishStatus.schedule_fail.getCode() == publishStatus) {
                                    return JobScheduleStatus.fail;
                                }
                                return JobScheduleStatus.scheduling;
                            }).get(),
                            keyword
                    );

                });

       return Pagination.fromPageHelper(page)
                .map(list -> {
                    return list.stream().map(item->{
                        return item.toBO()
                                .setPublishStatus(publishStatus);
                    }).collect(Collectors.toList());
                });
    }


    public Pagination<List<MaterialDynamicInfo>> pagePublishedDynamic(

            Integer pn, Integer ps, String keyword, Integer publishStatus, Long uid,
            BiFunction<Map<String, DynamicComponentAuditWithBluelink>, MaterialDynamicInfo, Boolean> filterPredicate
    ) {

        Pagination<List<CommerceDyn>> page = biliDynamicService.pageWithIdSearch(
                pn, ps, keyword, publishStatus, uid
        );

        Map<String, DynamicComponentAuditWithBluelink> dynamicId2Bluelinks = Try.of(
                        () -> bluelinkService.fetchDynamicResolveContent(

                                page.getData().stream().filter(dyn -> {
                                            if (dyn.getSummary().hasSummary()) {
                                                return true;
                                            }
                                            return false;
                                        })
                                        .map(dyn -> String.valueOf(dyn.getDynId()))
                                        .collect(Collectors.toList())))
                .getOrElse(new ArrayList<>()).stream().collect(Collectors.toMap(dyn -> dyn.getAudit().getDynamicId(),
                        dyn -> dyn, (a, b) -> b));

        return page
                .map(list -> list.stream().map(grpcDynamic -> {

                    MaterialDynamicInfo dynamicInfo = MaterialDynamicInfo.fromGrpcResp(grpcDynamic);

                    dynamicInfo.setRichContent(
                            Optional.ofNullable(dynamicId2Bluelinks.get(dynamicInfo.getDynId()))
                                    .map(DynamicComponentAuditWithBluelink::asRichTextDTO)
                                    .orElse(Lists.newArrayList(new RichTextDTO()
                                            .setRawText(dynamicInfo.getContent())
                                            .setRichTextType(RichTextType.RAW_TEXT)
                                            .setTextSegmentId(1)
                                    ))
                    );

                    return dynamicInfo;
                }).collect(Collectors.toList())).map(list -> {

                    //只针对审核通过进行二次过滤
//                    if (publishStatus != DynamicPublishStatus.audit_pass.getCode()) {
//                        return list;
//                    }

                    // 默认是否， 是filling， 不是filter-out
                    return this.filterOutAuditNotPassDynamics(list, filterPredicate);
                });

    }

    /**
     * @param dynamics
     * @param filterOut true 排除， false filling审核状态
     * @return
     */
    private List<MaterialDynamicInfo> filterOutAuditNotPassDynamics(List<MaterialDynamicInfo> dynamics,
            BiFunction<Map<String, DynamicComponentAuditWithBluelink>, MaterialDynamicInfo, Boolean> filterPredicate) {

        return Try.of(() -> {
            Map<String, DynamicComponentAuditWithBluelink> auditMap = Optional.ofNullable(dynamicBluelinkAuditService
                            .getDynamicBlueLinks(DynamicBlueLinkQueryBo
                                    .builder()
                                    .dynamicIds(dynamics.stream()
                                            .map(MaterialDynamicInfo::getDynId)
                                            .collect(Collectors.toList()))
                                    .page(1)
                                    .size(dynamics.size())
                                    .build()).getData()
                    )
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(item -> item.getAudit().getDynamicId(), item -> item, (a, b) -> b));

            return dynamics.stream()
                    .filter(dynamic -> {

                        return filterPredicate.apply(auditMap, dynamic);
                    })
                    .collect(Collectors.toList());
        }).onFailure(t -> {
            log.error("Fail to filterOutAuditNotPassDynamics, ", t);
        }).getOrElse(dynamics);

    }
}
