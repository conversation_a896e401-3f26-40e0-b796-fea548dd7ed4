package com.bilibili.mgk.material.center.service.creative;

import com.bilibili.mgk.material.center.service.creative.model.RisingVideoAdItem;
import com.bilibili.mgk.material.center.service.creative.model.RisingVideoAdItemCurve;
import com.bilibili.mgk.material.center.service.creative.vo.RisingVideoAdItemCurveQuery;
import com.bilibili.mgk.material.center.service.creative.vo.RisingVideoAdItemQuery;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/4
 */
public interface RisingVideoAdItemService {



    /**
     * 获取飙升商品的明细，其中包括商品信息、 商品day_type区间内增量聚合数据、商品180天聚合数据
     * 需要注意注意增量数据来自视频daily表，180天数据来自商品聚合表
     * @param query
     * @return
     */
    RisingVideoAdItem detail(RisingVideoAdItemQuery query);

    /**
     * 获取商品的某个维度下（curve_type, 如播放量、不同up数）的近day_type天曲线，包括增量曲线和180天累计曲线
     * 需要注意注意增量数据来自视频daily表，180天数据来自商品聚合表
     * @param curveQuery
     * @return
     */
    RisingVideoAdItemCurve curve(RisingVideoAdItemCurveQuery curveQuery);
}
