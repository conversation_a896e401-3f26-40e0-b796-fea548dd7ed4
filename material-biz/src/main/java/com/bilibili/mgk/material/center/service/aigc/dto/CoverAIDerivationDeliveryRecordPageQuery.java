package com.bilibili.mgk.material.center.service.aigc.dto;

import com.bilibili.mgk.material.center.service.creative.vo.SnakeCaseBody;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/23
 */
@Data
@Accessors(chain = true)
public class CoverAIDerivationDeliveryRecordPageQuery implements SnakeCaseBody {


    private Long accountId;


    private Integer pn;

    private Integer ps;


}
