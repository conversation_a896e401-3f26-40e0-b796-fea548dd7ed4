package com.bilibili.mgk.material.center.repository.impl;

import com.bilibili.mgk.material.center.repository.Img2ImgProgressRecordRepository;
import com.bilibili.mgk.material.center.repository.mysql.AIGeneratedImageMapper;
import com.bilibili.mgk.material.center.repository.mysql.Img2ImgProgressRecordMapper;
import com.bilibili.mgk.material.center.service.aigc.dto.GeneratedImageThumbupAction;
import com.bilibili.mgk.material.center.service.aigc.model.AIGeneratedImage;
import com.bilibili.mgk.material.center.service.aigc.model.Img2ImgProgressRecord;
import com.bilibili.mgk.material.center.service.aigc.model.Img2ImgProgressStatus;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortBy;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialSortOrder;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * dao层存在是因为需要对repository进行简单持久化逻辑的封装， 如果一对多的嵌套逻辑等； 当然实际有jpa等可以很方便，这里简化
 *
 * <AUTHOR>
 * @desc
 * @date 2024/7/19
 */
@Slf4j
@Repository
public class Img2ImgProgressRecordRepositoryImpl implements Img2ImgProgressRecordRepository {


    @Resource
    private Img2ImgProgressRecordMapper progressRecordMapper;


    @Resource
    private AIGeneratedImageMapper generatedImageMapper;


    /**
     * @param recordId
     * @param progressStatus
     * @param completedImgs
     * @param code
     * @param errMsg
     * @deprecated 从目前使用开， 特别是接入了机审后， 更新状态和插入图片需要分开，所以这个方法会弃用
     */
    @Override
    @Deprecated
    public void updateRecordStatusAndInsertGeneratedImgs(
            Long recordId, Img2ImgProgressStatus progressStatus,
            List<AIGeneratedImage> completedImgs, @Nullable Integer code, @Nullable String errMsg) {

        Try.run(() -> {
            progressRecordMapper.updateByPrimaryKeySelective(
                    new Img2ImgProgressRecord()
                            .setId(recordId)
                            .setProgressStatus(progressStatus)
                            .setErrCode(code)
                            .setErrMsg(errMsg)
            );
        }).andThen(r -> {

            // 直接insert，失败则认为是重复的
            completedImgs.stream().forEach(img -> {
                Try.run(() -> {
                    generatedImageMapper.insertSelective(img);
                });
            });

        }).onFailure(t -> {
            log.error("Fail to updateRecordStatusAndInsertGeneratedImgs, record={}, imgs={}, status={}",
                    recordId, completedImgs, progressStatus, t);
        });

    }


    @Override
    public void updateRecordStatus(Long recordId, Img2ImgProgressStatus progressStatus, @Nullable Integer code,
            @Nullable String errMsg) {
        Try.run(() -> {
            progressRecordMapper.updateByPrimaryKeySelective(
                    new Img2ImgProgressRecord()
                            .setId(recordId)
                            .setProgressStatus(progressStatus)
                            .setErrCode(code)
                            .setErrMsg(errMsg)
            );
        }).onFailure(t -> {
            log.error("Fail to updateRecordStatus, record={}, status={}",
                    recordId, progressStatus, t);
        });
    }

    @Override
    public void insertCompletedImgs(List<AIGeneratedImage> completedImgs) {

        Try.run(() -> {
            // 直接insert，失败则认为是重复的
            completedImgs.stream().forEach(img -> {
                Try.run(() -> {
                    generatedImageMapper.insertSelective(img);
                });
            });
        }).onFailure(t -> {
            log.error("Fail to insertCompletedImgs,  imgs={},",
                    completedImgs, t);
        });
    }


    public void updateGeneratedImgSelective(AIGeneratedImage image) {

        generatedImageMapper.updateByPrimaryKeySelective(image);
    }

    /**
     * @return recordId
     */
    @Override
    public int insertProgressRecord(Img2ImgProgressRecord insertSelective) {

        return progressRecordMapper.insertSelective(insertSelective);
    }


    @Override
    public void deleteRecordByRecordId(Long accountId, Long recordId) {

        progressRecordMapper.updateByPrimaryKeySelective(
                new Img2ImgProgressRecord()
                        .setId(recordId)
                        .setDeleted(true)
        );
    }


    @Override
    public void deleteGeneratedImgByRecordIdAndMd5(Long recordId, String imgMd5) {
        generatedImageMapper.updateByRecordIdAndImgMd5Selective(
                new AIGeneratedImage()
                        .setRecordId(recordId)
                        .setGenImgMd5(imgMd5)
                        .setDeleted(true)
        );
    }


    @Override
    public void updateGeneratedImgLikeDislikeByRecordIdAndMd5(
            Long recordId, String imgMd5,
            GeneratedImageThumbupAction action,
            String dislikeReasons) {

        AIGeneratedImage selective = Try.of(() -> {

            AIGeneratedImage request = new AIGeneratedImage()
                    .setRecordId(recordId)
                    .setGenImgMd5(imgMd5);

            switch (action) {
                case dislike: {

                    return request.setIsDislike(true)
                            .setIsLike(false)
                            .setDislikeReasons(dislikeReasons);

                }

                case like: {

                    return request.setIsLike(true)
                            .setIsDislike(false);
                }

                case cancel_like: {
                    return request.setIsLike(false);
                }
                case cancel_dislike: {

                    return request.setIsDislike(false);
                }
                // 此处认为是重置所有点赞点踩；
                default: {

                    request.setIsLike(false)
                            .setIsDislike(false);
                    return request;
                }

            }
        }).get();

        generatedImageMapper.updateByRecordIdAndImgMd5Selective(selective);


    }


    @Override
    public Optional<Img2ImgProgressRecord> selectRecordByRecordId(Long recordId, boolean withGeneratedImgs) {

        return Optional.ofNullable(progressRecordMapper.selectByPrimaryKey(recordId))
                .map(record -> {

                    if (!withGeneratedImgs) {
                        return record;
                    }

                    record.setGeneratedImgs(
                            generatedImageMapper.selectByRecordIdIn(Lists.newArrayList(recordId), false));

                    return record;
                });


    }


    @Override
    public List<AIGeneratedImage> selectGeneratedImgsByRecordId(Long recordId) {

        return generatedImageMapper.selectByRecordIdIn(Lists.newArrayList(recordId), false);
    }


    /**
     * @param pn
     * @param ps
     * @param accountId
     * @param sortBy
     * @param order
     * @return
     */

    @Override
    public Pagination<List<Img2ImgProgressRecord>> pageByAccountIdAndStatusIn(
            Long accountId, @Nullable List<Img2ImgProgressStatus> progressStatus,
            Integer pn, Integer ps, MaterialSortBy sortBy, MaterialSortOrder order
    ) {

        Page<Img2ImgProgressRecord> page = PageHelper.startPage(pn, ps)
                .doSelectPage(new ISelect() {
                    @Override
                    public void doSelect() {
                        progressRecordMapper.selectByAccountIdAndStatusInOrderByCtimeDesc(
                                accountId, progressStatus, false);
                    }
                });

        Map<Long, List<AIGeneratedImage>> generatedImgs = Try.of(() -> {
            List<AIGeneratedImage> images = generatedImageMapper.selectByRecordIdIn(
                    page.getResult().stream().map(r -> r.getId())
                            .distinct()
                            .collect(Collectors.toList()),
                    false);

            return images.stream().collect(Collectors.groupingBy(
                    img -> img.getRecordId()
            ));

        }).getOrElse(new HashMap<>());

        return Pagination.fromPageHelper(page)
                .map(records -> {

                    return records.stream()
                            .map(record -> {

                                List<AIGeneratedImage> gen = Optional.ofNullable(generatedImgs.get(record.getId()))
                                        .orElse(new ArrayList<>());

                                record.setGeneratedImgs(gen);

                                return record;
                            })
                            .collect(Collectors.toList());

                });


    }

}
