package com.bilibili.mgk.material.center.service.course.vo;

import com.bilibili.mgk.material.center.service.course.model.CreativeCourse;
import com.biz.common.doc.tree.common.SnakeCaseBody;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/18
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CreativeCourseSearchResult extends CreativeCourse implements SnakeCaseBody {


    private List<String> docTitleHighlight;

    private List<String> docSummaryHighlight;

    private List<String> docContentHighlight;

}
