package com.bilibili.mgk.material.center.service.creative.vo;

import com.bilibili.mgk.material.center.service.creative.model.MaterialType;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/6
 */
@ApiModel
public enum ImgTypeQuery {

    all(""),
    /**
     *
     */
    gif(""),

    /**
     * 当前查询情境下只需要区分gif和其他
     */
    except_gif(""),

    ;


    private String desc;


    ImgTypeQuery(String desc) {
        this.desc = desc;
    }


    public static List<String> toOnesQueryValue(ImgTypeQuery imgType) {

        if (imgType == null) {
            return Lists.newArrayList(MaterialType.img.getMaterialTypeId().toString());
        } else if (imgType == ImgTypeQuery.all) {
            return Lists.newArrayList(MaterialType.gif.getMaterialTypeId().toString(),
                    MaterialType.img.getMaterialTypeId().toString());
        } else if (imgType == ImgTypeQuery.gif) {
            return Lists.newArrayList(MaterialType.gif.getMaterialTypeId().toString());
        } else if (imgType == ImgTypeQuery.except_gif) {

            return Lists.newArrayList(MaterialType.img.getMaterialTypeId().toString());
        } else {
            return Lists.newArrayList(MaterialType.img.getMaterialTypeId().toString());
        }

    }


    public static List<String> toOnesQueryValueV2(ImgTypeQuery imgType) {

        if (imgType == null) {
            return Lists.newArrayList(MaterialType.img.getMaterialTypeIdV2().toString());
        } else if (imgType == ImgTypeQuery.all) {
            return Lists.newArrayList(MaterialType.gif.getMaterialTypeIdV2().toString(),
                    MaterialType.img.getMaterialTypeIdV2().toString());
        } else if (imgType == ImgTypeQuery.gif) {
            return Lists.newArrayList(MaterialType.gif.getMaterialTypeIdV2().toString());
        } else if (imgType == ImgTypeQuery.except_gif) {

            return Lists.newArrayList(MaterialType.img.getMaterialTypeIdV2().toString());
        } else {
            return Lists.newArrayList(MaterialType.img.getMaterialTypeIdV2().toString());
        }

    }



}
