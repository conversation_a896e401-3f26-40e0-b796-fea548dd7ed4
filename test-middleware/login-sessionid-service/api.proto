// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package infra.infqa.loginsessionid.service.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/test-middleware/login.sessionid.service;api";
option java_package = "com.bapis.test.middleware.login.sessionid.service";
option java_multiple_files = true;
option (wdcli.appid) = "infra.inf-qa.login-sessionid-service";

service LoginSessionIDService {
  rpc GetAJSessionID(AJSessionIDReq) returns (AJSessionIDResp) {
    option (google.api.http) = {
      post: "/infqa/ajsessionid"
      body: "*"
    };
  };
}

message AJSessionIDReq {
  string user_name = 1 [(gogoproto.moretags) = 'json:"user_name" form:"user_name"'];
  string password = 2 [(gogoproto.moretags) = 'json:"password" form:"password"'];
  string secret_key = 3 [(gogoproto.moretags) = 'json:"secret_key" form:"secret_key"'];
}

message AJSessionIDResp {
  string session_id = 1 [(gogoproto.moretags) = 'json:"session_id"'];
}
