package com.bilibili.collage.api.dto;

import com.bilibili.adp.common.util.Utils;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/12/23 下午12:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CollageEnterpriseVideoQueryDto implements Serializable {

    private static final long serialVersionUID = 784141453990901099L;

    private Integer accountId;

    private Integer page;
    private Integer pageSize;

    private String orderBy;


    /**
     * 使用avid 进行过滤
     */
    private Long avid;


}
