package com.bilibili.collage.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @file: QueryCollageCoverDto
 * @author: gaoming
 * @date: 2020/11/30
 * @version: 1.0
 * @description:
 **/

@Data
@Builder
@AllArgsConstructor
public class QueryCollageCoverDto implements Serializable {
    private static final long serialVersionUID = -1078009028624486394L;

    /**
     * 对象Ids
     */
    private List<Integer> objIds;

    /**
     * 对象类型
     */
    private List<Integer> objTypes;
}
