package com.bilibili.collage.api.service.customer;

import com.bilibili.collage.api.dto.DrawingDownloadDto;
import com.bilibili.collage.api.dto.DrawingReqDto;
import com.bilibili.collage.api.dto.DrawingRespDto;

import java.io.IOException;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @date 2018/12/11
 **/
public interface IDrawingService {

    DrawingRespDto draw (DrawingReqDto reqDto);

    void download (Integer accountId, String downloadKey, OutputStream out) throws IOException;

    String getDownloadKey (DrawingDownloadDto downloadDto);
}
