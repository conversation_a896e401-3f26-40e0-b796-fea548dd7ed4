// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

package openplatform.grand.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/openplatform-grand/service.provider;v1";
option (gogoproto.goproto_getters_all) = false;
option java_package = "com.bapis.openplatform.grand.v1";
option java_multiple_files = true;
option (wdcli.appid) = "live.openplatform.grand";


service provider {
  // 获取限流封禁黑名单
  rpc MetadataGet(MetadataGetReq) returns(MetadataGetRes);
  // 同步规则
  rpc SyncRule(SyncRuleReq) returns(SyncRuleRes);

  // 同步接口超限记录
  rpc SyncQpxBan(SyncQpxBanReq) returns(SyncQpxBanRes);
}




message MetadataGetReq{
  // api白名单最新版本
  int64 white_api_version = 1 [(gogoproto.jsontag) = "white_api_version", (gogoproto.moretags) = 'form:"white_api_version"'];
  // 封禁最新版本
  int64 ban_version = 2 [(gogoproto.jsontag) = "ban_version", (gogoproto.moretags) = 'form:"ban_version"'];
  // 规则转封禁最新版本
  int64 rule_ban_version = 3 [(gogoproto.jsontag) = "rule_ban_version", (gogoproto.moretags) = 'form:"rule_ban_version"'];
  // true：全量更新 false: 增量
  bool full = 4 [(gogoproto.jsontag) = "full", (gogoproto.moretags) = 'form:"full"'];
  int64 tree_id = 5 [(gogoproto.jsontag) = "tree_id", (gogoproto.moretags) = 'form:"tree_id"'];
}


message MetadataGetRes{
  int64 white_api_version = 1 [(gogoproto.jsontag) = "white_api_version", (gogoproto.moretags) = 'form:"white_api_version"'];
  int64 ban_version = 2 [(gogoproto.jsontag) = "ban_version", (gogoproto.moretags) = 'form:"ban_version"'];
  int64 rule_ban_version = 3 [(gogoproto.jsontag) = "rule_ban_version", (gogoproto.moretags) = 'form:"rule_ban_version"'];
  // key: 维度_维度值  val: 黑名单生效时间: -1 永久
  // 封禁名单
  map<string, int64> ban_map = 5 [(gogoproto.jsontag) = "ban_map", (gogoproto.moretags) = 'form:"ban_map"'];
  // 规则转封禁名单
  map<string, int64> rule_ban_map = 6 [(gogoproto.jsontag) = "rule_ban_map", (gogoproto.moretags) = 'form:"rule_ban_map"'];
  // 规则转api白名单
  map<string, int64> white_api_map = 7 [(gogoproto.jsontag) = "white_api_map", (gogoproto.moretags) = 'form:"white_api_map"'];
}

message SyncRuleReq{
  int64 rule_version = 1 [(gogoproto.jsontag) = "rule_version", (gogoproto.moretags) = 'form:"rule_version"'];
  bool full = 2 [(gogoproto.jsontag) = "full", (gogoproto.moretags) = 'form:"full"'];
  int64 tree_id = 3 [(gogoproto.jsontag) = "tree_id", (gogoproto.moretags) = 'form:"tree_id"'];
  int64 id_offset = 4 [(gogoproto.jsontag) = "id_offset", (gogoproto.moretags) = 'form:"id_offset"'];
}


message MRule{
  string Url = 1 [(gogoproto.jsontag) = "Url", (gogoproto.moretags) = 'form:"Url"'];
  string Dm = 2 [(gogoproto.jsontag) = "Dm", (gogoproto.moretags) = 'form:"Dm"'];
  string DmId = 3 [(gogoproto.jsontag) = "DmId", (gogoproto.moretags) = 'form:"DmId"'];
  int64 Qps = 4 [(gogoproto.jsontag) = "Qps", (gogoproto.moretags) = 'form:"Qps"'];
  int64 enabled = 5 [(gogoproto.jsontag) = "enabled", (gogoproto.moretags) = 'form:"enabled"'];
  int64 Qpd = 6 [(gogoproto.jsontag) = "Qpd", (gogoproto.moretags) = 'form:"Qpd"'];
  int64 QpM = 7 [(gogoproto.jsontag) = "QpM", (gogoproto.moretags) = 'form:"QpM"'];
  int64 version = 8 [(gogoproto.jsontag) = "version", (gogoproto.moretags) = 'form:"version"'];
  int64 id = 9 [(gogoproto.jsontag) = "id", (gogoproto.moretags) = 'form:"id"'];
  string title = 10  [(gogoproto.jsontag) = "title", (gogoproto.moretags) = 'form:"title"'];
}

message SyncRuleRes{
  int64 rule_version = 1 [(gogoproto.jsontag) = "rule_version", (gogoproto.moretags) = 'form:"rule_version"'];
  repeated MRule rules = 2 [(gogoproto.jsontag) = "rules", (gogoproto.moretags) = 'form:"rules"'];
  // next 是否有下一页
  bool next = 3 [(gogoproto.jsontag) = "tree_id", (gogoproto.moretags) = 'form:"tree_id"'];
  int64 id_offset = 4 [(gogoproto.jsontag) = "id_offset", (gogoproto.moretags) = 'form:"id_offset"'];
}



message SyncQpxBanReq{
  int64 tree_id = 1 [(gogoproto.jsontag) = "tree_id", (gogoproto.moretags) = 'form:"tree_id"'];
  // 分页查询用
  int64 id_offset = 2 [(gogoproto.jsontag) = "id_offset", (gogoproto.moretags) = 'form:"id_offset"'];
  // 增量更新用，版本号 实际是mtime
  int64 version = 3 [(gogoproto.jsontag) = "version", (gogoproto.moretags) = 'form:"version"'];
  // 日期 20240102
  string date_type = 4 [(gogoproto.jsontag) = "date_type", (gogoproto.moretags) = 'form:"date_type"'];
  // 日期 20240102;
  string date_key = 5  [(gogoproto.jsontag) = "date_key", (gogoproto.moretags) = 'form:"date_key"'];
}


message QpxBan{
  string url = 1 [(gogoproto.jsontag) = "url", (gogoproto.moretags) = 'form:"url"'];
  string dm = 2 [(gogoproto.jsontag) = "dm", (gogoproto.moretags) = 'form:"dm"'];
  string dm_id = 3 [(gogoproto.jsontag) = "dm_id", (gogoproto.moretags) = 'form:"dm_id"'];
  int64 version = 4 [(gogoproto.jsontag) = "version", (gogoproto.moretags) = 'form:"version"'];
  int64 qpx = 5 [(gogoproto.jsontag) = "qpx", (gogoproto.moretags) = 'form:"qpx"'];
  //  // qpd 20240102;qpM 202401
  //  string date_key = 4  [(gogoproto.jsontag) = "version", (gogoproto.moretags) = 'form:"date_key"'];
  // 规则是否失效；
  // 增量查询的时候可能会返回false;超限记录后，上调了规则的qpx，那么该记录可能失效了，需要给到sdk感知失效
  bool enabled = 6 [(gogoproto.jsontag) = "enabled", (gogoproto.moretags) = 'form:"enabled"'];
}

message SyncQpxBanRes{
  // next 是否有下一页
  bool next = 1 [(gogoproto.jsontag) = "tree_id", (gogoproto.moretags) = 'form:"tree_id"'];
  int64 id_offset = 2 [(gogoproto.jsontag) = "id_offset", (gogoproto.moretags) = 'form:"id_offset"'];
  int64 version = 3 [(gogoproto.jsontag) = "version", (gogoproto.moretags) = 'form:"version"'];
  string date_key = 4  [(gogoproto.jsontag) = "date_key", (gogoproto.moretags) = 'form:"date_key"'];
  repeated QpxBan  list = 5 [(gogoproto.jsontag) = "records", (gogoproto.moretags) = 'form:"records"'];
}
