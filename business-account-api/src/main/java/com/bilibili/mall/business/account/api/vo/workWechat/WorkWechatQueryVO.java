package com.bilibili.mall.business.account.api.vo.workWechat;

import com.bilibili.mall.business.account.api.vo.base.BasePageVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @since 2024/2/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("企微查询")
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class WorkWechatQueryVO extends BasePageVO {
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("状态, 1-有效, 2-无效")
    private Integer status;
    @ApiModelProperty("ID")
    private Long id;
}
