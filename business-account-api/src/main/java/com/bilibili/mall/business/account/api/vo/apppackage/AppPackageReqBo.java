package com.bilibili.mall.business.account.api.vo.apppackage;

import jnr.ffi.annotations.In;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppPackageReqBo {

    private Integer page;

    private Integer pageSize;

    private String appName;

    private List<Integer> platforms;

    /**
     * -1 全部返回  0 有效  1 无效
     */
    private Integer appStatus;
}
