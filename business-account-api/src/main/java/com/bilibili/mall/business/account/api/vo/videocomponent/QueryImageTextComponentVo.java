package com.bilibili.mall.business.account.api.vo.videocomponent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/9/20
 **/
@Data
public class QueryImageTextComponentVo {

    @ApiModelProperty("动态ID")
    @NotNull(message = "动态ID不能为空")
    private List<Long> dynamicIds;

    private String businessComponentId;
}
