package com.bilibili.mall.business.account.api.service.intranet;

import com.bilibili.mall.business.account.api.vo.booking.BookingQueryVO;
import com.bilibili.mall.business.account.api.vo.booking.BookingVO;
import com.bilibili.mall.business.account.api.vo.bookingcomponent.BookingComponentDTO;
import com.bilibili.mall.business.account.api.vo.bookingcomponent.BookingComponentQuery;
import com.bilibili.mall.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/4/10 14:40
 */
@Api(value = "BusinessComponentRemoteService", tags = "组件相关接口")
@RequestMapping("/business-account/intranet/businesscomponent")
public interface BusinessComponentRemoteService {

    @ApiOperation(value = "查询在线预约客服组件列表", notes = "查询在线预约客服组件列表")
    @PostMapping("/list")
    BaseResponse<PageInfo<BookingComponentDTO>> queryBookingComponentList(@Valid @RequestBody BookingComponentQuery bookingComponentQuery);

    @ApiOperation(value = "查询预约组件列表", notes = "查询预约组件列表")
    @PostMapping("/booking_list")
    BaseResponse<PageInfo<BookingVO>> queryBooking(@Valid @RequestBody BookingQueryVO queryVO);
}
