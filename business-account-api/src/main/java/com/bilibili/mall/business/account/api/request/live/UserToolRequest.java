package com.bilibili.mall.business.account.api.request.live;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/2/27 6:56 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserToolRequest {

    @NotNull(message = "uid不能为空")
    @ApiModelProperty(value = "uid")
    private Long uid;
    // 0 在线预约 1 联系企微 2 微信小程序 3 在线咨询 4 三方落地页 5 应用推广
    @ApiModelProperty(value = "组件类型")
    private Integer authType;
}
