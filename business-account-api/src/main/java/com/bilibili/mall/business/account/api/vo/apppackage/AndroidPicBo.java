package com.bilibili.mall.business.account.api.vo.apppackage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AndroidPicBo {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * lau_app_conversion_component 组件ID
     */
    private Long appConversionComponentId;

    /**
     * 详情页图片
     */
    private String detailPic;

    /**
     * 1-横图2:1 , 2-竖图3：5
     */
    private Integer type;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 是否删除
     */
    private Integer isDeleted;


}
