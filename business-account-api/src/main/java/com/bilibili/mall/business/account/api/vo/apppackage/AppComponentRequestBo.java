package com.bilibili.mall.business.account.api.vo.apppackage;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppComponentRequestBo{
    /**
     * 工具名称
     */
    private String bizName;

    /**
     * 组件id
     */
    private String toolId;

    /**
     * 0-已生效 1 已失效
     */
    private Integer bizStatus;

    private Integer page;

    private Integer pageSize;

    private String pageId;

}
