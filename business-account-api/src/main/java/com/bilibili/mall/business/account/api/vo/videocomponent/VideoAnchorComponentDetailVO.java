package com.bilibili.mall.business.account.api.vo.videocomponent;

import com.bilibili.mall.business.account.api.vo.qualification.QualificationVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class VideoAnchorComponentDetailVO {

    @ApiModelProperty("组件id")
    private Long componentId;

    @ApiModelProperty("转化组件类型")
    private String businessComponentCode;

    @ApiModelProperty("转化组件ID")
    private String businessComponentId;

    @ApiModelProperty("转化组件名称")
    private String businessComponentName;

    @ApiModelProperty("组件状态 0审核中 1审核通过 2审核拒绝")
    private Integer componentStatus;

    @ApiModelProperty("拒绝原因")
    private String rejectReason;

    @ApiModelProperty("主标题")
    private String mainTitle;

    @ApiModelProperty("副标题")
    private String subTitle;

    @ApiModelProperty("按钮文案")
    private String buttonText;

    @ApiModelProperty("资质列表")
    private List<QualificationVO> qualificationList;
}
