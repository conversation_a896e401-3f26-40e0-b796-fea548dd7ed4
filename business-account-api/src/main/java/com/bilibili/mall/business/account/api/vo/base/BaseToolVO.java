package com.bilibili.mall.business.account.api.vo.base;

import com.bilibili.mall.business.account.api.vo.apppackage.AppPackageBo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/2/21
 */
@Data
@ApiModel("BaseToolVO")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BaseToolVO {
    @ApiModelProperty("ID")
    private String id;
    @ApiModelProperty("原始id")
    private String originId;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("小程序url")
    private String gameUrl;
    @ApiModelProperty("主图片地址")
    private String imgUrl;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("状态，不同工具类型状态各异，使用者自己解析")
    private Integer status;
    @ApiModelProperty(value = "投放链接")
    @JsonIgnore
    private String launchUrl;
    @ApiModelProperty(value = "ios app信息")
    private AppPackageBo iosAppPackageBo;
    @ApiModelProperty(value = "android app信息")
    private AppPackageBo androidAppPackageBo;
}
