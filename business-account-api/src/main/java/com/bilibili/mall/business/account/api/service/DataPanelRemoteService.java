package com.bilibili.mall.business.account.api.service;

import com.bilibili.mall.business.account.api.vo.base.BaseDataExportRepVO;
import com.bilibili.mall.business.account.api.vo.base.BaseDataQueryVO;
import com.bilibili.mall.business.account.api.vo.common.PageVO;
import com.bilibili.mall.business.account.api.vo.datapanel.*;
import com.bilibili.mall.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(value = "DataPanelRemoteService", tags = "数据看板")
@RequestMapping("/business-account/data_panel")
public interface DataPanelRemoteService {
    @ApiOperation(value = "总数据面板查询", notes = "总数据面板查询")
    @PostMapping("/total")
    BaseResponse<TotalDataPanelModelVO> totalDataPanelQuery(@RequestBody SumPanelQueryVO sumPanelQueryVO);

    @ApiOperation(value = "工具数据面板列表查询", notes = "工具数据面板列表查询")
    @PostMapping("/tool/list")
    BaseResponse<PageVO<ToolListDataPanelModelVO>> toolListDataPanelQuery(@RequestBody ToolPanelQueryVO toolPanelQueryVO);

    @ApiOperation(value = "在线咨询数据总计查询", notes = "在线咨询数据总计查询")
    @PostMapping("/online_consult_total")
    BaseResponse<OnlineConsultTotalDataVO> onlineConsultTotalDataPanelQuery(@RequestBody OnlineConsultTotalDataPanelQuery query);

    @ApiOperation(value = "在线咨询天维度数据查询", notes = "在线咨询天维度数据查询")
    @PostMapping("/online_consult_day_data")
    BaseResponse<List<OnlineConsultDataByDayVO>> onlineConsultDataByDayQuery(@RequestBody OnlineConsultTotalDataPanelQuery query);

    @ApiOperation(value = "在线咨询来源维度数据查询", notes = "在线咨询来源维度数据查询")
    @PostMapping("/online_consult_source_data")
    BaseResponse<List<OnlineConsultDataBySourceVO>> onlineConsultDataBySourceQuery(@RequestBody OnlineConsultTotalDataPanelQuery query);

    @ApiOperation(value = "视频经营数-总览", notes = "视频经营数据-总览")
    @PostMapping("/total_video_manager")
    BaseResponse<VideoManagerBo> totalVideoManager(@RequestBody VideoManagerQueryVo queryVo);

    @ApiOperation(value = "视频经营数-list", notes = "视频经营数据-list")
    @PostMapping("/detail_video_manager")
    BaseResponse<PageInfo<VideoManagerBo>> detailVideoManager(@RequestBody VideoManagerQueryVo queryVo);

    @ApiOperation(value = "视频经营数-导出数据明细",notes = "视频经营数-导出数据明细")
    @PostMapping("/detail_video_manager/export")
    BaseResponse<BaseDataExportRepVO> exportVideoManagerData(@Valid @RequestBody VideoManagerQueryVo queryVo);


    @ApiOperation(value = "直播经营数据-总览", notes = "直播经营数据-总览")
    @PostMapping("/total_live_manager")
    BaseResponse<LiveManagerTotalBo> totalLiveManager(@RequestBody LiveManagerQueryVo queryVo);

    @ApiOperation(value = "直播经营数据-list", notes = "直播经营数据-list")
    @PostMapping("/detail_live_manager")
    BaseResponse<PageInfo<LiveManagerDetailBo>> detailLiveManager(@RequestBody LiveManagerQueryVo queryVo);

    @ApiOperation(value = "直播经营数据-导出数据明细",notes = "直播经营数据-导出数据明细")
    @PostMapping("/detail_live_manager/export")
    BaseResponse<LiveManagerDetailBo> exportLiveManagerData(@Valid @RequestBody LiveManagerQueryVo queryVo);

    @ApiOperation(value = "图文-总览", notes = "图文-总览")
    @PostMapping("/total_dynamic_manager")
    BaseResponse<DynamicManagerBo> totalDynamicManager(@RequestBody DynamicManagerQueryVo queryVo);

    @ApiOperation(value = "图文-list", notes = "图文-list")
    @PostMapping("/detail_dynamic_manager")
    BaseResponse<PageInfo<DynamicManagerBo>> detailDynamicManager(@RequestBody DynamicManagerQueryVo queryVo);

    @ApiOperation(value = "图文-导出数据明细",notes = "图文-导出数据明细")
    @PostMapping("/detail_dynamic_manager/export")
    BaseResponse<BaseDataExportRepVO> exportDynamicManagerData(@Valid @RequestBody DynamicManagerQueryVo queryVo);

    @ApiOperation(value = "获取全部可选择自定义列", notes = "获取全部可选择自定义列")
    @GetMapping("/can_chose_customer_column")
    BaseResponse<List<String>> getCanChoseCustomerColumn(@RequestParam("bizName   liveDataDetail：直播数据选择列") String bizName);

    @ApiOperation(value = "获取用户配置的自定义列", notes = "获取用户配置的自定义列")
    @GetMapping("/customer_column")
    BaseResponse<List<String>> getCustomerColumn(@RequestParam("bizName") String bizName);

    @ApiOperation(value = "保存用户配置的自定义列", notes = "保存用户配置的自定义列")
    @PostMapping("/save_customer_column")
    BaseResponse<String> saveCustomerColumn(@RequestBody SaveCustomerColumnReqBo saveCustomerColumnReqBo);

}
