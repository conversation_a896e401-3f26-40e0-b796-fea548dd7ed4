package com.bilibili.mall.business.account.api.vo.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/31 11:37 上午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserLoginAccountVO {
    @ApiModelProperty("用户ID")
    private Long uid;

    @ApiModelProperty("用户昵称")
    private String userNickName;

    @ApiModelProperty("用户头像")
    private String userFace;

    @ApiModelProperty("经营号id")
    private Long accountId;

    @ApiModelProperty("角色标记 1：企业账号 2：员工账号")
    private Integer roleType;

    @ApiModelProperty("员工授权列表 RESERVE:在线预约、CONTACT_QW:联系企微、MINI_APP:微信小程序、ONLINE_CONSULT:在线咨询")
    private List<String> authList;

    private List<String> authListV2;

    /**
     * 是否有三方落地页权限
     */
    private Boolean hasThirdPartyLandingPage;

    /**
     * 是否有视频数据分析商业权限
     */
    private Boolean hasVideoManagerBusinessData;

    @ApiModelProperty("shopId")
    private Integer shopId;

    /**
     * 一跳唤起链接权限
     */
    private boolean firstJumpCallUp;

    private boolean isPersonalAccount;


}
