package com.bilibili.mall.business.account.api.service;

import com.bilibili.mall.business.account.api.vo.base.BaseToolVO;
import com.bilibili.mall.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/21
 */
@Api(value = "BaseToolRemoteService", tags = "工具基础相关")
@RequestMapping("/business-account/tools/base")
public interface BaseToolRemoteService {

    @ApiOperation(value = "工具详情", notes = "工具详情")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "toolId", value = "工具id", dataType = "Long", paramType = "query", required = true),
            @ApiImplicitParam(name = "toolType", value = "工具类型 RESERVE-预约, CONTACT_QW-企微, MINI_APP-小程序", dataType = "String", paramType = "query", required = true)
    })
    @GetMapping("/detail")
    BaseResponse<BaseToolVO> findTools(@RequestParam("toolId") Long toolId, @RequestParam("toolType") String toolType);

    @ApiOperation(value = "工具列表", notes = "工具列表")
    @ApiImplicitParam(name = "toolType", value = "工具类型 RESERVE-预约, CONTACT_QW-企微, MINI_APP-小程序", dataType = "String", paramType = "query", required = true)
    @GetMapping("/query")
    BaseResponse<List<BaseToolVO>> queryTools(@RequestParam("toolType") String toolType);

    @ApiOperation(value = "上传文件接口", notes = "上传文件")
    @PostMapping(value = "/file_upload")
    public BaseResponse<String> fileUpload();

    @ApiOperation(value = "上传文件接口完整路径", notes = "上传文件完整路径")
    @PostMapping(value = "/file_upload_full_name")
    public BaseResponse<String> fileUploadFullName();

    @ApiOperation(value = "校验是否绑定广告账户", notes = "校验是否绑定广告账户")
    @GetMapping(value = "/check/adId")
    BaseResponse<Boolean> checkAdId();
}
