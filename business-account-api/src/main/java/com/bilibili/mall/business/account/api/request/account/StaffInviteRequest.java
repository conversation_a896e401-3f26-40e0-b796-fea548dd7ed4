package com.bilibili.mall.business.account.api.request.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/27 6:56 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaffInviteRequest {

    @NotNull(message = "员工id不能为空")
    @ApiModelProperty(value = "员工id")
    private Long staffUid;


    @ApiModelProperty(value = "员工备注")
    private String staffRemarkName;


    @NotNull(message = "员工权限列表不能为空")
    @ApiModelProperty(value = "员工权限列表")
    private List<String> authList;
}
