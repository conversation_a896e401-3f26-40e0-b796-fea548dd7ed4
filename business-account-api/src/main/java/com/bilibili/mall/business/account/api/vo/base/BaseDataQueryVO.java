package com.bilibili.mall.business.account.api.vo.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/2/1
 */
@Data
@ApiModel("查询导出数据区间")
@EqualsAndHashCode(callSuper = true)
public class BaseDataQueryVO extends BasePageVO {
    @ApiModelProperty("开始时间")
    private Date start;
    @ApiModelProperty("结束时间")
    private Date end;
    @ApiModelProperty("工具id")
    private Long toolId;
    @ApiModelProperty("异步请求key")
    private String asyncKey;
    @ApiModelProperty("是否异步")
    private Boolean async;
}
