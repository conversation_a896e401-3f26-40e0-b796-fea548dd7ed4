
//apply from: 'properties.gradle'
apply from: 'thirdparty.gradle'
group = 'com.bilibili.sycp'
version = '0.0.1-SNAPSHOT'


allprojects {
    apply plugin: 'idea'
    repositories {
        maven { url "https://nexus.bilibili.co/content/groups/public/" }
        maven { url "https://nexus.bilibili.co/content/repositories/releases/" }
        maven { url "https://nexus.bilibili.co/content/repositories/central/" }
        maven { url "https://nexus.bilibili.co/content/repositories/snapshots/" }
        maven { url "https://nexus.bilibili.co/content/repositories/bapis/" }
    }
}

repositories {
    maven { url "https://nexus.bilibili.co/content/groups/public/" }
    maven { url "https://nexus.bilibili.co/content/repositories/" }
    mavenCentral()
}


subprojects {
    apply plugin: 'java'

    tasks.named('test') {
        useJUnitPlatform()
    }
}




