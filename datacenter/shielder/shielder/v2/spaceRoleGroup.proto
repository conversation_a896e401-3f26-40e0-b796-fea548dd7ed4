syntax = "proto3";

package datacenter.shielder.shielder.v2;

option go_package = "buf.bilibili.co/bapis/bapis-gen/datacenter/shielder.shielder.v2;api";
option java_package = "com.bapis.datacenter.shielder.shielder.v2";
option java_multiple_files = true;

service SpaceRoleGroup {
    rpc listSpaceRoleGroup(ListSpaceRoleGroupRequest) returns(ListSpaceRoleGroupResponse);
    rpc addSpaceRoleGroup(AddSpaceRoleGroupRequest) returns(AddSpaceRoleGroupResponse);
}

message ListSpaceRoleGroupRequest {}
message ListSpaceRoleGroupResponse {
    repeated RoleGroupResult roleGroups = 1;

    message RoleGroupResult {
        string roleGroupAccount = 1;
        string accountFormat = 2;
        string nickname = 3;
        string comment = 4;
        int32 approvalStatus = 5;
        int64 ctime = 6;
        int64 mtime = 7;
    }
}

message AddSpaceRoleGroupRequest {
    string formatKey = 1;
    string nickname = 2;
    string comment = 3;
    string creatorAccount = 4;
    int32 approvalStatus = 5;
}
message AddSpaceRoleGroupResponse {}