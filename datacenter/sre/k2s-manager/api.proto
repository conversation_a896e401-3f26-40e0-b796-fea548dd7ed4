syntax = "proto3";

package datacenter.sre.k2smanager.v1;
import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "datacenter.alter.k2s-manager";
option go_package = "buf.bilibili.co/bapis/bapis-gen/datacenter/sre.k2s.manager;api";
option java_multiple_files = true;
option java_package = "com.bapis.datacenter.sre.k2s.manager";

service Manager {
    rpc ListNodes (ListNodeReq) returns (ListNodesResp);
}

message ListNodesResp {
    repeated Node nodes = 1;
    int64 total = 2;
}

message ListNodeReq {
    repeated Label labels = 1;
    int64 page_size = 2;
    int64 page_num = 3;
    string hostname = 4;
    string internal_ip = 5;
}

message Node {
    string hostname = 1;
    string bs = 2;
    string internal_ip = 3;
    string location = 4;
    string rack = 5;
    map<string, string> attr = 6;
}

message Label {
    string bs = 1;
    string name = 2;
    string value = 3;
}