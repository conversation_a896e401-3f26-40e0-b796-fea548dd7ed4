// Dqc服务端代码
syntax = "proto3";

import "datacenter/service/dqc/common.proto";
option java_multiple_files = true;
option java_package = "com.bapis.datacenter.service.dqc";
option go_package = "buf.bilibili.co/bapis/bapis-gen/datacenter/service.dqc;api";

package datacenter.service.dqc;
//注释
service DqcExecutorService {
    rpc BatchExecuteRules(RuleReq) returns(RuleTaskList) {}
    rpc BatchQueryRuleTask(LongList) returns(RuleTaskList) {}
    rpc BatchStopRules(LongList) returns(RuleTaskList) {}
}

message RuleReq {
    LongList ruleIdList = 1;
    string targetTableName = 2;
    ArcherRuleReq archerRuleReq = 3;
    string referenceTableName = 4;
}

message ArcherRuleReq {
    int64 expressionTime = 1; // 上游archer传递的时间
    int32 executeType = 2;
    string traceId = 3;
}

message RuleTask {
    int64 ruleTaskId = 1;
    int32 taskStatus = 2; //取值自TaskStatus枚举值
    string taskResult = 3;
    repeated TaskJudgeInfo taskJudgeInfo = 4;
    int64 startRunTime = 5;
    int64 endRunTime = 6;
    int64 ruleId = 7;
    string errorMsg = 8; // 失败原因
}

message TaskJudgeInfo {
    TaskJudgeStatus taskJudgeStatus = 1;
    string alertType = 2;
}

message RuleTaskList {
    repeated RuleTask ruleTaskList = 1;
    
}