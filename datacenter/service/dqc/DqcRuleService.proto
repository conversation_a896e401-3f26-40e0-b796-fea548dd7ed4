syntax = "proto3";

import "datacenter/service/dqc/common.proto";
option java_multiple_files = true;
option java_package = "com.bapis.datacenter.service.dqc";
option go_package = "buf.bilibili.co/bapis/bapis-gen/datacenter/service.dqc;api";

package datacenter.service.dqc;
//注释
service DqcRuleService {
    rpc GetDqcRule(DqcRuleParam) returns (DqcRuleList) {} //根据表名查规则列表
}

message DqcRuleParam {
    string tableName = 1;
    string tmpTableName = 2;
}