syntax = "proto3";

package datacenter.service.oneservice.v1;

import "datacenter/service/oneservice/common.proto";
import "datacenter/service/oneservice/model.proto";
import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "datacenter.oneservice.associative-service";

option go_package = "buf.bilibili.co/bapis/bapis-gen/datacenter/service.oneservice;api";
option java_package = "com.bapis.datacenter.service.oneservice";
option java_multiple_files = true;


// 直播中台专用接口
service OneServiceLiveMiddlePlatform {

    // 指标维度离线+实时数据混合查询api
    rpc mixtureQuery2(MixtureQuery2Req) returns (MixtureQuery2Resp);
}

message MixtureQuery2Req {
    // 请求头
    OsHeader osHeader = 1;
    // 离线数据api请求
    OpenApiReqV2 offlineReq = 2;
    // 实时数据api请求
    OpenApiReqV2 realTimeReq = 3;
    // 跨api混合二次计算 (会针对实时数据进行按天聚合后,再进行二次计算)
    repeated MixtureCalculate mixtureCalculates = 4;
}

message MixtureCalculate {
    // 计算后的字段名
    string field = 1;
    // api指标集合 e.g. api1.m1, api2.m2
    repeated string apiMetrics = 2;
    // 计算类型 支持:sum,max,min,avg
    string calculateType = 3;
}

message MixtureQuery2Resp {
    // 二次计算后的结果 填写mixtureCalculates返回
    MapValue calculateResult = 1;
    // 离线api数据 未填mixtureCalculates返回
    OpenApiResp offlineResp = 2;
    // 实时api数据 未填mixtureCalculates返回
    OpenApiResp realTimeResp = 3;
}