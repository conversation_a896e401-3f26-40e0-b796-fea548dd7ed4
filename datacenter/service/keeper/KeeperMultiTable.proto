syntax = "proto3";

// use {app_id}.{version} as package name
package datacenter.service.keeper.v1;

import "datacenter/service/keeper/common.proto";
import "datacenter/service/keeper/Keeper.proto";
option go_package = "buf.bilibili.co/bapis/bapis-gen/datacenter/service.keeper;api";
option java_package = "com.bapis.datacenter.service.keeper";
option java_multiple_files = true;

// 多种数据源表接口
service KeeperMultiTable {

    //创建表
    rpc CreateTable (BaseTable) returns (NoReply);
    //通用返回结构
    rpc CreateTableWithCode (BaseTable) returns (CommonReply);
    //获取数据源权限接口
    rpc getDsPrivilegesByUser(DsUserPrivilegeRequest) returns(AuthItemCommon);
    //获取数据库权限接口
    rpc getDbPrivilegesByUser(DbUserPrivilegeRequest) returns(AuthItemCommon);
    //获取表tag
    rpc getMulTableWholeTag(WholeTagReq) returns(MulTableWholeTagDtoList);
    //更新表
    rpc updateTable (BaseTable) returns (NoReply);

    //批量修改责任人接口
    rpc alterMulTableOwner(LongStrList) returns (NoReply);
    // 根据userId查询用户的mul_tab的数量
    rpc getMulTableCounts(QueryDsTypeReq) returns(RespLong);
    // 获取用户的mul_tab表
    rpc getMulTablePage(MulTableDto) returns(TableMulTablePageList);
    // 根据表名模糊查询列表（可鉴权）
    rpc getMulTableByTableName(MetaRequest) returns(TableDtoList);
    // 根据数据源名称获取所有表名（只适用mysql和tidb）
    rpc getOuterTablesByDsName(MetaRequest) returns(TableDtoList);
    // 根据 urn 获取表详情信息
    rpc getTableDetail (RequestStr) returns (ResponseStr);
}

message TableDtoList {
    repeated TableDto tableDto = 1;
}

message QueryDsTypeReq {
    DbType dsType = 1;
    int64 userId = 2;
}

message MulTableDto{
    int64 userId = 1;
    repeated string tabIds = 2;
    string searchFullTableName = 3;
    repeated string fullTableNames = 4;
    int32 current = 5;
    int32 size = 6;
    DbType dsType = 7;
}

message TableMulTablePageList{
    repeated TableMulInfo tableMulInfo=1;
    int64 total=2;
    int32 current=3;
    int32 size=4;
}

message TableMulInfo{
    string id=1;
    string tableName=2;
    string securityLevel=3;
    string businessLines=4;
    string dsName=5;
    string dbName=6;
    string urn=7;
}

//查询表请求参数
message GetTableParam {
    DbType dsType = 1; //必传
    string tabId = 2; //如果选择传tabId则下午三个参数不用传
    string dsName = 3; //如果选择传name,tabId不用传
    string dbName = 4;
    string tabName = 5;
}
//表信息
message BaseTable {
    DbType dsType = 1;
    BasicModule basicModule = 2;
    BusinessModule businessModule = 3;
    ContentModule contentModule = 4;
    ConfigurationModule configurationModule = 5;
    ModelModule modelModule = 6;
    repeated Column cols = 7;
    Datasource datasource = 8;
    bool initDataWithoutBuildTable=9;//true：插入keeper数据，而不在底层实际创建表，false创建表
}
//基本信息
message BasicModule {
    string dsId = 1;
    string dsName = 2;
    string dbId = 3;
    string dbName = 4;
    string tabId = 5;
    string tabName = 6;
    string tabDesc = 7;
    int32 dataDuration = 8;
    int32 tableDuration = 9;
    int64 userId = 10;
    string userName = 11;
    string ctime = 12;
    string mtime = 13;
    string label = 14;
    string dateTimeExpr=15;
    string unit=16;
    string action=17;
    string workspace=18;
}
//业务信息
message BusinessModule {
    StringList businessTags = 1; //业务线
    string privilegeLevel = 2;
    int64 departmentId = 3;
    string departmentName = 4;
    int32 publicLevel = 5;
}
//字段信息
message Column {
    int32 colIndex = 1;
    string colName = 2;
    string colType = 3;
    string valueDesc = 4;
    string privilegeLevel = 5;
    bool isPartition = 6;
    UnitedColTypeEnum mappingColTypeEnum=7;
    int64 decType=8;
    string colTypeDesc=9;
    string colDesc=10;
}
//内容信息
message ContentModule {
    string type = 1;
    string dataLevel = 2;
}
//配置信息
message ConfigurationModule {
    int32 partitionNumber = 1;
    string colSeparator = 2;
    string lineSeparator = 3;
    TableFormat tableFormat = 4;
    repeated StringField partitionBy=5;
    repeated StringField orderBy=6;
    repeated StringField primaryKey=7;
    repeated SecondaryIndex secondaryIndices=8;
    ShardingKey shardingKey=9;
    Engine engine=10;
    repeated CustomSetting customSettings=11;
    string kfkLabel=12;
}

message StringField {
    string label=1;
    string expression=2;
}

message SecondaryIndex {
    string name=1;
    string type=2;
    string label=3;
    string expression=4;
    int64 granularity=5;
    string indexType=6;
    repeated string typeContent=7;
}

message ShardingKey {
    string func=1;
    repeated string columns=2;
}

message Engine {
    string type=1;
}

message CustomSetting {
    string name=1;
    string value=2;
}

//模型信息
message ModelModule {
    int32 modelLevel = 1;
    LongList dataDomainList = 2;
}

enum TableFormat {
    DEFAULT = 0;
    ORC = 1;
    TEXT = 2;
}

//数据源信息
message Datasource {
    //mysql tidb clickhouse用
    string host = 1;
    string port = 2;
    string dbName = 3;
    string user = 4;
    string password = 5;
    string discoveryId = 6;
    //kfk 用
    int32 kerberos = 7; // 0 接入kerberos 1
    repeated KafkaBootstrapServer bootstrapServers = 8;
}

message KafkaBootstrapServer {
    string bootstrapServer = 1;
    string port = 2;
}

message DsUserPrivilegeRequest {
    int64 userId = 1;
    DbType dsType = 2;
}

message DbUserPrivilegeRequest {
    int64 userId = 1;
    DbType dsType = 2;
}

message AuthItemCommon{
    AuthItemList list =1;
    CommonReply reply =2;
}

message AuthItemList{
    repeated AuthItem list=1;
}
message AuthItem{
 string code =1;
 string name=2;
}

message WholeTagReq{
    DbType dsType =1;
    repeated string tableList=2;
}

message MulTableWholeTagDtoList{
    repeated MulTableWholeTagDto list=1;
}

message MulTableWholeTagDto{
    int64 id=1;
    string databaseName=2;
    string tableName=3;
    string description=4;
    int64 createUserId=5;
    int64 belongDepartmentId=6;
    int64 createDepartmentId=7;
    int64 privilegeId=8;
    int64 tableId=9;
    int32 duration=10;
    string type=11;
    string granularity=12;
    string domainTag=13;
    string layerTag=14;
    string indexTag=15;
    string dataLevel=16;
    string securityLevel=17;
    string dataDefinition=18;
    string dataInfo=19;
    string privacyLevel=20;
    int32 publicLevel=21;
    string manageType=22;
    double score=23;
    string privilegeLevel=24;
    string tagFirst=25;
    string tagSecond=26;
    string ctime=27;
    int32 dataDomain1=28;
    repeated string businessTag=29;
    int32 modelLevel=30;
    string dsName=31;
    string dsId=32;
    string dbName=33;
    string dbId=34;
    string tabId=35;
}





