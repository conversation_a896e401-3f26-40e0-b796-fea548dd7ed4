syntax = "proto3";

import "datacenter/service/saber/saberJob.proto";
option go_package = "buf.bilibili.co/bapis/bapis-gen/datacenter/service.saber;api";
option java_package = "com.bapis.datacenter.service.saber";
option java_multiple_files = true;

// The saberInfo service definition.
service SaberJobInnerService {

  /**
 修改作业，并上线-同步接口
 */
  rpc ModifyAndStartJob(ModifyAndStartJobRequest) returns (ModifyAndStartJobReply){}

  /**
上线历史版本-同步接口
*/
  rpc StartJobHistory(StartJobHistoryRequest) returns (StartJobHistoryReply){}

  /**
 * 作业下线-同步接口
 */
  rpc SyncStopJob(StopJobRequest) returns (StopJobReply){}

  /**
  查询作业
   */
  rpc QueryJob(QueryJobRequest) returns (QueryJobReply){}

  /**
  执行计划
   */
  rpc ExecutePlan(ExecutePlanRequest) returns (ExecutePlanReply){}

  /**
  执行计划（调用validate接口版本）
   */
  rpc ExecutePlanByValidate(ExecutePlanRequest) returns (ExecutePlanReply2){}

  // 查询saberJob列表
  rpc GetJobInfoList(JobInfoListReq) returns (JobInfoListResp){}
}

//-------------------修改作业，并上线----------------------
message ModifyAndStartJobRequest{
  ModifySaberJob saberJob = 1;
  Operator operator = 2;
}

message ModifyAndStartJobReply{
  bool isSuccess = 1;
  string errMsg = 2;
  string applicationId = 3;
}
/**
修改saber任务
 */
message ModifySaberJob{
  //任务id
  string jobId = 1;
  //任务名称
  string jobName = 2;
  //用户名
  string userName = 3;
  //优先级
  Priority priority = 4;
  //提交队列
  string queue = 5;
  //是否开启ck
  bool  checkpoint = 6;
  //并发度
  int32 parallelism = 8;
  //tm内存
  int32 taskManagerMemory = 9;
  //flink版本
  string flinkVersion = 10;
  //任务参数
  string mainArgs = 11;
  //集群参数
  string sysParameters = 12;
  //任务详情
  oneof job{
    //bsql任务
    BsqlJobInner bsqlJob = 13;
    //flink jar任务
    FlinkJarJobInner flinkJarJob = 14;
  }
  //checkpoint
  bool recoverFromLatestCheckpoint = 16;
  //开启AutoScaling
  bool AutoScaling = 20;
  //部署模式：老集群、新集群
  DeployMode DeployMode = 21;
}

//-------------------上线历史版本----------------------
message StartJobHistoryRequest{
  StartJobHistory startJobHistory = 1;
  Operator operator = 2;
}
message StartJobHistoryReply{
  bool isSuccess = 1;
  string errMsg = 2;
  string applicationId = 3;
}
message StartJobHistory{
  int64  jobHistoryId = 1;
  //checkpoint
  bool recoverFromLatestCheckpoint = 2;
  //任务id
  string jobId = 3;
}
//-------------------------查询任务---------------------------
message QueryJobRequest{
  //yarn application_id
  string applicationId = 1;
  //操作人
  Operator operator = 2;
}

message QueryJobReply{
  bool isSuccess = 1;
  string errMsg = 2;
  SaberJobDetail saberJobDetail = 3;
}
/**
saber任务详情
 */
message SaberJobDetail{
  //saber任务
  SaberJobInner saberJob = 1;
  //血缘信息
  JobGene jobGene = 2;
  //当前运行的实例
  SaberJobHistory saberJobHistory = 3;
}
/**
saber任务
 */
message SaberJobInner{
  //任务id
  string jobId = 1;
  //任务名称
  string jobName = 2;
  //用户名
  string userName = 3;
  //优先级
  Priority priority = 4;
  //提交队列
  string queue = 5;
  //是否开启ck
  bool  checkpoint = 6;
  //并发度
  int32 parallelism = 8;
  //tm内存
  int32 taskManagerMemory = 9;
  //flink版本
  string flinkVersion = 10;
  //任务参数
  string mainArgs = 11;
  //集群参数
  string sysParameters = 12;
  //任务详情
  oneof job{
    //bsql任务
    BsqlJobInner bsqlJob = 13;
    //flink jar任务
    FlinkJarJobInner flinkJarJob = 14;
  }
  //上下线状态
  Type type = 18;
  //当前运行实例
  int64 runJobHistoryId = 19;
  //开启AutoScaling
  bool AutoScaling = 20;
  //部门ID
  int64 departmentId = 21;
  //部门名称
  string departmentName = 22;
  //部署模式：老集群、新集群
  DeployMode DeployMode = 23;
}
/**
bsql任务类型
 */
message BsqlJobInner{
  //sql代码
  string bsqlCode = 1;
  //udf jar
  repeated string udfJarPath = 2;
  //sql 版本
  string bsqlVersion = 3;
}
/**
flink jar任务类型
 */
message FlinkJarJobInner{
  //执行累
  string runClass = 1;
  //flink jar包
  string flinkJarPath = 2;
}
/**
任务上下线状态
 */
enum Type{
  OFFLINE = 0;
  ONLINE = 1;
}
/**
作业血缘
 */
message JobGene{
  repeated GeneInfo geneInfo = 1;
}
/**
血缘
 */
message GeneInfo{
  //表类型：source sink等
  TableType tableType = 1;
  //表类型：hbase kafka等
  string typeKey = 2;
  //表名
  string tableName = 3;
  //配置信息：连接信息等
  repeated Property property = 4;
}
/**
表类型
 */
enum TableType{
  //未知
  UNKNOWN = 0;
  //源表
  SOURCE = 1;
  //目的表
  SINK = 2;
  // 维度表
  SIDE = 3;
}
/**
配置信息
 */
message Property{
  string key = 1;
  string value = 2;
}
/**
运行实例
 */
message SaberJobHistory{
  //实例id
  int64  id = 1;
  //任务id
  string jobId = 2;
  //yarn上任务id
  string applicationId = 3;
  //运行状态
  Status status = 4;
  //运行配置版本
  string version = 5;
  //启动时间
  string startTime = 6;
  //结束时间
  string endTime = 7;
  //操作人
  string operator = 8;
  //运行类型
  JobRunType jobRunType = 9;
}
/**
运行类型：生产or测试
 */
enum JobRunType {
  /**
   * 生产
   */
  PRO = 0;
  /**
   * 测试
   */
  TEST = 1;
}
//-------------------修改作业，并上线----------------------
message ExecutePlanRequest{
  ExecutePlanJob saberJob = 1;
  Operator operator = 2;
}

message ExecutePlanReply{
  bool isSuccess = 1;
  string errMsg = 2;
  repeated PhysicalExecutionPlan PhysicalExecutionPlans = 3;
}

message ExecutePlanReply2{
  bool isSuccess = 1;
  string errMsg = 2;
  string executionPlan = 3;
}


/**
执行计划
 */
message PhysicalExecutionPlan{
  string  operatorName = 1;
  int32  id = 2;
  string nodeType = 3;
  string content = 4;
  string shipStrategy = 5;
}

/**
修改saber任务
 */
message ExecutePlanJob{
  //任务id
  string jobId = 1;
  //任务名称
  string jobName = 2;
  //用户名
  string userName = 3;
  //优先级
  Priority priority = 4;
  //提交队列
  string queue = 5;
  //是否开启ck
  bool  checkpoint = 6;
  //并发度
  int32 parallelism = 8;
  //tm内存
  int32 taskManagerMemory = 9;
  //flink版本
  string flinkVersion = 10;
  //任务参数
  string mainArgs = 11;
  //集群参数
  string sysParameters = 12;
  //任务详情
  oneof job{
    //bsql任务
    BsqlJobInner bsqlJob = 13;
    //flink jar任务
    FlinkJarJobInner flinkJarJob = 14;
  }
  //开启AutoScaling
  bool AutoScaling = 20;
}

//--------------------------任务列表--------------------------
/**
查询任务列表请求
 */
message JobInfoListReq{
  //name模糊匹配
  string jobName = 1;
  //owner user name
  string owner = 2;
  int64 departmentId = 3;
  repeated string tag = 4;
  int32 pageSize = 5;
  int32 pageNo = 6;
}

/**
查询任务列表请求
 */

message Page {
  int32 pageSize = 1;
  int32 pageNum = 2;
  int64 total = 3;
}

message JobInfoListResp{
  bool isSuccess = 1;
  string errMsg = 2;
  Page page = 3;
  repeated SaberJobInner saberJobInner = 4;
}