syntax = "proto3";

package datacenter.service.titan.v1;

import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/datacenter/service.titanapi;api";
option java_package = "com.bapis.datacenter.service.titanapi";
option java_multiple_files = true;

option (wdcli.appid) = "datacenter.titan.titan";

service TitanService {
    // 【标签】标签信息查询
    rpc tagList(TagListReq) returns (TagListResp);

    // 【人群】人群创建by标签
    rpc crowdCreate(CrowdCreateReq) returns (CrowdCreateResp);

    // 【人群】人群预估请求提交"
    rpc crowdCount(CrowdCountReq) returns (CrowdCountResp);

    // 【人群】人群预估量级获取
    rpc crowdCountFetch(CrowdCountFetchReq) returns (CrowdCountFetchResp);

    // 【人群】人群创建byCSV
    rpc crowdCreateByCsv(CrowdCreateByCsvReq) returns (CrowdCreateByCsvResp);

    // 【人群】人群创建byhttp下载链接
    rpc crowdCreateByHttp(CrowdCreateByHttpReq) returns (CrowdCreateResp);


    // 【人群】人群状态和下载地址查询"
    rpc crowdResultQuery(CrowdResultQueryReq) returns (CrowdResultQueryResp);

    // 【人群】人群列表查询
    rpc crowdList(CrowdListReq) returns (CrowdListResp);

    // 【人群】人群配置详情查询
    rpc crowdConfigSearch(CrowdConfigSearchReq) returns (CrowdConfigSearchResp);

    // 【人群】人群例行记录列表查询
    rpc crowdRoutineList(CrowdRoutineListReq) returns (CrowdRoutineListResp);

    // 【画像】人群画像信息查询
    rpc queryPortrait(QueryPortraitReq) returns (QueryPortraitResp);

    // 【人群】黑白名单实现人群迁移
    rpc crowdTransplant(CrowdTransplantReq) returns (CrowdTransplantResp);

    //人群下线
    rpc crowdInvalid(CrowdInvalidReq) returns (CommonResp);
}

message CrowdInvalidReq {
    // 人群id
    int64 crowdId = 1;
    //操作人，需要是对人群有权限的用户
    string opUser = 2;
}

message CommonResp{
    int32 code = 1;
    string message = 2;
}

// tagMapTree() start
message TagListReq {

    // value = "业务域Id, 业务域Id和数据源Id至少需要指定一个", example = "1221"
    int32 businessId = 1;

    // value = "数据源Id，业务域Id和数据源Id至少需要指定一个", example = "1221"
    int64 viewId = 2;

    // value = "标签数据源列映射名，当tagColNames有传时viewId必须指定，可批量传入，多列逗号隔开，不传则返回视图所有标签，示例值(device_os,sex_type)", example = "device_os,sex_type"
    repeated string tagColNames = 3;

    // 操作人群，required = true
    string opUser = 4;
}

message TagListResp {
    int32 code = 1;
    repeated TagMapTree data = 2;
    string message = 3;
}

message TagMapTree {
    // value = "标签id", example = "123")
    int64 tagId = 1;

    // value = "标签名", example = "测试标签")
    string tagName = 2;

    // value = "标签类型，1-离散（枚举），2-连续（非枚举）", example = "1"
    int32 tagType = 3;

    // value = "数据源id",example = "3"
    int64 viewId = 4;

    // value = "标签节点类型 2-二级标签  3-三级标签（枚举标签）", example = "1")
    int32 tagNodeType = 5;

    // value = "标签数据列字段类型", example = "bigint"
    string tagColType = 6;

    // value = "标签数据列字段名", example = "sex"
    string tagColName = 7;

    // value = "子标签列表"
    repeated ChildTag childTagList = 8;
}
message ChildTag {
    // value = "标签id", example = "123"
    int64 tagId = 1;

    // value = "标签名", example = "测试标签2"
    string tagName = 2;

    // value = "子标签圈选值，圈选会用这个值来圈选", example = "test1"
    string tagColValue = 3;
}
// tagMapTree() end

// crowdCreate() start
message CrowdCreateReq{
    // value = "titan平台上配置的业务域id",required = true
    int32 businessId = 1;

    // value = "用户Id类型,1-mid,2-buvid", required = true, example = "1"
    int32 idType =2;

    // value = "圈选交并条件【仅支持三层】", required = true
    Condition condition =3;

    // value = "差集条件【仅支持三层】"
    Condition filterCondition =4;

    // value = "人群名称", required = true, min = 1, max = 64,message = "人群名称长度在1和64之间")//限制人群名称长度，和db一致
    string crowdName = 5;

    // value = "更新方式：1-例行更新0-不例行, 默认不例行", example = "1"
    int32 updateType = 6;

    // value = "例行人群结束例行的日期，如果更新方式为例行，例行结束时间需必填", example = "20221213"
    string routineEndTime =7;

    // value = "人群下线时间，默认30天", example = "20221211"
    string expiredTime =8;

    // value = "例行化失败是否开启告警，1-开启，0-不开启，默认不开启", example = "1"
    int32 failAlarm =9;

    // value = "告警人", example = "xxx,yyy"
    string alarmPerson = 10;

    // value = "人群明细是否需要导出(1-导出 0-不导出)", required = true, allowableValues = "0,1
    int32 isDownload =11;

    // 操作人群，required = true
    string opUser = 12;

    //授权人
    repeated string  members = 13;
}

message Condition{
    // value = "圈选子条件组合", required = true
    repeated Condition conditions =1;

    // value = "圈选条件列表", required = true
    repeated ConditionItem paramRules =2;

    // value = "条件操作符", required = true
    ConditionRelationEnum relation =3;
}

message ConditionItem{
    // value = "类型 1-标签；2-人群", required = true, example = "1"
    int32 type =1;

    // value = "标签id/人群id", required = true, example = "12345"
    int64 tagId =2;

    // value = "标签值【type=2 作为人群条件时不需要填】", required = false, example = "12345"
    repeated string params =3;

    // value = "标签运算条件【type=2时作为人群条件时不需要填】", required = false, example = "EQUAL"
    CrowdOperateEnum function =4;

    // value = "like 查询模式 0-默认分隔符内精确匹配；1-不指定分隔符，模糊匹配", required = false, example = "0"
    int32 likePattern =5;

    // value = "like 指定分隔符 精确匹配，默认英文逗号", required = false, example = ","
    string likeDelimiter =6;
}

message CrowdCreateResp{
    int32 code = 1;
    CrowdCreateRespData data = 2;
    string message = 3;
}

message CrowdCreateRespData{
    // value = "人群包id"
    int64 crowdId =1;

    // value = "实例id"
    int64 instanceId =2;
}
// crowdCreate() end

// crowdCount() start
message CrowdCountReq{
    // value = "用户Id类型,1-mid,2-buvid", required = true, example = "1"
    int32 idType = 1;
    // value = "圈选交并条件【仅支持三层】", required = true
    Condition condition = 2;
    // value = "差集条件【仅支持三层】"
    Condition filterCondition = 3;
    // 操作人群，required = true
    string opUser = 4;
}
message CrowdCountResp{
    int32 code = 1;
    CrowdCountRespData data = 2;
    string message = 3;
}
message CrowdCountRespData{
    // value = "人群预估任务ID"
    int64 taskId = 1;
}
// crowdCount() end

// crowdCountFetch() start
message CrowdCountFetchReq{
    // value = "任务Id", required = true, example = "1123"
    int64 taskId = 1;
    // 操作人群，required = true
    string opUser = 2;
}
message CrowdCountFetchResp{
    int32 code = 1;
    CrowdCountFetchRespData data = 2;
    string message  = 3;
}
message CrowdCountFetchRespData{
    // allowableValues = "1,2,3", value ="1-成功；2-失败；3-计算中",example = "1"
    int32 state = 1;
    // 量级
    int64 count = 2;
}

message CrowdCreateByHttpReq{
    // value = "人群包名字",required = true
    string crowdName = 1;
    // value = "用户id类型,1-mid,2-buvid",required = true
    int32 idType = 2;
    // value = "业务域id,后续将调整到header中获取",required = true
    int32 businessId = 3;
    // value = "人群明细是否需要导出(1-导出 0-不导出)", required = true, allowableValues = "0,1",example = "0"
    int32 isDownload = 4;
    // value = "人群下线时间格式为yyyyMMdd", example = "20221211"
    string expiredTime =5;

    // 操作人，required = true
    string opUser = 6;

    //下载链接 required = true
    string httpUrl = 7;
    //授权人
    repeated string  members = 8;
}

// crowdCreateByCsv() start
message CrowdCreateByCsvReq{
    // value = "人群包名字",required = true,position = 4
    string crowdName = 1;
    // value = "用户id类型,1-mid,2-buvid",required = true,position = 5
    int32 idType = 2;
    // value = "业务域id,后续将调整到header中获取",required = true,position = 6
    int32 businessId = 3;
    // value = "人群明细是否需要导出(1-导出 0-不导出)", required = true, allowableValues = "0,1",example = "0",position = 7
    int32 isDownload = 4;
    // value = "人群下线时间格式为yyyyMMdd", example = "20221211",position = 9
    string expiredTime =5;
    // 文件名和内容
    CsvFile file = 6;
    // 操作人群，required = true
    string opUser = 7;

    //授权人
    repeated string  members = 8;
}
message CsvFile{
    string fileName = 1;
    bytes content = 2;
}
message CrowdCreateByCsvResp{
    int32 code = 1;
    CrowdCreateByCsvRespData data = 2;
    string message = 3;
}
message CrowdCreateByCsvRespData{
    // crowdId
    int64 crowdId = 1;
    // instance keyId
    int64 instanceId = 2;
}
// crowdCreateByCsv() end

// crowdResultQuery() start
message CrowdResultQueryReq{
    // value = "查询结果状态 1-最新成功状态 0-最新状态", required = true, example = "0"
    int32 validState = 1;
    // value = "人群包id", required = true, example = "1123"
    int64 crowdId = 2;
    // 操作人群，required = true
    string opUser = 3;
}
message CrowdResultQueryResp{
    int32 code = 1;
    CrowdResultQueryRespData data = 2;
    string message = 3;
}
message CrowdResultQueryRespData{
    // allowableValues = "1,2,3,5,6,7,8",value ="1-成功；2-失败；3-计算中,5-等待中，6-已失效（人群已下线），7-停止更新（例行人群已停止更新）,8-当日更新中（例行人群由于数据降级等待例行）"
    int32 state = 1;
    // value = "是否降级 0-未降级；1-降级"
    int32 isDegrade = 2;
    // value = "人群分区时间"
    string crowdLogDate = 3;
    // value = "人群量级"
    int64 count = 4;
    // value = "人群下载地址"
    string path = 5;
    // value = "人群版本"
    int64 version = 6;
    //[仅对天级人群有效]表示人群例行计算的时间-人群分区crowdLogHour。 例行人群当天应该就绪的分区：crowdLogHour=today()-offset
    int32 offsetDay = 7;
    // value = "实例完成时间"
    string etime = 8;
    //人群来源 4、11- hive人群
    int32 createSource = 9;
    // 是否写入线上集群 0-否;1-是
    int32 isToOnline = 10;
}
// crowdResultQuery() end

// crowdConfigSearch() start
message CrowdConfigSearchReq {
    // 人群id，required = true
    int64 crowdId = 1;
    // 操作人群，required = true
    string opUser = 2;
}

message CrowdConfigSearchResp {
    int32 code = 1;
    ConfigSearchRespData data = 2;
    string message = 3;
}

message ConfigSearchRespData {
    // 人群名称
    string crowdName = 1;

    // 更新方式：1-例行更新0-不例行
    int32 updateType = 2;

    // "例行人群结束例行的日期", example = "20221213"
    string routineEndTime = 3;

    // "人群下线时间", example = "20221211"
    string expiredTime = 4 ;

    // "例行化失败是否开启告警，1-开启，0-不开启", example = "1"
    int32 failAlarm = 5;

    // "告警人", example = "xxx,yyy"
    string alarmPerson = 6;

    // "人群明细是否需要导出(1-导出 0-不导出)", allowableValues = "0,1"
    int32 isDownload = 7;

    // "是否写入Redis在线集群:1-写入，0-不写入", example = "1"
    int32 isToRedis = 8;

    // 人群归属业务域Id
    int32 businessId = 9;

    // 人群归属业务域名称
    string businessName = 10;

    // "用户id类型 1-mid 2-buvid"
    int32 idType = 11;

    // 人群配置参数: 非规则圈选人群使用
    CrowdParamConfig crowdParamConfig = 12;

    // 人群配置参数: 规则圈选交并条件【仅支持三层】
    ConditionConfigDTO condition = 13;

    // 人群配置参数: 规则圈选差集过滤条件【仅支持三层】
    ConditionConfigDTO filterCondition = 14;

    // 人群量级
    int64 crowdCount = 15;

    // value = "是否过期，0-未过期，1-过期"
    int32 isExpired = 16;

    //人群状态 1-成功；2-失败；3-计算中,5-等待中，6-已失效（人群已下线），7-停止更新（例行人群已停止更新）,8-当日更新中（例行人群由于数据降级等待例行
    int32 crowdState = 17;

    //审批状态 1-审批通过，2-审批驳回，3-审批中
    int32 oaFlowStatus = 18;

}

message CrowdParamConfig {
    string fileName = 1;
    int64 dmpId = 2;
    string httpUrl = 3;
}

message ConditionConfigDTO {
    // 圈选子条件组合
    repeated ConditionConfigDTO conditions = 1;
    ConditionRelationEnum relation = 2;
    repeated ConditionConfigItemDTO paramRules = 3;
}

enum ConditionRelationEnum {
    AND = 0;
    OR = 1;
}

message ConditionConfigItemDTO {

    // value = "标签id/人群id", required = true
    int64 tagId = 1;

    // value = "标签值", required = true
    repeated string params = 2;

    // value = "标签名称/人群名称", required = true
    string tagName = 3;

    // value = "类型 1-标签;2-人群", required = true
    int32 type = 4;

    // value = "标签类型  1-离散；2-连续", required = true
    int32 tagType = 5;

    // value = "字段类型", required = false, example = "string", allowableValues = "bigint,string"
    string colType = 6;

    // value = "标签操作类型枚举", required = false, example = "NORMAL", allowableValues = "NORMAL,DATE"
    OperateEnums operate = 7;

    // value = "操作符", required = false
    CrowdOperateEnum function = 8;

    // value = "子标签条件", required = false
    repeated TagCommonConfigDTO childTags = 9;
}

enum OperateEnums {
    NORMAL = 0;
    DATE = 1;
}

enum CrowdOperateEnum {
    UNKNOWN = 0;
    LESS = 1;
    MOST = 2;
    GREATER = 3;
    LEAST = 4;
    NOTEQUAL = 5;
    BETWEEN = 6;
    IN = 7;
    EQUAL = 8;
    ISNULL = 9;
    NOTNULL = 10;
    LIKE = 11;
    MULTI_LIKE = 12;
}

message TagCommonConfigDTO {
    // 子标签id
    int64 tagId = 1;
    // 子标签名字
    string tagName = 2;
}
// crowdConfigSearch() end

// crowdList() start
message CrowdListReq{
    // value = "查询关键词，支持搜索：人群名，业务域，来源，维护人"
    string keyword = 1;
    // value = "业务域Id：当有指定业务域时，会根据指定业务域过滤人群"
    int64 businessId = 2;
    // value = "人群责任人", example = "yanhanguang"
    string owner = 3;
    // value = "授权人列表, 示例：yanhanguang,yuanmin", example = "yanhanguang,yuanmin"
    repeated string members = 4;
    // value = "人群uid类型：1:mid,2:buvid，会根据指定类型过滤人群"
    int32 uidType = 5;
    // value = "排序字段,必须配合sortModel使用", example = "ctime", allowableValues = "ctime,mtime,crowdStatus,crowdCount"
    string sortField = 6;
    //value = "排序类型true即asc ,false即desc,必须配合sortField使用", example = "false"
    bool sortModel = 7;
    // value = "（平台）页码", required = true, example = "1"
    int32 pageNum = 8;
    // alue = "（平台）每页数量", required = true, example = "10"
    int32 pageSize = 9;
    // 操作人群，required = true
    string opUser = 10;

    repeated int64 crowdIds = 11;
}
message CrowdListResp{
    int32 code = 1;
    CrowdListRespData data = 2;
    string message = 3;
}
message CrowdListRespData{
    // 当前页
    int32 pageNum = 1;
    // 页面大小
    int32 pageSize = 2;
    // 总页数
    int32 totalPage = 3;
    // 总条目数量
    int64 total = 4;
    // 人群列表
    repeated CrowdListRespItem list = 5;
}
message CrowdListRespItem{
    // value = "【Long】人群包id", example = "10086"
    int64 crowdId = 1;
    // value = "【String】人群包名称", dataType = "String" ,example = "测试人群包"
    string crowdName = 2;
    // value = "【Long】人群clickhouse数据量级别", dataType = "Long" ,example = "111111"
    int64 crowdCkCount = 3;
    // value = "【Integer】更新方式 0-不更新 1-例行化",example = "0"
    int32 ckUpdateType = 4;
    // value = "【String】业务域名", example = "基础"
    string businessName = 5;
    // value = "【Long】业务域id", dataType = "java.lang.Long", example = "12"
    int64 businessId = 6;
    // value = "【String】责任人", example = "yanhanguang"
    string owner = 7;
    // value = "【String】责任人昵称", example = "暮里"
    string ownerNickName = 8;
    // value = "【Integer】ck最新状态 0-初始化 1-成功 2-失败 3-运行中", example = "1"
    int32 ckInstanceState = 9;
    // value = "【Integer】人群包创建来源", example = "1"
    int32 createSourceId = 10;
    // value = "【Integer】人群包类型 1-mid,2-buvid", example = "1"
    int32 uidType = 11;
    // value = "【String】人群包创建时间,13位时间戳", example = "1670897505000"
    string ctime = 12;
    // value = "【String】人人群包修改时间,13位时间戳", dataType = "java.lang.Long", example = "1670897505000"
    string mtime = 13;
    // value = "【String】人ck最新数据分区,yyyy-MM-dd", example = "2022-12-13"
    string ckLatestPartition = 14;
    // value = "【Integer】授权成员数量", example = "123123"
    int32 memberCount = 15;
    // value = "【List<String>】权限成员列表", example = "[\"yanhanguang\"]"
    repeated string members = 16;
    // value = "过期时间； 对标原来 validDay"
    string syncEndDate = 17;
    // value = "【Integer】是否在线人群包,0否,1是", example = "1"
    int32 isOnline = 18;
    // archer同步项目状态 0-关闭 1-开启
    int32 projectStatus = 19;
    // 原数据表
    string metadataTable = 20;
    // 流量比例
    int32 flowRatio = 21;
    // 替换人群
    repeated string replaceCrowdDesc = 22;
    // 命中次数
    string hitCount = 23;
    // 命中率
    string hitRate = 24;

    //人群创建来源
    string crowdCreateSource = 25;

    int32 isToRedis = 26;

    string expiredTime = 27;
}
// crowdList() end

// crowdRoutineList() start
message CrowdRoutineListReq{
    // value = "人群包Id", required = true
    int64 crowdId = 1;
    // value = "具体实例开始时间，ctime大于stime且小于etime的记录，stime和etime一起指定才生效",example = "2023-02-08 10:03:06"
    string stime = 2;
    // value = "具体执行结束时间，ctime大于stime且小于etime的记录，stime和etime一起指定才生效",example = "2023-02-08 11:03:06"
    string etime = 3;
    // value = "（平台）页码", required = true, example = "1"
    int32 pageNum = 4;
    // value = "（平台）每页数量", required = true, example = "10"
    int32 pageSize = 5;
    // 操作人群，required = true
    string opUser = 6;
}
message CrowdRoutineListResp{
    int32 code = 1;
    CrowdRoutineListRespData data = 2;
    string message = 3;
}
message CrowdRoutineListRespData{
    // 当前页
    int32 pageNum = 1;
    // 页面大小
    int32 pageSize = 2;
    // 总页数
    int32 totalPage = 3;
    // 总条目数量
    int64 total = 4;
    // 人群列表
    repeated CrowdRoutineListRespItem list = 5;
}
message CrowdRoutineListRespItem{
    // value = "例行开始时间"
    string stime = 1;
    // value = "例行完成时间"
    string etime = 2;
    // value = "人群可用分区"
    string dataPartition = 3;
    // value = "状态 1-成功 2-失败  3-运行中"
    int32 status = 4;
    // value = "人群包数量"
    int64 crowdCount = 5;
    // value = "archer实例url"
    string archerUrl = 6;
}
// crowdRoutineList() end

// queryPortrait() start
message QueryPortraitReq{
    // value = "人群包Id", required = true
    int64 crowdId = 1;
    // value = "标签Id列表", required = true
    repeated PortraitTagInner tagIds = 2;
    // 操作人群，required = true
    string opUser = 10;
}
message PortraitTagInner{
    // value = "标签Id", required = true
    int64 tagId = 1;
    // value = "标签关联的数据源Id", required = true
    int64 viewId = 2;
}
message QueryPortraitResp{
    int32 code = 1;
    QueryPortraitRespData data = 2;
    string message = 3;
}
message QueryPortraitRespData{
    // value = "人群画像查询状态：failed, completed, inprogress"
    string status = 1;
    // value = "人群画像查询详细信息"
    string detailedMsg = 2;
    // value = "父标签画像信息，例如可识别性别用户情况"
    map<string, PortraitInfoPerTag> parentTagPortraitInfo = 3;
    // value = "子标签画像信息分布，例如男、女、未知用户分布情况"
    map<string, PortraitInfoPerTag> childTagPortraitInfo = 4;
    // value = "子标签人数从多到少排序列表"
    repeated string sortList = 5;
    // value = "画像计算完成百分比, 两位小数点"
    double completionRates = 6;
    // value = "子标签人数从多到少排序列表"
    string portraitId = 7;
    // value = "子标签人数从多到少排序列表"
    int64 totalSize = 8;
}
message PortraitInfoPerTag{
    // 多个标签
    string tagIds = 2;
    // 标签对应人数量
    int64 count = 3;
    // 标签对应人数在人群包中占比
    string percent = 4;
    // 标签对应人数在人群包中占比数值类型
    double percentDigital = 5;
}
// queryPortrait() end

// crowdTransplant() start
message CrowdTransplantReq {
    // value = "业务域Id",required = true
    int32 businessId = 1;

    // value = "操作类型 0-删除（destCrowdId为空，相当于只添加黑名单；1-新增（sourceCrowdId为空，相当于只添加白名单）；2-迁移（sourceCrowdId和destCrowdId都不为空，同时添加黑白名单）",
    // required = true, allowableValues = "0,1,2"
    int32 operation = 2;

    // value = "人群迁移信息",required = true
    repeated  TransInfoItem transInfos = 3;

    // 操作人群，required = true
    string opUser = 4;

}

message TransInfoItem {
    // value = "uid", required = true
    string uid = 1;

    // value = "移除uid的人群【添加黑名单】"
    int64 sourceCrowdId = 2;

    // value = "添加uid的人群【添加白名单】"
    int64 destCrowdId = 3;
}

message CrowdTransplantResp {
    int32 code = 1;
    repeated TransInfoItem data = 2;
    string message = 3;
}
// crowdTransplant() end






