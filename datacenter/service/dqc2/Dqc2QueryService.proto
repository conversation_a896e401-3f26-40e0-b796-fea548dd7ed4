// Dqc服务查询接口代码
syntax = "proto3";

import "datacenter/service/dqc2/DqcCommon.proto";
option java_multiple_files = true;
option java_package = "com.bapis.datacenter.service.dqc2";
option go_package = "buf.bilibili.co/bapis/bapis-gen/datacenter/service.dqc2;api";

package datacenter.service.dqc2;

service Dqc2QueryService {
    rpc queryDqc(DqcQueryReq) returns (DqcQueryRep); //根据表名查询dqc
    rpc queryDqcRule(DqcRuleReq) returns (DqcRuleRep); //查询规则
    rpc queryDqcResult(DqcResultReq) returns (DqcResultRep);//查询结果
    rpc getDqcBlockConfiguration(DqcBlockConfigReq) returns (DqcBlockConfigResp);//批量查询表是否配置dqc阻塞
    rpc getDqcBlockDetail(DqcBlockReq) returns (DqcBlockResp);//批量查询表dqc阻塞状态详情
    rpc queryDqcByTableNames(BatchDqcQueryReq) returns (BatchDqcQueryRep); //根据表名查询dqc
    rpc queryDqcTaskInstanceList(DqcTaskInstanceReq) returns (DqcTaskInstanceResp); // 查询DQC运行实例
}


message DqcTaskInstanceReq{
    int64 dqcId = 1;
    int64 startTime = 2;
    int64 endTime = 3;
}

message DqcTaskInstanceResp{
    repeated DqcTaskInstanceVO dqcTaskInstanceVO = 1;
    int32 code = 2;
    string msg = 3;
}

message DqcTaskInstanceVO{
    int64 dqcId = 1;
    string tableName = 2;
    int32 taskStatus = 3;
    bool isAlarm = 4;
    int32 rulesCount = 5; 
    int32 expCount = 6; 
    int64 startTime = 7;
    int64 endTime = 8; 
    int64 eventTime = 9;
}


message DqcBlockResp{
    repeated TableWithDqcBlockRespVO tableWithDqcBlockRespVO = 1;
    int32 code = 2;
    string msg = 3;
}

message TableWithDqcBlockRespVO{
    string tableName = 1;
    string category = 2;
    int64  archerEndTime = 3;
    int32 blockStatusCode = 4;
    int64 blockDetailMsgCode = 5;
}

message DqcBlockReq{
    repeated TableWithDqcBlockReqVO tableWithDqcBlockReqVO = 1;
}

message TableWithDqcBlockReqVO{
    string tableName = 1;
    string category = 2;
    int64  archerEndTime = 3;
}

message DqcBlockConfigReq{
    repeated TableWithDqcBlockConfigReqVO tableWithDqcBlockConfigReqVO= 1;
}

message TableWithDqcBlockConfigReqVO{
    string tableName = 1;
    string category = 2;
}

message DqcBlockConfigResp{
    repeated TableWithDqcBlockConfigRespVO tableWithDqcBlockConfigRespVO = 1;
    int32 code = 2;
    string msg = 3;
}

message TableWithDqcBlockConfigRespVO{
    string tableName = 1;
    string category = 2;
    bool withBlock = 3;
    int32 maxBlockWaitTime = 4;
    int64 dqcId = 5;
    string workspace = 6;
}

message DqcQueryReq {
    string tableName = 1;
}

message DqcQueryRep{
    int64 dqcId = 1;
    int32 code= 2;
    string msg = 3;
}

message BatchDqcQueryReq {
    repeated string tableNameList = 1;
}

message BatchDqcQueryRep {
    repeated TableWithDqcListVO tableWithDqcListVO = 1;
    int32 code= 2;
    string msg = 3;
}

message TableWithDqcListVO{
    string tableName = 1;
    repeated DqcContent dqcContent = 2;
}

message DqcRuleReq {
    int64 dqcId = 1;
    string monitorItem = 2;
}

message DqcRuleRep{
   repeated DqcBizRule bizRuleList = 1;
   int32 code= 2;
   string msg = 3;
}

message DqcBizRule {
    int64 bizRuleId = 1;
    string bizRuleName = 2;
    string refValue = 3;
    string fatalRefValue = 4;
    string breakRefValue = 5;
    string checkLogic = 6;
    string templateName = 7;
    string monitorItem = 8;
    repeated RuleParam ruleParams = 9;
}

message DqcResultReq {
    repeated int64 ruleList = 1;
    int64 monitorBeginTime = 2;
    int64 monitorEndTime = 3;
    string userName = 4;
}

message DqcResultRep {
    repeated CheckResult checkResultList = 1;
    int32 code= 2;
    string msg = 3;
}

message CheckResult {
    int64 bizRuleId = 1;
    bool ratioRule = 2;
    string monitorItem = 3;
    string refValue = 4;
    string fatalRefValue = 5;
    string breakRefValue = 6;
    string ruleName = 7;
    repeated ResultDetail details = 8;
    string timeGrading = 9;
    string checkLogic = 10;
    bool switchFlag = 11;
    int32 byAutoRefValue = 12;
}

message ResultDetail {
    string time = 1;
    string value = 2;
    string checkStatus = 3;
    int32 alertLevel = 4;
    string yhatLower = 5;
    string yhatUpper = 6;
}

message RuleParam {
    string name = 1;
    string code = 2;
    string value = 3;
}