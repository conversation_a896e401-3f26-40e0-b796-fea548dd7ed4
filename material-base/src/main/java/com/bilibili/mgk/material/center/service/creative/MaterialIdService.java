package com.bilibili.mgk.material.center.service.creative;

import com.bilibili.mgk.material.center.service.creative.model.MaterialIdReference;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdRegistry;
import com.bilibili.mgk.material.center.service.creative.model.MaterialIdType;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialIdRegisterReq;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialIdRegisterResp;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceDeleteReq;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceDeleteResp;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceSearchReq;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceUpdateReq;
import com.bilibili.mgk.material.center.service.creative.vo.MaterialReferenceUpdateResp;
import com.bilibili.mgk.material.center.service.creative.vo.Pagination;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/13
 */
public interface MaterialIdService {


    /**
     * 用户注册新的素材，并创建新的素材引用
     *
     * @param request
     * @return
     */
    MaterialIdRegisterResp register(MaterialIdRegisterReq request);


    /**
     * 删除素材引用， 但是不删除素材，所以可能存在有素材而无引用的情况
     *
     * @param req
     * @return
     */
    MaterialReferenceDeleteResp deleteReference(MaterialReferenceDeleteReq req);

    /**
     * 更新素材引用，只能优先有限字段，如name,content
     *
     * @param req
     * @return
     */
    MaterialReferenceUpdateResp updateReference(MaterialReferenceUpdateReq req);


    /**
     * 使用素材类型和唯一键查找素材的id，通常用于回显素材id
     *
     * @param materialIdType 为枚举类型{@link MaterialIdType} ，但是支持字符透传，用于支持新的枚举类型
     * @param materialUks    通常为md5
     * @return
     */
    Map<String, MaterialIdRegistry> findByTypeAndUks(String materialIdType, List<String> materialUks);

    /**
     * 使用素材的查找素材的md5等，通常用于使用素材id搜索素材MD5等；
     *
     * @param materialIds
     * @return
     */
    Map<String, MaterialIdRegistry> findByMaterialIds(List<String> materialIds);


    /**
     * 分页搜索
     *
     * @param req 名称和账号
     * @return
     */
    Pagination<List<MaterialIdReference>> searchReference(MaterialReferenceSearchReq req);


}
