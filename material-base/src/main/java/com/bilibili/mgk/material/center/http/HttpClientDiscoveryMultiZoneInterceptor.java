package com.bilibili.mgk.material.center.http;

import com.google.common.collect.Lists;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Response;
import org.springframework.util.CollectionUtils;
import pleiades.component.env.Environment;
import pleiades.component.env.EnvironmentKeys;
import pleiades.venus.naming.client.resolve.NamingResolver;

/**
 * plan2
 *
 * <AUTHOR>
 * @desc
 * @date 2024/7/26
 */
@Slf4j
public class HttpClientDiscoveryMultiZoneInterceptor implements Interceptor {

    private final Map<String, AtomicLong> zone2addressIndex = new HashMap<>();
    private final AtomicLong zoneIndex = new AtomicLong();
    private volatile Map<String, List<InetSocketAddress>> zone2Addresses = new HashMap<>();

    // TODO 可支持权重
    private List<String> zones;


    // TODO 可支持的包括 random round_robin

    // TODO 还可以考虑下机房选择时的容灾，没可用实例不参与选择，或者直接所有实例进行round-robin
    private String loadBalancePolicy = "round-robin";


    public HttpClientDiscoveryMultiZoneInterceptor(
            List<String> zones,
            NamingResolver namingResolver) {

        if (CollectionUtils.isEmpty(zones)) {
            zones = Lists.newArrayList(Environment.of(EnvironmentKeys.ZONE).get());
        }

        this.zones = zones;

        zones.forEach((zone) -> {
            HttpClientNameResolver.registerRefreshListener(zone, namingResolver, addr -> {
                // overwrite
                this.zone2Addresses.put(zone, addr);
            });
            this.zone2Addresses.put(zone, HttpClientNameResolver.addresses(zone, namingResolver));

            this.zone2addressIndex.put(zone, new AtomicLong(0L));
        });


    }

    @Override
    public Response intercept(Chain chain) throws IOException {

        String zone = getZone();

        List<InetSocketAddress> addresses = zone2Addresses.get(zone);

        AtomicLong addressIndex = zone2addressIndex.computeIfAbsent(zone, k -> new AtomicLong());

        if (addresses == null || zone2Addresses.isEmpty()) {
            throw new IOException("Empty address");
        }
        HttpUrl originURL = chain.request().url();
        InetSocketAddress target = getAddress(addresses, addressIndex);
        if (target != null) {
            originURL = originURL.newBuilder()
                    .scheme("http")
                    .host(target.getHostString())
                    .port(target.getPort())
                    .build();
        }
        return chain.proceed(chain.request().newBuilder().url(originURL).build());
    }

    private InetSocketAddress getAddress(List<InetSocketAddress> addresses, AtomicLong addressIndex) {
        long sequenceNumber = addressIndex.getAndIncrement();
        return addresses.get((int) (sequenceNumber % addresses.size()));
    }


    private String getZone() {

        long zoneIndexSeq = zoneIndex.getAndIncrement();

        return this.zones.get((int) (zoneIndexSeq % zones.size()));
    }
}
