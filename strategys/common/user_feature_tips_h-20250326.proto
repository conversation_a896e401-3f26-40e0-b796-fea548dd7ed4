syntax = "proto3";

package  strategys.common;

// import "extension/wdcli/wdcli.proto";
// import "github.com/gogo/protobuf/gogoproto/gogo.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/strategys/common;common";
option java_package = "com.bapis.strategys.common";
option java_multiple_files = true;

message UserFeatureTipsH {
  int64 tips_id = 1;
  int64 unit_id = 2;
  int64 position_id = 3;
  int64 activity_id = 4;
  int64 tips_show_cnt_1h = 5;
  int64 tips_click_cnt_1h = 6;
  double tips_cnt_ctr_1h = 7;
  int64 tips_order_cnt_1h = 8;
  double tips_order_gmv_1h = 9;
  double tips_order_cnt_cvr_1h = 10;
  double tips_ecpm_1h = 11;
  double tips_click_cnt_rto_byunit_1h = 12;
  double tips_click_cnt_rto_byact_1h = 13;
  int64 tips_show_cnt_4h = 14;
  int64 tips_click_cnt_4h = 15;
  double tips_cnt_ctr_4h = 16;
  int64 tips_order_cnt_4h = 17;
  double tips_order_gmv_4h = 18;
  double tips_order_cnt_cvr_4h = 19;
  double tips_ecpm_4h = 20;
  double tips_click_cnt_rto_byunit_4h = 21;
  double tips_click_cnt_rto_byact_4h = 22;
  int64 tips_show_cnt_8h = 23;
  int64 tips_click_cnt_8h = 24;
  double tips_cnt_ctr_8h = 25;
  int64 tips_order_cnt_8h = 26;
  double tips_order_gmv_8h = 27;
  double tips_order_cnt_cvr_8h = 28;
  double tips_ecpm_8h = 29;
  double tips_click_cnt_rto_byunit_8h = 30;
  double tips_click_cnt_rto_byact_8h = 31;
  int64 tips_show_cnt_24h = 32;
  int64 tips_click_cnt_24h = 33;
  double tips_cnt_ctr_24h = 34;
  int64 tips_order_cnt_24h = 35;
  double tips_order_gmv_24h = 36;
  double tips_order_cnt_cvr_24h = 37;
  double tips_ecpm_24h = 38;
  double tips_click_cnt_rto_byunit_24h = 39;
  double tips_click_cnt_rto_byact_24h = 40;
  double tips_show_cnt_1h_hb = 41;
  double tips_click_cnt_1h_hb = 42;
  double tips_cnt_ctr_1h_hb = 43;
  double tips_order_cnt_1h_hb = 44;
  double tips_order_gmv_1h_hb = 45;
  double tips_order_cnt_cvr_1h_hb = 46;
  double tips_ecpm_1h_hb = 47;
  double tips_click_cnt_rto_byunit_1h_hb = 48;
  double tips_click_cnt_rto_byact_1h_hb = 49;
  double tips_show_cnt_4h_hb = 50;
  double tips_click_cnt_4h_hb = 51;
  double tips_cnt_ctr_4h_hb = 52;
  double tips_order_cnt_4h_hb = 53;
  double tips_order_gmv_4h_hb = 54;
  double tips_order_cnt_cvr_4h_hb = 55;
  double tips_ecpm_4h_hb = 56;
  double tips_click_cnt_rto_byunit_4h_hb = 57;
  double tips_click_cnt_rto_byact_4h_hb = 58;
  double tips_show_cnt_8h_hb = 59;
  double tips_click_cnt_8h_hb = 60;
  double tips_cnt_ctr_8h_hb = 61;
  double tips_order_cnt_8h_hb = 62;
  double tips_order_gmv_8h_hb = 63;
  double tips_order_cnt_cvr_8h_hb = 64;
  double tips_ecpm_8h_hb = 65;
  double tips_click_cnt_rto_byunit_8h_hb = 66;
  double tips_click_cnt_rto_byact_8h_hb = 67;
  double tips_show_cnt_24h_hb = 68;
  double tips_click_cnt_24h_hb = 69;
  double tips_cnt_ctr_24h_hb = 70;
  double tips_order_cnt_24h_hb = 71;
  double tips_order_gmv_24h_hb = 72;
  double tips_order_cnt_cvr_24h_hb = 73;
  double tips_ecpm_24h_hb = 74;
  double tips_click_cnt_rto_byunit_24h_hb = 75;
  double tips_click_cnt_rto_byact_24h_hb = 76;
  int64 unit_show_cnt_1h = 77;
  int64 unit_click_cnt_1h = 78;
  double unit_cnt_ctr_1h = 79;
  int64 unit_order_cnt_1h = 80;
  double unit_order_gmv_1h = 81;
  double unit_order_cnt_cvr_1h = 82;
  double unit_ecpm_1h = 83;
  double unit_click_cnt_rto_byact_1h = 84;
  int64 unit_show_cnt_4h = 85;
  int64 unit_click_cnt_4h = 86;
  double unit_cnt_ctr_4h = 87;
  int64 unit_order_cnt_4h = 88;
  double unit_order_gmv_4h = 89;
  double unit_order_cnt_cvr_4h = 90;
  double unit_ecpm_4h = 91;
  double unit_click_cnt_rto_byact_4h = 92;
  int64 unit_show_cnt_8h = 93;
  int64 unit_click_cnt_8h = 94;
  double unit_cnt_ctr_8h = 95;
  int64 unit_order_cnt_8h = 96;
  double unit_order_gmv_8h = 97;
  double unit_order_cnt_cvr_8h = 98;
  double unit_ecpm_8h = 99;
  double unit_click_cnt_rto_byact_8h = 100;
  int64 unit_show_cnt_24h = 101;
  int64 unit_click_cnt_24h = 102;
  double unit_cnt_ctr_24h = 103;
  int64 unit_order_cnt_24h = 104;
  double unit_order_gmv_24h = 105;
  double unit_order_cnt_cvr_24h = 106;
  double unit_ecpm_24h = 107;
  double unit_click_cnt_rto_byact_24h = 108;
  double unit_show_cnt_1h_hb = 109;
  double unit_click_cnt_1h_hb = 110;
  double unit_cnt_ctr_1h_hb = 111;
  double unit_order_cnt_1h_hb = 112;
  double unit_order_gmv_1h_hb = 113;
  double unit_order_cnt_cvr_1h_hb = 114;
  double unit_ecpm_1h_hb = 115;
  double unit_click_cnt_rto_byact_1h_hb = 116;
  double unit_show_cnt_4h_hb = 117;
  double unit_click_cnt_4h_hb = 118;
  double unit_cnt_ctr_4h_hb = 119;
  double unit_order_cnt_4h_hb = 120;
  double unit_order_gmv_4h_hb = 121;
  double unit_order_cnt_cvr_4h_hb = 122;
  double unit_ecpm_4h_hb = 123;
  double unit_click_cnt_rto_byact_4h_hb = 124;
  double unit_show_cnt_8h_hb = 125;
  double unit_click_cnt_8h_hb = 126;
  double unit_cnt_ctr_8h_hb = 127;
  double unit_order_cnt_8h_hb = 128;
  double unit_order_gmv_8h_hb = 129;
  double unit_order_cnt_cvr_8h_hb = 130;
  double unit_ecpm_8h_hb = 131;
  double unit_click_cnt_rto_byact_8h_hb = 132;
  double unit_show_cnt_24h_hb = 133;
  double unit_click_cnt_24h_hb = 134;
  double unit_cnt_ctr_24h_hb = 135;
  double unit_order_cnt_24h_hb = 136;
  double unit_order_gmv_24h_hb = 137;
  double unit_order_cnt_cvr_24h_hb = 138;
  double unit_ecpm_24h_hb = 139;
  double unit_click_cnt_rto_byact_24h_hb = 140;
  int64 activity_show_cnt_1h = 141;
  int64 activity_click_cnt_1h = 142;
  double activity_cnt_ctr_1h = 143;
  int64 activity_order_cnt_1h = 144;
  double activity_order_gmv_1h = 145;
  double activity_order_cnt_cvr_1h = 146;
  double activity_ecpm_1h = 147;
  int64 activity_show_cnt_4h = 148;
  int64 activity_click_cnt_4h = 149;
  double activity_cnt_ctr_4h = 150;
  int64 activity_order_cnt_4h = 151;
  double activity_order_gmv_4h = 152;
  double activity_order_cnt_cvr_4h = 153;
  double activity_ecpm_4h = 154;
  int64 activity_show_cnt_8h = 155;
  int64 activity_click_cnt_8h = 156;
  double activity_cnt_ctr_8h = 157;
  int64 activity_order_cnt_8h = 158;
  double activity_order_gmv_8h = 159;
  double activity_order_cnt_cvr_8h = 160;
  double activity_ecpm_8h = 161;
  int64 activity_show_cnt_24h = 162;
  int64 activity_click_cnt_24h = 163;
  double activity_cnt_ctr_24h = 164;
  int64 activity_order_cnt_24h = 165;
  double activity_order_gmv_24h = 166;
  double activity_order_cnt_cvr_24h = 167;
  double activity_ecpm_24h = 168;
  double activity_show_cnt_1h_hb = 169;
  double activity_click_cnt_1h_hb = 170;
  double activity_cnt_ctr_1h_hb = 171;
  double activity_order_cnt_1h_hb = 172;
  double activity_order_gmv_1h_hb = 173;
  double activity_order_cnt_cvr_1h_hb = 174;
  double activity_ecpm_1h_hb = 175;
  double activity_show_cnt_4h_hb = 176;
  double activity_click_cnt_4h_hb = 177;
  double activity_cnt_ctr_4h_hb = 178;
  double activity_order_cnt_4h_hb = 179;
  double activity_order_gmv_4h_hb = 180;
  double activity_order_cnt_cvr_4h_hb = 181;
  double activity_ecpm_4h_hb = 182;
  double activity_show_cnt_8h_hb = 183;
  double activity_click_cnt_8h_hb = 184;
  double activity_cnt_ctr_8h_hb = 185;
  double activity_order_cnt_8h_hb = 186;
  double activity_order_gmv_8h_hb = 187;
  double activity_order_cnt_cvr_8h_hb = 188;
  double activity_ecpm_8h_hb = 189;
  double activity_show_cnt_24h_hb = 190;
  double activity_click_cnt_24h_hb = 191;
  double activity_cnt_ctr_24h_hb = 192;
  double activity_order_cnt_24h_hb = 193;
  double activity_order_gmv_24h_hb = 194;
  double activity_order_cnt_cvr_24h_hb = 195;
  double activity_ecpm_24h_hb = 196;
}
