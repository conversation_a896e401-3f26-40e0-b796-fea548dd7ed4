// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package main.fission.lottery.service.v1;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";

option go_package = "buf.bilibili.co/bapis/bapis-gen/ott/lottery.service;api";
option java_package = "com.bapis.ott.lottery.service";
option java_multiple_files = true;
option (wdcli.appid) = "main.fission.fission-lottery";

service Lottery {
    // 查询活动信息
    rpc ActivityInfo(ActivityInfoReq) returns (ActivityInfoReply);
    //  批量查询活动信息
    rpc ActivityInfos(ActivityInfosReq) returns (ActivityInfosReply);
    // 查询权益奖励信息
    rpc PrizeInfo(PrizeInfoReq) returns (PrizeInfoReply);
    // 批量查询权益奖励信息
    rpc PrizeInfos(PrizeInfosReq) returns (PrizeInfosReply);
    // 活动详情
    rpc LotteryInfo(MidReq) returns (LotteryInfoReply);
    // 弹幕
    rpc LotteryScroll(LotteryScrollReq) returns (LotteryScrollReply);

    // 购买任务
    rpc CreateLotteryOrder(CreateOrderReq) returns (EmptyReply);

    // 抽奖
    rpc LotteryDoV2(LotteryDoV2Req) returns (LotteryDoV2Reply);
    // 开启宝箱
    rpc BoxDo(BoxDoReq) returns (BoxDoReply);
    // 无积分抽奖
    rpc LotteryDoInfinite(LotteryDoInfiniteReq) returns (LotteryDoInfiniteReply);

    // 奖品中心
    rpc PrizeCenter(PrizeCenterReq) returns (PrizeCenterReply);
    // 奖品列表
    rpc LotteryPrizeList(LotteryPrizeListReq) returns (LotteryPrizeListReply);
    // 奖励列表
    rpc LotteryAwardList(LotteryAwardListReq) returns (LotteryAwardListReply);
    // 领取兑换码
    rpc PrizeCodeExchange(PrizeCodeExchangeReq) returns (PrizeCodeExchangeReply);
    // 宝箱列表
    rpc BoxList(BoxListReq) returns (BoxListReply);

    // 任务详情
    rpc TaskInfo(TaskInfoReq) returns (TaskInfoReply);
    // 任务列表
    rpc LotteryTaskList(LotteryTaskListReq) returns (LotteryTaskListReply);
    // 用户任务完成-手动完成
    rpc LotteryUserTaskFinish(LotteryTaskFinishReq) returns (EmptyReply);
    // 通过任务类型完成任务同步接口-自动完成
    rpc TaskFinishByType(TaskFinishByTypeReq) returns (EmptyReply);
    // 通过任务ID完成任务同步接口-自动完成
    rpc TaskFinishById(TaskFinishByIdReq) returns (EmptyReply);
    // 通过任务ID完成任务异步接口-自动完成
    rpc SyncTaskFinishById(TaskFinishByIdReq) returns (EmptyReply);
    // 查询收银台购买用户任务加赠信息
    rpc CashierOrderAwards(CashierOrderAwardsReq)
        returns (CashierOrderAwardsReply);

    // 增加用户积分
    rpc IncrUserPoint(IncrUserPointReq) returns (EmptyReply);
    // 减少用户积分
    rpc ReduceUserPoint(ReduceUserPointReq) returns (EmptyReply);
    // 查询积分
    rpc UserPoint(UserPointReq) returns (UserPointReply);

    // 用户奖励发放
    rpc AddUserAward(AddUserAwardReq) returns (EmptyReply);

    // 大会员订单回调
    rpc VipOrderNotify(VipOrderNotifyReq) returns (VipOrderNotifyReply);

    // 活动套餐接口
    rpc ActivityPanel(ActivityPanelReq) returns (ActivityPanelReply);
    // 活动规则接口
    rpc ActivityRule(ActivityRuleReq) returns (ActivityRuleReply);

    // tv启动接口-用于送会员实验
    rpc TvLaunch(TvLaunchReq) returns (EmptyReply);

    // 签到查询
    rpc Sign(MidActivityReq) returns (SignReply);
    // 签到
    rpc SignDo(MidActivityReq) returns (SignDoReply);
    // 是否命中签到实验
    rpc HitSignExp(HitSignExpReq) returns (HitSignExpReply);

    // 问卷题目信息接口
    rpc QuestionInfo(QuestionInfoReq) returns (QuestionInfoReply);
    // 用户提交问卷接口
    rpc QuestionSubmit(QuestionSubmitReq) returns (EmptyReply);

    // 送会员实验屏蔽霸屏
    rpc SendVipFilterNotify(SendVipFilterNotifyReq)
        returns (SendVipFilterNotifyReply);
}

enum TaskStatus {
    // 进行中
    TASK_STATUS_UNDONE = 0;
    // 已完成
    TASK_STATUS_FINISHED = 1;
    // 已过期
    TASK_STATUS_EXPIRED = 2;
}

enum TaskType {
    TASK_TYPE_UNKNOWN = 0;
    // 浏览页面
    TASK_TYPE_VIEW_PAGE = 8;
    // 购买tv大会员
    TASK_TYPE_PAY_ORDER = 14;
    // 预约tv活动
    TASK_TYPE_RESERVE = 15;
    // tv观看直播时长任务
    TASK_TYPE_WATCH_LIVE = 16;
    // tv观看视频时长任务
    TASK_TYPE_WATCH_VIDEO = 17;
    // tv登录任务
    TASK_TYPE_TV_LOGIN = 18;
    // tv自动抽奖任务/盲盒任务：完成任务自动抽一次奖
    TASK_TYPE_AUTO_LOTTERY = 19;
    // 领取任务
    TASK_TYPE_RECEIVE = 20;
    // 渠道进端主启任务
    TASK_CHANNEL_TO_TV = 21;
    // 单片购买任务
    TASK_TVOD_ORDER = 26;
}

enum PrizeType {
    // 会员购商品
    PRIZE_TYPE_MALL = 0;
    // 大会员
    PRIZE_TYPE_VIP = 1;
    // 盲盒
    PRIZE_TYPE_LUCKY_BAG = 3;
    // 粉版大会员优惠券
    PRIZE_TYPE_VIP_COUPON = 5;
    // 会员购优惠券
    PRIZE_TYPE_MALL_COUPON = 10;
    // 会员购魔晶
    PRIZE_TYPE_MALL_CRYSTAL = 11;
    // 祝福语
    PRIZE_TYPE_BLESS = 12;
    // 积分
    PRIZE_TYPE_POINT = 20;
    // 实物
    PRIZE_TYPE_ACTUAL_GOODS = 24;
    // 基础套装装扮体验1个月
    PRIZE_TYPE_FULL_SKIN = 34;
    // 观影券
    PRIZE_TYPE_OGV_COUPON = 40;
    // tv会员优惠券
    PRIZE_TYPE_TV_VIP_COUPON = 41;
    // 虚拟奖品(无需自动发放，导出mid人工发放)
    PRIZE_TYPE_TV_VIRTUAL = 42;
    // 兑换码
    PRIZE_TYPE_COUPON_CODE = 43;
    // 限免片单
    PRIZE_TYPE_FREE_PLAY_LIST = 44;
    // 大会员兑换码
    PRIZE_TYPE_VIP_COUPON_CODE = 45;
    // 通用兑换码
    PRIZE_TYPE_SINGLE_CODEE = 46;
    // 免费看权益
    PRIZE_TYPE_FREE_WATCH = 47;
}

enum UserType {
    // 登录用户
    USER_TYPE_LOGIN = 0;
    // 游客
    USER_TYPE_GUEST = 1;
}

message AddUserAwardReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
    string activity_uid = 2;
    int64 award_id = 3
    [(gogoproto.moretags) = 'form:"award_id" validate:"required"'];
    bool need_pay = 4;
}

message TaskInfoReq {
    int64 task_id = 1;
    int64 mid = 2;
    int64 guest_id = 3;
}

message TaskInfoReply {
    int64 id = 1;
    string activity_uid = 2;
    string name = 3;
    // 任务类型
    TaskType task_type = 4;
    // 任务奖励
    repeated TaskAwardItem task_awards = 5;
    UserTaskItem user_task = 6;
    int64 begin_time = 7;
    int64 end_time = 8;
}

message UserTaskItem {
    int64 mid = 1;
    string activity_uid = 2;
    int64 task_id = 3;
    // 任务状态
    TaskStatus task_status = 4;
    // 累计完成次数
    int64 finish_sum = 5;
    // 单日完成次数
    int64 day_finish_sum = 6;
    // 最近一次完成时间
    int64 last_time = 7;
}

message TaskAwardItem {
    int64 prize_id = 1 [(gogoproto.jsontag) = 'prize_id'];
    // 奖品名称
    string name = 2 [(gogoproto.jsontag) = 'name'];
    // 奖品类型
    PrizeType prize_type = 3 [(gogoproto.jsontag) = 'prize_type'];
    // 库存
    int64 num = 4 [(gogoproto.jsontag) = 'num'];
    // 获得数量
    int64 receive_num = 5 [(gogoproto.jsontag) = 'receive_num'];
}

message CashierOrderAwardsReq {
    int64 mid = 1;
}

message CashierOrderAwardsReply {
    repeated WindowExtra extras = 1;
}

message ActivityInfoReq {
    string activity_uid = 1
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
}

message ActivityInfoReply {
    ActivityItem activity = 1;
}

message ActivityInfosReq {
    repeated string activity_uids = 1
    [(gogoproto.moretags) = 'form:"activity_uids" validate:"required"'];
}

message ActivityInfosReply {
    repeated ActivityItem activity_list = 1;
}

message ActivityItem {
    int64 id = 1;
    string name = 2;
    string activity_uid = 3;
    int64 begin_time = 4;
    int64 end_time = 5;
    int64 disabled = 6;
    int64 activity_type = 7;
    string biz = 8;
    string exchange_url = 9;
    string link = 10;
    ActivityExtra extra = 11;
}

message ActivityExtra {
    int64 platform_id = 1;
}

message UserPointReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
}

message UserPointReply {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid'];
    int64 point_balance = 2 [(gogoproto.jsontag) = 'point_balance'];
    int64 used_point = 3;
}

message ReduceUserPointReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    string scene = 3 [(gogoproto.moretags) = 'form:"scene" validate:"required"'];
    string point_desc = 4
    [(gogoproto.moretags) = 'form:"point_desc" validate:"required"'];
    string order_id = 5
    [(gogoproto.moretags) = 'form:"order_id" validate:"required"'];
    int64 prize_id = 6 [(gogoproto.moretags) = 'form:"prize_id"'];
    int64 block_status = 7 [(gogoproto.moretags) = 'form:"block_status"'];
    string biz_id = 8 [(gogoproto.moretags) = 'form:"biz_id"'];
    int64 point = 9 [(gogoproto.moretags) = 'form:"point"'];
}

message IncrUserPointReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    string scene = 3 [(gogoproto.moretags) = 'form:"scene" validate:"required"'];
    string biz_id = 4
    [(gogoproto.moretags) = 'form:"biz_id" validate:"required"'];
    string point_desc = 5
    [(gogoproto.moretags) = 'form:"point_desc" validate:"required"'];
    string order_id = 6
    [(gogoproto.moretags) = 'form:"order_id" validate:"required"'];
    int64 level = 7 [(gogoproto.moretags) = 'form:"level"'];
    int64 block_status = 8 [(gogoproto.moretags) = 'form:"block_status"'];
    int64 invited_mid = 9 [(gogoproto.moretags) = 'form:"invited_mid"'];
    int64 point = 10 [(gogoproto.moretags) = 'form:"point"'];
}

message LotteryDoInfiniteReply {
    // 奖品id
    int64 prize_id = 1 [(gogoproto.jsontag) = 'prize_id'];
    // 奖品名称
    string prize_name = 2 [(gogoproto.jsontag) = 'prize_name'];
    // 奖品类型
    int64 prize_type = 3 [(gogoproto.jsontag) = 'prize_type'];
    // 奖品图
    string pic_url = 4 [(gogoproto.jsontag) = 'pic_url'];
}

message LotteryDoInfiniteReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    DeviceInfo device_info = 3 [(gogoproto.moretags) = 'form:"device_info"'];
    int64 task_id = 4
    [(gogoproto.moretags) = 'form:"task_id" validate:"required"'];
    int64 guest_id = 5;
}

message TaskFinishByIdReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 2 [(gogoproto.moretags) = 'form:"activity_uid"'];
    DeviceInfo device_info = 3 [(gogoproto.moretags) = 'form:"device_info"'];
    int64 task_id = 4
    [(gogoproto.moretags) = 'form:"task_id"  validate:"required"'];
    int64 guest_id = 5;
    // 观看时长
    int64 duration = 6;
    // 扩展字段
    TaskFinishExtra extra = 7;
}

message TaskFinishByTypeReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    // 活动uid
    string activity_uid = 2 [(gogoproto.moretags) = 'form:"activity_uid"'];
    DeviceInfo device_info = 3 [(gogoproto.moretags) = 'form:"device_info"'];
    int64 task_type = 4
    [(gogoproto.moretags) = 'form:"task_type"  validate:"required"'];
    int64 guest_id = 5;
    // 唯一id，可用于幂等
    string uniq_id = 6;
    // 扩展字段
    TaskFinishExtra extra = 7;
}

message TaskFinishExtra {
    int64 season_id = 1;
}

message BoxDoReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    DeviceInfo device_info = 3 [(gogoproto.moretags) = 'form:"device_info"'];
    int64 stage = 4 [(gogoproto.moretags) = 'form:"stage"  validate:"required"'];
    int64 guest_id = 5;
}

message BoxDoReply {
    // 奖品id
    int64 prize_id = 1 [(gogoproto.jsontag) = 'prize_id'];
    // 奖品名称
    string prize_name = 2 [(gogoproto.jsontag) = 'prize_name'];
    // 奖品类型
    int64 prize_type = 3 [(gogoproto.jsontag) = 'prize_type'];
    // 奖品图
    string pic_url = 4 [(gogoproto.jsontag) = 'pic_url'];
    // 跳转地址
    string jump_url = 5 [(gogoproto.jsontag) = 'jump_url'];
}

message BoxListReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    int64 guest_id = 3;
}

message BoxListReply {
    repeated BoxItem list = 1 [(gogoproto.jsontag) = 'list'];
}

message BoxItem {
    int64 id = 1 [(gogoproto.jsontag) = 'id'];
    // 阶段
    int64 stage = 2 [(gogoproto.jsontag) = 'stage'];
    // 开启所需积分
    int64 point = 3 [(gogoproto.jsontag) = 'point'];
    // 默认宝箱图
    string pic_url = 4 [(gogoproto.jsontag) = 'pic_url'];
    // 宝箱状态 0-未开启 1-待开启 2-已开启
    int64 status = 5 [(gogoproto.jsontag) = 'status'];
}

message PrizeCenterReq {
    int64 mid = 1;
    int64 pn = 2;
    int64 ps = 3;
    DeviceInfo device_info = 4;
    int64 guest_id = 5;
}

message PrizeCenterReply {
    repeated AwardModel list = 1;
    PageModel page = 2;
}

message AwardModel {
    // 奖品id
    int64 prize_id = 1;
    // 奖品名称
    string prize_name = 2;
    // 奖品类型
    int64 prize_type = 3;
    // 奖品图
    string pic_url = 4;
    // 兑换码
    string code = 5;
    // 起始有效时间
    string stime = 6;
    // 截止有效时间
    string etime = 7;
    // 任务名称
    string task_name = 8;
    // 奖励状态
    int64 award_status = 9;
    // 用户奖励id
    int64 award_id = 10;
    // 活动id
    string activity_uid = 11;
    // 活动名称
    string activity_name = 12;
    // 是否可以兑换
    bool can_exchange = 13;
    // 兑换地址
    string exchange_url = 14;
    // 标题(以下字段给客户端用)
    string name = 15;
    // 使用说明
    string use_desc = 16;
    // 过期时间说明
    string expire_desc = 17;
    // 按钮文案
    string icon_text = 18;
    // 按钮状态 1-根据code展示兑换二维码 2-根据exchange_url展示问卷二维码
    // 3-根据jump_url跳转到schema 4-请求exchange接口 5-根据toast_msg直接toast文案
    int64 icon_status = 19;
    // 文案
    string toast_msg = 20;
    // 跳转地址
    string jump_url = 21;
    // 问卷标题
    string exchange_title = 22;
    // 问卷说明
    string exchange_desc = 23;
}

message PrizeCodeExchangeReq {
    int64 mid = 1;
    string activity_uid = 2 [(gogoproto.moretags) = 'validate:"required"'];
    int64 award_id = 3 [(gogoproto.moretags) = 'validate:"required"'];
    int64 guest_id = 4;
}

message PrizeCodeExchangeReply {
    string code = 1;
}

message LotteryTaskListReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    DeviceInfo device_info = 3 [(gogoproto.moretags) = 'form:"device_info"'];
    int64 guest_id = 4;
    bool family_mode = 5;
}

message LotteryTaskListReply {
    repeated LotteryTask list = 1 [(gogoproto.moretags) = 'form:"list"'];
}

message LotteryTaskFinishReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    int64 task_id = 3
    [(gogoproto.moretags) = 'form:"task_id" validate:"required"'];
    DeviceInfo device_info = 4 [(gogoproto.moretags) = 'form:"device_info"'];
    int64 guest_id = 5;
    // 子任务id
    int64 sub_task_id = 6;
}

message LotteryTask {
    int64 task_id = 1 [(gogoproto.jsontag) = 'task_id'];
    string task_name = 2 [(gogoproto.jsontag) = 'task_name'];
    int64 countdown = 3 [(gogoproto.jsontag) = 'countdown'];
    int64 task_status = 4 [(gogoproto.jsontag) = 'task_status'];
    int64 task_type = 5 [(gogoproto.jsontag) = 'task_type'];
    string jump_url = 6 [(gogoproto.jsontag) = 'jump_url'];
    string tag = 7 [(gogoproto.jsontag) = 'tag'];
    string condition = 8 [(gogoproto.jsontag) = 'condition'];
    repeated LotteryTaskAward task_award = 9 [(gogoproto.jsontag) = 'task_award'];
    int64 begin_time = 10;
    int64 end_time = 11;
    // 是否支持游客用户
    bool guest_allow = 12;
    // 是否满足会员条件
    bool vip_allow = 13;
    // 子任务列表
    repeated LotteryTask sub_task_list = 14;
    // 任务可完成次数限制
    int64 total_limit = 15;
    // 任务已完成总次数
    int64 finish_sum = 16;
    // 当天已完成次数
    int64 day_finish_sum = 17;
}

message LotteryTaskAward {
    int64 prize_id = 1 [(gogoproto.jsontag) = 'prize_id'];
    string name = 2 [(gogoproto.jsontag) = 'name'];
    int64 prize_type = 3 [(gogoproto.jsontag) = 'prize_type'];
    int64 num = 4 [(gogoproto.jsontag) = 'num'];
    // 奖品图
    string pic_url = 5 [(gogoproto.jsontag) = 'pic_url'];
    // 观看任务时长
    int64 duration = 6 [(gogoproto.jsontag) = 'duration'];
    // 发放状态
    int32 status = 7 [(gogoproto.jsontag) = 'status'];
}

message LotteryScrollReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
}

message LotteryScrollReply {
    repeated DanmuModel list = 1 [(gogoproto.jsontag) = 'list'];
}

message DanmuModel {
    // 奖品id
    int64 prize_id = 1 [(gogoproto.jsontag) = 'prize_id'];
    // 奖品名称
    string prize_name = 2 [(gogoproto.jsontag) = 'prize_name'];
    // 奖品类型
    int64 prize_type = 3 [(gogoproto.jsontag) = 'prize_type'];
    // 奖品图
    string pic_url = 4 [(gogoproto.jsontag) = 'pic_url'];
    // 昵称
    string nick_name = 5 [(gogoproto.jsontag) = 'nick_name'];
}

message LotteryAwardListReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"  validate:"required"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
}

message LotteryAwardListReply {
    repeated PrizeModel list = 1 [(gogoproto.jsontag) = 'list'];
    PageModel page = 2 [(gogoproto.jsontag) = 'page'];
}

message LotteryPrizeListReq {
    string activity_uid = 1
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
}

message LotteryPrizeListReply {
    repeated PrizeModel list = 1 [(gogoproto.jsontag) = 'list'];
    int64 needed_point = 2 [(gogoproto.jsontag) = 'needed_point'];
}

message PrizeModel {
    // 奖品id
    int64 prize_id = 1 [(gogoproto.jsontag) = 'prize_id'];
    // 奖品名称
    string prize_name = 2 [(gogoproto.jsontag) = 'prize_name'];
    // 奖品类型
    int64 prize_type = 3 [(gogoproto.jsontag) = 'prize_type'];
    // 奖品图
    string pic_url = 4 [(gogoproto.jsontag) = 'pic_url'];
    // 兑换码
    string code = 5 [(gogoproto.jsontag) = 'code'];
    // 奖池奖品图
    string preview_pic_url = 6 [(gogoproto.jsontag) = 'preview_pic_url'];
    // 开始有效期,时间戳
    int64 stime = 7;
    // 结束有效期,时间戳
    int64 etime = 8;
    // 库存
    int64 num = 9;
    // 有效时长,单位秒
    int64 valid_time = 10;
    // 静态or动态时间 0-静态 1-动态
    PrizeTimeTp time_type = 11;
    // 过期奖品图
    string expire_pic_url = 12 [(gogoproto.jsontag) = 'expire_pic_url'];
}

enum PrizeTimeTp {
    PRIZE_TIME_TP_STATIC = 0;
    PRIZE_TIME_TP_DYNAMIC = 1;
}

message LotteryDoV2Req {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 2
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    DeviceInfo device_info = 3 [(gogoproto.moretags) = 'form:"device_info"'];
    int64 guest_id = 4;
}

message LotteryDoV2Reply {
    // 奖品id
    int64 prize_id = 1 [(gogoproto.jsontag) = 'prize_id'];
    // 奖品名称
    string prize_name = 2 [(gogoproto.jsontag) = 'prize_name'];
    // 奖品类型
    int64 prize_type = 3 [(gogoproto.jsontag) = 'prize_type'];
    // 奖品图
    string pic_url = 4 [(gogoproto.jsontag) = 'pic_url'];
}

message CreateOrderReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
    string activity_uid = 2 [(gogoproto.moretags) = 'form:"activity_uid"'];
    string order_id = 3
    [(gogoproto.moretags) = 'form:"order_id" validate:"required"'];
    int64 task_type = 4 [(gogoproto.moretags) = 'form:"task_type"'];
    int64 activity_platform_id = 5
    [(gogoproto.moretags) = 'form:"activity_platform_id"'];
    int64 ctime = 6 [(gogoproto.moretags) = 'form:"ctime"'];
    UserType user_type = 7;
}

message LotteryLogsReq {
    string token = 1 [(gogoproto.moretags) = 'form:"token" validate:"required"'];
    DeviceInfo device_info = 2 [(gogoproto.moretags) = 'form:"device_info"'];
    string activity_uid = 3
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    int64 pn = 4 [(gogoproto.moretags) = 'form:"pn"'];
    int64 ps = 5 [(gogoproto.moretags) = 'form:"ps"'];
}

message LotteryLogsReply {
    repeated LotteryLog list = 1 [(gogoproto.jsontag) = 'list'];
    PageModel page = 2 [(gogoproto.jsontag) = 'page'];
}

message LotteryLog {
    int64 mid = 1 [(gogoproto.jsontag) = 'mid'];
    // 昵称
    string nick_name = 2 [(gogoproto.jsontag) = 'nick_name'];
    // 奖品id
    int64 prize_id = 3 [(gogoproto.jsontag) = 'prize_id'];
    // 奖品类型
    int64 prize_type = 4 [(gogoproto.jsontag) = 'prize_type'];
    // 奖品名称
    string prize_name = 5 [(gogoproto.jsontag) = 'prize_name'];
}

message LotteryDoReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"  validate:"required"'];
    string token = 2 [(gogoproto.moretags) = 'form:"token"'];
    string activity_uid = 3
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    DeviceInfo device_info = 4 [(gogoproto.moretags) = 'form:"device_info"'];
}

message LotteryDoReply {
    // 奖品id
    int64 prize_id = 1 [(gogoproto.jsontag) = 'prize_id'];
    // 奖品名称
    string prize_name = 2 [(gogoproto.jsontag) = 'prize_name'];
    // 奖品类型
    int64 prize_type = 3 [(gogoproto.jsontag) = 'prize_type'];
    // 奖品图
    string pic_url = 4 [(gogoproto.jsontag) = 'pic_url'];
}

message LotteryListReply {
    repeated LotteryInfo list = 1 [(gogoproto.jsontag) = 'list'];
    PageModel page = 2 [(gogoproto.jsontag) = 'page'];
}

message LotteryInfo {
    // 红包id
    string lottery_id = 1 [(gogoproto.jsontag) = 'lottery_id'];
    // 静态图
    string pic_url = 2 [(gogoproto.jsontag) = 'pic_url'];
    // 红包总个数
    int64 total_num = 3 [(gogoproto.jsontag) = 'total_num'];
    // 红包剩余个数
    int64 num = 4 [(gogoproto.jsontag) = 'num'];
}

message LotteryWindowReply {
    // 红包id
    string lottery_id = 1 [(gogoproto.jsontag) = 'lottery_id'];
    // 静态图
    string pic_url = 2 [(gogoproto.jsontag) = 'pic_url'];
    // 红包总个数
    int64 total_num = 3 [(gogoproto.jsontag) = 'total_num'];
    // 红包剩余个数
    int64 num = 4 [(gogoproto.jsontag) = 'num'];
}

message LotteryInfoReply {
    // 活动状态
    int64 activity_status = 1 [(gogoproto.jsontag) = 'activity_status'];
    // 用户剩余积分
    int64 cnt = 2 [(gogoproto.jsontag) = 'cnt'];
    // 用户总积分
    int64 total_cnt = 3;
    // 活动倒计时
    int64 countdown = 4;
    // 奖池剩余库存
    int64 pool_num = 5;
    // 弹窗
    InfoWindow window = 6;
    int64 begin_time = 7;
    int64 end_time = 8;
    repeated int64 paid_product_ids = 9;
}

message InfoWindow {
    // 类型
    string window_type = 1;
    // 标题
    string window_title = 2;
    // 副标题
    string window_subtitle = 3;
    // 参数
    repeated WindowExtra window_extras = 4;
    // 按钮
    repeated WindowIcon icon = 5;
}

message WindowExtra {
    string prize_name = 1;
    string pic_url = 2;
}

message WindowIcon {
    string title = 1;
    string url = 2;
    int64 window_event = 3;
}

message PopItem {
    string text = 1;
    string pic_url = 2;
}

message EmptyReply {}

message PageModel {
    int32 num = 1;
    int32 size = 2;
    int32 count = 3;
}

message MidReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    DeviceInfo device_info = 2 [(gogoproto.moretags) = 'form:"device_info"'];
    string activity_uid = 3
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    int64 guest_id = 4;
}

message MidPageReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    DeviceInfo device_info = 2 [(gogoproto.moretags) = 'form:"device_info"'];
    string activity_uid = 3
    [(gogoproto.moretags) = 'form:"activity_uid" validate:"required"'];
    int64 pn = 4 [(gogoproto.moretags) = 'form:"pn"'];
    int64 ps = 5 [(gogoproto.moretags) = 'form:"ps"'];
}

message DeviceInfo {
    string buvid = 1;
    string mobi_app = 2;
    string device = 3;
    string platform = 4;
    int64 build = 5;
    string ip = 6;
    string ua = 7;
    string referer = 8;
    string origin = 9;
    string cdn_ip = 10;
    string channel = 11;
    string brand = 12;  // 手机品牌
    string model = 13;  // 手机型号
    string osver = 14;  // 系统版本
    string statistics = 15;
}

message PrizeInfoReq {
    int64 prize_id = 1
    [(gogoproto.moretags) = 'form:"prize_id" validate:"required"'];
}

message PrizeInfoReply {
    PrizeModel item = 1;
}

message PrizeInfosReq {
    repeated int64 prize_ids = 1
    [(gogoproto.moretags) = 'form:"prize_ids" validate:"required"'];
}

message PrizeInfosReply {
    repeated PrizeModel items = 1;
}

message VipOrderNotifyReq {
    int64 mid = 1;
    // 用户类型 0-正常用户 1-tv游客
    int64 user_type = 2;
    // 订单号
    string order_no = 3;
    // 2-权益套餐
    int64 sku_type = 4;
    // 订单状态，1.消费中 2.消费成功 3.消费失败'
    int64 order_status = 5;
    // 订单数据，key="rights_id"，value="12344"
    map<string, string> content = 6;
    // 备注
    string remark = 7;
    // 创建时间
    int64 ctime = 8 [
        (gogoproto.jsontag) = "ctime",
        (gogoproto.casttype) = "go-common/library/time.Time"
    ];
}

message VipOrderNotifyReply {}

message ActivityPanelReq {
    int64 mid = 1;
    int64 guest_id = 2;
    string activity_uid = 3;
    DeviceInfo device = 4;
    string panel_type = 5;
}

message ActivityPanelReply {
    repeated ActivityPanelItem list = 1;
}

message ActivityPanelItem {
    // 套餐
    int64 product_id = 1;
    // 父套餐 deprecated
    int64 parent_product_id = 2;
    // 面板类型
    string panel_type = 3;
    // 面板渠道 登录-11 游客-20
    int32 panel_platform = 4;
    // 套餐背景图
    string pic_url = 5;
    // 父套餐背景图 deprecated
    string parent_pic_url = 6;
    int64 id = 7;
    // 套餐加赠弹窗图
    string send_img = 8;
    // 父套餐加赠弹窗图 deprecated
    string parent_send_img = 9;
    // 套餐背景图-落焦态
    string focus_pic_url = 10;
    // 二维码角标
    string qr_corner_mark = 11;
    VipPanel vip_sku = 12;
}

message VipPanel {
    int32 month = 1 [(gogoproto.jsontag) = "month", json_name = "month"];
    string product_name = 2
    [(gogoproto.jsontag) = "product_name", json_name = "product_name"];
    string product_id = 3
    [(gogoproto.jsontag) = "product_id", json_name = "product_id"];
    double original_price = 4
    [(gogoproto.jsontag) = "original_price", json_name = "original_price"];
    string discount_rate = 5
    [(gogoproto.jsontag) = "discount_rate", json_name = "discount_rate"];
    int32 selected = 6 [(gogoproto.jsontag) = "selected", json_name = "selected"];
    int32 id = 7 [(gogoproto.jsontag) = "id", json_name = "id"];
    string remark = 8 [(gogoproto.jsontag) = "remark", json_name = "remark"];
    int32 sub_type = 9 [(gogoproto.jsontag) = "sub_type", json_name = "sub_type"];
    // 适配人群
    int32 suit_type = 10
    [(gogoproto.jsontag) = "suit_type", json_name = "suit_type"];
    int32 max_num = 11 [(gogoproto.jsontag) = "max_num", json_name = "max_num"];
    double discount_price = 12
    [(gogoproto.jsontag) = "discount_price", json_name = "discount_price"];
    string channel_type = 13
    [(gogoproto.jsontag) = "channel_type", json_name = "channel_type"];
}

message ActivityRuleReq {
    int64 id = 1;
}

message ActivityRuleReply {
    string rule_content = 1;
}

message TvLaunchReq {
    int64 mid = 1;
    int64 guest_id = 2;
    DeviceInfo device = 3;
    // 首次启动时间
    int64 first_launch_ts = 5;
    bool is_exp = 6;
    // 来源，如feed region
    string from = 7;
}

message MidActivityReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    int64 guest_id = 2 [(gogoproto.moretags) = 'form:"mid"'];
    string activity_uid = 3
    [(gogoproto.moretags) = 'form:"activity_id" validate:"required"'];
    DeviceInfo device_info = 4 [(gogoproto.moretags) = 'form:"device_info"'];
}

message SignReply {
    repeated Sign list = 1 [(gogoproto.jsontag) = 'list'];
    bool is_today_signed = 2 [(gogoproto.jsontag) = 'is_today_signed'];
}

message Sign {
    string sign_desc = 1 [(gogoproto.jsontag) = 'sign_desc'];
    int64 status = 2 [(gogoproto.jsontag) = 'status'];
    string title = 3 [(gogoproto.jsontag) = 'title'];
    string sub_title = 4 [(gogoproto.jsontag) = 'sub_title'];
    repeated PrizeModel award_list = 5 [(gogoproto.jsontag) = 'award_list'];
    int64 sign_cfg_id = 6 [(gogoproto.jsontag) = 'sign_cfg_id'];
}

message SignDoReply {
    repeated PrizeModel award_list = 1 [(gogoproto.jsontag) = 'award_list'];
}

message QuestionInfoReq {
    string activity_uid = 1;
    int64 mid = 2;
    int64 guest_id = 3;
    DeviceInfo device = 4;
}

message QuestionInfoReply {
    // 问卷标题
    string title = 1;
    // 用户状态 0-未参与 1-已参与
    int32 user_status = 2;
    // 题目列表
    repeated Question question_list = 3;
}

message Question {
    // 题目id
    int64 question_id = 1;
    // 题目标题
    string question_title = 2;
    // 题目类型1-单选 2-多选 3-文本框
    int32 question_type = 3;
    // 答案列表
    repeated Answer answer_list = 4;
}

message Answer {
    // 答案id
    int64 answer_id = 1;
    // 答案标题
    string answer_title = 2;
    // 选择答案后跳转到题目
    int64 next_question_id = 3;
    // 是否打乱顺序
    int32 shuffle = 4;
    // 是否默认落焦
    int32 is_default = 5;
}

message QuestionSubmitReq {
    string activity_uid = 1;
    int64 mid = 2;
    int64 guest_id = 3;
    // 用户提交的答案
    string user_answer = 4;
    DeviceInfo device = 5;
}

message HitSignExpReq {
    int64 mid = 1;
    int64 guest_id = 2;
    DeviceInfo device = 3;
}

message HitSignExpReply {
    bool hit = 1;
}

message SendVipFilterNotifyReq {
    int64 mid = 1 [(gogoproto.moretags) = 'form:"mid"'];
    int64 guest_id = 2 [(gogoproto.moretags) = 'form:"mid"'];
    DeviceInfo device_info = 3 [(gogoproto.moretags) = 'form:"device_info"'];
}

message SendVipFilterNotifyReply {
    bool hit = 1;
}