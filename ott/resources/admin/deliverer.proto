syntax = "proto3";

package resources.admin;
option go_package = "buf.bilibili.co/bapis/bapis-gen/ott/resources.admin;api";
option java_package = "com.bapis.ott.resources.admin";
option java_multiple_files = true;

import "ott/resources/admin/page.proto";

message Deliverer {
  int64 id = 1;
  string deliverer_name = 2;
  string path = 3;
  string description = 4;
  int32 state = 5;
  int32 is_deleted = 6;
  string ctime = 7;
  string mtime = 8;
}

message ListDeliverersReq {
  int64 id = 1;
  string deliverer_name = 2;
  Page page = 3;
}

message ListDeliverersReply {
  repeated Deliverer list = 1;
  Page page = 2;
}

message DelivererBindResourcesReq {
  int64 deliverer_id = 1;
  repeated int64 resource_ids = 2;
}

message ListResourcesByDelivererReq {
  int64 deliverer_id = 1;
  Page page = 2;
}

message DelivererBindResourceReq {
  int64 deliverer_id = 1;
  int64 resource_id = 2;
}