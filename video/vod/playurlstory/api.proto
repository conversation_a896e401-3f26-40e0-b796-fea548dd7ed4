syntax="proto3";

package video.vod.playurlstory;

import "extension/wdcli/wdcli.proto";

option (wdcli.appid) = "video.vod.playurlstory";
option go_package = "buf.bilibili.co/bapis/bapis-gen/video/vod.playurlstory;v1";
option java_package = "com.bapis.video.vod.playurlstory";
option java_multiple_files = true;

// Request networks type & traffic free types .
enum NetworkType {
    // 未知
    NT_UNKNOWN      = 0;
    // WIFI
    WIFI            = 1;
    // 移动网络
    CELLULAR        = 2;
    // 未连接
    OFFLINE         = 3;
    // 其他网络
    OTHERNET        = 4;
    // 以太网
    ETHERNET        = 5;
}

// Request TFType type & traffic free types .
enum TFType {
    // 正常计费
    TF_UNKNOWN      = 0;
    // 联通卡
    U_CARD          = 1;
    // 联通包
    U_PKG           = 2;
    // 移动卡
    C_CARD          = 3;
    // 移动包
    C_PKG           = 4;
    // 电信卡
    T_CARD          = 5;
    // 电信包
    T_PKG           = 6;
}

// RequestMsg .
message RequestMsg {
    //视频标识id号
    repeated uint64 cids       = 1;
    //用户ipstr
    string          uip        = 2;
    //请求端平台
    string          platform   = 3;
    //用户id号
    uint64          mid        = 4;
    //返回url是否强制使用域名(非ip地址), force_host=1使用http域名，force_host=2使用https域名, 0为使用ip地址
    uint32          force_host = 5 [json_name = "force_host"];
    // 网络类型
    NetworkType     net_type   = 6 [json_name = "net_type"];
    // 免流类型
    TFType          tf_type    = 7 [json_name = "tf_type"];
    //返回备用url的个数（最多可能的个数）
    uint32          backup_num = 8 [json_name = "backup_num"];
}

// VideoFileInfo .
message VideoFileInfo {
    //视频分片的大小, 单位Byte
    uint64 filesize   = 1;
    //视频分片的时长, 单位ms
    uint64 timelength = 2;
    //视频md5信息
    string md5        = 3;
}

// ResponseItem .
message ResponseItem {
    //视频id
    uint64          cid         = 1;
    //返回视频播放的url
    string          url         = 2;
    //视频分片的备用url地址列表
    repeated string backup_url  = 3 [json_name = "backup_url"];
    //视频文件信息
    VideoFileInfo   file_info   = 4;
    //播放地址过期时间戳
    uint64          expire_time = 5 [json_name = "expire_time"];
}

// ResponseMsg .
message ResponseMsg {
    //错误状态码
    uint32                    code    = 1;
    //错误描述
    string                    message = 2;
    //视频映射表  
    map<uint64, ResponseItem> data    = 3;

}

// PlayurlService api .
service PlayurlService {
    rpc Playurl(RequestMsg) returns (ResponseMsg);
};