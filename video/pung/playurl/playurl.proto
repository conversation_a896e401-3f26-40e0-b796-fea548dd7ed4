// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/protobuf/empty.proto";
// import "google/api/annotations.proto";
import "extension/wdcli/wdcli.proto";
import "infra/mg/playurl-v3/api.proto";
import "video/live/playurl-onetier/api.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package video.pung.playurl.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/video/pung.playurl;v1";
option java_package = "com.bapis.video.pung.playurl";
option java_multiple_files = true;
option (wdcli.appid) = "video.pung.playurl";
// NOTE: 最后请删除这些无用的注释 (゜-゜)つロ

option (gogoproto.goproto_getters_all) = true;

message Ipv6 {
  uint64 start = 1;
  uint64 end = 2;
}


enum Protocol {
  HTTP_STREAM = 0;
  HTTP_HLS = 1;
}

enum Format {
  FLV = 0;
  TS = 1;
  FMP4 = 2;
  // 原生 ts，各 CDN 通过 RTMP 转封装而来；未来会废弃
  TS_original = 3;
}

enum Codec {
  AVC = 0;
  HEVC = 1;
  AV1 = 2;
}

enum DolbyType {
  NONE = 0;
  COMMON = 1;
  ATMOS = 2;
}

enum HdrType {
  SDR = 0;
  PQ = 1;
}

message QualityDescription {
  int32      qn = 1;
  string     desc = 2;

  string     hdr_desc = 3; // 已废弃
  repeated   AttrDesc attr_desc = 4; // 已废弃
  // 媒体信息，qn + 媒体信息是唯一 key
  HdrType   hdr_type = 5; //0: none, 1: PQ
  MediaBaseDesc media_base_desc = 6;
}

message MediaBaseDesc {
  DetailDesc detail_desc = 1;
  BriefDesc  brief_desc = 2;
}

message DetailDesc {
  string desc = 1;
  repeated string tag = 2;
}

message BriefDesc {
  string desc = 1;
  string badge = 2;
}

message AttrDesc {
  string    attr_name = 1;
  string    desc = 2;
}

message StreamProtocol {
  string                protocol_name = 1;
  repeated StreamFormat format = 2;
}

message StreamFormat {
  string                 format_name = 1;
  repeated StreamCodec   codec = 2;
  string                 master_url = 3;
}

message StreamCodec {
  string           codec_name = 1;
  int32            current_qn = 2;
  repeated int32   accept_qn = 3;
  string           base_url = 4;
  repeated UrlInfo url_info = 5;
  repeated int32   hdr_qn = 6;
  uint64           dolby_type = 7;
  string           attr_name = 8;
  // 媒体信息
  HdrType  hdr_type = 12; //0: SDR, 1: PQ

  // for internal
  infra.mg.playurl_v3.v1.StreamReq streamResourceReq = 9;
  StreamAvCover av_cover = 10;
  FreezeStreamInfo freeze_stream_info = 11;
}

message FreezeStreamInfo {
  bool Freeze_stream = 1;
  SourceBack source_back_info = 2;
}

message SourceBack {
  // 边缘ip
  string  ec_ip = 1;
  // http端口
  int64   http_port = 2;
  // https端口
  int64   https_port = 3;
}

message UrlInfo {
  string host = 1;
  string extra = 2;
}

message BatchUrlInfo {
  Protocol        protocol = 1;
  Format          format = 2;
  Codec           codec = 3;
  int32           current_qn = 4;
  repeated int32  accept_qn = 5;
  string          url = 6;
  uint64          dolby_type = 7;
  HdrType   hdr_type = 8;
  repeated string backup_urls = 9;
}

message P2PData {
  bool         p2p = 1;
  int32        p2p_type = 2;
  bool         m_p2p = 3;
  repeated string m_servers = 4;
}

message OldOnetierUrlInfo {
  string url = 1;
  uint32 length = 2;
  uint32 order = 3;
  // 表示stream类型,按位表示
  // Value|  1   |  1  |  1   |  1   |     1
  // --------------------------------------------
  // desc | mask | p2p | dash | hevc | only-audio
  uint32 stream_type = 4;
  // 表示支持p2p的cdn厂商,按位表示
  // 值   | 1  |  1  |  1  | 1  |  1  | 1  | 1  | 1
  // -----------------------------------------------
  // CDN	| hw | bdy | bsy | ws | txy | qn | js | bvc

  uint32 ptag = 5;
}

message PFC {
  repeated Protocol   protocol = 1;
  repeated Format     format = 2;
  repeated Codec      codec = 3;
  repeated HdrType    hdr_type = 4;
}

message QnPolicy {
  uint32 qn = 1;
  string policy = 2;
}

message RoomLevel {
  int64 room_level = 1;
  repeated StreamLevel stream_level = 2;
}

message StreamLevel {
  string stream_name = 1;
  map<string, int64> bvc_hot_stream_level_map = 2;
}

message StreamAvCover {
  int64 bandwidth = 1;
  string audio_codec = 2;
  string video_codec = 3;
  int64 height = 4;
  int64 width = 5;
}

message PickOption {
  repeated Protocol   protocol = 1;
  repeated Format     format = 2;
  repeated Codec      codec = 3;
  map<uint32, PFC>     req_pfc = 4;
  uint32              qn = 5;
  uint32              stream_type = 6;
  uint32              net_status = 7;
  string              platform = 8;
  int64               dolby_val = 9;
  string              stream_name = 10;
  map<uint32, uint32> quality_map = 11;
  repeated HdrType    hdr_type = 12;
  int64               build = 13;
}

message UserOption {
  QnPolicy qn_policy = 1;
}

message AbOption {
  string ab_stream_group = 1;
  int32  ab_p2p_type = 2;
}

message SignOption {
  uint32 free_type = 1;
  bool   only_audio = 2;
  bool   only_video = 3;
  bool   https_url_req = 4;
  string uipstr = 5;
  map<uint32, int32>   p2p_type = 6;
  map<uint32, uint32> internal_map = 7;
  map<uint32, RoomLevel> level_map = 8;
  map<uint32, uint32> delay_map = 9;
  bool isProj = 10;
  map<uint32, payload> payload_map = 11;
}

// ott
message OttPlayurlReq {
  uint32              cid = 1;
  uint32              quality = 2;  // 0, [4, 3, 2, ]10000, 400, 250, 150, 80
  uint32              stream_type = 3; // 0=>all_random, 1=>main_stream, 2=>backup_stream
  uint32              stream_len = 4;  // 0=>持续拉流，-1=>不可拉流, >0 => 可拉流的时长，单位秒
  string              uipstr = 5;
  uint32              free_type = 6; // 0 noe, 1 cu, 2 ct, 3 cmcc
  bool                only_audio = 7;
  bool                only_video = 8;
  bool                https_url_req = 9;
  uint32              mask = 10; // 0 none, 1 mask
  int64               uid = 11;
  string              platform = 12;
  uint32              build = 13;
  uint32              p2p = 14;
  uint32              net_status = 15;
  string              device_name = 16;
  string              req_biz = 17;
  repeated Protocol   protocol = 18;
  repeated Format     format = 19;
  repeated Codec      codec = 20;
  DolbyType           dolby = 21;
  string              uuid = 22;
  uint32              uip = 23;
  Ipv6                uipv6 = 24;
  uint32              group = 25; // 用户阵营，0代表A阵营，1代表B阵营
  int64               area_parent_id = 26 ;
  int64               area_id = 27;
}

message OttPlayurlResp {
  int32        code = 1;
  string       msg = 2;
  OttPlayurlData data = 3;
  string       uuid = 4;
}

message OttPlayurlData {
  int32                       cid = 1;
  repeated QualityDescription g_qn_desc = 2;
  repeated StreamProtocol     stream = 3;
  P2PData                     p2p_data = 4;
  repeated int32              dolby_qn = 5;
}

// onetier
message OnetierPlayurlReq {
  uint32              cid = 1;
  uint32              quality = 2;  // 0, [4, 3, 2, ]10000, 400, 250, 150, 80
  uint32              stream_type = 3; // 0=>all_random, 1=>main_stream, 2=>backup_stream
  uint32              stream_len = 4;  // 0=>持续拉流，-1=>不可拉流, >0 => 可拉流的时长，单位秒
  string              uipstr = 5;
  uint32              free_type = 6; // 0 noe, 1 cu, 2 ct, 3 cmcc
  bool                only_audio = 7;
  bool                only_video = 8;
  bool                https_url_req = 9;
  uint32              mask = 10; // 0 none, 1 mask
  int64               uid = 11;
  string              platform = 12;
  uint32              build = 13;
  uint32              p2p = 14;
  uint32              net_status = 15;
  string              device_name = 16;
  string              req_biz = 17;
  repeated Protocol   protocol = 18;
  repeated Format     format = 19;
  repeated Codec      codec = 20;
  string              uuid = 21;
  uint32              uip = 22;
  Ipv6                uipv6 = 23;
  DolbyType           dolby = 24;
  bool                is_support_hdr = 25;
  uint32              group = 26; // 双路流0=>A组，1=>B组
  uint64              dolbyval = 27; // bit位 0:不请求杜比 1:请求杜比 2:杜比音效 4:杜比全景声 8:杜比视界
  uint32              attr_val = 28; // attribute value  0:none 1:全景流
  uint32              force_delay = 29; // 房间强制延迟的秒数，当前只有format=FMP4(即hlsv7)支持延迟
  bool                isProj = 30; // 是否是投屏请求
  int64               area_parent_id = 31;
  int64               area_id = 32;
  payload             payload = 33;
  repeated HdrType    hdr_type = 34; // 支持的hdr类型
}

message OnetierPlayurlResp {
  int32        code = 1;
  string       msg = 2;
  OnetierPlayurlData data = 3;
  string       uuid = 4;
}

message OldOnetierPlayurlResp {
  int32        code = 1;
  string       msg = 2;
  video.live.playurl.one.ResponseData data = 3;
  string       uuid = 4;
}

message OnetierPlayurlData {
  int32                       cid = 1;
  repeated QualityDescription g_qn_desc = 2;
  repeated StreamProtocol     stream = 3;
  P2PData                     p2p_data = 4;
  repeated int32              dolby_qn = 5;
}

// batch
message BatchPlayurlReq {
  repeated uint32     room_ids = 1;

  repeated Protocol   protocol = 2;
  repeated Format     format = 3;
  repeated Codec      codec = 4;

  uint32              quality = 5;  // 0, [4, 3, 2, ]10000, 400, 250, 150, 80
  string              uipstr = 6;
  bool                https_url_req = 7;
  uint32              mask = 8; // 0 none, 1 mask
  map<uint32, uint32> p2p = 9;
  string              platform = 10;
  int64               uid = 11;
  uint32              build = 12;
  uint32              net_status = 13;
  string              device_name = 14;
  string              req_biz = 15;
  DolbyType           dolby = 16;

  string              uuid = 17;
  uint32              uip = 18;
  Ipv6                uipv6 = 19;
  map<uint32, uint32>  group = 20; // key:房间,value:0=>A组，1=>B组
  uint64              dolbyval = 21; // bit位 0:不请求杜比 1:请求杜比 2:杜比音效 4:杜比全景声 8:杜比视界
  map<uint32, PFC>     req_pfc = 22; // key:房间id,value:房间请求的protocol + format + codec组合
  map<uint32, uint32>  force_delay = 23; // key:房间id,value:强制延迟的秒数
  int64               area_parent_id = 24;
  int64               area_id = 25;
  map<uint32, uint32> quality_map = 26;
  map<uint32, payload> payload_map = 27;
  repeated HdrType    hdr_type = 28; // 支持的hdr类型
}

message BatchPlayurlResp {
  int32        code = 1;
  string       msg = 2;
  BatchPlayurlData data = 3;
  string       uuid = 4;
}

message BatchPlayurlData {
  repeated QualityDescription g_qn_desc = 1;
  repeated BatchStreamUrl          stream = 2;
}

message BatchStreamUrl {
  int32            cid = 1;
  repeated BatchUrlInfo urlinfo = 2;
  int32            p2p_type = 3;
  repeated string  m_servers = 4;
  repeated int32   dolby_qn = 5;
  repeated QualityDescription qn_desc = 6; // 如果qn_desc不为空此房间应该使用qn_desc去替换g_qn_desc
  string           master_url = 7;
}

// 如果与播放地址本身包含的 url query 参数冲突，则不会生效
message payload {
  repeated payloadKV payload_kv = 1;
}

message payloadKV {
  string key = 1;
  string value = 2;
}

message MasterPlayListParam {
  int64 room_id = 1;
  string origin_stream_name = 2;
  string rate = 3;
  int64 mid = 4;
  string uip = 5;
  int64 stream_type = 6;
  string platform = 7;
  int64 p2p_type = 8;
  int64 net_status = 9;
  uint32 free_type = 10;
  int64 time_shift = 11;
  repeated HdrType hdr_type = 12;
  repeated Codec codec = 13;
  int32 qn = 14;
  payload payload = 15;
}

message MasterPlayList {
  ExtXContentSteering steering = 1;
  repeated ExtXStreamInf streams = 2;
}

message ExtXContentSteering {
  string server_uri = 1;
  repeated string bili_pathway_priority = 2;
  int32 bili_ctr_mode = 3;
  int32 bili_ttl = 4;
}

message ExtXStreamInf {
  StreamAvCover av_cover = 1;
  uint64 bili_orders = 2;
  string bili_display = 3;
  string pathway_id = 4;
  int32 bili_qn = 5;
  string playurl = 6;
  string stream_name = 7;
  Codec codec = 8;
}

message MasterListResp {
  int32  code = 1;
  string msg = 2;
  string payload = 3;
}

message MasterListReq {
  int64 cid = 1;
  int64 mid = 2;
  string platform = 3;
  string uipstr = 4;
  int32 p2p_type = 5;
  int64 net = 6;
  string stream_name = 7;
  int64 time_shift = 8;
  repeated Codec codec = 9;
  int32 qn = 10;
  payload payload = 11;
}

service Playurl {
  rpc Ping(.google.protobuf.Empty) returns (.google.protobuf.Empty);
  // ott
  rpc OttPlayUrl(OttPlayurlReq) returns (OttPlayurlResp);
  // onetier
  rpc OnetierPlayurl(OnetierPlayurlReq)returns(OnetierPlayurlResp);
  rpc OldOnetierPlayurl(OnetierPlayurlReq)returns(OldOnetierPlayurlResp);
  // batch
  rpc BatchPlayurl(BatchPlayurlReq)returns(BatchPlayurlResp);
  // masterList
  rpc MasterListPlayurl(MasterListReq)returns(MasterListResp);
}
