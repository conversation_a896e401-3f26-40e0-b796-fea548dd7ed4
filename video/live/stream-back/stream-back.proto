syntax = "proto3";

// // protoc -I=./ --go_out=plugins=grpc:./ stream-back.proto
import "extension/wdcli/wdcli.proto";
import "github.com/gogo/protobuf/gogoproto/gogo.proto";

package video.live_core.stream_back.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/video/live.stream.back;v1";
option java_package = "com.bapis.video.live.stream.back";
option java_multiple_files = true;
option (wdcli.appid) = "video.live-core.stream-back";


enum HeartbeatType {
  origin = 0;
  edge = 1;
}

message Empty {
}

message StreamAddr {
  string clarity = 1; // 清晰度
  string machine_name = 2; // 对应的机器名称
}

message StreamCDN {
  uint64  vendor = 1; // cdn
  repeated StreamAddr addr = 2;// 对应的地址
}

message StreamBackReq {
  string stream_name = 1; // 流名
}

message StreamBackResponse {
  string stream_name = 1; // 流名
  repeated  StreamCDN cdn = 2; // 可调度的CDN列表
}

message MachineValueReq {
  string vendor = 1; // cdn的属性
}

message GetBackAddrReq {
  int64 vendor = 1;
}

message Machine {
  string addr = 1; // 回源地址
  string isp = 2 ; // 运营商
  repeated Arg args = 3; //参数
  string version = 4;// 版本
  string playSsig = 5 [(gogoproto.jsontag) = "play_ssig", json_name = "play_ssig"];
  map<string, string> tag = 6;
  string cluster = 7;
}

message MachineHls {
  string addr = 1; // 回源地址
  string isp = 2 ; // 运营商
  repeated Arg args = 3; //参数
  string version = 4;// 版本
  string playSsig = 5 [(gogoproto.jsontag) = "play_ssig", json_name = "play_ssig"];
}

message Arg {
  string key = 1;
  string value = 2;
}

message MachineList {
  string name = 1;  // 名称
  repeated Machine list = 2; // 对应的列表
}

message MachineListHls {
  string name = 1;  // 名称
  repeated MachineHls list = 2;
  repeated MachineHls timeShiftList = 3;
}

message MachineValueResponse{
  repeated MachineList list = 1; //
  repeated Machine default = 2;
}

message MachineValueHlsResponse{
  repeated MachineListHls list = 1; //
  MachineListHls default = 2;
}

message MachineValuePortalResponse{
  repeated MachineList list = 1; //
  repeated Machine default = 2;
  repeated MachineListHls hlsList = 3; //
  MachineListHls hlsDefault = 4;
}

message RoomMulBackReq {
  repeated int64 room_ids = 1; // 批量房间号
}

message RoomMulBackItem {
  int64 room_id = 1;
  repeated StreamBackResponse detail = 2;
}

message RoomMulBackResponse {
  repeated RoomMulBackItem list = 1;
}

message StreamBackList {
  string stream_name = 1;
  map<string, string>  back = 2; // 映射关系，一个清晰度，一个回源地址
}

message  RoomItem {
  int64 room_id = 1; // 房间号
  map<string, StreamBackList>  stream_back_data = 2; // 回源地址列表
}

message RoomMulBackResponseV2 {
  map<int64, RoomItem> data = 1;
}

message RecordNotifyReq {
  int64 vendor = 1;
  string source = 2;
  string streamName = 3;
  string ECIp = 4;
  string extras = 5;
}

message Cdn {
  string name = 1;
  int64 vendor = 2;
}

message GetAllBackCdnResp {
  repeated Cdn cdn = 1;
}

message BatchGetBackAddrReq {
  repeated int64 vendors = 1;
}

message BatchGetBackAddrResp {
  repeated CdnMachineValue info = 1;
}

message CdnMachineValue{
  MachineValueResponse info = 1;
  int64 vendor = 2;
}

message HlsHeartBeatReq {
  string streamName = 1;
  string ip = 2;
  HeartbeatType source = 3;
}

message HlsMonitorReq {
  string streamName = 1;
  int64  expires = 2;
}

message HlsMonitorResp {
  bool Edge = 1;
  bool Origin = 2;
}

service StreamBack {
  // 批量查询房间回源地址
  rpc GetRoomMulBack (RoomMulBackReq) returns(RoomMulBackResponse){}
  // 批量查询房间回源地址v2版本
  rpc GetRoomMulBackV2 (RoomMulBackReq) returns(RoomMulBackResponseV2){}
  // 查询地址机器名对应的地址
  rpc GetMachineValue (MachineValueReq) returns (MachineValueResponse) {}
  // 厂商获取全量的AES加密回源数据
  rpc GetBackAddr (GetBackAddrReq) returns (MachineValueResponse) {}
  // 供开播回调使用 设置一条流的数据(边缘记录所在边缘ip，非边缘记录转推数据)
  rpc RecordNotify (RecordNotifyReq) returns (Empty) {}
  // 供hls开播回调使用 设置一条流的数据(边缘记录所在边缘ip，非边缘记录转推数据)
  rpc RecordHlsNotify (RecordNotifyReq) returns (Empty) {}
  // 供rtc开播回调使用 设置一条的数据（记录流所在我primary、secondary rtc服务ip）
  rpc RecordRtcNotify(RecordNotifyReq) returns (Empty) {}
  // 厂商获取全量的AES加密回源数据(HLS)
  rpc GetBackAddrHls(GetBackAddrReq) returns (MachineValueHlsResponse) {}
  // hls心跳
  rpc HlsHeartBeat(HlsHeartBeatReq) returns(Empty){}
  // 获取hls监控
  rpc HlsMonitor(HlsMonitorReq) returns(HlsMonitorResp){}
}