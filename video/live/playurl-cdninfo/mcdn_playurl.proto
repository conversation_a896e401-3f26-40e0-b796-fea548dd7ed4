syntax="proto3";

import "video/live/playurl-cdninfo/mcdn_idc_info.proto";

package video.playurl.live_playurl_cdninfo.v1;
option go_package = "buf.bilibili.co/bapis/bapis-gen/video/live.playurl.cdninfo;v1";
option java_package = "com.bapis.video.live.playurl.cdninfo";
option java_multiple_files = true;

option cc_generic_services = true;

message McdnNode {
    bool bw_status           = 1; // 节点带宽是否已满
    repeated Slot slot_range = 2; // 节点支持的hash范围
}

message McdnIdcsInfo {
    bool bw_status                  = 1; // 机房带宽是否已满
    repeated Slot slot_range        = 2; // 机房支持的hash范围
    map<uint32, McdnNode> node_info = 3; // 机房内的节点
}

message McdnServersInfo {
    int32 total_bandwidth       = 1; // 总带宽
    int32 line_bandwidth        = 2; // 单线带宽
    int32 curr_bandwidth        = 3; // 当前带宽
    map<uint32, int32> ips_wnd  = 4; // key : iplong, value : ip窗口
    repeated string valid_ports = 5; // 节点支持的有效端口
}

message McdnResponseData {
    map<uint32, McdnIdcsInfo> idc_info    = 1; // key : idc, value : 机房和机房内节点的带宽和hash信息
    map<uint32, McdnServersInfo> srv_info = 2; // key : sid, value : 节点详细信息
    map<uint32, ZoneCover> zone_info      = 3; // key : zoneid, value : 覆盖区域IDC信息
}

message HashIdc {
    uint32 hash_id = 1;
    uint32 idc_id  = 2;
}

message ZoneList {
    repeated uint32 idcs      = 1;
    repeated HashIdc hash_idc = 2;
}

message ZoneCover {
    uint32            zone_id   = 1;
    repeated ZoneList zone_list = 2;
}

message FetchMcdnInfoMsg {
    uint32 code           = 1;
    string message        = 2;
    McdnResponseData data = 3;
}