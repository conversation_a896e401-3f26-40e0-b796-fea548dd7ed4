syntax = "proto3";


import "extension/wdcli/wdcli.proto";
package xcode;
option go_package = "buf.bilibili.co/bapis/bapis-gen/video/live.xcode;v1";
option java_package = "com.bapis.video.live.xcode";
option java_multiple_files = true;
option (wdcli.appid) = "video.live.xcode-controller";

//kratos tool protoc --grpc api.proto
message StreamInfoReq {
  repeated string streamNames = 1;
}
message StreamInfoRsp{
  repeated StreamInfo streamInfos = 1;
}
message StreamInfo {
  string streamName = 1;
  int32  streamType = 2;
  repeated string codeConfigs = 3;
}

message BrushRoomReq {
  repeated int64 RoomIds = 1;
}

message BrushRoomResp {
}

message SetRoomConfReq {
  string Attach = 1;
  int64  OutPut = 2;
  int64  RoomId = 3;
  int64  Ttl = 4;
}

message SetRoomConfResp {
}

message RoomConfStatsReq {
  repeated int64 RoomIds = 1;
}

message RoomConfStatsResp {
  map<int64, bool> Data = 1;
}

message RoomDefaultSuffixReq {
  int64 roomId = 1;
}

message RoomDefaultSuffixResp {
  string WebDefaultSuffix = 1;
  string MobileDefaultSuffix = 2;
}

message SaveWaterMarkRoomReq {
  repeated int64 roomId = 1;
}

message SaveWaterMarkRoomResp {

}

message AddHlsV3RoomReq {
  int64  RoomId = 1;
  // user 根据业务情况传入
  string User = 2;
}

message AddHlsV3RoomResp {
}

message DelHlsV3RoomReq {
  int64  RoomId = 1;
}

message DelHlsV3RoomResp {
}

message AllHlsV3RoomReq {
}

message AllHlsV3RoomResp {
  repeated int64 RoomIds = 1;
}

enum TimeShiftSetType{
  Query = 0;
  Add = 1;
  Delete = 2;
}

message TimeShiftSetReq {
  int64 roomId = 1;
  TimeShiftSetType operation = 2;
}

message TimeShiftSetRsp{
  bool status = 1;
}

message QueryRoomHlsV3Req{
  // 要查询的直播间号(长号)
  int64 RoomId = 1;
}

message QueryRoomHlsV3Resp{
  // 房间是否配置了hlsv7
  bool  HlsV7 = 1;
  // 房间是否配置了hlsv3
  bool  HlsV3 = 2;
  // 直播间下所有流的hlsv3配置详情。如果HlsV3和HlsV7都为true时,有流的配置不是true请联系小胖儿
  map<string, bool> StreamDetail = 3;
}


service XcodeService {
  //Gps查询服务
  rpc GetStreamInfo (StreamInfoReq) returns (StreamInfoRsp) {}
  //增加刷子房间接口
  rpc BrushRoom(BrushRoomReq)returns(BrushRoomResp){}
  //为房间配置套餐接口
  rpc AddRoomConf(SetRoomConfReq)returns(SetRoomConfResp){}
  //判断房间是否有套餐
  rpc RoomConfStats(RoomConfStatsReq)returns(RoomConfStatsResp){}
  //获得房间默认清晰度
  rpc RoomDefaultSuffix (RoomDefaultSuffixReq)returns(RoomDefaultSuffixResp){}
  //waterMarkConf
  rpc SaveWaterMarkRoom(SaveWaterMarkRoomReq) returns (SaveWaterMarkRoomResp) {}
  // 增加hlsV3配置
  rpc AddHlsV3Room(AddHlsV3RoomReq)returns(AddHlsV3RoomResp) {}
  // 删除hlsV3配置
  rpc DelHlsV3Room(DelHlsV3RoomReq)returns(DelHlsV3RoomResp) {}
  // 获得所有hlsV3配置
  rpc AllHlsV3Room(AllHlsV3RoomReq)returns(AllHlsV3RoomResp) {}
  // 时移房间配置
  rpc TimeShiftSet(TimeShiftSetReq)returns(TimeShiftSetRsp){}
  // 查询房间hlsv3配置
  rpc QueryRoomHlsV3(QueryRoomHlsV3Req)returns(QueryRoomHlsV3Resp){}
}