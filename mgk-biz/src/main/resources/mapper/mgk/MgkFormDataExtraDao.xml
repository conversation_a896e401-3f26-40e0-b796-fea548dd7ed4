<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkFormDataExtraDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="form_data_id" jdbcType="BIGINT" property="formDataId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_item_id" jdbcType="BIGINT" property="formItemId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="is_cheat" jdbcType="TINYINT" property="isCheat" />
    <result column="sales_type" jdbcType="INTEGER" property="salesType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, form_data_id, form_id, form_item_id, label, value, ctime, mtime, is_deleted, 
    code, is_cheat, sales_type
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_form_data_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_form_data_extra
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mgk_form_data_extra
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPoExample">
    delete from mgk_form_data_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_data_extra (form_data_id, form_id, form_item_id, 
      label, value, ctime, 
      mtime, is_deleted, code, 
      is_cheat, sales_type)
    values (#{formDataId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{formItemId,jdbcType=BIGINT}, 
      #{label,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{code,jdbcType=VARCHAR}, 
      #{isCheat,jdbcType=TINYINT}, #{salesType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_data_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="formDataId != null">
        form_data_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formItemId != null">
        form_item_id,
      </if>
      <if test="label != null">
        label,
      </if>
      <if test="value != null">
        value,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="isCheat != null">
        is_cheat,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="formDataId != null">
        #{formDataId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formItemId != null">
        #{formItemId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="isCheat != null">
        #{isCheat,jdbcType=TINYINT},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPoExample" resultType="java.lang.Long">
    select count(*) from mgk_form_data_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_form_data_extra
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.formDataId != null">
        form_data_id = #{record.formDataId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formItemId != null">
        form_item_id = #{record.formItemId,jdbcType=BIGINT},
      </if>
      <if test="record.label != null">
        label = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null">
        value = #{record.value,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.isCheat != null">
        is_cheat = #{record.isCheat,jdbcType=TINYINT},
      </if>
      <if test="record.salesType != null">
        sales_type = #{record.salesType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_form_data_extra
    set id = #{record.id,jdbcType=BIGINT},
      form_data_id = #{record.formDataId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_item_id = #{record.formItemId,jdbcType=BIGINT},
      label = #{record.label,jdbcType=VARCHAR},
      value = #{record.value,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      code = #{record.code,jdbcType=VARCHAR},
      is_cheat = #{record.isCheat,jdbcType=TINYINT},
      sales_type = #{record.salesType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPo">
    update mgk_form_data_extra
    <set>
      <if test="formDataId != null">
        form_data_id = #{formDataId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formItemId != null">
        form_item_id = #{formItemId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        label = #{label,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="isCheat != null">
        is_cheat = #{isCheat,jdbcType=TINYINT},
      </if>
      <if test="salesType != null">
        sales_type = #{salesType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPo">
    update mgk_form_data_extra
    set form_data_id = #{formDataId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      form_item_id = #{formItemId,jdbcType=BIGINT},
      label = #{label,jdbcType=VARCHAR},
      value = #{value,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      code = #{code,jdbcType=VARCHAR},
      is_cheat = #{isCheat,jdbcType=TINYINT},
      sales_type = #{salesType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_data_extra (form_data_id, form_id, form_item_id, 
      label, value, ctime, 
      mtime, is_deleted, code, 
      is_cheat, sales_type)
    values (#{formDataId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{formItemId,jdbcType=BIGINT}, 
      #{label,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{code,jdbcType=VARCHAR}, 
      #{isCheat,jdbcType=TINYINT}, #{salesType,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      form_data_id = values(form_data_id),
      form_id = values(form_id),
      form_item_id = values(form_item_id),
      label = values(label),
      value = values(value),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      code = values(code),
      is_cheat = values(is_cheat),
      sales_type = values(sales_type),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_form_data_extra
      (form_data_id,form_id,form_item_id,label,value,ctime,mtime,is_deleted,code,is_cheat,sales_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.formDataId,jdbcType=BIGINT},
        #{item.formId,jdbcType=BIGINT},
        #{item.formItemId,jdbcType=BIGINT},
        #{item.label,jdbcType=VARCHAR},
        #{item.value,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.code,jdbcType=VARCHAR},
        #{item.isCheat,jdbcType=TINYINT},
        #{item.salesType,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_form_data_extra
      (form_data_id,form_id,form_item_id,label,value,ctime,mtime,is_deleted,code,is_cheat,sales_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.formDataId,jdbcType=BIGINT},
        #{item.formId,jdbcType=BIGINT},
        #{item.formItemId,jdbcType=BIGINT},
        #{item.label,jdbcType=VARCHAR},
        #{item.value,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.code,jdbcType=VARCHAR},
        #{item.isCheat,jdbcType=TINYINT},
        #{item.salesType,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      form_data_id = values(form_data_id),
      form_id = values(form_id),
      form_item_id = values(form_item_id),
      label = values(label),
      value = values(value),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      code = values(code),
      is_cheat = values(is_cheat),
      sales_type = values(sales_type),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkFormDataExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_form_data_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="formDataId != null">
        form_data_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formItemId != null">
        form_item_id,
      </if>
      <if test="label != null">
        label,
      </if>
      <if test="value != null">
        value,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="isCheat != null">
        is_cheat,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="formDataId != null">
        #{formDataId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formItemId != null">
        #{formItemId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="isCheat != null">
        #{isCheat,jdbcType=TINYINT},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="formDataId != null">
        form_data_id = values(form_data_id),
      </if>
      <if test="formId != null">
        form_id = values(form_id),
      </if>
      <if test="formItemId != null">
        form_item_id = values(form_item_id),
      </if>
      <if test="label != null">
        label = values(label),
      </if>
      <if test="value != null">
        value = values(value),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="code != null">
        code = values(code),
      </if>
      <if test="isCheat != null">
        is_cheat = values(is_cheat),
      </if>
      <if test="salesType != null">
        sales_type = values(sales_type),
      </if>
    </trim>
  </insert>
</mapper>