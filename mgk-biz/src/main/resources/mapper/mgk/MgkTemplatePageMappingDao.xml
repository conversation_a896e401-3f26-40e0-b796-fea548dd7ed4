<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.mgk.platform.biz.dao.MgkTemplatePageMappingDao">
  <resultMap id="BaseResultMap" type="com.bilibili.mgk.platform.biz.po.MgkTemplatePageMappingPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="hash_key" jdbcType="VARCHAR" property="hashKey" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="page_id" jdbcType="BIGINT" property="pageId" />
    <result column="game_base_id" jdbcType="INTEGER" property="gameBaseId" />
    <result column="package_id" jdbcType="INTEGER" property="packageId" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="template_page_id" jdbcType="BIGINT" property="templatePageId" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, hash_key, account_id, page_id, game_base_id, package_id, url, template_page_id, 
    is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkTemplatePageMappingPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mgk_template_page_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mgk_template_page_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mgk_template_page_mapping
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkTemplatePageMappingPoExample">
    delete from mgk_template_page_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.mgk.platform.biz.po.MgkTemplatePageMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_template_page_mapping (hash_key, account_id, page_id, 
      game_base_id, package_id, url, 
      template_page_id, is_deleted, ctime, 
      mtime)
    values (#{hashKey,jdbcType=VARCHAR}, #{accountId,jdbcType=INTEGER}, #{pageId,jdbcType=BIGINT}, 
      #{gameBaseId,jdbcType=INTEGER}, #{packageId,jdbcType=INTEGER}, #{url,jdbcType=VARCHAR}, 
      #{templatePageId,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkTemplatePageMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_template_page_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hashKey != null">
        hash_key,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="pageId != null">
        page_id,
      </if>
      <if test="gameBaseId != null">
        game_base_id,
      </if>
      <if test="packageId != null">
        package_id,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="templatePageId != null">
        template_page_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hashKey != null">
        #{hashKey,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="gameBaseId != null">
        #{gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="packageId != null">
        #{packageId,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="templatePageId != null">
        #{templatePageId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.mgk.platform.biz.po.MgkTemplatePageMappingPoExample" resultType="java.lang.Long">
    select count(*) from mgk_template_page_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mgk_template_page_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.hashKey != null">
        hash_key = #{record.hashKey,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.pageId != null">
        page_id = #{record.pageId,jdbcType=BIGINT},
      </if>
      <if test="record.gameBaseId != null">
        game_base_id = #{record.gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="record.packageId != null">
        package_id = #{record.packageId,jdbcType=INTEGER},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.templatePageId != null">
        template_page_id = #{record.templatePageId,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mgk_template_page_mapping
    set id = #{record.id,jdbcType=INTEGER},
      hash_key = #{record.hashKey,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=INTEGER},
      page_id = #{record.pageId,jdbcType=BIGINT},
      game_base_id = #{record.gameBaseId,jdbcType=INTEGER},
      package_id = #{record.packageId,jdbcType=INTEGER},
      url = #{record.url,jdbcType=VARCHAR},
      template_page_id = #{record.templatePageId,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkTemplatePageMappingPo">
    update mgk_template_page_mapping
    <set>
      <if test="hashKey != null">
        hash_key = #{hashKey,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="pageId != null">
        page_id = #{pageId,jdbcType=BIGINT},
      </if>
      <if test="gameBaseId != null">
        game_base_id = #{gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="packageId != null">
        package_id = #{packageId,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="templatePageId != null">
        template_page_id = #{templatePageId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.mgk.platform.biz.po.MgkTemplatePageMappingPo">
    update mgk_template_page_mapping
    set hash_key = #{hashKey,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=INTEGER},
      page_id = #{pageId,jdbcType=BIGINT},
      game_base_id = #{gameBaseId,jdbcType=INTEGER},
      package_id = #{packageId,jdbcType=INTEGER},
      url = #{url,jdbcType=VARCHAR},
      template_page_id = #{templatePageId,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.mgk.platform.biz.po.MgkTemplatePageMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_template_page_mapping (hash_key, account_id, page_id, 
      game_base_id, package_id, url, 
      template_page_id, is_deleted, ctime, 
      mtime)
    values (#{hashKey,jdbcType=VARCHAR}, #{accountId,jdbcType=INTEGER}, #{pageId,jdbcType=BIGINT}, 
      #{gameBaseId,jdbcType=INTEGER}, #{packageId,jdbcType=INTEGER}, #{url,jdbcType=VARCHAR}, 
      #{templatePageId,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      hash_key = values(hash_key),
      account_id = values(account_id),
      page_id = values(page_id),
      game_base_id = values(game_base_id),
      package_id = values(package_id),
      url = values(url),
      template_page_id = values(template_page_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mgk_template_page_mapping
      (hash_key,account_id,page_id,game_base_id,package_id,url,template_page_id,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.hashKey,jdbcType=VARCHAR},
        #{item.accountId,jdbcType=INTEGER},
        #{item.pageId,jdbcType=BIGINT},
        #{item.gameBaseId,jdbcType=INTEGER},
        #{item.packageId,jdbcType=INTEGER},
        #{item.url,jdbcType=VARCHAR},
        #{item.templatePageId,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mgk_template_page_mapping
      (hash_key,account_id,page_id,game_base_id,package_id,url,template_page_id,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.hashKey,jdbcType=VARCHAR},
        #{item.accountId,jdbcType=INTEGER},
        #{item.pageId,jdbcType=BIGINT},
        #{item.gameBaseId,jdbcType=INTEGER},
        #{item.packageId,jdbcType=INTEGER},
        #{item.url,jdbcType=VARCHAR},
        #{item.templatePageId,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      hash_key = values(hash_key),
      account_id = values(account_id),
      page_id = values(page_id),
      game_base_id = values(game_base_id),
      package_id = values(package_id),
      url = values(url),
      template_page_id = values(template_page_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.mgk.platform.biz.po.MgkTemplatePageMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mgk_template_page_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hashKey != null">
        hash_key,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="pageId != null">
        page_id,
      </if>
      <if test="gameBaseId != null">
        game_base_id,
      </if>
      <if test="packageId != null">
        package_id,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="templatePageId != null">
        template_page_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hashKey != null">
        #{hashKey,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="gameBaseId != null">
        #{gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="packageId != null">
        #{packageId,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="templatePageId != null">
        #{templatePageId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="hashKey != null">
        hash_key = values(hash_key),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="pageId != null">
        page_id = values(page_id),
      </if>
      <if test="gameBaseId != null">
        game_base_id = values(game_base_id),
      </if>
      <if test="packageId != null">
        package_id = values(package_id),
      </if>
      <if test="url != null">
        url = values(url),
      </if>
      <if test="templatePageId != null">
        template_page_id = values(template_page_id),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>