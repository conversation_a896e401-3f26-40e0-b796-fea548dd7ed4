package com.bilibili.mgk.platform.biz.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @ClassName ExtMgkWechatAccountDataPo
 * <AUTHOR>
 * @Date 2022/6/18 4:32 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExtMgkWorkWechatDataPo implements Serializable {


    private static final long serialVersionUID = -5185417177748717986L;
    /**
     * 获客链接id
     */
    private Long linkDataId;

    /**
     * 计数
     */
    private Integer dataCount;

    /**
     * 提交类型 0-复制 1-跳转
     */
    private Integer dataType;

    /**
     * 最近提交数据的时间
     */
    private Timestamp recentSubmitTime;
}
