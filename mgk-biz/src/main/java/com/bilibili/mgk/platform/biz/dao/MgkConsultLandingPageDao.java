package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkConsultLandingPageExample;
import com.bilibili.mgk.platform.biz.po.MgkConsultLandingPagePo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MgkConsultLandingPageDao {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    long countByExample(MgkConsultLandingPageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int deleteByExample(MgkConsultLandingPageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int insertUpdate(MgkConsultLandingPagePo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int insertBatch(List<MgkConsultLandingPagePo> records);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int insertUpdateBatch(List<MgkConsultLandingPagePo> records);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int insertIgnore(MgkConsultLandingPagePo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int insertIgnoreBatch(List<MgkConsultLandingPagePo> records);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int insert(MgkConsultLandingPagePo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int insertUpdateSelective(MgkConsultLandingPagePo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int insertIgnoreSelective(MgkConsultLandingPagePo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int insertSelective(MgkConsultLandingPagePo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    List<MgkConsultLandingPagePo> selectByExample(MgkConsultLandingPageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    MgkConsultLandingPagePo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MgkConsultLandingPagePo record, @Param("example") MgkConsultLandingPageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MgkConsultLandingPagePo record, @Param("example") MgkConsultLandingPageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MgkConsultLandingPagePo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mgk_consult_landing_page
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MgkConsultLandingPagePo record);
}