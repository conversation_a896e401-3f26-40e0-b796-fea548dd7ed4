package com.bilibili.mgk.platform.biz.ad.account.dao;

import com.bilibili.mgk.platform.biz.ad.po.AccAccountMidPo;
import com.bilibili.mgk.platform.biz.ad.po.AccAccountMidPoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface AccAccountMidDao {
    long countByExample(AccAccountMidPoExample example);

    int deleteByExample(AccAccountMidPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(AccAccountMidPo record);

    int insertBatch(List<AccAccountMidPo> records);

    int insertUpdateBatch(List<AccAccountMidPo> records);

    int insert(AccAccountMidPo record);

    int insertUpdateSelective(AccAccountMidPo record);

    int insertSelective(AccAccountMidPo record);

    List<AccAccountMidPo> selectByExample(AccAccountMidPoExample example);

    AccAccountMidPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AccAccountMidPo record, @Param("example") AccAccountMidPoExample example);

    int updateByExample(@Param("record") AccAccountMidPo record, @Param("example") AccAccountMidPoExample example);

    int updateByPrimaryKeySelective(AccAccountMidPo record);

    int updateByPrimaryKey(AccAccountMidPo record);
}