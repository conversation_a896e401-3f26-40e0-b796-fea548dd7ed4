package com.bilibili.mgk.platform.biz.ad.account.dao;

import com.bilibili.mgk.platform.biz.ad.po.CustomerPo;
import com.bilibili.mgk.platform.biz.ad.po.CustomerPoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface CustomerDao {
    long countByExample(CustomerPoExample example);

    int deleteByExample(CustomerPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CustomerPo record);

    int insertBatch(List<CustomerPo> records);

    int insertUpdateBatch(List<CustomerPo> records);

    int insert(CustomerPo record);

    int insertUpdateSelective(CustomerPo record);

    int insertSelective(CustomerPo record);

    List<CustomerPo> selectByExample(CustomerPoExample example);

    CustomerPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CustomerPo record, @Param("example") CustomerPoExample example);

    int updateByExample(@Param("record") CustomerPo record, @Param("example") CustomerPoExample example);

    int updateByPrimaryKeySelective(CustomerPo record);

    int updateByPrimaryKey(CustomerPo record);
}