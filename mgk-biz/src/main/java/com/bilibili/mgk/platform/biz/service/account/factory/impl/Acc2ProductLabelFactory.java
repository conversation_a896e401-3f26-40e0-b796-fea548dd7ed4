package com.bilibili.mgk.platform.biz.service.account.factory.impl;

import com.bilibili.mgk.platform.biz.ad.po.AccAccountPo;
import com.bilibili.mgk.platform.biz.service.account.factory.AccountLabelFactory;
import com.bilibili.mgk.platform.biz.service.account.factory.bo.AccountLabelInfoBo;
import com.bilibili.mgk.platform.common.MgkAccountLabelEnum;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

@Slf4j
@Service(value = "acc2ProductLabelFactory")
public class Acc2ProductLabelFactory implements AccountLabelFactory {

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    @Override
    public void refreshLabel2redis(AccountLabelInfoBo labelInfoBo) {
        MgkAccountLabelEnum labelEnum = labelInfoBo.getLabelEnum();
        if(CollectionUtils.isEmpty(labelInfoBo.getAccountIds())){
            return;
        }
        labelInfoBo.getAccountIds().forEach(accountId->{
            try {
                AccAccountPo accAccountPo = labelInfoBo.getAccountId2PoMap().get(accountId);
                String accountId2ProductIdRedisKey = labelEnum.getPrefixInRedis() + accountId;
                stringRedisTemplate.opsForValue().set(accountId2ProductIdRedisKey,accAccountPo.getProductId().toString());
            } catch (Exception e) {
                log.error("acc2ProductLabelFactory refreshLabel2redis error" + Throwables.getStackTraceAsString(e));
            }
        });
    }
}
