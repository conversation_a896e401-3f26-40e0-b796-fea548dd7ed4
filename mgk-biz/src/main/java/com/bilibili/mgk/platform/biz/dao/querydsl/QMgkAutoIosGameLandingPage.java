package com.bilibili.mgk.platform.biz.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.mgk.platform.biz.dao.querydsl.pos.MgkAutoIosGameLandingPageQueryDSLPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QMgkAutoIosGameLandingPage is a Querydsl query type for MgkAutoIosGameLandingPageQueryDSLPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QMgkAutoIosGameLandingPage extends com.querydsl.sql.RelationalPathBase<MgkAutoIosGameLandingPageQueryDSLPo> {

    private static final long serialVersionUID = -********;

    public static final QMgkAutoIosGameLandingPage mgkAutoIosGameLandingPage = new QMgkAutoIosGameLandingPage("mgk_auto_ios_game_landing_page");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final NumberPath<Long> appStoreId = createNumber("appStoreId", Long.class);

    public final StringPath appStoreMd5 = createString("appStoreMd5");

    public final StringPath appStoreUrl = createString("appStoreUrl");

    public final NumberPath<Long> coverId = createNumber("coverId", Long.class);

    public final StringPath coverMd5 = createString("coverMd5");

    public final StringPath coverUrl = createString("coverUrl");

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> fallbackH5Id = createNumber("fallbackH5Id", Long.class);

    public final StringPath fallbackH5Md5 = createString("fallbackH5Md5");

    public final StringPath fallbackH5Url = createString("fallbackH5Url");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> landingPageId = createNumber("landingPageId", Long.class);

    public final StringPath landingPageUrl = createString("landingPageUrl");

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final NumberPath<Long> videoId = createNumber("videoId", Long.class);

    public final NumberPath<Integer> videoLibraryId = createNumber("videoLibraryId", Integer.class);

    public final StringPath videoMd5 = createString("videoMd5");

    public final StringPath videoUrl = createString("videoUrl");

    public final com.querydsl.sql.PrimaryKey<MgkAutoIosGameLandingPageQueryDSLPo> primary = createPrimaryKey(id);

    public QMgkAutoIosGameLandingPage(String variable) {
        super(MgkAutoIosGameLandingPageQueryDSLPo.class, forVariable(variable), "null", "mgk_auto_ios_game_landing_page");
        addMetadata();
    }

    public QMgkAutoIosGameLandingPage(String variable, String schema, String table) {
        super(MgkAutoIosGameLandingPageQueryDSLPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QMgkAutoIosGameLandingPage(String variable, String schema) {
        super(MgkAutoIosGameLandingPageQueryDSLPo.class, forVariable(variable), schema, "mgk_auto_ios_game_landing_page");
        addMetadata();
    }

    public QMgkAutoIosGameLandingPage(Path<? extends MgkAutoIosGameLandingPageQueryDSLPo> path) {
        super(path.getType(), path.getMetadata(), "null", "mgk_auto_ios_game_landing_page");
        addMetadata();
    }

    public QMgkAutoIosGameLandingPage(PathMetadata metadata) {
        super(MgkAutoIosGameLandingPageQueryDSLPo.class, metadata, "null", "mgk_auto_ios_game_landing_page");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(appStoreId, ColumnMetadata.named("app_store_id").withIndex(12).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(appStoreMd5, ColumnMetadata.named("app_store_md5").withIndex(11).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(appStoreUrl, ColumnMetadata.named("app_store_url").withIndex(10).ofType(Types.VARCHAR).withSize(512).notNull());
        addMetadata(coverId, ColumnMetadata.named("cover_id").withIndex(6).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(coverMd5, ColumnMetadata.named("cover_md5").withIndex(5).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(coverUrl, ColumnMetadata.named("cover_url").withIndex(4).ofType(Types.VARCHAR).withSize(512).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(17).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(fallbackH5Id, ColumnMetadata.named("fallback_h5_id").withIndex(15).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(fallbackH5Md5, ColumnMetadata.named("fallback_h5_md5").withIndex(14).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(fallbackH5Url, ColumnMetadata.named("fallback_h5_url").withIndex(13).ofType(Types.VARCHAR).withSize(512).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(landingPageId, ColumnMetadata.named("landing_page_id").withIndex(20).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(landingPageUrl, ColumnMetadata.named("landing_page_url").withIndex(3).ofType(Types.VARCHAR).withSize(512).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(18).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(status, ColumnMetadata.named("status").withIndex(16).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(videoId, ColumnMetadata.named("video_id").withIndex(9).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(videoLibraryId, ColumnMetadata.named("video_library_id").withIndex(19).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(videoMd5, ColumnMetadata.named("video_md5").withIndex(8).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(videoUrl, ColumnMetadata.named("video_url").withIndex(7).ofType(Types.VARCHAR).withSize(512).notNull());
    }

}

