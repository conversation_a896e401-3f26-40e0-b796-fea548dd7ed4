package com.bilibili.mgk.platform.biz.service.agent;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.passport.api.dto.BidUserInfoDto;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.crm.platform.common.AgentType;
import com.bilibili.mgk.platform.api.agent.IMgkAgentService;
import com.bilibili.mgk.platform.api.agent.dto.AgentDto;
import com.bilibili.mgk.platform.api.agent.dto.SecondaryAgentDto;
import com.bilibili.mgk.platform.biz.ad.account.dao.CrmAgentDao;
import com.bilibili.mgk.platform.biz.ad.dao.CrmSecondaryAgentCustomerDao;
import com.bilibili.mgk.platform.biz.ad.dao.CrmSecondaryAgentRoleDao;
import com.bilibili.mgk.platform.biz.ad.po.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName MgkAgentServiceImpl
 * <AUTHOR>
 * @Date 2022/6/20 11:52 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkAgentServiceImpl implements IMgkAgentService {

    @Autowired
    private CrmAgentDao crmAgentDao;

    @Autowired
    private CrmSecondaryAgentRoleDao crmSecondaryAgentRoleDao;

    @Autowired
    private CrmSecondaryAgentCustomerDao crmSecondaryAgentCustomerDao;

    @Autowired
    private IPassportService passportService;

    @Override
    public AgentDto getBuAgentByAccountId(Integer accountId) {
        Assert.notNull(accountId, "账户ID不可为空");
        log.info("AgentService.getBuAgentByAccountId accountId {}", accountId);
        CrmAgentPoExample example = new CrmAgentPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andTypeEqualTo(AgentType.OTHER.getCode())
                .andSysAgentIdEqualTo(accountId);
        List<CrmAgentPo> agentPos = crmAgentDao.selectByExample(example);
        Assert.notEmpty(agentPos, "该代理商不存在");
        return agentPo2Dto(agentPos.get(0));
    }

    private AgentDto agentPo2Dto(CrmAgentPo po) {
        AgentDto dto = AgentDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Override
    public SecondaryAgentDto getSecondaryAgentSimpleDtoById(Integer id, Long bid) throws Exception {
        Assert.notNull(id, "二级代理ID不可为空");
        CrmSecondaryAgentRolePo po = this.getById(id);
        Assert.notNull(po, "根据id="+id+"未查询到二级代理商信息");
        SecondaryAgentDto secondaryAgentDto = this.agentSecondaryRolePo2Dto(po);
        BidUserInfoDto userInfo = passportService.queryUserByBid(bid);
        Assert.notNull(userInfo, "无效的B站UID");
        secondaryAgentDto.setNickName(userInfo.getName());
        AgentDto agentDto = this.getBuAgentByAgentId(po.getAgentId());
        secondaryAgentDto.setAgentName(agentDto.getName());
        return secondaryAgentDto;
    }

    public CrmSecondaryAgentRolePo getById(Integer id){
        return crmSecondaryAgentRoleDao.selectByPrimaryKey(id);
    }

    private SecondaryAgentDto agentSecondaryRolePo2Dto(CrmSecondaryAgentRolePo po){
        SecondaryAgentDto dto = SecondaryAgentDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Override
    public AgentDto getBuAgentByAgentId(Integer agentId) {
        Assert.notNull(agentId, "代理商ID不可为空");
        log.info("AgentService.getBuAgentByAgentId agentId {}", agentId);
        CrmAgentPoExample example = new CrmAgentPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(agentId)
                .andTypeEqualTo(AgentType.OTHER.getCode());
        List<CrmAgentPo> agentPos = crmAgentDao.selectByExample(example);
        Assert.notEmpty(agentPos, "该代理商不存在");
        return agentPo2Dto(agentPos.get(0));
    }

    @Override
    public Map<Integer, AgentDto> getAgentMapInIds(List<Integer> agentIds) {
        List<AgentDto> agentDtos = this.getAgentsInIds(agentIds);
        return agentDtos.stream().collect(Collectors.toMap(AgentDto::getId, Function.identity()));
    }

    public List<AgentDto> getAgentsInIds(List<Integer> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            return Collections.emptyList();
        }
        CrmAgentPoExample example = new CrmAgentPoExample();
        example.or().andIdIn(agentIds);
        List<CrmAgentPo> agentPos = crmAgentDao.selectByExample(example);
        return agentPos.stream().map(this::agentPo2Dto).collect(Collectors.toList());
    }

    @Override
    public List<Integer> getSecondaryAgentAccountIds(Integer secondaryAgentId) {
        Assert.notNull(secondaryAgentId, "");
        List<CrmSecondaryAgentCustomerPo> agentCustomerPos = this.getSecondaryAgentCustomerIds(secondaryAgentId);
        if (CollectionUtils.isEmpty(agentCustomerPos)){
            return Collections.emptyList();
        }
        return agentCustomerPos.stream()
                .map(CrmSecondaryAgentCustomerPo::getAccountId)
                .collect(Collectors.toList());
    }

    private List<CrmSecondaryAgentCustomerPo> getSecondaryAgentCustomerIds(Integer secondaryAgentId){
        CrmSecondaryAgentCustomerPoExample example = new CrmSecondaryAgentCustomerPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andSecondaryAgentIdEqualTo(secondaryAgentId);
        return crmSecondaryAgentCustomerDao.selectByExample(example);
    }
}
