package com.bilibili.mgk.platform.biz.service.lbs_area;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.mgk.platform.biz.dao.LbsAreaDao;
import com.bilibili.mgk.platform.biz.po.LbsAreaPo;
import com.bilibili.mgk.platform.biz.po.LbsAreaPoExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class LbsAreaService {

    @Resource
    private LbsAreaDao lbsAreaDao;


    public LbsAreaPo queryCityCodeByCityName(String cityName) {
        LbsAreaPoExample example = new LbsAreaPoExample();
        example.or().andNameEqualTo(cityName)
                .andLevelEqualTo(2)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<LbsAreaPo> lbsAreaPos = lbsAreaDao.selectByExample(example);
        if(CollectionUtils.isEmpty(lbsAreaPos)){
            return null;
        }
        LbsAreaPo lbsAreaPo = lbsAreaPos.get(0);
        return lbsAreaPo;
    }
}
