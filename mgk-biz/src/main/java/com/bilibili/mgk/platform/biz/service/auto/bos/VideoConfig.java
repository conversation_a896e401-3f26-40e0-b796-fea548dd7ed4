package com.bilibili.mgk.platform.biz.service.auto.bos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VideoConfig {
    private Integer muteButton;
    private Integer progressBar;
    private Integer avid;
    private Integer cid;
    private Integer page;
    private String from;
    private Integer width;
    private Integer height;
    private String url;
    private Integer bizId;
    private String name;
    private String cover;
    @JsonProperty("play_3s_urls")
    private List<String> play3sUrls;
    @JsonProperty("play_5s_urls")
    private List<String> play5sUrls;
    private List<String> process0Urls;
    private List<String> process1Urls;
    private List<String> process2Urls;
    private List<String> process3Urls;
    private List<String> process4Urls;
    private Integer id;
    private String image;
    private Integer coverType;
    private Integer duration;
}
