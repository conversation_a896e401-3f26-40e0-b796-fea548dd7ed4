package com.bilibili.mgk.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChinaAreaCmPo implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 父级行政代码
     */
    private String parentCode;

    /**
     * 行政代码
     */
    private String areaCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}