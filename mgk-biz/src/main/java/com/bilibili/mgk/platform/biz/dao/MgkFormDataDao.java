package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkFormDataPo;
import com.bilibili.mgk.platform.biz.po.MgkFormDataPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MgkFormDataDao {
    long countByExample(MgkFormDataPoExample example);

    int deleteByExample(MgkFormDataPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MgkFormDataPo record);

    int insertBatch(List<MgkFormDataPo> records);

    int insertUpdateBatch(List<MgkFormDataPo> records);

    int insert(MgkFormDataPo record);

    int insertUpdateSelective(MgkFormDataPo record);

    int insertSelective(MgkFormDataPo record);

    List<MgkFormDataPo> selectByExample(MgkFormDataPoExample example);

    MgkFormDataPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MgkFormDataPo record, @Param("example") MgkFormDataPoExample example);

    int updateByExample(@Param("record") MgkFormDataPo record, @Param("example") MgkFormDataPoExample example);

    int updateByPrimaryKeySelective(MgkFormDataPo record);

    int updateByPrimaryKey(MgkFormDataPo record);
}