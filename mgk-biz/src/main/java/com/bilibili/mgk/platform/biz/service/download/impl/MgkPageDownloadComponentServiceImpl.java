package com.bilibili.mgk.platform.biz.service.download.impl;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mgk.platform.api.download.IMgkPageDownloadComponentService;
import com.bilibili.mgk.platform.api.download.dto.MgkPageDownloadComponentHeightDto;
import com.bilibili.mgk.platform.biz.dao.MgkPageDownloadComponentHeightDao;
import com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPo;
import com.bilibili.mgk.platform.biz.po.MgkPageDownloadComponentHeightPoExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName MgkPageDownloadComponentServiceImpl
 * <AUTHOR>
 * @Date 2022/8/15 4:36 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class MgkPageDownloadComponentServiceImpl implements IMgkPageDownloadComponentService {

    @Autowired
    private MgkPageDownloadComponentHeightDao mgkPageDownloadComponentHeightDao;

    @Override
    public int insertDownloadComponentHeightInfo(MgkPageDownloadComponentHeightDto dto) {
        MgkPageDownloadComponentHeightPo insertPo = MgkPageDownloadComponentHeightPo.builder()
                .pageId(dto.getPageId())
                .totalBlockSize(dto.getTotalBlockSize())
                .totalDownloadComponentSize(dto.getTotalDownloadComponentSize())
                .totalFirstScreenDownloadComponentHeight(dto.getTotalFirstScreenDownloadComponentSize())
                .maxDownloadComponentSize(dto.getMaxDownloadComponentSize())
                .build();
        int result = mgkPageDownloadComponentHeightDao.insertSelective(insertPo);
        Assert.isTrue(Utils.isPositive(result), "插入下载按钮组件信息失败");
        return result;
    }

    @Override
    public int updateDownloadComponentHeightInfo(MgkPageDownloadComponentHeightDto dto) {
        MgkPageDownloadComponentHeightPo updatePo = MgkPageDownloadComponentHeightPo.builder()
                .pageId(dto.getPageId())
                .totalBlockSize(dto.getTotalBlockSize())
                .totalDownloadComponentSize(dto.getTotalDownloadComponentSize())
                .totalFirstScreenDownloadComponentHeight(dto.getTotalFirstScreenDownloadComponentSize())
                .maxDownloadComponentSize(dto.getMaxDownloadComponentSize())
                .build();
        MgkPageDownloadComponentHeightPoExample exm = new MgkPageDownloadComponentHeightPoExample();
        exm.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(dto.getPageId());
        // 兼容线上
        List<MgkPageDownloadComponentHeightPo> existPos = mgkPageDownloadComponentHeightDao.selectByExample(exm);
        if (CollectionUtils.isEmpty(existPos)) {
            return insertDownloadComponentHeightInfo(dto);
        }
        int result = mgkPageDownloadComponentHeightDao.updateByExampleSelective(updatePo, exm);
        Assert.isTrue(Utils.isPositive(result), "更新下载按钮组件信息失败,落地页对应记录不存在");
        return result;
    }

    @Override
    public MgkPageDownloadComponentHeightDto getByPageId(Long pageId) {
        MgkPageDownloadComponentHeightPoExample exm = new MgkPageDownloadComponentHeightPoExample();
        exm.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(pageId);
        List<MgkPageDownloadComponentHeightPo> result = mgkPageDownloadComponentHeightDao.selectByExample(exm);
        if (CollectionUtils.isEmpty(result)) {
            return MgkPageDownloadComponentHeightDto.builder().build();
        }
        MgkPageDownloadComponentHeightPo resultPo = result.get(0);
        return convertPo2Dto(resultPo);
    }

    @Override
    public Map<Long, MgkPageDownloadComponentHeightDto> getMapByPageList(List<Long> pageIdList) {
        MgkPageDownloadComponentHeightPoExample exm = new MgkPageDownloadComponentHeightPoExample();
        exm.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdIn(pageIdList);
        List<MgkPageDownloadComponentHeightPo> result = mgkPageDownloadComponentHeightDao.selectByExample(exm);
        return result.stream().collect(Collectors.toMap(MgkPageDownloadComponentHeightPo::getPageId, this::convertPo2Dto));
    }

    @Override
    public boolean checkDownloadComponentHeightIsEqual(MgkPageDownloadComponentHeightDto checkDto) {
        MgkPageDownloadComponentHeightPoExample exm = new MgkPageDownloadComponentHeightPoExample();
        exm.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPageIdEqualTo(checkDto.getPageId());
        List<MgkPageDownloadComponentHeightPo> result = mgkPageDownloadComponentHeightDao.selectByExample(exm);
        if (CollectionUtils.isEmpty(result)) {
            return false;
        }
        MgkPageDownloadComponentHeightPo existPo = result.get(0);
        return existPo.getPageId().equals(checkDto.getPageId()) && existPo.getTotalBlockSize().equals(checkDto.getTotalBlockSize())
                && existPo.getTotalDownloadComponentSize().equals(checkDto.getTotalDownloadComponentSize())
                && existPo.getTotalFirstScreenDownloadComponentHeight().equals(checkDto.getTotalFirstScreenDownloadComponentSize())
                && existPo.getMaxDownloadComponentSize().equals(checkDto.getMaxDownloadComponentSize());
    }

    private MgkPageDownloadComponentHeightDto convertPo2Dto(MgkPageDownloadComponentHeightPo po) {
        return MgkPageDownloadComponentHeightDto.builder()
                .pageId(po.getPageId())
                .totalBlockSize(po.getTotalBlockSize())
                .totalDownloadComponentSize(po.getTotalDownloadComponentSize())
                .totalFirstScreenDownloadComponentSize(po.getTotalFirstScreenDownloadComponentHeight())
                .maxDownloadComponentSize(po.getMaxDownloadComponentSize())
                .build();
    }
}
