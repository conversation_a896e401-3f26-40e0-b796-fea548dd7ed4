package com.bilibili.mgk.platform.biz.dao;

import com.bilibili.mgk.platform.biz.po.MgkAccountProfilePo;
import com.bilibili.mgk.platform.biz.po.MgkAccountProfilePoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MgkAccountProfileDao {
    long countByExample(MgkAccountProfilePoExample example);

    int deleteByExample(MgkAccountProfilePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(MgkAccountProfilePo record);

    int insertBatch(List<MgkAccountProfilePo> records);

    int insertUpdateBatch(List<MgkAccountProfilePo> records);

    int insert(MgkAccountProfilePo record);

    int insertUpdateSelective(MgkAccountProfilePo record);

    int insertSelective(MgkAccountProfilePo record);

    List<MgkAccountProfilePo> selectByExample(MgkAccountProfilePoExample example);

    MgkAccountProfilePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") MgkAccountProfilePo record, @Param("example") MgkAccountProfilePoExample example);

    int updateByExample(@Param("record") MgkAccountProfilePo record, @Param("example") MgkAccountProfilePoExample example);

    int updateByPrimaryKeySelective(MgkAccountProfilePo record);

    int updateByPrimaryKey(MgkAccountProfilePo record);
}