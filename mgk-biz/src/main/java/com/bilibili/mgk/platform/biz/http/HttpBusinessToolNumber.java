package com.bilibili.mgk.platform.biz.http;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.cpt.platform.util.OkHttpUtils;
import com.bilibili.mgk.platform.api.http.IHttpBusinessToolNumber;
import com.bilibili.mgk.platform.api.http.dto.BusinessNumberResponse;
import com.bilibili.mgk.platform.api.http.dto.BusinessNumberResultDto;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Slf4j
@Service
public class HttpBusinessToolNumber implements IHttpBusinessToolNumber {

    @Value("${business.tool.host:http://mall.bilibili.co}")
    private String host;
    @Value("${business.tool.url:/business-account/intranet/account/queryUserInfo}")
    private String uidInfoUrl;

    /**
     * 根据 mids 查询经营号类型，员工号需要返回其经营号
     *
     * @param mid 主站mid
     * @return 账户信息
     */
    public BusinessNumberResultDto queryBusinessNoInfo(Long mid) {

        Assert.isTrue(Utils.isPositive(mid), "mid 不能为空");

        // 示例: http://uat-mall.bilibili.co/business-account/intranet/account/queryUserInfo?uid=****************
        String url = host + uidInfoUrl + "?uid=" + mid;
        log.info("queryBusinessNoInfo request [{}]", mid);
        BusinessNumberResponse<BusinessNumberResultDto> response = OkHttpUtils.get(url)
                .callForObject(new TypeToken<BusinessNumberResponse<BusinessNumberResultDto>>() {
        });
        log.info("queryBusinessNoInfo response [{}]", response);
        Assert.notNull(response, "queryBusinessNoInfo fail response is null");
        Assert.isTrue(BusinessNumberResponse.SUCCESS.equals(response.getCode()),
                "queryBusinessNoInfo fail msg" + response.getMessage());
        return response.getData();
    }


}
