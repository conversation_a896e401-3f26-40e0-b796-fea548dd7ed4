package com.bilibili.mgk.platform.biz.config;

import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2020/07/02
 **/
@Configuration
@PropertySource(value = "classpath:es.properties")
//@EnableElasticsearchRepositories(basePackages = "com.bilibili.mgk.platform.biz.repositories")
public class ESConfig {

    @Value("${new.es.cluster.nodes}")
    private String clusterNodes;

//    @Bean(name = "restHighLevelClient")
//    public RestHighLevelClient restHighLevelClient() {
//
//        final HttpHost[] httpHosts = parseClusterNodes(clusterNodes);
//
//        final RestClient restClient = RestClient.builder(httpHosts).build();
//
//        return new RestHighLevelClient(restClient);
//    }
//
//    private HttpHost[] parseClusterNodes(String clusterNodes) {
//        return Arrays.stream(clusterNodes.split(",")).map(HttpHost::create).toArray(HttpHost[]::new);
//    }
}
