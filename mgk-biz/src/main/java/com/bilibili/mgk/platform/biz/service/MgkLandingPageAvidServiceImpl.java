package com.bilibili.mgk.platform.biz.service;

import com.bilibili.mgk.platform.api.landing_page.service.IMgkLandingPageAvidService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @file: MgkLandingPageAvidServiceImpl
 * @author: gaoming
 * @date: 2021/12/13
 * @version: 1.0
 * @description:
 **/
@Service
public class MgkLandingPageAvidServiceImpl implements IMgkLandingPageAvidService {

    @Autowired
    private MgkLandingPageAvidServiceDelegate mgkLandingPageAvidServiceDelegate;

    @Override
    public void insert(Long pageId, Long avid) {
        mgkLandingPageAvidServiceDelegate.insert(pageId, avid);
    }

    @Override
    public void insertOrUpdate(Long pageId, Long avid) {
        mgkLandingPageAvidServiceDelegate.insertOrUpdate(pageId, avid);
    }

    @Override
    public void refreshPageIdToAvidMappingInRedis(Long pageId, Long avid) {
        mgkLandingPageAvidServiceDelegate.refreshPageIdToAvidMappingInRedis(pageId, avid);
    }

    @Override
    public void refreshPageIdToAvidMappingInRedis() {
        mgkLandingPageAvidServiceDelegate.refreshPageIdToAvidMappingInRedis();
    }

    @Override
    public void deletedByPageId(Long pageId) {
        mgkLandingPageAvidServiceDelegate.deletedByPageId(pageId);
    }

    @Override
    public Long getAvidByPageId(Long pageId) {
        return mgkLandingPageAvidServiceDelegate.getAvidByPageId(pageId);
    }

    @Override
    public void deletedPageIdToAvidMappingInRedis(Long pageId) {
        mgkLandingPageAvidServiceDelegate.deletedPageIdToAvidMappingInRedis(pageId);
    }

    @Override
    public Long getPageIdToAvidMappingInRedis(Long pageId) {
        return mgkLandingPageAvidServiceDelegate.getPageIdToAvidMappingInRedis(pageId);
    }
}
