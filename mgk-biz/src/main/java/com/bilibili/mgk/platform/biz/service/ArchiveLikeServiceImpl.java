package com.bilibili.mgk.platform.biz.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.mgk.platform.api.archive.dto.MgkArchiveLikeCountDto;
import com.bilibili.mgk.platform.api.archive.dto.MgkArchiveLikeStatsReplyDto;
import com.bilibili.mgk.platform.api.archive.dto.MgkArchiveLikeStatsReqDto;
import com.bilibili.mgk.platform.api.archive.service.IMgkArchiveLikeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @file: ArchiveLikeServiceImpl
 * @author: gaoming
 * @date: 2021/12/14
 * @version: 1.0
 * @description:
 **/
@Service
public class ArchiveLikeServiceImpl implements IMgkArchiveLikeService {

    @Autowired
    private ArchiveLikeServiceDelegate archiveLikeServiceDelegate;

    @Override
    public MgkArchiveLikeCountDto getArchiveLikeCount(Long avid) throws ServiceException {
        return archiveLikeServiceDelegate.getArchiveLikeCount(avid);
    }

    @Override
    public List<MgkArchiveLikeCountDto> getArchivesLikeCount(List<Long> avids) throws ServiceException {
        return archiveLikeServiceDelegate.getArchivesLikeCount(avids);
    }

    @Override
    public MgkArchiveLikeStatsReplyDto archiveLikeStats(MgkArchiveLikeStatsReqDto reqDto) throws ServiceException {
        return archiveLikeServiceDelegate.archiveLikeStats(reqDto);
    }

    @Override
    public MgkArchiveLikeStatsReplyDto getArchiveLikeStats(Long pageId, Long mid, String buvid) throws ServiceException {
        return archiveLikeServiceDelegate.getArchiveLikeStats(pageId, mid, buvid);
    }
}
