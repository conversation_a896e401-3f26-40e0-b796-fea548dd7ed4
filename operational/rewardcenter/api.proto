syntax = "proto3";

import "extension/wdcli/wdcli.proto";
import "google/protobuf/empty.proto";
import "operational/model/rewardcenter/model.proto";

package operational.rewardcenter.v1;

option (wdcli.appid)       = "operational.reward-center.reward-center-service";
option go_package          = "buf.bilibili.co/bapis/bapis-gen/operational/rewardcenter;v1";
option java_multiple_files = true;
option java_package        = "com.bapis.operational.rewardcenter";

//奖励中心，C端接口
service rewardService {
  // 发送包裹奖励
  rpc sendReward (.operational.model.rewardcenter.v1.SendRewardReq) returns (.operational.model.rewardcenter.v1.SendRewardResp);
  // 同步发送包裹奖励(暂未启用)
  rpc sendRewardSync (.operational.model.rewardcenter.v1.SendRewardSyncReq) returns (.operational.model.rewardcenter.v1.SendRewardSyncResp);
  // 奖励（包裹）回收接口
  rpc recycleReward (.operational.model.rewardcenter.v1.CommonRewardReq) returns (.operational.model.rewardcenter.v1.CommonRewardResp);
  // 奖励（包裹）回收接口v2
  rpc recycleRewardV2 (.operational.model.rewardcenter.v1.CommonRewardReqV2) returns (.operational.model.rewardcenter.v1.CommonRewardResp);
  // 奖励（包裹）佩戴接口
  rpc wearReward (.operational.model.rewardcenter.v1.CommonRewardReq) returns (.operational.model.rewardcenter.v1.CommonRewardResp);
  // 奖励（包裹）取消佩戴接口
  rpc cancelWearReward (.operational.model.rewardcenter.v1.CommonRewardReq) returns (.operational.model.rewardcenter.v1.CommonRewardResp);
  // 初始化发奖策略
  rpc initSendReward(.operational.model.rewardcenter.v1.InitSendRewardReq) returns (.operational.model.rewardcenter.v1.InitSendRewardResp);
  // 直发接口
  rpc sendRewardDirect(.operational.model.rewardcenter.v1.SendRewardDirectReq) returns (.operational.model.rewardcenter.v1.SendRewardDirectResp);
  // 获取用户个人中奖列表
  rpc getMyRewardListBySetId(.operational.model.rewardcenter.v1.GetMyRewardListBySetIdReq) returns (.operational.model.rewardcenter.v1.GetMyRewardListBySetIdResp);
  // 发送包裹奖励-v2
  rpc sendRewardV2 (.operational.model.rewardcenter.v1.SendRewardV2Req) returns (.operational.model.rewardcenter.v1.SendRewardResp);
  // 奖励平台清除用户缓存
  rpc rewardsCleanRecordCache(.operational.model.rewardcenter.v1.GetMyRewardListBySetIdReq) returns  (.google.protobuf.Empty);
  // 通过奖励记录ID，查奖励信息
  rpc getRewardRecordById(.operational.model.rewardcenter.v1.RewardRecordByIdReq) returns (.operational.model.rewardcenter.v1.RewardRecordByIdResp);
  // 发送丢失的数据（state = 0），其他的状态发送，请用retry接口
  rpc sendLossSingleRecord(.operational.model.rewardcenter.v1.SendLossSingleRecordReq) returns(.google.protobuf.Empty);

  rpc callBackMallCouponV2(.operational.model.rewardcenter.v1.CallBackMallCouponV2Req) returns(.operational.model.rewardcenter.v1.CallBackMallCouponV2Resp);
  // 批量获取泰山缓存
  rpc batchGetTaishanCacheRecords(.operational.model.rewardcenter.v1.batchGetTaishanCacheRecordsReq) returns (.operational.model.rewardcenter.v1.batchGetTaishanCacheRecordsResp);
  // 手动添加奖励记录到缓存
  rpc manualAddAwardSendRecordToCache(.operational.model.rewardcenter.v1.AwardSendRecord) returns (.google.protobuf.Empty);
  // cny游戏包裹领奖
  rpc CnyGamePackageReceiveAward(.operational.model.rewardcenter.v1.CnyGamePackageReceiveAwardReq) returns(.operational.model.rewardcenter.v1.CnyGamePackageReceiveAwardResp);
}

