// 定义项目 API 的 proto 文件 可以同时描述 gRPC 和 HTTP API
// protobuf 文件参考:
//  - https://developers.google.com/protocol-buffers/
syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..
package operational.model.questions.v1;

option go_package = "buf.bilibili.co/bapis/bapis-gen/operational/model.questions;model";
option java_multiple_files = true;
option java_package = "com.bapis.operational.model.questions";

message QuestionConfig {
  Stem                     stem                          = 1 [(gogoproto.jsontag) = "stem", json_name = "stem"];// 题干内容
  repeated Option          options                       = 2 [(gogoproto.jsontag) = "options", json_name = "options"];//选项
  int64                    question_creator_mid            = 3 [(gogoproto.jsontag) = "question_creator_mid", json_name = "question_creator_mid"];//选项
}

message Stem {
  string                   stem_detail = 1 [(gogoproto.jsontag) = "stem_detail", json_name = "stem_detail"];//题干内容
}

message Option {
  string                   content    = 1 [(gogoproto.jsontag) = "content", json_name = "content"];// 题目内容
  int64                    is_right    = 2 [(gogoproto.jsontag) = "is_right", json_name = "is_right"];//是否正确，目前1为正确，0为错误
  string                   unique_mark    = 3 [(gogoproto.jsontag) = "unique_mark", json_name = "unique_mark"];// 选项唯一id
}



