syntax = "proto3";

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "extension/wdcli/wdcli.proto";
import "operational/model/components/model.proto";
import "operational/model/lottery/model.proto";
import "operational/model/configure/model.proto";


package operational.lotteryservice.v1;

// do not generate getXXX() method
option (wdcli.appid)       = "main.operational.new-lottery-service";
option go_package          = "buf.bilibili.co/bapis/bapis-gen/operational/lotteryservice;v1";
option java_multiple_files = true;
option java_package        = "com.bapis.operational.lotteryservice";

service LotteryAct {
  // 抽奖接口
  rpc LotteryDo(LotteryDoReq) returns (LotteryDoReply);
  // 增加抽奖次数
  rpc AddLotteryTimes(AddTimesReq)  returns (AddTimesReply);
  // Deprecated: 抽奖下线
  rpc LotteryOffline(LotteryOfflineReq) returns (NoReply);
  // Deprecated: 抽奖次数配置获取
  rpc LotteryTimes(LotteryTimesReq) returns (LotteryTimesReply);
  // Deprecated: 抽奖详情
  rpc LotteryDetail(LotteryDetailReq) returns (LotteryDetailReply);
  // Deprecated: 奖品列表 , 请使用 LotteryGiftList 方法
  rpc LotteryGift(LotteryGiftReq) returns (LotteryGiftReply);
  // 获取库存报警相关的抽奖配置及奖品信息
  rpc LotteryStockWarning(LotteryStockWarningReq) returns (LotteryStockWarningReply);
  // Deprecated: 用户组列表
  rpc LotteryMemberGroup(LotteryMemberGroupReq) returns (LotteryMemberGroupReply);
  // Deprecated: 中奖记录导出
  rpc LotteryWinExport(LotteryWinExportReq) returns (NoReply);
  // 奖品列表
  rpc LotteryGiftList(LotteryGiftListReq) returns (LotteryGiftListReply);
  // Deprecated: 方法废弃，请使用 LotteryGetWinList 方法
  rpc LotteryWinList(LotteryWinListReq) returns (LotteryWinListReply);
  // Deprecated: 检查vip套餐id是否有效
  rpc LotteryVipCheck(LotteryVipCheckReq) returns (LotteryVipCheckReply);
  // Deprecated: 用户维度抽奖记录
  rpc LotteryUserRecord(LotteryUserRecordReq) returns (LotteryUserRecordReply);
  // 批量加抽奖次数
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用
  rpc LotteryAddTimesBatch(LotteryAddTimesBatchReq) returns (NoReply);
  // Deprecated: 批量加抽奖次数重试
  rpc LotteryAddTimesBatchRetry(LotteryAddTimesBatchRetryReq) returns (NoReply);
  // Deprecated: 批量增加抽奖次数记录
  rpc LotteryAddTimesBatchLog(LotteryAddTimesBatchLogReq) returns (LotteryAddTimesBatchLogReply);
  // 查询用户剩余抽奖次数
  rpc LotteryUserGetTimes(LotteryUserGetTimesReq) returns (LotteryUserGetTimesReply);
  // 查询用户抽奖次数
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用
  rpc LotteryUserTimesLog(LotteryUserTimesLogReq) returns (LotteryUserTimesLogReply);
  // 查询中奖名单接口
  rpc LotteryGetWinList(LotteryGetWinListReq) returns (LotteryGetWinListReply);
  // 查询抽奖记录接口
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用
  rpc LotteryUserGetList(LotteryUserGetListReq) returns (LotteryUserGetListReply);
  // 获取线上奖池信息
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用
  rpc GetAllOnlineLottery(GetAllOnlineLotteryReq) returns (GetAllOnlineLotteryReply);
  // 抽奖奖池草稿列表
  rpc LotteryActDraftList(LotteryActDraftListReq) returns (LotteryActDraftListReply);
  // 根据线上奖池信息获得奖品
  // Deprecated: 该proto后续废弃使用，请新接入方不要使用
  rpc GetAllOnlineLotteryGift(GetAllOnlineLotteryGiftReq) returns (GetAllOnlineLotteryGiftReply);
  // 仅供内部job调用interface使用, 外部调用请使用新接口RewardsSendAwardV2
  // 奖励平台发放奖励
  rpc RewardsSendAward(RewardsSendAwardReq) returns  (RewardsSendAwardReply);
  // 奖励平台发放奖励
  rpc RewardsSendAwardV2(RewardsSendAwardV2Req) returns  (RewardsSendAwardReply);
  // 奖励平台发放奖励V3
  rpc RewardsSendAwardV3(RewardsSendAwardV3Req) returns  (RewardsSendAwardReply);
  // 奖励平台批量发放奖励V3
  // 目前只支持对单个用户+单个活动下, 批量发放多个奖励. 参数内的mid和活动id必须相同.
  rpc RewardsBatchSendAwardV3(RewardsBatchSendAwardV3Req) returns  (RewardsBatchSendAwardV3Reply);
  // 奖励平台发放奖励检查
  rpc RewardsSendAwardPreCheck(RewardsSendAwardPreCheckReq) returns  (RewardsSendAwardPreCheckReply);
  // 奖励平台发放奖励
  rpc RetryRewardsSendAward(RetryRewardsSendAwardReq) returns  (NoReply);
  // 奖励平台清除用户缓存
  rpc RewardsCleanRecordCache(RewardsCleanRecordCacheReq) returns  (NoReply);
  // 奖励平台：获取用户个人中奖列表
  rpc RewardsGetMyList(RewardsGetMyListReq) returns (RewardsGetMyListReply);
  // 奖励平台添加奖励
  rpc RewardsAddAward(RewardsAddAwardReq) returns (NoReply);
  // 奖励平台删除奖励
  rpc RewardsDelAward(RewardsDelAwardReq) returns (NoReply);
  // 奖励平台修改奖励
  rpc RewardsUpdateAward(RewardsAwardInfo) returns (NoReply);
  // 奖励平台查看奖励
  rpc RewardsListAward(RewardsListAwardReq) returns (RewardsListAwardReply);
  // 奖励平台根据奖品id获取奖品信息
  rpc RewardsGetAwardConfigById(RewardsGetAwardConfigByIdReq) returns (RewardsAwardInfo);
  // 奖励平台根据奖品ids批量获取奖品信息
  rpc RewardsBatchGetAwardConfigByIds(RewardsBatchGetAwardConfigByIdsReq) returns (RewardsBatchGetAwardConfigByIdsReply);
  // 奖励平台根据奖品id获取奖品信息(没有私密信息, 对外展示用)
  rpc RewardsGetAwardInfoById(RewardsGetAwardInfoByIdReq) returns (RewardsAwardInfoWithoutPrivate);
  // 奖励平台根据奖品ids批量获取奖品信息(没有私密信息, 对外展示用)
  rpc RewardsGetAwardInfoByIds(RewardsGetAwardInfoByIdsReq) returns (RewardsGetAwardInfoByIdsReply);
  // 奖励平台添加活动
  rpc RewardsAddActivity(RewardsAddActivityReq) returns (NoReply);
  // 奖励平台删除活动
  rpc RewardsDelActivity(RewardsDelActivityReq) returns (NoReply);
  // 奖励平台修改活动
  rpc RewardsUpdateActivity(RewardsUpdateActivityReq) returns (NoReply);
  // 奖励平台查看活动列表
  rpc RewardsListActivity(RewardsListActivityReq) returns (RewardsListActivityReply);
  // 奖励平台查看活动详情(列出所有活动下的奖品)
  rpc RewardsGetActivityDetail(RewardsGetActivityDetailReq) returns (RewardsGetActivityDetailReply);
  // 奖励平台-根据mid和幂等ID查询发放情况
  rpc RewardsCheckSentStatus(RewardsCheckSentStatusReq) returns (RewardsCheckSentStatusResp);
  // cdkeyV3数量
  rpc RewardsCdkeyV3Count(RewardsCdkeyV3CountReq) returns (RewardsCdkeyV3CountReply);
  // 创建一个奖池草稿（慎用）
  rpc LotteryActDraftAdd(ActDraftAddReq) returns (ActDraftAddReply);

  // 抽奖组件一体化 -- 抽奖
  rpc LotteryXDo(LotteryXDoReq) returns (LotteryXDoReply);
  // 抽奖组件一体化 -- 查询用户剩余抽奖次数
  rpc LotteryXUserGetTimes(LotteryUserGetTimesReq) returns (LotteryXUserGetTimesReply);
  // 抽奖组件一体化 -- 获取奖池奖品列表
  rpc LotteryXGiftList(LotteryGiftListReq) returns (LotteryXGiftListReply);
  // 抽奖组件一体化 -- 查询奖池中奖列表
  rpc LotteryXGetWinList(LotteryGetWinListReq) returns (LotteryXGetWinListReply);
  // 抽奖组件一体化 -- action表异步落库
  rpc AddLotteryXAction(BatchActLotteryActionsAll) returns (NoReply);
  // 抽奖组件一体化 -- 获取抽奖的基础信息
  rpc LotteryXBase(LotteryXBaseReq) returns (LotteryXBaseReply);
  // 抽奖组件一体化 -- 获取所有线上奖池信息 admin使用
  rpc GetAllOnlineLotteryX(GetAllOnlineLotteryXReq) returns (GetAllOnlineLotteryXReply);
  // 抽奖组件一体化 -- 用户维度抽奖记录 admin使用
  rpc LotteryXUserActionRecord(.operational.model.components.v1.CommonListReq) returns (LotteryXUserActionRecordReply);
  // 抽奖组件一体化 -- 用户抽奖记录导出 admin使用
  rpc LotteryXActionRecordExport(LotteryXActionRecordExportReq) returns (NoReply);
  // 抽奖组件一体化 -- 抽奖配置check方法
  rpc NewLotteryConfigureCheck(.operational.model.components.v1.ActConfigureCheckReq)returns(.operational.model.components.v1.ActConfigureCheckReply);
  // 抽奖组件一体化 -- 配置上线方法
  rpc NewLotteryConfigurePublish(.operational.model.components.v1.ActConfigureCheckReq)returns(.operational.model.components.v1.ActConfigureCheckReply);
  // 抽奖组件一体化 -- 创建抽奖配置
  rpc LotteryXConfigureCreat(LotteryXConfigureCreatReq) returns(LotteryXConfigureCreatReply);
  // 抽奖组件一体化 -- 押注组件获取抽奖创建配置
  rpc LotteryXCreatConfForBet(LotteryXCreatConfForBetReq) returns(.operational.model.components.v1.AddDraftVersionReq);
  // 抽奖组件一体化 -- 用户组校验 - 押注组件使用 20230808
  rpc MemberGroupCheck(MemberGroupCheckReq) returns (MemberGroupCheckReply);
  // 抽奖组件一体化 -- 中奖记录解冻
  rpc LotteryXRecordUnfreeze(LotteryXRecordUnfreezeReq) returns (NoReply);
  // 抽奖组件一体化 -- 增加抽奖次数
  rpc LotteryXAddTimes(LotteryXAddTimesReq) returns (NoReply);


  // 奖励组件一体化 -- 奖励配置check方法
  rpc NewRewardsConfigureCheck(.operational.model.components.v1.ActConfigureCheckReq)returns(.operational.model.components.v1.ActConfigureCheckReply);
  // 奖励组件一体化 -- 奖励配置copycheck方法
  rpc NewRewardsConfigureCopyCheck(.operational.model.components.v1.ActConfigureCheckReq)returns(.operational.model.components.v1.ActConfigureCheckReply);

  // 奖励组件一体化 --  奖励配置预检查
  rpc RewardsSendAwardPreCheckByOuterId(RewardsSendAwardPreCheckByOuterIdReq) returns  (RewardsSendAwardPreCheckReply);
  // 奖励组件一体化 --  通过奖品ID查找剩余奖品数量
  rpc RewardsGetAwardStockById(RewardsGetAwardStockByIdReq) returns (RewardsGetAwardStockByIdReply);
  // 奖励组件一体化 --  通过奖品ID批量查找剩余奖品数量(线上配置，非草稿，如未配置库存，则不返回相关数据)
  rpc RewardsBatchGetStockByIds(RewardsBatchGetStockByIdsReq) returns (RewardsBatchGetStockByIdsReply);
  // 奖励组件一体化 --  根据订单号查记录
  rpc RewardsCheckSentStatusByOuterId(RewardsCheckSentStatusByOuterIdReq) returns (RewardsCheckSentStatusByOuterIdReply);
  // 奖励组件一体化 --  发放奖励
  rpc RewardsSendAwardByOuterId(RewardsSendAwardByOuterIdReq) returns  (RewardsSendAwardByOuterIdReply);
  // 奖励组件一体化 --  发放奖励（批量）
  rpc RewardsBatchSendAwardV3ByOuterId(RewardsBatchSendAwardV3ByOuterIdReq) returns  (RewardsBatchSendAwardV3ByOuterIdReply);
  // 奖励组件一体化 --  根据奖品id获取奖品信息(去除后台配置的敏感信息)
  rpc GetAwardInfosByOuterIds(GetAwardConfigsByOuterIdsReq) returns (GetAwardInfosByOuterIdsReply);
  // 奖励组件一体化 --  根据奖品id获取奖品信息
  rpc GetAwardConfigsByOuterIds(GetAwardConfigsByOuterIdsReq) returns (GetAwardConfigsByOuterIdsReply);
  // 奖励组件一体化 --  获取用户个人中奖列表
  rpc RewardsGetMyListByOuterId(RewardsGetMyListByOuterIdReq) returns (RewardsGetMyListReply);
  // 奖励组件一体化 --  通过奖品ID批量查找剩余奖品数量(草稿)
  rpc RewardsBatchGetDraftStockByIds(.operational.model.components.v1.ActConfigureDetailReq) returns (.operational.model.components.v1.ActConfigureDetailReply);
  // 奖励组件一体化 --  通过奖品ID扣减库存
  rpc RewardsConsumerStockByOuterId(.operational.model.lottery.v1.RewardsConsumerStockByOuterIdReq) returns (.operational.model.lottery.v1.RewardsConsumerStockByOuterIdReply);
  // 奖励组件一体化 -- 用户维度奖品发奖记录
  rpc RewardsAwardSendRecord(.operational.model.components.v1.CommonListReq) returns (RewardsAwardSendRecordReply);
  // 奖励组件一体化 -- 奖品维度发奖记录导出接口 -- 通过二维数组形式返回数据
  rpc RewardsAwardSendRecordExport(RewardsAwardSendRecordExportReq) returns (NoReply);
  // 判断是新、旧奖励
  rpc RewardsCheckType(RewardsCheckTypeReq) returns (RewardsCheckTypeReply);
  // 奖励组件一体化 -- 奖励策略效果
  rpc RewardsStrategyEffectSummary( .operational.model.components.v1.ActivityDataResultReq) returns(RewardsStrategyEffectSummaryReply);
  // 奖励组件一体化 -- 奖励策略效果
  rpc RewardsStrategyEffectSingleSummary( .operational.model.components.v1.ActivityDataResultReq) returns(RewardsStrategyEffectSingleSummaryReply);
  // 奖励组件一体化 -- 奖励策略效果
  rpc RewardsStrategyEffectSingleDayDetail( .operational.model.components.v1.ActivityDataResultReq) returns(RewardsStrategyEffectSingleDayDetailReply);
  // 奖励组件一体化 --  发放奖励 by InnerId
  rpc RewardsSendAwardById(RewardsSendAwardByIdReq) returns  (RewardsSendAwardByOuterIdReply);
  //  奖励组件一体化 -- 奖励批量补发
  rpc RetryRewardsSendAwardV2(RetryRewardsSendAwardV2Req) returns  (NoReply);
  // CDKEY 直发接口
  rpc RewardsSendCdkey(RewardsSendAwardByOuterIdReq) returns (RewardsSendCdkeyReply);
  // 用户查看收货地址
  rpc RewardsUserGetAddress(RewardsUserGetAddressReq) returns (AddressInfo);
  // 用户填写收集信息
  rpc RewardsUserAddCollectionInfo(GeneralDummyRewardsCollectionAddReq) returns (NoReply);
  // 用户查看收集信息
  rpc RewardsUserGetCollectionInfo(GeneralDummyRewardsCollectionGetReq) returns (GeneralDummyRewardsCollectionRep);
  // 奖励组件一体化 -- 用户维度奖品发奖记录
  rpc LotteryScoreRecord(.operational.model.components.v1.CommonListReq) returns (LotteryScoreRecordReply);
  // 通过奖品ID获取需导出&删除的cdkey
  rpc GetDelDataByAwardId(GetDelDataByAwardIdReq) returns (GetDelDataByAwardIdReply);
  // 通过奖品ID删除对应的cdkey
  rpc DelDataByAwardId(DelDataByAwardIdReq) returns (NoReply);
  // 通过奖品ID获取taishan cdkey list分页数据
  rpc GetTaishanCdKeyPageList(GetTaishanCdKeyPageListReq) returns (GetTaishanCdKeyPageListReply);
  //奖励物品类型搜索转换为es搜索项
  rpc ConvertRewardConfigureToSearchItems(.operational.model.configure.v1.ConvertConfigureToSearchItemsReq)returns(.operational.model.configure.v1.ConvertConfigureToSearchItemsReply);

  // cny 发放奖励并冻结
  rpc RewardsSendFreezeAwardByOuterId(RewardsSendFreezeAwardByOuterIdReq) returns  (RewardsSendFreezeCashAwardByOuterIdReply);
  // cny 解冻奖励
  rpc UnFreezeAwardByOuterId(UnFreezeAwardByOuterIdReq) returns  (NoReply);
  // cny 直接发放现金奖励
  rpc RewardsSendCashAwardByOuterId(RewardsSendCashAwardByOuterIdReq) returns  (RewardsSendFreezeCashAwardByOuterIdReply);
  // cny 通过UniqueId获取奖励
  rpc GetMyRewardByUniqueId (GetMyRewardByUniqueIdReq) returns (GetMyRewardByUniqueIdReply);

  // cny 批量发放奖励，非现金
  rpc CnyBatchRewardsSendAwardByOuterId(CnyBatchRewardsSendAwardByOuterIdReq) returns  (CnyBatchRewardsSendAwardByOuterIdReply);
    // cny 发放奖励,非现金，支持冻结和不扣库存
    rpc CnyRewardsSendAwardByOuterId(CnyRewardsSendAwardByOuterIdReq) returns  (RewardsSendAwardByOuterIdReply);
    // cny 获取库存
    rpc CnyRewardsGetAwardStockById(CnyRewardsGetAwardStockByIdReq) returns (CnyRewardsGetAwardStockByIdReply);
  // AddActivityRewardAddressSnapshot 用户填写收货地址快照数据存储 (活动维度)
  rpc AddActivityRewardAddressSnapshot(AddAddressSnapshotReq) returns (NoReply);
  // GetActivityRewardAddressSnapshot 获取收货地址快照数据存储 (活动维度)
  rpc GetActivityRewardAddressSnapshot(GetAddressSnapshotReq) returns (AddressInfo);
  // UpdateActivityRewardAddressSnapshot 更新收货地址快照数据存储 (活动维度)
  rpc UpdateActivityRewardAddressSnapshot(AddAddressSnapshotReq) returns (NoReply);
  // GetMemberAddress 查询会员购收货地址
  rpc GetMailAddress(GetMailAddressReq) returns (AddressInfo);
}

message GetMailAddressReq {
  // 用户UID
  int64 Mid                = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = 'validate:"required"'];
  // 会员购收货地址ID
  int64 MailAddressId      = 2 [(gogoproto.jsontag) = "mail_address_id", json_name="mail_address_id", (gogoproto.moretags) = 'form:"mail_address_id" validate:"min=1"'];
}

message GetAddressSnapshotReq {
  // 活动ID，如：1ERA****
  string ActivityId    = 1 [(gogoproto.jsontag) = "activity_id", json_name="activity_id", (gogoproto.moretags) = 'form:"activity_id" validate:"required"'];
  // 用户UID
  int64 Mid            = 2 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = 'validate:"required"'];
}

// EntityRewardsAddressDetailAdd 存储收货地址信息，支持修改：https://www.tapd.cn/60942802/prong/stories/view/1160942802004391043
message EntityRewardsAddressDetail {
  // 活动ID，如：1ERA****
  string ActivityId    = 1 [(gogoproto.jsontag) = "activity_id", json_name="activity_id", (gogoproto.moretags) = 'form:"activity_id" validate:"required"'];
  // 会员购收货地址ID
  int64 AddressId      = 2 [(gogoproto.jsontag) = "address_id", json_name="address_id", (gogoproto.moretags) = 'form:"address_id" validate:"min=1"'];
  // 奖品ID，如：5ERA****
  string AwardOuterSid = 3 [(gogoproto.jsontag) = "award_outer_sid", json_name="award_outer_sid", (gogoproto.moretags) = 'form:"award_outer_sid" validate:"required"'];
  // 收货人姓名
  string Name          = 4 [(gogoproto.jsontag) = "name", json_name="name", (gogoproto.moretags) = 'form:"name" validate:"required"'];
  // 收货人手机号
  string Phone         = 5 [(gogoproto.jsontag) = "phone", json_name="phone", (gogoproto.moretags) = 'form:"phone" validate:"required"'];
  // 省份ID
  int64 ProvId         = 6 [(gogoproto.jsontag) = "prov_id", json_name="prov_id", (gogoproto.moretags) = 'form:"prov_id" validate:"required"'];
  // 省份名称
  string ProvName      = 7 [(gogoproto.jsontag) = "prov_name", json_name="prov_name", (gogoproto.moretags) = 'form:"prov_name" validate:"required"'];
  // 城市ID
  int64 CityId         = 8 [(gogoproto.jsontag) = "city_id", json_name="city_id", (gogoproto.moretags) = 'form:"city_id" validate:"required"'];
  // 城市名称
  string CityName      = 9 [(gogoproto.jsontag) = "city_name", json_name="city_name", (gogoproto.moretags) = 'form:"city_name" validate:"required"'];
  // 区ID
  int64 AreaId         = 10 [(gogoproto.jsontag) = "area_id", json_name="area_id", (gogoproto.moretags) = 'form:"area_id" validate:"required"'];
  // 区名称
  string AreaName      = 11 [(gogoproto.jsontag) = "area_name", json_name="area_name", (gogoproto.moretags) = 'form:"area_name" validate:"required"'];
  // 详细地址,最大长度100（会员购底表长度100）
  string Addr          = 12 [(gogoproto.jsontag) = "addr", json_name="addr", (gogoproto.moretags) = 'form:"addr" validate:"required,max=100"'];
  // 邮编
  string ZipCode       = 13 [(gogoproto.jsontag) = "zip_code", json_name="zip_code", (gogoproto.moretags) = 'form:"zip_code"'];
}

message AddAddressSnapshotReq {
  // 用户UID
  int64 Mid                                = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = 'validate:"required"'];
  // 收货地址详情信息
  EntityRewardsAddressDetail AddressDetail = 2 [(gogoproto.jsontag) = "address_detail", json_name = "address_detail", (gogoproto.moretags) = 'validate:"required"'];
}

message CnyRewardsGetAwardStockByIdReply {
    int64    RemainingStock           = 1 [(gogoproto.jsontag) = "remaining_stock", json_name = "remaining_stock"];
}
message CnyRewardsGetAwardStockByIdReq {
    string AwardOuterId    = 1 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id" , (gogoproto.moretags) = 'form:"award_outer_id" validate:"required"'];
    int64 mid    = 2 [(gogoproto.jsontag) = "mid", json_name = "mid" ];
}
message CnyBatchRewardsSendAwardByOuterIdReq {
   repeated  SendAwardByOuterIdReq  list = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}
message SendAwardByOuterIdReq {
  // 发奖的目标用户id
  int64 Mid            = 1 [(gogoproto.jsontag) = "mid", json_name = "mid" , (gogoproto.moretags) = 'validate:"required"'];
  // 限制长度不超过35个字符，请保持唯一
  string UniqueId      = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'validate:"required"'];
  // 业务类型
  BusinessType  BusinessType = 3 [(gogoproto.jsontag) = "business_type", json_name = "business_type", (gogoproto.moretags) = 'validate:"required"'];
  // 奖励ID，取自新活动后台配置，格式：5ERA****
  string AwardOuterId  = 4 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id" , (gogoproto.moretags) = 'validate:"required"'];
  // 业务id
  string business_id = 5 [(gogoproto.jsontag) = "business_id", json_name = "business_id"];
  // 发奖时间
  int64 msg_time         = 6 [(gogoproto.jsontag) = 'msg_time', (gogoproto.moretags) = 'validate:"required"'];
}
message CnyBatchRewardsSendAwardByOuterIdReply {
  repeated  BatchRewardsSendAwardByOuterIdReply  list = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}
message BatchRewardsSendAwardByOuterIdReply {
  RewardsSendAwardByOuterIdReply Reply  = 1  [(gogoproto.jsontag) = "reply", json_name = "reply"]; //  发奖结果
  bool  IsStockFail             = 2 [(gogoproto.jsontag) = "is_stock_fail", json_name = "is_stock_fail"];  // 是否是库存原因失败
}
message CnyRewardsSendAwardByOuterIdReq {
  // 发奖的目标用户id
  int64 Mid            = 1 [(gogoproto.jsontag) = "mid", json_name = "mid" , (gogoproto.moretags) = 'validate:"required"'];
  // 限制长度不超过35个字符，请保持唯一
  string UniqueId      = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'validate:"required"'];
  // 业务类型
  BusinessType  BusinessType = 3 [(gogoproto.jsontag) = "business_type", json_name = "business_type", (gogoproto.moretags) = 'validate:"required"'];
  // 业务id
  string BusinessId = 4 [(gogoproto.jsontag) = "business_id", json_name = "business_id"];
  // 奖励ID，取自新活动后台配置，格式：5ERA****
  string AwardOuterId  = 5 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id" , (gogoproto.moretags) = 'validate:"required"'];
  // 发奖时间
  int64 MsgTime = 6 [(gogoproto.jsontag) = 'msg_time', json_name = "msg_time", (gogoproto.moretags) = 'validate:"required"'];
  // 中奖列表展示时间 - 奖品冻结发放专用
  int64 ShowTime = 7 [(gogoproto.jsontag) = "show_time", json_name = "show_time"];
  // 是否不扣库存---答题瓜分用
  bool is_not_consumer_stock = 8 [(gogoproto.jsontag) = "is_not_consumer_stock", json_name = "is_not_consumer_stock"];
}

message GetMyRewardByUniqueIdReply {
  // id 分片自增ID,无业务含义
  int64 ID = 1 [(gogoproto.jsontag) = "record_id"];
  // 用户id
  int64 Uid = 2 [(gogoproto.jsontag) = "uid"];
  // 调用来源 https://info.bilibili.co/pages/viewpage.action?pageId=498610112
  int64 SendSource = 3 [(gogoproto.jsontag) = "send_source"];
  // 消息id 长度上限：50， 会被下游当成幂等键，请保持唯一
  string MsgID = 4 [(gogoproto.jsontag) = "msg_id"];
  // 奖励包裹id
  string PackageID = 5 [(gogoproto.jsontag) = "package_id"];
  // 发奖类型 0:奖励中心 1:活动奖励
  int64 SendType = 6 [(gogoproto.jsontag) = "send_type"];
  // 奖品类型,详见枚举值
  int32 AwardType = 7 [(gogoproto.jsontag) = "award_type"];
  // 下游奖品id
  int64 AwardID = 8 [(gogoproto.jsontag) = "award_id"];
  // 奖励名
  string AwardName = 9 [(gogoproto.jsontag) = "award_name"];
  // 奖励图片
  string AwardImg = 10 [(gogoproto.jsontag) = "award_img"];
  // 奖品数量，不存在为0
  int32 Num = 11 [(gogoproto.jsontag) = "num"];
  // 0 初始化 1 开始发放 2 成功 3 失败 4 待绑定
  int32 State = 12 [(gogoproto.jsontag) = "state"];
  // 扩展数据，给下游奖励中心，详见struct定义
  string ExtraJson = 13 [(gogoproto.jsontag) = "extra_json"];
  // 上游传入额外数据，透传使用
  string ExtraData = 14 [(gogoproto.jsontag) = "extra_data"];
  // 业务类型，统计用
  string BusinessType = 15 [(gogoproto.jsontag) = "business_type"];
  // 业务id，统计用
  string BusinessID = 16 [(gogoproto.jsontag) = "business_id"];
  // 消息投递时间(业务指定)
  int64 MsgTime = 17 [(gogoproto.jsontag) = "msg_time"];
  // 奖励初始化发放时间（进入发奖队列）
  int64 SendTime = 18 [(gogoproto.jsontag) = "send_time"];
  // 奖励发放时间（实际调用下游接口）
  int64 ReceiveTime = 19 [(gogoproto.jsontag) = "receive_time"];
  // set_id 比包裹更高一级的集合ID，奖励活动/活动项目/奖励属性配置
  string SetId = 20 [(gogoproto.jsontag) = "set_id"];
  // 奖励ID (外部sid) , 针对使用奖励ID发放的奖品，会携带此信息
  string award_outer_sid = 21 [(gogoproto.jsontag) = 'award_outer_sid'];
  // 冻结奖品-中奖列表展示时间
  int64 show_time = 22 [(gogoproto.jsontag) = 'show_time'];
}
message GetMyRewardByUniqueIdReq {
  // 发奖的目标用户id
  int64 Mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = 'validate:"required"'];
  // 限制长度不超过35个字符，请保持唯一
  string UniqueId = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'validate:"required"'];
  // 活动ID
  string ActivityId = 3 [(gogoproto.jsontag) = "activity_id", json_name = "activity_id", (gogoproto.moretags) = 'validate:"required"'];
}
enum BusinessType {
  BusinessTypeInvalid = 0 ;
  BusinessTypeCnyQuestion = 1; //答题
  BusinessTypeCnyScratchCard = 2; //刮刮乐
  BusinessTypeCnyQuestionLottery = 3; //答题抽奖
  BusinessTypeCnyQuestionLotteryCompensation = 4; //答题抽奖-兜底奖励
}
message RewardsSendCashAwardByOuterIdReq {
  // 发奖的目标用户id
  int64 Mid            = 1 [(gogoproto.jsontag) = "mid", json_name = "mid" , (gogoproto.moretags) = 'validate:"required"'];
  // 限制长度不超过35个字符，请保持唯一
  string UniqueId      = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'validate:"required"'];
  // 业务类型
  BusinessType  BusinessType = 3 [(gogoproto.jsontag) = "business_type", json_name = "business_type", (gogoproto.moretags) = 'validate:"required"'];
  // 奖励ID，取自新活动后台配置，格式：5ERA****
  string AwardOuterId  = 4 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id" , (gogoproto.moretags) = 'validate:"required"'];
  //控制同步/异步发放
  int64 Amount            = 5 [(gogoproto.jsontag) = "amount", json_name = "amount", (gogoproto.moretags) = 'validate:"required"'];
  // 发奖时间
  int64 msg_time         = 6 [(gogoproto.jsontag) = 'msg_time', (gogoproto.moretags) = 'validate:"required"'];
  // 业务id
  string business_id = 7 [(gogoproto.jsontag) = "business_id", json_name = "business_id"];
  // 是否跳过库存拦截 -- 刮刮卡用
  bool is_skip_stock_beak = 8 [(gogoproto.jsontag) = "is_skip_stock_beak", json_name = "is_skip_stock_beak"];
  // 是否不扣库存---瓜分现金用
  bool is_not_consumer_stock = 9 [(gogoproto.jsontag) = "is_not_consumer_stock", json_name = "is_not_consumer_stock"];
}

message UnFreezeAwardByOuterIdReq {
  // 发奖的目标用户id
  int64 Mid            = 1 [(gogoproto.jsontag) = "mid", json_name = "mid" , (gogoproto.moretags) = 'validate:"required"'];
  // 限制长度不超过35个字符，请保持唯一
  string UniqueId      = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'validate:"required"'];
  // 业务类型
  BusinessType  BusinessType = 3 [(gogoproto.jsontag) = "business_type", json_name = "business_type", (gogoproto.moretags) = 'validate:"required"'];
  // 奖励ID，取自新活动后台配置，格式：5ERA****
  string AwardOuterId  = 4 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id" , (gogoproto.moretags) = 'validate:"required"'];
  // 发奖时间
  int64 msg_time         = 6 [(gogoproto.jsontag) = 'msg_time', (gogoproto.moretags) = 'validate:"required"'];

}
message RewardsSendFreezeAwardByOuterIdReq {
  // 发奖的目标用户id
  int64 Mid            = 1 [(gogoproto.jsontag) = "mid", json_name = "mid" , (gogoproto.moretags) = 'validate:"required"'];
  // 限制长度不超过35个字符，请保持唯一
  string UniqueId      = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'validate:"required"'];
  // 业务类型
  BusinessType  BusinessType = 3 [(gogoproto.jsontag) = "business_type", json_name = "business_type", (gogoproto.moretags) = 'validate:"required"'];
  // 奖励ID，取自新活动后台配置，格式：5ERA****
  string AwardOuterId  = 4 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id" , (gogoproto.moretags) = 'validate:"required"'];
  //现金金额
  int64 Amount            = 5 [(gogoproto.jsontag) = "amount", json_name = "amount"];
  // 发奖时间
  int64 msg_time         = 6 [(gogoproto.jsontag) = 'msg_time', (gogoproto.moretags) = 'validate:"required"'];
  // 中奖列表展示时间 - 奖品冻结发放专用
  int64 show_time        = 7 [(gogoproto.jsontag) = "show_time", (gogoproto.moretags) = 'validate:"required"'];
  // 是否是自定义金额红包
  bool is_custom_cash        = 8 [(gogoproto.jsontag) = "is_custom_cash"];
  // 业务id
  string business_id = 9 [(gogoproto.jsontag) = "business_id", json_name = "business_id"];
  // 是否不扣库存---瓜分现金用
  bool is_not_consumer_stock = 10 [(gogoproto.jsontag) = "is_not_consumer_stock", json_name = "is_not_consumer_stock"];
  //   轮次ID
  string sid = 11 [(gogoproto.jsontag) = "sid", json_name = "sid"];
}


message RewardsSendFreezeCashAwardByOuterIdReply {
  int64 Mid              = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  int64  ActivityInnerId = 2 [(gogoproto.jsontag) = "activity_inner_id", json_name = "activity_inner_id"];
  string ActivityOuterId = 3 [(gogoproto.jsontag) = "activity_outer_id", json_name = "activity_outer_id"];
  string ActivityName    = 4 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name"];
  int64  AwardInnerId    = 5 [(gogoproto.jsontag) = "award_inner_id", json_name = "award_inner_id"];
  string AwardOuterId    = 6 [(gogoproto.jsontag) = "award_out_id", json_name = "award_out_id"];
  string AwardType       = 7 [(gogoproto.jsontag) = "award_type", json_name = "award_type"];
  string AwardName       = 8 [(gogoproto.jsontag) = "award_name", json_name = "award_name"];
  string IconUrl         = 9 [(gogoproto.jsontag) = "icon_url", json_name = "icon_url"];
  //奖励介绍信息
  string Description     = 10 [(gogoproto.jsontag) = "description", json_name = "description"];
  map<string, string> ExtraInfo = 11 [(gogoproto.jsontag) = "extra_info", json_name = "extra_info"];
  //是否通过发奖前置条件检查
  bool PreCheckOk             = 12 [(gogoproto.jsontag) = "pre_check_ok", json_name = "pre_check_ok"];
  //发奖前置条件检查信息
  string PreCheckMsg          = 13 [(gogoproto.jsontag) = "pre_check_msg", json_name = "pre_check_msg"];

  //发奖前置条件检查信息
  string PreCheckErrorType    = 14 [(gogoproto.jsontag) = "pre_check_error_type", json_name = "pre_check_error_type"];
  //发奖前置条件检查错误码
  int64 CheckErrorCode         = 15 [(gogoproto.jsontag) = "check_error_code",json_name = "check_error_code"];
  //订单号
  string UniqueId               = 16 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
  //发放状态的拓展字段
  map<string, string> SendExtraInfo = 17 [(gogoproto.jsontag) = "send_extra", json_name = "send_extra"];
  int64 ReceiveTime                 = 18 [(gogoproto.jsontag) = "receive_time", json_name = "receive_time"];
}

message RewardsCheckTypeReq {
  string OuterSid = 1 [(gogoproto.jsontag) = "outer_id", json_name = "outer_id"];
}
message RewardsCheckTypeReply {
  bool IsNew = 1 [(gogoproto.jsontag) = "is_new", json_name = "is_new"];
  AwardConfigItem SingleConf = 2 [(gogoproto.jsontag) = "single_conf", json_name = "single_conf"];
}

message RetryRewardsSendAwardV2Req {
  // 奖励活动ID：1ERAxxxxx
  string AwardOuterSid = 1 [(gogoproto.jsontag) = "award_outer_id", json_name="award_outer_id", (gogoproto.moretags) = 'validate:"required"'];
  // 订单ID
  string UniqueId      = 2 [(gogoproto.jsontag) = "unique_id", json_name="unique_id", (gogoproto.moretags) = 'validate:"required"'];
  // 业务标识
  string Business      = 4 [(gogoproto.jsontag) = "business", json_name = "business", (gogoproto.moretags) = 'validate:"required"'];
  // Mid
  int64 Mid            = 3 [(gogoproto.jsontag) = "mid", json_name="mid",  (gogoproto.moretags) = 'validate:"required"'];
}

message LotteryXRecordUnfreezeReq {
  string lottery_outer_sid = 1 [(gogoproto.jsontag) = "lottery_outer_sid", json_name = "lottery_outer_sid"];
  string award_outer_sid = 2 [(gogoproto.jsontag) = "award_outer_sid", json_name = "award_outer_sid"];
  string bet_outer_sid = 3 [(gogoproto.jsontag) = "bet_outer_sid", json_name = "bet_outer_sid"];
}

message MemberGroupCheckReq {
  // 用户MID
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  // 用户组ID
  repeated string member_group_ids = 2 [(gogoproto.jsontag) = "member_group_ids", json_name = "member_group_ids"];
  repeated string data_flow = 3 [(gogoproto.jsontag) = "data_flow", json_name = "data_flow"];
}

message MemberGroupCheckReply {
  // 用户组名称
  map<string, MemberGroupCheckResult> result = 1 [(gogoproto.jsontag) = "result", json_name = "result"];
}

message MemberGroupCheckResult {
  // 是否校验通过，false表示不通过
  bool check = 1 [(gogoproto.jsontag) = "check", json_name = "check"];
  // 详情
  string result_message = 2 [(gogoproto.jsontag) = "result_message", json_name = "result_message"];
}

message LotteryXConfigureCreatReq {
  string outer_sid = 1 [json_name = "outer_sid", (gogoproto.jsontag) = "outer_sid"];
  .operational.model.components.v1.AddActConfigureReq lottery_conf = 2 [json_name = "lottery_conf", (gogoproto.jsontag) = "lottery_conf"];
}

message LotteryXCreatConfForBetReq {
  repeated GiftConfForBet gifts_conf_for_bet = 1 [json_name = "gifts_conf_for_bet", (gogoproto.jsontag) = "gifts_conf_for_bet"];
  CycleTypeX can_win_time_type =2 [json_name = "can_win_time_type", (gogoproto.jsontag) = "can_win_time_type"];
  int64 can_win_times = 3 [json_name = "can_win_times", (gogoproto.jsontag) = "can_win_times"];
  string outer_sid = 4 [json_name = "outer_sid", (gogoproto.jsontag) = "outer_sid"];
  .operational.model.components.v1.AddActConfigureReq add_act_configure = 5 [json_name = "add_act_configure", (gogoproto.jsontag) = "add_act_configure"];
}

message GiftConfForBet {
  string outer_sid = 1 [json_name = "outer_sid", (gogoproto.jsontag) = "outer_sid"];
  bool is_show =2 [json_name = "is_show", (gogoproto.jsontag) = "is_show"];
  repeated string member_group_ids = 3 [json_name = "member_group_ids", (gogoproto.jsontag) = "member_group_ids"];
  int64 open_time = 4  [json_name = "open_time", (gogoproto.jsontag) = "open_time"];
  bool is_sun = 5  [json_name = "is_sun", (gogoproto.jsontag) = "is_sun"];
  int64  bet_type =6 [json_name = "bet_type", (gogoproto.jsontag) = "bet_type"];
}


message LotteryXConfigureCreatReply {
  string sid    = 1 [json_name = "sid", (gogoproto.jsontag) = "sid"];
}

message RewardsSendCdkeyReply {
  map<string, string> ExtraInfo = 1 [json_name = "extra_info", (gogoproto.jsontag) = "extra_info"];
}

message RewardsSendAwardByIdReq {
  int64 Mid            = 1 [(gogoproto.jsontag) = "mid", json_name = "mid" , (gogoproto.moretags) = 'validate:"required"'];
  string UniqueId      = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
  string Business      = 3 [(gogoproto.jsontag) = "business", json_name = "business"];
  int64  AwardInnerId  = 4 [(gogoproto.jsontag) = "award_inner_id", json_name = "award_inner_id"];
  string AwardOuterId  = 5 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id"];
  //控制同步/异步发放
  bool Sync            = 6 [(gogoproto.jsontag) = "sync", json_name = "sync"];
  //updateDB: 是否更新DB, 可避免消息队列丢失导致丢数据. 只在sync=true时有效
  //updateDB=true: 一致性高,容忍消息丢失
  //updateDB=false: 性能高,需要调用方自身提供额外的对账机制
  bool UpdateDb        = 7 [(gogoproto.jsontag) = "update_db", json_name = "update_db"];
  //updateCache: 是否将此条记录更新到缓存,拜年纪会主动添加缓存所以无需再次更新
  //只在need_send=true时有效
  bool UpdateCache     = 8 [(gogoproto.jsontag) = "update_cache", json_name = "update_cache"];
}

enum LotteryTypeX {
  LotteryTypeXInvalid = 0 ;
  LotteryTypeXIntegral = 1; //积分抽奖
  LotteryTypeXTask = 2; //任务抽奖
}

enum ActionTypeX {
  ActionTypeXNone = 0;
  ActionTypeXBase = 1; // 根据基础次数抽奖
  ActionTypeXTask = 2; // 通过完成任务抽奖
}


enum LotteryStateX {
  LotteryStateXOnline = 0 ;//有效
  LotteryStateXOffline = 1 ;//无效
}

enum LotteryTable {
  LotteryTable0 = 0;
  LotteryTable1 = 1;
  LotteryTable8 = 8;
  LotteryTable32 = 32;
  LotteryTable128 = 128;
}

enum LotteryXCoreModel {
  LotteryXCoreModelDefault = 0;
  LotteryXCoreModelWeight = 1;
}

enum CycleTypeX {
  CycleTypeXNone = 0;
  CycleTypeXDay = 1; // 每天
  CycleTypeXAll = 2; // 整个活动时间内
}

message RewardsStrategyEffectSummaryReply {
  string RewardStockTotal    = 1 [json_name = "reward_stock_total", (gogoproto.jsontag) = "reward_stock_total"];
  string RewardSendTotal    = 2 [json_name = "reward_send_total", (gogoproto.jsontag) = "reward_send_total"];
  string RewardSendRatio    = 3 [json_name = "reward_send_ratio", (gogoproto.jsontag) = "reward_send_ratio"];
}

message RewardsStrategyEffectSingleSummaryReply {
  repeated RewardsStrategyEffectDetail Details = 1 [json_name = "details", (gogoproto.jsontag) = "details"];
  .operational.model.components.v1.Page Page = 2[json_name = "page", (gogoproto.jsontag) = "page"];
}
message RewardsStrategyEffectSingleDayDetailReply {
  repeated .operational.model.components.v1.TwoDimensionalChart List = 1 [json_name = "list", (gogoproto.jsontag) = "list"];
  int64 AwardStock = 2 [json_name = "award_stock", (gogoproto.jsontag) = "award_stock"];
}

message RewardsStrategyEffectDetail {
  string OuterSid    = 1 [ json_name = "outer_sid", (gogoproto.jsontag) = "outer_sid"];
  string Name    = 2 [json_name = "name", (gogoproto.jsontag) = "name"];
  string AwardStock    = 3 [json_name = "award_stock", (gogoproto.jsontag) = "award_stock"];
  string AwardSend    = 4 [json_name = "award_send", (gogoproto.jsontag) = "award_send"];
  string AwardResidue    = 5 [json_name = "award_residue", (gogoproto.jsontag) = "award_residue"];
  string AwardSendRatio    = 6 [json_name = "award_send_ratio", (gogoproto.jsontag) = "award_send_ratio"];
}


message RewardsAwardSendRecordExportReq {
  repeated .operational.model.components.v1.CommonExportParam Params = 1 [json_name = "params", (gogoproto.moretags) = 'validate:"required"'];
  string Username = 2 [json_name = "username", (gogoproto.moretags) = 'validate:"required"'];
}

message RewardsAwardSendRecordReply {
  repeated RewardsAwardSendRecord List  = 1 [json_name = "list", (gogoproto.jsontag) = 'list'];
  Page Page  = 2 [(gogoproto.jsontag) = "page", json_name = "page"];
}

message LotteryScoreRecordReply {
  repeated LotteryScoreRecord List  = 1 [json_name = "list", (gogoproto.jsontag) = 'list'];
  Page Page  = 2 [(gogoproto.jsontag) = "page", json_name = "page"];
}

message LotteryScoreRecord{
  // 抽奖ID
  string Id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  // 抽奖名称
  string Name = 2 [(gogoproto.jsontag) = "name", json_name = "name"];
  // 用户id
  int64 Uid = 3 [(gogoproto.jsontag) = "uid", json_name = "uid"];
  // 时间
  int64 UpdateTime= 4 [(gogoproto.jsontag) = "update_time", json_name = "update_time"];
  // 下发类型
  string UpdateType = 5 [(gogoproto.jsontag) = "update_type", json_name = "update_type"];
  // 数量
  int64 Num = 6 [(gogoproto.jsontag) = "num", json_name = "num"];
  // 操作人
  string Operator =7 [(gogoproto.jsontag) = "operator", json_name = "operator"];
}

message RewardsAwardSendRecord {
  string Sid = 1 [(gogoproto.jsontag) = 'sid', json_name = "sid"];
  string Name = 2 [(gogoproto.jsontag) = 'name', json_name = "name"];
  int64  Mid = 3 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
  int64  Time = 4 [(gogoproto.jsontag) = 'time', json_name = "time"];
  string OrderId = 5 [(gogoproto.jsontag) = 'order_id', json_name = "order_id"];
  string  HasAddress = 6 [(gogoproto.jsontag) = 'has_address', json_name = "has_address"];
  string From = 7 [(gogoproto.jsontag) = 'from', json_name = "from"];
  string StateDetail = 8 [(gogoproto.jsontag) = 'state_detail', json_name = "state_detail"];
  string ErrorMsg = 9 [(gogoproto.jsontag) = 'error_msg', json_name = "error_msg"];
  int64  AwardId = 10 [(gogoproto.jsontag) = 'award_id', json_name = "award_id"];
  int64  State = 11 [(gogoproto.jsontag) = 'state', json_name = "state"];
  int64  SendSource = 12 [(gogoproto.jsontag) = 'send_source', json_name = "send_source"];
  int64  RetryState = 13 [(gogoproto.jsontag) = 'retry_state', json_name = "retry_state"];
  bool   RewardCenterCheck = 14 [(gogoproto.jsontag) = 'reward_center_check', json_name = "reward_center_check"];
  string   HasExtraInfo = 15 [(gogoproto.jsontag) = 'has_extra_info', json_name = "has_extra_info"];
}

message LotteryXDoReq {
  // 抽奖奖池ID（示例：4ERA*****），必选
  string Sid = 1 [(gogoproto.jsontag) = "sid", json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  // 抽奖用户UID ，必选
  int64  Mid = 2 [(gogoproto.jsontag) = "mid" , json_name = "mid" , (gogoproto.moretags) = 'validate:"min=1"'];
  // 抽几次：1：单抽，10：10连抽，必选
  int32  Num = 3 [(gogoproto.jsontag) = "num" , json_name = "num" , (gogoproto.moretags) = 'validate:"min=1,max=10"'];
  // 抽奖行为订单号（用于超时重试时的全局幂等）：限制长度不超过35个字符，并且请保持全局唯一
  string OrderNo = 4 [(gogoproto.jsontag) = "order_no", json_name = "order_no"];
  // 风控参数相关，详见（或咨询@琛琛）：风控介绍:http://gaia-risk.bilibili.co
  RiskBaseInfo  Risk = 5[(gogoproto.jsontag) = "risk", json_name = "risk" , (gogoproto.moretags) = 'validate:"required"'];
  // 抽奖来源。某些奖池不对外，为了防止被C端爬取，需要根据from定向拦截。(默认不需要，如需启用，请先咨询@活动平台姬)。示例：lottery-x-http，lottery-http
  string From = 6 [(gogoproto.jsontag) = "from", json_name = "from"];
  // DataFlow 数据流(研发专用)
  repeated string DataFlow = 7 [(gogoproto.jsontag) = "data_flow", json_name = "data_flow"];
  // 抽奖的活动页ID，用户BI数据分析使用，非必选。目前只接受活动平台的页面ID，示例：3ERAmwloghv0bn00
  string PageId = 8 [(gogoproto.jsontag) = "page_id", json_name = "page_id"];
  // 通过用户组优先过滤奖池(默认不需要开启，如需启用，请先咨询@活动平台姬)
  bool MemberGroupFilterPriority = 9 [(gogoproto.jsontag) = "member_group_filter_priority", json_name = "member_group_filter_priority"];
  // 押注展示时间(默认不需要开启，如需启用，请先咨询@活动平台姬)
  int64 ShowTime=10[(gogoproto.jsontag) = "show_time", json_name = "show_time"];
  //是否被风控
  bool HasRisk = 11[(gogoproto.jsontag) = "has_risk", json_name = "has_risk"];
  // 抽奖来源
  string Source = 12 [(gogoproto.jsontag) = "source", json_name = "source"];
}


message LotteryXDoReply {
  repeated RecordDetailX Rewards  = 1 [(gogoproto.jsontag) = "rewards" ,json_name = "rewards"];
}

message RecordDetailX {
  int64  Mid = 1 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
  string  AwardSid = 2 [(gogoproto.jsontag) = 'award_sid', json_name = "award_sid"];
  ActionTypeX  Type = 3 [(gogoproto.jsontag) = 'type', json_name = "type"];
  int64  Ctime = 4 [(gogoproto.jsontag) = 'ctime', json_name = "ctime" , (gogoproto.casttype) = "go-common/library/time.Time" ];
  RewardsAwardInfoWithoutPrivate AwardInfo = 5 [(gogoproto.jsontag) = 'award_info', json_name = "award_info"];
  string OrderNo = 6 [(gogoproto.jsontag) = 'order_no', json_name = "order_no"];
}

message Integral {
  // 数据源id
  string Sid = 1 [(gogoproto.jsontag) = "sid", json_name="sid"];
  // 名称
  string Name = 2 [(gogoproto.jsontag) = "name", json_name="name"];
  // 图片
  string Icon = 3 [(gogoproto.jsontag) = "icon", json_name="icon"];
}

message LotteryXUserGetTimesReply {
  // 剩余抽奖次数
  int64 Times = 1 [(gogoproto.jsontag) = "times", json_name="times"];
  // 抽奖类型
  LotteryTypeX LotteryType = 2 [(gogoproto.jsontag) = "lottery_type", json_name="lottery_type"];
  // 剩余积分
  int64 Points = 3 [(gogoproto.jsontag) = "points", json_name="points"];
  // 每次抽奖消耗的积分，任务抽奖下=0
  int64 PointsPerTime = 4 [(gogoproto.jsontag) = "points_per_time", json_name="points_per_time"];
  // 积分数据源信息
  Integral Integral = 5 [(gogoproto.jsontag) = "intergral", json_name="intergral"];
}

message LotteryXGiftListReply {
  repeated LotteryXGiftListItem List = 1 [(gogoproto.jsontag) = 'list', json_name = "list"];
}

message LotteryXGiftListItem {
  string AwardSid = 1 [(gogoproto.jsontag) = 'sid', json_name = "sid"];
  RewardsAwardInfoWithoutPrivate AwardInfo = 2 [(gogoproto.jsontag) = "award_info", json_name="award_info"];
}

message LotteryXWinListItem {
  string Name = 1 [(gogoproto.jsontag) = "name" ,json_name = "name"];
  int64 Mid = 2 [(gogoproto.jsontag) = "mid" ,json_name = "mid"];
  string AwardSid = 3 [(gogoproto.jsontag) = 'sid', json_name = "sid"];
  int64 Ctime = 4 [(gogoproto.jsontag) = "ctime" ,json_name = "ctime" , (gogoproto.casttype) = "go-common/library/time.Time"];
  RewardsAwardInfoWithoutPrivate AwardInfo = 5 [(gogoproto.jsontag) = "award_info", json_name="award_info"];
}

message LotteryXGetWinListReply {
  repeated LotteryXWinListItem List  = 1 [(gogoproto.jsontag) = "list" ,json_name = "list"];
}

message BatchActLotteryActionsAll {
  int64 LotteryId = 1 [(gogoproto.jsontag) = 'lottery_id', json_name = "lottery_id"];
  string Sid = 2 [(gogoproto.jsontag) = 'sid', json_name = "sid"];
  int64 Tables = 3 [(gogoproto.jsontag) = 'tables', json_name = "tables"];
  repeated ActLotteryActionRecord ActionList = 4 [(gogoproto.jsontag) = 'action_list', json_name = "action_list"];
  repeated ActLotteryXActionRecord ActionListX = 5 [(gogoproto.jsontag) = 'action_list_x', json_name = "action_list_x"];
}

message ActLotteryXActionRecord {
  int64  ID  = 1 [(gogoproto.jsontag) = 'id', json_name = "id"];
  string Sid = 2 [(gogoproto.jsontag) = 'sid', json_name = "sid"];
  int64  Mid = 3 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
  string IP  = 4 [(gogoproto.jsontag) = 'ip', json_name = "ip"];
  int32  state = 5 [(gogoproto.jsontag) = 'state', json_name = "state"];
  string OrderNo = 6 [(gogoproto.jsontag) = 'order_no', json_name = "order_no"];
  ActionTypeX  ActionType = 7[(gogoproto.jsontag) = 'action_type', json_name = "action_type"];
  string Ctime = 8 [(gogoproto.jsontag) = 'ctime', json_name = "ctime" ];
  string Mtime = 9 [(gogoproto.jsontag) = 'mtime', json_name = "mtime" ];
  string UniqGiftNo = 10 [(gogoproto.jsontag) = 'uniq_gift_no', json_name = "uniq_gift_no"];
  string  AwardSid  = 11 [(gogoproto.jsontag) = 'award_sid', json_name = "award_sid"];
  string Extra    = 12 [(gogoproto.jsontag) = 'extra', json_name = "extra"];
}

message LotteryXBaseReq {
  string SID = 1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
}

message LotteryXBaseReply {
  BaseX Base   = 1 [(gogoproto.jsontag)  = "base",json_name = "base"];
}

message BaseX {
  int64 InnerId = 1  [(gogoproto.jsontag)  = "id",json_name = "id"];
  string LotteryID = 2  [(gogoproto.jsontag)  = "lottery_id",json_name = "lottery_id"];
  string ActivitySid = 3  [(gogoproto.jsontag)  = "activity_sid",json_name = "activity_sid"];
  int64 ActivityInnerId = 4  [(gogoproto.jsontag)  = "activity_id",json_name = "activity_id"];
  string Author = 5  [(gogoproto.jsontag)  = "author",json_name = "author"];
  LotteryTypeX LotteryType = 6  [(gogoproto.jsontag)  = "lottery_type",json_name = "lottery_type"];
  LotteryStateX State = 7  [(gogoproto.jsontag)  = "state",json_name = "state"];
  string Name = 8  [(gogoproto.jsontag)  = "name",json_name = "name"];
  string IntegralId = 9  [(gogoproto.jsontag)  = "integral_id",json_name = "integral_id"];
  int64 PointsPerTime = 10  [(gogoproto.jsontag)  = "points_per_time",json_name = "points_per_time"];
  LotteryTable Tables = 11  [(gogoproto.jsontag)  = "tables",json_name = "tables"];
  int64 Stime = 12  [(gogoproto.jsontag)  = "stime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "stime"];
  int64 Etime = 13  [(gogoproto.jsontag)  = "etime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "etime"];
  int64 Ctime = 14  [(gogoproto.jsontag)  = "ctime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "ctime"];
  int64 Mtime = 15  [(gogoproto.jsontag)  = "mtime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "mtime"];
}

message GetAllOnlineLotteryXReq{
  int64 Pn = 1 [json_name = "pn", (gogoproto.moretags) = 'default:"1"'];
  int64 Ps = 2 [json_name = "ps", (gogoproto.moretags) = 'default:"100"'];
}

message GetAllOnlineLotteryXReply{
  repeated BaseX List  = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
  .operational.model.components.v1.Page Page = 2 [(gogoproto.jsontag) = 'page', json_name = "page"];
}


message RewardsSendAwardPreCheckByOuterIdReq {
  //发放的Mid
  int64 Mid              = 1 [(gogoproto.jsontag) = "mid", json_name = "mid",(gogoproto.moretags) = 'validate:"required"'];
  //幂等id, 使用相同的幂等id发放奖励会返回code=(75971) 活动奖励已发放~
  string UniqueId        = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
  //业务标识
  string Business        = 3 [(gogoproto.jsontag) = "business", json_name = "business"];
  //奖励id
  string AwardOuterId    = 4 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id",(gogoproto.moretags) = 'validate:"required"'];
  //是否跳过风控检查
  bool SkipRiskCheck     = 5 [(gogoproto.jsontag) = "skip_risk_check", json_name = "skip_risk_check"];
  //自定义风控Ctx
  bytes CustomRiskCtx    = 6 [(gogoproto.jsontag) = "custom_risk_ctx", json_name = "custom_risk_ctx"];
}


message RewardsGetAwardStockByIdReq {
  string AwardOuterId    = 1 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id" , (gogoproto.moretags) = 'form:"award_outer_id" validate:"required"'];
  int64  Mid             = 2 [(gogoproto.jsontag) = "mid", json_name = "mid" , (gogoproto.moretags) = 'form:"mid" '];
}


enum StockCycleType {
  StockCycleTypeNone = 0;      // 无效类型
  StockCycleTypeDay  = 1;      // 每日库存
  StockCycleTypeAct  = 2;      // 活动期间库存
}

enum StockLimitType {
  StockLimitTypeNone        = 0;      // 无效类型
  StockLimitTypeTotal       = 1;      // 奖品总发放限制
  StockLimitTypeSingleUser  = 2;      // 单用户发放限制
}

message StockItem {
  StockCycleType CycleType    = 1 [(gogoproto.jsontag) = "cycle_type", json_name = "cycle_type"];
  StockLimitType LimitType    = 2 [(gogoproto.jsontag) = "limit_type", json_name = "limit_type"];
  int64          Limit        = 3 [(gogoproto.jsontag) = "limit", json_name = "limit"];
  int64          SendNum      = 4 [(gogoproto.jsontag) = "send_num", json_name = "send_num"];
  int64          AvailableNum = 5 [(gogoproto.jsontag) = "available_num", json_name = "available_num"];
}

message RewardsGetAwardStockByIdReply {
  repeated  StockItem StockInfo  = 1 [(gogoproto.jsontag) = "stock_info", json_name = "stock_info"];
}

message RewardsCheckSentStatusByOuterIdReq {
  int64  Mid             = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"'];
  string UniqueId        = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'form:"unique_id" validate:"required"'];
  string AwardOuterId    = 3 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id"];
}


message RewardsCheckSentStatusByOuterIdReply {
  bool Result = 1 [(gogoproto.jsontag) = "result", json_name = "result"];
}


message RewardsSendAwardByOuterIdReq {
  // 发奖的目标用户id
  int64 Mid            = 1 [(gogoproto.jsontag) = "mid", json_name = "mid" , (gogoproto.moretags) = 'validate:"required"'];
  // 限制长度不超过35个字符，请保持唯一
  string UniqueId      = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
  // 业务类型
  string Business      = 3 [(gogoproto.jsontag) = "business", json_name = "business"];
  // 奖励ID，取自新活动后台配置，格式：5ERA****
  string AwardOuterId  = 4 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id" , (gogoproto.moretags) = 'validate:"required"'];
  //控制同步/异步发放
  bool Sync            = 5 [(gogoproto.jsontag) = "sync", json_name = "sync"];
  //updateDB: 是否更新DB, 可避免消息队列丢失导致丢数据. 只在sync=true时有效
  //updateDB=true: 一致性高,容忍消息丢失
  //updateDB=false: 性能高,需要调用方自身提供额外的对账机制
  bool UpdateDb   = 6 [(gogoproto.jsontag) = "update_db", json_name = "update_db"];
  //updateCache: 是否将此条记录更新到缓存,拜年纪会主动添加缓存所以无需再次更新
  //只在need_send=true时有效
  bool UpdateCache = 7 [(gogoproto.jsontag) = "update_cache", json_name = "update_cache"];
  // Deprecated
  AwardConfigType  ConfVersion = 8 [(gogoproto.jsontag) = "conf_version", json_name = "conf_version"];
  // 不发私信，默认：false 发送私信；true 关闭私信通道
  bool DoNotSendMsg    = 9 [(gogoproto.jsontag) = "do_not_send_msg", json_name = "do_not_send_msg"];
  // 上游传递下来的操作时间
  int64 OperateTime = 10 [(gogoproto.jsontag) = "operate_time", (gogoproto.casttype) = "go-common/library/time.Time", json_name = "operate_time", (gogoproto.moretags) = 'form:"operate_time"'];
}


message RewardsSendAwardByOuterIdReply {
  int64 Mid              = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  int64  ActivityInnerId = 2 [(gogoproto.jsontag) = "activity_inner_id", json_name = "activity_inner_id"];
  string ActivityOuterId = 3 [(gogoproto.jsontag) = "activity_outer_id", json_name = "activity_outer_id"];
  string ActivityName    = 4 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name"];
  int64  AwardInnerId    = 5 [(gogoproto.jsontag) = "award_inner_id", json_name = "award_inner_id"];
  string AwardOuterId    = 6 [(gogoproto.jsontag) = "award_out_id", json_name = "award_out_id"];
  string AwardType       = 7 [(gogoproto.jsontag) = "award_type", json_name = "award_type"];
  string AwardName       = 8 [(gogoproto.jsontag) = "award_name", json_name = "award_name"];
  string IconUrl         = 9 [(gogoproto.jsontag) = "icon_url", json_name = "icon_url"];
  //奖励介绍信息
  string Description     = 10 [(gogoproto.jsontag) = "description", json_name = "description"];
  map<string, string> ExtraInfo = 11 [(gogoproto.jsontag) = "extra_info", json_name = "extra_info"];
  //是否通过发奖前置条件检查
  bool PreCheckOk             = 12 [(gogoproto.jsontag) = "pre_check_ok", json_name = "pre_check_ok"];
  //发奖前置条件检查信息
  string PreCheckMsg          = 13 [(gogoproto.jsontag) = "pre_check_msg", json_name = "pre_check_msg"];

  //发奖前置条件检查信息
  string PreCheckErrorType    = 14 [(gogoproto.jsontag) = "pre_check_error_type", json_name = "pre_check_error_type"];
  //发奖前置条件检查错误码
  int64 CheckErrorCode         = 15 [(gogoproto.jsontag) = "check_error_code",json_name = "check_error_code"];
  //订单号
  string UniqueId               = 16 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
  //发放状态的拓展字段
  map<string, string> SendExtraInfo = 17 [(gogoproto.jsontag) = "send_extra", json_name = "send_extra"];
  int64 ReceiveTime                 = 18 [(gogoproto.jsontag) = "receive_time", json_name = "receive_time"];
}


message RewardsSendAwardV3ByOuterIdReq {
  //发放的Mid
  int64 Mid              = 1 [(gogoproto.jsontag) = "mid", json_name = "mid",(gogoproto.moretags) = 'validate:"required"'];
  //幂等id, 使用相同的幂等id发放奖励会返回code=(75971) 活动奖励已发放~
  string UniqueId        = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id",(gogoproto.moretags) = 'validate:"required,max=32"'];
  //业务标识
  string Business        = 3 [(gogoproto.jsontag) = "business", json_name = "business",(gogoproto.moretags) = 'validate:"required,max=10"'];
  //奖励id
  string AwardOuterId    = 4 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id",(gogoproto.moretags) = 'validate:"required"'];
  // 冻结发放
  bool freeze_send     = 5 [(gogoproto.jsontag) = "freeze_send", json_name = "freeze_send"];
  // 中奖列表展示时间 - 奖品冻结发放专用
  int64 show_time     = 6 [(gogoproto.jsontag) = "show_time"];
}

message RewardsBatchSendAwardV3ByOuterIdReq {
  repeated RewardsSendAwardV3ByOuterIdReq List = 1 [(gogoproto.jsontag) = "list", json_name = "list", (gogoproto.moretags) = 'validate:"max=100,min=1"'];
}

message RewardsBatchSendAwardV3ByOuterIdItem {
  RewardsSendAwardByOuterIdReply Result = 1 [(gogoproto.jsontag) = "result", json_name = "result"];
  int32 ErrorCode = 2 [(gogoproto.jsontag) = "error_code", json_name = "error_code"];
  string ErrorMsg = 3 [(gogoproto.jsontag) = "error_msg", json_name = "error_msg"];
}

message RewardsBatchSendAwardV3ByOuterIdReply {
  //key为unique_id, value为本次发放的结果
  map<string, RewardsBatchSendAwardV3ByOuterIdItem> ResMap = 1 [(gogoproto.jsontag) = "res_map", json_name = "res_map"];
}


enum AwardConfigType {
  ConfigVersionTypeNone  = 0;      // 无效类型
  ConfigVersionTypeDraft = 1;      // 草稿环境配置
  ConfigVersionTypePre   = 2;      // 预览环境配置
  ConfigVersionTypeProd  = 3;      // 生产环境配置
}

message GetAwardConfigsByOuterIdsReq {
  repeated string AwardOuterIds = 1 [(gogoproto.jsontag) = "award_outer_ids", json_name = "award_outer_ids", (gogoproto.moretags) = 'form:"award_outer_ids" validate:"min=1,max=100"'];
  AwardConfigType  ConfVersion  = 2 [(gogoproto.jsontag) = "conf_version", json_name = "conf_version" , (gogoproto.moretags) = 'form:"conf_version"'];
}

message GetAwardConfigsByOuterIdsReply {
  repeated AwardConfigItem List = 1 [(gogoproto.jsontag)  = "list", json_name = "list"];
}

enum AwardStockLimitType {
  AwardStockLimitTypeNone      = 0;
  AwardStockLimitTypeLimited   = 1;   // 限量
  AwardStockLimitTypeNoLimited = 2;   // 不限量
}

message AwardConfigItem {
  int64   AwardInnerId   = 1 [(gogoproto.jsontag) = "award_inner_id", json_name = "award_inner_id"];
  string  AwardOuterId   = 2 [(gogoproto.jsontag) = "award_out_id", json_name = "award_out_id"];
  int64  ActivityInnerId = 3 [(gogoproto.jsontag) = "activity_inner_id", json_name = "activity_inner_id"];
  string ActivityOuterId = 4 [(gogoproto.jsontag) = "activity_outer_id", json_name = "activity_outer_id"];
  string ActivityName    = 5 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name"];
  string AwardType       = 6 [(gogoproto.jsontag) = "award_type", json_name = "award_type"];
  string AwardName       = 7 [(gogoproto.jsontag) = "award_name", json_name = "award_name"];
  string IconUrl         = 8 [(gogoproto.jsontag) = "icon", json_name = "icon"];
  // 奖励介绍信息
  string description     = 9 [(gogoproto.jsontag) = "description", json_name = "description"];
  // 奖品库存限制类型
  AwardStockLimitType StockLimitType   = 10 [(gogoproto.jsontag) = "stock_limit_type", json_name = "stock_limit_type"];
  // 奖品库存数量
  int64 StockNumTotal    = 11 [(gogoproto.jsontag) = "stock_num_total", json_name = "stock_num_total"];
  // 是否需要绑定手机
  bool NeedBindPhone     = 12 [(gogoproto.jsontag) = "need_bind_phone", json_name = "need_bind_phone"];
  // 库存告警的用户，多个用户以逗号隔开
  string Warner          = 13 [(gogoproto.jsontag) = "warner", json_name = "warner"];
  // 库存告警数量
  int64 WarningNum      = 14 [(gogoproto.jsontag) = "warning_num", json_name = "warning_num"];
  // 中奖通知发送号码
  int64 NotifySenderId  = 15 [(gogoproto.jsontag) = "notify_sender_id", json_name = "notify_sender_id"];
  // 中奖通知跳转链接
  string NotifyJumpUrl   = 16 [(gogoproto.jsontag) = "notify_jump_url", json_name = "notify_jump_url"];
  // 奖品总发放限制:  1：每日、 2：活动期间
  StockCycleType AwardLimitType   = 17 [(gogoproto.jsontag) = "award_limit_type", json_name = "award_limit_type"];
  // 奖品发放上限
  int64 AwardLimitNum    = 18 [(gogoproto.jsontag) = "award_limit_num", json_name = "award_limit_num"];

  // 用户发放限制:  1：每日、 2：活动期间
  StockCycleType UserLimitType   = 19 [(gogoproto.jsontag) = "user_limit_type", json_name = "user_limit_type"];
  // 用户发放上限
  int64 UserLimitNum    = 20 [(gogoproto.jsontag) = "user_limit_num", json_name = "user_limit_num"];

  bool CheckPassed       = 21[(gogoproto.jsontag) = "pre_check_ok", json_name = "pre_check_ok"];
  // ExtraInfo
  map<string, string> ExtraInfo  = 22 [(gogoproto.jsontag)  = "extra", json_name = "extra"];
  string JsonStr   = 23 [(gogoproto.jsontag) = "json_str", json_name = "json_str"];
  // GroupName 页面ID
  string GroupName = 24 [(gogoproto.jsontag) = "group_name", json_name = "group_name"];
  //奖励标签
  string RewardTag=25[(gogoproto.jsontag) = "reward_tag", json_name = "reward_tag"];
  //备注名称
  string RemarksName = 26 [(gogoproto.jsontag) = "remarks_name", json_name = "remarks_name"];
  //白名单人群包
  string WhiteMemberId = 27 [(gogoproto.jsontag) = "white_member_id", json_name = "white_member_id"];
  //黑名单人群包
  string BlackMemberId = 28 [(gogoproto.jsontag) = "black_member_id", json_name = "black_member_id"];
  //每日领奖开始时间
  int64 EveryDayPrizeStime = 29 [(gogoproto.jsontag) = "everyday_prize_stime", json_name = "everyday_prize_stime"];
  //每日领奖结束时间
  int64 EveryDayPrizeEtime = 30 [(gogoproto.jsontag) = "everyday_prize_etime", json_name = "everyday_prize_etime"];
  //领奖截止时间
  int64 ReceivePrizeDDL = 31 [(gogoproto.jsontag) = "receive_prize_ddl", json_name = "receive_prize_ddl"];
  //领奖页强提示
  string StrongHint = 32 [(gogoproto.jsontag) = "strong_hint", json_name = "strong_hint"];
  // 库存提醒修改为开关模式
  bool StockWarningSwitch    = 33 [(gogoproto.jsontag) = "stock_warning_switch", json_name = "stock_warning_switch"];
  // 消耗进度通知开关,打开后每日定时通知发放进度
  bool ConsumeNotifySwitch   = 34 [(gogoproto.jsontag) = "consume_notify_switch", json_name = "consume_notify_switch"];
  // 奖品展示类型:主要用来区分不同迭代版本，奖品展示的样式,如果是多个类型，用逗号分隔
  string  AwardDisplayTypes  = 35 [(gogoproto.jsontag) = "award_display_types", json_name = "award_display_types"];
  // 奖品当前的状态
  int32   AwardState         = 36 [(gogoproto.jsontag) = "award_state", json_name = "award_state"];
  int64  Ctime = 37 [(gogoproto.jsontag) = 'ctime', json_name = "ctime" , (gogoproto.casttype) = "go-common/library/time.Time" ];
}


message GetAwardInfosByOuterIdsReply {
  repeated GetAwardInfosByOuterIdsItem List = 1 [(gogoproto.jsontag) = "list", json_name = "list", (gogoproto.moretags) = 'validate:"max=100,min=1"'];
}


message GetAwardInfosByOuterIdsItem {
  int64   AwardInnerId   = 1 [(gogoproto.jsontag) = "award_inner_id", json_name = "award_inner_id"];
  string  AwardOuterId   = 2 [(gogoproto.jsontag) = "award_out_id", json_name = "award_out_id"];
  int64  ActivityInnerId = 3 [(gogoproto.jsontag) = "activity_inner_id", json_name = "activity_inner_id"];
  string ActivityOuterId = 4 [(gogoproto.jsontag) = "activity_outer_id", json_name = "activity_outer_id"];
  string ActivityName    = 5 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name"];
  string Type            = 6 [(gogoproto.jsontag) = "type", json_name = "type", (gogoproto.moretags) = 'form:"type" validate:"required"'];
  string Name            = 7 [(gogoproto.jsontag) = "name", json_name = "name", (gogoproto.moretags) = 'form:"name" validate:"required"'];
  string IconUrl         = 8 [(gogoproto.jsontag) = "icon", json_name = "icon"];
  //奖励介绍信息
  string description     = 9 [(gogoproto.jsontag) = "description", json_name = "description", (gogoproto.moretags) = 'form:"description"'];
  bool CheckPassed       = 10 [(gogoproto.jsontag) = "pre_check_ok", json_name = "pre_check_ok"];
  // ExtraInfo
  map<string, string> ExtraInfo  = 11 [(gogoproto.jsontag)  = "extra", json_name = "extra"];
  //备注名称
  string RemarksName = 12 [(gogoproto.jsontag) = "remarks_name", json_name = "remarks_name"];
  //白名单人群包
  string WhiteMemberId = 13 [(gogoproto.jsontag) = "white_member_id", json_name = "white_member_id"];
  //黑名单人群包
  string BlackMemberId = 14 [(gogoproto.jsontag) = "black_member_id", json_name = "black_member_id"];
  //每日领奖开始时间
  int64 EveryDayPrizeStime = 15 [(gogoproto.jsontag) = "everyday_prize_stime", json_name = "everyday_prize_stime"];
  //每日领奖结束时间
  int64 EveryDayPrizeEtime = 16 [(gogoproto.jsontag) = "everyday_prize_etime", json_name = "everyday_prize_etime"];
  //领奖截止时间
  int64 ReceivePrizeDDL = 17 [(gogoproto.jsontag) = "receive_prize_ddl", json_name = "receive_prize_ddl"];
  //领奖页强提示
  string StrongHint = 18 [(gogoproto.jsontag) = "strong_hint", json_name = "strong_hint"];
}


message RewardsGetMyListByOuterIdReq {
  int64 Mid                      = 1 [(gogoproto.jsontag) = "mid",  json_name = "mid" , (gogoproto.moretags) = 'form:"mid" validate:"required"'];
  repeated string ActivityOutIds = 2 [(gogoproto.jsontag) = "activity_out_ids",  json_name = "activity_out_ids" , (gogoproto.moretags) = 'form:"activity_out_ids" validate:"required"'];
  bool IfNeedCollectCard = 3 [(gogoproto.jsontag) = "if_need_collect_card", json_name = "if_need_collect_card"];
}


message ActDraftAddReq {
  string Name = 1 [(gogoproto.jsontag)  = "name", json_name = "name", (gogoproto.moretags) = 'form:"name" validate:"required"'];
  // 活动开始时间
  int64 Stime = 2 [(gogoproto.jsontag)  = "stime", (gogoproto.casttype) = "go-common/library/time.Time", json_name = "stime",(gogoproto.moretags) = 'form:"stime" validate:"required"'];
  // 活动结束时间
  int64 Etime = 3 [(gogoproto.jsontag)  = "etime", (gogoproto.casttype) = "go-common/library/time.Time", json_name = "etime",(gogoproto.moretags) = 'form:"etime" validate:"required"'];
  LotteryType Type = 4 [(gogoproto.jsontag)  = "type", json_name = "type"];
  string Author = 5 [(gogoproto.jsontag) = "author",(gogoproto.moretags) = 'form:"author"  validate:"required"', json_name = "author"];
  int64 AwardActivityID = 6 [(gogoproto.jsontag) = "award_activity_id",(gogoproto.moretags) = 'form:"award_activity_id"  validate:"required"', json_name = "award_activity_id"];
}

message ActDraftAddReply {
  int64 ID = 1 [(gogoproto.jsontag) = 'id', json_name = "id"];
  string LotteryID = 2 [(gogoproto.jsontag) = 'lottery_id', json_name = "lottery_id"];
}


message LotteryActDraftListReq {
  DraftListReqState State = 1 [(gogoproto.jsontag) = "state", json_name = "state", (gogoproto.moretags) = 'form:"state"'];;
  string Keyword = 2 [json_name = "keyword",  (gogoproto.moretags) = 'form:"keyword"'];
  int64 Pn = 3 [json_name = "pn" ,(gogoproto.moretags) = 'form:"pn" default:"1"'];
  int64 Ps = 4 [json_name = "ps" , (gogoproto.moretags) = 'form:"ps" default:"20"'];
  string Rank = 5 [json_name = "rank" ,(gogoproto.moretags) = 'form:"rank"'];
  string Operator = 6 [json_name = "operator" ,(gogoproto.moretags) = 'form:"operator"'];
}

message LotteryActDraftListReply {
  repeated LotteryDraft List  = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
  Page Page  = 2 [(gogoproto.jsontag) = "page", json_name = "page"];
}

message LotteryDraft {
  int64 ID = 1  [(gogoproto.jsontag) = "id",json_name = "id"];
  string LotteryID = 2  [(gogoproto.jsontag) = "lottery_id",json_name = "lottery_id"];
  string Author = 3  [(gogoproto.jsontag) = "author",json_name = "author"];
  DraftState State = 4  [(gogoproto.jsontag) = "state",json_name = "state"];
  string Reviewer = 5  [(gogoproto.jsontag) = "reviewer",json_name = "reviewer"];
  string Name = 6  [(gogoproto.jsontag) = "name",json_name = "name"];
  int64 Stime = 7  [(gogoproto.jsontag)  = "stime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "stime"];
  int64 Etime = 8  [(gogoproto.jsontag)  = "etime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "etime"];
}

message RewardsCdkeyV3CountReq {
  int64 AwardID = 1 [(gogoproto.jsontag) = "award_id", json_name = "award_id" , (gogoproto.moretags) = 'validate:"required"'];
  bool  NeedRemain      = 2 [(gogoproto.jsontag)  = "need_remain", json_name = "need_remain"];
}

message RewardsCdkeyV3CountReply{
  int64 Count = 1 [(gogoproto.jsontag) = "count", json_name = "count"];
}

message RewardsAwardInfoWithoutPrivate {
  int64 Id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.moretags) = 'form:"id" validate:"required"'];
  int64 ActivityId = 2 [(gogoproto.jsontag) = "activity_id", json_name = "activity_id"];
  string ActivityName = 3 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name"];
  string Type = 4 [(gogoproto.jsontag) = "type", json_name = "type", (gogoproto.moretags) = 'form:"type" validate:"required"'];
  string Name = 5 [(gogoproto.jsontag) = "name", json_name = "name", (gogoproto.moretags) = 'form:"name" validate:"required"'];
  string IconUrl = 6 [(gogoproto.jsontag) = "icon", json_name = "icon"];
  //奖励介绍信息
  string description = 7 [(gogoproto.jsontag) = "description", json_name = "description", (gogoproto.moretags) = 'form:"description"'];
  bool CheckPassed = 8[(gogoproto.jsontag) = "pre_check_ok", json_name = "pre_check_ok"];
  // ExtraInfo 额外信息
  map<string, string> ExtraInfo  = 9 [(gogoproto.jsontag)  = "extra", json_name = "extra"];
}

// Deprecated
message LotteryOfflineReq {
  string LotteryID = 1 [(gogoproto.jsontag)  = "lottery_id", json_name = "lottery_id", (gogoproto.moretags) = 'validate:"required"'];
  string Author = 2 [(gogoproto.jsontag)  = "author", json_name = "author", (gogoproto.moretags) = 'validate:"required"'];
}

// Deprecated
message GetAllOnlineLotteryReq{
}
// Deprecated
message GetAllOnlineLotteryReply{
  repeated Lottery List  = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}

// Deprecated
message GetAllOnlineLotteryGiftReq {
  repeated string LotteryIDs   = 1 [(gogoproto.jsontag) = "sids", json_name = "sids"];
}
// Deprecated
message GetAllOnlineLotteryGiftReply{
  repeated Gift List  = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}

// Deprecated
message LotteryAddTimesBatchLogReply {
  repeated AddTimesBatchLog List = 1 [(gogoproto.jsontag) = 'list', json_name = "list"];
  Page Page = 2 [(gogoproto.jsontag) = 'page', json_name = "page"];
}

// Deprecated
message AddTimesBatchLog {
  int64  ID  = 1 [(gogoproto.jsontag) = 'id', json_name = "id"];
  string Operator = 2 [(gogoproto.jsontag) = 'author', json_name = "author"];
  string SID  = 3 [(gogoproto.jsontag) = 'sid', json_name = "sid"];
  int64  CID  = 4 [(gogoproto.jsontag) = 'cid', json_name = "cid"];
  AddTimesBatchLogState  State  = 5 [(gogoproto.jsontag) = 'state', json_name = "state"];
  string  FileName  = 6 [(gogoproto.jsontag) = 'filename', json_name = "filename"];
  int64  Ctime = 7 [(gogoproto.jsontag) = 'ctime', json_name = "ctime" , (gogoproto.casttype) = "go-common/library/time.Time" ];
  int64  Mtime = 8 [(gogoproto.jsontag) = 'mtime', json_name = "mtime" , (gogoproto.casttype) = "go-common/library/time.Time" ];
}

enum AddTimesBatchLogState {
  AddTimesBatchLogStateOffline  = 0;
  AddTimesBatchLogStateOnline  = 1;
}

message LotteryAddTimesBatchLogReq{
  string SID = 1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  int64 Pn = 2 [json_name = "pn", (gogoproto.moretags) = 'default:"1"'];
  int64 Ps = 3 [json_name = "ps", (gogoproto.moretags) = 'default:"20"'];
}

// Deprecated
message LotteryAddTimesBatchRetryReq {
  int64 ID = 1 [json_name = "id", (gogoproto.moretags) = 'validate:"required"'];
  string Operator = 2 [json_name = "operator", (gogoproto.moretags) = 'validate:"required"'];
}

// Deprecated
message LotteryAddTimesBatchReq {
  string SID = 1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  int64 CID = 2 [json_name = "cid", (gogoproto.moretags) = 'validate:"required"'];
  repeated int64 MID = 3 [json_name = "mid", (gogoproto.moretags) = 'validate:"required"'];
  string Operator = 4 [json_name = "operator", (gogoproto.moretags) = 'validate:"required"'];
}

// Deprecated
message LotteryUserRecordReq {
  string SID = 1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  int64 MID = 2 [json_name = "mid", (gogoproto.moretags) = 'validate:"required"'];
}

// Deprecated
message LotteryUserRecordReply {
  repeated RecordDetail List  = 1 [json_name = "list", (gogoproto.jsontag) = 'list'];
}

message LotteryXActionRecordExportReq {
  repeated .operational.model.components.v1.CommonExportParam Params = 1 [json_name = "params", (gogoproto.moretags) = 'validate:"required"'];
  string Username = 2 [json_name = "username", (gogoproto.moretags) = 'validate:"required"'];
}

message LotteryXUserActionRecordReply {
  repeated RecordDetailOuterX List  = 1 [json_name = "list", (gogoproto.jsontag) = 'list'];
  Page Page  = 2 [(gogoproto.jsontag) = "page", json_name = "page"];
}

message RecordDetailOuterX {
  string LotterySid = 1 [(gogoproto.jsontag) = 'lottery_sid', json_name = "lottery_sid"];
  string LotteryName = 2 [(gogoproto.jsontag) = 'lottery_name', json_name = "lottery_name"];
  int64  Mid = 3 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
  int64  Time = 4 [(gogoproto.jsontag) = 'time', json_name = "time"];
  string IsWin = 5 [(gogoproto.jsontag) = 'is_win', json_name = "is_win"];
  string  AwardSid = 6 [(gogoproto.jsontag) = 'award_sid', json_name = "award_sid"];
  string AwardName = 7 [(gogoproto.jsontag) = 'award_name', json_name = "award_name"];
  ActionTypeX  Type =8[(gogoproto.jsontag) ='type', json_name="type"];
  string  extra = 9 [(gogoproto.jsontag) = 'extra', json_name = "extra"];
}

// Deprecated
message LotteryVipCheckReq {
  string Cookie = 1 [json_name = "cookie", (gogoproto.moretags) = 'validate:"required"'];
  string VipID = 2 [json_name = "vip_id", (gogoproto.moretags) = 'validate:"required"'];
}

// Deprecated
message LotteryVipCheckReply{
  int64 Check = 1 [json_name = "check"];
}

message LotteryWinListReq {
  string SID = 1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  int64 GiftID = 2 [json_name = "gift_id"];
  int64 Pn = 3 [json_name = "pn" ,(gogoproto.moretags) = 'default:"1"'];
  int64 Ps = 4 [json_name = "ps" , (gogoproto.moretags) = 'default:"20"'];
}

message Address {
  string Addr = 1 [(gogoproto.jsontag) = "addr", json_name = "addr"];
  string Area = 2 [(gogoproto.jsontag) = "area", json_name = "area"];
  int64 AreaID = 3 [(gogoproto.jsontag) = "area_id", json_name = "area_id"];
  string City = 4 [(gogoproto.jsontag) = "city", json_name = "city"];
  int64 CityID = 5 [(gogoproto.jsontag) = "city_id", json_name = "city_id"];
  string Name = 6 [(gogoproto.jsontag) = "name", json_name = "name"];
  int64 Phone = 7 [(gogoproto.jsontag) = "phone", json_name = "phone"];
  string Prov = 8 [(gogoproto.jsontag) = "prov", json_name = "prov"];
  int64 ProvID = 9 [(gogoproto.jsontag) = "prov_id", json_name = "prov_id"];
  string ZipCode = 10 [(gogoproto.jsontag) = "zip_code", json_name = "zip_code"];
}

message WinList {
  int64 GiftID = 1 [(gogoproto.jsontag) = "gift_id", json_name = "gift_id"];
  int64 ID = 2 [(gogoproto.jsontag) = "id", json_name = "id"];
  int64 MID = 3 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  int64 Ctime = 4  [(gogoproto.jsontag)  = "ctime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "ctime"];
  int64 Mtime = 5  [(gogoproto.jsontag)  = "mtime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "mtime"];
}

message LotteryWinListReply {
  repeated WinList List  = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}

// Deprecated
message LotteryWinExportReq {
  string SID = 1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  int64 GiftID = 2 [json_name = "gift_id"];
  string Operator = 3 [json_name = "operator",(gogoproto.moretags) = 'validate:"required"'];
  bool IsWin = 4 [json_name = "is_win"];
}

// Deprecated
message LotteryGiftReq {
  string SID =  1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  // 是否进入奖池 0=未进入 1=进入
  Efficient Efficient = 2 [json_name = "efficient"];
  // 是否删除 0=未删除 1=删除
  GiftState State = 11 [json_name = "state"];
}

message LotteryStockWarningReq {
  string SID =  1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  // 奖品是否进入奖池 0=未进入 1=进入
  Efficient Efficient = 2 [json_name = "efficient"];
  // 奖品是否删除 0=未删除 1=删除
  GiftState State = 3 [json_name = "state"];
  // 库存id
  int64 StockId = 4 [json_name = "stock_id"];
}

message LotteryStockWarningReply {
  Gift Gift = 1 [(gogoproto.jsontag) = 'list', json_name = "list"];
  Lottery conf   = 2 [(gogoproto.jsontag)  = "conf",json_name = "conf"];
}

// Deprecated
message LotteryGiftReply {
  repeated Gift Gift = 1 [(gogoproto.jsontag) = 'list', json_name = "list"];
  Page Page= 2 [(gogoproto.jsontag) = "page", json_name = "page"];

}


message Gift {
  int64 ID  = 1 [(gogoproto.jsontag) = 'id', json_name = "id"];
  string SID  = 2 [(gogoproto.jsontag) = 'sid', json_name = "sid"];
  string GiftName  = 3 [(gogoproto.jsontag) = 'gift_name', json_name = "gift_name"];
  int64 Num  = 4 [(gogoproto.jsontag) = 'num', json_name = "num"];
  GiftType GiftType  = 5 [(gogoproto.jsontag) = 'gift_type', json_name = "gift_type"];
  string ImgUrl  = 6 [(gogoproto.jsontag) = 'img_url', json_name = "img_url"];
  int64 TimeLimit  = 7  [(gogoproto.jsontag) = 'time_limit',( gogoproto.casttype) = "go-common/library/time.Time", json_name = "time_limit"];
  IsShow IsShow  = 8  [(gogoproto.jsontag) = 'is_show', json_name = "is_show"];
  LeastMark LeastMark  =  9 [(gogoproto.jsontag) = 'least_mark', json_name = "least_mark"];
  Efficient Efficient  =  10 [(gogoproto.jsontag) = 'effect', json_name = "effect"];
  GiftState State  =  11 [(gogoproto.jsontag) = 'state', json_name = "state"];
  string MemberGroup  =  12 [(gogoproto.jsontag) = 'member_group', json_name = "member_group"];
  repeated int64 MemberGroupList  =  13 [(gogoproto.jsontag) = 'member_group_list', json_name = "member_group_list"];
  string DayNum  =  14 [(gogoproto.jsontag) = 'day_num', json_name = "day_num"];
  GiftDayNum DayNumStruct  =  15 [(gogoproto.jsontag) = 'day_num_struct', json_name = "day_num_struct"];
  int64 SendNum  =  16 [(gogoproto.jsontag) = 'send_num', json_name = "send_num"];
  float Probability  =  17 [(gogoproto.jsontag) = 'probability', json_name = "probability"];
  string Extra  =  18 [(gogoproto.jsontag) = 'extra', json_name = "extra"];
  map<int64, int64> ExtraStruct  =  19 [(gogoproto.jsontag) = 'extra_struct', json_name = "extra_struct"];
  string Params  =  20 [(gogoproto.jsontag) = 'params', json_name = "params"];
  int64 Version  =  21 [(gogoproto.jsontag) = 'version', json_name = "version"];
  int64 Ctime = 22  [(gogoproto.jsontag)  = "ctime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "ctime"];
  int64 Mtime = 23  [(gogoproto.jsontag)  = "mtime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "mtime"];
  int64 Store = 24  [(gogoproto.jsontag) = 'store', json_name = "store"];
}

// Deprecated
message LotteryMemberGroupReply {
  repeated MemberGroupData MemberGroup = 1 [(gogoproto.jsontag)  = "member_group",json_name = "member_group"];
}


// Deprecated
message LotteryDetailReq {
  string SID = 1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
}

// Deprecated
message LotteryDetailReply {
  Lottery Info   = 1 [(gogoproto.jsontag)  = "info",json_name = "info"];
}

message Lottery {
  int64 ID = 1  [(gogoproto.jsontag)  = "id",json_name = "id"];
  string LotteryID = 2  [(gogoproto.jsontag)  = "lottery_id",json_name = "lottery_id"];
  string Author = 3  [(gogoproto.jsontag)  = "author",json_name = "author"];
  LotteryType LotteryType = 4  [(gogoproto.jsontag)  = "lottery_type",json_name = "lottery_type"];
  LotteryState State = 5  [(gogoproto.jsontag)  = "state",json_name = "state"];
  string Name = 6  [(gogoproto.jsontag)  = "name",json_name = "name"];
  int64 LevelLimit = 7  [(gogoproto.jsontag)  = "level_limit",json_name = "level_limit"];
  int64 RegTimeStime = 8  [(gogoproto.jsontag)  = "regtime_stime",json_name = "regtime_stime"];
  int64 RegTimeEtime = 9  [(gogoproto.jsontag)  = "regtime_etime",json_name = "regtime_etime"];
  VipCheck VipCheck = 10  [(gogoproto.jsontag)  = "vip_check",json_name = "vip_check"];
  RiskLevel RiskLevel = 11  [(gogoproto.jsontag)  = "risk_level",json_name = "risk_level"];
  FsIp FsIp = 12  [(gogoproto.jsontag)  = "fs_ip",json_name = "fs_ip"];
  HighType HighType = 13  [(gogoproto.jsontag)  = "high_type",json_name = "high_type"];
  int64 HighRate = 14  [(gogoproto.jsontag)  = "high_rate",json_name = "high_rate"];
  int64 GiftRate = 15  [(gogoproto.jsontag)  = "gift_rate",json_name = "gift_rate"];
  int64 Tables = 16  [(gogoproto.jsontag)  = "tables",json_name = "tables"];
  int64 Stime = 17  [(gogoproto.jsontag)  = "stime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "stime"];
  int64 Etime = 18  [(gogoproto.jsontag)  = "etime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "etime"];
  int64 Ctime = 19  [(gogoproto.jsontag)  = "ctime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "ctime"];
  int64 Mtime = 20  [(gogoproto.jsontag)  = "mtime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "mtime"];
  int64 AwardActivityID = 21 [(gogoproto.jsontag) = "award_activity_id", json_name = "award_activity_id"];

}

// Deprecated
message LotteryMemberGroupReq {
  string SID = 1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
}

message MemberGroupData {
  int64 ID  = 1 [(gogoproto.jsontag)  = "id",json_name = "id"];
  string Group  = 2 [(gogoproto.jsontag)  = "group",json_name = "group"];
  string Name  = 3 [(gogoproto.jsontag)  = "group_name",json_name = "group_name"];
  string Sid  = 4 [(gogoproto.jsontag)  = "sid",json_name = "sid"];
  GroupState State  = 5 [(gogoproto.jsontag)  = "state",json_name = "state"];
  int64 Ctime = 6  [(gogoproto.jsontag)  = "ctime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "ctime"];
  int64 Mtime = 7  [(gogoproto.jsontag)  = "mtime", (gogoproto.casttype) = "go-common/library/time.Time",json_name = "mtime"];

}
enum LotteryType {
  LotteryTypeImmediately = 0;   // 即时抽奖
  LotteryTypeOffline = 1;       // 离线抽奖
}

enum LotteryAddtimesBatchLogState {
  LotteryAddtimesBatchLogStateNone = 0; //无定义
  LotteryAddtimesBatchLogStateInit = 1; //初始化
  LotteryAddtimesBatchLogStateFinish = 2; //完成
  LotteryAddtimesBatchLogStateError = 3; //失败
  LotteryAddtimesBatchLogStateFileError = 4; //文件导入失败
}



enum LotteryState {
  LotteryStateOnline = 0 ;//有效
  LotteryStateOffline = 1 ;//无效


}

enum DraftState {
  DraftStateFinish = 0 ;//已同步
  DraftStateOffline = 1 ;//已下线
  DraftStateDraft = 2 ;//草稿
  DraftStateWaitAudit = 3 ;//待审
  DraftStateReject = 4 ;// 打回
  DraftStateHandling = 5 ;//处理中
}


enum RiskLevel {
  RiskLevelNormal = 0; //通用
}

enum VipCheck {
  VipCheckNoLimit = 0; //不限制
  VipCheckVip = 1; //vip专享
  VipCheckMonthVip = 2; //月度大会员
  VipCheckYearVip = 3; //年度大会员
}

enum FsIp {
  FsIpClose = 0; //未开启
  FsIpOpen = 1; //开启
}

enum HighType {
  HighTypeClose = 0; // 未开启
  HighTypeBuyVip = 1; // 购买大会员
  HighTypeArchive = 2; // 投稿
}

// Deprecated
message LotteryTimesReq {
  string SID = 1 [json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
}

// Deprecated
message LotteryTimesReply {
  TimesConfig PriceTimes = 1 [json_name = "price_times"];
  TimesConfig LotteryTimes = 2 [json_name = "lottery_times"];
  repeated TimesConfig TimesConf  = 3 [json_name = "times_conf"];
}


message Page {
  int64 Num = 1  [(gogoproto.jsontag) = "num",json_name = "num"];
  int64 PageSize = 2  [(gogoproto.jsontag) = "size",json_name = "size"];
  int64 Total = 3  [(gogoproto.jsontag) = "total",json_name = "total"];
  int64 PageNum = 4  [(gogoproto.jsontag) = "page_num",json_name = "page_num"];
}

message NoReply {}

enum DraftListReqState {
  DraftListReqStateOffline = 0 ;//已下线
  DraftListReqStateFinish = 1;//已同步
  DraftListReqStateDraft = 2 ;//草稿
  DraftListReqStateAudit = 3 ;//待审
  DraftListReqStateReject = 4 ;//待审
  DraftListReqStateHandling = 5 ;//处理中
  DraftListReqStateAll = 100; // all
}



message LotteryDoReq {
  // Sid 抽奖id
  string Sid = 1 [(gogoproto.jsontag) = "sid", json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  // Type 目前固定1
  int32  Type = 2 [(gogoproto.jsontag) = "type", json_name = "type", (gogoproto.moretags) = 'validate:"min=1"'];
  // Mid 用户MID
  int64  Mid = 3 [(gogoproto.jsontag) = "mid" , json_name = "mid" , (gogoproto.moretags) = 'validate:"min=1"'];
  // Num 连抽数量，1-10以内
  int32  Num = 4 [(gogoproto.jsontag) = "num" , json_name = "num" , (gogoproto.moretags) = 'validate:"min=1,max=10"'];
  // OrderNo 幂等字段
  string OrderNo = 5 [(gogoproto.jsontag) = "order_no", json_name = "order_no"];
  // Risk 风控字段
  RiskBaseInfo  Risk = 6[(gogoproto.jsontag) = "risk", json_name = "risk" , (gogoproto.moretags) = 'validate:"required"'];
  // From 来源字段
  string From = 7 [(gogoproto.jsontag) = "from", json_name = "from"];
  // MemberGroupFilterPriority 通过用户组优先过滤奖池
  bool MemberGroupFilterPriority = 8 [(gogoproto.jsontag) = "member_group_filter_priority", json_name = "member_group_filter_priority"];
  // DataFlow 数据流(研发专用)
  repeated string DataFlow = 9 [(gogoproto.jsontag) = "data_flow", json_name = "data_flow"];
  // StockFilterPriority 通过库存优先过滤奖品
  bool StockFilterPriority = 10 [(gogoproto.jsontag) = "stock_filter_priority", json_name = "stock_filter_priority"];
}

message AddTimesReq {
  string Sid = 1 [(gogoproto.jsontag) = "sid", json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  int64  Mid = 2 [(gogoproto.jsontag) = "mid" , json_name = "mid" , (gogoproto.moretags) = 'validate:"min=1"'];
  TimesType  ActionType = 3 [(gogoproto.jsontag) = "action_type", json_name = "action_type", (gogoproto.moretags) = 'validate:"required"'];
  string OrderNo = 4 [(gogoproto.jsontag) = "order_no", json_name = "order_no" ];
  int64 Cid = 5 [(gogoproto.jsontag) = "cid", json_name = "cid" ];
  string Ip = 6 [(gogoproto.jsontag) = "ip", json_name = "ip" ];
  bool   IsOut = 7 [(gogoproto.jsontag) = "is_out", json_name = "is_out" ];
}

message AddTimesReply {
  int32 AddNum = 1 [(gogoproto.jsontag) = "add_num", json_name = "add_num" ];
}


message RecordDetail {
  int64  ID  = 1 [(gogoproto.jsontag) = 'id', json_name = "id"];
  int64  Mid = 2 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
  int64  IP  = 3 [(gogoproto.jsontag) = 'ip', json_name = "ip"];
  int32  Num = 4 [(gogoproto.jsontag) = 'num', json_name = "num"];
  int64  GiftID = 5 [(gogoproto.jsontag) = 'gift_id', json_name = "gift_id"];
  string GiftName = 6 [(gogoproto.jsontag) = 'gift_name', json_name = "gift_name"];
  int32  GiftType = 7 [(gogoproto.jsontag) = 'gift_type', json_name = "gift_type"];
  string ImgURL = 8 [(gogoproto.jsontag) = 'img_url', json_name = "img_url"];
  TimesType  Type = 9[(gogoproto.jsontag) = 'type', json_name = "type"];
  int64  Ctime = 10 [(gogoproto.jsontag) = 'ctime', json_name = "ctime" , (gogoproto.casttype) = "go-common/library/time.Time" ];
  int64  CID = 11 [(gogoproto.jsontag) = 'cid', json_name = "cid"];
  map<string , string> Extra = 12 [(gogoproto.jsontag) = 'extra', json_name = "extra"];
  RewardsAwardInfoWithoutPrivate AwardInfo = 13 [(gogoproto.jsontag) = 'award_info', json_name = "award_info"];
}

// RiskBaseInfo 风控参数
message RiskBaseInfo {
  // 用户设备号，请透传，示例：XX7571A762014F49B574DF299927E9FC8EFAC
  string Buvid  = 1 [(gogoproto.jsontag) = 'buvid', json_name = "buvid"];
  // 请求的来源地址，请透传， 示例：https://www.bilibili.com
  string Origin = 2 [(gogoproto.jsontag) = 'origin', json_name = "origin"];
  // 浏览器请求来源，请透传，示例：https://www.bilibili.com/blackboard/redpack/spring-event-2024.html?from_channel=bullet\u0026half_view=1\u0026na_close_hide=1#/
  string Referer= 3 [(gogoproto.jsontag) = 'referer', json_name = "referer"];
  // 用户ID地址，请透传，示例：************
  string IP     = 4 [(gogoproto.jsontag) = 'ip', json_name = "ip" ,(gogoproto.moretags) = 'validate:"required"'];
  string Ctime  = 5 [(gogoproto.jsontag) = 'ctime', json_name = "ctime"];
  string UserAgent = 6  [(gogoproto.jsontag) = 'user_agent', json_name = "user_agent"];
  string Build     = 7  [(gogoproto.jsontag) = 'build', json_name = "build"];
  // 请求来源的平台，示例：web
  string Platform  = 8  [(gogoproto.jsontag) = 'platform', json_name = "platform"];
  // 风控场景值，支持透传：活动平台默认使用activity_lottery。如需个性化业务风控场景，请咨询Gaia，@琛琛老板
  string Action    = 9  [(gogoproto.jsontag) = 'action', json_name = "action"];
  // 当前抽奖用户uid。示例：354367673
  int64  MID       = 10 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
  string API       = 11 [(gogoproto.jsontag) = 'api', json_name = "api"];
  int64  EsTime    = 12 [(gogoproto.jsontag) = 'estime', json_name = "estime"];
  int64  ActivityID= 13 [(gogoproto.jsontag) = 'activity_id', json_name = "activity_id"];
}

message LotteryDoReply {
  repeated RecordDetail Rewards  = 1 [(gogoproto.jsontag) = "rewards" ,json_name = "rewards"];
}


enum GiftType {
  None = 0;
  GiftTypeMatrial = 1; // 实物奖
  GiftTypeVip = 2; // 大会员
  GiftTypeFace = 3; // 头像挂件
  GiftTypeCoupon = 4; // 优惠券
  GiftTypeCoin = 5; // 硬币
  GiftTypeVipCoupon = 6; // 大会员抵用券
  GiftTypeOther = 7; // 虚拟
  GiftTypeOgv = 8; // Ogv
  GiftTypeVipBuyCoupon = 9; // 会员购券
  GiftTypeMoney= 10; // 现金
  GiftTypeAwardPlatform = 11; // 奖励平台
}

enum IsShow {
  IsShowNotShow = 0; // 不展示
  IsShowShow = 1; //展示
}

enum LeastMark {
  LeastMarkNotLeast = 0; // 保底
  LeastMarkLeast = 1; // 保底奖
}

enum Efficient {
  EfficientOffline = 0; // 未进入
  EfficientOnline = 1; // 进入
  EfficientAll = 2; // all
}

enum GiftState {
  GiftStateOnline = 0; // 有效
  GiftStateOffline = 1; // 无效
  GiftStateAll = 2; // 全部
}



message GiftDayNum {
  // 单日上限
  int64 Store = 1 [(gogoproto.jsontag) = 'store',json_name = "store"];
  // 单用户上限
  int64 Mid = 2 [(gogoproto.jsontag) = 'mid',json_name = "mid"];
}


enum GroupState {
  Offline = 0; // 已删除
  Online =1; //有效
  All =2; //全部
}

message MemberGroup {
  map<int64, int64> Params=1 [(gogoproto.jsontag) = "params" , json_name = "params" ];
  int64 GroupType=2 [(gogoproto.jsontag) = "group_type" , json_name = "group_type" ];
}


enum TimesConfigStatus {
  TimesConfigStatusNotChange = 0; // 不变更
  TimesConfigStatusUpdate = 1; // 更新
  TimesConfigStatusDelete = 2; // 删除
  TimesConfigStatusAdd = 3; // 新增
}

enum TimesType {
  TimesTypeNone = 0; // 未配置
  TimesTypeBase = 1; // 基础抽奖次数
  TimesTypeWin = 2; // 最多赢的次数
  TimesTypeShare = 3; // 分享增加次数
  TimesTypeFollow = 4; // 关注增加次数
  TimesTypeArchiveOrReserve = 5; // 投稿/预约增加次数
  TimesTypeBuyVip = 6; // 购买大会员
  TimesTypeOther = 7; // 其他
  TimesTypeVip = 8; // 大会员行为
  TimesTypeOgv = 9; // 追番
  TimesTypeFe = 10; // 前端增加次数
  TimesTypeLikes = 11; // 点赞
  TimesTypeCoins = 12; // 投币
  TimesTypeExtra = 13; // 额外赠送次数
  TimesTypeTask = 14; // 任务行为增加次数
  TimesTypePoints = 15; // 积分统计增加次数
}

message TimesConfig {
  int64 ID = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  string SID = 2 [(gogoproto.jsontag) = "sid", json_name = "sid"];
  TimesType TimesType = 3 [(gogoproto.jsontag) = "type", json_name = "type"];
  string Info = 4 [(gogoproto.jsontag) = "info", json_name = "info"];
  int64 Times = 5 [(gogoproto.jsontag) = "times", json_name = "times"];
  TimesAddType AddType = 6 [(gogoproto.jsontag) = "add_type", json_name = "add_type"];
  int64 Most = 7 [(gogoproto.jsontag) = "most", json_name = "most"];
  TimesConfigStatus Status = 8 [(gogoproto.jsontag) = "status", json_name = "status"];
  TimesConfigState State = 9 [(gogoproto.jsontag) = "state", json_name = "state"];
}

enum TimesConfigState {
  TimesStateOnline=0; //线上
  TimesStateOffline=1;  //线下
  TimesStateAll=2; //全部

}



enum TimesAddType {
  TimesAddTypeAll = 0; //整个活动期间
  TimesAddTypeDay = 1; //每日
}

// Deprecated
message LotteryUserTimesLogReq {
  //抽奖活动ID
  string Sid = 1 [(gogoproto.jsontag) = "sid", json_name="sid", (gogoproto.moretags) = 'form:"sid" validate:"required"'];
  //Mid
  int64 Mid = 2 [(gogoproto.jsontag) = "mid", json_name="mid",  (gogoproto.moretags) = 'validate:"required"'];
  int64 Pn = 3 [(gogoproto.jsontag) = "pn", json_name = "pn", (gogoproto.moretags) = 'default:"1"'];
  int64 Ps = 4 [(gogoproto.jsontag) = "ps", json_name = "ps", (gogoproto.moretags) = 'default:"20"'];
  int64 Cid = 5 [(gogoproto.jsontag) = "cid", json_name = "cid"];
}

message LotteryAddTimesItem {
  int64 Id = 1 [(gogoproto.jsontag) = "id", json_name="id"];
  int64 Cid = 2 [(gogoproto.jsontag) = "cid", json_name="cid"];
  int64 Ctime = 3 [(gogoproto.jsontag) = "ctime" ,json_name = "ctime" , (gogoproto.casttype) = "go-common/library/time.Time"];
  int64 Mid = 4 [(gogoproto.jsontag) = "mid" ,json_name = "mid"];
  int64 Num = 5 [(gogoproto.jsontag) = "num" ,json_name = "num"];
  TimesType Type = 6 [(gogoproto.jsontag) = "type" ,json_name = "type"];
}

message LotteryUserTimesLogReply {
  repeated LotteryAddTimesItem List  = 1 [(gogoproto.jsontag) = "list" ,json_name = "list"];
  Page Page = 2 [(gogoproto.jsontag) = 'page', json_name = "page"];
}


message LotteryUserGetTimesReq {
  //抽奖活动ID
  string Sid = 1 [(gogoproto.jsontag) = "sid", json_name="sid", (gogoproto.moretags) = 'form:"sid" validate:"required"'];
  //Mid
  int64 Mid = 2 [(gogoproto.jsontag) = "mid", json_name="mid",  (gogoproto.moretags) = 'validate:"required"'];
}

message LotteryUserGetTimesReply {
  //添加抽奖次数
  int64 Times = 1 [(gogoproto.jsontag) = "times", json_name="times"];
}

message LotteryGetWinListReq {
  string Sid       = 1 [(gogoproto.jsontag) = "sid", json_name="sid", (gogoproto.moretags) = 'form:"sid" validate:"required"'];
  int64 Num       = 2 [(gogoproto.jsontag) = "num", json_name="num", (gogoproto.moretags) = 'form:"num" default:"10" validate:"min=1"'];
  bool NeedCache       = 3 [(gogoproto.jsontag) = "need_cache", json_name = "need_cache", (gogoproto.moretags) = 'form:"need_cache"'];
}

message LotteryGetWinListReply {
  repeated LotteryWinListItem List  = 1 [(gogoproto.jsontag) = "list" ,json_name = "list"];
}

message LotteryWinListItem {
  string Name = 1 [(gogoproto.jsontag) = "name" ,json_name = "name"];
  int64 GiftID = 2 [(gogoproto.jsontag) = "gift_id" ,json_name = "gift_id"];
  string GiftName = 3 [(gogoproto.jsontag) = "gift_name" ,json_name = "gift_name"];
  int64 Mid = 4 [(gogoproto.jsontag) = "mid" ,json_name = "mid"];
  string GiftImgUrl = 5 [(gogoproto.jsontag) = "gift_img_url" ,json_name = "gift_img_url"];
  int64 Ctime = 6 [(gogoproto.jsontag) = "ctime" ,json_name = "ctime" , (gogoproto.casttype) = "go-common/library/time.Time"];
}


message LotteryUserGetListReq {
  //抽奖活动ID
  string Sid = 1 [(gogoproto.jsontag) = "sid", json_name="sid", (gogoproto.moretags) = 'validate:"required"'];
  //Mid
  int64 Mid = 2 [(gogoproto.jsontag) = "mid", json_name="mid",  (gogoproto.moretags) = 'validate:"required"'];
  int64 Pn = 3 [(gogoproto.jsontag) = "pn", json_name = "pn", (gogoproto.moretags) = 'default:"1"'];
  int64 Ps = 4 [(gogoproto.jsontag) = "ps", json_name = "ps", (gogoproto.moretags) = 'default:"20"'];
  bool NeedAddress = 5 [(gogoproto.jsontag) = "need_address", json_name = "need_address"];
  //只展示中奖的记录
  bool OnlyWin = 6 [(gogoproto.jsontag) = "only_win", json_name = "only_win",  (gogoproto.moretags) = 'form:"only_win" '];
}

message LotteryUserGetListReply {
  repeated RecordDetail List = 1 [(gogoproto.jsontag) = 'list', json_name = "list"];
  Page Page = 2 [(gogoproto.jsontag) = 'page', json_name = "page"];
  bool IsAddAddress = 3 [(gogoproto.jsontag) = 'is_add_address', json_name = "is_add_address"];
}


message LotteryUserAddAddressReq {
  //抽奖活动ID
  string Sid = 1 [(gogoproto.jsontag) = "sid", json_name="sid", (gogoproto.moretags) = 'form:"sid" validate:"required"'];
  //地址ID
  int64 AddressId = 2 [(gogoproto.jsontag) = "id", json_name="id", (gogoproto.moretags) = 'form:"id" validate:"required"'];
  //Mid
  int64 Mid = 3 [(gogoproto.jsontag) = "mid", json_name="mid",  (gogoproto.moretags) = 'validate:"required"'];
}

message LotteryUserGetAddressReq {
  //抽奖活动ID
  string Sid = 1 [(gogoproto.jsontag) = "sid", json_name="sid", (gogoproto.moretags) = 'validate:"required"'];
  //Mid
  int64 Mid = 3 [(gogoproto.jsontag) = "mid", json_name="mid",  (gogoproto.moretags) = 'validate:"required"'];
}

message AddressInfo {
  int64 ID = 1 [(gogoproto.jsontag) = "id", json_name="id"];
  int64 Type = 2 [(gogoproto.jsontag) = "type", json_name="type"];
  int64 Def = 3 [(gogoproto.jsontag) = "def", json_name="def"];
  int64 ProvID = 4 [(gogoproto.jsontag) = "prov_id", json_name="prov_id"];
  int64 CityID = 5 [(gogoproto.jsontag) = "city_id", json_name="city_id"];
  int64 AreaID = 6 [(gogoproto.jsontag) = "area_id", json_name="area_id"];
  string Name = 7 [(gogoproto.jsontag) = "name", json_name="name"];
  string Phone = 8 [(gogoproto.jsontag) = "phone", json_name="phone"];
  string Addr = 9 [(gogoproto.jsontag) = "addr", json_name="addr"];
  string ZipCode = 10 [(gogoproto.jsontag) = "zip_code", json_name="zip_code"];
  string Prov = 11 [(gogoproto.jsontag) = "prov", json_name="prov"];
  string City = 12 [(gogoproto.jsontag) = "city", json_name="city"];
  string Area = 13 [(gogoproto.jsontag) = "area", json_name="area"];
}



message ActLotteryActionRecord {
  int64  ID  = 1 [(gogoproto.jsontag) = 'id', json_name = "id"];
  string Sid = 2 [(gogoproto.jsontag) = 'sid', json_name = "sid"];
  int64  Mid = 3 [(gogoproto.jsontag) = 'mid', json_name = "mid"];
  string IP  = 4 [(gogoproto.jsontag) = 'ip', json_name = "ip"];
  int32  Num = 5 [(gogoproto.jsontag) = 'num', json_name = "num"];
  int64  GiftID = 6 [(gogoproto.jsontag) = 'gift_id', json_name = "gift_id"];
  int32  state = 7 [(gogoproto.jsontag) = 'state', json_name = "state"];
  string OrderNo = 8 [(gogoproto.jsontag) = 'order_no', json_name = "order_no"];
  TimesType  ActionType = 9[(gogoproto.jsontag) = 'action_type', json_name = "action_type"];
  string Ctime = 10 [(gogoproto.jsontag) = 'ctime', json_name = "ctime" ];
  string Mtime = 11 [(gogoproto.jsontag) = 'mtime', json_name = "mtime" ];
  int64  CID = 12 [(gogoproto.jsontag) = 'cid', json_name = "cid"];
  string UniqGiftNo = 13 [(gogoproto.jsontag) = 'uniq_gift_no', json_name = "uniq_gift_no"];
  int64  AwardId  = 14 [(gogoproto.jsontag) = 'award_id', json_name = "award_id"];
  string Extra    = 15 [(gogoproto.jsontag) = 'extra', json_name = "extra"];
}

message BatchActLotteryActions {
  int64 LotteryId = 1 [(gogoproto.jsontag) = 'lottery_id', json_name = "lottery_id"];
  int64 Tables = 2 [(gogoproto.jsontag) = 'tables', json_name = "tables"];
  repeated ActLotteryActionRecord ActionList = 3 [(gogoproto.jsontag) = 'action_list', json_name = "action_list"];
}



message LotteryGiftListReq {
  string Sid = 1 [(gogoproto.jsontag) = 'sid', json_name = "sid"];
}

message LotteryGiftListReply {
  repeated LotteryGiftListItem List = 1 [(gogoproto.jsontag) = 'list', json_name = "list"];
}

message LotteryGiftListItem {
  int64  ID  = 1 [(gogoproto.jsontag) = 'id', json_name = "id"];
  string Sid = 2 [(gogoproto.jsontag) = 'sid', json_name = "sid"];
  string Name = 3 [(gogoproto.jsontag) = "name", json_name="name"];
  GiftType Type = 4 [(gogoproto.jsontag) = "type", json_name="type"];
  string AwardType = 5 [(gogoproto.jsontag) = "award_type", json_name="award_type"];
  string ImgURL = 6 [(gogoproto.jsontag) = "img_url", json_name="img_url"];
  RewardsAwardInfoWithoutPrivate AwardInfo = 7 [(gogoproto.jsontag) = "award_info", json_name="award_info"];
}

message RewardsGetAwardInfoByIdsReq {
  repeated int64 AwardIds = 1 [json_name = "award_ids", (gogoproto.jsontag) = "award_ids", (gogoproto.moretags) = 'validate:"required"'];
}

message RewardsGetAwardInfoByIdsReply {
  map<int64, RewardsAwardInfoWithoutPrivate>  AwardInfos = 1 [json_name = "award_infos", (gogoproto.jsontag) = "award_infos", (gogoproto.moretags) = 'validate:"required"'];
}


message RewardsSendAwardReq {
  int64 mid = 1 [(gogoproto.moretags) = 'validate:"required"'];
  string unique_id = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
  string business = 3 [(gogoproto.jsontag) = "business", json_name = "business"];
  int64 award_id = 4 [(gogoproto.moretags) = 'validate:"required"'];
  //控制同步/异步发放
  bool sync = 5 [json_name = "sync"];
  //updateDB: 是否更新DB, 可避免消息队列丢失导致丢数据. 只在sync=true时有效
  //updateDB=true: 一致性高,容忍消息丢失
  //updateDB=false: 性能高,需要调用方自身提供额外的对账机制
  bool update_db = 6 [json_name = "update_db"];
  //updateCache: 是否将此条记录更新到缓存,拜年纪会主动添加缓存所以无需再次更新
  //只在need_send=true时有效
  bool update_cache = 7 [json_name = "update_cache"];
}

message RewardsSendAwardV2Req {
  //发放的Mid
  int64 mid = 1 [(gogoproto.moretags) = 'validate:"required"'];
  //幂等id, 使用相同的幂等id发放奖励会返回code=0, 但不会再次发放
  string unique_id = 2 [(gogoproto.moretags) = 'validate:"required"'];
  //业务标识
  string business = 3 [(gogoproto.moretags) = 'validate:"required"'];
  //奖励id
  int64 award_id = 4 [(gogoproto.moretags) = 'validate:"required"'];
}

message RewardsSendAwardV3Req {
    //发放的Mid
    int64 mid            = 1 [(gogoproto.moretags) = 'validate:"required"'];
    //幂等id, 使用相同的幂等id发放奖励会返回code=(75971) 活动奖励已发放~
    string unique_id     = 2 [(gogoproto.moretags) = 'validate:"required,max=32"'];
    //业务标识
    string business      = 3 [(gogoproto.moretags) = 'validate:"required,max=32"'];
    //奖励id
    int64 award_id       = 4 [(gogoproto.moretags) = 'validate:"required"'];
}

message RewardsSendAwardPreCheckReq {
  //发放的Mid
  int64 mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = 'validate:"required"'];
  //幂等id, 使用相同的幂等id发放奖励会返回code=0, 但不会再次发放
  string unique_id = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
  //业务标识
  string business = 3 [(gogoproto.jsontag) = "business", json_name = "business"];
  //奖励id
  int64 award_id = 4 [(gogoproto.jsontag) = "award_id", json_name = "award_id", (gogoproto.moretags) = 'validate:"required"'];
  //是否跳过风控检查
  bool skip_risk_check = 5 [(gogoproto.jsontag) = "skip_risk_check", json_name = "skip_risk_check"];
  //自定义风控Ctx
  bytes custom_risk_ctx = 6 [(gogoproto.jsontag) = "custom_risk_ctx", json_name = "custom_risk_ctx"];
}

message RewardsSendAwardPreCheckReply {
  //检查是否通过
  bool check_passed = 1 [json_name = "check_passed"];
  int64 check_error_code = 2 [json_name = "check_error_code"];
  //错误种类
  string check_error_type = 3 [(gogoproto.jsontag) = "check_error_type", json_name = "check_error_type"];
  //业务标识
  string check_error_msg = 4 [(gogoproto.jsontag) = "check_error_msg", json_name = "check_error_msg"];
}


message RewardsSendAwardReply {
  int64 activity_id = 1 [(gogoproto.jsontag) = "activity_id", json_name = "activity_id"];
  string activity_name = 2 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name"];
  int64 receive_time = 3 [(gogoproto.jsontag) = "receive_time", json_name = "receive_time"];
  string type = 4 [(gogoproto.jsontag) = "type", json_name = "type"];
  string name = 5 [(gogoproto.jsontag) = "name", json_name = "name"];
  string icon = 6 [(gogoproto.jsontag) = "icon", json_name = "icon"];
  map<string, string> ExtraInfo = 7 [(gogoproto.jsontag) = "extra", json_name = "extra"];
  int64 award_id = 8 [(gogoproto.jsontag) = "award_id", json_name = "award_id"];
  int64 mid = 9 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  //奖励介绍信息
  string description = 10 [(gogoproto.jsontag) = "description", json_name = "description"];
  //是否通过发奖前置条件检查
  bool pre_check_ok = 11 [(gogoproto.jsontag) = "pre_check_ok", json_name = "pre_check_ok"];
  //发奖前置条件检查信息
  string pre_check_msg = 12 [(gogoproto.jsontag) = "pre_check_msg", json_name = "pre_check_msg"];
  //发放状态的拓展字段
  map<string, string> SendExtraInfo = 13 [(gogoproto.jsontag) = "send_extra", json_name = "send_extra"];
  //发奖前置条件检查信息
  string pre_check_error_type = 14 [(gogoproto.jsontag) = "pre_check_error_type", json_name = "pre_check_error_type"];
  //发奖前置条件检查错误码
  int64 check_error_code = 15 [json_name = "check_error_code"];
  //订单号
  string unique_id = 16 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
}

message RetryRewardsSendAwardReq {
  int64 mid = 1 [(gogoproto.moretags) = 'validate:"required"'];
  string unique_id = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
  string business = 3 [(gogoproto.jsontag) = "business", json_name = "business"];
  int64 award_id = 4 [(gogoproto.moretags) = 'validate:"required"'];
}

message RewardsCleanRecordCacheReq {
  int64 activity_id= 1 [(gogoproto.moretags) = 'form:"activity_id" validate:"required"'];
  int64 mid= 2 [(gogoproto.moretags) = 'form:"mid" validate:"required"'];
}

message RewardsGetMyListReq {
  int64 Mid = 1 [(gogoproto.jsontag) = "mid",  json_name = "mid" , (gogoproto.moretags) = 'form:"mid" validate:"required"'];
  repeated int64 ActivityIds = 2 [(gogoproto.jsontag) = "activity_ids",  json_name = "activity_ids" , (gogoproto.moretags) = 'form:"activity_ids" validate:"required"'];
}

message AwardSentInfo {
    int64 Mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid"];
    int64 AwardId = 2 [(gogoproto.jsontag) = "award_id", json_name = "award_id"];
    string AwardName  = 3 [(gogoproto.jsontag) = "award_name", json_name = "award_name"];
    int64 ActivityId  = 4 [(gogoproto.jsontag) = "activity_id", json_name = "activity_id"];
    string ActivityName  = 5 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name"];
    string Type = 6 [(gogoproto.jsontag) = "type", json_name = "type"];
    string  IconUrl = 7 [(gogoproto.jsontag) = "icon", json_name = "icon"];
    int64  SentTime = 8 [(gogoproto.jsontag)  = "receive_time", (gogoproto.casttype) = "go-common/library/time.Time", json_name = "receive_time"];
    map<string , string>  ExtraInfo   = 9 [(gogoproto.jsontag) = "extra_info", json_name = "extra_info"];
    string Description   = 10 [(gogoproto.jsontag) = "description", json_name = "description"];
    int64 State      = 11 [(gogoproto.jsontag) = "state", json_name = "state"];
    bool PreCheckOk  = 12  [(gogoproto.jsontag) = "pre_check_ok", json_name = "pre_check_ok"];
    string PreCheckMsg = 13 [(gogoproto.jsontag) = "pre_check_msg", json_name = "pre_check_msg"];
    string PreCheckErrType = 14 [(gogoproto.jsontag) = "pre_check_error_type", json_name = "pre_check_error_type"];
    int64 PreCheckErrCode = 15 [(gogoproto.jsontag) = "pre_check_error_code", json_name = "pre_check_error_code"];
    string UniqueId = 16 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
    bool HadCollectionInfo = 17  [(gogoproto.jsontag) = "had_collection_info", json_name = "had_collection_info"];
    int64 Deadline = 18 [(gogoproto.jsontag) = "deadline", json_name = "deadline"];
    string  BusinessType = 19 [(gogoproto.jsontag) = "business_type", json_name = "business_type"];
}

message RewardsGetMyListReply {
   bool HadAddress = 1 [(gogoproto.jsontag) = "had_address", json_name = "had_address"];
   repeated AwardSentInfo List = 2 [(gogoproto.jsontag) = "list", json_name = "list"];
}

message RewardsAddAwardReq {
  int64 ActivityId = 2 [(gogoproto.jsontag) = "activity_id", json_name = "activity_id", (gogoproto.moretags) = 'form:"activity_id" validate:"required"'];
  string ActivityName = 3 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name", (gogoproto.moretags) = 'form:"activity_id" validate:"required"'];
  string Type = 4 [(gogoproto.jsontag) = "type", json_name = "type", (gogoproto.moretags) = 'form:"type" validate:"required"'];
  string Name = 5 [(gogoproto.jsontag) = "name", json_name = "name", (gogoproto.moretags) = 'form:"name" validate:"required"'];
  bool ShouldSendNotify = 6 [(gogoproto.jsontag) = "should_send_notify", json_name = "should_send_notify", (gogoproto.moretags) = 'form:"should_send_notify"'];
  int64 NotifySenderId = 7 [(gogoproto.jsontag) = "notify_sender_id", json_name = "notify_sender_id", (gogoproto.moretags) = 'form:"notify_sender_id"'];
  string NotifyCode = 8 [(gogoproto.jsontag) = "notify_code", json_name = "notify_code", (gogoproto.moretags) = 'form:"notify_code"'];
  string NotifyMessage = 9 [(gogoproto.jsontag) = "notify_message", json_name = "notify_message", (gogoproto.moretags) = 'form:"notify_message"'];
  string NotifyJumpUri1 = 10 [(gogoproto.jsontag) = "notify_jump_uri1", json_name = "notify_jump_uri1", (gogoproto.moretags) = 'form:"notify_jump_uri1"'];
  string NotifyJumpUri2 = 11 [(gogoproto.jsontag) = "notify_jump_uri2", json_name = "notify_jump_uri2", (gogoproto.moretags) = 'form:"notify_jump_uri2"'];
  string JsonStr = 12 [(gogoproto.jsontag) = "json_str", json_name = "json_str", (gogoproto.moretags) = 'form:"json_str" validate:"required"'];
  map<string, string> ExtraInfo = 13 [(gogoproto.jsontag) = "extra_info", json_name = "extra_info"];
  string IconUrl = 14 [(gogoproto.jsontag) = "icon_url", json_name = "icon_url", (gogoproto.moretags) = 'form:"icon_url" validate:"required"'];
  //奖励介绍信息
  string description = 15 [(gogoproto.jsontag) = "description", json_name = "description", (gogoproto.moretags) = 'form:"description"'];
}

message RewardsDelAwardReq {
  int64 Id = 1 [(gogoproto.moretags) = 'form:"id" validate:"required"'];
}

message RewardsAwardInfo {
  int64 Id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.moretags) = 'form:"id" validate:"required"'];
  int64 ActivityId = 2 [(gogoproto.jsontag) = "activity_id", json_name = "activity_id", (gogoproto.moretags) = 'form:"activity_id" validate:"required"'];
  string ActivityName = 3 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name", (gogoproto.moretags) = 'form:"activity_name" validate:"required"'];
  string Type = 4 [(gogoproto.jsontag) = "type", json_name = "type", (gogoproto.moretags) = 'form:"type" validate:"required"'];
  string Name = 5 [(gogoproto.jsontag) = "name", json_name = "name", (gogoproto.moretags) = 'form:"name" validate:"required"'];
  bool ShouldSendNotify = 6 [(gogoproto.jsontag) = "should_send_notify", json_name = "should_send_notify", (gogoproto.moretags) = 'form:"should_send_notify"'];
  int64 NotifySenderId = 7 [(gogoproto.jsontag) = "notify_sender_id", json_name = "notify_sender_id", (gogoproto.moretags) = 'form:"notify_sender_id"'];
  string NotifyCode = 8 [(gogoproto.jsontag) = "notify_code", json_name = "notify_code", (gogoproto.moretags) = 'form:"notify_code"'];
  string NotifyMessage = 9 [(gogoproto.jsontag) = "notify_message", json_name = "notify_message", (gogoproto.moretags) = 'form:"notify_message"'];
  string NotifyJumpUri1 = 10 [(gogoproto.jsontag) = "notify_jump_uri1", json_name = "notify_jump_uri1", (gogoproto.moretags) = 'form:"notify_jump_uri1"'];
  string NotifyJumpUri2 = 11 [(gogoproto.jsontag) = "notify_jump_uri2", json_name = "notify_jump_uri2", (gogoproto.moretags) = 'form:"notify_jump_uri2"'];
  string JsonStr = 12 [(gogoproto.jsontag) = "json_str", json_name = "json_str", (gogoproto.moretags) = 'form:"json_str" validate:"required"'];
  map<string, string> ExtraInfo = 13 [(gogoproto.jsontag) = "extra_info", json_name = "extra_info"];
  string IconUrl = 14 [(gogoproto.jsontag) = "icon_url", json_name = "icon_url", (gogoproto.moretags) = 'form:"icon_url" validate:"required"'];
  //奖励介绍信息
  string description = 15 [(gogoproto.jsontag) = "description", json_name = "description", (gogoproto.moretags) = 'form:"description"'];
  string Reviser = 16 [(gogoproto.jsontag)  = "reviser", json_name = "reviser"];
  int64  Ctime = 17 [(gogoproto.jsontag) = 'ctime', json_name = "ctime" , (gogoproto.casttype) = "go-common/library/time.Time" ];
}


message RewardsListAwardReq {
  int64 ActivityId = 1 [(gogoproto.moretags) = 'form:"activity_id"'];
  string Keyword = 2 [(gogoproto.moretags) = 'form:"keyword"'];
}


message RewardsListAwardReply {
  repeated RewardsAwardInfo List = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}

message RewardsAddActivityReq {
  string Name = 1 [(gogoproto.moretags) = 'form:"name" validate:"required"'];
  int64 NotifySenderId = 2 [(gogoproto.moretags) = 'form:"notify_sender_id"'];
  string NotifyCode = 3 [(gogoproto.moretags) = 'form:"notify_code"'];
  string NotifyMessage = 4 [(gogoproto.moretags) = 'form:"notify_message"'];
  string NotifyJumpUri1 = 5 [(gogoproto.moretags) = 'form:"notify_jump_uri1"'];
  string NotifyJumpUri2 = 6 [(gogoproto.moretags) = 'form:"notify_jump_uri2"'];
}

message RewardsDelActivityReq {
  int64 ActivityId = 1 [(gogoproto.moretags) = 'form:"activity_id" validate:"required"'];
}

message RewardsUpdateActivityReq {
  int64 Id = 1 [(gogoproto.moretags) = 'form:"id" validate:"required"'];
  string Name = 2 [(gogoproto.moretags) = 'form:"name" validate:"required"'];
  int64 NotifySenderId = 3 [(gogoproto.moretags) = 'form:"notify_sender_id"'];
  string NotifyCode = 4 [(gogoproto.moretags) = 'form:"notify_code"'];
  string NotifyMessage = 5 [(gogoproto.moretags) = 'form:"notify_message"'];
  string NotifyJumpUri1 = 6 [(gogoproto.moretags) = 'form:"notify_jump_uri1"'];
  string NotifyJumpUri2 = 7 [(gogoproto.moretags) = 'form:"notify_jump_uri2"'];
}

message RewardsActivityListInfo {
  int64 Id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.moretags) = 'validate:"required"'];
  string Name = 2 [(gogoproto.jsontag) = "name", json_name = "name", (gogoproto.moretags) = 'validate:"required"'];
  int64 NotifySenderId = 3 [(gogoproto.jsontag) = "notify_sender_id", json_name = "notify_sender_id"];
  string NotifyCode = 4 [(gogoproto.jsontag) = "notify_code", json_name = "notify_code"];
  string NotifyMessage = 5 [(gogoproto.jsontag) = "notify_message", json_name = "notify_message"];
  string NotifyJumpUri1 = 6 [(gogoproto.jsontag) = "notify_jump_uri1", json_name = "notify_jump_uri1"];
  string NotifyJumpUri2 = 7 [(gogoproto.jsontag) = "notify_jump_uri2", json_name = "notify_jump_uri2"];
  int64 AwardsCount = 8 [(gogoproto.jsontag) = "awards_count", json_name = "awards_count"];
}

message RewardsListActivityPage {
  int64 Num = 1 [(gogoproto.jsontag) = "num", json_name = "num"];
  int64 Ps = 2 [(gogoproto.jsontag) = "size", json_name = "size"];
  int64 Total = 3 [(gogoproto.jsontag) = "total", json_name = "total"];
}

message RewardsListActivityReq {
  int64 ActivityId = 1 [(gogoproto.moretags) = 'form:"activity_id"'];
  int64 PageNumber = 2 [(gogoproto.moretags) = 'form:"pn" default:"1"  validate:"min=1"'];
  int64 PageSize = 3 [(gogoproto.moretags) = 'form:"ps" default:"1"  validate:"min=0,max=50"'];
  string Keyword = 4 [(gogoproto.moretags) = 'form:"keyword"'];
}

message RewardsListActivityReply {
  repeated RewardsActivityListInfo List = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
  RewardsListActivityPage Page = 2 [(gogoproto.jsontag) = "page", json_name = "page"];
}

message RewardsGetActivityDetailReq {
  int64 ActivityId = 1 [(gogoproto.moretags) = 'form:"activity_id" validate:"required"'];
}

message RewardsGetActivityDetailReply {
  int64 Id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.moretags) = 'validate:"required"'];
  string Name = 2 [(gogoproto.jsontag) = "name", json_name = "name", (gogoproto.moretags) = 'validate:"required"'];
  int64 NotifySenderId = 3 [(gogoproto.jsontag) = "notify_sender_id", json_name = "notify_sender_id"];
  string NotifyCode = 4 [(gogoproto.jsontag) = "notify_code", json_name = "notify_code"];
  string NotifyMessage = 5 [(gogoproto.jsontag) = "notify_message", json_name = "notify_message"];
  string NotifyJumpUri1 = 6 [(gogoproto.jsontag) = "notify_jump_uri1", json_name = "notify_jump_uri1"];
  string NotifyJumpUri2 = 7 [(gogoproto.jsontag) = "notify_jump_uri2", json_name = "notify_jump_uri2"];
  int64 AwardsCount = 8 [(gogoproto.jsontag) = "awards_count", json_name = "awards_count"];
  repeated RewardsAwardInfo List = 9 [(gogoproto.jsontag) = "award_list", json_name = "award_list"];
}

message RewardsCheckSentStatusReq {
  int64 Mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid", (gogoproto.moretags) = 'form:"mid" validate:"required"'];
  string UniqueId = 2 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'form:"unique_id" validate:"required"'];
  int64  AwardId = 3 [(gogoproto.jsontag) = "award_id", json_name = "award_id"];
}

message RewardsCheckSentStatusResp{
  bool Result = 1 [(gogoproto.jsontag) = "result", json_name = "result"];
}

message RewardsGetAwardConfigByIdReq {
  int64 Id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.moretags) = 'form:"id" validate:"required"'];
}

message RewardsGetAwardInfoByIdReq {
  int64 Id = 1 [(gogoproto.jsontag) = "id", json_name = "id", (gogoproto.moretags) = 'form:"id" validate:"required"'];
}

message RewardsBatchSendAwardV3Req {
 repeated RewardsSendAwardV3Req List = 1 [(gogoproto.jsontag) = "list", json_name = "list", (gogoproto.moretags) = 'validate:"max=100,min=1"'];
}

message RewardsBatchSendAwardV3SingleReply {
  RewardsSendAwardReply Result = 1 [(gogoproto.jsontag) = "result", json_name = "result"];
  int32 ErrorCode = 2 [(gogoproto.jsontag) = "error_code", json_name = "error_code"];
  string ErrorMsg = 3 [(gogoproto.jsontag) = "error_msg", json_name = "error_msg"];
}

message RewardsBatchSendAwardV3Reply {
 //key为unique_id, value为本次发放的结果
 map<string, RewardsBatchSendAwardV3SingleReply> ResMap = 1 [(gogoproto.jsontag) = "res_map", json_name = "res_map"];
}


message NewAwardConfig {
  // 奖品类型
  string AwardType    = 1 [(gogoproto.jsontag) = "award_type", json_name="award_type"];
  // 奖品类型
  string DisplayName  = 2 [(gogoproto.jsontag) = "display_name", json_name="display_name"];
  // 奖品图片
  string IconUrl      = 3 [(gogoproto.jsontag) = "icon_url", json_name="icon_url"];
  // 奖励介绍信息
  string Description  = 4 [(gogoproto.jsontag) = "description", json_name="description"];
  string JsonStr    = 5 [(gogoproto.jsontag) = "json_str", json_name="json_str"];
  // 是否需要绑定手机
  bool NeedBindPhone               = 6 [(gogoproto.jsontag) = "need_bind_phone", json_name="need_bind_phone"];
  // 奖品库存限制类型
  AwardStockLimitType StockLimitType = 7 [(gogoproto.jsontag) = "stock_limit_type", json_name="stock_limit_type"];
  // 奖品库存数量
  int64 StockNumTotal              = 8 [(gogoproto.jsontag) = "stock_num_total", json_name="stock_num_total"];
  // 库存告警的用户，多个用户以逗号隔开
  string Warner                    = 9 [(gogoproto.jsontag) = "warner", json_name="warner"];
  // 库存告警数量
  int64 WarningNum                 = 10 [(gogoproto.jsontag) = "warning_num", json_name="warning_num"];
  // 中奖通知发送号码
  int64 NotifySenderId             = 11 [(gogoproto.jsontag) = "notify_sender_id", json_name="notify_sender_id"];
  // 中奖通知跳转链接
  string NotifyJumpUrl             = 12 [(gogoproto.jsontag) = "notify_jump_url", json_name="notify_jump_url"];
  // 奖品总发放限制:  1：每日、 2：活动期间
  StockCycleType AwardLimitType      = 13 [(gogoproto.jsontag) = "award_limit_type,omitempty", json_name="award_limit_type"];
  // 奖品发放上限
  int64 AwardLimitNum              = 14 [(gogoproto.jsontag) = "award_limit_num,omitempty", json_name="award_limit_num"];
  // 用户发放限制:  1：每日、 2：活动期间
  StockCycleType UserLimitType       = 15 [(gogoproto.jsontag) = "user_limit_type,omitempty", json_name="user_limit_type"];
  // 用户发放上限
  int64 UserLimitNum               = 16 [(gogoproto.jsontag) = "user_limit_num,omitempty", json_name="user_limit_num"];
  // ExtraInfo
  map<string,string> ExtraInfo       = 17 [(gogoproto.jsontag) = "extra", json_name="extra"];
  //是否开启积分兑换
  bool IfNeedScore                   = 18 [(gogoproto.jsontag) = "if_need_score", json_name = "if_need_score"];
  //积分id
  string ScoreId                     = 19 [(gogoproto.jsontag) = "score_id", json_name = "score_id"];
  //积分兑换比例
  int64 ConsumeScoreRatio            = 20 [(gogoproto.jsontag) = "consume_score_ratio", json_name = "consume_score_ratio"];
  //奖励标签
  string RewardTag   =21 [(gogoproto.jsontag) = "reward_tag", json_name = "reward_tag"];
  // trading兑换id
  int64 TransactionId = 22 [(gogoproto.jsontag) = "transaction_id", json_name = "transaction_id"];
  //备注名称
  string RemarksName = 23 [(gogoproto.jsontag) = "remarks_name", json_name = "remarks_name"];
  //白名单人群包
  string WhiteMemberId = 24 [(gogoproto.jsontag) = "white_member_id", json_name = "white_member_id"];
  //黑名单人群包
  string BlackMemberId = 25 [(gogoproto.jsontag) = "black_member_id", json_name = "black_member_id"];
  //每日领奖开始时间
  int64 EveryDayPrizeStime = 26 [(gogoproto.jsontag) = "everyday_prize_stime", json_name = "everyday_prize_stime"];
  //每日领奖结束时间
  int64 EveryDayPrizeEtime = 27 [(gogoproto.jsontag) = "everyday_prize_etime", json_name = "everyday_prize_etime"];
  //领奖截止时间
  int64 ReceivePrizeDDL = 28 [(gogoproto.jsontag) = "receive_prize_ddl", json_name = "receive_prize_ddl"];
  //领奖页强提示
  string StrongHint = 29 [(gogoproto.jsontag) = "strong_hint", json_name = "strong_hint"];
  // 库存提醒修改为开关模式
  bool StockWarningSwitch    = 30 [(gogoproto.jsontag) = "stock_warning_switch", json_name = "stock_warning_switch"];
  // 消耗进度通知开关,打开后每日定时通知发放进度
  bool ConsumeNotifySwitch   = 31 [(gogoproto.jsontag) = "consume_notify_switch", json_name = "consume_notify_switch"];
  // 奖品展示类型:主要用来区分不同迭代版本，奖品展示的样式,如果是多个类型，用逗号分隔
  string  AwardDisplayTypes  = 32 [(gogoproto.jsontag) = "award_display_types", json_name = "award_display_types"];
  // 库存告警的用户组名称，用于复现
  repeated WarnerInfo WarnerList =33 [(gogoproto.jsontag) = "warner_list", json_name = "warner_list"];
  // 奖品库存id
  string  StockID  = 34 [(gogoproto.jsontag) = "stock_id", json_name = "stock_id"];
}

message WarnerInfo {
  string AdAccount =1  [(gogoproto.jsontag) = "ad_account", json_name = "ad_account"];
  string NickName=2  [(gogoproto.jsontag) = "nick_name", json_name = "nick_name"];
}

message RewardsUserGetAddressReq {
  //奖励活动ID
  int64 ActivityId = 1 [(gogoproto.jsontag) = "activity_id", json_name="activity_id", (gogoproto.moretags) = 'form:"activity_id" validate:"required"'];
  //Mid
  int64 Mid = 3 [(gogoproto.jsontag) = "mid", json_name="mid",  (gogoproto.moretags) = 'validate:"required"'];
}

message LotteryXConfig {
  LotteryXBaseConfig base = 1 [(gogoproto.jsontag) = "base", json_name="base", (gogoproto.moretags) = 'form:"base"'];
  repeated LotteryXTimeConfig times = 2 [(gogoproto.jsontag) = "times", json_name="times", (gogoproto.moretags) = 'form:"times"'];
  repeated LotteryXGiftConfig gift = 3 [(gogoproto.jsontag) = "gift", json_name="gift", (gogoproto.moretags) = 'form:"gift"'];
}


message LotteryXBaseConfig {
  LotteryTable tables                    = 1 [(gogoproto.jsontag) = "tables", json_name="tables", (gogoproto.moretags) = 'form:"tables"'];
  LotteryTypeX lottery_type              = 2 [(gogoproto.jsontag) = "lottery_type", json_name="lottery_type", (gogoproto.moretags) = 'form:"lottery_type"'];
  string integral_id                     = 3 [(gogoproto.jsontag) = "integral_id", json_name="integral_id", (gogoproto.moretags) = 'form:"integral_id"'];
  int64 points_per_time                  = 4 [(gogoproto.jsontag) = "points_per_time", json_name="points_per_time", (gogoproto.moretags) = 'form:"points_per_time"'];
  repeated string counter_id             = 5 [(gogoproto.jsontag) = "counter_id", json_name="counter_id", (gogoproto.moretags) = 'form:"counter_id"'];
  LotteryXCoreModel core_model           = 6 [(gogoproto.jsontag) = "core_model", json_name="core_model", (gogoproto.moretags) = 'form:"core_model"'];
  int64 probability                      = 7 [(gogoproto.jsontag) = "probability", json_name="probability", (gogoproto.moretags) = 'form:"probability"'];
  bool no_need_fresh                     = 8 [(gogoproto.jsontag) = "no_need_fresh", json_name="no_need_fresh", (gogoproto.moretags) = 'form:"no_need_fresh"'];
  .operational.model.lottery.v1.RiskLimitEnum risk_limit = 9 [(gogoproto.jsontag) = "risk_limit", json_name="risk_limit", (gogoproto.moretags) = 'form:"risk_limit"'];
  // 概率之和：万分一：10,000(默认)，千万分之一：10,000,000
  int64 max_probability_sum              = 10 [(gogoproto.jsontag) = "max_probability_sum", json_name="max_probability_sum", (gogoproto.moretags) = 'form:"max_probability_sum"'];
}


enum TimesTypeX {
  TimesTypeXNone = 0;
  TimesTypeXBase = 1; // 基础抽奖次数
  TimesTypeXWin = 2; // 最多中奖次数
}

message LotteryXGiftConfig {
  string id = 1 [(gogoproto.jsontag) = "id", json_name="id", (gogoproto.moretags) = 'form:"id"'];
  bool is_show = 2 [(gogoproto.jsontag) = "is_show", json_name="is_show", (gogoproto.moretags) = 'form:"is_show"'];
  bool least_mark = 3 [(gogoproto.jsontag) = "least_mark", json_name="least_mark", (gogoproto.moretags) = 'form:"least_mark"'];
  repeated string member_group = 4 [(gogoproto.jsontag) = "member_group", json_name="member_group", (gogoproto.moretags) = 'form:"member_group"'];
  int64 probability = 5 [(gogoproto.jsontag) = "probability", json_name="probability", (gogoproto.moretags) = 'form:"probability"'];
  string extra = 6 [(gogoproto.jsontag) = "extra", json_name="extra", (gogoproto.moretags) = 'form:"extra"'];
  int64  time_limit = 7 [(gogoproto.jsontag)  = "time_limit", (gogoproto.casttype) = "go-common/library/time.Time", json_name = "time_limit"];
  int64 weight = 8 [(gogoproto.jsontag) = "weight", json_name="weight", (gogoproto.moretags) = 'form:"weight"'];
  int64 num = 9 [(gogoproto.jsontag) = "num", json_name="num", (gogoproto.moretags) = 'form:"num"'];
}

message LotteryXTimeConfig {
  TimesTypeX type = 1 [(gogoproto.jsontag) = "type", json_name="type", (gogoproto.moretags) = 'form:"type"'];
  int64 times = 2 [(gogoproto.jsontag) = "times", json_name="times", (gogoproto.moretags) = 'form:"times"'];
  CycleTypeX cycle_type = 3 [(gogoproto.jsontag) = "cycle_type", json_name="cycle_type", (gogoproto.moretags) = 'form:"cycle_type"'];
}

message LotteryXAddTimesReq {
  string Sid     = 1 [(gogoproto.jsontag) = "sid", json_name = "sid", (gogoproto.moretags) = 'validate:"required"'];
  int64  Mid     = 2 [(gogoproto.jsontag) = "mid" , json_name = "mid" , (gogoproto.moretags) = 'validate:"min=1"'];
  int32 AddNum   = 3 [(gogoproto.jsontag) = "add_num", json_name = "add_num" , (gogoproto.moretags) = 'validate:"min=1"'];
  string OrderNo = 4 [(gogoproto.jsontag) = "order_no", json_name = "order_no" , (gogoproto.moretags) = 'validate:"required"'];
}

message GeneralDummyRewardsCollectionAddReq {
  int64 Mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid",  (gogoproto.moretags) = 'validate:"required"'];
  int64 ActivityId = 2 [(gogoproto.jsontag) = "activity_id", json_name = "activity_id", (gogoproto.moretags) = 'validate:"required"'];
  string OuterActivityId = 3 [(gogoproto.jsontag) = "outer_activity_id", json_name = "outer_activity_id", (gogoproto.moretags) = 'validate:"required"'];
  string ActivityName = 4 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name", (gogoproto.moretags) = 'validate:"required"'];
  int64 AwardId = 5 [(gogoproto.jsontag) = "award_id", json_name = "award_id", (gogoproto.moretags) = 'validate:"required"'];
  string UniqueId = 6 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'validate:"required"'];
  repeated string CollectionInfo = 7 [(gogoproto.jsontag) = "collection_info", json_name = "collection_info", (gogoproto.moretags) = 'validate:"required"'];
}

message GeneralDummyRewardsCollectionGetReq {
  string UniqueId = 1 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'validate:"required"'];
  int64 AwardId = 2 [(gogoproto.jsontag) = "award_id", json_name = "award_id", (gogoproto.moretags) = 'validate:"required"'];
}

message GeneralDummyRewardsCollectionRep {
  int64 Mid = 1 [(gogoproto.jsontag) = "mid", json_name = "mid",  (gogoproto.moretags) = 'validate:"required"'];
  int64 ActivityId = 2 [(gogoproto.jsontag) = "activity_id", json_name = "activity_id", (gogoproto.moretags) = 'validate:"required"'];
  string ActivityName = 3 [(gogoproto.jsontag) = "activity_name", json_name = "activity_name", (gogoproto.moretags) = 'validate:"required"'];
  int64 AwardId = 4 [(gogoproto.jsontag) = "award_id", json_name = "award_id", (gogoproto.moretags) = 'validate:"required"'];
  string UniqueId = 5 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id", (gogoproto.moretags) = 'validate:"required"'];
  repeated CollectionMessage CollectionInfo = 6 [(gogoproto.jsontag) = "collection_info", json_name = "collection_info", (gogoproto.moretags) = 'validate:"required"'];
}

message CollectionMessage {
  string Field = 1 [(gogoproto.jsontag) = "field", json_name = "field", (gogoproto.moretags) = 'validate:"required"'];
  int64 Status = 2 [(gogoproto.jsontag) = "status", json_name = "status", (gogoproto.moretags) = 'validate:"required"'];
  string Value = 3 [(gogoproto.jsontag) = "value", json_name = "value", (gogoproto.moretags) = 'validate:"required"'];
}

message RewardsBatchGetAwardConfigByIdsReq {
  repeated int64 Ids = 1 [(gogoproto.jsontag) = "ids", json_name = "ids", (gogoproto.moretags) = 'form:"ids" validate:"required"'];
}

message RewardsBatchGetAwardConfigByIdsReply {
  map<int64, RewardsAwardInfo>  AwardConfigs = 1 [(gogoproto.jsontag) = "award_configs",json_name = "award_configs"];
}

message RewardsBatchGetStockByIdsReq {
  // 奖品ID,示例：5ERAXXXXXX00（单批次最多10条）
  repeated string OuterSids    = 1 [(gogoproto.jsontag) = "outer_sids", json_name = "outer_sids" , (gogoproto.moretags) = 'validate:"min=1,max=10"'];
  // 奖品配置版本号，当前只能查线上剩余库存，未具体实现。
  AwardConfigType  ConfVersion = 2 [(gogoproto.jsontag) = "conf_version", json_name = "conf_version" , (gogoproto.moretags) = 'validate:"required"'];
  // 用户ID,部分奖品会设置单用户库存上限
  int64 Mid                    = 3 [(gogoproto.jsontag) = "mid", json_name = "mid"];
}

message RewardsBatchGetStockByIdsReply {
  map<string, RewardsGetAwardStockByIdReply> StockInfos = 1 [(gogoproto.jsontag) = "stock_infos", json_name = "stock_infos"];
}

message GetDelDataByAwardIdReq {
  string AwardOuterID = 1 [(gogoproto.jsontag) = "award_outer_id", json_name = "award_outer_id", (gogoproto.moretags) = 'form:"award_outer_id" validate:"required"'];
  int64 Pn = 2 [json_name = "pn", (gogoproto.moretags) = 'default:"1"'];
  int64 Ps = 3 [json_name = "ps", (gogoproto.moretags) = 'default:"100"'];
}

message GetDelDataByAwardIdReply {
  repeated CdKeyV3 List  = 1 [(gogoproto.jsontag) = "list", json_name = "list"];
}

message CdKeyV3 {
  int64 Id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  int64 Mid = 2 [(gogoproto.jsontag) = "mid", json_name = "mid"];
  int64 AwardID = 3 [(gogoproto.jsontag) = "award_id", json_name = "award_id"];
  string CdKeyContent = 4 [(gogoproto.jsontag) = "cdkey_content", json_name = "cdkey_content"];
  string UniqueID = 5 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
  string UploadBatch = 6 [(gogoproto.jsontag) = "upload_batch", json_name = "upload_batch"];
  int64 IsUsed = 7 [(gogoproto.jsontag) = "is_used", json_name = "is_used"];
}

message DelDataByAwardIdReq {
  int64 AwardID = 1 [(gogoproto.jsontag) = "award_id", json_name = "award_id", (gogoproto.moretags) = 'form:"award_id" validate:"required"'];
}

message CdKeyV2 {
  int64 Id = 1 [(gogoproto.jsontag) = "id", json_name = "id"];
  string Content = 2 [(gogoproto.jsontag) = "content", json_name = "content"];
  string UniqueID = 3 [(gogoproto.jsontag) = "unique_id", json_name = "unique_id"];
}

message GetTaishanCdKeyPageListReq {
  int64 AwardID = 1 [(gogoproto.jsontag) = "award_id", json_name = "award_id", (gogoproto.moretags) = 'form:"award_id" validate:"required"'];
  int64 Start = 2 [(gogoproto.jsontag) = "start", json_name = "start", (gogoproto.moretags) = 'form:"start" default:"0"'];
  int64 Limit = 3 [(gogoproto.jsontag) = "limit", json_name = "limit", (gogoproto.moretags) = 'form:"limit" default:"20" validate:"min=0,max=1000"'];
}

message GetTaishanCdKeyPageListReply {
  repeated CdKeyV2 CdkeyList = 1 [(gogoproto.jsontag) = "cdkey_list", json_name = "cdkey_list"];
}
